import { Icon } from '@commons';
import { But<PERSON>, Result } from 'antd';
import axios from 'axios';
import Image from 'next/image';
import { useRouter } from 'next/router';
import { getSlackErrorLink, isWithinOfficeHour } from '../src/utils/app.utils';

const Custom404 = context => {
  const router = useRouter();

  // get project details
  const projectId = typeof localStorage !== 'undefined' ? localStorage.getItem('ProjectId') : null;
  const projectName = typeof localStorage !== 'undefined' ? localStorage.getItem('ProjectName') : null;
  const role = typeof localStorage !== 'undefined' ? localStorage.getItem('ProjectUserRole') : null;

  // get user details
  const user = typeof sessionStorage !== 'undefined' ? sessionStorage.getItem('user') : null;
  const userData = user ? JSON.parse(user) : null;

  const { userAgent, platform, appName, appVersion } = typeof navigator !== 'undefined' ? navigator : {};

  const subTitle = isWithinOfficeHour() ? (
    <b className="text-binaBlue">
      We&apos;re sorry for the inconvenience, there&apos;s a maintenance work happening on this area, our team are
      working diligently to resolves the issues. <br></br>Thank you for your patient
    </b>
  ) : (
    <b className="text-binaBlue">
      Our web application is currently receiving an unusually high volume of traffic. Rest assured, our team is working
      diligently to scale our system for smoother access. <br></br>We appreciate your patience, and kindly ask you to
      try accessing it again shortly.
    </b>
  );

  const image = isWithinOfficeHour() ? (
    <Image alt="maintenance" src="/assets/maintenance.jpg" layout="fixed" height={500} width={700} />
  ) : (
    <Image alt="maintenance" src="/assets/traffic.png" layout="fixed" height={400} width={600} />
  );

  const name = userData?.name;
  const email = userData?.email;
  const phone = userData?.phone;

  return (
    <Result
      className="bg-white h-screen"
      icon={image}
      subTitle={subTitle}
      extra={
        <Button
          onClick={async () => {
            try {
              const data = {
                text: `Type:${process.env.NODE_ENV}\n\nPage Not Found at page ${router.asPath}\n\nProject Detail\nProject Id: ${projectId}\nProject name: ${projectName}\n\nDevice detail\nPlatform:${platform}\nUser Agent:${userAgent}\nApp Version:${appVersion}\n\nUser Detail\nUser Name: ${name}\nUser Email: ${email}\nUser Phone No: ${phone}\nUser Role: ${role}`
              };

              await axios.post(getSlackErrorLink(), JSON.stringify(data), {
                withCredentials: false,
                transformRequest: [
                  (data, headers) => {
                    delete headers.post['Content-Type'];
                    return data;
                  }
                ]
              });
              router.back();
            } catch (e) {
              onerror(e);
            }
          }}
          type="primary"
        >
          Dismiss
        </Button>
      }
    />
  );
};

export default Custom404;
