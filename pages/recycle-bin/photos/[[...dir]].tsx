import { ExclamationCircleOutlined, HomeOutlined } from '@ant-design/icons';
import SearchInput from '@components/forms/FormsInput/SearchInput';
import useQueries from '@components/hooks/useQueries';
import DeletedFilesCard from '@components/recycle-bin/DeletedFilesCard';
import DeletedFoldersCard from '@components/recycle-bin/DeletedFoldersCard';
import RecycleBinTabLayout from '@components/recycle-bin/RecycleBinTabLayout';
import * as Gql from '@graphql';
import { manageFiles } from '@utils/authority';
import { onError } from '@utils/error';
import { tz } from '@utils/timezone';
import { Breadcrumb, Button, Col, Form, message, Modal, Row, Space, Spin } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import _, { debounce, isEmpty } from 'lodash';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { AppLayoutContext } from 'pages/_app';
import { useContext, useEffect, useRef, useState } from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';
import useDropzone from 'src/hooks/useDropzone';

const Photos = () => {
  const router = useRouter();
  const [form] = useForm();
  const [projectUserRole, setProjectUserRole] = useState<string>('');
  const [dataSource, setDataSource] = useState<any>([]);
  const [dataSourceFiles, setDataSourceFiles] = useState<any>([]);
  const [isDocOwner, setIsDocOwner] = useState<boolean>(false);

  const [breadcrumbs, setBreadcrumbs] = useState<
    Gql.GetProjectDocumentsBreadcrumbQuery['getProjectDocumentsBreadcrumb']
  >([]);
  const uploadPhotoModalRef = useRef<any>(null);

  const canEdit = projectUserRole === 'CanEdit';
  const dirId = router.query.dir && (router.query.dir as string);
  const { userData: userMeData } = useContext(AppLayoutContext);

  const [loading, setLoading] = useState<any>(false);

  // For multiple selection
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [isManage, setIsManage] = useState(false);
  const [date, setDate] = useState<any>(null);

  const { confirm } = Modal;

  const canDelete = projectUserRole === 'ProjectOwner' || projectUserRole === 'CloudCoordinator' || isDocOwner;
  const { canCreate, canMove } = manageFiles(projectUserRole, dirId);
  const name = form.getFieldValue('keyword') ?? '';

  const dropzoneRef = useRef<any>(null);
  useDropzone({ dropzoneRef, pushModal: uploadPhotoModalRef?.current?.openModal });

  // check if user is editor of the project, if yes, then allow to delete the photos that added by him
  useEffect(() => {
    if (selectedRowKeys.length > 0 && canEdit) {
      // find the selected photos and folder that added by the user
      const selectedPhotos = dataSourceFiles.filter((file: any) => selectedRowKeys.includes(file.id));
      const selectedFolders = dataSource.filter((folder: any) => selectedRowKeys.includes(folder.id));
      // if the selected photos and folders are added by the user, then allow to delete
      if (selectedPhotos.length > 0 || selectedFolders.length > 0) {
        const addedByUser =
          selectedPhotos.every((photo: any) => photo.addedBy === userMeData?.getUserMe.id) &&
          selectedFolders.every((folder: any) => folder.addedBy === userMeData?.getUserMe.id);
        if (addedByUser) {
          setIsDocOwner(true);
        } else {
          setIsDocOwner(false);
        }
      }
    }
  }, [selectedRowKeys]);

  const onSelectChange = (id: string, status: boolean) => {
    if (status) {
      setSelectedRowKeys(prev => [...prev, id]);
    } else {
      setSelectedRowKeys(prev => prev.filter(key => key !== id));
    }
  };

  const [getBreadcrumbs, { data: breadcrumbData }] = Gql.useGetProjectDocumentsBreadcrumbLazyQuery();

  useEffect(() => {
    refetch();
    refetchFiles();
    // setFilter({ name: { like: "" } });

    if (!dirId) {
      setBreadcrumbs([]);
      return;
    }

    if (dirId) {
      getBreadcrumbs({
        variables: { input: { id: parseInt(dirId as string) } }
      }).then(res => {
        setBreadcrumbs(res.data?.getProjectDocumentsBreadcrumb ?? []);
      });
    }
  }, [dirId, breadcrumbData]);

  useEffect(() => {
    setProjectUserRole(localStorage.getItem('ProjectUserRole') || '');
  }, []);

  const { queries, setFilter } = useQueries<Gql.ProjectDocumentFilter, Gql.ProjectDocumentSort>({
    paging: { offset: 0, limit: 20 },
    usePagingParam: false
  });

  const search = queries?.filter?.name?.like;

  const {
    data,
    refetch,
    fetchMore,
    loading: loadingData
  } = Gql.useGetDeletedProjectDocumentsQuery({
    variables: {
      ...queries,
      filter: {
        ...queries.filter,
        ...{
          // projectDocumentId: {
          //   ...(dirId ? { eq: dirId?.toString() } : { eq: null })
          // },
          ...(!search || search?.length === 0
            ? {
              projectDocumentId: {
                ...(dirId ? { eq: dirId?.toString() } : { eq: null })
              }
            }
            : {}),
          category: { eq: Gql.CategoryType.Photo },
          fileSystemType: { eq: Gql.FileSystemType.Folder }
        }
      },
      sorting: [
        {
          direction: Gql.SortDirection.Desc,
          field: Gql.DeletedProjectDocumentSortFields.DeletedAt
        }
      ]
    },
    onError: onError
  });

  const { queries: queriesFiles, setFilter: setFilterFiles } = useQueries<
    Gql.ProjectDocumentFilter,
    Gql.ProjectDocumentSort
  >({ paging: { offset: 0, limit: 20 }, usePagingParam: false });

  const {
    data: photoFiles,
    refetch: refetchFiles,
    fetchMore: fetchMoreFiles,
    loading: loadingFiles
  } = Gql.useGetDeletedProjectDocumentsQuery({
    variables: {
      ...queriesFiles,
      filter: {
        ...queries.filter,
        ...{
          ...(!search || search?.length === 0
            ? {
              projectDocumentId: {
                ...(dirId ? { eq: dirId?.toString() } : { eq: null })
              }
            }
            : {}),
          category: { eq: Gql.CategoryType.Photo },
          fileSystemType: { eq: Gql.FileSystemType.Document }
        }
      },
      sorting: [
        {
          direction: Gql.SortDirection.Desc,
          field: Gql.DeletedProjectDocumentSortFields.DeletedAt
        }
      ]
    },
    onError: onError
  });

  useEffect(() => {
    setDataSource(data?.getDeletedProjectDocuments?.nodes ?? []);
  }, [data]);

  useEffect(() => {
    setDataSourceFiles(photoFiles?.getDeletedProjectDocuments?.nodes ?? []);
  }, [photoFiles]);

  // SEARCH
  const onFilter = (data: any) => {
    // const search = ProjectDocumentApiService.searchData({
    //   body: {
    //     name: data,
    //     projectDocumentId: dirId as string,
    //     category: Gql.CategoryType.Photo,
    //   },
    // });
    // search.then((res: any) => {
    //   // filter data based on fileSystemType
    //   setDataSource(
    //     res.filter(
    //       (item: any) => item.fileSystemType === Gql.FileSystemType.Folder
    //     ) ?? []
    //   );
    //   setDataSourceFiles(
    //     res.filter(
    //       (item: any) => item.fileSystemType === Gql.FileSystemType.Document
    //     ) ?? []
    //   );
    // });

    // search files and folders
    setFilter({
      name: { like: data },
      ...(date && date !== 'Invalid date' ? { createdAt: { gte: date } } : {})
    });
    setFilterFiles({
      name: { like: data },
      ...(date && date !== 'Invalid date' ? { createdAt: { gte: date } } : {})
    });
  };


  const debouncedOnFilter = _.debounce((data: any) => {
    onFilter(data.keyword);
  }, 500);

  //* infinite scroll *//
  const fetchMoreData = async () => {
    fetchMore({
      variables: {
        paging: {
          offset: data?.getDeletedProjectDocuments?.nodes?.length ?? 0,
          limit: 20
        }
      },
      //   updateQuery: (prev, { fetchMoreResult }) => {
      //     if (!fetchMoreResult) return prev;
      //     return {
      //       getProjectDocuments: {
      //         ...fetchMoreResult.getDeletedProjectDocuments,
      //         nodes: [...(prev.getDeletedProjectDocuments?.nodes ?? []), ...(fetchMoreResult.getDeletedProjectDocuments?.nodes ?? [])]
      //       }
      //     };
      //   }
    });
  };

  const fetchMoreFileData = async () => {
    fetchMoreFiles({
      variables: {
        paging: {
          offset: photoFiles?.getDeletedProjectDocuments.nodes.length,
          limit: 20
        }
      },
      //   updateQuery: (prev, { fetchMoreResult }) => {
      //     if (!fetchMoreResult) return prev;
      //     return {
      //       getProjectDocuments: {
      //         ...fetchMoreResult.getDeletedProjectDocuments,
      //         nodes: [...(prev.getDeletedProjectDocuments?.nodes ?? []), ...(fetchMoreResult.getDeletedProjectDocuments?.nodes ?? [])]
      //       }
      //     };
      //   }
    });
  };


  const onRestore = async (id: number) => {
    try {
      await restoreDocument({
        variables: {
          id: +id
        }
      });
    } catch (e) {
      onError(e);
    }
  };

  const onRestoreMany = async () => {
    try {
      await restoreDocuments({
        variables: {
          input: {
            ids: selectedRowKeys.map(str => Number(str))
          }
        }
      });
    } catch (e) {
      onError(e);
    }
  };

  const onDelete = async (id: number) => {
    try {
      await deleteDocument({
        variables: {
          id: +id
        }
      });
    } catch (e) {
      onError(e);
    }
  };

  const onDeleteMany = async () => {
    try {
      await deleteDocuments({
        variables: {
          input: {
            ids: selectedRowKeys.map(str => Number(str))
          }
        }
      });
    } catch (e) {
      onError(e);
    }
  };

  const [restoreDocument, { loading: documentRestoring }] = Gql.useRestoreOneDocumentMutation({
    onCompleted: () => {
      message.success('Folder / Document restored successfully');
      setIsManage(false)
      refetch();
    },
    onError(error) {
      onError(error);
    }
  });

  const [restoreDocuments, { loading: documentsRestoring }] = Gql.useRestoreManyDocumentsMutation({
    onCompleted: () => {
      message.success('Contents restored back to origin directory');
      setIsManage(false)
      setSelectedRowKeys([]);
      refetch();
    },
    onError: onError as any
  });

  const [deleteDocument, { loading: documentDeleting }] = Gql.usePermanentDeleteOneDocumentMutation({
    onCompleted: () => {
      message.success('Folder / Document deleted permanently from bin');
      setIsManage(false)
      refetch();
    },
    onError(error) {
      onError(error);
    }
  });

  const [deleteDocuments, { loading: documentsDeleting }] = Gql.usePermanentDeleteManyDocumentsMutation({
    onCompleted: () => {
      message.success('Contents deleted permanently from bin');
      setIsManage(false)
      setSelectedRowKeys([]);
      refetch();
    },
    onError: onError as any
  });

  const showDeleteConfirm = (id: string, title: string, type: string) => {
    confirm({
      title: 'Delete document permanently? ',
      icon: <ExclamationCircleOutlined />,
      content: (
        (!id) ?
          <p>Do you really want to delete {selectedRowKeys.length} content(s) permanently?</p>
          :
          <p>Do you really want to delete the {type} <span className="font-semibold">{title}</span> permanently?</p>
      ),
      okText: 'Yes',
      okType: 'danger',
      cancelText: 'No',
      onOk() {
        id ? onDelete(+id) : onDeleteMany()
      },
      onCancel() { }
    });
  };

  const showRestoreConfirm = (id: string, title: string, type: string, category: string) => {
    confirm({
      title: 'Restore document? ',
      icon: <ExclamationCircleOutlined />,
      content: (
        (!id) ?
          <p>Do you really want to restore {selectedRowKeys.length} content(s) back to their directory?</p>
          :
          <p>Do you really want to restore the {type} <span className="font-semibold">{title}</span> back to {category}?</p>
      ),
      okText: 'Yes',
      okType: 'danger',
      cancelText: 'No',
      onOk() {
        id ? onRestore(+id) : onRestoreMany()
          ;
      },
      onCancel() { }
    });
  };

  return (
    <div className="absolute overflow-auto h-full w-full" ref={dropzoneRef}>
      {/* {modal} */}

      <Spin
        tip={'Loading...'}
        spinning={loadingData || loading || documentsDeleting}
      >
        {selectedRowKeys.length > 0 ? (
          <div className="flex flex-nowrap items-center justify-between px-5 pt-[20px] pb-[12px]">
            <div>
              <Button
                className="h-12 bg-primary rounded-lg text-white border-gray40 mr-5"
                onClick={() => {
                  setSelectedRowKeys([]);
                  setIsManage(false);
                }}
              >
                Cancel
              </Button>

              {selectedRowKeys.length > 0
                ? `Selected ${selectedRowKeys.length} item${selectedRowKeys.length > 1 ? 's' : ''}`
                : ''}
            </div>
            <div className="flex gap-3">
              <Form
                form={form}
                onValuesChange={debounce(data => {
                  setFilter({ name: { like: data.keyword } });
                }, 500)}
              >
                <SearchInput
                  onPressEnter={() => {
                    // searchForm.submit();
                  }}
                  placeholder={'Search by name'}
                />
              </Form>
              <Button
                className="h-12 bg-yellow-500 rounded-lg text-white border-gray40"
                onClick={() => {
                  showRestoreConfirm('', '', '', '');
                }}
              >
                Restore
              </Button>
              <Button
                className="h-12 bg-red-500 rounded-lg text-white border-gray40"
                onClick={() => showDeleteConfirm('', '', '')}
              >
                Delete from Bin
              </Button>
            </div>
          </div>
        ) :
          <div className="flex flex-nowrap items-center justify-between px-5 pt-[20px] pb-[12px]">
            <div>
              <h3>Deleted Photos</h3>
              {dirId && (
                <Breadcrumb>
                  <Breadcrumb.Item className="text-gray90 text-md">
                    <Link href="/photos/folder">
                      <HomeOutlined />
                    </Link>
                  </Breadcrumb.Item>
                  {breadcrumbs.map(value => (
                    <Breadcrumb.Item className="text-gray90 text-md" key={value.id}>
                      <Link href={`/photos/folder?dir=${value.id}`}>{value.name}</Link>
                    </Breadcrumb.Item>
                  ))}
                </Breadcrumb>
              )}
            </div>
            <Row>
              <Col>
                <Form
                  form={form}
                  onValuesChange={(data: any) => {
                    // console.log(data);
                    debouncedOnFilter(data);
                  }}
                >
                  <Space>
                    <SearchInput onPressEnter={() => { }} placeholder={'Search by name'} />
                  </Space>
                </Form>
              </Col>
            </Row>
          </div>

        }

        {/* <DndProvider backend={HTML5Backend}> */}
        <div className="mb-12 px-5" style={{}}>
          <h1 className="mb-3 font-bold">Folders</h1>
          <div className="mb-7 overflow-x-hidden bg-slate-50 rounded-md" id="scrollableDiv">
            <InfiniteScroll
              dataLength={dataSource.length}
              next={fetchMoreData}
              hasMore={data?.getDeletedProjectDocuments.pageInfo.hasNextPage || false}
              loader={null}
              height={250}
              style={{ overflowX: 'hidden' }}
            >
              {isEmpty(dataSource) && !loadingData && name ? (
                <Row gutter={8} className="overflow-x-hidden overflow-y-hidden flex justify-center mt-16">
                  <img alt="not-found" src="/assets/not_found.png" className="object-contain w-32" />
                </Row>
              ) : (
                <Row gutter={8} className="overflow-x-hidden overflow-y-hidden">
                  {dataSource
                    .filter((d: any) => d.type === 'folder')
                    .map((folder: any) => {
                      return (
                        <Col span={6} className="mb-2" key={folder.id}>
                          <DeletedFoldersCard
                            refetch={refetch}
                            folderId={folder.id}
                            folderName={folder.name}
                            folderType={folder.type}
                            folderCategory={folder.category}
                            dataSource={dataSource}
                            onClick={() => {
                              if (folder?.fileSystemType === 'Folder') {
                                confirm({
                                  title: 'This folder has been deleted ',
                                  icon: <ExclamationCircleOutlined />,
                                  content: (
                                    <p>
                                      To view the contents of this folder, please restore first.
                                    </p>
                                  ),
                                  okText: 'Restore',
                                  okType: 'danger',
                                  cancelText: 'Dismiss',
                                  onOk() {
                                    showRestoreConfirm(folder.id, folder.name, folder.type, folder.category);
                                  },
                                  onCancel() { }
                                });
                              }
                            }}
                            onSelect={onSelectChange}
                            isManage={isManage}
                            setIsManage={setIsManage}
                            selectedRowKeys={selectedRowKeys}
                            showDeleteConfirm={showDeleteConfirm}
                            showRestoreConfirm={showRestoreConfirm}
                          />
                        </Col>
                      );
                    })}
                </Row>
              )}
            </InfiniteScroll>
          </div>
          <h1 className="mb-3 font-bold">Files</h1>
          <div className="mb-7 bg-slate-50 rounded-md" id="scrollableDiv">
            <InfiniteScroll
              dataLength={dataSourceFiles.length}
              next={fetchMoreFileData}
              hasMore={photoFiles?.getDeletedProjectDocuments.pageInfo.hasNextPage || false}
              loader={null}
              height={650}
              style={{ overflowX: 'hidden' }}
            >
              {isEmpty(dataSourceFiles) && !loadingFiles && name ? (
                <Row gutter={8} className="overflow-hidden flex justify-center mt-[20%]">
                  <img alt="not-found" src="/assets/not_found.png" className="object-contain w-32" />
                </Row>
              ) : (
                <Row gutter={8}>
                  {dataSourceFiles
                    .filter((file: any) => !(file.type === 'folder'))
                    ?.map?.((file: any) => {
                      return (
                        <Col span={6} className="mb-0" key={file.id}>
                          <DeletedFilesCard
                            refetch={refetchFiles}
                            fileUrl={file.fileUrl}
                            name={file.name}
                            dataSource={dataSource}
                            fileId={file.id}
                            deletedAt={tz(file.deletedAt) as any}
                            onSelect={onSelectChange}
                            isManage={isManage}
                            setIsManage={setIsManage}
                            selectedRowKeys={selectedRowKeys}
                            file={file}
                            showDeleteConfirm={showDeleteConfirm}
                            showRestoreConfirm={showRestoreConfirm}
                          />
                        </Col>
                      );
                    })}
                </Row>
              )}
            </InfiniteScroll>
          </div>
        </div>
        {/* </DndProvider> */}
      </Spin>
    </div>
  );
};

Photos.auth = true;
Photos.Layout = RecycleBinTabLayout;
export default Photos;
