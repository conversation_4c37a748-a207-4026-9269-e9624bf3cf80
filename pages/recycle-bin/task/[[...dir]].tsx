import { ExclamationCircleOutlined, FilterOutlined, UserOutlined } from '@ant-design/icons';
import { default as useQueries } from '@components/hooks/useQueries';
import RecycleBinTabLayout from '@components/recycle-bin/RecycleBinTabLayout';
import FilterDrawer from '@components/tasks/FilterDrawerTasks';
import TaskAddDrawer from '@components/tasks/TaskAddDrawer';
import TaskEditDrawer from '@components/tasks/TaskEditDrawer';
import ThreeDotsDropDown from '@components/ThreeDotsDropDown';
import { ProjectAccess } from '@constants/subscription';
import * as Gql from '@graphql';
import { isAllowed } from '@lib/helper';
import { onError } from '@utils/error';
import { tz } from '@utils/timezone';
import { Avatar, Badge, Button, Form, Menu, message, Modal, Row, Space, Switch, Table, Tooltip } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import { ColumnsType } from 'antd/lib/table';
import Paragraph from 'antd/lib/typography/Paragraph';
import moment from 'moment';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { AppLayoutContext } from 'pages/_app';
import { AlignType } from 'rc-table/lib/interface';
import { memo, useContext, useEffect, useMemo, useRef, useState } from 'react';
import { TasksApiService, UserAuthService } from 'src/api';

const Tasks = () => {
  const [isOnlyMeFilter, setIsOnlyMeFilter] = useState(false);
  const [isCompleteTask, setIsCompleteTask] = useState(false);
  const router = useRouter();
  const { groupId, status, projectGroupId } = router.query;
  const companyId = router.query.companyId as any;
  const taskEditDrawerRef = useRef<any>(null);
  const taskAddDrawerRef = useRef<any>(null);
  const { confirm } = Modal;
  const filterDrawerRef = useRef<any>(null);
  const [searchForm] = useForm();
  const [dataSource, setDataSource] = useState<any>(false);
  const setUserRole = async () => {
    const role = await UserAuthService.currentRole({});
    localStorage.setItem('ProjectUserRole', role);
  };

  const [switchCompany] = Gql.useSwitchCompanyMutation({
    onCompleted: () => { },
    onError: onError as any
  });

  const [projectUserRole, setProjectUserRole] = useState<string>('');

  useEffect(() => {
    setProjectUserRole(localStorage.getItem('ProjectUserRole') || '');
  }, []);

  const projectOwner = projectUserRole === Gql.ProjectUserRoleType.ProjectOwner;
  const cloudCoordinator = projectUserRole === Gql.ProjectUserRoleType.CloudCoordinator;

  const { userData: userMeData } = useContext(AppLayoutContext);
  const viewOnly = projectUserRole === 'CanView';

  useEffect(() => {
    if (router.query.taskId) {
      taskEditDrawerRef?.current?.pushDrawer();
    }
  }, [router.query.taskId]);

  const { queries, setPaging, setFilter } = useQueries<Gql.TaskFilter, Gql.TaskSort>();

  const params = router.query;

  const { data, refetch, loading } = Gql.useGetDeletedTasksQuery({
    fetchPolicy: 'cache-and-network',
    variables: {
      ...queries,
      filter: {
        ...queries.filter,
        ...(projectGroupId && {
          group: { projectGroupId: { eq: projectGroupId as string } }
        }),
        // ...{
        //   dueDate: {
        //     isNot: null
        //   }
        // },
        ...(queries?.filter?.title && {
          title: {
            like: `%${queries?.filter?.title}%`
          }
        }),
        ...(queries?.filter?.groupId && {
          groupId: { eq: queries?.filter?.groupId as string }
        }),
        ...(groupId && {
          groupId: { eq: groupId as string }
        }),
        ...(queries?.filter?.id && {
          taskCode: {
            eq: queries?.filter?.id as string
          }
        }),
        ...(queries?.filter?.status && {
          status: {
            eq: queries?.filter?.status as any
          }
        }),
        ...(status && {
          status: {
            eq: status as string
          }
        }),
        ...(queries?.filter?.assignees && {
          assignees: {
            userId: { in: queries?.filter?.assignees } as any
          }
        }),
        ...(queries?.filter?.dueDate && {
          dueDate: {
            eq: queries?.filter?.dueDate
          }
        }),
      },
      sorting: [
        {
          direction: Gql.SortDirection.Desc,
          field: Gql.TaskDeletedSortFields.DeletedAt
        }
      ]
    },
    onError: onError,
    onCompleted(data) {
      setDataSource(data?.getDeletedTasks.nodes ?? []);
    }
  });
  // const totalCount = data?.getDeletedTasks;
  const totalCount = 0;

  const showDeleteConfirm = (id: string, title: string) => {
    confirm({
      title: 'Delete task permanently? ',
      icon: <ExclamationCircleOutlined />,
      content: (
        <p>
          Do you really want to delete the task <span className="font-semibold">{title}</span> permanently?
        </p>
      ),
      okText: 'Yes',
      okType: 'danger',
      cancelText: 'No',
      onOk() {
        onDelete(+id);
      },
      onCancel() { }
    });
  };

  const onDelete = async (id: number) => {
    try {
      await deleteTasks({
        variables: {
          id: +id
        }
      });
    } catch (e) {
      onError(e);
    }
  };

  const [deleteTasks] = Gql.usePermanentDeleteOneTaskMutation({
    onCompleted: () => {
      message.success('Task deleted permanently');
      refetch();
    },
    onError(error, clientOptions) {
      onError(error);
    }
  });

  const onRestore = async (id: number) => {
    try {
      await restoreTask({
        variables: {
          id: +id
        }
      });
    } catch (e) {
      onError(e);
    }
  };

  const [restoreTask] = Gql.useRestoreOneTaskMutation({
    onCompleted: () => {
      message.success('Task restored successfully');
      refetch();
    },
    onError(error, clientOptions) {
      onError(error);
    }
  });

  const showRestoreConfirm = (id: string, title: string) => {
    confirm({
      title: 'Restore task? ',
      icon: <ExclamationCircleOutlined />,
      content: (
        <p>
          Do you really want to restore the task <span className="font-semibold">{title}</span> ?
        </p>
      ),
      okText: 'Yes',
      okType: 'danger',
      cancelText: 'No',
      onOk() {
        onRestore(+id);
      },
      onCancel() { }
    });
  };

  const [ellipsis, setEllipsis] = useState(true);

  const isFilterDataExist = Object.values({ ...queries?.filter }).some(value => {
    if (Array.isArray(value)) {
      return value.length > 0;
    }
    return !!value;
  });

  const columns = useMemo(
    (): ColumnsType<any> => [
      {
        title: 'TITLE',
        // sorter: (a, b) => a?.title?.localeCompare(b?.title),
        // sorter:true,
        render: (dataSource, index: number) => {
          return (
            <Space key={index}>
              {/* {dataSource.status === Gql.TaskStatusType.Completed ? (
                <Icon name="taskCheckmarkDone" />
              ) : (
                <Icon name="taskCheckmarkUndone" />
              )} */}

              <div
                className="cursor-pointer"
                onClick={() => {
                  confirm({
                    title: 'This task has been deleted ',
                    icon: <ExclamationCircleOutlined />,
                    content: (
                      <p>
                        To view more details of this task, please restore first.
                      </p>
                    ),
                    okText: 'Restore',
                    okType: 'danger',
                    cancelText: 'Dismiss',
                    onOk() {
                      showRestoreConfirm(dataSource.id, dataSource.title);
                    },
                    onCancel() { }
                  });
                }}
              >
                <Paragraph
                  style={{ margin: 0, width: 400, fontSize: 15 }}
                  ellipsis={ellipsis ? { rows: 1, expandable: false } : false}
                  id="task-name"
                >
                  {dataSource.title}
                </Paragraph>
                <Paragraph
                  style={{ margin: 0, color: '#898E92' }}
                  className="text-base max-w-lg"
                  ellipsis={ellipsis ? { rows: 1, expandable: false } : false}
                >
                  {dataSource.description}
                </Paragraph>
              </div>
            </Space>
          );
        }
      },
      {
        title: 'ID',
        key: 'taskCode',
        width: 80,
        // sorter: (a: any, b: any) => (a?.taskCode || 0) - (b?.taskCode || 0),
        render: (data: any) => {
          // const task  = String(data.taskCode).padStart(3, '0')
          // return task ? <p>{'T' + task}</p> : "";
          return data ? (
            <p className="cursor-pointer" id="id">
              {'#' + data.taskCode}
            </p>
          ) : (
            ''
          );
        }
      },
      {
        title: 'STATUS',
        key: 'status',
        width: window.innerWidth < 1200 ? 225 : 150,
        // sorter: (a, b) => a?.status?.localeCompare(b?.status),
        render: (data: any) => {
          const color = data.status === Gql.TaskStatusType.Completed ? '#5BBA5F' : '#585757';

          let borderColor = '';

          if (data.status === Gql.TaskStatusType.InProgress) {
            // data.status = 'In Progress'
            borderColor = '#3E78CF';
          } else if (data.status === Gql.TaskStatusType.Completed) {
            // data.status = 'Closed'
            borderColor = '#18A601';
          }
          // else if (data.status === Gql.TaskStatusType.Overdue)
          //   borderColor = "#F40000"
          else if (data.status === Gql.TaskStatusType.Open) borderColor = '#F40000';
          else if (data.status === Gql.TaskStatusType.Hold) borderColor = '#F29100';
          return (
            <Row className="cursor-pointer">
              <Space
                style={{
                  width: 4,
                  background: borderColor,
                  color: borderColor
                }}
              >
                .
              </Space>
              <p style={{ color }} className="max-w-fit ml-2" id="status">
                {data.status === Gql.TaskStatusType.Completed
                  ? 'Closed'
                  : data.status.replace(/([A-Z])/g, ' $1').trim()}
              </p>
            </Row>
          );
        }
      },
      {
        title: 'DUE DATE',
        key: 'dueDate',
        width: 160,
        // sorter: (a: any, b: any) => new Date(a?.dueDate).getTime() - new Date(b?.dueDate).getTime(),
        render: (data: any) => {
          const color =
            moment(data.dueDate).isBefore(moment()) && data.status !== Gql.TaskStatusType.Completed
              ? '#FF2020'
              : '#585757';

          return data.dueDate ? (
            <p className="max-w-fit cursor-pointer" id="due-date" style={{ color }}>
              {moment(data.dueDate).format('DD/MM/YYYY')}
            </p>
          ) : (
            ''
          );
        }
      },
      {
        title: 'ASSIGNED TO',
        dataIndex: 'assignees',
        key: 'assignees',
        // sorter: (a: any, b: any) => (a?.assignees?.nodes[0]?.userId || 0) - (b?.assignees?.nodes[0]?.userId || 0),
        width: 160,
        render: (assignees: any, index: any) => {
          return (
            <div className="cursor-pointer">
              {/* Filters */}
              {index?.assignees?.map?.((assignee: any, index: any) => (
                <Tooltip key={index} title={assignee?.user?.name} placement="bottom">
                  {assignee?.user?.name ? (
                    <Avatar
                      className="align-middle"
                      style={{ backgroundColor: '#69c0ff', marginLeft: '5px' }}
                      src={assignee?.user?.avatar}
                    />
                  ) : (
                    <Avatar style={{ backgroundColor: '#69c0ff', marginLeft: '15px' }} icon={<UserOutlined />} />
                  )}
                </Tooltip>
              ))}
              {/* Assigned To Me */}
              {assignees?.nodes?.map((assignee: any, index: any) => (
                <Tooltip key={index} title={assignee?.user?.name} placement="bottom">
                  <Avatar
                    className="align-middle avatar-default"
                    style={{ marginLeft: '5px' }}
                    src={assignee?.user?.avatar}
                  />
                </Tooltip>
              ))}
            </div>
          );
        }
      },
      {
        title: 'GROUP',
        dataIndex: 'group',
        key: 'group',
        width: 160,
        // sorter: (a, b) => a?.name?.localeCompare(b?.name),
        // sorter: (a, b) => a?.group?.title?.localeCompare(b?.group?.title),
        render: (group: any) => {
          return group ? (
            <Paragraph id="group" className="cursor-pointer" style={{marginBottom: 0}} ellipsis={{ rows: 2, expandable: false }}>
              {group?.title}
            </Paragraph>
          ) : (
            'N/A'
          );
        }
      },
      {
        title: 'DELETED ON',
        key: 'deletedAt',
        width: 160,
        dataIndex: 'deletedAt',
        render(value, record, index) {
          return (
            <p>{moment(tz(value)).format('D MMM YYYY')}</p>
          );
        },
      },
      {
        title: '',
        key: 'action',
        width: 20,
        align: 'left' as AlignType,
        onCell: () => {
          return {
            onClick: (e: any) => {
              e.stopPropagation();
            }
          };
        },
        render: (data: any) => {
          // only owner and project owner and cloud coordinator can delete
          if (userMeData?.getUserMe.id === data?.ownerId || projectOwner || cloudCoordinator) {
            return (
              <ThreeDotsDropDown
                onClick={(e: any) => {
                  if (e.key === '2') {
                    showDeleteConfirm(data.id, data.title);
                  }
                  else if (e.key === '1') {
                    showRestoreConfirm(data.id, data.title);
                  }
                }}
                items={[
                  {
                    label: 'Restore',
                    key: '1',
                  },
                  {
                    key: '2',
                    label: 'Delete Permanently'
                  }
                ]}
              />
            );
          } else {
            return;
          }
        }
      }
    ],
    [router]
  );

  const route = useRouter();

  // const onFilter = (data: any) => {
  //   const search = TasksApiService.filterTasks({
  //     body: {
  //       title: data.keyword ?? '',
  //       taskCode: data.id ?? '',
  //       status: data.status ?? '',
  //       dueDate: data.dueDate ?? '',
  //       group: data.group ?? '',
  //       assignedTo: data.assignee ?? ''
  //     }
  //   });
  //   search.then((res: any) => {
  //     // console.log(res)
  //     // setDataSource(res);
  //   });
  // };

  const clearFilter = () => {
    // clear the router query
    router.replace('recycle-bin/task');
  };

  // /** START ACL */
  // const companySubscriptions = Gql.useCompanySubscriptionsQuery({
  //   variables: {
  //     sorting: {
  //       direction: Gql.SortDirection.Desc,
  //       field: Gql.CompanySubscriptionSortFields.SubscriptionEndDate
  //     },
  //     paging: {
  //       limit: 1
  //     }
  //   },
  //   nextFetchPolicy: 'cache-first'
  // });
  // /** END ACL */

  return (
    <>
      <div className="pt-5 pb-9 absolute overflow-auto h-full w-full">
        <div className="px-5 flex pb-[12px] justify-between items-center">
          <div>
            <h3>Deleted Tasks</h3>
          </div>

          <div>
            <Form form={searchForm} >
              <Space>
                {/* <Switch
                  checkedChildren="Assigned To Me"
                  unCheckedChildren="Assigned To Me"
                  onChange={async checked => {
                    await setIsOnlyMeFilter(checked);
                  }}
                /> */}

                <Badge dot={isFilterDataExist} offset={[-3, 3]}>
                  <Button
                    type="default"
                    className="ml-4 h-12 rounded-lg border-gray40"
                    onClick={() => {
                      filterDrawerRef?.current?.pushDrawer();
                    }}
                    icon={<FilterOutlined />}
                  >
                    Filters
                  </Button>
                </Badge>
                {/* <Button
                  type="primary"
                  className="rounded-lg h-12"
                  disabled={viewOnly}
                  onClick={() => {
                    // taskAddDrawerRef?.current?.pushDrawer();
                  }}
                >
                  Delete All
                </Button> */}
              </Space>
            </Form>
          </div>
        </div>
        <div>
          <Table
            className="pt-2 dashboard-table px-5"
            size="small"
            columns={columns}
            // onRow={(record, rowIndex) => {
            //   return {
            //     onClick: e => {
            //       router.replace({
            //         query: {
            //           ...router.query,
            //           taskId: record.id
            //         }
            //       });
            //     }
            //   };
            // }}
            dataSource={dataSource}
            pagination={{
              pageSize: queries.paging.limit,
              current: queries.paging.offset / queries.paging.limit + 1,
              total: totalCount ?? 0
            }}
            scroll={{
              scrollToFirstRowOnChange: false
              // y: window.innerHeight - 200
            }}
            tableLayout="auto"
            onChange={paginate => {
              const { current, pageSize } = paginate;

              if (pageSize !== undefined && current !== undefined) {
                setPaging({
                  offset: (current - 1) * pageSize,
                  limit: pageSize
                });
              }
            }}
            loading={loading}
          />
        </div>

        <FilterDrawer
          ref={filterDrawerRef}
          onSubmit={async (values: any) => {
            const { groupId, status, dueDate, assignees, keyword, id } = values;
            await setFilter({
              ...(assignees && {
                assignees
              }),
              ...(id && {
                taskCode: {
                  eq: id
                }
              }),
              ...(groupId && {
                groupId
              }),
              ...(keyword && {
                title: keyword
              }),
              ...(status && {
                status
              }),
              ...(dueDate && {
                dueDate: values.dueDate ? moment(values.dueDate?._d).format('YYYY-MM-DD') : ('' as any)
              }),
            });
          }}
          onClear={clearFilter}
        />
      </div>
    </>
  );
};
Tasks.auth = true;
Tasks.Layout = RecycleBinTabLayout;
export default Tasks;
