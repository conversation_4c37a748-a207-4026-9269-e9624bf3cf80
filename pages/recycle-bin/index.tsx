import { ProjectAccess } from '@constants/subscription';
import * as Gql from '@graphql';
import { isAllowed } from '@lib/helper';
import { onError } from '@utils/error';
import { useRouter } from 'next/router';
import { AppLayoutContext } from 'pages/_app';
import { useContext } from 'react';

const RecycleBin = () => {
  const route = useRouter();
  const { userRole } = useContext(AppLayoutContext);

  /** START ACL */
  const companySubscriptions = Gql.useCompanySubscriptionsQuery({
    variables: {
      sorting: {
        direction: Gql.SortDirection.Desc,
        field: Gql.CompanySubscriptionSortFields.SubscriptionEndDate
      },
      paging: {
        limit: 1
      }
    },
    onError: onError,
    nextFetchPolicy: 'cache-first'
  });
  /** END ACL */

  // return the previous page if user is not allowed to access this page
  if (userRole === Gql.ProjectUserRoleType.CanView) return route.back();
  const canViewTask = userRole !== Gql.ProjectUserRoleType.CanEdit || userRole !== Gql.ProjectUserRoleType.CanEdit;
  
  if (isAllowed(companySubscriptions, ProjectAccess.TASK) && canViewTask) route.push('/recycle-bin/task');
  else if (isAllowed(companySubscriptions, ProjectAccess.PROJECT_DOCUMENT)) route.push('/recycle-bin/cloud-docs');

  return null;
};

RecycleBin.auth = true;
export default RecycleBin;
