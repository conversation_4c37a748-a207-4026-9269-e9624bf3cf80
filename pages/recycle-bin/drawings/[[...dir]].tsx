import Icon, { ExclamationCircleOutlined, HomeOutlined, UsergroupAddOutlined, UserOutlined } from '@ant-design/icons';
import FileUpload from '@components/FileUpload';
import SearchInput from '@components/forms/FormsInput/SearchInput';
import useQueries from '@components/hooks/useQueries';
import MoveBulkCloudDocsModal from '@components/MoveBulkCloudDocsModal';
import MoveFileModal from '@components/MoveCloudDocsModal';
import PdfTronModal from '@components/pdf-tron/PdfTronModal';
import RecycleBinTabLayout from '@components/recycle-bin/RecycleBinTabLayout';
import ThreeDotsDropDown from '@components/ThreeDotsDropDown';
import * as Gql from '@graphql';
import { manageFiles } from '@utils/authority';
import { onError } from '@utils/error';
import { tz } from '@utils/timezone';
import {
  Avatar,
  Breadcrumb,
  Button,
  Form,
  message,
  Modal,
  Row,
  Space,
  Spin,
  Table,
  Tooltip
} from 'antd';
import { useForm } from 'antd/lib/form/Form';
import { ColumnsType } from 'antd/lib/table';
import Paragraph from 'antd/lib/typography/Paragraph';
import _, { debounce, isArray, set } from 'lodash';
import moment from 'moment';
import Link from 'next/link';
import { useRouter } from 'next/router';
import React, { useEffect, useRef, useState } from 'react';
import { getFileIcon } from 'src/commons/FileIcon';
import useDropzone from 'src/hooks/useDropzone';

const ProjectDocument = () => {
  const router = useRouter();
  const [form] = useForm();
  const pdfTronTabRef = useRef<any>(null);
  const documentId = router.query.documentId as string;
  const documentUrl = router.query.documentUrl as string;
  const [selectedFormId, setSelectedFormId] = useState<string>('');
  const [formName, setFormName] = useState<string>('');
  const [fileType, setFileType] = useState<string>('');
  const [category, setCategory] = useState<string>('');
  const [viewSharedModal, setViewSharedModal] = useState(false);
  const [searchForm] = useForm();
  const { confirm } = Modal;
  const [projectUserRole, setProjectUserRole] = useState<string>('');
  const [ellipsis, setEllipsis] = useState(true);
  const [dataSource, setDataSource] = useState<any>(false);
  const uploadDocModalRef = useRef<any>(null);
  const moveFileModal = useRef<any>(null);
  const moveDocumentsModal = useRef<any>(null);
  const [breadcrumbs, setBreadcrumbs] = useState<
    Gql.GetProjectDocumentsBreadcrumbQuery['getProjectDocumentsBreadcrumb']
  >([]);
  const dirId = router.query.dir && router.query.dir[0];
  const [getBreadcrumbs] = Gql.useGetProjectDocumentsBreadcrumbLazyQuery();

  const tableRef = useRef<any>(null);
  const [loading, setLoading] = useState(false);
  const [driveType, setDriveType] = useState<Gql.ProjectDocumentDriveType>(Gql.ProjectDocumentDriveType.Shared);
  const [isManage, setIsManage] = useState(false);

  const dropzoneRef = useRef<any>(null);
  useDropzone({ dropzoneRef, pushModal: uploadDocModalRef?.current?.openModal });

  // For multiple selection
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const fixturesGroups = ['Ungroup Documents', 'Site Diary'];

  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  useEffect(() => {
    if (!dirId) {
      setBreadcrumbs([]);
      return;
    }

    getBreadcrumbs({
      variables: { input: { id: parseInt(dirId as string) } }
    }).then(res => {
      setBreadcrumbs(res.data?.getProjectDocumentsBreadcrumb ?? []);
    });
  }, [dirId]);

  // GET PROJECT USER ROLE FROM LOCAL STORAGE
  useEffect(() => {
    setProjectUserRole(localStorage.getItem('ProjectUserRole') || '');
  }, []);

  const handleTableScroll = (e: { target: any }) => {
    const { target } = e;
    if (target.scrollHeight - target.scrollTop <= target.clientHeight + 1) {
      onLoadMore();
    }
  };

  // HOOK FOR QUERIES DATA
  const { queries, setFilter } = useQueries<Gql.ProjectDocumentFilter, Gql.ProjectDocumentSort>({
    usePagingParam: false,
    paging: { offset: 0, limit: 20 }
  });

  const {
    data,
    refetch,
    loading: loadingProjectDocument
  } = Gql.useGetDeletedProjectDocumentsQuery({
    variables: {
      ...queries,
      filter: {
        ...queries.filter,
        ...{
          projectDocumentId: {
            ...(dirId ? { eq: dirId?.toString() } : { eq: null })
          },
          category: {
            in: [
              Gql.CategoryType.TwoDDrawings,
              Gql.CategoryType.BimDrawings
            ],
          },
        }
      },
      sorting: [
        {
          direction: Gql.SortDirection.Desc,
          field: Gql.DeletedProjectDocumentSortFields.DeletedAt
        }
      ]
    },
    onError: onError,
    fetchPolicy: 'cache-and-network'
  });

  useEffect(() => {
    if (documentId) {
      pdfTronTabRef.current.openModal();
    }
  }, [documentId]);

  useEffect(() => {
    if (documentUrl) {
      pdfTronTabRef.current.openModal();
    }
  }, [documentUrl]);

  //Setting Data Source
  useEffect(() => {
    setDataSource(data?.getDeletedProjectDocuments?.nodes ?? []);
  }, [data]);

  // refetch the data when dirId is null
  useEffect(() => {
    if (!dirId) {
      refetch();
    }
  }, [dirId]);

  // update dataSource state onLoadMore function is fire
  useEffect(() => {
    setDataSource(data?.getDeletedProjectDocuments?.nodes ?? []);

    if (tableRef.current) {
      const tableBody = tableRef.current.querySelector('.ant-table-body');
      tableBody.addEventListener('scroll', handleTableScroll);
    }
    return () => {
      if (tableRef.current) {
        const tableBody = tableRef.current.querySelector('.ant-table-body');
        tableBody.removeEventListener('scroll', handleTableScroll);
      }
    };
  }, [handleTableScroll]);

  const onLoadMore = async () => {
    if (!data?.getDeletedProjectDocuments.pageInfo.hasNextPage) return;
    setLoading(true);
    await refetch({
      paging: {
        offset: 0,
        limit: data.getDeletedProjectDocuments.nodes.length + 20
      }
    }).then(res => {
      setDataSource([...dataSource, res.data?.getDeletedProjectDocuments?.nodes ?? []]);
      setLoading(false);
    });
  };

  // CHECK FILE TYPE (PDF - SHOW PDFTRON, DWG - SHOW AUTODESK)
  const checkType = async (id: string) => {
  };

  //Querying document ID
  useEffect(() => {
    if (router.query.documentId) {
      checkType(router.query.documentId as string);
    }
  }, [router.query.documentId]);

  const showDeleteConfirm = (id: string, title: string, type: string) => {
    confirm({
      title: 'Delete document permanently? ',
      icon: <ExclamationCircleOutlined />,
      content: (
        (!id) ?
          <p>Do you really want to delete {selectedRowKeys.length} content(s) permanently?</p>
          :
          <p>Do you really want to delete the {type} <span className="font-semibold">{title}</span> permanently?</p>
      ),
      okText: 'Yes',
      okType: 'danger',
      cancelText: 'No',
      onOk() {
        id ? onDelete(+id) : onDeleteMany()
      },
      onCancel() { }
    });
  };

  const showRestoreConfirm = (id: string, title: string, type: string, category: string) => {
    confirm({
      title: 'Restore document? ',
      icon: <ExclamationCircleOutlined />,
      content: (
        (!id) ?
          <p>Do you really want to restore {selectedRowKeys.length} content(s) back to their directory?</p>
          :
          <p>Do you really want to restore the {type} <span className="font-semibold">{title}</span> back to {category == Gql.CategoryType.BimDrawings ? "BIM Models" : "Drawings"}?</p>
      ),
      okText: 'Yes',
      okType: 'danger',
      cancelText: 'No',
      onOk() {
        id ? onRestore(+id) : onRestoreMany()
          ;
      },
      onCancel() { }
    });
  };

  const onRestore = async (id: number) => {
    try {
      await restoreDocument({
        variables: {
          id: +id
        }
      });
    } catch (e) {
      onError(e);
    }
  };

  const onRestoreMany = async () => {
    try {
      await restoreDocuments({
        variables: {
          input: {
            ids: selectedRowKeys.map(str => Number(str))
          }
        }
      });
    } catch (e) {
      onError(e);
    }
  };

  const onDelete = async (id: number) => {
    try {
      await deleteDocument({
        variables: {
          id: +id
        }
      });
    } catch (e) {
      onError(e);
    }
  };

  const onDeleteMany = async () => {
    try {
      await deleteDocuments({
        variables: {
          input: {
            ids: selectedRowKeys.map(str => Number(str))
          }
        }
      });
    } catch (e) {
      onError(e);
    }
  };

  const [restoreDocument, { loading: documentRestoring }] = Gql.useRestoreOneDocumentMutation({
    onCompleted: () => {
      message.success('Folder / Document restored successfully');
      refetch();
    },
    onError(error) {
      onError(error);
    }
  });

  const [restoreDocuments, { loading: documentsRestoring }] = Gql.useRestoreManyDocumentsMutation({
    onCompleted: () => {
      message.success('Contents restored back to origin directory');
      setSelectedRowKeys([]);
      refetch();
    },
    onError: onError as any
  });

  const [deleteDocument, { loading: documentDeleting }] = Gql.usePermanentDeleteOneDocumentMutation({
    onCompleted: () => {
      message.success('Folder / Document deleted permanently from bin');
      refetch();
    },
    onError(error) {
      onError(error);
    }
  });

  const [deleteDocuments, { loading: documentsDeleting }] = Gql.usePermanentDeleteManyDocumentsMutation({
    onCompleted: () => {
      message.success('Contents deleted permanently from bin');
      setSelectedRowKeys([]);
      refetch();
    },
    onError: onError as any
  });

  const columns = [
    {
      title: 'TITLE',
      key: 'name',
      // sorter: (a: any, b: any) => {
      //   let aNum = parseInt(a?.name.match(/^\d+/) || 0);
      //   let bNum = parseInt(b?.name.match(/^\d+/) || 0);
      //   return aNum - bNum || a?.name.localeCompare(b?.name);
      // },
      render: (data: any) => {
        const { fileUrl } = data;

        return (
          <Space className="p-1" align="start">
            {data.isDocsStored === true ? <Icon name="store-document"></Icon> : getFileIcon(data.type)}

            <div
              className="cursor-pointer"
              style={{ wordWrap: 'break-word', wordBreak: 'break-word' }}
              onClick={() => {
                if (fileUrl) {
                  if (router.query && data.fileSystemType === 'Document') {
                    setSelectedRowKeys([]);
                    router.push({
                      query: {
                        ...router.query,
                        documentId: data.id,
                        documentUrl: encodeURI(data.fileUrl).toString()
                      }
                    });
                  }
                }
                if (data?.fileSystemType === 'Folder') {
                  confirm({
                    title: 'This folder has been deleted ',
                    icon: <ExclamationCircleOutlined />,
                    content: (
                      <p>
                        To view the contents of this folder, please restore first.
                      </p>
                    ),
                    okText: 'Restore',
                    okType: 'danger',
                    cancelText: 'Dismiss',
                    onOk() {
                      showRestoreConfirm(data.id, data.name, data.type, data.category);
                    },
                    onCancel() { }
                  });
                }
              }}
            >
              <Paragraph
                className="font-medium"
                style={{ margin: 0, width: 550 }}
                ellipsis={ellipsis ? { rows: 1, expandable: false } : false}
                id="document-name"
              >
                {data?.name}
              </Paragraph>
              <Paragraph
                className="text-base"
                style={{ margin: 0, width: 550, color: '#8c8c8c' }}
                ellipsis={ellipsis ? { rows: 1, expandable: false } : false}
              >
                {data?.description}
              </Paragraph>
              <span className="flex text-sm text-gray70" style={{ fontStyle: 'italic' }}>
                deleted from {data?.category == Gql.CategoryType.BimDrawings ? "BIM Models" : "Drawings"}
              </span>
            </div>
          </Space>
        );
      }
    },

    {
      title: 'FILE SIZE',
      key: 'fileSize',
      width: 120,
      // align: 'center' as const,
      // sorter: (a, b) => (a?.fileSize || 0) - (b?.fileSize || 0),
      render: (data: any) => {
        return data.fileSystemType === Gql.FileSystemType.Document ? (
          <p> {data?.fileSize ? `${data?.fileSize} MB` : '-'}</p>
        ) : (
          '-'
        );
      }
    },
    {
      title: 'FILE TYPE',
      dataIndex: 'type',
      key: 'type',
      width: 120,
      // align: 'center' as const,
      // sorter: (a: any, b: any) => a?.type?.localeCompare(b?.type),
      render: (type: any) => {
        return <p>{type?.toUpperCase?.()}</p>;
      }
    },
    {
      title: 'ADDED BY',
      dataIndex: 'owner',
      key: 'owner',
      width: 160,
      // align: 'center' as const,
      // sorter: (a: any, b: any) => a?.owner?.name?.localeCompare(b?.owner?.name),
      render: (owner: any, index: any) => {
        return (
          <Tooltip key={index} title={owner?.name} placement="bottom">
            {owner?.avatar && (
              <Avatar
                className="align-middle"
                style={{ backgroundColor: '#69c0ff', marginLeft: '5px' }}
                src={owner?.avatar}
              />
            )}
            {!owner?.avatar && (
              <Avatar
                className="align-middle"
                style={{ backgroundColor: '#69c0ff', marginLeft: '5px' }}
                icon={<UserOutlined />}
              />
            )}
          </Tooltip>
        );
      }
    },
    {
      title: 'DELETED AT',
      dataIndex: 'deletedAt',
      key: 'deletedAt',
      width: 160,
      render: (deletedAt: any) => {
        return <p>{moment(tz(deletedAt)).format('D MMM YYYY')}</p>;
      }
    },
    {
      title: '',
      key: 'action',
      width: 40,
      render: (data: any) => {
        const items: any = [];
        items.push({ label: <p>Restore</p>, key: '1' });
        items.push({ label: <p>Delete Permanently</p>, key: '2' });
        items.push({ label: <p>Manage</p>, key: '3' });

        return (
          <ThreeDotsDropDown
            onClick={(e: any) => {
              if (e.key === '1') {
                showRestoreConfirm(data.id, data.name, data.type, data.category);
              }
              else if (e.key === '2') {
                showDeleteConfirm(data.id, data.name, data.type);
              }
              else if (e.key === '3') {
                setSelectedRowKeys([data.id]);
                setIsManage(true);
              }
            }}
            items={items}
          />
        );
      }
    }
  ];

  return (
    <div className="absolute overflow-auto h-full w-full" ref={dropzoneRef}>

      <Spin tip={'Loading...'} spinning={documentRestoring || documentsRestoring || documentDeleting || documentsDeleting || loading}>
        {selectedRowKeys.length > 0 ? (
          <div className="flex flex-nowrap items-center justify-between px-5 pt-[20px] pb-[12px]">
            <div>
              <Button
                className="h-12 bg-primary rounded-lg text-white border-gray40 mr-5"
                onClick={() => {
                  setSelectedRowKeys([]);
                  setIsManage(false);
                }}
              >
                Cancel
              </Button>

              {selectedRowKeys.length > 0
                ? `Selected ${selectedRowKeys.length} item${selectedRowKeys.length > 1 ? 's' : ''}`
                : ''}
            </div>
            <div className="flex gap-3">
              <Form
                form={searchForm}
                onValuesChange={debounce(data => {
                  // onSearch(data);
                  setFilter({ name: { like: data.keyword } });
                }, 500)}
              >
                <SearchInput
                  onPressEnter={() => {
                    // searchForm.submit();
                  }}
                  placeholder={'Search'}
                />
              </Form>
              <Button
                className="h-12 bg-yellow-500 rounded-lg text-white border-gray40"
                onClick={() => {
                  showRestoreConfirm('', '', '', '');
                }}
              >
                Restore
              </Button>
              <Button
                className="h-12 bg-red-500 rounded-lg text-white border-gray40"
                onClick={() => showDeleteConfirm('', '', '')}
              >
                Delete from Bin
              </Button>
            </div>
          </div>
        ) : (
          <div className="flex flex-nowrap items-center justify-between px-5 pt-[20px] pb-[12px]">
            <div>
              <div>
                <h3>Deleted Drawings</h3>
              </div>
              {dirId && (
                <>
                  <h3>{breadcrumbs[0]?.name}</h3>
                  <Breadcrumb>
                    <Breadcrumb.Item className="text-gray90 text-md">
                      <Link href="/recycle-bin/workspace">
                        <HomeOutlined />
                      </Link>
                    </Breadcrumb.Item>
                    {breadcrumbs.map(
                      (value, index) =>
                        index !== 0 && (
                          <Breadcrumb.Item className="text-gray90 text-md" key={value.id}>
                            <a
                              onClick={() => {
                                setSelectedRowKeys([]);
                                router.push(`/recycle-bin/workspace/${value.id}`);
                              }}
                            >
                              {value.name}
                            </a>
                          </Breadcrumb.Item>
                        )
                    )}
                  </Breadcrumb>
                </>
              )}
            </div>
            <Row>
              <Space>
                <Form
                  form={searchForm}
                  onValuesChange={debounce(data => {
                    // onSearch(data);
                    setFilter({ name: { like: data.keyword } });
                  }, 500)}
                >
                  <SearchInput
                    onPressEnter={() => {
                      // searchForm.submit();
                    }}
                    placeholder={'Search'}
                  />
                </Form>

              </Space>
            </Row>
          </div>
        )}

        {/* <DndProvider backend={HTML5Backend}> */}
        <Table
          rowSelection={{
            type: 'checkbox',
            selectedRowKeys,
            onChange: onSelectChange,
            hideSelectAll: !isManage,
            getCheckboxProps: () => ({
              style: {
                display: !isManage ? 'none' : ''
              }
            })
          }}
          ref={tableRef}
          className="dashboard-table px-5"
          dataSource={
            isArray(dataSource)
              ? dataSource?.map((item: any) => ({
                ...item,
                key: item.id
              }))
              : dataSource
          }
          columns={columns}
          size="small"
          pagination={false}
          tableLayout="auto"
          //   onRow={(_, index) => {
          //     const attr = {
          //       index,
          //       moveDoc
          //     };
          //     return attr as React.HTMLAttributes<any>;
          //   }}
          scroll={{
            scrollToFirstRowOnChange: false,
            y: window.innerHeight - 230
          }}
          loading={loading || loadingProjectDocument}
        />
        {/* </DndProvider> */}

        <PdfTronModal ref={pdfTronTabRef} documentId={documentId} documentUrl={documentUrl} onSaved={() => refetch()} />
        {/* <UploadDocModal
          dirId={dirId as any}
          ref={uploadDocModalRef}
          onSaved={() => refetch()}
          type={Gql.CategoryType.ProjectDocument}
          driveType={driveType}
          accept=".pdf,.PDF"
        /> */}
        {/* <FileUpload
          ref={uploadDocModalRef}
          dirId={dirId as any}
          onSaved={refetch}
          type={Gql.CategoryType.ProjectDocument}
          driveType={driveType}
        /> */}

        <MoveFileModal
          data={dataSource}
          dirId={dirId as any}
          ref={moveFileModal}
          onSaved={() => refetch()}
          selectedFormId={selectedFormId}
          formName={formName}
          fileType={fileType}
          category={category}
        />

        <MoveBulkCloudDocsModal
          data={dataSource}
          ids={selectedRowKeys}
          dirId={dirId as any}
          ref={moveDocumentsModal}
          onSaved={() => {
            refetch();
            setSelectedRowKeys([]);
            setIsManage(false);
          }}
          category={Gql.CategoryType.ProjectDocument}
          setSelectedRowKeys={setSelectedRowKeys}
          onLoadMore={onLoadMore}
        />
      </Spin>
    </div>
  );
};

// Status of documents
const getStatus = (type: string) => _.get(status, type) ?? '';

const status = {
  Draft: (
    <Space align="center">
      <Icon name="status-draft" />
      <p className="mb-1" id="status">
        Draft
      </p>
    </Space>
  ),
  Submitted: (
    <Space align="center">
      <Icon name="status-submitted" />
      <p className="mb-1" id="status">
        Submitted
      </p>
    </Space>
  ),
  Pending: (
    <Space align="center">
      <Icon name="status-pending" />
      <p className="mb-1" id="status">
        Pending
      </p>
    </Space>
  ),
  InReview: (
    <Space align="center">
      <Icon name="status-inReview" />
      <p className="mb-1" id="status">
        In Review
      </p>
    </Space>
  ),
  InProgress: (
    <Space align="center">
      <Icon name="status-inProgress" />
      <p className="mb-1" id="status">
        In Progress
      </p>
    </Space>
  ),
  Approved: (
    <Space align="center">
      <Icon name="status-approved" />
      <p className="mb-1" id="status">
        Approved
      </p>
    </Space>
  ),
  Rejected: (
    <Space align="center">
      <Icon name="status-rejected" />
      <p className="mb-1" id="status">
        Rejected
      </p>
    </Space>
  )
};

ProjectDocument.auth = true;
ProjectDocument.Layout = RecycleBinTabLayout;
export default ProjectDocument;
