import { DownOutlined, ExclamationCircleOutlined, RightOutlined, UnorderedListOutlined } from '@ant-design/icons';
import ThreeDotsDropDown from '@components/ThreeDotsDropDown';
import SearchInput, { SearchRef } from '@components/forms/FormsInput/SearchInput';
import useModal from '@components/hooks/useModal';
import { default as useQueries } from '@components/hooks/useQueries';
import { ProjectAccess } from '@constants/subscription';
import * as Gql from '@graphql';
import { manageFiles } from '@utils/authority';
import { onError } from '@utils/error';
import { Button, Col, Form, Input, InputRef, Menu, MenuProps, Modal, Space, Spin, Table, message } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import { ColumnsType } from 'antd/lib/table';
import Paragraph from 'antd/lib/typography/Paragraph';
import _ from 'lodash';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { AlignType } from 'rc-table/lib/interface';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { ProjectGroupApiService } from 'src/api';

const TasksOverview = () => {
  const router = useRouter();
  const { parentId } = router.query;
  const searchRef = useRef<SearchRef>(null);

  const [dataSource, setDataSource] = useState<any>([]);
  const [isLoading, setIsLoading] = useState(false);

  const projectUserRole = (localStorage.getItem('ProjectUserRole') as Gql.ProjectUserRoleType) || ''
  const preferredPageSize = localStorage.getItem('preferredPageSize')
  const [openAccordion, setOpenAccordion] = useState(false);
  const [defaultExpendedRowKeys, setDefaultExpendedRowKeys] = useState<string[]>(['Ungroup Tasks']);

  const { confirm } = Modal;

  const inputRef = useRef<any>(null);

  const [searchForm] = useForm();
  const [newGroupForm] = useForm();

  const viewOnly = projectUserRole === 'CanView';

  const { canDelete } = manageFiles(projectUserRole);
  const fixturesGroup = 'Ungroup Tasks';

  const { queries, setPaging, setFilter } = useQueries<
    Gql.ProjectGroupWithChildrenFilter,
    Gql.ProjectGroupWithChildrenSort
  >();

  // set default page size
  useEffect(() => {
    if (preferredPageSize) {
      setPaging({
        offset: queries.paging.offset,
        limit: parseInt(preferredPageSize)
      })
    }
  },[])

  const [getProjectGroups, { data, refetch, loading }] = Gql.useGetProjectGroupsLazyQuery({
    variables: {
      ...queries
    },
    fetchPolicy: 'cache-and-network',
    onError: onError
  });
  const totalCount = data?.getProjectGroups?.totalCount || 0;

  const calculateProjectGroups = useCallback(async () => {
    // get total count for ungroup tasks
    const res = await getProjectGroups();
    const projectGroups = res?.data?.getProjectGroups.nodes || [];

    const tableData =
      (projectGroups?.map?.((d: any) => {
        return {
          key: d.id,
          name: d.title,
          openCount: d?.openCount ?? 0,
          inProgressCount: d?.inProgressCount ?? 0,
          holdCount: d?.holdCount ?? 0,
          closedCount: d?.completeCount ?? 0,
          totalCount: d?.totalCount ?? 0,
          children: d?.children?.map((child: any) => {
            return { ...child, isChild: true };
          }),
          ..._.omit(d, ['children'])
        };
      }) as object[]) || {};

    setDataSource(tableData);
  }, [totalCount, data]);

  useEffect(() => {
    calculateProjectGroups();
  }, [queries.filter]);

  //filter function
  const onFilterStatus = (status: Gql.TaskStatusType, type: 'groupId' | 'projectGroupId', id: string) => {
    // pass the filter to the router query
    router.push({
      pathname: '/tasks',
      query: {
        status,
        [type]: id
      }
    });
  };

  const showDeleteConfirm = (data: any, title: string) => {
    const role = localStorage.getItem('ProjectUserRole') || '';
    confirm({
      title: 'Are you sure?',
      icon: <ExclamationCircleOutlined />,
      content: (
        <p>
          Do you really want to delete the <span className="font-semibold">{title}</span>?
        </p>
      ),
      okText: 'Yes',
      okType: 'danger',
      cancelText: 'No',
      okButtonProps: {
        disabled: role === 'CanView'
      },
      onOk() {
        deleteGroup(data);
      },
      onCancel() {}
    });
  };

  const handleOnCellClick = (status: Gql.TaskStatusType, id: string, isChild: boolean) => {
    const type = isChild ? 'groupId' : 'projectGroupId';
    onFilterStatus(status, type, id);
  };

  const columns = useMemo(
    (): ColumnsType<any> => [
      {
        title: <div className='ml-4'>GROUPS</div>,
        key: 'title',
        className: 'flex h-[62px] items-center',
        align: 'left' as AlignType,
        render: (data: any) => {
          const { title, id, tasks, name, totalCount, isChild } = data;
          const getName = name ? `${name} (${totalCount || 0})` : `${title} (${totalCount || 0})`;

          return (
            <Paragraph
              onClick={() => {
                if (getName.includes('Ungroup Tasks') || tasks) {
                  router.push({
                    pathname: '/tasks',
                    query: {
                      groupId: id
                    }
                  });
                }
              }}
              className={`flex cursor-pointer ${!isChild && 'font-medium '}`}
              style={{ color: 'black', width: '80%', margin: '0.5em', marginLeft: data?.isChild ? '3em' : '0' }}
              ellipsis={{ rows: 1, expandable: false, tooltip: getName, suffix: '' }}
            >
              {getName}
            </Paragraph>
          );
        }
      },
      {
        title: 'OPEN',
        align: 'center' as AlignType,
        key: 'openCount',
        width: 80,
        // dataIndex: 'openCount',
        onCell: (record, rowIndex) => {
          const { id, isChild } = record;
          const isUngroupTasks = record?.name?.includes('Ungroup Tasks');

          return {
            onClick: () => handleOnCellClick(Gql.TaskStatusType.Open, id, isUngroupTasks ? true : isChild)
          };
        },
        render: (record: any, index: number) => {
          const { isChild, openCount } = record;

          return <p className={`cursor-pointer ${!isChild && 'font-bold '}`} > {openCount || 0} </p>;
        }
      },
      {
        title: 'IN PROGRESS',
        align: 'center' as AlignType,
        key: 'tasks-inprogress',
        width: 120,
        // dataIndex: 'inProgressCount',
        onCell: (record, rowIndex) => {
          const { id, isChild } = record;
          const isUngroupTasks = record?.name?.includes('Ungroup Tasks');

          return {
            onClick: () => handleOnCellClick(Gql.TaskStatusType.InProgress, id, isUngroupTasks ? true : isChild)
          };
        },
        render: (record: any, index: number) => {
          const { isChild, inProgressCount } = record;

          return <p className={`cursor-pointer ${!isChild && 'font-bold '}`}> {inProgressCount || 0} </p>;
        }
      },
      {
        title: 'HOLD',
        align: 'center' as AlignType,
        key: 'tasks-hold',
        width: 100,
        // dataIndex: 'holdCount',
        onCell: (record, rowIndex) => {
          const { id, isChild } = record;
          const isUngroupTasks = record?.name?.includes('Ungroup Tasks');
          return {
            onClick: () => handleOnCellClick(Gql.TaskStatusType.Hold, id, isUngroupTasks ? true : isChild)
          };
        },
        render: (record: any, index: number) => {
          const { isChild, holdCount } = record;

          return <p className={`cursor-pointer ${!isChild && 'font-bold '}`}> {holdCount || 0} </p>;
        }
      },
      {
        title: 'CLOSED',
        align: 'center' as AlignType,
        key: 'tasks-closed',
        width: 80,
        // dataIndex: 'closedCount',
        onCell: (record, rowIndex) => {
          const { id, isChild } = record;
          const isUngroupTasks = record?.name?.includes('Ungroup Tasks');

          return {
            onClick: () => handleOnCellClick(Gql.TaskStatusType.Completed, id, isUngroupTasks ? true : isChild)
          };
        },
        render: (record: any, index: number) => {
          const { isChild, closedCount } = record;

          return <p className={`cursor-pointer ${!isChild && 'font-bold '}`}> {closedCount || 0} </p>;
        }
      },
      {
        title: '',
        key: 'action',
        width: 80,
        align: 'center' as AlignType,
        render: (data: any, index: object) => {
          const { children, taskGroupName, tasks } = data;

          const items: MenuProps['items'] = [];
          if (children !== undefined) {
            items.push({ label: <p>Add Sub Group</p>, key: '3', disabled: viewOnly }, { type: 'divider' });
          }

          items.push({ label: <p>Rename</p>, key: '1', disabled: viewOnly });

          // children only have delete and rename option
          // Parent will have rename and add sub group
          if (children === undefined && canDelete) {
            items.push({ type: 'divider' }, { label: <p>Delete Sub Group</p>, key: '2', disabled: viewOnly });
          }

          if (children?.length === 0 && canDelete) {
            items.push({ type: 'divider' }, { label: <p>Delete</p>, key: '2', disabled: viewOnly });
          }

          return Object.prototype.hasOwnProperty.call(data, 'key') ? (
            <div
              onClick={e => e.stopPropagation()}
              className={`transparent-button ${data?.name === 'Ungroup Tasks' || viewOnly ? 'invisible' : 'visible'}`}
            >
              <ThreeDotsDropDown
                onClick={(e: any) => {
                  if (e.key === '1') {
                    newGroupForm.setFieldsValue({ id: data.id, title: data.title });
                    showUpdateGroupModal();
                  }
                  if (e.key === '2') {
                    showDeleteConfirm(data.id, data.title ?? '');
                  }
                  if (e.key === '3') {
                    router.replace({
                      query: { ...router.query, parentId: data.id }
                    });
                    showAddGroupModal();
                  }
                }}
                items={items}
              />
            </div>
          ) : (
            <>
              {taskGroupName === 'Ungroup Tasks' ? null : (
                <ThreeDotsDropDown
                  onClick={(e: any) => {
                    if (e.key === '1') {
                      newGroupForm.setFieldsValue({ id: data.id, title: data.title });
                      showUpdateGroupModal();
                    }
                    if (e.key === '2') {
                      showDeleteConfirm(data.id, data.title ?? '');
                    }
                    if (e.key === '3') {
                      router.replace({
                        query: { ...router.query, parentId: data.id }
                      });
                      showAddGroupModal();
                    }
                  }}
                  items={items}
                />
              )}
            </>
          );
        }
      }
    ],
    [router]
  );

  const route = useRouter();

  // ADD GROUP MODAL
  const [modal, showAddGroupModal, closeAddGroupModal] = useModal({
    // onCancel: () => newGroupForm.resetFields(),
    title: 'Create a new group',
    content:
      (setTimeout(() => {
        inputRef?.current?.focus();
      }, 100),
      (
        <Form layout="vertical" form={newGroupForm} requiredMark={true}>
          <Form.Item label="Group name" name="title" rules={[{ required: true, message: 'Group name is required!' }]}>
            <Input ref={inputRef} />
          </Form.Item>
          <div className="flex justify-end">
            <Button
              htmlType="submit"
              type="primary"
              onClick={() => {
                parentId
                  ? createOrUpdateGroup(newGroupForm, 'add sub group')
                  : createOrUpdateGroup(newGroupForm, 'create');
              }}
            >
              Create
            </Button>
          </div>
        </Form>
      )),
    onCancel: () => {
      router.replace({
        query: _.omit(router.query, 'parentId')
      });
    }
  });

  const deleteGroup = async (value: any) => {
    await ProjectGroupApiService.delete({ body: { id: value } })
      .then((res: any) => {
        message.success('Deleted Sucessfully');
        newGroupForm.resetFields();
      })
      .catch((err: any) => {
        onError(err);
        newGroupForm.resetFields();
      });

    refetch();

    // await groups();
  };

  const createOrUpdateGroup = async (data: any, ops = '') => {
    if (isLoading) return;
    setIsLoading(true);
    const { title, id } = data.getFieldValue();

    try {
      if (ops == 'create') {
        await ProjectGroupApiService.create({ body: { title } });
      } else if (ops == 'update') {
        if (!title) {
          return message.error('Group name is required');
        }
        await ProjectGroupApiService.update({ body: { id, title } });
      } else if (ops == 'add sub group') {
        await ProjectGroupApiService.create({ body: { title, projectGroupId: Number(parentId) } });
      }
    } catch (e) {
      onError(e);
    } finally {
      newGroupForm.resetFields();
      closeAddGroupModal();
      closeUpdateGroupModal();
      // refetch();
      calculateProjectGroups();
      setIsLoading(false);
      router.replace({
        query: _.omit(router.query, 'parentId')
      });
    }
  };

  // UPDATE GROUP MODAL
  const [updateModal, showUpdateGroupModal, closeUpdateGroupModal] = useModal({
    title: 'Updating group name',
    content:
      (setTimeout(() => {
        inputRef?.current?.focus();
      }, 100),
      (
        <Form layout="vertical" form={newGroupForm} requiredMark={true}>
          <Form.Item label="Group name" name="title" rules={[{ required: true, message: 'Group name is required!' }]}>
            <Input ref={inputRef} />
          </Form.Item>
          <div className="flex justify-end">
            <Button
              htmlType="submit"
              type="primary"
              onClick={async () => {
                await createOrUpdateGroup(newGroupForm, 'update');
              }}
              disabled={viewOnly}
            >
              Update
            </Button>
          </div>
        </Form>
      ))
  });

  /** START ACL */
  const companySubscriptions = Gql.useCompanySubscriptionsQuery({
    variables: {
      sorting: {
        direction: Gql.SortDirection.Desc,
        field: Gql.CompanySubscriptionSortFields.SubscriptionEndDate
      },
      paging: {
        limit: 1
      }
    },
    onError: onError,
    nextFetchPolicy: 'cache-first'
  });
  /** END ACL */

  if (!data) return;

  const menuItems = [
    {
      key: '/tasks-overview',
      label: (
        <div key="/tasks-overview" className="font-semibold">
          <Link href="/tasks-overview"> Overview </Link>
        </div>
      )
    },
    {
      key: '/tasks',
      label: (
        <div key="/tasks" className="font-semibold">
          <Link href="/tasks"> Tasks </Link>
        </div>
      )
    }
  ];

  return (
    <div className="absolute xl:overflow-auto h-full w-full">
      {modal}
      {updateModal}
      <Menu
        mode="horizontal"
        defaultSelectedKeys={[route.pathname.replace('/[[...dir]]', '')]}
        className="pl-0"
        items={
          companySubscriptions.data?.companySubscriptions.nodes?.[0]?.subscriptionPackage?.[ProjectAccess.TASK]
            ? menuItems
            : []
        }
      />
      <div className="flex flex-nowrap items-center justify-between px-5 pt-[20px] pb-[12px]">
        <div>
          <h3>Overview</h3>
        </div>
        <Col>
          <Form
            form={searchForm}
            onValuesChange={_.debounce(async data => {
              const { keyword } = data;

              await setFilter({
                title: {
                  like: `%${keyword}%`
                }
              });

              const res = await getProjectGroups({
                variables: {
                  filter: {
                    title: {
                      like: `%${keyword}%`
                    }
                  }
                }
              });

              // get all children id
              const keys = res?.data?.getProjectGroups?.nodes?.map(obj => obj.id);

              // if didnt have data it will only send 'Ungroup Tasks'
              if (keyword) {
                setDefaultExpendedRowKeys(['Ungroup Tasks', ...(keys || []).flat()]);
                setOpenAccordion(true);
              } else {
                setDefaultExpendedRowKeys(['Ungroup Tasks']);
                setOpenAccordion(false);
              }

              searchRef?.current?.focus();

              // onSearch(data);
            }, 600)}
          >
            <Space>
              <div style={{paddingTop: '10px'}}>  
                <SearchInput ref={searchRef} onPressEnter={() => {}} placeholder={'Search'} />
              </div>

              <Button
                type="primary"
                className="rounded-lg w-[125px] h-[41px] mb-[2px]"
                onClick={() => {
                  showAddGroupModal();
                }}
                disabled={viewOnly}
              >
                Create Group
              </Button>
            </Space>
          </Form>
        </Col>
      </div>

      <div className="">
        <Spin spinning={isLoading}>
          {dataSource.length > 0 && (
            <Table
              scroll={{ y: 600 }}
              className="pt-2 px-5 dashboard-table"
              size="small"
              columns={columns}
              dataSource={dataSource}
              onRow={(record, rowIndex) => {
                return {
                  onClick: event => {
                    event.stopPropagation();

                    // if have same id it will remove it from the array
                    // it will cause the sub folder close

                    if (defaultExpendedRowKeys.includes(record.key)) {
                      setDefaultExpendedRowKeys(prev => prev.filter(key => key !== record.key));
                    } else {
                      setDefaultExpendedRowKeys(prev => [...prev, record.key]);
                    }
                  }
                };
              }}
              expandable={{
                expandedRowKeys: _.uniq(defaultExpendedRowKeys),
                expandIcon: ({ expanded, onExpand, record }) => {
                  if (fixturesGroup?.includes(record?.name)) return <UnorderedListOutlined className="mx-5" />;
                  if (record?.projectGroupId !== undefined) {
                    // Check if the record is the first child or not
                    return expanded || openAccordion ? (
                      <DownOutlined className="mx-5" />
                    ) : (
                      <RightOutlined className="mx-5" />
                    );
                  } else if (record?.children === undefined) {
                    // Check if the record does not have children or not
                    return null;
                  } else {
                    return expanded || openAccordion ? (
                      <DownOutlined
                        onClick={e => {
                          if (!expanded) {
                            setDefaultExpendedRowKeys(prev =>
                              prev ? [...prev, 'Ungroup Tasks', record?.key] : ['Ungroup Tasks', record?.key]
                            );
                            setOpenAccordion(false);
                          } else setDefaultExpendedRowKeys(prev => prev.filter(key => key !== record.id));
                        }}
                        className="mx-5"
                      />
                    ) : (
                      <RightOutlined
                        onClick={e => {
                          setDefaultExpendedRowKeys(prev =>
                            prev ? [...prev, 'Ungroup Tasks', record?.key] : ['Ungroup Tasks', record?.key]
                          );
                        }}
                        className="mx-5"
                      />
                    );
                  }
                }
              }}
              pagination={{
                showSizeChanger: true,
                pageSize: queries.paging.limit,
                current: queries.paging.offset / queries.paging.limit + 1,
                total: totalCount,
                onChange: (_page, pageSize) => {
                  if (preferredPageSize != pageSize.toString()) {
                    localStorage.setItem('preferredPageSize', pageSize.toString())
                  }
                }
              }}
              onChange={paginate => {
                const { current, pageSize } = paginate;
                if (pageSize !== undefined && current !== undefined) {
                  setPaging({
                    offset: (current - 1) * pageSize,
                    limit: pageSize
                  });
                  calculateProjectGroups();
                }
              }}
              loading={loading}
            />
          )}
        </Spin>
      </div>
    </div>
  );
};
TasksOverview.auth = true;
export default TasksOverview;
