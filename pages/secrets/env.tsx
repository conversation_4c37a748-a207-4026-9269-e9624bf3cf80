import * as Gql from '@graphql';
import { <PERSON><PERSON>, Card, Divider, List, Typography } from 'antd';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

const Env = () => {
  const router = useRouter();
  const { data: userMeData } = Gql.useGetUserMeQuery({});

  const [envs, setEnvs] = useState([
    { code: 'NODE_ENV', value: process.env.NODE_ENV },
    { code: 'APP_ENV', value: process.env.APP_ENV },
    { code: 'NEXTAUTH_URL', value: process.env.NEXTAUTH_URL },
    { code: 'NEXT_AUTH_JWT_SECRET', value: process.env.NEXT_AUTH_JWT_SECRET },
    { code: 'NEXT_AUTH_PUBLIC_API_URL', value: process.env.NEXT_AUTH_PUBLIC_API_URL },
    { code: 'API_SCHEMA_PATH', value: process.env.API_SCHEMA_PATH },
    { code: 'GRAPHQL_SCHEMA_PATH', value: process.env.GRAPHQL_SCHEMA_PATH },

    { code: 'FACEBOOK_APP_ID', value: process.env.FACEBOOK_APP_ID },
    { code: 'FACEBOOK_APP_SECRET', value: process.env.FACEBOOK_APP_SECRET },
    { code: 'GOOGLE_APP_ID', value: process.env.GOOGLE_APP_ID },
    { code: 'GOOGLE_APP_SECRET', value: process.env.GOOGLE_APP_SECRET },
    

    { code: 'NEXT_PUBLIC_APP_NAME', value: process.env.NEXT_PUBLIC_APP_NAME },
    { code: 'NEXT_PUBLIC_API_URL', value: process.env.NEXT_PUBLIC_API_URL },
    { code: 'NEXT_PUBLIC_GRAPHQL_URL', value: process.env.NEXT_PUBLIC_GRAPHQL_URL },
    { code: 'NEXT_PUBLIC_SOCKET_URL', value: process.env.NEXT_PUBLIC_SOCKET_URL },
    { code: 'NEXT_PUBLIC_NOVU_BACKEND_URL', value: process.env.NEXT_PUBLIC_NOVU_BACKEND_URL },
    { code: 'NEXT_PUBLIC_NOVU_SOCKET_URL', value: process.env.NEXT_PUBLIC_NOVU_SOCKET_URL },
    { code: 'NEXT_PUBLIC_NOVU_APP_ID', value: process.env.NEXT_PUBLIC_NOVU_APP_ID },
    { code: 'NEXT_PUBLIC_SENANGPAY_URI', value: process.env.NEXT_PUBLIC_SENANGPAY_URI },
    { code: 'NEXT_PUBLIC_PDFTRON_LICENSE_KEY', value: process.env.NEXT_PUBLIC_PDFTRON_LICENSE_KEY }
  ]);

  useEffect(() => {
    // declare the data fetching function
    const fetchData = async () => {
      const response = await fetch('/api/secrets/env');
      const jsonData = await response.json();
      const newEnvs = envs.map((item, i) => Object.assign({}, item, jsonData[i]));
      setEnvs(newEnvs);
    };

    // call the function
    fetchData()
      // make sure to catch any error
      .catch(console.error);
  }, []);

  return (
    <div className="p-5 mb-16">
      {userMeData?.getUserMe?.email &&
      (/@bina.cloud\s*$/.test(userMeData?.getUserMe?.email) ||
        userMeData?.getUserMe?.email === '<EMAIL>') ? (
        <List
          style={{ backgroundColor: '#F5F8FF', overflow: 'auto', height: '85vh' }}
          header={<div>ENV</div>}
          itemLayout="vertical"
          bordered
          dataSource={envs}
          renderItem={item => (
            <List.Item>
              <Typography.Text strong>{item.code}</Typography.Text>
              <Typography.Text style={{ display: 'block' }}>{item.value}</Typography.Text>
            </List.Item>
          )}
        ></List>
      ) : (
        <Alert message="Invalid Access. This page only viewable by user email ended with bina.cloud" type="error" />
      )}
    </div>
  );
};

Env.auth = true;
export default Env;
