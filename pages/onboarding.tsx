import OnBoardingForm from '@components/forms/OnBoardingForm';
import * as Gql from '@graphql';
import { Button, Card, Form, message, Spin } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import _ from 'lodash';
import { signIn, useSession } from 'next-auth/react';
import Image from 'next/image';
import { useRouter } from 'next/router';
import React, { useCallback, useEffect, useState } from 'react';
import { isMobile } from 'react-device-detect';
import { UserAuthService } from 'src/api';

const OnBoarding: React.FC = () => {
  const router = useRouter();
  const [form] = useForm();
  const [loading, setLoading] = useState(false);

  const { data, loading: fetching } = Gql.useGetUserMeQuery({
    fetchPolicy: 'no-cache'
  });

  const { data: session, status } = useSession();

  useEffect(() => {
    if (router.isReady && router.query.token) signUp();
  }, [router.isReady]);

  const signUp = async () => {
    setLoading(true);
    // if (isMobile) {
    //   window.location.href = `bina://onboarding?token=${router.query.token}`;
    //   return;
    // }
    const signUpToken = router.query.token as string;
    const data = await UserAuthService.emailVerification({
      body: { signUpToken }
    });
    await signIn('credentials', {
      redirect: false,
      accessToken: data.accessToken,
      refreshToken: data.refreshToken
    });
    setLoading(false);
    // router.push("/");
  };

  // SAVE ONBOARDING DATA
  const [updateUser, { loading: updatingUser }] = Gql.useUpdateUserMeMutation({
    onCompleted: () => {
      message.success('Saved Successfully');
      router.push('/');
    },
    onError: (e: any) => {
      message.error(e.message);
    }
  });

  const onFinish = (values: Gql.UpdateUserInputDto) => {
    const { name, phoneNo, position, avatar } = values;
    updateUser({
      variables: { input: { name, phoneNo, position, ...(_.isString(avatar) ? {} : { avatar }) } }
    });
  };

  // RETRIEVE INFO IF THE USER SIGN IN WITH SOCIAL MEDIA ACCOUNT
  const initialValues = {
    ...data?.getUserMe,
    companyName: data?.getUserMe?.company?.name
  };

  if (fetching || status !== 'authenticated') return null;

  return (
    <div
      className="h-screen flex flex-row justify-center items-center bg-no-repeat bg-center bg-fixed bg-cover"
      style={{ backgroundImage: 'url(/assets/onboarding-bg.png)' }}
    >
      <div className="absolute left-10 top-10">
        <div className="flex items-center">
          <Image src="/assets/new-logo.svg" width={200} height={70} />
          {/* <div className="ml-3 tracking-widest text-xl text-binaBlue">BINA</div> */}
        </div>
      </div>

      <Spin spinning={fetching || updatingUser}>
        <Card bordered={false} className=" flex justify-center w-96">
          <div className="w-80">
            <h1 className="text-2xl font-bold mb-2 leading-tight">Your 30-day free trial starts today</h1>
            <Form
              layout="vertical"
              form={form}
              onFinish={onFinish}
              initialValues={initialValues ?? {}}
              requiredMark={false}
            >
              <OnBoardingForm data={data} />

              <Button htmlType="submit" type="primary" className="rounded-lg h-10 w-full font-medium">
                Continue
              </Button>
            </Form>
          </div>
        </Card>
      </Spin>
    </div>
  );
};

export default OnBoarding;
