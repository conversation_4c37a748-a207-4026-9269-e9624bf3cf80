import { SendOutlined } from '@ant-design/icons';
import { Avatar, Button, Form, Input, Spin, message } from 'antd';
import axios from 'axios';
import { get } from 'lodash';
import moment from 'moment';
import { AppLayoutContext } from 'pages/_app';
import React, { useContext, useEffect, useRef, useState } from 'react';
const { TextArea } = Input;

type Props = {};

type Message = {
  content: string;
  role: 'user' | 'assistant';
  timestamp: string;
};

const ChatUI = (props: Props) => {
  const [form] = Form.useForm();
  const [messages, setMessages] = useState<Message[]>([]);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [currentDocumentName, setCurrentDocumentName] = useState<string | null>(null);
  const [isAIProcessing, setIsAIProcessing] = useState(false);
  const [userInput, setUserInput] = useState<string>('');
  const { userData: user } = useContext(AppLayoutContext);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const apiBaseUrl = process.env.NEXT_PUBLIC_SCHEDULE_AI_BASE_URL;
  const apiRunEndpoint = process.env.NEXT_PUBLIC_RAG_RUN_ENDPOINT;

  const suggestions = [
    'What are the equipment requirements for high-strain dynamic pile testing on driven piles as per the JKR specifications?',
    'What are the steps required for surface preparation before waterproofing by impregnation with isobutyltriethoxy silane?',
    'What precautions must be taken by the contractor when handling precast culverts to prevent damage to the bitumen or zinc coating?',
    'What are considered unsuitable materials for earthworks, and how should they be handled during excavation?​'
  ];

  const getRandomSuggestions = () => {
    const shuffled = suggestions.sort(() => 0.5 - Math.random());
    return shuffled.slice(0, 2);
  };

  const [suggestion, setSuggestion] = useState<string[]>(getRandomSuggestions());

  useEffect(() => {
    const storedSessionId = localStorage.getItem('sessionId');
    const storedMessages = localStorage.getItem('messages');
    const storedDocumentName = localStorage.getItem('documentName');
    if (storedSessionId) {
      setSessionId(storedSessionId);
    }
    if (storedMessages) {
      setMessages(JSON.parse(storedMessages));
    }
    if (storedDocumentName) {
      setCurrentDocumentName(storedDocumentName);
    }
  }, []);

  useEffect(() => {
    if (sessionId) {
      localStorage.setItem('sessionId', sessionId);
    }
    if (messages.length > 0) {
      localStorage.setItem('messages', JSON.stringify(messages));
    }
    if (currentDocumentName) {
      localStorage.setItem('documentName', currentDocumentName);
    }
  }, [sessionId, messages, currentDocumentName]);

  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  const handleSendMessage = async (values: { message: string }) => {
    setIsAIProcessing(true);
    const newMessage: Message = {
      content: values.message,
      role: 'user',
      timestamp: moment().format('HH:mm')
    };

    setMessages(prevMessages => [...prevMessages, newMessage]);
    form.resetFields();

    try {
      const initResponse = await axios.post(
        `${apiBaseUrl}${apiRunEndpoint}?stream=true&x-api-key=${process.env.NEXT_PUBLIC_RAG_API_KEY}`,
        {
          input_value: values.message,
          output_type: 'chat',
          input_type: 'chat',
          tweaks: {
            'Prompt-qLC0j': {},
            'ChatInput-sCCeG': {},
            'ChatOutput-MLN16': {},
            'OpenAIModel-U1Ray': {},
            'AstraDB-99FVL': {},
            'OpenAIEmbeddings-flMKR': {},
            'SplitText-1WeNl': {},
            'File-taAcA': {},
            'ParseData-MQul1': {},
            'Memory-fCwSn': {},
            'ParseData-4liXO': {},
            'SerpAPI-8RYcl': {}
          }
        },
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );
      

      const { stream_url, session_id } = initResponse.data.outputs[0].outputs[0].artifacts;

      if (!stream_url) {
        throw new Error('No stream URL returned');
      }

      let assistantMessageIndex: number;
      setMessages(prevMessages => {
        assistantMessageIndex = prevMessages.length;
        return [
          ...prevMessages,
          {
            content: '',
            role: 'assistant',
            timestamp: moment().format('HH:mm')
          }
        ];
      });

      const streamSource = new EventSource(`${apiBaseUrl}${stream_url}?session_id=${session_id}&x-api-key=${process.env.NEXT_PUBLIC_RAG_API_KEY}`);
      let accumulatedContent = '';

      streamSource.onmessage = event => {
        const data = JSON.parse(event.data);

        if (data && data.chunk) {
          accumulatedContent += data.chunk;

          setMessages(prevMessages => {
            const updatedMessages = [...prevMessages];
            updatedMessages[assistantMessageIndex] = {
              ...updatedMessages[assistantMessageIndex],
              content: accumulatedContent.trim(),
              role: 'assistant'
            };
            return updatedMessages;
          });
        }
      };

      streamSource.onerror = () => {
        streamSource.close();
        setIsAIProcessing(false);
      };
    } catch (error) {
      message.error('Something went wrong...');
      console.error(error);
    } finally {
      setIsAIProcessing(false);
    }
  };

  const handleSuggestionClick = async (suggestion: string) => {
    return await handleSendMessage({ message: suggestion });
  };

  const ChatMessage = ({ message }: { message: Message }) => (
    <div
      className={`flex gap-1 my-2 mx-5 justify-start px-4 py-2 rounded-xl ${
        message.role === 'assistant' && 'bg-white shadow-md'
      }`}
    >
      <Avatar
        src={message.role === 'assistant' ? '/assets/logo.png' : user?.getUserMe?.avatar}
        className="w-12 h-12"
        alt="assistant"
        shape="square"
      />
      <div
        className={`p-3 rounded-lg ${message.role === 'user' ? ' text-black' : 'bg-gray-200 text-black'} w-full mx-5`}
      >
        <p>
          <span className="font-bold">{message.role === 'user' ? 'You' : 'Bina Cloud AI'}</span>
          <span className="text-[11px] text-slate-400"> • {message.timestamp}</span>
        </p>
        {message.role !== 'user' ? (
          <div className="text-black mt-3" dangerouslySetInnerHTML={{ __html: message.content }} />
        ) : (
          <p>{message.content}</p>
        )}
      </div>
    </div>
  );

  return (
    <div className="flex flex-col h-full max-w-4xl m-auto p-4">
      <div className="flex-1 overflow-auto mb-4">
        {messages.map((message, index) => (
          <div key={`${message.role}-${index}`} className="my-5">
            <ChatMessage key={index} message={message} />
          </div>
        ))}
        <div ref={messagesEndRef} />
        {isAIProcessing && (
          <div className="flex gap-1 my-2 mx-5 justify-start px-4 py-2 rounded-xl bg-white shadow-md">
            <Avatar src="/assets/logo.png" className="w-12 h-12" alt="assistant" shape="square" />
            <div className="p-3 rounded-lg bg-gray-200 text-black w-full mx-5">
              <p>
                <span className="font-bold">Bina AI</span>
                <span className="text-[11px] text-slate-400"> • {moment().format('HH:mm')}</span>
              </p>
              <p>Processing...</p>
            </div>
          </div>
        )}
      </div>
      {messages.length === 0 && (
        <div className="w-full flex gap-2 mb-5 px-3">
          {suggestion.map((suggestion, index) => (
            <div
              key={index}
              className="p-3 w-[50%] h-24 cursor-pointer rounded-xl border-1 border-gray500 border-solid hover:border-[#1EA8E0]"
              onClick={() => handleSuggestionClick(suggestion)}
            >
              {suggestion}
            </div>
          ))}
        </div>
      )}

      <Form layout="vertical" form={form} onFinish={handleSendMessage} requiredMark={false} className="w-full">
        <div className="w-full flex justify-center gap-3 px-4">
          <Form.Item name="message" className="w-full">
            <div style={{ position: 'relative', display: 'flex', alignItems: 'center' }}>
              <TextArea
                placeholder="Send a message..."
                autoSize={{ minRows: 4, maxRows: 4 }}
                className="rounded-2xl pr-[40px] pt-2"
                onChange={e => setUserInput(e.target.value)}
                onPressEnter={e => {
                  e.preventDefault();
                  form.submit();
                }}
              />
              <Button
                icon={<SendOutlined />}
                type="primary"
                shape="circle"
                htmlType="submit"
                className="absolute right-3 bottom-3 w-5 h-10"
                disabled={!userInput || isAIProcessing}
              />
            </div>
          </Form.Item>
        </div>
        {currentDocumentName && (
          <p className="text-slate-400">
            Active document: <i>{currentDocumentName}</i>
          </p>
        )}
      </Form>
    </div>
  );
};

ChatUI.auth = true;
export default ChatUI;
