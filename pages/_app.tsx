import AppLayout from '@components/AppLayout';
import HeaderProvider from '@components/HeaderProvider';
import RefreshTokenHandler from '@components/RefreshTokenHandler';
// import "@styles/variables.less";
import { ApolloProvider } from '@apollo/client';
import ErrorBoundary from '@components/ErrorBoundary';
import { AUTH_SESSION_KEY, USER_SESSION_KEY } from '@constants/auth';
import * as Gql from '@graphql';
import { GoogleAnalytics } from '@next/third-parties/google';
import '@styles/global.css';
import '@styles/invoice.css';
import { getSessionStorage, setSessionStorage } from '@utils/sessionStorage';
import { ConfigProvider, message, Space } from 'antd';
import 'antd/dist/reset.css';
import { getSession, SessionProvider } from 'next-auth/react';
import type { AppProps } from 'next/app';
import Router, { useRouter } from 'next/router';
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';
import React, { useCallback, useContext, useEffect, useState } from 'react';
import apolloClient from 'src/lib/apollo';
import 'src/utils/axios';

require('@styles/global.less');

interface Props extends AppProps {
  auth: true;
  Component: any;
}

type AppLayoutContextType = {
  headerBanner: JSX.Element;
  setHeaderBanner: (value: JSX.Element) => void;
  graceBanner: JSX.Element;
  setGraceBanner: (value: JSX.Element) => void;

  userData: Gql.GetUserMeQuery | null;
  isFetchingUser: boolean;
  setIsFetchingUser: (value: boolean) => void;
  setUserData: (value: Gql.GetUserMeQuery | null) => void;

  userRole: Gql.ProjectUserRoleType | null;
  setUserRole: (value: Gql.ProjectUserRoleType | null) => void;
};

export const AppLayoutContext = React.createContext<AppLayoutContextType>({
  headerBanner: <></>,
  setHeaderBanner: (value: JSX.Element) => { },
  graceBanner: <></>,
  setGraceBanner: (value: JSX.Element) => { },

  userData: null,

  isFetchingUser: false,
  setIsFetchingUser: (value: boolean) => { },

  setUserData: (value: Gql.GetUserMeQuery | null) => { },

  userRole: null,
  setUserRole: (value: Gql.ProjectUserRoleType | null) => { }
});

const MyApp: React.FC<Props> = ({ Component, pageProps }) => {
  const [interval, setInterval] = useState(5);
  const Layout = Component.Layout || EmptyLayout;

  const [headerBanner, setHeaderBanner] = useState(<></>);
  const [graceBanner, setGraceBanner] = useState(<></>);

  const [userData, setUserData] = useState<Gql.GetUserMeQuery | null>(null);
  const [isFetchingUser, setIsFetchingUser] = useState(false);

  const [userRole, setUserRole] = useState<Gql.ProjectUserRoleType | null>(null);

  const [isMobile, setIsMobile] = useState(false);
  const router = useRouter();


  if (!getSessionStorage(AUTH_SESSION_KEY))
    getSession().then(session => {
      if (session) setSessionStorage(AUTH_SESSION_KEY, JSON.stringify(session));
    });

  NProgress.configure({ showSpinner: false, parent: '#__next' });
  Router.events.on('routeChangeStart', url => {
    NProgress.start();
  });

  Router.events.on('routeChangeComplete', url => {
    NProgress.done(false);
    // setHeaderBanner(<></>);
    // setGraceBanner(<></>);
  });

  Router.events.on('routeChangeError', url => {
    NProgress.done(false);
  });

  useEffect(() => {
    if (typeof window !== 'undefined') {
      // check if the routes is the onboarding then return;
      if (
        router.pathname.includes('/invitation') ||
        router.pathname.includes('/onboarding') ||
        router.pathname.includes('/forgot-password') ||
        router.pathname.includes('/reset-password-successful') ||
        router.pathname.includes('/reset-password') ||
        router.pathname.includes('/unsubscribe-weekly-email') ||
        router.pathname.includes('/company') ||
        router.pathname.includes('/signature') ||
        router.pathname.includes('/BIM-mobile-view')

      )
        return;

      if (window.innerWidth < 900 && !isMobile) {
        setIsMobile(true);
        message.info('You may only access this site with desktop, laptop or tablet browsers.', 30);
      }
    }
  }, [router.pathname]);

  return (
    //@ts-ignore
    <SessionProvider session={pageProps.session} refetchInterval={interval}>
      <GoogleAnalytics gaId="G-KDKSKMT80Z" />
      {!isMobile && (
        <ApolloProvider client={apolloClient}>
          <AppLayoutContext.Provider
            value={{
              headerBanner,
              setHeaderBanner,
              graceBanner,
              setGraceBanner,
              userData,
              isFetchingUser,
              setIsFetchingUser,
              setUserData,
              userRole,
              setUserRole
            }}
          >
            <ConfigProvider
              theme={{
                token: {
                  // Seed Token
                  colorPrimary: '#058dd8',
                  colorPrimaryBgHover: '#29aae6',
                },
              }}
            >
              <Space direction="vertical" style={{ width: '100%' }}>
                {headerBanner}
                {graceBanner}
              </Space>
              {
                //@ts-ignore
                Component?.auth ? (
                  <HeaderProvider>
                    <AppLayout>
                      <Layout className="navbar">
                        <ErrorBoundary>
                          <Component {...pageProps} />
                        </ErrorBoundary>
                      </Layout>
                      <RefreshTokenHandler setInterval={setInterval} />
                    </AppLayout>
                  </HeaderProvider>
                ) : (
                  <ErrorBoundary>
                    <Component {...pageProps} />
                  </ErrorBoundary>
                )
              }
            </ConfigProvider>
          </AppLayoutContext.Provider>
        </ApolloProvider>
      )}
    </SessionProvider>
  );
};

const EmptyLayout = ({ children }: { children: React.ReactNode }) => {
  const [getUser] = Gql.useGetUserMeLazyQuery();

  const fetchUser = useCallback(async () => {
    setSessionStorage(USER_SESSION_KEY, JSON.stringify((await getUser())?.data?.getUserMe));
  }, [getUser]);

  useEffect(() => {
    if (!getSessionStorage(USER_SESSION_KEY)) fetchUser();
  }, [fetchUser]);

  return <>{children}</>;
};

export default MyApp;
