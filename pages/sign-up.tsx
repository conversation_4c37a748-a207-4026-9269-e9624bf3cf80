import { Icon } from '@commons';
import { onError } from '@utils/error';
import { Button, Card, Checkbox, Form, Input, message } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import _ from 'lodash';
import { signIn, useSession } from 'next-auth/react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import React, { useRef, useState } from 'react';
import { UserAuthService } from 'src/api';
import SocialMediaButton from '../src/components/Buttons/SocialMediaButton';
import AuthenticationLayout from '../src/components/Layout/AuthenticationLayout';

const SignUp: React.FC = () => {
  type FormData = { email: string; password: string };
  const [form] = useForm();
  const router = useRouter();
  const [isSubmitting, setSubmitting] = useState(false);
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const { status } = useSession();
  const inputRef = useRef<any>(null);

  const onFinish = async (formData: FormData) => {
    setSubmitting(true);
    const email = formData.email;
    const password = formData.password;

    UserAuthService.signUp({ body: { email, password } })
      .then(async () => {
        setSuccess(true);
        message.success('Sign up successfully, Check your email to verify the account.');
        router.push('/sign-up-successfully');
      })
      .catch(e => {
        onError(e);
        setLoading(false);
        setSubmitting(false);
        form.resetFields();
      });
  };

  // check if user sign up with social media account
  if (status === 'authenticated') {
    router.push('/');
  }

  return (
    setTimeout(() => {
      inputRef?.current?.focus();
    }, 100),
    (
      <AuthenticationLayout>
        <div className="flex-auto space-y-4 p-0">
          <div className="flex justify-center">
            <Card className="flex w-96 justify-center drop-shadow-lg rounded-lg" style={{ borderRadius: '16px' }}>
              <h1 className="text-2xl font-bold mb-4">Sign Up</h1>

              <Form layout="vertical" form={form} onFinish={onFinish} requiredMark={false}>
                <Form.Item
                  className="text-gray90"
                  label="Email address"
                  name="email"
                  rules={[{ required: true, message: 'Email is required' }]}
                >
                  <Input className="rounded-lg h-10 w-72" ref={inputRef} />
                </Form.Item>

                <Form.Item
                  className="text-gray90"
                  label="Password"
                  name="password"
                  rules={[
                    {
                      required: true,
                      message: 'Password is required'
                    },
                    {
                      min: 8,
                      message: 'Minimum 8 characters long'
                    },
                    {
                      pattern: /^(?=.*[a-z])/,
                      message: 'One lowercase letter [a-z]'
                    },
                    {
                      pattern: /^(?=.*[A-Z])/,
                      message: 'One uppercase letter [A-Z]'
                    },
                    {
                      pattern: /^(?=.*[0-9])/,
                      message: 'One number [0-9]'
                    }
                  ]}
                >
                  <Input.Password
                    className="rounded-lg h-10 w-72"
                    iconRender={(visible) => (
                      <Button type="link" className="text-gray90">
                        <Icon name={visible ? 'eye-visible' : 'eye-invisible'} />
                      </Button>
                    )}
                  />
                </Form.Item>

                <Form.Item
                  name="term"
                  valuePropName="checked"
                  rules={[
                    {
                      validator: (_, value) =>
                        value ? Promise.resolve() : Promise.reject(new Error('Please agree the terms'))
                    }
                  ]}
                >
                  <Checkbox style={{ color: 'rgba(88, 87, 87, 1)' }}>
                    I agree to the{' '}
                    <a href="https://bina.cloud/terms-and-conditions" target="_blank" rel="noreferrer">
                      terms & conditions
                    </a>
                    .
                  </Checkbox>
                </Form.Item>

                <Button
                  htmlType="submit"
                  type="primary"
                  className="rounded-lg h-10 w-72 font-medium"
                  loading={isSubmitting}
                >
                  Continue
                </Button>
              </Form>

              <p className="text-gray90 text-center mt-2">
                <span>
                  Already have an account? {''}
                  <Link href="/login">Login</Link>
                </span>
              </p>
              {/* <p className="text-gray90 text-center mt-2">
                <span>or continue with</span>
              </p>
              <div className="flex justify-center gap-4 mt-3">
                <SocialMediaButton icon={<Icon name="google" />} onClick={() => signIn('google')} />
                <SocialMediaButton icon={<Icon name="facebook" />} onClick={() => signIn('facebook')} />
                <SocialMediaButton icon={<Icon name="apple" />} />
              </div> */}
            </Card>
          </div>
        </div>
      </AuthenticationLayout>
    )
  );
};

export default SignUp;
