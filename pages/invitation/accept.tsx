import { Icon } from '@commons';
import useQueries from '@components/hooks/useQueries';
import * as Gql from '@graphql';
import { onError } from '@utils/error';
import { Button, Card, Checkbox, Form, Input, message, Spin } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import { signIn } from 'next-auth/react';
import Image from 'next/image';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';
import { ProjectInvitationApiService } from 'src/api';

const Invitation: React.FC = () => {
  const router = useRouter();
  const [form] = useForm();
  const [inviterAndTitle, setInviterAndTitle] = useState<any>();
  const [getInvitationRef, setGetInvitationRef] = useState<any>();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    setGetInvitationRef(localStorage.getItem('InvitationRef') ?? '');
  }, []);

  const invitation = async () => {
    setLoading(true);
    const getInvitation = await ProjectInvitationApiService.inviterAndTitle({
      body: { invitationRef: getInvitationRef }
    });
    setInviterAndTitle(getInvitation);
    setLoading(false);
  };

  useEffect(() => {
    if (getInvitationRef && !inviterAndTitle) {
      invitation();
    }
  }, [getInvitationRef]);

  const [updateMyPassword, { loading: passwordUpdating }] = Gql.useUpdateMyPasswordMutation({
    onCompleted: () => {
      message.success('Saved Successfully');
      router.push('/invitation/profile');
    },
    onError: onError as any
  });

  const onFinish = (values: Gql.UpdateUserPasswordDto) => {
    updateMyPassword({
      variables: { input: { password: values.password } }
    });
  };

  if (!getInvitationRef) return null;

  return (
    <div
      className="h-screen flex flex-row justify-center items-center bg-no-repeat bg-center bg-fixed bg-cover"
      style={{ backgroundImage: 'url(/assets/onboarding-bg.png)' }}
    >
      <div className="absolute left-10 top-10 sm:left-[20%]">
        <div className="flex items-center">
          <Image src="/assets/new-logo.svg" width={200} height={70} />
          {/* <div className="ml-3 tracking-widest text-xl text-binaBlue">BINA</div> */}
        </div>
      </div>

      <Spin spinning={loading || passwordUpdating}>
        <Card
          bordered={false}
          className="flex justify-center sm:w-[340px] w-[477px]"
          style={{ padding: '24px ! important' }}
        >
          <h1 className="sm:text-[20px] text-2xl font-bold mb-6 leading-tight mb-20">Create Your Password</h1>
          {/* <h4 className="mb-6">
            <span className="font-bold">
              {`${inviterAndTitle?.inviterName} `}{" "}
            </span>
            invited you to join the project
            <span className="font-bold">{` "${inviterAndTitle?.projectTitle ?? '...'}"`}</span>
          </h4> */}
          <Form layout="vertical" form={form} onFinish={onFinish} requiredMark={false}>
            <Form.Item
              className="text-gray90"
              label=""
              name="password"
              rules={[
                {
                  required: true,
                  message: 'Password is required'
                },
                {
                  min: 8,
                  message: 'Minimum 8 characters long'
                },
                {
                  pattern: /^(?=.*[a-z])/,
                  message: 'One lowercase letter [a-z]'
                },
                {
                  pattern: /^(?=.*[A-Z])/,
                  message: 'One uppercase letter [A-Z]'
                },
                {
                  pattern: /^(?=.*[0-9])/,
                  message: 'One number [0-9]'
                }
              ]}
            >
              <Input.Password
                className="rounded-lg h-10"
                iconRender={(visible) => (
                  <Button type="link" className="text-gray90">
                    <Icon name={visible ? 'eye-visible' : 'eye-invisible'} />
                  </Button>
                )}
              />
            </Form.Item>
            {/* <span className="text-sm italic">You can change password in profile Settings after logging in.</span> */}

            <Form.Item
              name="term"
              className="flex justify-center"
              valuePropName="checked"
              rules={[
                {
                  validator: (_, value) =>
                    value ? Promise.resolve() : Promise.reject(new Error('Please agree the terms'))
                }
              ]}
            >
              <Checkbox style={{ color: 'rgba(88, 87, 87, 1)' }}>
                I agree to the{' '}
                <a href="https://bina.cloud/terms-and-conditions" target="_blank" rel="noreferrer">
                  terms & conditions
                </a>
                .
              </Checkbox>
            </Form.Item>

            <Button htmlType="submit" type="primary" className="rounded-lg h-10 w-full font-medium">
              Continue
            </Button>
          </Form>
        </Card>
      </Spin>
    </div>
  );
};

export default Invitation;
