import { ApolloError } from '@apollo/client';
import ProfileForm from '@components/invitation/ProfileForm';
import * as Gql from '@graphql';
import { onError } from '@utils/error';
import { Button, Card, Form, message, Spin } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import Image from 'next/image';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';
import { ProjectInvitationApiService } from 'src/api';

const Profile: React.FC = () => {
  const router = useRouter();
  const [form] = useForm();

  const [getInvitationRef, setGetInvitationRef] = useState<any>();

  useEffect(() => {
    setGetInvitationRef(localStorage.getItem('InvitationRef') ?? '');
  }, []);

  const [updateUser, { loading: updateLoading }] = Gql.useUpdateUserMeMutation({
    onCompleted: async (res) => {

      const getInvitation = await ProjectInvitationApiService.inviterAndTitle({
        body: { invitationRef: getInvitationRef }
      });
              
      await createEmailContact({ variables: { input: Number(getInvitation?.projectId) } });
      message.success('Saved Successfully');
      localStorage.removeItem('InvitationRef');
      router.push('/');
    },
    onError: onError as any
  });


  // Create email mutation
  const [createEmailContact, { loading: creating }] = Gql.useInitContactEmailOnLoginMutation({
    onError: (error: ApolloError) => {
      onError(error);
    }
  });

  const onFinish = async (values: Gql.UpdateUserInputDto) => {
    const { name, phoneNo, position, avatar, companyOrigin } = values;

    await updateUser({
      variables: { input: { name, phoneNo, position, avatar, companyOrigin } }
    });


  };

  if (!getInvitationRef) return null;

  return (
    <div
      className="h-screen flex flex-row justify-center items-center bg-no-repeat bg-center bg-fixed bg-cover"
      style={{ backgroundImage: 'url(/assets/onboarding-bg.png)' }}
    >
      <div className="absolute left-10 top-10">
        <div className="flex items-center">
          <Image src="/assets/new-logo.svg" width={200} height={80} />
          {/* <div className="ml-3 tracking-widest text-xl text-binaBlue">BINA</div> */}
        </div>
      </div>

      <Spin spinning={updateLoading}>
        <Card bordered={false} className=" flex justify-center w-96">
          <div className="w-80">
            <h1 className="text-2xl font-bold mb-4 leading-tight">Set up your profile</h1>
            <h4 className="mb-6 text-gray90">Almost done! Please enter your details below</h4>
            <Form layout="vertical" form={form} onFinish={onFinish} requiredMark={false}>
              <ProfileForm />

              <Button htmlType="submit" type="primary" className="rounded-lg h-10 w-full font-medium">
                Continue
              </Button>
            </Form>
          </div>
        </Card>
      </Spin>
    </div>
  );
};

export default Profile;
