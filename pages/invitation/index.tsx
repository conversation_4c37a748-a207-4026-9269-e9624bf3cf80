import { ApolloError } from '@apollo/client';
import { onError } from '@utils/error';
import { Button, Card, Form, Spin } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import { signIn } from 'next-auth/react';
import Image from 'next/image';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';
import { ContactEmailApiService, ProjectInvitationApiService, UserAuthService } from 'src/api';
import * as Gql from 'src/graphql';

const Acceptation = () => {
  const [form] = useForm();
  const router = useRouter();
  const [inviterAndTitle, setInviterAndTitle] = useState<any>();
  const [loading, setLoading] = useState(false);

  const invitationRef = router.query.invitation_ref as string;
  const setInvitationRef = async () => {
    const localInvitation = await invitationRef;
    localStorage.setItem('InvitationRef', localInvitation);
  };

  const invitation = async () => {
    try {
      setLoading(true);
      const getInvitation = await ProjectInvitationApiService.inviterAndTitle({
        body: { invitationRef: invitationRef }
      });

      setInviterAndTitle(getInvitation);
      setLoading(false);
    } catch (e: any) {
      setLoading(false);
      onError(e);
    }
  };

  useEffect(() => {
    if (invitationRef && !inviterAndTitle) {
      invitation();
    }
  }, [invitationRef]);


  const onFinish = async () => {
    setLoading(true);

    try {
      const data = await ProjectInvitationApiService.invitation({
        params: { invitation_ref: router.query.invitation_ref }
      });

      if (!data) throw Error('Invalid Token');

      await signIn('credentials', {
        redirect: false,
        accessToken: data.accessToken,
        refreshToken: data.refreshToken
      });      

      const isFirstTimeLogin = await UserAuthService.isFirstTimeSignIn({});
      setLoading(false);
      if (isFirstTimeLogin) {
        router.push('/invitation/accept');
      } else {

        await ContactEmailApiService.createContactEmail({ body: {
          projectId: inviterAndTitle.projectId,
        }})

        localStorage.removeItem('InvitationRef');
        router.push('/');
      }
    } catch (e) {
      onError(e);
    } finally {
      setLoading(false);
    }
  };

  if (!invitationRef) return;


  return (
    <div
      className="h-screen flex flex-row justify-center items-center bg-no-repeat bg-center bg-fixed bg-cover"
      style={{ backgroundImage: 'url(/assets/onboarding-bg.png)' }}
    >
      <div className="absolute left-10 top-10">
        <div className="flex items-center">
          <Image src="/assets/new-logo.svg" width={200} height={70} alt='logo' loading='lazy' />
          {/* <div className="ml-3 tracking-widest text-xl text-binaBlue">BINA</div> */}
        </div>
      </div>

      <Spin spinning={loading}>
        <Card bordered={false} className=" flex justify-center sm:w-[350px]">
          <div className="w-80 text-center">
            <h1 className="text-2xl font-bold mb-4 leading-tight">ACCEPT INVITATION</h1>

            <h3 className="font-bold">Welcome to the team!</h3>
            <h4 className="mb-6 text-gray90 text-start mt-5 mx-5">
              We're excited to have you on board for the
              <span className="font-bold">{` "${inviterAndTitle?.projectTitle ?? '...'}" `}</span>
              project. Please accept the invitation to get started.
            </h4>
            <Form layout="vertical" form={form} onFinish={onFinish} requiredMark={false}>
              <div className="flex justify-between">
                <Button htmlType="submit" className="rounded-lg h-10 w-[80px] font-medium" disabled={!inviterAndTitle}>
                  Decline
                </Button>
                <Button
                  htmlType="submit"
                  type="primary"
                  className="rounded-lg h-10 w-[80px] font-medium"
                  onClick={async () => {
                    await setInvitationRef();
                  }}
                  disabled={!inviterAndTitle}
                >
                  Accept
                </Button>
              </div>
            </Form>
          </div>
        </Card>
      </Spin>
    </div>
  );
};

export default Acceptation;
