import { ProjectAccess } from '@constants/subscription';
import * as Gql from '@graphql';
import { isAllowed } from '@lib/helper';
import { onError } from '@utils/error';
import { useRouter } from 'next/router';

const CloudDocs = () => {
  const route = useRouter();

  /** START ACL */
  const companySubscriptions = Gql.useCompanySubscriptionsQuery({
    variables: {
      sorting: {
        direction: Gql.SortDirection.Desc,
        field: Gql.CompanySubscriptionSortFields.SubscriptionEndDate
      },
      paging: {
        limit: 1
      }
    },
    onError: onError,
    nextFetchPolicy: 'cache-first'
  });
  /** END ACL */

  if (isAllowed(companySubscriptions, ProjectAccess.PROJECT_DOCUMENT)) route.push('/cloud-docs/project-document');
  else if (isAllowed(companySubscriptions, ProjectAccess.WORK_PROGRAMME)) route.push('/cloud-docs/work-programme');
  else if (isAllowed(companySubscriptions, ProjectAccess.CORRESPONDENCE)) route.push('/cloud-docs/correspondence');

  return null;
};

CloudDocs.auth = true;
export default CloudDocs;
