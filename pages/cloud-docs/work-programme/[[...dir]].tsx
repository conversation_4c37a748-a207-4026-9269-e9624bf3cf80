import { ExclamationCircleOutlined, HomeOutlined, UsergroupAddOutlined } from '@ant-design/icons';
import { Icon } from '@commons';
import CloudDocsTabLayout from '@components/cloud-docs/CloudDocsTabLayout';
import { useShareModal } from '@components/cloud-docs/ShareModal';
import SyncModal from '@components/cloud-docs/SyncModal';
import CloudDocUpload from '@components/CloudDocUpload';
import SearchInput from '@components/forms/FormsInput/SearchInputs/WorkProgrammeSearch';
import useModal from '@components/hooks/useModal';
import useQueries from '@components/hooks/useQueries';
import ManageModal from '@components/ManageModal';
import MoveBulkCloudDocsModal from '@components/MoveBulkCloudDocsModal';
import MoveFileModal from '@components/MoveCloudDocsModal';
import PdfTronModal from '@components/pdf-tron/PdfTronModal';
import ThreeDotsDropDown from '@components/ThreeDotsDropDown';
import <PERSON>Viewer, { DocViewerRenderers } from '@cyntler/react-doc-viewer';
import '@cyntler/react-doc-viewer/dist/index.css';
import * as Gql from '@graphql';
import apolloClient from '@lib/apollo';
import { handleRowClick, nameAlias } from '@utils/app.utils';
import { manageFiles } from '@utils/authority';
import { onError } from '@utils/error';
import { getFileName } from '@utils/filename';
import { tz } from '@utils/timezone';
import {
  Avatar,
  Breadcrumb,
  Button,
  Dropdown,
  Form,
  Input,
  MenuProps,
  message,
  Modal,
  Row,
  Space,
  Spin,
  Table,
  Tooltip
} from 'antd';
import { useForm } from 'antd/lib/form/Form';
import { ColumnsType } from 'antd/lib/table';
import Paragraph from 'antd/lib/typography/Paragraph';
import axios from 'axios';
import FileSaver from 'file-saver';
import _, { debounce, isArray } from 'lodash';
import moment from 'moment';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useCallback, useEffect, useRef, useState } from 'react';
import { ProjectDocumentApiService } from 'src/api';
import { getFileIcon } from 'src/commons/FileIcon';
import UserAvatar from 'src/components/UserAvatar';
import useDropzone from 'src/hooks/useDropzone';

const WorkProgramme = () => {
  const router = useRouter();
  const [form] = useForm();
  const pdfTronModalRef = useRef<any>(null);
  const { confirm } = Modal;
  const documentId = router.query.documentId as string;
  const [selectedFormId, setSelectedFormId] = useState<string>();
  const {
    shareModal,
    showShareModal,
    setShareModalTitle,
    setProjectDocumentId,
    setShareFileOwner,
    listOfUsersWithAccess
  } = useShareModal({ viewOnly: false });
  const [dataSource, setDataSource] = useState<any>([]);
  const uploadDocModalRef = useRef<any>(null);
  const [breadcrumbs, setBreadcrumbs] = useState<
    Gql.GetProjectDocumentsBreadcrumbQuery['getProjectDocumentsBreadcrumb']
  >([]);
  const dirId = router.query.dir && router.query.dir[0];
  const [getBreadcrumbs, { data: breadcrumbData }] = Gql.useGetProjectDocumentsBreadcrumbLazyQuery();
  const [fileType, setFileType] = useState<string>('');
  const [formName, setFormName] = useState<string>('');
  const [category, setCategory] = useState<string>('');
  const [fileUrl, setFileUrl] = useState<any>([]);
  const moveFileModal = useRef<any>(null);
  const inputRef = useRef<any>(null);
  const moveDocumentsModal = useRef<any>(null);
  const [loading, setLoading] = useState(false);
  const [hoveredRowIndex, setHoveredRowIndex] = useState(null);

  const [searchForm] = useForm();

  // For multiple selection
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  const dropzoneRef = useRef<any>(null);
  useDropzone({ dropzoneRef, pushModal: uploadDocModalRef?.current?.openModal });

  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  useEffect(() => {
    if (!dirId) {
      setBreadcrumbs([]);
      return;
    }

    getBreadcrumbs({
      variables: { input: { id: parseInt(dirId as string) } }
    }).then(res => {
      setBreadcrumbs(res.data?.getProjectDocumentsBreadcrumb ?? []);
    });
  }, [dirId, getBreadcrumbs, breadcrumbData]);

  const tableRef = useRef<any>(null);

  const [projectUserRole, setProjectUserRole] = useState<string>('');

  useEffect(() => {
    setProjectUserRole(localStorage.getItem('ProjectUserRole') || '');
  }, []);

  useEffect(() => {
    if (documentId) {
      pdfTronModalRef.current.openModal();
    }
  }, [documentId]);

  const viewOnly = projectUserRole === 'CanView';
  const { canCreate, canDelete, canMove } = manageFiles(projectUserRole, dirId ?? '');

  const handleTableScroll = (e: { target: any }) => {
    const { target } = e;
    if (target.scrollHeight - target.scrollTop <= target.clientHeight + 1) {
      onLoadMore();
    }
  };

  // check file type and show modal
  const checkType = async (id: string) => {
    const res = await apolloClient.query<Gql.ProjectDocumentQuery, Gql.ProjectDocumentQueryVariables>({
      query: Gql.ProjectDocumentDocument,
      variables: { id }
    });
  };

  useEffect(() => {
    if (router.query.documentId) {
      checkType(router.query.documentId as string);
    }
  }, [router.query.documentId]);

  const { queries, setFilter, setPaging } = useQueries<Gql.ProjectDocumentFilter, Gql.ProjectDocumentSort>({
    usePagingParam: false,
    paging: { offset: 0, limit: 20 }
  });

  const {
    data,
    refetch,
    loading: loadingProjectDocument
  } = Gql.useGetProjectDocumentsQuery({
    variables: {
      ...queries,
      filter: {
        ...queries.filter,
        ...{
          projectDocumentId: {
            ...(dirId ? { eq: dirId?.toString() } : { eq: null })
          },
          category: { eq: Gql.CategoryType.WorkProgramme }
        }
      }
    },
    onError: onError,
    fetchPolicy: 'cache-and-network'
  });

  useEffect(() => {
    setDataSource(data?.getProjectDocuments?.nodes ?? []);
  }, [data]);

  // refetch the data when dirId is null
  useEffect(() => {
    if (!dirId) {
      refetch();
    }
  }, [dirId]);

  // update dataSource state onLoadMore function is fire
  useEffect(() => {
    setDataSource(data?.getProjectDocuments?.nodes ?? []);

    if (tableRef.current) {
      const tableBody = tableRef.current.querySelector('.ant-table-body');
      tableBody.addEventListener('scroll', handleTableScroll);
    }
    return () => {
      if (tableRef.current) {
        const tableBody = tableRef.current.querySelector('.ant-table-body');
        tableBody.removeEventListener('scroll', handleTableScroll);
      }
    };
  }, [handleTableScroll]);

  const onLoadMore = async () => {
    if (!data?.getProjectDocuments.pageInfo.hasNextPage) return;
    setLoading(true);
    await refetch({
      paging: {
        offset: 0,
        limit: data.getProjectDocuments.nodes.length + 20
      }
    })
      .then(res => {
        setDataSource([...dataSource, res.data?.getProjectDocuments?.nodes ?? []]);
        setLoading(false);
      })
      .catch(e => {
        onError(e);
      });
  };

  const moveDoc = useCallback(
    (dragIndex: number, hoverIndex: number) => {
      const dragObject = dataSource[dragIndex];
      const dropObject = dataSource[hoverIndex];

      if (dragObject?.fileSystemType === Gql.FileSystemType.Folder) {
        message.error('Cannot Move Folder');
      } else {
        updateProjectDocument({
          variables: {
            input: {
              id: dragObject.id,
              parentId: dropObject.id
            }
          }
        });
      }
    },
    [dataSource]
  );

  const [createFolder, { loading: createFolderLoading }] = Gql.useCreateOneProjectDocumentMutation({
    onCompleted: () => {
      form.resetFields();
      message.success('Folder created successfully');
      refetch();
      closeAddFolderModal();
    },
    onError: onError as any
  });

  const [updateProjectDocument] = Gql.useUpdateProjectDocumentParentMutation({
    onCompleted: () => {
      message.success('Moved document successfully');
      refetch();
    },
    onError: onError as any
  });

  const [deleteFolder] = Gql.useDeleteProjectDocumentMutation({
    onCompleted: () => {
      message.success('Deleted successfully');
      refetch();
    },
    onError: onError as any
  });

  const [deleteDocuments, { loading: documentsDeleting }] = Gql.useDeleteProjectDocumentsMutation({
    onCompleted: () => {
      refetch();
      message.success('Deleted successfully');
      setSelectedRowKeys([]);
    },
    onError: onError as any
  });

  const showDeleteConfirm = (id: string, name: string) => {
    confirm({
      title: 'Are you sure?',
      icon: <ExclamationCircleOutlined />,
      content: `Do you really want to delete the document '${name}'?`,
      okText: 'Yes',
      okType: 'danger',
      cancelText: 'No',
      onOk() {
        onDelete(id);
      },
      onCancel() {}
    });
  };

  const showDeletesConfirm = () => {
    const deletedDocumentNames = dataSource
      .filter((doc: any) => selectedRowKeys.includes(doc.id.toString()))
      .map((doc: any) => doc.name)
      .slice(0, 10);

    confirm({
      title: 'Are you sure?',
      icon: <ExclamationCircleOutlined />,
      content: (
        <>
          <div>
            Do you really want to delete the{' '}
            {selectedRowKeys.length < 2 && (
              <span className="font-semibold">
                {deletedDocumentNames[0]} <span className="font-normal">?</span>
              </span>
            )}
          </div>
          {selectedRowKeys.length > 1 && (
            <ol className="list-decimal mt-2">
              {deletedDocumentNames.map((name: string, index: number) => (
                <li className="my-2 font-semibold" key={index}>
                  {name}
                </li>
              ))}
            </ol>
          )}
        </>
      ),
      okText: 'Yes',
      okType: 'danger',
      cancelText: 'No',
      onOk() {
        deleteDocuments({
          variables: {
            input: {
              ids: selectedRowKeys.map(id => parseInt(id as string))
            }
          }
        });
        setSelectedRowKeys([]);
      },
      onCancel() {}
    });
  };

  const onFinish = (values: Gql.CreateProjectDocumentInputDto) => {
    const { name } = values;
    const category = Gql.CategoryType.WorkProgramme;
    const fileSystemType = Gql.FileSystemType.Folder;
    const type = 'folder';
    const updatedName = getFileName(type, name ?? '', '');
    const projectDocumentId = dirId as string;
    createFolder({
      variables: {
        input: {
          projectDocument: {
            name: updatedName ?? '',
            category,
            fileSystemType,
            type,
            projectDocumentId
          }
        }
      }
    });
  };

  const onBulkDownload = async () => {
    setLoading(true);
    try {
      const nodeBuffer = await ProjectDocumentApiService.downloadBulkZip(
        {
          body: {
            ids: selectedRowKeys as string[]
          }
        },
        {
          responseType: 'arraybuffer'
        }
      ).catch(e => {
        message.error(e?.message ?? 'Something went wrong');
      });

      const file = new Blob([nodeBuffer], { type: 'application/zip' });
      const fileName = 'bulkdownload.zip';
      setSelectedRowKeys([]);
      setLoading(false);
      FileSaver(file, fileName);
    } catch (e) {
      onError(e);
    }
  };

  const onDelete = (id: string) => {
    deleteFolder({ variables: { id: parseInt(id) } });
  };

  const [modal, showAddFolderModal, closeAddFolderModal] = useModal({
    onCancel: () => form.resetFields(),
    title: 'Create folder',
    content:
      (setTimeout(() => {
        inputRef?.current?.focus();
      }, 100),
      (
        <Form layout="vertical" form={form} onFinish={onFinish} requiredMark={false}>
          <Form.Item label="Folder name" name="name" rules={[{ required: true, message: 'Folder name is required!' }]}>
            <Input ref={inputRef} />
          </Form.Item>
          <div className="flex justify-end">
            <Button htmlType="submit" type="primary" disabled={viewOnly}>
              Create
            </Button>
          </div>
        </Form>
      ))
  });

  const [viewDocumentModal, showViewDocumentModal] = useModal({
    title: '',
    width: '80vw',
    content: (
      <div>
        <DocViewer
          documents={fileUrl}
          pluginRenderers={DocViewerRenderers}
          config={{
            header: {
              disableHeader: true
            }
          }}
        />
      </div>
    )
  });

  const [updateName] = Gql.useUpdateOneProjectDocumentMutation({
    onCompleted: () => {
      message.success('Saved successfully');
      closeEditFolderNameModal();
      refetch();
      form.setFieldValue('name', '');
    },
    onError: onError as any
  });

  const onUpdate = (values: Gql.UpdateProjectDocumentInputDto) => {
    const selectedDocument = dataSource?.find((item: any) => item.id === selectedFormId) ?? null;
    const updatedName = getFileName(selectedDocument.type, values.name ?? '', formName);

    if (!values.name) {
      return message.error('Folder name is required');
    }

    updateName({
      variables: {
        input: {
          id: selectedFormId?.toString() ?? '',
          update: {
            name: updatedName ?? ''
          }
        }
      }
    });
  };

  // Edit Form Category Modal
  const [editFolderNameModal, showEditFolderNameModal, closeEditFolderNameModal] = useModal({
    title: 'Edit name',
    content:
      (setTimeout(() => {
        inputRef?.current?.focus();
      }, 100),
      (
        <Form layout="vertical" form={form} requiredMark={false} onFinish={onUpdate} initialValues={{}}>
          <Form.Item label="Folder/File name" name="name">
            <Input defaultValue={formName} ref={inputRef} />
          </Form.Item>
          <div className="flex justify-end">
            <Button htmlType="submit" type="primary">
              Save
            </Button>
          </div>
        </Form>
      ))
  });

  useEffect(() => {
    if (tableRef.current) {
      const tableBody = tableRef.current.querySelector('.ant-table-body');
      tableBody.addEventListener('scroll', handleTableScroll);
    }
    return () => {
      if (tableRef.current) {
        const tableBody = tableRef.current.querySelector('.ant-table-body');
        tableBody.removeEventListener('scroll', handleTableScroll);
      }
    };
  }, [tableRef]);

  // Download File
  const onDownload = async (id: string) => {
    try {
      const doc = await apolloClient.query<Gql.ProjectDocumentQuery, Gql.ProjectDocumentQueryVariables>({
        query: Gql.ProjectDocumentDocument,
        variables: { id }
      });
      const file = doc.data.projectDocument;

      const { data: fileBlob } = await axios.get(file?.fileUrl ?? '', {
        responseType: 'blob'
      });
      const fileName = file?.name ?? '';

      FileSaver(fileBlob, fileName);
    } catch (e) {
      onError(e);
    }
  };

  const onDownloadFolder = async (id: number, name: string) => {
    try {
      message.open({
        type: 'loading',
        content: `Downloading ${name}...`,
        duration: 0
      });
      const nodeBuffer = await ProjectDocumentApiService.downloadZip(
        {
          body: {
            id
          }
        },
        {
          responseType: 'arraybuffer'
        }
      );

      const file = new Blob([nodeBuffer], { type: 'application/zip' });
      const fileName = name + '.zip';
      FileSaver(file, fileName);
    } catch (e) {
      onError(e);
    } finally {
      message.destroy();
    }
  };

  const columns: ColumnsType<any> = [
    {
      title: 'NAME',
      key: 'name',
      render: (data: any) => {
        const { fileUrl } = data;
        return (
          <Space className="p-1" align="start">
            {getFileIcon(data.type)}
            <div
              className="font-medium cursor-pointer"
              onClick={() => {
                if (fileUrl) {
                  if (data?.fileSystemType === Gql.FileSystemType.Folder) {
                    setFilter({ name: { like: '' } });
                    searchForm.resetFields();
                  }
                  if (router.query && data.fileSystemType === 'Document') {
                    setSelectedRowKeys([]);
                    if (data.type === 'pdf') {
                      router.push({
                        query: {
                          ...router.query,
                          documentId: data.id
                        }
                      });
                    } else {
                      setFileUrl([{ uri: fileUrl, fileType: data.type }]);
                      showViewDocumentModal();
                    }
                  }
                }
                if (data?.fileSystemType === 'Folder') {
                  setFilter({ name: { like: '' } });
                  setSelectedRowKeys([]);
                  router.push(`/cloud-docs/work-programme/${data.id}`);
                }
              }}
            >
              <Tooltip
                title={data?.name}
                mouseEnterDelay={0.5}
                placement="topLeft"
                overlayStyle={{ maxWidth: '550px' }}
              >
                <Paragraph style={{ margin: 0, width: 550 }} ellipsis={{ rows: 2, expandable: false }}>
                  {data?.name} {data?.rootProjectDocumentUsers?.length > 0 && <UsergroupAddOutlined />}
                </Paragraph>
              </Tooltip>
            </div>
          </Space>
        );
      }
    },
    {
      title: 'FILE SIZE',
      key: 'fileSize',
      width: 120,
      // sorter: (a, b) => a?.fileSize - b?.fileSize,
      // align: 'center' as const,
      render: (data: any) => {
        return data.fileSystemType === Gql.FileSystemType.Document ? (
          <p> {data?.fileSize ? `${data?.fileSize} MB` : '-'}</p>
        ) : (
          '-'
        );
      }
    },
    {
      title: 'ADDED BY',
      dataIndex: 'owner',
      key: 'owner',
      width: 160,
      render: (owner: any, index: any) => {
        return (
          <UserAvatar
              key={index}
              username={owner?.name || ""}
              src={owner?.avatar}
              tooltip={owner?.name} 
              align={{
                offset: [-12, -8], 
                }}
                style={{ backgroundColor: !owner?.avatar ? owner?.color || "" : "transparent", // Apply only if no avatar
                  color: "#ffffff"
                }}
            />
        );
      }
    },
    {
      title: 'LAST MODIFIED',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 160,
      render: (updatedAt: any) => {
        return <p>{moment(tz(updatedAt)).format('D MMM YYYY')}</p>;
      }
    },
    {
      title: '',
      key: 'action',
      width: 40,
      align: 'center' as const,
      render: (data: any) => {
        const items: any = [];

        if (data?.fileSystemType === 'Folder') {
          items.push({
            label: <p>Download Folder</p>,
            key: 'DownloadFolder'
          });
          items.push({ type: 'divider' });
        }
        if (data?.fileSystemType === 'Document') {
          items.push({
            label: <p>Download</p>,
            key: 'DownloadDocument'
          });
          items.push({ type: 'divider' });
        }

        if (!viewOnly) {
          items.push({ label: <p>Rename</p>, key: 'Rename' });
        }

        return (
          <ThreeDotsDropDown
            onClick={(e: any) => {
              if (e.key === 'DownloadDocument') {
                onDownload(data.id);
              }
              if (e.key === 'DownloadFolder') {
                onDownloadFolder(data.id, data.name);
              }
              if (e.key === 'Rename') {
                setFormName(data.name);
                form.setFieldValue('name', data.name);
                setSelectedFormId(data.id);
                showEditFolderNameModal();
              }
              if (e.key === 'Share') {
                setShareFileOwner(data.owner);
                setShareModalTitle(`Share "${data.name}"`);
                setProjectDocumentId(data.id);
                showShareModal();
                listOfUsersWithAccess({
                  variables: {
                    projectDocumentId: parseInt(data.id)
                  }
                });
              }
              if (e.key === 'Delete') {
                showDeleteConfirm(data.id, data.name);
              }
              if (e.key === 'MoveFile') {
                setCategory(data.category);
                setFormName(data.name);
                setFileType(data.type);
                setSelectedFormId('' + data.id);
                moveFileModal?.current?.openModal();
              }
            }}
            items={items}
          />
        );
      }
    }
  ];

  // sync modal
  const [syncmodal, showSyncModal, closeSyncModal] = useModal({
    closable: false,

    content: (
      <>
        <SyncModal
          isPersonal={false}
          mimeType="pdf"
          dirId={dirId as string}
          category={Gql.CategoryType.WorkProgramme}
          onComplete={() => {
            refetch();
            closeSyncModal();
          }}
        />
      </>
    )
  });

  const handleNewMenuClick: MenuProps['onClick'] = e => {
    switch (e.key) {
      case '1': {
        showAddFolderModal();
        break;
      }
      case '2': {
        uploadDocModalRef?.current?.openModal();
        break;
      }
      case '3': {
        showSyncModal();
        break;
      }
    }
  };

  const newMenuProps = {
    items: [
      {
        icon: <Icon name="new-folder" />,
        label: <> Add Folder</>,
        key: '1'
      },
      dirId
        ? {
            icon: <Icon name="file-upload" />,
            label: <>Upload Files</>,
            key: '2'
          }
        : null,
      dirId
        ? {
            icon: <Icon name="import" />,
            label: <>Import</>,
            key: '3'
          }
        : null
    ],
    onClick: handleNewMenuClick
  };

  const onMove = () => {
    if (queries?.filter?.name) {
      setFilter({ name: { like: '' } });
    }
    moveDocumentsModal?.current?.openModal();
  };

  return (
    <div className="absolute h-full w-full" ref={dropzoneRef}>
      {modal}
      {shareModal}
      {editFolderNameModal}
      {syncmodal}
      {viewDocumentModal}

      {/* {percentage} */}
      <Spin tip={'Loading...'} spinning={createFolderLoading || documentsDeleting || loading}>
        <div className="flex flex-nowrap items-center justify-between px-5 pt-[20px] pb-[12px]">
          <div>
            {!dirId && <h3>Work Programme</h3>}
            {dirId && (
              <>
                <h3>{breadcrumbs[0]?.name}</h3>
                <Breadcrumb>
                  <Breadcrumb.Item className="text-gray90 text-md">
                    <Link href="/cloud-docs/work-programme">
                      <HomeOutlined />
                    </Link>
                  </Breadcrumb.Item>
                  {breadcrumbs.map(
                    (value, index) =>
                      index !== 0 && (
                        <Breadcrumb.Item className="text-gray90 text-md" key={value.id}>
                          <a
                            onClick={() => {
                              setSelectedRowKeys([]);
                              router.push(`/cloud-docs/work-programme/${value.id}`);
                            }}
                          >
                            {value.name}
                          </a>
                        </Breadcrumb.Item>
                      )
                  )}
                </Breadcrumb>
              </>
            )}
          </div>
          <Row>
            <Space>
              <Form
                form={searchForm}
                onValuesChange={debounce(data => {
                  // onSearch(data);
                  setFilter({ name: { like: data.keyword } });
                }, 300)}
              >
                <div style={{ paddingTop: '10px' }}>
                  <SearchInput
                    onPressEnter={() => {
                      // searchForm.submit();
                    }}
                    placeholder={'Search'}
                  />
                </div>
              </Form>
              {canCreate && (
                <Dropdown menu={newMenuProps}>
                  <Button
                    type="primary"
                    icon={<Icon name="plus-white" className="pt-1 mr-1" />}
                    disabled={viewOnly}
                    className="h-[40px] rounded-lg border-gray40"
                  >
                    <Space>New</Space>
                  </Button>
                </Dropdown>
              )}
            </Space>
          </Row>
        </div>

        <Table
          ref={tableRef}
          rowSelection={{
            type: 'checkbox',
            selectedRowKeys,
            onChange: onSelectChange,
            hideSelectAll: selectedRowKeys.length === 0,
            getCheckboxProps: e => {
              return {
                style: {
                  display: selectedRowKeys.includes(e.id) || e.id === hoveredRowIndex ? 'flex' : 'none',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginBottom: 10
                }
              };
            }
          }}
          className="dashboard-table mb-[30px] px-5"
          dataSource={
            isArray(dataSource)
              ? dataSource?.map((item: any) => ({
                  ...item,
                  key: item.id
                }))
              : dataSource
          }
          columns={columns}
          size="small"
          pagination={false}
          tableLayout="auto"
          scroll={{
            scrollToFirstRowOnChange: false,
            y: window.innerHeight - 230
          }}
          loading={loading || loadingProjectDocument}
          onRow={(record, index) => {
            const attr = {
              index,
              moveDoc,
              onSelect: () => handleRowClick(record),
              onMouseEnter: () => setHoveredRowIndex(record?.id), // when mouse enters the row
              onMouseLeave: () => setHoveredRowIndex(null) // when mouse leaves the row
            };
            return attr as React.HTMLAttributes<any>;
          }}
        />

        <PdfTronModal
          ref={pdfTronModalRef}
          documentId={documentId}
          onSaved={() => refetch()}
          type={Gql.CategoryType.WorkProgramme}
        />

        <CloudDocUpload
          dirId={dirId as any}
          ref={uploadDocModalRef}
          onSaved={() => refetch()}
          type={Gql.CategoryType.WorkProgramme}
        />
        <MoveFileModal
          data={dataSource}
          dirId={dirId as any}
          ref={moveFileModal}
          onSaved={() => refetch()}
          selectedFormId={selectedFormId}
          formName={formName}
          fileType={fileType}
          category={category}
        />

        <MoveBulkCloudDocsModal
          data={dataSource}
          ids={selectedRowKeys}
          dirId={dirId as any}
          ref={moveDocumentsModal}
          onSaved={() => {
            refetch();
            setSelectedRowKeys([]);
          }}
          category={Gql.CategoryType.WorkProgramme}
          setSelectedRowKeys={setSelectedRowKeys}
          onLoadMore={onLoadMore}
        />
      </Spin>

      <ManageModal
        canMove={canMove as boolean}
        onMove={onMove}
        selectedRowKeys={selectedRowKeys}
        onBulkDownload={onBulkDownload}
        showDeletesConfirm={showDeletesConfirm}
        canDelete={canDelete ?? false}
        setSelectedRowKeys={setSelectedRowKeys}
        data={dataSource}
      />
    </div>
  );
};

WorkProgramme.auth = true;
WorkProgramme.Layout = CloudDocsTabLayout;
export default WorkProgramme;
