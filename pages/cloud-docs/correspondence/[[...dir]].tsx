import { ExclamationCircleOutlined, HomeOutlined, UsergroupAddOutlined } from '@ant-design/icons';
import { Icon } from '@commons';
import CloudDocsTabLayout from '@components/cloud-docs/CloudDocsTabLayout';
import { useShareModal } from '@components/cloud-docs/ShareModal';
import SyncModal from '@components/cloud-docs/SyncModal';
import CloudDocUpload from '@components/CloudDocUpload';
import SearchInput from '@components/forms/FormsInput/SearchInput';
import useModal from '@components/hooks/useModal';
import useQueries from '@components/hooks/useQueries';
import ManageModal from '@components/ManageModal';
import MoveBulkCloudDocsModal from '@components/MoveBulkCloudDocsModal';
import MoveFileModal from '@components/MoveCloudDocsModal';
import PdfTronModal from '@components/pdf-tron/PdfTronModal';
import useReminder from '@components/Reminder';
import ThreeDotsDropDown from '@components/ThreeDotsDropDown';
import <PERSON>Viewer, { DocViewerRenderers } from '@cyntler/react-doc-viewer';
import '@cyntler/react-doc-viewer/dist/index.css';
import * as Gql from '@graphql';
import apolloClient from '@lib/apollo';
import { handleRowClick, nameAlias } from '@utils/app.utils';
import { manageFiles } from '@utils/authority';
import { onError } from '@utils/error';
import { getFileName } from '@utils/filename';
import { tz } from '@utils/timezone';
import {
  Avatar,
  Breadcrumb,
  Button,
  Dropdown,
  Form,
  Input,
  MenuProps,
  message,
  Modal,
  Radio,
  RadioChangeEvent,
  Space,
  Spin,
  Table,
  Tooltip
} from 'antd';
import UserAvatar from 'src/components/UserAvatar';
import { useForm } from 'antd/lib/form/Form';
import { ColumnsType } from 'antd/lib/table';
import Paragraph from 'antd/lib/typography/Paragraph';
import axios from 'axios';
import FileSaver from 'file-saver';
import { debounce, isArray } from 'lodash';
import moment from 'moment';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useCallback, useEffect, useRef, useState } from 'react';
import { ProjectDocumentApiService } from 'src/api';
import { getFileIcon } from 'src/commons/FileIcon';
import useDropzone from 'src/hooks/useDropzone';

interface DraggableBodyRowProps extends React.HTMLAttributes<HTMLTableRowElement> {
  index: number;
  moveDoc: (dragIndex: number, hoverIndex: number) => void;
}

interface OrgChart {
  id: string;
  value: {
    name: string;
  };
  children?: OrgChart[];
}

const Correspondence = () => {
  const router = useRouter();
  const pdfTronModalRef = useRef<any>(null);
  const { confirm } = Modal;
  const [form] = useForm();
  const [selectedFormId, setSelectedFormId] = useState<string>();
  const documentId = router.query.documentId as string;
  const [projectUserRole, setProjectUserRole] = useState<string>('');
  const [viewSharedModal, setViewSharedModal] = useState<boolean>(false);
  const [dataSource, setDataSource] = useState<any>(false);
  const uploadDocModalRef = useRef<any>(null);
  const [breadcrumbs, setBreadcrumbs] = useState<
    Gql.GetProjectDocumentsBreadcrumbQuery['getProjectDocumentsBreadcrumb']
  >([]);
  const dirId = router.query.dir && router.query.dir[0];
  const [getBreadcrumbs, { data: breadcrumbData }] = Gql.useGetProjectDocumentsBreadcrumbLazyQuery();
  const [formName, setFormName] = useState<string>('');
  const [fileType, setFileType] = useState<string>('');
  const [category, setCategory] = useState<string>('');
  const moveFileModal = useRef<any>(null);
  const moveDocumentsModal = useRef<any>(null);
  const inputRef = useRef<any>(null);
  const [fileUrl, setFileUrl] = useState<any>([]);
  const [searchForm] = useForm();

  // For multiple selection
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [hoveredRowIndex, setHoveredRowIndex] = useState<number | null>(null);

  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const [loading, setLoading] = useState(false);
  const [driveType, setDriveType] = useState<Gql.ProjectDocumentDriveType>(Gql.ProjectDocumentDriveType.Shared);
  const tableRef = useRef<any>(null);

  const dropzoneRef = useRef<any>(null);
  useDropzone({ dropzoneRef, pushModal: uploadDocModalRef?.current?.openModal });

  useEffect(() => {
    // get projectDocumentReminder from local storage and parse it to boolean
    const reminder = JSON.parse(localStorage.getItem('correspondenceReminder') || 'false');

    if (!reminder) {
      return showReminder();
    }
  }, []);

  useEffect(() => {
    // setFilter({ name: { like: "" } });

    if (!dirId) {
      setBreadcrumbs([]);
      return;
    }

    getBreadcrumbs({
      variables: { input: { id: parseInt(dirId as string) } }
    }).then(res => {
      setBreadcrumbs(res.data?.getProjectDocumentsBreadcrumb ?? []);
    });
  }, [dirId, getBreadcrumbs, breadcrumbData]);

  useEffect(() => {
    setProjectUserRole(localStorage.getItem('ProjectUserRole') || '');
  }, []);

  useEffect(() => {
    if (documentId) {
      pdfTronModalRef.current.openModal();
    }
  }, [documentId]);

  const viewOnly = projectUserRole === 'CanView';
  const canShare =
    (projectUserRole === 'ProjectOwner' || projectUserRole === 'CloudCoordinator') &&
    !dirId &&
    driveType === Gql.ProjectDocumentDriveType.Shared;

  const { canCreate, canDelete, canMove } = manageFiles(projectUserRole, dirId ?? '', driveType);

  const handleTableScroll = (e: { target: any }) => {
    const { target } = e;
    if (target.scrollHeight - target.scrollTop <= target.clientHeight + 1) {
      onLoadMore();
    }
  };

  // OPEN PDF EDITOR
  useEffect(() => {
    if (router.query.documentId) {
      pdfTronModalRef?.current?.openModal();
    }
  }, [router.query.documentId]);

  // sync modal
  const [syncmodal, showSyncModal, closeSyncModal] = useModal({
    closable: false,

    content: (
      <>
        <SyncModal
          mimeType="pdf"
          isPersonal={false}
          dirId={dirId as string}
          category={Gql.CategoryType.Correspondence}
          onComplete={() => {
            refetch();
            closeSyncModal();
          }}
        />
      </>
    )
  });

  const { queries, setFilter } = useQueries<Gql.ProjectDocumentFilter, Gql.ProjectDocumentSort>({
    usePagingParam: false,
    paging: { offset: 0, limit: 20 }
  });
  const {
    data,
    refetch,
    loading: loadingProjectDocument
  } = Gql.useGetProjectDocumentsQuery({
    variables: {
      ...queries,
      filter: {
        ...queries.filter,
        ...{
          projectDocumentId: {
            ...(dirId ? { eq: dirId?.toString() } : { eq: null })
          },
          category: { eq: Gql.CategoryType.Correspondence },
          driveType: { eq: driveType }
        }
      },
      sorting: [
        {
          direction: Gql.SortDirection.Asc,
          field: Gql.ProjectDocumentSortFields.UpdatedAt
        }
      ]
    },
    onError: onError,
    fetchPolicy: 'cache-and-network'
  });

  const {
    shareModal,
    showShareModal,
    setShareModalTitle,
    setProjectDocumentId,
    setShareFileOwner,
    listOfUsersWithAccess
  } = useShareModal({ viewOnly: viewSharedModal, refetch });

  useEffect(() => {
    setDataSource(data?.getProjectDocuments?.nodes ?? []);
  }, [data]);

  // refetch the data when dirId is null
  useEffect(() => {
    if (!dirId) {
      refetch();
    }
  }, [dirId]);

  // update dataSource state onLoadMore function is fire
  useEffect(() => {
    setDataSource(data?.getProjectDocuments?.nodes ?? []);

    if (tableRef.current) {
      const tableBody = tableRef.current.querySelector('.ant-table-body');
      tableBody.addEventListener('scroll', handleTableScroll);
    }
    return () => {
      if (tableRef.current) {
        const tableBody = tableRef.current.querySelector('.ant-table-body');
        tableBody.removeEventListener('scroll', handleTableScroll);
      }
    };
  }, [handleTableScroll]);

  const onLoadMore = async () => {
    if (!data?.getProjectDocuments.pageInfo.hasNextPage) return;
    setLoading(true);
    await refetch({
      paging: {
        offset: 0,
        limit: data.getProjectDocuments.nodes.length + 20
      }
    }).then(res => {
      setDataSource([...dataSource, res.data?.getProjectDocuments?.nodes ?? []]);
      setLoading(false);
    });
  };

  const moveDoc = useCallback(
    (dragIndex: number, hoverIndex: number) => {
      const dragObject = dataSource[dragIndex];
      const dropObject = dataSource[hoverIndex];

      if (dragObject?.fileSystemType === Gql.FileSystemType.Folder) {
        message.error('Cannot Move Folder');
      } else {
        updateProjectDocument({
          variables: {
            input: {
              id: dragObject.id,
              parentId: dropObject.id
            }
          }
        });
      }
    },
    [dataSource]
  );

  const [createFolder, { loading: createFolderLoading }] = Gql.useCreateOneProjectDocumentMutation({
    onCompleted: () => {
      form.resetFields();
      message.success('Folder created successfully');
      refetch();
      closeAddFolderModal();
    },
    onError: onError as any
  });

  const [updateProjectDocument] = Gql.useUpdateProjectDocumentParentMutation({
    onCompleted: () => {
      message.success('Moved document successfully');
      refetch();
    },
    onError: onError as any
  });

  const [deleteFolder] = Gql.useDeleteProjectDocumentMutation({
    onCompleted: () => {
      message.success('Deleted successfully');
      refetch();
    },
    onError: onError as any
  });

  const onFinish = (values: Gql.CreateProjectDocumentInputDto) => {
    const { name } = values;
    const category = Gql.CategoryType.Correspondence;
    const fileSystemType = Gql.FileSystemType.Folder;
    const type = 'folder';
    const updatedName = getFileName(type, name ?? '', '');
    const projectDocumentId = dirId as string;
    createFolder({
      variables: {
        input: {
          projectDocument: {
            name: updatedName,
            category,
            fileSystemType,
            type,
            projectDocumentId,
            driveType
          }
        }
      }
    });
  };

  const onDelete = (id: string) => {
    deleteFolder({ variables: { id: parseInt(id) } });
  };

  const [updateName] = Gql.useUpdateOneProjectDocumentMutation({
    onCompleted: () => {
      message.success('Saved successfully');
      closeEditFolderNameModal();
      refetch();
      form.setFieldValue('name', '');
    },
    onError: onError as any
  });

  const onUpdate = (values: Gql.UpdateProjectDocumentInputDto) => {
    const selectedDocument = dataSource?.find((item: any) => item.id === selectedFormId) ?? null;
    const updatedName = getFileName(selectedDocument.type, values.name ?? '', formName ?? '');

    if (!values.name) {
      return message.error('Folder name is required');
    }

    updateName({
      variables: {
        input: {
          id: selectedFormId?.toString() ?? '',
          update: {
            name: updatedName ?? ''
          }
        }
      }
    });
  };

  // Edit Form Category Modal
  const [editFolderNameModal, showEditFolderNameModal, closeEditFolderNameModal] = useModal({
    title: 'Edit name',
    content:
      (setTimeout(() => {
        inputRef?.current?.focus();
      }, 100),
      (
        <Form layout="vertical" form={form} requiredMark={false} onFinish={onUpdate} initialValues={{}}>
          <Form.Item label="Folder/File name" name="name">
            <Input defaultValue={formName} ref={inputRef} />
          </Form.Item>
          <div className="flex justify-end">
            <Button htmlType="submit" type="primary">
              Save
            </Button>
          </div>
        </Form>
      ))
  });

  const [modal, showAddFolderModal, closeAddFolderModal] = useModal({
    onCancel: () => form.resetFields(),
    title: 'Create folder',
    content:
      (setTimeout(() => {
        inputRef?.current?.focus();
      }, 100),
      (
        <Form layout="vertical" form={form} onFinish={onFinish} requiredMark={false}>
          <Form.Item label="Folder name" name="name" rules={[{ required: true, message: 'Folder name is required!' }]}>
            <Input ref={inputRef} />
          </Form.Item>
          <div className="flex justify-end">
            <Button htmlType="submit" type="primary" disabled={viewOnly}>
              Create
            </Button>
          </div>
        </Form>
      ))
  });

  const [viewDocumentModal, showViewDocumentModal, closeViewDocumentModal] = useModal({
    title: '',
    width: '80vw',
    content: (
      <div>
        <DocViewer
          documents={fileUrl}
          pluginRenderers={DocViewerRenderers}
          config={{
            header: {
              disableHeader: true
            }
          }}
        />
      </div>
    )
  });

  const [deleteDocuments, { loading: documentsDeleting }] = Gql.useDeleteProjectDocumentsMutation({
    onCompleted: () => {
      message.success('Deleted successfully');
      setSelectedRowKeys([]);
      refetch();
    },
    onError: onError as any
  });

  const showDeletesConfirm = () => {
    const deletedDocumentNames = dataSource
      .filter((doc: any) => selectedRowKeys.includes(doc.id.toString()))
      .map((doc: any) => doc.name)
      .slice(0, 10);

    confirm({
      title: 'Are you sure?',
      icon: <ExclamationCircleOutlined />,
      content: (
        <>
          <div>
            Do you really want to delete the{' '}
            {selectedRowKeys.length < 2 && (
              <span className="font-semibold">
                {deletedDocumentNames[0]} <span className="font-normal">?</span>
              </span>
            )}
          </div>
          {selectedRowKeys.length > 1 && (
            <ol className="list-decimal mt-2">
              {deletedDocumentNames.map((name: string, index: number) => (
                <li className="my-2 font-semibold" key={index}>
                  {name}
                </li>
              ))}
            </ol>
          )}
        </>
      ),
      okText: 'Yes',
      okType: 'danger',
      cancelText: 'No',
      onOk() {
        deleteDocuments({
          variables: {
            input: {
              ids: selectedRowKeys.map(id => parseInt(id as string))
            }
          }
        });
        setSelectedRowKeys([]);
      },
      onCancel() {}
    });
  };

  const { reminder, showReminder, closeModal } = useReminder({
    title: 'How cloud documents works',
    description: [
      'Utilize shared drives to organise, store, and collaborate on files. Owner or Cloud Coordinator should grant access to the user.',
      'You can only download files, not subfolders, when downloading a folder'
    ],
    disableReminder: () => {
      localStorage.setItem('correspondenceReminder', 'true');
      closeModal();
    }
  });

  // Download File
  const onDownload = async (id: string) => {
    message.open({
      type: 'loading',
      content: `Downloading ...`,
      duration: 0
    });
    try {
      const doc = await apolloClient.query<Gql.ProjectDocumentQuery, Gql.ProjectDocumentQueryVariables>({
        query: Gql.ProjectDocumentDocument,
        variables: { id }
      });
      const file = doc.data.projectDocument;
      const { data: fileBlob } = await axios.get(file?.fileUrl ?? '', {
        responseType: 'blob'
      });
      const fileName = file?.name ?? '';
      FileSaver(fileBlob, fileName);
    } catch (e) {
      onError(e);
    } finally {
      message.destroy();
    }
  };

  const onBulkDownload = async () => {
    setLoading(true);
    message.open({
      type: 'loading',
      content: `Downloading ...`,
      duration: 0
    });

    try {
      const nodeBuffer = await ProjectDocumentApiService.downloadBulkZip(
        {
          body: {
            ids: selectedRowKeys as string[]
          }
        },
        {
          responseType: 'arraybuffer'
        }
      ).catch(e => {
        message.error(e?.message ?? 'Something went wrong');
      });

      const file = new Blob([nodeBuffer], { type: 'application/zip' });
      const fileName = 'bulkdownload.zip';
      setSelectedRowKeys([]);
      setLoading(false);
      FileSaver(file, fileName);
    } catch (e) {
      onError(e);
    } finally {
      message.destroy();
    }
  };

  const onDownloadFolder = async (id: number, name: string) => {
    try {
      message.open({
        type: 'loading',
        content: `Downloading ${name}...`,
        duration: 0
      });
      const nodeBuffer = await ProjectDocumentApiService.downloadZip(
        {
          body: {
            id
          }
        },
        {
          responseType: 'arraybuffer'
        }
      );

      const file = new Blob([nodeBuffer], { type: 'application/zip' });
      const fileName = name + '.zip';
      FileSaver(file, fileName);
    } catch (e) {
      onError(e);
    } finally {
      message.destroy();
    }
  };

  const projectDocumentUsersCount = (projectDocument: any) => {
    return (
      projectDocument?.rootProjectDocumentUsers
        ?.filter((node: any) => {
          return node.type === Gql.ProjectDocumentUserPermissionType.Include;
        })
        ?.filter((item: { userId: any }, index: any, self: any[]) => {
          return self.findIndex(i => i.userId === item.userId) === index;
        })?.length || 0
    );
  };

  const columns: ColumnsType<any> = [
    {
      title: 'NAME',
      key: 'name',
      defaultSortOrder: 'ascend',
      render: (data: any) => {
        const { fileUrl } = data;
        
        return (
          <Space className="p-1" align="start">
            {getFileIcon(data.type)}
            <div
              className="font-medium cursor-pointer"
              onClick={() => {
                if (fileUrl) {
                  if (data?.fileSystemType === Gql.FileSystemType.Folder) {
                    setFilter({ name: { like: '' } });
                    searchForm.resetFields();
                  }
                  if (router.query && data.fileSystemType === 'Document') {
                    setSelectedRowKeys([]);

                    if (data.type === 'pdf') {
                      router.push({
                        query: {
                          ...router.query,
                          documentId: data.id
                        }
                      });
                    } else {
                      setFileUrl([{ uri: fileUrl, fileType: data.type }]);
                      showViewDocumentModal();
                    }
                  }
                }
                if (data?.fileSystemType === 'Folder') {
                  setFilter({ name: { like: '' } });
                  setSelectedRowKeys([]);
                  router.push(`/cloud-docs/correspondence/${data.id}`);
                }
              }}
            >
              <Tooltip
                title={data?.name}
                mouseEnterDelay={0.5}
                placement="topLeft"
                overlayStyle={{ maxWidth: '550px' }}
              >
                <Paragraph
                  style={{ margin: 0, width: 550 }}
                  ellipsis={{ rows: 2, expandable: false }}
                  title={
                    projectDocumentUsersCount(data) > 0
                      ? 'Shared to ' + projectDocumentUsersCount(data) + ' members'
                      : ''
                  }
                >
                  <span className="flex">
                    {data?.name}{' '}
                    {projectDocumentUsersCount(data) > 0 && (
                      <div
                        onClick={e => {
                          e.stopPropagation();
                          setViewSharedModal(true);
                          setShareFileOwner(data.owner);
                          setShareModalTitle(`Share "${data.name}"`);
                          setProjectDocumentId(data.id);
                          showShareModal();
                          listOfUsersWithAccess({
                            variables: {
                              projectDocumentId: parseInt(data.id)
                            }
                          });
                        }}
                        className="cursor-pointer ml-2 hover:text-blue-500 flex pt-[4px]"
                      >
                        <UsergroupAddOutlined />
                        <span className="text-sm">{projectDocumentUsersCount(data)}</span>
                      </div>
                    )}
                  </span>
                </Paragraph>
              </Tooltip>
            </div>
          </Space>
        );
      }
    },
    {
      title: 'FILE SIZE',
      key: 'fileSize',
      width: 120,
      render: (data: any) => {
        return data.fileSystemType === Gql.FileSystemType.Document ? (
          <p> {data?.fileSize ? `${data?.fileSize} MB` : '-'}</p>
        ) : (
          '-'
        );
      }
    },
    {
      title: dirId ? 'ADDED BY' : 'OWNED BY',
      dataIndex: 'owner',
      key: 'owner',
      width: 160,
      render: (owner: any, index: any) => {
        return (
          <UserAvatar
            key={index} 
            username={owner?.name || ""} // Map owner?.name to username
            src={owner?.avatar} // Map owner?.avatar to src
            tooltip={owner?.name || ""} // Map owner?.name to tooltip
            align={{
              offset: [-12, -9], 
              }}
            style={{ backgroundColor: !owner?.avatar ? owner?.color || "" : "transparent", // Apply only if no avatar
            color: "#ffffff"
            }}
         />
        );
      }
    },
    {
      title: 'LAST MODIFIED',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 160,
      render: (updatedAt: any) => {
        return <p>{moment(tz(updatedAt)).format('D MMM YYYY')}</p>;
      }
    },
    {
      title: '',
      key: 'action',
      width: 40,
      align: 'center' as const,
      render: (data: any) => {
        const items: any = [];

        if (data?.fileSystemType === 'Folder') {
          items.push({
            label: <p>Download Folder</p>,
            key: 'DownloadFolder'
          });
          items.push({ type: 'divider' });
        }
        if (data?.fileSystemType === 'Document') {
          items.push({
            label: <p>Download</p>,
            key: 'DownloadDocument'
          });
          items.push({ type: 'divider' });
        }

        if (!viewOnly) {
          // Only Owner & Cloud Coordinator can Share Folders
          {
            canShare && items.push({ label: <p>Share</p>, key: 'Share' });
            canShare && items.push({ type: 'divider' });
          }
          items.push({ label: <p>Rename</p>, key: 'Rename' });
        }

        return (
          <ThreeDotsDropDown
            onClick={(e: any) => {
              if (e.key === 'DownloadDocument') {
                onDownload(data.id);
              }
              if (e.key === 'DownloadFolder') {
                onDownloadFolder(data.id, data.name);
              }
              if (e.key === 'Rename') {
                setSelectedFormId(data.id);
                setFormName(data.name);
                form.setFieldValue('name', data.name);
                showEditFolderNameModal();
              }
              if (e.key === 'Share') {
                setViewSharedModal(false);
                setShareFileOwner(data.owner);
                setShareModalTitle(`Share "${data.name}"`);
                setProjectDocumentId(data.id);
                showShareModal();
                listOfUsersWithAccess({
                  variables: {
                    projectDocumentId: parseInt(data.id)
                  }
                });
              }
              if (e.key === 'Manage') {
                setSelectedRowKeys([data.id]);
              }
              if (e.key === 'MoveFile') {
                // console.log(data)
                setCategory(data.category);
                setFormName(data.name);
                setFileType(data.type);
                setSelectedFormId('' + data.id);
                moveFileModal?.current?.openModal();
              }
            }}
            items={items}
          />
        );
      }
    }
  ];

  const handleNewMenuClick: MenuProps['onClick'] = e => {
    switch (e.key) {
      case '1': {
        showAddFolderModal();
        break;
      }
      case '2': {
        uploadDocModalRef?.current?.openModal();
        break;
      }
      case '3': {
        showSyncModal();
        break;
      }
    }
  };

  const newMenuProps = {
    items: [
      {
        icon: <Icon name="new-folder" />,
        label: <> {driveType} Folder</>,
        key: '1'
      },
      dirId
        ? {
            icon: <Icon name="file-upload" />,
            label: <>Upload Files</>,
            key: '2'
          }
        : null,
      dirId
        ? {
            icon: <Icon name="import" />,
            label: <>Import</>,
            key: '3'
          }
        : null
    ],
    onClick: handleNewMenuClick
  };

  const handleDriveChange = (e: RadioChangeEvent) => {
    const temp = e.target?.value?.toString() ?? null;
    setDriveType(temp);
  };

  const onMove = () => {
    if (queries?.filter?.name) {
      setFilter({ name: { like: '' } });
    }
    moveDocumentsModal?.current?.openModal();
  };

  return (
    <div className="absolute h-full w-full" ref={dropzoneRef}>
      {modal}
      {shareModal}
      {editFolderNameModal}
      {reminder}
      {syncmodal}
      {viewDocumentModal}
      <Spin tip={'Loading...'} spinning={createFolderLoading || documentsDeleting || loading}>
        <div className="flex flex-nowrap items-center justify-between px-5 pt-[20px] pb-[12px]">
          <div>
            {!dirId && (
              <Radio.Group value={driveType} onChange={handleDriveChange} className='allFormRadio'>
                <Radio.Button key={0} value={'Shared'} className="font-semibold">
                  Shared Drives
                </Radio.Button>
              </Radio.Group>
            )}
            {dirId && (
              <>
                <h3>{breadcrumbs[0]?.name}</h3>
                <Breadcrumb>
                  <Breadcrumb.Item className="text-gray90 text-md">
                    <Link href="/cloud-docs/correspondence">
                      <HomeOutlined />
                    </Link>
                  </Breadcrumb.Item>
                  {breadcrumbs.map(
                    (value, index) =>
                      index !== 0 && (
                        <Breadcrumb.Item className="text-gray90 text-md" key={value.id}>
                          <a
                            onClick={() => {
                              setSelectedRowKeys([]);
                              router.push(`/cloud-docs/correspondence/${value.id}`);
                            }}
                          >
                            {value.name}
                          </a>
                        </Breadcrumb.Item>
                      )
                  )}
                </Breadcrumb>
              </>
            )}
          </div>

          <Space>
            <Form
              form={searchForm}
              onValuesChange={debounce(data => {
                // onSearch(data);
                setFilter({ name: { like: data.keyword } });
              }, 500)}
            >
              <div style={{ paddingTop: '10px' }}>
                <SearchInput
                  onPressEnter={() => {
                    // searchForm.submit();
                  }}
                  placeholder={'Search'}
                />
              </div>
            </Form>
            {canCreate && (
              <Dropdown menu={newMenuProps}>
                <Button
                  type="primary"
                  icon={<Icon name="plus-white" className="pt-1 mr-1" />}
                  disabled={viewOnly}
                  className="h-[40px] rounded-lg border-gray40"
                >
                  <Space>New</Space>
                </Button>
              </Dropdown>
            )}
          </Space>
        </div>

        <Table
          className="dashboard-table px-5"
          rowSelection={{
            type: 'checkbox',
            selectedRowKeys,
            onChange: onSelectChange,
            hideSelectAll: selectedRowKeys.length === 0,
            getCheckboxProps: e => {
              return {
                style: {
                  display: selectedRowKeys.includes(e.id) || e.id === hoveredRowIndex ? 'flex' : 'none',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginBottom: 10
                }
              };
            }
          }}
          dataSource={
            isArray(dataSource)
              ? dataSource?.map((item: any) => ({
                  ...item,
                  key: item.id
                }))
              : dataSource
          }
          ref={tableRef}
          columns={columns}
          size="small"
          pagination={false}
          tableLayout="auto"
          onRow={(record, index) => {
            const attr = {
              index,
              moveDoc,
              onSelect: () => handleRowClick(record),
              onMouseEnter: () => setHoveredRowIndex(record?.id), // when mouse enters the row
              onMouseLeave: () => setHoveredRowIndex(null) // when mouse leaves the row
            };
            return attr as React.HTMLAttributes<any>;
          }}
          scroll={{
            scrollToFirstRowOnChange: false,
            y: window.innerHeight - 230
          }}
          loading={loading || loadingProjectDocument}
        />

        <PdfTronModal ref={pdfTronModalRef} documentId={documentId} onSaved={() => refetch()} />
        <CloudDocUpload
          dirId={dirId as any}
          ref={uploadDocModalRef}
          onSaved={() => refetch()}
          type={Gql.CategoryType.Correspondence}
          driveType={driveType}
        />
        <MoveFileModal
          data={dataSource}
          dirId={dirId as any}
          ref={moveFileModal}
          onSaved={() => refetch()}
          selectedFormId={selectedFormId}
          formName={formName}
          fileType={fileType}
          category={category}
        />

        <MoveBulkCloudDocsModal
          data={dataSource}
          ids={selectedRowKeys}
          dirId={dirId as any}
          ref={moveDocumentsModal}
          onSaved={() => {
            refetch();
            setSelectedRowKeys([]);
          }}
          category={Gql.CategoryType.Correspondence}
          setSelectedRowKeys={setSelectedRowKeys}
          onLoadMore={onLoadMore}
        />
      </Spin>

      <ManageModal
        canMove={canMove as boolean}
        onMove={onMove}
        selectedRowKeys={selectedRowKeys}
        onBulkDownload={onBulkDownload}
        showDeletesConfirm={showDeletesConfirm}
        canDelete={canDelete ?? false}
        setSelectedRowKeys={setSelectedRowKeys}
        data={dataSource}
      />
    </div>
  );
};

Correspondence.auth = true;
Correspondence.Layout = CloudDocsTabLayout;
export default Correspondence;
