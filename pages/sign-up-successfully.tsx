import { MailTwoTone } from '@ant-design/icons';
import { Card } from 'antd';
import { useRouter } from 'next/router';
import React from 'react';
import ForgotPasswordLayout from '../src/components/Layout/ForgotPasswordLayout';

const SignUpSuccessful: React.FC = () => {
  const router = useRouter();
  return (
    <ForgotPasswordLayout>
      <div className="flex justify-center pt-60">
        <Card style={{ height: 300, width: 600, backgroundColor: 'transparent' }}>
          <div>
            <MailTwoTone style={{ fontSize: '600%' }} />
            <h1 className="text-2xl font-bold mb-6 mt-6">A verification link has been sent to your email account</h1>

            <p className="text-gray90 text-base">
              Please click on the link that has just been sent to your email account to verify your email and continue
              the registration process.
            </p>

            <p className="text-gray90 text-base">
              You can ask for an email resend by login to the account before activate.
            </p>
          </div>
        </Card>
      </div>
    </ForgotPasswordLayout>
  );
};

export default SignUpSuccessful;
