import { ExclamationCircleOutlined, FilterOutlined, UserOutlined } from '@ant-design/icons';
import { useApolloClient } from '@apollo/client';
import { Icon } from '@commons';
import ThreeDotsDropDown from '@components/ThreeDotsDropDown';
import ImageUploadInput from '@components/forms/FormsInput/ImageUploadInput';
import useModal from '@components/hooks/useModal';
import { default as useQueries } from '@components/hooks/useQueries';
import FilterDrawer from '@components/tasks/FilterDrawerTasks';
import TaskAddDrawer from '@components/tasks/TaskAddDrawer';
import TaskCollapse from '@components/tasks/TaskCollapse';
import TaskEditDrawer from '@components/tasks/TaskEditDrawer';
import { ProjectAccess } from '@constants/subscription';
import * as Gql from '@graphql';
import { isAllowed } from '@lib/helper';
import { getEnumValues, nameAlias } from '@utils/app.utils';
import { onError } from '@utils/error';
import {
  Avatar,
  Badge,
  Button,
  Card,
  Checkbox,
  Form,
  Input,
  Menu,
  Modal,
  Select,
  Skeleton,
  Space,
  Switch,
  Table,
  Tooltip,
  message
} from 'antd';
import { useForm } from 'antd/lib/form/Form';
import { ColumnsType } from 'antd/lib/table';
import Paragraph from 'antd/lib/typography/Paragraph';
import _ from 'lodash';
import moment from 'moment';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { AlignType } from 'rc-table/lib/interface';
import { useContext, useEffect, useMemo, useRef, useState } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import { TasksApiService, UserAuthService } from 'src/api';
import UserAvatar from 'src/components/UserAvatar';
import { AppLayoutContext } from './_app';
pdfjs.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.6.172/pdf.worker.min.js';

const Tasks = () => {
  const [isOnlyMeFilter, setIsOnlyMeFilter] = useState(false);
  const router = useRouter();
  const { groupId, status, projectGroupId } = router.query;
  const companyId = router.query.companyId as any;
  const taskEditDrawerRef = useRef<any>(null);
  const taskAddDrawerRef = useRef<any>(null);
  const { confirm } = Modal;
  const filterDrawerRef = useRef<any>(null);
  const [searchForm] = useForm();
  const [dataSource, setDataSource] = useState<any>(false);
  const [isPreview, setIsPreview] = useState(false);
  const [previewMemoUrl, setPreviewMemoUrl] = useState<any>();
  const setUserRole = async () => {
    const role = await UserAuthService.currentRole({});
    localStorage.setItem('ProjectUserRole', role);
  };
  const [form] = useForm();

  const [switchCompany] = Gql.useSwitchCompanyMutation({
    onCompleted: () => {},
    onError: onError as any
  });

  const [projectUserRole, setProjectUserRole] = useState<string>('');

  useEffect(() => {
    setProjectUserRole(localStorage.getItem('ProjectUserRole') || '');
  }, []);

  const projectOwner = projectUserRole === Gql.ProjectUserRoleType.ProjectOwner;
  const cloudCoordinator = projectUserRole === Gql.ProjectUserRoleType.CloudCoordinator;

  const { userData: userMeData } = useContext(AppLayoutContext);
  const [getMe] = Gql.useGetUserMeLazyQuery({});

  const [getTask] = Gql.useGetTaskLazyQuery({
    onError: onError,
    onCompleted: res => {}
  });

  const openInNewTab = (url: any) => {
    const redirectNewTab = window.open(url, '_blank', 'noopener,noreferrer');

    if (!redirectNewTab || redirectNewTab.closed || typeof redirectNewTab.closed === 'undefined') {
      setTimeout(() => {
        message.info('Please allow popups for viewing the document.');
      }, 1000);
    } else {
      redirectNewTab.focus();
    }
  };

  //generate memo mutation
  const [generateMemo, { data: generatedMemoData, loading: generating }] = Gql.useGenerateMemoMutation({
    onError: onError as any,
    onCompleted: async res => {
      if (!isPreview) {
        message.success('Memo has been successfully sent.');
        form.resetFields();
        closePreviewModal();
        closeModalMemo();
      } else {
        setTimeout(async () => {
          openInNewTab(`/viewer/task-viewer?documentId=${res?.generateMemo?.id}&preview=true`);
        }, 1000);
      }

      await client.refetchQueries({
        include: [Gql.GetTaskDocument]
      });
    }
  });

  useEffect(() => {
    if (userMeData && companyId) {
      if (userMeData.getUserMe.companyId != companyId) {
        Modal.confirm({
          title: 'Different Projects/Companies',
          content: `Are you sure you want to switch Projects/Companies?`,
          okText: 'Yes',
          cancelText: 'No',
          onCancel: async () => {
            window.close();
          },
          onOk: async () => {
            await switchCompany({
              variables: {
                companyId: parseInt(companyId)
              }
            });
            await setUserRole();
            window.location.reload();
          }
        });
      }
    }
  }, [userMeData]);

  const viewOnly = projectUserRole === 'CanView';

  useEffect(() => {
    if (router.query.taskId) {
      taskEditDrawerRef?.current?.pushDrawer();
    }
  }, [router.query.taskId]);

  const { queries, setPaging, setFilter } = useQueries<Gql.TaskFilter, Gql.TaskSort>();

  const client = useApolloClient();
  const { data, refetch, loading } = Gql.useGetTasksQuery({
    fetchPolicy: 'cache-and-network',
    variables: {
      ...queries,
      filter: {
        ...queries.filter,
        ...(projectGroupId && {
          group: { projectGroupId: { eq: projectGroupId as string } }
        }),
        ...{
          dueDate: {
            isNot: null
          }
        },
        ...(queries?.filter?.title && {
          title: {
            like: `%${queries?.filter?.title}%`
          }
        }),
        ...(queries?.filter?.groupId && {
          groupId: { eq: queries?.filter?.groupId as string }
        }),
        ...(groupId && {
          groupId: { eq: groupId as string }
        }),
        ...(queries?.filter?.id && {
          taskCode: {
            eq: queries?.filter?.id as string
          }
        }),
        ...(queries?.filter?.status && {
          status: {
            eq: queries?.filter?.status as any
          }
        }),
        ...(status && {
          status: {
            eq: status as string
          }
        }),
        ...(queries?.filter?.assignees && {
          assignees: {
            userId: { in: queries?.filter?.assignees } as any
          }
        }),
        ...(isOnlyMeFilter &&
          userMeData && {
            assignees: queries?.filter?.assignees
          }),
        ...(queries?.filter?.dueDate && {
          dueDate: {
            between: {
              lower: `${queries?.filter?.dueDate} 00:00:00`,
              upper: `${queries?.filter?.dueDate} 23:59:59`
            }
          }
        })
      },
      sorting: [
        {
          direction: Gql.SortDirection.Desc,
          field: Gql.TaskSortFields.IsUrgent
        },
        {
          direction: Gql.SortDirection.Desc,
          field: Gql.TaskSortFields.CreatedAt
        }
      ]
    },
    onError: onError,
    onCompleted(data) {
      setDataSource(data?.tasks?.nodes ?? []);
    }
  });
  const totalCount = data?.tasks?.totalCount;

  const [updateTask, { loading: updateLoading }] = Gql.useUpdateOneTaskMutation({
    onCompleted(data, clientOptions) {
      refetch();
      if (data?.updateOneTask?.isUrgent) {
        return message.success('Task has been set to urgent');
      } else {
        return message.success('Task has been set to non urgent');
      }
    },
    onError: onError as any
  });

  // Filtering for assigned to me
  useEffect(() => {
    setFilter({
      ...(isOnlyMeFilter &&
        userMeData && {
          assignees: { userId: { eq: userMeData?.getUserMe?.id } }
        }),
    });
    setPaging({
      limit: 10,
      offset: 0
    });
  }, [isOnlyMeFilter]);

  const [deleteTasks] = Gql.useDeleteOneTaskMutation({
    onCompleted: () => {
      message.success('Task moved to recycle bin successfully');
      refetch();
    },
    onError(error) {
      onError(error);
    }
  });

  const onDelete = async (id: string) => {
    try {
      await deleteTasks({
        variables: {
          id
        }
      });
    } catch (e) {
      onError(e);
    }
  };

  const showDeleteConfirm = (id: string, title: string) => {
    confirm({
      title: 'Are you sure?',
      icon: <ExclamationCircleOutlined />,
      content: (
        <p>
          Do you really want to delete the <span className="font-semibold">{title}</span>?
        </p>
      ),
      okText: 'Yes',
      okType: 'danger',
      cancelText: 'No',
      onOk() {
        onDelete(id);
      },
      onCancel() {}
    });
  };

  const isFilterDataExist = Object.values({ ...queries?.filter }).some(value => {
    if (Array.isArray(value)) {
      return value.length > 0;
    }
    return !!value;
  });

  const concatenateTaskAssignees = (assignees: any[]) => {
    let listedAssignees: any[] = []
  
    if (assignees.length > 4){
      const consolidatedNames = assignees.map((assignee: any, index: number) => {
        if ([0,1, assignees.length - 1].includes(index)) {
          return
        }

        return assignee?.user?.name
      }).filter((name: any) => (name)).join(',')
      // kalau lebih dari 4, kita akan pick first 2 and the last assignee sahaja, lain kita sorok
      const balanceCount = assignees.length - 3
  
      listedAssignees = [
        assignees[0],
        assignees[1],
        assignees[assignees.length - 1],
        {
          user: {
            name: '+ ' + balanceCount
          },
          title: consolidatedNames
        }
      ]
    } else {
      listedAssignees = assignees
    }
  
    return listedAssignees
  }
  

  const columns = useMemo(
    (): ColumnsType<any> => [
      {
        title: <p className="ml-2">TITLE</p>,
        render: (dataSource, index: number) => {
          return (
            <Space key={index}>
              <div
                className="cursor-pointer"
                onClick={() => {
                  router.replace({
                    query: {
                      ...router.query,
                      taskId: dataSource.id
                    }
                  });
                }}
              >
                <Paragraph
                  style={{ margin: '10px', width: 400, fontSize: 15 }}
                  ellipsis={{ rows: 1, expandable: false }}
                  id="task-name"
                >
                  <Tooltip placement="topLeft" title={dataSource.title}>
                    {dataSource.title} {dataSource?.memoUrl && <Icon name="memo" width={20} height={16} />}
                  </Tooltip>
                </Paragraph>

                <Paragraph
                  style={{ margin: '10px', color: '#898E92' }}
                  className="text-base max-w-lg"
                  ellipsis={{ rows: 1, expandable: false }}
                >
                  {dataSource.description}
                </Paragraph>
              </div>
            </Space>
          );
        }
      },
      {
        title: 'PRIORITY',
        key: 'isUrgent',
        align: 'center',
        width: 90,
        render: (data: Gql.Task) => {
          const onClick = (e: any) => {
            e.stopPropagation();
            updateTask({
              variables: {
                id: data?.id,
                input: {
                  isUrgent: !data?.isUrgent
                }
              }
            });
          };
          return (
            <div onClick={onClick} className="cursor-pointer">
              {data?.isUrgent ? (
                <Icon name="flag" fill="#ff0000" width={20} height={20} />
              ) : (
                <Icon name="flag" width={20} height={20} />
              )}
            </div>
          );
        }
      },
      {
        title: 'ID',
        key: 'taskCode',
        width: 80,
        render: (data: any) => {
          return data ? (
            <p className="cursor-pointer" id="id">
              {'#' + data.taskCode}
            </p>
          ) : (
            ''
          );
        }
      },
      {
        title: 'STATUS',
        key: 'status',
        width: window.innerWidth < 1200 ? 235 : 200,
        render: (data: any) => {
          let color = data.status === Gql.TaskStatusType.Completed ? '#5BBA5F' : '#585757';

          let borderColor = '';
          let text = data.status;

          if (data.status === Gql.TaskStatusType.InProgress) {
            borderColor = '#3E78CF';
          } else if (data.status === Gql.TaskStatusType.Completed) {
            borderColor = '#18A601';
          } else if (data.status === Gql.TaskStatusType.Open) {
            borderColor = '#F40000';
          } else if (data.status === Gql.TaskStatusType.Hold) {
            borderColor = '#F29100';
          }

          if (data?.proposedStatus) {
            text = `Proposed > ${data?.proposedStatus === 'Completed' ? `Closed` : data?.proposedStatus}`;

            if (data?.proposedStatus === 'Hold') {
              color = '#585757';
              borderColor = '#ffc266';
            } else if (data?.proposedStatus === 'Completed') {
              borderColor = '#9ce255';
            }
          }

          if (data?.status === Gql.TaskStatusType.Completed && !data?.proposedStatus) {
            text = 'Closed';
          }

          return (
            <div className="cursor-pointer flex flex-row ">
              <Space
                style={{
                  width: 4,
                  background: borderColor,
                  color: borderColor
                }}
              >
                .
              </Space>
              <p style={{ color }} className="max-w-fit ml-2" id="status">
                {text.replace(/([A-Z])/g, ' $1').trim()}
              </p>
            </div>
          );
        }
      },
      {
        title: 'DUE DATE',
        key: 'dueDate',
        width: 150,
        render: (data: any) => {
          const color =
            moment(data.dueDate).isBefore(moment()) && data.status !== Gql.TaskStatusType.Completed
              ? '#FF2020'
              : '#585757';

          return data.dueDate ? (
            <p className="max-w-fit cursor-pointer" id="due-date" style={{ color }}>
              {moment(data.dueDate).format('DD/MM/YYYY')}
            </p>
          ) : (
            ''
          );
        }
      },
      {
        title: "ASSIGNED TO",
        dataIndex: "assignees",
        key: "assignees",
        width: 100,
        align: "center" as const,
        render: (assignees: any, index: any) => {
          const listedAssignees = concatenateTaskAssignees(assignees.nodes);

          return (
            <div className="pr-4">
                <Avatar.Group className="grid grid-cols-4 px-[8px] evenly-spaced">
                  {listedAssignees.map((assignee: any, index: any) => {
                    const isHidden = assignee?.user?.name?.startsWith('+ ') || assignee?.isHidden;
                    const backgroundColor = isHidden
          ?           '#F5F5F5' :
                      assignee?.user?.color || // First try assignee's color
                      (assignee?.user?.id === userMeData?.getUserMe?.id
                        ? userMeData?.getUserMe?.color // Then current user's color if assignee is me
                        : "#E8EFFF"); // Fallback

                    const fontColor = isHidden ? '#565656' : '#FFFFFF';
                    const fontWeight = isHidden ? 'bold' : 'normal';

                    const tooltipContent = isHidden
                    ? (() => {
                        const namesString = assignee?.title || assignee?.user?.name || '';
  
                        const names = namesString.includes(',')
                          ? namesString.split(',') 
                          : namesString.split(' ').filter(Boolean); 
                        return (
                          <div style={{ display: 'flex', flexDirection: 'column', lineHeight: '1.2' }}>
                            {names.map((name: string, idx: number) => (
                              <span key={idx}>{name.trim()}</span> // One name per line
                            ))}
                          </div>
                        );
                      })()
                    : `${assignee?.user?.name}${assignee?.status ? ` - ${assignee?.status}` : ''}`;
                    

                    return (
                          <UserAvatar
                            key={index}
                            username={assignee?.user?.name || ""}
                            src={assignee?.user?.avatar || ""}
                            tooltip={tooltipContent} 
                            style={{ backgroundColor, color: fontColor, fontWeight }}
                          />
                    );
                  })}
              </Avatar.Group>
            </div>
          );
          }
        },
      {
        title: 'GROUP',
        dataIndex: 'group',
        key: 'group',
        width: 160,
        render: (group: any) => {
          return group ? (
            <Paragraph
              id="group"
              className="cursor-pointer"
              style={{ marginBottom: 0 }}
              ellipsis={{ rows: 2, expandable: false }}
            >
              {group?.title}
            </Paragraph>
          ) : (
            'N/A'
          );
        }
      },
      {
        title: '',
        key: 'action',
        width: 20,
        align: 'left' as AlignType,
        onCell: () => {
          return {
            onClick: (e: any) => {
              e.stopPropagation();
            }
          };
        },
        render: (data: any) => {
          // only owner and project owner and cloud coordinator can delete
          if (userMeData?.getUserMe.id === data?.ownerId || projectOwner || cloudCoordinator) {
            return (
              <ThreeDotsDropDown
                onClick={async (e: any) => {
                  if (e.key === '1') {
                    showDeleteConfirm(data.id, data.title);
                  } else if (e.key === '2') {
                    let getDefaultLanguage = localStorage.getItem('defaultLanguage');
                    let getDefaultSignatureOption = localStorage.getItem('defaultSignatureOption');

                    if (!getDefaultSignatureOption) {
                      localStorage.setItem('defaultSignatureOption', JSON.stringify(['Stamp', 'Signature']));
                    }

                    if (!getDefaultLanguage) {
                      localStorage.setItem('defaultLanguage', 'BahasaMelayu');
                    }

                    await form.setFieldValue('signatureOptions', getDefaultSignatureOption ?? ['Stamp', 'Signature']);
                    await form.setFieldValue('languageType', getDefaultLanguage ?? Gql.LanguageType.BahasaMelayu);

                    onShowModal(data?.id);
                  }
                }}
                items={[
                  {
                    key: '1',
                    label: 'Delete'
                  },
                  {
                    key: '2',
                    label: 'Generate Memo'
                  }
                ]}
              />
            );
          } else {
            return;
          }
        }
      }
    ],
    [router]
  );

  const route = useRouter();

  const onFilter = (data: any) => {
    const search = TasksApiService.filterTasks({
      body: {
        title: data.keyword ?? '',
        taskCode: data.id ?? '',
        status: data.status ?? '',
        dueDate: data.dueDate ?? '',
        group: data.group ?? '',
        assignedTo: data.assignee ?? ''
      }
    });
    search.then((res: any) => {
      setDataSource(res);
    });
  };

  const clearFilter = () => {
    // clear the router query
    router.replace('/tasks');
  };

  /** START ACL */
  const companySubscriptions = Gql.useCompanySubscriptionsQuery({
    variables: {
      sorting: {
        direction: Gql.SortDirection.Desc,
        field: Gql.CompanySubscriptionSortFields.SubscriptionEndDate
      },
      paging: {
        limit: 1
      }
    },
    nextFetchPolicy: 'cache-first'
  });
  /** END ACL */

  const onShowModal = async (id: string) => {
    const taskRes = await getTask({ variables: { id } });
    const task = taskRes?.data?.task;
    const me = await getMe();

    if (me?.data?.getUserMe?.signUrl) {
      await form.setFieldsValue({
        id: task?.id,
        companyName: userMeData?.getUserMe?.company?.name,
        logoUrl: userMeData?.getUserMe?.company?.logoUrl,
        attachments: task?.attachments?.nodes,
        medias: task?.medias?.nodes.filter(obj => obj.type !== 'mp4' && obj.type !== 'mov'),
        documents: task?.documents?.nodes.filter(obj => obj.type === 'pdf')
      });

      showModalMemo();
    } else {
      Modal.confirm({
        title: 'Signature not found',
        content: 'Please upload your signature before generating memo.',
        okText: 'Upload Signature',
        cancelText: 'Cancel',
        onOk: () => {
          router.push('/settings/profile');
        }
      });
    }
  };

  const onFinish = async (val: any) => {
    let attachments = [];
    let medias = [];
    let documents = [];

    //filter out doc without tick
    if (val?.attachments) {
      attachments = val.attachments
        .filter((attachment: any) => attachment.tick)
        .map((attachment: any) => attachment.id);
    }

    if (val?.medias) {
      medias = val.medias.filter((media: any) => media.tick).map((media: any) => media.id);
    }

    if (val?.documents) {
      documents = val.documents.filter((document: any) => document.tick).map((document: any) => document.id);
    }

    const url = typeof val?.logoUrl === 'string' ? 'logoUrl' : 'customUrl';

    await generateMemo({
      variables: {
        input: {
          languageType: val?.languageType,
          taskId: form?.getFieldValue('id'),
          [url]: val?.logoUrl,
          ..._.omit(val, ['attachments', 'medias', 'documents', 'logoUrl', 'signatureOptions']),
          refNo: val?.refNo,
          attachmentIds: attachments,
          mediaIds: medias,
          documentIds: documents,
          hasSignature: val?.signatureOptions?.includes('Signature'),
          hasStamp: val?.signatureOptions?.includes('Stamp'),
          isPreview: form?.getFieldValue('isPreview')
        }
      }
    });
  };

  const languages = getEnumValues({ GqlTypeEnum: Gql.LanguageType });

  const documentSigningOptions = ['Stamp', 'Signature'];
  const onSignatureCheck = (checkedValues: any) => {
    localStorage.setItem('defaultSignatureOption', JSON.stringify(checkedValues));
  };

  const [modal, showModalMemo, closeModalMemo] = useModal({
    disableClose: generating,
    onCancel: () => {
      if (generating) return;
      form?.resetFields();
    },
    title: 'Generate Memo',
    content: (
      <Form layout="vertical" form={form} onFinish={onFinish} requiredMark={false}>
        <Form.Item className="text-gray90" label="Logo" name="logoUrl">
          <ImageUploadInput />
        </Form.Item>

        <Form.Item
          className="text-gray90"
          label="Company Name"
          name="companyName"
          rules={[{ required: true, message: 'Company Name is required' }]}
        >
          <Input className="rounded-lg h-10 w-70" />
        </Form.Item>

        <Form.Item
          className="text-gray90"
          label="Reference No."
          name="refNo"
          rules={[{ required: true, message: 'Reference No. is required' }]}
        >
          <Input className="rounded-lg h-10 w-70" />
        </Form.Item>

        <Form.Item
          className="text-gray90"
          label="Language"
          name="languageType"
          rules={[{ required: true, message: 'Language is required' }]}
        >
          <Select
            onChange={val => localStorage.setItem('defaultLanguage', val)}
            options={languages}
            className="rounded-lg h-10 w-70"
          />
        </Form.Item>

        <Form.Item
          className="text-gray90"
          label="Additional Details"
          name="additionalDetails"
          rules={[
            {
              max: 700,
              message: 'Additional Detail cannot be more than 700 characters'
            }
          ]}
        >
          <Input.TextArea rows={3} className="rounded-lg h-10 w-70" />
        </Form.Item>

        <Form.Item name="signatureOptions">
          <Checkbox.Group
            options={documentSigningOptions}
            defaultValue={['Stamp', 'Signature']}
            onChange={onSignatureCheck}
          />
        </Form.Item>

        <TaskCollapse />

        <div className="flex gap-4 ">
          <Button
            className="rounded-lg h-10 w-full font-medium"
            loading={loading || generating}
            disabled={loading}
            onClick={async () => {
              setIsPreview(true);
              await form.setFieldValue('isPreview', true);
              await form.submit();
            }}
          >
            Preview
          </Button>
          <Button
            htmlType="submit"
            type="primary"
            className="rounded-lg h-10 w-full font-medium"
            loading={loading || generating}
            disabled={loading}
            onClick={async () => {
              setIsPreview(false);
              await form.setFieldValue('isPreview', false);
            }}
          >
            Generate
          </Button>
        </div>
      </Form>
    )
  });

  const [previewModal, closePreviewModal] = useModal({
    onCancel: () => {},
    title: 'Preview',
    content: (
      <>
        <Card loading={generating} bordered={false} className=" flex justify-center align-center">
          {previewMemoUrl && (
            <Document
              file={previewMemoUrl}
              onLoadError={err => console.log('err', err)}
              loading={<Skeleton active className="w-full h-[350px] relative" />}
            >
              <Page width={400} pageNumber={1} renderAnnotationLayer={false} renderTextLayer={false} />
            </Document>
          )}
        </Card>
        <Button
          type="primary"
          loading={generating}
          onClick={() => {
            setIsPreview(false);
            form.setFieldValue('isPreview', false);
            form.submit();
          }}
        >
          Generate
        </Button>
      </>
    )
  });

  return (
    <>
      {modal}
      {previewModal}
      <div className="mt-0">
        <Menu
          mode="horizontal"
          defaultSelectedKeys={[route.pathname.replace('/[[...dir]]', '')]}
          className="pl-0"
          items={
            isAllowed(companySubscriptions, ProjectAccess.TASK)
              ? [
                  {
                    key: '/tasks-overview',
                    label: (
                      <div key="/tasks-overview" className="font-semibold">
                        <Link href="/tasks-overview"> Overview </Link>
                      </div>
                    )
                  },
                  {
                    key: '/tasks',
                    label: (
                      <div key="/tasks" className="font-semibold">
                        <Link href="/tasks"> Tasks </Link>
                      </div>
                    )
                  }
                ]
              : []
          }
        />
      </div>
      <div className="pt-5 pb-9 absolute overflow-auto h-full w-full">
        <div className="px-5 flex pb-[12px] justify-between items-center">
          <div>
            <h3>Tasks</h3>
          </div>

          <div>
            <Form form={searchForm} onFinish={onFilter}>
              <Space>
                <Switch
                  checkedChildren="Assigned To Me"
                  unCheckedChildren="Assigned To Me"
                  onChange={async checked => {
                    await setIsOnlyMeFilter(checked);
                  }}
                />

                <Badge dot={isFilterDataExist} offset={[-3, 3]}>
                  <Button
                    type="default"
                    className="ml-[4px] h-[40px] rounded-lg border-gray40"
                    onClick={() => {
                      filterDrawerRef?.current?.pushDrawer();
                    }}
                    icon={<FilterOutlined />}
                  >
                    Filters
                  </Button>
                </Badge>
                <Button
                  type="primary"
                  className="rounded-lg h-[40px]"
                  disabled={viewOnly}
                  onClick={() => {
                    taskAddDrawerRef?.current?.pushDrawer();
                  }}
                >
                  Create Task
                </Button>
              </Space>
            </Form>
          </div>
        </div>
        <div>
          <Table
            className="pt-2 dashboard-table px-5"
            size="small"
            columns={columns}
            onRow={(record) => {
              return {
                className: 'cursor-pointer',
                onClick: e => {
                  router.replace({
                    query: {
                      ...router.query,
                      taskId: record.id
                    }
                  });
                }
              };
            }}
            dataSource={dataSource}
            pagination={{
              pageSize: queries.paging.limit,
              current: queries.paging.offset / queries.paging.limit + 1,
              total: totalCount ?? 0
            }}
            scroll={{
              scrollToFirstRowOnChange: false
            }}
            tableLayout="auto"
            onChange={paginate => {
              const { current, pageSize } = paginate;

              if (pageSize !== undefined && current !== undefined) {
                setPaging({
                  offset: (current - 1) * pageSize,
                  limit: pageSize
                });
              }
            }}
            loading={loading}
          />
        </div>

        <TaskAddDrawer ref={taskAddDrawerRef} onSaved={() => refetch()} />
        <TaskEditDrawer ref={taskEditDrawerRef} onSaved={() => refetch()} />
        <FilterDrawer
          ref={filterDrawerRef}
          onSubmit={async (values: any) => {
            const { groupId, status, dueDate, assignees, keyword, id } = values;
            await setFilter({
              ...(assignees && {
                assignees
              }),
              ...(id && {
                taskCode: {
                  eq: id
                }
              }),
              ...(groupId && {
                groupId
              }),
              ...(keyword && {
                title: keyword
              }),
              ...(status && {
                status
              }),
              ...(dueDate && {
                dueDate: values.dueDate ? moment(values.dueDate?._d).format('YYYY-MM-DD') : ('' as any)
              }),
              ...(isOnlyMeFilter &&
                userMeData && {
                  assignees: { userId: { eq: userMeData?.getUserMe?.id } }
                })
            });
          }}
          onClear={clearFilter}
        />
      </div>
    </>
  );
};
Tasks.auth = true;
export default Tasks;
