import { AppContext } from '@components/context/AppContext';
import * as Gql from '@graphql';
import { onError } from '@utils/error';
import { Button, message, ModalProps, Space, Spin, Tabs } from 'antd';
import _ from 'lodash';
import { useRouter } from 'next/router';
import { forwardRef, useContext, useEffect, useRef, useState } from 'react';

interface Props {
  onSaved?: () => void;
  onSavedXFDF?: (values: any) => void;
  documentId?: string;
}

const PdfTronTab = forwardRef<Props>((props: any) => {
  const router = useRouter();
  const documentId = router.query.documentId as any;
  const tabRef = useRef<any>(null);
  const viewerDiv = useRef<HTMLDivElement>(null);
  const [pdfTron, setPdfTron] = useState<any>();
  const context = useContext(AppContext);
  const room = `project-document-room-${props.documentId}`;
  const [getDoc, { data }] = Gql.useGetTasksAttachmentLazyQuery({ onError: onError as any });
  const [loading, setLoading] = useState(false);

  function timeout(delay: number) {
    return new Promise(res => setTimeout(res, delay));
  }

  async function flattenPDF() {
    setLoading(true);

    await pdfTron.Core.PDFNet.initialize();

    const doc = await pdfTron.docViewer?.getDocument().getPDFDoc();
    const annots = await pdfTron.docViewer?.getAnnotationManager().exportAnnotations();

    await pdfTron.PDFNet?.runWithCleanup(async () => {
      doc.lock();

      const fdf_doc = await pdfTron.PDFNet.FDFDoc.createFromXFDF(annots);
      await doc.fdfUpdate(fdf_doc);
      await doc.flattenAnnotations();
      pdfTron.docViewer
        ?.getAnnotationManager()
        .deleteAnnotations(pdfTron.docViewer.getAnnotationManager().getAnnotationsList());
    });

    const doc2 = pdfTron?.Core?.documentViewer.getDocument();
    const xfdfString = await pdfTron.Core?.annotationManager?.exportAnnotations();
    const options = { xfdfString, flatten: true };
    const data = await doc2.getFileData(options);
    const arr = new Uint8Array(data);
    const blob = new Blob([arr], { type: 'application/pdf' });
    const convert = blobToFile(blob, file?.name);

    pdfTron.docViewer?.refreshAll();
    pdfTron.docViewer?.updateView();
    pdfTron.docViewer?.getDocument().refreshTextData();

    onUpdate(convert);
    setLoading(false);
  }

  function blobToFile(theBlob: any, fileName: any) {
    theBlob.lastModifiedDate = new Date();
    theBlob.name = fileName;
    return theBlob;
  }

  //Getting documentId
  useEffect(() => {
    if (documentId) {
      getDoc({ variables: { id: documentId ?? '' } });
      setupPDF();
    }
  }, [documentId]);

  const file = data?.tasksAttachment;

  const [updateAttachments, { loading: updateFileLoading }] = Gql.useUpdateTasksAttachmentMutation({
    onCompleted: async () => {
      message.success('File saved successfully');
      await timeout(2000);
      window.close();
    },
    onError: onError as any
  });

  const onUpdate = (files: any) => {
    updateAttachments({
      variables: {
        id: _.toString(documentId),
        input: {
          fileUrl: files
        }
      }
    });
  };

  // Duplicated the edited file to All form
  const [duplicateEditedStandardForm] = Gql.useDuplicateEditedStandardFormMutation({
    onCompleted: () => {
      if (router.query.documentDetails) {
        router.replace({
          query: {
            ..._.omit(router.query, 'documentId'),
            documentDetailsId: router.query.documentId
          }
        });
      }
      message.success('File saved successfully in all form');
    },
    onError: onError as any
  });

  //Loading the document
  useEffect(() => {
    if (pdfTron && file?.fileUrl) {
      pdfTron?.UI.loadDocument(file?.fileUrl);
      pdfTron?.Core?.documentViewer.addEventListener('documentLoaded', async () => {
        // pdfTron.Core?.annotationManager?.importAnnotations(file?.xfdf);
      });
    }
  }, [pdfTron, file?.fileUrl]);

  function setupPDF() {
    if (!pdfTron) {
      import('@pdftron/webviewer').then(() => {
        // @ts-ignore
        WebViewer(
          {
            path: '/webviewer',
            licenseKey: process.env.NEXT_PUBLIC_PDFTRON_LICENSE_KEY,
            initialDoc: '',
            fullAPI: true
          },
          viewerDiv.current as HTMLDivElement
        ).then((instance: any) => {
          instance.UI.useEmbeddedPrint(true);
          instance.UI.setPrintQuality(5);

          var Feature = instance.UI.Feature;
          const { docViewer } = instance;
          const { Tools } = instance.Core;
          const panTool = Tools.ToolNames.PAN;
          instance.UI.setToolbarGroup('toolbarGroup-View');

          const style = instance.UI.iframeWindow.document.documentElement.style;

          /*************************************************************** PDFTRON UI CHANGES ********************************************************/
          style.setProperty(`--primary-button`, '#004b75');
          style.setProperty(`--primary-button-hover`, 'yellow');
          style.setProperty(`--view-header-icon-active-fill`, '#1A4971');
          style.setProperty(`--panel-background`, '#E9F0F4');
          style.setProperty(`--tools-header-background`, '#E9F0F4');
          style.setProperty(`--view-header-background`, '#175572');

          /*******************************************************************************************************************************************/

          // instance.UI.disableFeatures([Feature.Measurment, Feature.Copy]);

          docViewer?.addEventListener('documentLoaded', function () {
            // instance.UI.toggleFullScreen();
            instance.UI.setToolMode(panTool);
            instance.UI.setZoomLevel('100%');
          });

          instance.UI.disableTools([
            Tools.ToolNames.RUBBER_STAMP,
            Tools.ToolNames.FILEATTACHMENT,
            Tools.ToolNames.CALLOUT,
            Tools.ToolNames.CALLOUT2,
            Tools.ToolNames.CALLOUT3,
            Tools.ToolNames.CALLOUT4,
            Tools.ToolNames.FORM_FILL_CROSS,
            Tools.ToolNames.FORM_FILL_DOT,
            Tools.ToolNames.CALENDER
          ]);

          // const ToolNames = instance.Core.Tools.ToolNames;
          instance.UI.disableElements(['toolbarGroup-Forms']);
          instance.UI.disableElements(['toolbarGroup-Shapes']);
          instance.UI.disableElements(['toolbarGroup-Edit']);
          instance.UI.disableElements(['toolbarGroup-Annotate']);
          instance.UI.disableElements(['toolbarGroup-Insert']);
          instance.UI.disableElements(['toolbarGroup-Edit']);
          // instance.UI.disableElements(["toolbarGroup-FillAndSign"]);
          instance.UI.setHeaderItems(function (header: {
            getHeader: (arg0: string) => {
              (): any;
              new(): any;
              push: {
                (arg0: { type: string; toolGroup: string; dataElement: string; title: string }): void;
                new(): any;
              };
              delete: { (arg0: number): void; new(): any };
            };
          }) {
            // header.getHeader('toolbarGroup-FillAndSign').push({ type: 'toolGroupButton', toolGroup: 'stampTools', dataElement: 'stampToolGroupButton', title: 'Image' });
            // header.getHeader('toolbarGroup-Insert').push({ type: 'toolGroupButton', toolGroup: 'scaleTools', dataElement: 'scaleToolGroupButton', title: 'annotation.scale' });
            // header.getHeader('toolbarGroup-Shapes').delete(5);
            // header.getHeader('toolbarGroup-Insert').delete(3);
            setPdfTron(instance);
          });
        });
      });
    }
  }

  return (
    <div>
      <Spin spinning={updateFileLoading}>
        <div className="webviewer mt-6" ref={viewerDiv} style={{ height: '91vh' }}></div>
        <Space style={{ width: '100%', justifyContent: 'end' }}>
          <Button
            style={{ position: 'fixed', bottom: '10px', right: '10px' }}
            type="primary"
            onClick={() => {
              flattenPDF();
            }}
          >
            OK
          </Button>
          <Button
            style={{ position: 'fixed', bottom: '10px', right: '80px' }}
            onClick={() => {
              router.replace({
                query: _.omit(router.query, 'documentId')
              });
              window.close();
            }}
          >
            Cancel
          </Button>
        </Space>
      </Spin>
    </div>
  );
});
PdfTronTab.displayName = 'PdfTron';
export default PdfTronTab;
