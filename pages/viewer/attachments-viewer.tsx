import { AppContext } from '@components/context/AppContext';
import * as Gql from '@graphql';
import { Core } from '@pdftron/webviewer';
// import * as node from "../../public/assets/bina-location-icon.png";
// import TaskAddDrawingsDrawer from '@components/tasks/DrawingLinksAddDrawer';
import TaskAddDocsDrawer from '@components/tasks/TaskAddDocsAndRfiDrawer';
import TaskAddDrawer from '@components/tasks/TaskAddDrawer';
import TaskAddRFIDrawer from '@components/tasks/TaskAddPhotosDrawer';
import TaskEditDocsDrawer from '@components/tasks/TaskEditDocsAndRfiDrawer';
import TaskEditDrawer from '@components/tasks/TaskEditDrawer';
import TaskEditDrawingsDrawer from '@components/tasks/TaskEditDrawingsDrawer';
import TaskEditRFIDrawer from '@components/tasks/TaskEditPhotosDrawer';
import { onError } from '@utils/error';
import { But<PERSON>, Form, message, Modal, Space } from 'antd';
import _ from 'lodash';
import moment from 'moment';
import { useRouter } from 'next/router';
import { forwardRef, useContext, useEffect, useRef, useState } from 'react';

const PdfTronTab = () => {
  const router = useRouter();
  const documentId = router.query.documentId as any;
  const type = router.query.type as any;
  const viewerDiv = useRef<HTMLDivElement>(null);
  const [pdfTron, setPdfTron] = useState<any>();
  const context = useContext(AppContext);
  const [form] = Form.useForm();
  // const room = `project-document-room-${props.documentId}`
  const [getDoc, { data }] = Gql.useGetTasksAttachmentLazyQuery({ onError: onError as any });
  const [getDrawingLinkAttachmentDoc, { data: drawingLinkAttachmentsData }] = Gql.useGetDrawingLinkAttachmentLazyQuery({ onError: onError as any });
  const taskAddDrawerRef = useRef<any>(null);
  const taskEditDrawerRef = useRef<any>(null);
  const taskAddDrawingsDrawerRef = useRef<any>(null);
  const taskEditDrawingsDrawerRef = useRef<any>(null);
  const taskAddRFIDrawerRef = useRef<any>(null);
  const taskEditRFIDrawerRef = useRef<any>(null);
  const taskAddDocsDrawerRef = useRef<any>(null);
  const taskEditDocsDrawerRef = useRef<any>(null);
  const [annotId, setAnnotId] = useState('');
  const [annotType, setAnnotType] = useState('');
  const [taskId, setTaskId] = useState<any>();
  let storage = [] as any;

  function timeout(delay: number) {
    return new Promise(res => setTimeout(res, delay));
  }

  //Getting Backend Queries
  const { data: manyData, refetch: manyRefetch } = Gql.useGetTasksQuery({
    variables: {
      filter: {
        annotId: storage
      }
    },
    onError: onError as any
  });

  const annotTaskId = manyData?.tasks.nodes ?? ([] as any);

  //Checking types of icons
  useEffect(() => {
    const task = annotTaskId.some(function (obj: any) {
      if (obj.annotId === annotId) {
        setTaskId(obj.id);
        return obj;
      }
    });
    //Add Task Icons
    if (
      annotType === 'https://landing-page.obs.my-kualalumpur-1.alphaedge.tmone.com.my/EmailTemplatesImage/loc-icon.svg'
    ) {
      if (task) {
        taskEditDrawerRef?.current?.pushDrawer();
      } else {
        if (annotId === 'close') {
          form.resetFields();
        } else {
          taskAddDrawerRef?.current?.pushDrawer();
        }
      }
    }
    //Add Drawings Icons
    if (annotType === 'https://landing-page.obs.my-kualalumpur-1.alphaedge.tmone.com.my/EmailTemplatesImage/drw.svg') {
      if (task) {
        taskEditDrawingsDrawerRef?.current?.pushDrawer();
      } else {
        if (annotId === 'close') {
          form.resetFields();
        } else {
          taskAddDrawingsDrawerRef?.current?.pushDrawer();
        }
      }
    }
    //Add RFI Icons
    if (annotType === 'https://landing-page.obs.my-kualalumpur-1.alphaedge.tmone.com.my/EmailTemplatesImage/rfi.svg') {
      if (task) {
        taskEditRFIDrawerRef?.current?.pushDrawer();
      } else {
        if (annotId === 'close') {
          form.resetFields();
        } else {
          taskAddRFIDrawerRef?.current?.pushDrawer();
        }
      }
    }
    //Add Docs Icon
    if (annotType === 'https://landing-page.obs.my-kualalumpur-1.alphaedge.tmone.com.my/EmailTemplatesImage/docs.svg') {
      if (task) {
        taskEditDocsDrawerRef?.current?.pushDrawer();
      } else {
        if (annotId === 'close') {
          form.resetFields();
        } else {
          taskAddDocsDrawerRef?.current?.pushDrawer();
        }
      }
    }
  }, [annotId]);

  //Getting documentId
  useEffect(() => {
    if (documentId) {
      if (type === 'drawingLink') {
        getDrawingLinkAttachmentDoc({
          variables: {
            id: documentId
          }
        });
      } else {
        getDoc({
          variables: {
            id: documentId
          }
        });
      }
    }
  }, [documentId]);

  const file = type === 'drawingLink' ? drawingLinkAttachmentsData?.drawingLinkAttachment : data?.tasksAttachment;

  const [updateRequestSignatureXfdf] = Gql.useUpdateOneProjectDocumentMutation({
    onCompleted: () => {
      if (router.query.documentDetails) {
        router.replace({
          query: {
            ..._.omit(router.query, 'documentId'),
            documentDetailsId: router.query.documentDetails
          }
        });
      }
    },
    onError: (err: any) => {
      message.error(err.message);
    }
  });

  const saveXFDF = (xfdf: string) => {
    updateRequestSignatureXfdf({
      variables: {
        input: {
          id: _.toString(documentId),
          update: {
            xfdf
          }
        }
      }
    });
  };

  // Duplicated the edited file to All form
  const [duplicateEditedStandardForm] = Gql.useDuplicateEditedStandardFormMutation({
    onCompleted: () => {
      if (router.query.documentDetails) {
        router.replace({
          query: {
            ..._.omit(router.query, 'documentId'),
            documentDetailsId: router.query.documentId
          }
        });
      }
      message.success('File saved successfully in all form');
    },
    onError: (err: any) => {
      message.error(err.message);
    }
  });

  //Loading the document
  useEffect(() => {
    if (pdfTron && file?.fileUrl) {
      pdfTron?.UI.loadDocument(file?.fileUrl);
      pdfTron?.Core?.documentViewer.addEventListener('documentLoaded', () => {
        // pdfTron.Core?.annotationManager?.importAnnotations(file?.xfdf);
      });
    }
  }, [pdfTron, file?.fileUrl /*file?.xfdf*/]);

  // let imageName = 'https://bina-prod-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/drawing-link-icon/drawing-link-icon.png';

  useEffect(() => {
    if (!pdfTron) {
      import('@pdftron/webviewer').then(() => {
        // @ts-ignore
        WebViewer(
          {
            path: '/webviewer',
            licenseKey: process.env.NEXT_PUBLIC_PDFTRON_LICENSE_KEY,
            initialDoc: '',
            fullAPI: true
          },
          viewerDiv.current as HTMLDivElement
        ).then((instance: any) => {

          instance.UI.useEmbeddedPrint(true);
          instance.UI.setPrintQuality(5);

          //Docviewer declarations
          const iframeWindow = instance.UI.iframeWindow;
          const { docViewer } = instance;
          const { Tools, Annotations, annotationManager, documentViewer, PDFNet } = instance.Core;
          const panTool = Tools.ToolNames.PAN;
          let throttleTime: any;
          instance.UI.setToolbarGroup('toolbarGroup-View');

          const stamp = documentViewer.getTool('AnnotationCreateRubberStamp');
          const tool = documentViewer.getTool('AnnotationCreateSticky');
          // const task = documentViewer.getTool('AnnotationCreateFreeText');

          documentViewer.addEventListener('annotationsLoaded', () => {
            const annotations = annotationManager.getAnnotationsList();
            console.log(annotations);
            const map = annotations.map((id: any) => id.Id);
            Object.values(map).forEach(function (key) {
              storage.push(key);
            });
          });

          const style = instance.UI.iframeWindow.document.documentElement.style;

          /*************************************************************** PDFTRON UI CHANGES ********************************************************/
          style.setProperty(`--primary-button`, '#004b75');
          style.setProperty(`--primary-button-hover`, 'yellow');
          style.setProperty(`--view-header-icon-active-fill`, '#1A4971');
          style.setProperty(`--panel-background`, '#E9F0F4');
          style.setProperty(`--tools-header-background`, '#E9F0F4');
          style.setProperty(`--view-header-background`, '#175572');

          /*******************************************************************************************************************************************/

          // tool.setStandardStamps([
          //     'http://localhost/stamps/cbimage3.png',
          //     'http://landing-page.obs.my-kualalumpur-1.alphaedge.tmone.com.my/EmailTemplatesImage/bina-location-icon.png',
          // ])

          // CUSTOM ANNOTATIONS
          docViewer?.addEventListener('documentLoaded', function () {
            instance.UI.closeElements(['toggleNotes']);
            instance.UI.setToolMode(panTool);
            instance.UI.openElements(['toolbarGroup-View']);
            instance.UI.setZoomLevel('125%');
          });

          instance.UI.disableTools([
            Tools.ToolNames.UNDERLINE,
            Tools.ToolNames.UNDERLINE2,
            Tools.ToolNames.UNDERLINE3,
            Tools.ToolNames.UNDERLINE4
          ]);

          // instance.UI.updateElement('stickyToolGroupButton', {
          //     img: 'https://bina-prod-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/drawing-link-icon/drawing-link-icon.png',
          //     icon: 'https://bina-prod-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/drawing-link-icon/drawing-link-icon.png',
          //     title: 'Add Task'
          // })

          // instance.UI.updateElement('strikeoutToolGroupButton', {
          //     img: 'https://bina-prod-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/drawing-link-icon/drawing-link-icon.png',
          //     title: 'Add RFI'
          // })

          instance.UI.disableTools([
            Tools.ToolNames.STICKY,
            Tools.ToolNames.STICKY2,
            Tools.ToolNames.STICKY3,
            Tools.ToolNames.STICKY4
          ]);

          instance.UI.disableTools([
            Tools.ToolNames.STRIKEOUT,
            Tools.ToolNames.STRIKEOUT2,
            Tools.ToolNames.STRIKEOUT3,
            Tools.ToolNames.STRIKEOUT4
          ]);

          instance.UI.disableTools([
            Tools.ToolNames.SQUIGGLY,
            Tools.ToolNames.SQUIGGLY2,
            Tools.ToolNames.SQUIGGLY3,
            Tools.ToolNames.SQUIGGLY4
          ]);

          instance.UI.disableTools([
            Tools.ToolNames.HIGHLIGHT,
            Tools.ToolNames.HIGHLIGHT2,
            Tools.ToolNames.HIGHLIGHT3,
            Tools.ToolNames.HIGHLIGHT4
          ]);

          instance.UI.disableTools([
            Tools.ToolNames.MARK_INSERT_TEXT,
            Tools.ToolNames.MARK_INSERT_TEXT2,
            Tools.ToolNames.MARK_INSERT_TEXT3,
            Tools.ToolNames.MARK_INSERT_TEXT4
          ]);

          instance.UI.disableTools([
            Tools.ToolNames.MARK_REPLACE_TEXT,
            Tools.ToolNames.MARK_REPLACE_TEXT2,
            Tools.ToolNames.MARK_REPLACE_TEXT3,
            Tools.ToolNames.MARK_REPLACE_TEXT4
          ]);

          //   instance.UI.disableTools([
          //     Tools.ToolNames.RECTANGLE,
          //     Tools.ToolNames.RECTANGLE2,
          //     Tools.ToolNames.RECTANGLE3,
          //     Tools.ToolNames.RECTANGLE4
          //   ])

          //   instance.UI.disableTools([
          //     Tools.ToolNames.FREEHAND,
          //     Tools.ToolNames.FREEHAND2,
          //     Tools.ToolNames.FREEHAND3,
          //     Tools.ToolNames.FREEHAND4
          //   ])

          //   instance.UI.disableTools([
          //     Tools.ToolNames.FREEHAND_HIGHLIGHT,
          //     Tools.ToolNames.FREEHAND_HIGHLIGHT2,
          //     Tools.ToolNames.FREEHAND_HIGHLIGHT3,
          //     Tools.ToolNames.FREEHAND_HIGHLIGHT4
          //   ])

          // instance.UI.disableFeatures([Feature.Measurment, Feature.Copy]);

          // const ToolNames = instance.Core.Tools.ToolNames;
          instance.UI.disableElements(['toolbarGroup-Forms']);
          instance.UI.disableElements(['toolbarGroup-FillAndSign']);
          instance.UI.disableElements(['toolbarGroup-Shapes']);
          instance.UI.disableElements(['toolbarGroup-Edit']);
          instance.UI.disableElements(['toolbarGroup-Insert']);
          instance.UI.disableElements(['toolbarGroup-EditText']);
          instance.UI.disableElements(['toolbarGroup-Annotate']);
          instance.UI.disableElements(['notesPanel']);
          instance.UI.disableElements(['toggleNotesButton']);
          // instance.UI.disableElements(["toolbarGroup-Measure"]);

          // Moving sub menu item from one to another
          instance.UI.setHeaderItems(function (header: {
            getHeader: (arg0: string) => {
              (): any;
              new(): any;
              push: {
                (arg0: { type: string; toolGroup: string; dataElement: string; title: string }): void;
                new(): any;
              };
              delete: { (arg0: number): void; new(): any };
            };
          }) {
            header.getHeader('toolbarGroup-Annotate').push({
              type: 'toolGroupButton',
              toolGroup: 'cloudTools',
              dataElement: 'cloudToolGroupButton',
              title: 'Cloud'
            });
            header.getHeader('toolbarGroup-Annotate').push({
              type: 'toolGroupButton',
              toolGroup: 'arrowTools',
              dataElement: 'arrowToolGroupButton',
              title: 'Arrow'
            });
            header.getHeader('toolbarGroup-Annotate').push({
              type: 'toolGroupButton',
              toolGroup: 'fileAttachmentTools',
              dataElement: 'fileAttachmentToolGroupButton',
              title: 'File Attachment'
            });
            header.getHeader('toolbarGroup-Annotate').push({
              type: 'toolGroupButton',
              toolGroup: 'rubberStampTools',
              dataElement: 'rubberStampToolGroupButton',
              title: 'Add Stamp'
            });
            header.getHeader('toolbarGroup-Shapes').delete(5);
            setPdfTron(instance);
          });
          instance.UI.disableTools([]);
        });
      });
    }
  }, []);
  return (
    <div>
      <div className="webviewer mt-6" ref={viewerDiv} style={{ height: '91vh' }}></div>
      <Space style={{ width: '100%', justifyContent: 'start' }}>
        <Button
          style={{ position: 'fixed', bottom: '10px', right: '10px' }}
          onClick={() => {
            router.replace({
              query: _.omit(router.query, 'documentId')
            });
            Modal.confirm({
              title: 'Annotations will not be saved on cancel',
              content: 'Do you want to continue?',
              okText: 'Yes',
              cancelText: 'No',
              onOk: () => {
                window.close();
              }
            });
          }}
        >
          Cancel
        </Button>
      </Space>
      <TaskAddDrawer ref={taskAddDrawerRef} annotId={annotId} onSaved={() => manyRefetch()} />
      <TaskEditDrawer ref={taskEditDrawerRef} taskId={taskId} />
      {/* <TaskAddDrawingsDrawer ref={taskAddDrawingsDrawerRef} annotId={annotId} onSaved={() => manyRefetch()} /> */}
      <TaskEditDrawingsDrawer ref={taskEditDrawingsDrawerRef} taskId={taskId} />
      <TaskAddRFIDrawer ref={taskAddRFIDrawerRef} annotId={annotId} onSaved={() => manyRefetch()} />
      <TaskEditRFIDrawer ref={taskEditRFIDrawerRef} taskId={taskId} />
      <TaskAddDocsDrawer ref={taskAddDocsDrawerRef} annotId={annotId} onSaved={() => manyRefetch()} />
      <TaskEditDocsDrawer ref={taskEditDocsDrawerRef} taskId={taskId} />
    </div>
  );
};
PdfTronTab.displayName = 'PdfTron';
export default PdfTronTab;
