import { DownOutlined } from '@ant-design/icons';
import { Icon } from '@commons';
import { AppContext } from '@components/context/AppContext';
import CompareModal from '@components/drawings/2D-Drawings/CompareModal';
import DrawingLinksDrawer from '@components/tasks/DrawingLinksDrawer';
import * as Gql from '@graphql';
import { onError } from '@utils/error';
import { Button, Form, message, Modal, Select, Space } from 'antd';
import _, { omit, set } from 'lodash';
import { useRouter } from 'next/router';
import { useCallback, useContext, useEffect, useRef, useState } from 'react';

const PdfTronTab = () => {
  const router = useRouter();
  const documentId = router.query.documentId as any;
  const revisionId = router.query.revisionId as string;
  const dirId = router.query.dirId as string;
  const viewerDiv = useRef<HTMLDivElement>(null);
  const pdfTronInstance = useRef<any>(null);
  const [pdfTron, setPdfTron] = useState<any>();
  const [file, setFile] = useState<any | null>(null);
  const [form] = Form.useForm();
  const [revisions, setRevisions] = useState<any[]>([]);
  const [drawingLinkId, setDrawingLinkId] = useState<string>('');
  const [isOldVersion, setIsOldVersion] = useState(false);

  const [getDoc, { loading, data: ProjectDocument }] = Gql.useGetDrawingLazyQuery({
    onError: onError,
    onCompleted(data) {
      let currentFile: any;
      setRevisions(data?.projectDocument?.drawingRevisions ?? []);
      if (revisionId) {
        const revision = data?.projectDocument?.drawingRevisions?.find((revision: any) => revision.id === revisionId);
        if (revision) {
          currentFile = revision;
          setFile(revision);
        }
      } else {
        currentFile = data?.projectDocument;
        setFile(data?.projectDocument);
      }

      const highestVersion = data?.projectDocument?.drawingRevisions?.reduce((prev, current) => {
        return (prev?.version ?? 0) > (current?.version ?? 0) ? prev : current;
      });

      const isOldVersion = highestVersion?.id !== currentFile?.id && !currentFile?.drawingRevisions;
      setIsOldVersion(isOldVersion);
    }
  });

  const [getPdfTronIds, { data }] = Gql.useGetDrawingsPdfTronLazyQuery();

  // update drawing revision mutation
  const [updateDrawingRevision] = Gql.useUpdateDrawingRevisionMutation({
    onError: onError as any
  });

  const drawingLinksDrawer = useRef<any>(null);
  const compareModalRef = useRef<any>(null);
  const [annotId, setAnnotId] = useState('');
  const [annotType, setAnnotType] = useState('');
  const [activeTab, setActiveTab] = useState('view'); 
  let storage = [] as any;

  function timeout(delay: number) {
    return new Promise(res => setTimeout(res, delay));
  }

  async function saveStamp() {
    const signatureXfdf = await pdfTron?.Core.annotationManager.exportAnnotations({
      links: false,
      widgets: false
    });

    saveXFDF(signatureXfdf);
    // message.success("File saved successfully");
  }

  //Getting Backend Queries
  const { data: manyData, refetch: manyRefetch } = Gql.useGetDrawingLinksQuery({
    variables: {
      filter: {
        projectDocumentId: { eq: Number(documentId) }
      },
      paging: {
        limit: 99999
      }

    },
    skip: !documentId,
    onError: onError
  });

  const annotDrawingLinksId = manyData?.drawingLinks.nodes ?? ([] as any);  

  const handleDrawingLink = async () => {
    const getDrawingLinkId = await _.find(annotDrawingLinksId, { annotationId: annotId })?.id;
    
    if (getDrawingLinkId) {
      await setDrawingLinkId(getDrawingLinkId);
      drawingLinksDrawer?.current?.pushDrawer({ docId: getDrawingLinkId });
    } else {
      if (annotId === 'close') {
        form.resetFields();
      } else {
        drawingLinksDrawer?.current?.pushDrawer();
      }
    }
  };

  //Checking types of icons
  useEffect(() => {
    //Add Task Icons
    // if (
    //   annotType ===
    //   'https://bina-prod-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/Custom-Stamping/Drawings_Stamp%28100px%29/Task%20annotation%20final%20-%20pdftron%20150px%20X%20150px.png'
    // ) {
    //   //Task
    //   if (task) {
    //     taskEditDrawerRef?.current?.pushDrawer();
    //   } else {
    //     if (annotId === 'close') {
    //       form.resetFields();
    //     } else {
    //       taskAddDrawerRef?.current?.pushDrawer();
    //     }
    //   }
    // }
    //Add Drawings Icons    
    if (
      annotType ===
      'https://bina-prod-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/drawing-link-icon/drawing-link-icon.png'
    ) {
      //Drawing
      handleDrawingLink();
    }
    //Add RFI Icons
    // if (
    //   annotType ===
    //   'https://bina-prod-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/Custom-Stamping/Drawings_Stamp%28100px%29/Camera%20Annotation%20-%20Pdftron%20150px%20x%20150px.png'
    // ) {
    //   //Photos
    //   if (task) {
    //     taskEditPhotosDrawerRef?.current?.pushDrawer();
    //   } else {
    //     if (annotId === 'close') {
    //       form.resetFields();
    //     } else {
    //       taskAddPhotosDrawerRef?.current?.pushDrawer();
    //     }
    //   }
    // }
    //Add Docs Icon
    // if (
    //   annotType ===
    //   'https://bina-prod-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/Custom-Stamping/Drawings_Stamp%28100px%29/document%20annotation%20-%20pdftron%20150px%20X%20150px.png'
    // ) {
    //   //Docs and RFI
    //   if (task) {
    //     taskEditDocsAndRfiDrawerRef?.current?.pushDrawer();
    //   } else {
    //     if (annotId === 'close') {
    //       form.resetFields();
    //     } else {
    //       taskAddDocsAndRfiDrawerRef?.current?.pushDrawer();
    //     }
    //   }
    // }
  }, [annotId, annotDrawingLinksId, annotType, form]);

  useEffect(() => {
    if (documentId) {
      getDoc({
        variables: {
          id: documentId
        }
      });
      getPdfTronIds({
        variables: {
          input: {
            documentId: parseInt(documentId),
            folderId: parseInt(dirId)
          }
        }
      });
    }
  }, [dirId, documentId, getDoc, getPdfTronIds, revisionId]);

  const [updateRequestSignatureXfdf] = Gql.useUpdateOneProjectDocumentMutation({
    onCompleted: () => {
      if (router.query.documentDetails) {
        router.replace({
          query: {
            ..._.omit(router.query, 'documentId'),
            documentDetailsId: router.query.documentDetails
          }
        });
      }
    },
    onError: onError as any
  });

  const saveXFDF = async (xfdf: string) => {
    if (revisionId) {
      await updateDrawingRevision({
        variables: {
          input: {
            id: revisionId,
            update: {
              xfdf: xfdf
            }
          }
        }
      });
    } else {
      updateRequestSignatureXfdf({
        variables: {
          input: {
            id: _.toString(documentId),
            update: {
              xfdf: xfdf
            }
          }
        }
      });
    }
  };


  //Loading the document
  useEffect(() => {
    if (pdfTron && file?.fileUrl) {
      pdfTron?.UI.loadDocument(file?.fileUrl);
      pdfTron?.Core?.documentViewer.addEventListener('documentLoaded', async () => {
        // delete previous annotations
        const annotations = await pdfTron?.Core?.annotationManager.getAnnotationsList();
        pdfTron?.Core?.annotationManager.deleteAnnotations(annotations);

        pdfTron?.Core?.annotationManager.importAnnotations(file?.xfdf);
      });
    }
  }, [pdfTron, file]);

  useEffect(() => {
    if (!pdfTron) {
      import('@pdftron/webviewer').then(() => {
        // @ts-ignore
        WebViewer(
          {
            enableMeasurement: true,
            path: '/webviewer',
            licenseKey: process.env.NEXT_PUBLIC_PDFTRON_LICENSE_KEY,
            initialDoc: '',
            fullAPI: true,
            disableVirtualDisplayMode: true
          },
          viewerDiv.current as HTMLDivElement
        ).then((instance: any) => {
          //Docviewer declarations

          instance.UI.useEmbeddedPrint(true);
          instance.UI.setPrintQuality(5);

          pdfTronInstance.current = instance;
          const iframeWindow = instance.UI.iframeWindow;
          const origSetRubberStamp = instance.Core.Tools.RubberStampCreateTool.prototype.setRubberStamp;
          const { documentViewer } = instance.Core;
          const { docViewer } = instance;
          const { Tools, Annotations, annotationManager, PDFNet } = instance.Core;
          const panTool = Tools.ToolNames.PAN;
          const theme = instance.UI.Theme;
          // instance.UI.setTheme(theme.DARK);
          const measurementTools = [
            'AnnotationCreateDistanceMeasurement',
            'AnnotationCreatePerimeterMeasurement',
            'AnnotationCreateAreaMeasurement',
            'AnnotationCreateArcMeasurement',
            'AnnotationCreateRectangularAreaMeasurement'
          ];
          instance.UI.setToolbarGroup('toolbarGroup-View');
          const style = instance.UI.iframeWindow.document.documentElement.style;

          /*************************************************************** PDFTRON UI CHANGES ********************************************************/
          style.setProperty(`--primary-button`, '#004b75');
          style.setProperty(`--primary-button-hover`, 'yellow');
          style.setProperty(`--view-header-icon-active-fill`, '#1A4971');
          style.setProperty(`--panel-background`, '#E9F0F4');
          style.setProperty(`--tools-header-background`, '#E9F0F4');
          style.setProperty(`--view-header-background`, '#175572');

          /*******************************************************************************************************************************************/

          documentViewer.addEventListener('annotationsLoaded', () => {
            const annotations = annotationManager.getAnnotationsList();
            const map = annotations.map((id: any) => id.Id);
            Object.values(map).forEach(function (key) {
              storage.push(key);
            });
            manyRefetch();
          });

          documentViewer.addEventListener('documentLoaded', async () => {
            instance.UI.closeElements(['toggleNotes']);
            instance.UI.setToolMode();
            instance.UI.setToolMode(panTool);
            instance.UI.setZoomLevel(0.5);
            instance.UI.openElements(['toolbarGroup-View']);
            instance.UI.openElements(['leftPanel']);
            measurementTools.forEach(measurementTool => {
              instance?.Core?.documentViewer
                ?.getTool(measurementTool)
                ?.setSnapMode(instance.Core.Tools.SnapModes.DEFAULT);
            });
            instance.Core.annotationManager.setSnapDefaultOptions({
              indicatorColor: '#00a5e4'
            });
            instance.UI.setLayoutMode(instance.UI.LayoutMode.Single);
          });

            instance.UI.addEventListener('toolbarGroupChanged', (e: CustomEvent) => {
              const groupStr = String(e.detail).toLowerCase().replace('toolbargroup-', '');
              setActiveTab(prev => (prev === groupStr ? '' : groupStr));
            });
            
          documentViewer.getTool(instance.Core.Tools.ToolNames.RUBBER_STAMP).setStandardStamps([
            // 'https://bina-prod-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/Custom-Stamping/Drawings_Stamp%28100px%29/Task%20annotation%20final%20-%20pdftron%20150px%20X%20150px.png', //Task
            'https://bina-prod-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/drawing-link-icon/drawing-link-icon.png' //Drawing
            // 'https://bina-prod-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/Custom-Stamping/Drawings_Stamp%28100px%29/Camera%20Annotation%20-%20Pdftron%20150px%20x%20150px.png', //Photos
            // 'https://bina-prod-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/Custom-Stamping/Drawings_Stamp%28100px%29/document%20annotation%20-%20pdftron%20150px%20X%20150px.png' //Docs and RFI
          ]);

          instance.Core.Tools.RubberStampCreateTool.prototype.setRubberStamp = async function (annot: any, text: any) {
            if (annot.image && !annot.image.resized) {
              annot.image.width /= 3;
              annot.image.height /= 3;
              annot.image.resized = true;
            }
            await origSetRubberStamp.call(this, annot, text);
          };

          annotationManager.addEventListener('annotationSelected', (annotations: any, action: any) => {
            const annotation = annotations[0];

            if (action === 'selected') {
              setAnnotId(annotation.Id);
              setAnnotType(annotation.dk['trn-original-url']);
            } else if (action === 'deselected') {

              setAnnotId('close');
            }
          });

          instance.UI.updateTool('AnnotationCreateSticky', {
            buttonImage:
              'https://bina-prod-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/drawing-link-icon/drawing-link-icon.png'
            // Icon: customIcon,
          });

          instance.UI.updateElement('rubberStampToolGroupButton', {
            img: 'https://bina-prod-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/drawing-link-icon/drawing-link-icon.png',
            title: 'Add Link'
            // Icon: customIcon,
          });

          //Scroll To Mouse
          let throttleTime: any;
          let zoomLevel = 0.5;
          documentViewer.addEventListener('pageComplete', (pageNumber: any) => {
            const pageContainer = iframeWindow.document.getElementById('pageContainer' + pageNumber);
            pageContainer.addEventListener('wheel', (e: any) => {
              e.stopPropagation();
              e.preventDefault();

              //instance.setZoomLevel(instance.getZoomLevel() * 0.9)
              if (throttleTime) {
                return;
              } else {
                throttleTime = setTimeout(() => {
                  // change zoom level base on the direction of the mouse wheel
                  if (e.deltaY < 0) {
                    if (zoomLevel > 0.75) {
                      zoomLevel = zoomLevel + 0.3; // ScrollDownBig
                    } else {
                      zoomLevel = zoomLevel + 0.05; //ScrollDown
                    }
                  } else {
                    if (zoomLevel < 0.3) {
                      zoomLevel = zoomLevel - 0.01; //ScrollUpSmall
                    } else if (zoomLevel > 1) {
                      zoomLevel = zoomLevel - 0.3; //ScrollUpBig
                    } else {
                      zoomLevel = zoomLevel - 0.05; //ScrollUp
                    }
                  }
                  instance.Core.documentViewer.zoomToMouse(zoomLevel, 0, 0);
                  clearTimeout(throttleTime);
                  throttleTime = null;
                }, 1);
              }
            });
          });

          instance.UI.disableTools([
            Tools.ToolNames.UNDERLINE,
            Tools.ToolNames.UNDERLINE2,
            Tools.ToolNames.UNDERLINE3,
            Tools.ToolNames.UNDERLINE4
          ]);

          instance.UI.disableTools([
            Tools.ToolNames.FREETEXT,
            // Tools.ToolNames.FREETEXT2,
            Tools.ToolNames.FREETEXT3,
            Tools.ToolNames.FREETEXT4
          ]);

          instance.UI.disableTools([
            Tools.ToolNames.RECTANGLE,
            Tools.ToolNames.RECTANGLE2,
            // Tools.ToolNames.RECTANGLE3,
            Tools.ToolNames.RECTANGLE4
          ]);

          instance.UI.disableTools([
            Tools.ToolNames.FREEHAND,
            Tools.ToolNames.FREEHAND2,
            // Tools.ToolNames.FREEHAND3,
            Tools.ToolNames.FREEHAND4
          ]);

          instance.UI.disableTools([
            Tools.ToolNames.FREEHAND_HIGHLIGHT,
            Tools.ToolNames.FREEHAND_HIGHLIGHT2,
            // Tools.ToolNames.FREEHAND_HIGHLIGHT3,
            Tools.ToolNames.FREEHAND_HIGHLIGHT4
          ]);

          instance.UI.disableTools([
            Tools.ToolNames.POLYGON_CLOUD,
            Tools.ToolNames.POLYGON_CLOUD2,
            // Tools.ToolNames.POLYGON_CLOUD3,
            Tools.ToolNames.POLYGON_CLOUD4
          ]);

          instance.UI.disableTools([
            Tools.ToolNames.ARROW,
            Tools.ToolNames.ARROW2,
            // Tools.ToolNames.ARROW3,
            Tools.ToolNames.ARROW4
          ]);

          instance.UI.disableTools([
            Tools.ToolNames.STICKY,
            Tools.ToolNames.STICKY2,
            Tools.ToolNames.STICKY3,
            Tools.ToolNames.STICKY4
          ]);

          instance.UI.disableTools([
            Tools.ToolNames.STRIKEOUT,
            Tools.ToolNames.STRIKEOUT2,
            Tools.ToolNames.STRIKEOUT3,
            Tools.ToolNames.STRIKEOUT4
          ]);

          instance.UI.disableTools([
            Tools.ToolNames.SQUIGGLY,
            Tools.ToolNames.SQUIGGLY2,
            Tools.ToolNames.SQUIGGLY3,
            Tools.ToolNames.SQUIGGLY4
          ]);

          instance.UI.disableTools([
            Tools.ToolNames.HIGHLIGHT,
            Tools.ToolNames.HIGHLIGHT2,
            Tools.ToolNames.HIGHLIGHT3,
            Tools.ToolNames.HIGHLIGHT4
          ]);

          instance.UI.disableTools([
            Tools.ToolNames.MARK_INSERT_TEXT,
            Tools.ToolNames.MARK_INSERT_TEXT2,
            Tools.ToolNames.MARK_INSERT_TEXT3,
            Tools.ToolNames.MARK_INSERT_TEXT4
          ]);

          instance.UI.disableTools([
            Tools.ToolNames.MARK_REPLACE_TEXT,
            Tools.ToolNames.MARK_REPLACE_TEXT2,
            Tools.ToolNames.MARK_REPLACE_TEXT3,
            Tools.ToolNames.MARK_REPLACE_TEXT4
          ]);

          instance.UI.disableTools([
            Tools.ToolNames.DISTANCE_MEASUREMENT2,
            Tools.ToolNames.DISTANCE_MEASUREMENT3,
            Tools.ToolNames.DISTANCE_MEASUREMENT4
          ]);

          instance.UI.disableTools([
            Tools.ToolNames.PERIMETER_MEASUREMENT2,
            Tools.ToolNames.PERIMETER_MEASUREMENT3,
            Tools.ToolNames.PERIMETER_MEASUREMENT4
          ]);

          instance.UI.disableTools([
            Tools.ToolNames.AREA_MEASUREMENT2,
            Tools.ToolNames.AREA_MEASUREMENT3,
            Tools.ToolNames.AREA_MEASUREMENT4
          ]);

          instance.UI.disableTools([
            Tools.ToolNames.ARC_MEASUREMENT2,
            Tools.ToolNames.ARC_MEASUREMENT3,
            Tools.ToolNames.ARC_MEASUREMENT4
          ]);

          instance.UI.disableTools([
            Tools.ToolNames.ELLIPTICAL_AREA_MEASUREMENT2,
            Tools.ToolNames.ELLIPTICAL_AREA_MEASUREMENT4,
            Tools.ToolNames.ELLIPTICAL_AREA_MEASUREMENT3
          ]);

          instance.UI.disableTools([
            Tools.ToolNames.RECTANGULAR_AREA_MEASUREMENT2,
            Tools.ToolNames.RECTANGULAR_AREA_MEASUREMENT4,
            Tools.ToolNames.RECTANGULAR_AREA_MEASUREMENT3
          ]);

          instance.UI.disableTools([
            Tools.ToolNames.COUNT_MEASUREMENT2,
            Tools.ToolNames.COUNT_MEASUREMENT3,
            Tools.ToolNames.COUNT_MEASUREMENT4
          ]);

          // instance.UI.disableFeatures([Feature.Measurment, Feature.Copy]);

          // const ToolNames = instance.Core.Tools.ToolNames;
          instance.UI.disableElements(['toolbarGroup-Forms']);
          instance.UI.disableElements(['toolbarGroup-FillAndSign']);
          instance.UI.disableElements(['toolbarGroup-Shapes']);
          instance.UI.disableElements(['toolbarGroup-Edit']);
          instance.UI.disableElements(['toolbarGroup-Insert']);
          instance.UI.disableElements(['toolbarGroup-EditText']);
          instance.UI.disableElements(['notesPanel']);
          instance.UI.disableElements(['toggleNotesButton']);
          instance.UI.disableElement('customStampPanelButton');
          instance.UI.disableElement('thumbDelete');
          instance.UI.disableElements(['richTextPopup']);
          instance.UI.disableElements(['downloadButton', 'rotatePageCounterClockwise', 'deletePage']);
          instance.UI.disableElements(['signaturePanelButton', 'outlinesPanelButton']);
          instance.UI.disableElements(['annotationCommentButton', 'linkButton']);
          // instance.UI.disableElements(["toolbarGroup-Measure"]);

          // Moving sub menu item from one to another
          instance.UI.setHeaderItems(function (header: {
            getHeader: (arg0: string) => {
              (): any;
              new(): any;
              push: {
                (arg0: { type: string; toolGroup: string; dataElement: string; title: string }): void;
                new(): any;
              };
              delete: { (arg0: number): void; new(): any };
            };
          }) {
            header.getHeader('toolbarGroup-Annotate').push({
              type: 'toolGroupButton',
              toolGroup: 'cloudTools',
              dataElement: 'cloudToolGroupButton',
              title: 'Cloud'
            });
            header.getHeader('toolbarGroup-Annotate').push({
              type: 'toolGroupButton',
              toolGroup: 'arrowTools',
              dataElement: 'arrowToolGroupButton',
              title: 'Arrow'
            });
            header.getHeader('toolbarGroup-Annotate').push({
              type: 'toolGroupButton',
              toolGroup: 'rubberStampTools',
              dataElement: 'rubberStampToolGroupButton',
              title: 'Add Stamp'
            });
            // header.getHeader('toolbarGroup-Annotate').push({ type: 'toolGroupButton', toolGroup: 'fileAttachmentTools', dataElement: 'fileAttachmentToolGroupButton', title: 'File Attachment' });
            header.getHeader('toolbarGroup-Shapes').delete(5);

            // header.getHeader('toolbarGroup-Annotate').push(stamp2GroupButton)
            // header.getHeader('toolbarGroup-Annotate').push(stampGroupButton)
            // header.getHeader('toolbarGroup-Annotate').push(stamp2GroupButton)
            setPdfTron(instance);
          });
          instance.UI.disableTools([]);
          // instance.UI.disableTools(Object.values(ToolNames) as any);
        });
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const onCompare = async (versionId: string) => {
    // Find the revision with the id
    const selectedRevision = revisions?.find(revision => revision.id === versionId);
    const fileUrl = selectedRevision?.fileUrl;
    const currentDocumentUrl = file?.fileUrl; // Assuming this is the URL of the current document

    if (!fileUrl || !currentDocumentUrl) {
      return;
    }

    const instance = pdfTronInstance.current;
    if (!instance) {
      return;
    }

    const { Core } = instance;
    const { Annotations } = Core;
    const { documentViewer } = Core;

    await Core.PDFNet.initialize(); // Initialize the PDFNet

    documentViewer.addEventListener('documentLoaded', () => {
      instance.UI.setLayoutMode(instance.UI.LayoutMode.FacingContinuous);
    });

    const doc1 = await Core.PDFNet.PDFDoc.createFromURL(currentDocumentUrl);
    const doc2 = await Core.PDFNet.PDFDoc.createFromURL(fileUrl);

    const newDoc = await Core.PDFNet.PDFDoc.create();
    await newDoc.lock();

    await newDoc.appendTextDiffDoc(doc1, doc2);

    await newDoc.unlock();

    documentViewer.loadDocument(newDoc); // Load the comparison result
  };

  useEffect(() => {
    const instance = pdfTronInstance.current;
    if (!instance) {
      return;
    }

    const { Core } = instance;
    const { Annotations, annotationManager } = Core;
    const { documentViewer } = Core;

    const addAnnotations = async () => {
      await Core.PDFNet.initialize(); // Initialize the PDFNet

      const pageCount = documentViewer.getPageCount();
      const stampWidth = 800;
      const stampHeight = 800;
      const annotationsToAdd = [];

      for (let i = 1; i <= pageCount; i++) {
        const pageHeight = documentViewer.getPageHeight(i);
        const pageWidth = documentViewer.getPageWidth(i);

        const stampAnnot = new Annotations.StampAnnotation();
        stampAnnot.PageNumber = i;
        stampAnnot.X = (pageWidth - stampWidth) / 2;
        stampAnnot.Y = (pageHeight - stampHeight) / 2;
        stampAnnot.Width = stampWidth;
        stampAnnot.Height = stampHeight;
        stampAnnot.ImageData = '/assets/obsolete.png';
        stampAnnot.Author = annotationManager.getCurrentUser();
        stampAnnot.ReadOnly = true;
        stampAnnot.Printable = false;

        annotationsToAdd.push(stampAnnot);
      }

      annotationsToAdd.forEach(annot => annotationManager.addAnnotation(annot));
    };

    const handleDocumentLoaded = async () => {
      if (isOldVersion) {
        await addAnnotations();
      }
    };

    // Adding the event listener
    documentViewer.addEventListener('documentLoaded', handleDocumentLoaded);

    // Cleanup function to remove the event listener
    return () => {
      documentViewer.removeEventListener('documentLoaded', handleDocumentLoaded);
    };
  }, [isOldVersion, pdfTronInstance]);

  return (
    <div>
      <div className="webviewer mt-6" ref={viewerDiv} style={{ height: '98vh' }}></div>
      {!loading ? (
        <Space
          style={{
            width: '100%',
            justifyContent: 'space-between',
            position: 'absolute',
            bottom: 10,
            padding: '0 10px 0 18%'
          }}
        >
          <div className="flex">
          {revisions.length > 0 && (
              <Select
                className="w-[210px] flex items-center justify-center ml-[80px] mb-[0px]"
                value={revisionId || undefined} // Don't pre-fill anything unless selected
                placeholder="Select Revision"
                suffixIcon={<DownOutlined style={{ color: '#374151' }} />} 
                style={{ color: '#374151' }}
                onChange={async (value: any) => {
                  if (value === 'Compare') {
                    return compareModalRef?.current?.openModal();
                  }

                  const selectedRevision = revisions.find((revision: any) => revision.id === value);
                  router.push({
                    pathname: router.pathname,
                    query: {
                      ...router.query,
                      documentId: selectedRevision.projectDocumentId,
                      revisionId: selectedRevision.id,
                    },
                  });
                }}
              >
                {revisions
                  .map((revision: any) => (
                    <Select.Option key={revision.id} value={revision.id}>
                      {`${revision.versionName} | ID: ${revision.projectDocumentId}`}
                    </Select.Option>
                  ))
                  .reverse()}
              </Select>
            )}
            <span className="text-gray-100 font-medium w-[300px] flex items-center justify-center mb-[0px] ml-[160px]">
              {ProjectDocument?.projectDocument?.name ?? documentId ?? "Don't have file name"}
            </span>
            <div className="flex gap-3 ml-5 m-[0px]">
              <Button
                className="w-[35px] pt-[4px] px-[0px] h-[35px] mb-[3px]"
                onClick={() => {
                  const previousDocumentId = data?.getDrawingsPdfTron?.previousId;
                  if (previousDocumentId) {
                    const newQuery = omit(router.query, 'revisionId');

                    // Replace the route with the new query params
                    router.replace({
                      pathname: router.pathname,
                      query: {
                        ...newQuery,
                        documentId: previousDocumentId
                      }
                    });
                  } else {
                    return message.error('No previous document');
                  }
                }}
              >
                <Icon name="left-arrow" height={25} width={30} />
              </Button>
              <Button
                className="w-[35px] pt-[4px] px-[0px] h-[35px] pl-[3px]"
                onClick={() => {
                  const nextDocumentId = data?.getDrawingsPdfTron?.nextId;
                  if (nextDocumentId) {
                    const newQuery = omit(router.query, 'revisionId');
                    router.replace({
                      pathname: router.pathname,
                      query: {
                        ...newQuery,
                        documentId: nextDocumentId
                      }
                    });
                  } else {
                    return message.error('No next document');
                  }
                }}
              >
                <Icon name="right-arrow" height={25} width={30} />
              </Button>
            </div>
          </div> 
          <div className="flex gap-5" style={{ display: activeTab === 'view' ? 'none' : 'flex' }}
          >
            <Button
              type="primary"
              onClick={async () => {
                const annotations = await pdfTron.Core.annotationManager.getAnnotationsList();
                const obsoleteAnnotations = annotations?.filter?.((annot: any) => annot?.Subject === 'Obsolete');
                await pdfTron?.Core?.annotationManager?.deleteAnnotations(obsoleteAnnotations);

                const signatureXfdf = await pdfTron.Core.annotationManager.exportAnnotations({
                  links: false,
                  widgets: false
                });

                // After export, re-add the obsolete annotations so they remain visible in the viewer
                await pdfTron.Core.annotationManager.addAnnotations(obsoleteAnnotations);
                await pdfTron.Core.annotationManager.drawAnnotationsFromList(obsoleteAnnotations);

                saveXFDF(signatureXfdf);
                message.success('File saved successfully');
                await timeout(1000);
                window.close();
              }}
            >
              Publish
            </Button>
            <Button
              onClick={() => {
                Modal.confirm({
                  title: 'Annotations will not be saved on cancel',
                  content: 'Do you want to continue?',
                  okText: 'Yes',
                  cancelText: 'No',
                  onOk: () => {
                    window.close();
                    router.replace({
                      query: _.omit(router.query, 'documentId')
                    });
                  }
                });
              }}
            >
              Cancel
            </Button>
          </div>
        </Space>
      ) : null}
      <DrawingLinksDrawer
        ref={drawingLinksDrawer}
        annotId={annotId}
        docId={drawingLinkId}
        refetch={manyRefetch}
        onClose={async () => {

          await setAnnotId('close');
          await setDrawingLinkId('');
        }}
        onSaved={async () => {

          await manyRefetch();
          await setDrawingLinkId('');
          await setAnnotId('close');
        }}
        pdfSaved={() => saveStamp()}
        onCancel={async () => {

          const hasAnotId = annotDrawingLinksId?.find((annot: any) => annot.annotationId === annotId);
          if (!hasAnotId) {
            const annotations = await pdfTron.Core.annotationManager.getAnnotationsList();
            const getCurrentAnnot = annotations?.filter?.((annot: any) => annot.ik === annotId);
            pdfTron?.Core.annotationManager.deleteAnnotation(getCurrentAnnot);
          }
        }}
      />
      <CompareModal ref={compareModalRef} versions={revisions} onCompare={onCompare} />
      
    </div>
  );
};
PdfTronTab.displayName = 'PdfTron';
export default PdfTronTab;
