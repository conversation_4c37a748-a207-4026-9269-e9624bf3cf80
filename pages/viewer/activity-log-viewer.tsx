import { Icon } from '@commons';
import { Drawer } from '@commons/Drawer';
import { IconNames } from '@constants';
import * as Gql from '@graphql';
import { AuditLogModuleType } from '@graphql';
import { onError } from '@utils/error';
import { Divider, DrawerProps, Timeline } from 'antd';
import Paragraph from 'antd/lib/typography/Paragraph';
import _ from 'lodash';
import moment from 'moment';
import { useRouter } from 'next/router';
import React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import { Collapse } from 'react-collapse';
import InfiniteScroll from 'react-infinite-scroll-component';


interface Props {
  onSaved?: () => void;
  resourceId?: string;
}

export interface DrawerRef extends DrawerProps {
  pushDrawer: (v?: any) => void;
}

const ActivityLogViewer = forwardRef<DrawerRef, Props>((props, ref) => {
  const { resourceId } = props;
  const drawerRef = useRef<any>(null);
  const router = useRouter();
  const { documentId } = router.query;
  const [projectId, setProjectId] = useState<string | null>(null);
  const [data, setData] = useState<Gql.ActivityLogFieldsFragment[]>([]);
  const [paging, setPaging] = useState<any>({ offset: 0, limit: 10 });

  const boldWords = [
    'FREE',
    'TEXT',
    'SIGNATURE',
    'STAMP',
    'HAND',
    'TICK',
    'UNDERLINE',
    'HIGHLIGHT',
    'RECTANGLE',
    'SQUIGGLY',
    'STRIKE',
    'OUT',
    'REPLACE',
    'CLOUD',
    'ARROW',
    'ERASER',
    'CALENDAR',
    'IMAGE',
    'STRIKEOUT',
    'DATE',
    'FREEHAND HIGHLIGHT',
    'FREEHAND',
    'ROTATED',
    'INSERTED',
    'DELETED'
  ];

  useImperativeHandle(ref, () => ({
    pushDrawer: () => {
      drawerRef?.current?.pushDrawer();
      setProjectId(localStorage.getItem('ProjectId'));
    }
  }));

  // get audit logs query
  const {
    data: getAuditLog
  } = Gql.useGetActivityLogsQuery({
    variables: {
      paging,
      filter: {
        projectId: { eq: projectId as string },
        module: { eq: AuditLogModuleType.DocumentEditor },
        resourceId: { eq: (resourceId || documentId) as string }
      },
      sorting: [
        {
          direction: Gql.SortDirection.Desc,
          field: Gql.AuditLogSortFields.CreatedAt
        }
      ]
    },
    onCompleted: res => {
      setData((prev: Gql.ActivityLogFieldsFragment[]) => [...(prev || []), ...res?.auditLogs?.nodes]);
    },
    skip: !projectId || !(resourceId || documentId),
    onError: onError
  });

  const [showMoreStates, setShowMoreStates] = useState(Array(data?.length).fill(false));
  const handleToggleShowMore = (index: any) => {
    const newShowMoreStates = [...showMoreStates];
    newShowMoreStates[index] = !newShowMoreStates[index];
    setShowMoreStates(newShowMoreStates);
  };

  const loadMoreData = () => {
    const newPaging = { ...paging };
    newPaging.offset = newPaging.offset + newPaging.limit;
    setPaging(newPaging);
  };

  return (
    <Drawer ref={drawerRef} {...props} width={500} className="p-0">

      <div
        className=" w-full "
        id="scrollableDiv"
        style={{
          overflow: 'auto',
          padding: '0px',
          border: '0px solid rgba(140, 140, 140, 0.35)'
        }}
      >
        <InfiniteScroll
          className="p-4"
          dataLength={data.length || 0}
          height={700}
          next={loadMoreData}
          hasMore={getAuditLog?.auditLogs?.pageInfo?.hasNextPage || false}
          loader={''}
          endMessage={data?.length > 0 ? <Divider plain>End of activity logs</Divider> : <Divider plain>No activity logs</Divider>}
          scrollableTarget="scrollableDiv"
        >
          <Timeline >
            {data?.map((item, index) => {
              const isLastItem = index === data.length - 1;
              console.log('isLastItem', isLastItem);
              const name = item?.user?.name;

              const isShowMore = showMoreStates[index];

              const displayBoldString = () => {
                return item?.content?.replace(/\b(\w+)\b/g, function (match: any) {
                  return boldWords.includes(match) ? `<b>${match}</b>` : match;
                });
              };

              let icon: IconNames = 'signature';

              if (item?.content?.includes('STAMP') || item?.content?.includes('SIGNATURE') || item?.content?.includes('IMAGE') || item?.content?.includes('TICK') || item?.content?.includes('DATE')) {
                icon = 'signature';
              } else {
                icon = 'free-text';
              }

              const text = item?.text?.split('~');

              return (

                <Timeline.Item key={index} dot={<Icon name={icon} height={20} />} color="green">
                  <div className="text-base text-[#585757]">
                    <span className="text-[#0695D7]">{name}</span>
                    <span
                      dangerouslySetInnerHTML={{ __html: _.replace(displayBoldString() || '', name || '', '') }}
                    ></span>
                  </div>

                  <div className="text-gray70 text-base font-medium">
                    {moment(item.createdAt).format('DD MMM YYYY [at] hh:mm a')}
                  </div>

                  {text && text?.length > 0 && (
                    <div>
                      {' '}
                      <Collapse isOpened={isShowMore}>
                        {text?.map((item: string, index: number) => {
                          return (
                            <div className="flex ml-3 " key={index}>
                              <span className="text-gray70 ">&bull; </span>
                              <Paragraph className="" ellipsis={item?.length > 30 ? { rows: 2, tooltip: item } : false}>
                                <div className="text-gray70 text-base ml-1 mt-[2px]"> {item}</div>
                              </Paragraph>
                            </div>
                          );
                        })}
                      </Collapse>
                      {isShowMore ? (
                        <p onClick={() => handleToggleShowMore(index)} className="text-base cursor-pointer text-gray70">
                          <Icon name="chevron-up" width={10} height={7} /> Hide{' '}
                        </p>
                      ) : (
                        <p
                          onClick={() => handleToggleShowMore(index)}
                          className="text-base cursor-pointer text-gray70"
                        >
                          <Icon name="chevron-right"  /> Show more
                        </p>
                      )}
                    </div>
                  )}
                </Timeline.Item>

              );
            })}
          </Timeline>
        </InfiniteScroll>
      </div>
    </Drawer >
  );
});

ActivityLogViewer.displayName = 'ActivityLogViewer';

export default ActivityLogViewer;
