import { AppContext } from '@components/context/AppContext';
import LinkedToPdftron from '@components/LinkedToPdfTron';
import { getAnnotationMessage } from '@constants/annotationMessage';
import * as Gql from '@graphql';
import { onError } from '@utils/error';
import { Button, message, Modal, ModalProps, Space, Spin, Tabs } from 'antd';
import axios from 'axios';
import _, { filter, set } from 'lodash';
import moment from 'moment';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import { AppLayoutContext } from 'pages/_app';
import { forwardRef, useContext, useEffect, useRef, useState } from 'react';
import { UserAuthService } from 'src/api';
import { Url } from 'url';
import ActivityLogViewer from './activity-log-viewer';

interface Props {
  onSaved?: () => void;
  onSavedXFDF?: (values: any) => void;
  documentId?: string;
}

const PdfTronTab = forwardRef<Props>((props: any) => {
  const router = useRouter();
  const { data: session, status } = useSession();
  const documentId = router.query.documentId as any;
  const projectId = router.query.projectId as any;
  const companyId = router.query.companyId as any;
  const viewerDiv = useRef<HTMLDivElement>(null);
  const [pdfTron, setPdfTron] = useState<any>();
  const [getDoc, { data }] = Gql.useProjectDocumentLazyQuery({ onError: onError as any });
  const setUserRole = async () => {
    const role = await UserAuthService.currentRole({});
    localStorage.setItem('ProjectUserRole', role);
  };
  const isUser = !!session?.user;
  const { data: userMeData, loading: loadingUser } = Gql.useGetUserMeQuery({});
  const dateFreeText = ['DATE_FREETEXT', 'DATEFREETEXT2', 'DATEFREETEXT3', 'DATEFREETEXT4'];
  const freeText = ['FREETEXT', 'FREETEXT2', 'FREETEXT3', 'FREETEXT4'];
  const [logArray, setLogArray] = useState<any[]>([]);
  // query text size from db
  const { data: textSize } = Gql.useGetFontSizeQuery({});
  const fontSize = textSize?.getUserMe?.fontSize;
  const activityLogDrawerRef = useRef<any>(null);
  const [saveLoading, setSaveLoading] = useState(false);

  const [createAuditLog, { error }] = Gql.useCreateManyAuditLogsMutation({
    onCompleted: () => {},
    onError: onError as any
  });

  const linkedToPdfTronRef = useRef<any>(null);

  useEffect(() => {
    if (status === 'loading') return; // Do nothing while loading
    if (!isUser) {
      router.push({
        pathname: '/login',
        query: { redirect: router.asPath }
      });
    }
  }, [isUser, status]);

  const [linkPdftron, { loading: loadingMerge }] = Gql.useLinkPdftronDocumentMutation({
    onCompleted: async data => {
      const xfdfString = await pdfTron.Core.annotationManager.exportAnnotations();

      setTimeout(async () => {
        await pdfTron?.UI.loadDocument(data?.linkPdftronDocument?.url);
        pdfTron.Core.documentViewer.addEventListener('documentLoaded', async () => {
          await pdfTron.Core.annotationManager.importAnnotations(xfdfString);
          pdfTron.UI.openElements(['leftPanel']);
        });
      }, 1000);
    },
    onError: onError as any
  });

  function timeout(delay: number) {
    return new Promise(res => setTimeout(res, delay));
  }

  function blobToFile(theBlob: any, fileName: any) {
    theBlob.lastModifiedDate = new Date();
    theBlob.name = fileName;
    return theBlob;
  }

  async function getStampImage(url: string) {
    if (!url) {
      return null;
    }

    try {
      const response = await axios.get(`/api/download-stamp?url=${encodeURIComponent(url ?? '')}`, {
        responseType: 'arraybuffer'
      });

      // Convert the array buffer to base64
      const base64Data = Buffer.from(response.data, 'binary').toString('base64');

      // Prepend the prefix to the base64 encoded string
      const imageDataURI = `data:image/png;base64,${base64Data}`;

      return imageDataURI;
    } catch (error) {
      // fail silently if error in fetching image
      console.error('Error fetching image:', error);
      return null;
    }
  }

  async function convertSignUrltoBase64(url: string) {
    const response = await fetch(url);
    const blob = await response.blob();

    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onloadend = () => {
        // The result will be a base64-encoded string
        const base64String = reader.result as string;
        resolve(base64String);
      };

      reader.onerror = () => {
        reject(new Error('Error reading the blob data.'));
      };

      reader.readAsDataURL(blob);
    });
  }

  const handleMergePdfs = async (urls: string[]): Promise<any> => {
    urls.unshift(data?.projectDocument?.fileUrl as string);
  
    if (!data?.projectDocument?.fileUrl) {
      message.error('Document not found');
      throw new Error('Document not found');
    }
  
    return await linkPdftron({
      variables: {
        input: {
          title: data?.projectDocument?.name ?? '',
          urls: urls
        }
      }
    });
  };

  async function flattenPDF() {
    setSaveLoading(true);

    const updateFreeTextData = logArray.filter(log => log.content?.includes('FREE TEXT') && log.action === 'Update');

    const stampAndSignatureData = logArray.filter(log => !log.content?.includes('FREE TEXT'));

    // Combine 'text' values into a comma-separated string
    let combinedText = updateFreeTextData.map(update => update.text).join('~');

    let updateResult = null;
    if (updateFreeTextData.length > 0) {
      // Create the desired object
      updateResult = {
        action: updateFreeTextData[0].action,
        content: updateFreeTextData[0].content,
        module: updateFreeTextData[0].module,
        projectId: updateFreeTextData[0].projectId,
        resourceId: updateFreeTextData[0].resourceId,
        text: combinedText,
        userId: updateFreeTextData[0].userId
      };
    }

    const toSend = [...stampAndSignatureData, ...(updateResult ? [updateResult] : [])];
    // return
    if (toSend?.length > 0) {
      await createAuditLog({ variables: { input: { auditLogs: toSend } } });
    }

    const annotations = await pdfTron?.Core.annotationManager.getAnnotationsList();
    if (annotations && annotations.length > 0) {
      await pdfTron.UI.setToolbarGroup(['toolbarGroup-Forms']);
      const styleData = {
        //Change Form to white background
        FillColor: new pdfTron.Core.Annotations.Color(0, 0, 0, 0),
        StrokeColor: new pdfTron.Core.Annotations.Color(255, 255, 255),
        // TextColor: new pdfTron.Core.Annotations.Color(0, 0, 0),
        BackgroundColor: new pdfTron.Core.Annotations.Color(255, 255, 255),
        StrokeThickness: 0
      };
      //Filter Out form annotations
      for (const annot of annotations) {
        if (
          annot?.ToolName === 'SignatureFormFieldCreateTool' ||
          annot?.ToolName === 'TextFormFieldCreateTool' ||
          (annot?.ToolName === 'AnnotationCreateFreeText' && annot.Subject === 'Free Text') || //some text editor outside of pdftron has the same ToolName
          annot?.ToolName === 'CheckBoxFormFieldCreateTool' ||
          annot?.ToolName === 'RadioButtonFormFieldCreateTool' ||
          annot?.ToolName === 'ListBoxFormFieldCreateTool' ||
          annot?.ToolName === 'ComboBoxFormFieldCreateTool' ||
          annot instanceof pdfTron.Core.Annotations.WidgetAnnotation
        ) {
          await pdfTron?.Core.annotationManager.setAnnotationStyles(annot, styleData);

          if (annot instanceof pdfTron.Core.Annotations.WidgetAnnotation) {
            annot.backgroundColor = new pdfTron.Core.Annotations.Color(0, 0, 0, 0); // RGBA for transparent
            annot.StrokeThickness = 0;
            await pdfTron.Core.annotationManager.redrawAnnotation(annot);
          }
        }
      }
      await pdfTron.UI.setToolbarGroup(['toolbarGroup-View']);
    }

    // await pdfTron?.Core.documentViewer.refreshAll();
    await pdfTron?.Core.documentViewer.updateView();
    await pdfTron?.Core.documentViewer.getDocument().refreshTextData();

    const doc2 = pdfTron?.Core?.documentViewer.getDocument();
    const xfdfString = await pdfTron.Core?.annotationManager?.exportAnnotations();
    const options = { xfdfString, flatten: true };
    const data = await doc2.getFileData(options);
    const arr = new Uint8Array(data);
    const blob = new Blob([arr], { type: 'application/pdf' });
    const convert = blobToFile(blob, file?.name);

    await onUpdate(convert);

    // pdfTron.docViewer?.refreshAll();
    pdfTron.docViewer?.updateView();
    pdfTron.docViewer?.getDocument().refreshTextData();
  }

  async function updateAnnotations() {
    setSaveLoading(true);
    if (logArray.length > 0) {
      await createAuditLog({ variables: { input: { auditLogs: logArray } } });
    }
    const signatureXfdf = await pdfTron?.Core.annotationManager.exportAnnotations({
      links: true,
      widgets: true
    });
    saveXFDF(signatureXfdf);
  }

  //Getting documentId
  useEffect(() => {
    if (documentId && userMeData) {
      getDoc({ variables: { id: documentId ?? '' } }).then(data => {
        setupPDF(data.data?.projectDocument?.name);
      });
    }
  }, [userMeData, documentId]);

  const file = data?.projectDocument;

  const [updateRequestSignatureXfdf, { loading: draftLoading }] = Gql.useUpdateOneProjectDocumentMutation({
    onCompleted: async () => {
      message.success('File saved successfully');
      await timeout(2000);
      window.close();
      setSaveLoading(false);
    },
    onError: () => {
      setSaveLoading(false);
      onError as any;
    }
  });

  const saveXFDF = (xfdf: string) => {
    updateRequestSignatureXfdf({
      variables: {
        input: {
          id: _.toString(documentId),
          update: {
            xfdf
          }
        }
      }
    });
  };

  const [updateFile, { loading }] = Gql.useUpdateOneProjectDocumentMutation({
    onCompleted: async () => {
      message.success('File saved successfully');
      await timeout(2000);
      window.close();
      setSaveLoading(false);
    },
    onError: () => {
      setSaveLoading(false);
      onError as any;
    }
  });

  const [switchCompany, { loading: switchingCompany }] = Gql.useSwitchCompanyMutation({
    onCompleted: () => {},
    onError: e => {}
  });

  const onUpdate = (files: any) => {
    updateFile({
      variables: {
        input: {
          id: _.toString(documentId),
          update: {
            fileUrl: files,
            xfdf: null
          }
        }
      }
    });
  };

  // Duplicated the edited file to All form
  const [duplicateEditedStandardForm] = Gql.useDuplicateEditedStandardFormMutation({
    onCompleted: () => {
      if (router.query.documentDetails) {
        router.replace({
          query: {
            ..._.omit(router.query, 'documentId'),
            documentDetailsId: router.query.documentId
          }
        });
      }
      message.success('File saved successfully in all form');
    },
    onError: (err: any) => {
      message.error(err.message);
    }
  });

  //Loading the document
  useEffect(() => {
    if (companyId && projectId && documentId && !isNaN(Number(props.projectId))) {
      // set project-id only if it is a number
      localStorage.setItem('ProjectId', projectId);
    }
    if (pdfTron && file?.fileUrl && userMeData) {
      const role = localStorage.getItem('ProjectUserRole');
      pdfTron?.UI.loadDocument(file?.fileUrl);
      pdfTron?.Core?.documentViewer.addEventListener('documentLoaded', async () => {
        // setSaveLoading(true);
        freeText.forEach(element => {
          const tool = pdfTron?.Core?.documentViewer.getTool(pdfTron?.Core?.Tools.ToolNames[element]);
          if (tool) {
            tool.defaults.FontSize = `${fontSize}pt`;
          }
        });

        const companyStampUrl = await getStampImage(userMeData?.getUserMe?.company?.stampUrl as string);
        const stampUrl = await getStampImage(userMeData?.getUserMe?.stampUrl as string);
        const stampAndSignUrl = await getStampImage(userMeData?.getUserMe?.stampAndSignUrl as string);
        const signUrl = await getStampImage(userMeData?.getUserMe?.signUrl as string);

        pdfTron?.Core?.documentViewer
          .getTool(pdfTron?.Core?.Tools?.ToolNames?.RUBBER_STAMP)
          .setStandardStamps([
            role === 'ProjectOwner' || role === 'CloudCoordinator' ? companyStampUrl : null,
            stampUrl,
            stampAndSignUrl
          ]);

        pdfTron?.Core?.documentViewer.getTool('AnnotationCreateSignature').importSignatures([signUrl ?? '']);
        pdfTron?.Core?.documentViewer.getTool('AnnotationCreateSignature').setDefaultSignatureOptions({
          maximumDimensionSize: 75
        });
        await pdfTron.Core?.annotationManager?.importAnnotations(file?.xfdf);
        const annotations = await pdfTron?.Core.annotationManager.getAnnotationsList();
        if (annotations && annotations.length > 0) {
          await pdfTron.UI.setToolbarGroup(['toolbarGroup-Forms']);
          const styleData = {
            FillColor: new pdfTron.Core.Annotations.Color(222, 227, 255),
            StrokeColor: new pdfTron.Core.Annotations.Color(255, 255, 255),
            // TextColor: new pdfTron.Core.Annotations.Color(0, 0, 0),
            StrokeThickness: 0
          };
          const filteredAnnotsBox = await annotations.filter((annot: any) => annot.Subject === 'Rectangle');
          await filteredAnnotsBox.forEach(async (annot: any) => {
            if (annot.ToolName === 'AnnotationCreateRectangle') return;
            if (annot instanceof pdfTron.Core.Annotations.SignatureWidgetAnnotation) {
              return;
            }
            await pdfTron?.Core.annotationManager.setAnnotationStyles(annot, styleData);

            if (annot instanceof pdfTron.Core.Annotations.WidgetAnnotation) {
              annot.backgroundColor = new pdfTron.Core.Annotations.Color(222, 227, 255); // RGBA for transparent
              await pdfTron.Core.annotationManager.redrawAnnotation(annot);
            }
          });
          // await pdfTron.UI.setToolbarGroup(['toolbarGroup-FillAndSign']);

          await pdfTron?.Core.documentViewer.refreshAll();
          await pdfTron?.Core.documentViewer.updateView();
          await pdfTron?.Core.documentViewer.getDocument().refreshTextData();
          await pdfTron.UI.setToolbarGroup(['toolbarGroup-View']);
        }
      });

      setSaveLoading(false);
    } else if (pdfTron && userMeData && projectId && userMeData.getUserMe.companyId !== companyId) {
      if (userMeData.getUserMe.companyId !== companyId) {
        Modal.confirm({
          title: 'Different Projects/Companies',
          content: `Are you sure you want to switch Projects/Companies?`,
          okText: 'Yes',
          cancelText: 'No',
          onCancel: async () => {
            window.close();
          },
          onOk: async () => {
            await switchCompany({
              variables: {
                companyId: parseInt(companyId)
              }
            });
            await setUserRole();
            window.location.reload();
          }
        });
      } else if (userMeData.getUserMe.companyId === companyId && !file?.fileUrl) {
        Modal.info({
          title: 'Document not found',
          content: `This document has been deleted, do you want to go back?`,
          okText: 'Yes',

          onOk: async () => {
            window.close();
          }
        });
        return;
      }
    }
  }, [pdfTron, file?.fileUrl, userMeData]);

  function setupPDF(fileName?: string | null) {
    if (!pdfTron) {
      import('@pdftron/webviewer').then(() => {
        // @ts-ignore
        WebViewer(
          {
            path: '/webviewer',
            licenseKey: process.env.NEXT_PUBLIC_PDFTRON_LICENSE_KEY,
            initialDoc: '',
            fullAPI: true,
            disableVirtualDisplayMode: true
          },
          viewerDiv.current as HTMLDivElement
        ).then(async (instance: any) => {
          instance.UI.useEmbeddedPrint(true);
          instance.UI.setPrintQuality(5);

          instance.UI.pageManipulationOverlay.add([
            {
              type: 'customPageOperation',
              header: 'Page Attachment',
              dataElement: 'customPageOperations',
              operations: [
                {
                  title: 'Import from Bina Cloud',
                  img: '/assets/Link.svg',
                  onClick: () => linkedToPdfTronRef?.current?.openModal(),
                  dataElement: 'customPageOperationButton'
                }
              ]
            },
            { type: 'divider' }
          ]);

          instance.UI.setHeaderItems((header: any) => {
            header.push({
              type: 'actionButton',
              img:
                '<svg width="28" height="26" viewBox="0 0 28 26" fill="none" xmlns="http://www.w3.org/2000/svg">' +
                '<path d="M23.4022 1.71045H4.96485C3.03715 1.71045 1.46875 3.17782 1.46875 4.982V15.5406C1.46875 17.3407 3.03032 18.8057 4.95243 18.8121V23.6035L12.3108 18.8121H23.4022C25.3299 18.8121 26.8983 17.3445 26.8983 15.5406V4.982C26.8983 3.17782 25.3299 1.71045 23.4022 1.71045ZM25.4083 15.5406C25.4083 16.5756 24.5084 17.4178 23.4022 17.4178H11.8433L6.44245 20.9347V17.4178H4.96485C3.85867 17.4178 2.95876 16.5756 2.95876 15.5406V4.982C2.95876 3.94672 3.85867 3.10476 4.96485 3.10476H23.4022C24.5084 3.10476 25.4083 3.94672 25.4083 4.982V15.5406Z" fill="#56798B" />' +
                '<path d="M8.27539 6.63721H20.0928V8.03152H8.27539V6.63721Z" fill="#56798B"/>' +
                '<path d="M8.27539 9.61157H20.0928V11.0059H8.27539V9.61157Z" fill="#56798B"/>' +
                '<path d="M8.27539 12.5862H20.0928V13.9805H8.27539V12.5862Z" fill="#56798B"/>' +
                '</svg>',
              onClick: () => {
                activityLogDrawerRef.current?.pushDrawer();
              }
            });
          });

          const origSetRubberStamp = instance.Core.Tools.RubberStampCreateTool.prototype.setRubberStamp;
          const { Tools, documentViewer, PDFNet, annotationManager, Annotations } = instance.Core;
          const panTool = Tools.ToolNames.PAN;
          const style = instance.UI.iframeWindow.document.documentElement.style;

          /*************************************************************** PDFTRON UI CHANGES ********************************************************/
          style.setProperty(`--primary-button`, '#004b75');
          style.setProperty(`--primary-button-hover`, 'yellow');
          style.setProperty(`--view-header-icon-active-fill`, '#1A4971');
          style.setProperty(`--panel-background`, '#E9F0F4');
          style.setProperty(`--tools-header-background`, '#E9F0F4');
          style.setProperty(`--view-header-background`, '#175572');

          /*******************************************************************************************************************************************/

          dateFreeText.forEach(element => {
            documentViewer.getTool(instance.Core.Tools.ToolNames[element]).setDateFormat('DD-MM-YYYY');
          });

          const textCreateTool = documentViewer.getTool('AnnotationCreateFreeText');
          const dateCreateTool = documentViewer.getTool(Tools.ToolNames.DATE_FREETEXT);
          instance.UI.setToolbarGroup('toolbarGroup-View');
          textCreateTool.defaults.TextColor = new Annotations.Color(0, 0, 0);
          dateCreateTool.defaults.TextColor = new Annotations.Color(0, 0, 0);
          // textCreateTool.defaults.FontSize = `${fontSize}pt`;
          instance.UI.setZoomLevel('150%');
          instance.UI.setToolMode(panTool);
          instance.UI.openElements(['leftPanel']);

          const pageRotations: Record<number, number> = {};

          documentViewer.addEventListener('pagesUpdated', (changes: any) => {
            changes.contentChanged.forEach((pageNumber: number) => {
              const newRotation = documentViewer.getDocument().getPageRotation(pageNumber);
              const oldRotation = pageRotations[pageNumber];
              if (newRotation !== oldRotation) {
                const newLogObject = {
                  action: Gql.AuditLogActionType.Update,
                  module: Gql.AuditLogModuleType.DocumentEditor,
                  resourceId: documentId,
                  userId: userMeData?.getUserMe.id as any,
                  projectId: localStorage.getItem('ProjectId') as any,
                  content: `${userMeData?.getUserMe.name} ROTATED at ${fileName} | Page: ${pageNumber}`
                };
                setLogArray(prevLogArray => {
                  return [...prevLogArray, newLogObject];
                });
              }
            });

            if (changes?.added?.length > 0 || changes?.removed?.length > 0) {
              const isInsert = changes?.added?.length > 0;
              const action = isInsert ? changes?.added[0] : changes?.removed[0];

              const newLogObject = {
                action: isInsert ? Gql.AuditLogActionType.Add : Gql.AuditLogActionType.Delete,
                module: Gql.AuditLogModuleType.DocumentEditor,
                resourceId: documentId,
                userId: userMeData?.getUserMe.id as any,
                projectId: localStorage.getItem('ProjectId') as any,
                content: `${userMeData?.getUserMe.name} ${
                  isInsert ? 'INSERTED' : 'DELETED'
                } page in ${fileName} | Page: ${action}`
              };
              setLogArray(prevLogArray => {
                return [...prevLogArray, newLogObject];
              });
            }
          });

          annotationManager.addEventListener('annotationChanged', async (annotations: any, action: any) => {
            if (
              (annotations?.[0]?.Subject === 'Rectangle' &&
                annotations?.[0]?.ToolName !== 'AnnotationCreateRectangle') ||
              annotations?.[0]?.Subject === 'Widget'
            )
              return;
            const momentDateModified = moment(annotations[0]?.DateModified);
            if (
              action !== 'delete' &&
              (annotations.length === 0 ||
                annotations[0].Dya === 'Insert text here' ||
                momentDateModified.isBefore(moment().subtract(10, 'seconds')))
            ) {
              return;
            }

            const [annotationMessage, textMessage] = annotMessage(annotations);
            const actionMessage = actMessage(action);
            let actionLog;
            if (action === 'delete') actionLog = Gql.AuditLogActionType.Delete;
            else if (action === 'modify' && textMessage !== '') actionLog = Gql.AuditLogActionType.Update;
            else if (action === 'add') actionLog = Gql.AuditLogActionType.Add;
            else return;

            const newLogObject = {
              action: actionLog,
              module: Gql.AuditLogModuleType.DocumentEditor,
              resourceId: documentId,
              userId: userMeData?.getUserMe.id as any,
              projectId: localStorage.getItem('ProjectId') as any,
              content: `${
                userMeData?.getUserMe.name
              } ${actionMessage} ${annotationMessage?.toUpperCase()} at ${fileName} | Page: ${
                annotations[0].PageNumber
              }`,
              text: textMessage
            };

            setLogArray(prevLogArray => {
              if (action === 'modify' && prevLogArray.some(log => log.text === newLogObject.text)) {
                return prevLogArray;
              }
              return [...prevLogArray, newLogObject];
            });
          });

          instance.Core.Tools.RubberStampCreateTool.prototype.setRubberStamp = async function (annot: any, text: any) {
            if (annot.image.width >= 650) {
              annot.image.width /= 11;
              annot.image.height /= 11;
              annot.image.resized = true;
            } else if (annot.image.width >= 450) {
              annot.image.width /= 4;
              annot.image.height /= 4;
              annot.image.resized = true;
            }
            await origSetRubberStamp.call(this, annot, text);
          };

          instance.UI.disableTools([
            // Tools.ToolNames.RUBBER_STAMP,
            Tools.ToolNames.FILEATTACHMENT,
            Tools.ToolNames.CALLOUT,
            Tools.ToolNames.CALLOUT2,
            Tools.ToolNames.CALLOUT3,
            Tools.ToolNames.CALLOUT4,
            Tools.ToolNames.FORM_FILL_CROSS,
            Tools.ToolNames.FORM_FILL_DOT,
            Tools.ToolNames.CALENDER,
            Tools.ToolNames.DOWNLOAD
          ]);

          /* ANNOTATE TOOLBAR DISABLING */
          instance.UI.disableElements(['stickyToolGroupButton']);
          instance.UI.disableElements(['toolbarGroup-Forms']);
          instance.UI.disableElements(['toolbarGroup-Shapes']);
          instance.UI.disableElements(['toolbarGroup-Edit']);
          // instance.UI.disableElements(['toolbarGroup-Annotate']);
          instance.UI.disableElements(['toolbarGroup-Insert']);
          instance.UI.disableElements(['toolbarGroup-Edit']);
          instance.UI.disableElements(['downloadButton']);
          instance.UI.disableElements(['richTextPopup']);
          instance.UI.disableElements(['annotationCommentButton', 'linkButton']);
          instance.UI.disableElement('customStampPanelButton');
          instance.UI.disableElements(['toggleNotesButton']);
          instance.UI.disableElements(['rotatePageCounterClockwise', 'deletePage']);
          instance.UI.disableElements(['defaultSignatureDeleteButton']);
          // instance.UI.disableElements(['imageSignaturePanelButton', 'textSignaturePanelButton']);
          
          //Hide add signature button;
          instance.UI.setCreateSignatureButton(false);

          instance.UI.setHeaderItems(function (header: {
            getHeader: (arg0: string) => {
              (): any;
              new (): any;
              push: {
                (arg0: { type: string; toolGroup: string; dataElement: string; title: string }): void;
                new (): any;
              };
              delete: { (arg0: number): void; new (): any };
            };
          }) {
            header.getHeader('toolbarGroup-FillAndSign').push({
              type: 'toolGroupButton',
              toolGroup: 'stampTools',
              dataElement: 'stampToolGroupButton',
              title: 'Image'
            });
            header.getHeader('toolbarGroup-Annotate').push({
              type: 'toolGroupButton',
              toolGroup: 'cloudTools',
              dataElement: 'cloudToolGroupButton',
              title: 'Cloud'
            });
            header.getHeader('toolbarGroup-Annotate').push({
              type: 'toolGroupButton',
              toolGroup: 'arrowTools',
              dataElement: 'arrowToolGroupButton',
              title: 'Arrow'
            });
            setPdfTron(instance);
          });
        });
      });
    }
  }

  return (
    <div>
      <Spin spinning={loading || draftLoading || saveLoading} tip={'loading...'}>
        <div className="webviewer" ref={viewerDiv} style={{ height: '97vh' }}></div>
        {pdfTron && data?.projectDocument?.projectId && (
          <LinkedToPdftron
            ref={linkedToPdfTronRef}
            defaultFileList={[]}
            onChange={async urls => handleMergePdfs(urls)}
            projectId={data?.projectDocument?.projectId}
            loadingMerge={loadingMerge}
          />
        )}
        <Space style={{ width: '100%', justifyContent: 'end' }}>
          {data?.projectDocument?.status !== Gql.ProjectDocumentStatus?.Approved ? (
            <Button
              type="primary"
              size="middle"
              className=" flex items-center justify-center h-9 pb-2 rounded-md"
              style={{ position: 'fixed', bottom: '10px', right: '100px' }}
              onClick={async () => {
                await flattenPDF();
              }}
              loading={saveLoading}
            >
              Save
            </Button>
          ) : null}

          <Button
            className=" flex items-center justify-center h-9 pb-2 rounded-md"
            style={{ position: 'fixed', bottom: '10px', right: '10px' }}
            onClick={() => {
              router.replace({
                query: _.omit(router.query, 'documentId')
              });
              window.close();
            }}
          >
            Dismiss
          </Button>
          { data?.projectDocument?.workflow === 'Dynamic' && (
            <Button
              type="primary"
              // size='small'
              className="text-white bg-[#0298EB] border-none rounded-md h-9 pb-2 flex items-center justify-center"
              style={{ position: 'fixed', bottom: '10px', right: '170px' }}
              onClick={async () => {
                await updateAnnotations();
              }}
              loading={saveLoading}
            >
              Save as Draft
            </Button>
          )}
        </Space>
      </Spin>
      <ActivityLogViewer ref={activityLogDrawerRef} />
    </div>
  );
  function annotMessage(annotation: any) {
    let annotationLog = annotation[0]?.Subject;
    let annotationMessage;
    annotationMessage = getAnnotationMessage(annotation[0]?.ToolName, annotation[0]?.Subject);
    const textMessage = annotationMessage === 'Text' ? annotation?.[0]?.Dya : null;
    return [annotationMessage, textMessage];
  }
  function actMessage(action: any) {
    let actionMessage;
    if (action === 'add') actionMessage = 'added';
    else if (action === 'delete') actionMessage = 'deleted';
    else if (action === 'modify') actionMessage = 'modified';
    return actionMessage;
  }
});

PdfTronTab.displayName = 'PdfTron';
export default PdfTronTab;
