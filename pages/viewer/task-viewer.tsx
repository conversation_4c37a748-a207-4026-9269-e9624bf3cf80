import { ExclamationCircleOutlined } from '@ant-design/icons';
import * as Gql from '@graphql';
import { useReceiveMemoMutation } from '@graphql';
import { onError } from '@utils/error';
import { Button, message, Modal, Space } from 'antd';
import _ from 'lodash';
import { useRouter } from 'next/router';
import { useEffect, useRef, useState } from 'react';
const { confirm } = Modal;

const PdfTronTab = () => {
  const router = useRouter();
  const documentId = router.query.documentId as any;
  const revisionId = router.query.revisionId as string;
  const isPreview = router.query.preview as string;
  const dirId = router.query.dirId as string;
  const viewerDiv = useRef<HTMLDivElement>(null);
  const pdfTronInstance = useRef<any>(null);
  const [pdfTron, setPdfTron] = useState<any>();
  const [file, setFile] = useState<any | null>(null);
  const { data: userData } = Gql.useGetUserMeQuery({
    onError: onError as any
  });

  const {
    loading,
    data: taskData,
    refetch
  } = Gql.useGetTaskQuery({
    onError: onError,
    onCompleted: data => {
      if (isPreview === 'true') {
        return setFile(data?.task?.previewMemoUrl);
      }
      setFile(data?.task?.memoUrl);
    },
    variables: {
      id: documentId
    },
    skip: !documentId
  });

  const [updateTask, { loading: isGenerating }] = Gql.useUpdateOneTaskMutation({
    onCompleted: () => {
      message.success('Memo generated successfully');
      setTimeout(() => {
        window.close();
      }, 1000);
    }
  });

  let storage = [] as any;

  const isAssignee = taskData?.task?.assignees?.nodes?.some((data: any) => data?.userId === userData?.getUserMe?.id);

  //Loading the document
  useEffect(() => {
    if (pdfTron && file) {
      pdfTron?.UI.loadDocument(file);
      pdfTron?.Core?.documentViewer.addEventListener('documentLoaded', async () => {
        // delete previous annotations
        const annotations = await pdfTron?.Core?.annotationManager.getAnnotationsList();
        pdfTron?.Core?.annotationManager.deleteAnnotations(annotations);

        pdfTron?.Core?.annotationManager.importAnnotations(file?.xfdf);
      });
    }
  }, [pdfTron, file]);

  useEffect(() => {
    if (!pdfTron) {
      import('@pdftron/webviewer').then(() => {
        // @ts-ignore
        WebViewer(
          {
            enableMeasurement: true,
            path: '/webviewer',
            licenseKey: process.env.NEXT_PUBLIC_PDFTRON_LICENSE_KEY,
            initialDoc: '',
            fullAPI: true,
            disableVirtualDisplayMode: true
          },
          viewerDiv.current as HTMLDivElement
        ).then((instance: any) => {
          //Docviewer declarations

          instance.UI.useEmbeddedPrint(true);
          instance.UI.setPrintQuality(5);

          pdfTronInstance.current = instance;
          const iframeWindow = instance.UI.iframeWindow;
          const origSetRubberStamp = instance.Core.Tools.RubberStampCreateTool.prototype.setRubberStamp;
          const { documentViewer } = instance.Core;
          const { Tools, annotationManager } = instance.Core;
          const panTool = Tools.ToolNames.PAN;
          const measurementTools = [
            'AnnotationCreateDistanceMeasurement',
            'AnnotationCreatePerimeterMeasurement',
            'AnnotationCreateAreaMeasurement',
            'AnnotationCreateArcMeasurement',
            'AnnotationCreateRectangularAreaMeasurement'
          ];
          instance.UI.setToolbarGroup('toolbarGroup-View');
          const style = instance.UI.iframeWindow.document.documentElement.style;

          /*************************************************************** PDFTRON UI CHANGES ********************************************************/
          style.setProperty(`--primary-button`, '#004b75');
          style.setProperty(`--primary-button-hover`, 'yellow');
          style.setProperty(`--view-header-icon-active-fill`, '#1A4971');
          style.setProperty(`--panel-background`, '#E9F0F4');
          style.setProperty(`--tools-header-background`, '#E9F0F4');
          style.setProperty(`--view-header-background`, '#175572');

          /*******************************************************************************************************************************************/

          documentViewer.addEventListener('annotationsLoaded', () => {
            const annotations = annotationManager.getAnnotationsList();
            const map = annotations.map((id: any) => id.Id);
            Object.values(map).forEach(function (key) {
              storage.push(key);
            });
          });

          documentViewer.addEventListener('documentLoaded', async () => {
            instance.UI.closeElements(['toggleNotes']);
            instance.UI.setToolMode();
            instance.UI.setToolMode(panTool);
            instance.UI.setZoomLevel('100%');
            instance.UI.openElements(['toolbarGroup-View']);
            instance.UI.openElements(['leftPanel']);
            measurementTools.forEach(measurementTool => {
              instance?.Core?.documentViewer
                ?.getTool(measurementTool)
                ?.setSnapMode(instance.Core.Tools.SnapModes.DEFAULT);
            });
            instance.Core.annotationManager.setSnapDefaultOptions({
              indicatorColor: '#00a5e4'
            });
            instance.UI.setLayoutMode(instance.UI.LayoutMode.Single);
          });

          instance.Core.Tools.RubberStampCreateTool.prototype.setRubberStamp = async function (annot: any, text: any) {
            if (annot.image && !annot.image.resized) {
              annot.image.width /= 3;
              annot.image.height /= 3;
              annot.image.resized = true;
            }
            await origSetRubberStamp.call(this, annot, text);
          };

          instance.UI.updateTool('AnnotationCreateSticky', {
            buttonImage:

              'https://bina-prod-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/drawing-link-icon/drawing-link-icon.png'
            // Icon: customIcon,

          });

          instance.UI.disableTools([
            Tools.ToolNames.UNDERLINE,
            Tools.ToolNames.UNDERLINE2,
            Tools.ToolNames.UNDERLINE3,
            Tools.ToolNames.UNDERLINE4
          ]);

          instance.UI.disableTools([
            Tools.ToolNames.FREETEXT,
            // Tools.ToolNames.FREETEXT2,
            Tools.ToolNames.FREETEXT3,
            Tools.ToolNames.FREETEXT4
          ]);

          instance.UI.disableTools([
            Tools.ToolNames.RECTANGLE,
            Tools.ToolNames.RECTANGLE2,
            // Tools.ToolNames.RECTANGLE3,
            Tools.ToolNames.RECTANGLE4
          ]);

          instance.UI.disableTools([
            Tools.ToolNames.FREEHAND,
            Tools.ToolNames.FREEHAND2,
            // Tools.ToolNames.FREEHAND3,
            Tools.ToolNames.FREEHAND4
          ]);

          instance.UI.disableTools([
            Tools.ToolNames.FREEHAND_HIGHLIGHT,
            Tools.ToolNames.FREEHAND_HIGHLIGHT2,
            // Tools.ToolNames.FREEHAND_HIGHLIGHT3,
            Tools.ToolNames.FREEHAND_HIGHLIGHT4
          ]);

          instance.UI.disableTools([
            Tools.ToolNames.POLYGON_CLOUD,
            Tools.ToolNames.POLYGON_CLOUD2,
            // Tools.ToolNames.POLYGON_CLOUD3,
            Tools.ToolNames.POLYGON_CLOUD4
          ]);

          instance.UI.disableTools([
            Tools.ToolNames.ARROW,
            Tools.ToolNames.ARROW2,
            // Tools.ToolNames.ARROW3,
            Tools.ToolNames.ARROW4
          ]);

          instance.UI.disableTools([
            Tools.ToolNames.STICKY,
            Tools.ToolNames.STICKY2,
            Tools.ToolNames.STICKY3,
            Tools.ToolNames.STICKY4
          ]);

          instance.UI.disableTools([
            Tools.ToolNames.STRIKEOUT,
            Tools.ToolNames.STRIKEOUT2,
            Tools.ToolNames.STRIKEOUT3,
            Tools.ToolNames.STRIKEOUT4
          ]);

          instance.UI.disableTools([
            Tools.ToolNames.SQUIGGLY,
            Tools.ToolNames.SQUIGGLY2,
            Tools.ToolNames.SQUIGGLY3,
            Tools.ToolNames.SQUIGGLY4
          ]);

          instance.UI.disableTools([
            Tools.ToolNames.HIGHLIGHT,
            Tools.ToolNames.HIGHLIGHT2,
            Tools.ToolNames.HIGHLIGHT3,
            Tools.ToolNames.HIGHLIGHT4
          ]);

          instance.UI.disableTools([
            Tools.ToolNames.MARK_INSERT_TEXT,
            Tools.ToolNames.MARK_INSERT_TEXT2,
            Tools.ToolNames.MARK_INSERT_TEXT3,
            Tools.ToolNames.MARK_INSERT_TEXT4
          ]);

          instance.UI.disableTools([
            Tools.ToolNames.MARK_REPLACE_TEXT,
            Tools.ToolNames.MARK_REPLACE_TEXT2,
            Tools.ToolNames.MARK_REPLACE_TEXT3,
            Tools.ToolNames.MARK_REPLACE_TEXT4
          ]);

          instance.UI.disableTools([
            Tools.ToolNames.DISTANCE_MEASUREMENT2,
            Tools.ToolNames.DISTANCE_MEASUREMENT3,
            Tools.ToolNames.DISTANCE_MEASUREMENT4
          ]);

          instance.UI.disableTools([
            Tools.ToolNames.PERIMETER_MEASUREMENT2,
            Tools.ToolNames.PERIMETER_MEASUREMENT3,
            Tools.ToolNames.PERIMETER_MEASUREMENT4
          ]);

          instance.UI.disableTools([
            Tools.ToolNames.AREA_MEASUREMENT2,
            Tools.ToolNames.AREA_MEASUREMENT3,
            Tools.ToolNames.AREA_MEASUREMENT4
          ]);

          instance.UI.disableTools([
            Tools.ToolNames.ARC_MEASUREMENT2,
            Tools.ToolNames.ARC_MEASUREMENT3,
            Tools.ToolNames.ARC_MEASUREMENT4
          ]);

          instance.UI.disableTools([
            Tools.ToolNames.ELLIPTICAL_AREA_MEASUREMENT2,
            Tools.ToolNames.ELLIPTICAL_AREA_MEASUREMENT4,
            Tools.ToolNames.ELLIPTICAL_AREA_MEASUREMENT3
          ]);

          instance.UI.disableTools([
            Tools.ToolNames.RECTANGULAR_AREA_MEASUREMENT2,
            Tools.ToolNames.RECTANGULAR_AREA_MEASUREMENT4,
            Tools.ToolNames.RECTANGULAR_AREA_MEASUREMENT3
          ]);

          instance.UI.disableTools([
            Tools.ToolNames.COUNT_MEASUREMENT2,
            Tools.ToolNames.COUNT_MEASUREMENT3,
            Tools.ToolNames.COUNT_MEASUREMENT4
          ]);

          instance.UI.disableElements(['toolbarGroup-Forms']);
          instance.UI.disableElements(['toolbarGroup-FillAndSign']);
          instance.UI.disableElements(['toolbarGroup-Shapes']);
          instance.UI.disableElements(['toolbarGroup-Edit']);
          instance.UI.disableElements(['toolbarGroup-Insert']);
          instance.UI.disableElements(['toolbarGroup-EditText', 'toolbarGroup-Annotate', 'toolbarGroup-Measure']);
          instance.UI.disableElements(['notesPanel']);
          instance.UI.disableElements(['toggleNotesButton']);
          instance.UI.disableElement('customStampPanelButton');
          instance.UI.disableElement('thumbDelete');
          instance.UI.disableElements(['richTextPopup']);
          instance.UI.disableElements(['downloadButton', 'rotatePageCounterClockwise', 'deletePage']);
          instance.UI.disableElements(['signaturePanelButton', 'outlinesPanelButton']);
          instance.UI.disableElements(['annotationCommentButton', 'linkButton']);
          // instance.UI.disableElements(["toolbarGroup-Measure"]);

          // Moving sub menu item from one to another
          instance.UI.setHeaderItems(function (header: {
            getHeader: (arg0: string) => {
              (): any;
              new(): any;
              push: {
                (arg0: { type: string; toolGroup: string; dataElement: string; title: string }): void;
                new(): any;
              };
              delete: { (arg0: number): void; new(): any };
            };
          }) {
            header.getHeader('toolbarGroup-Shapes').delete(5);

            setPdfTron(instance);
          });
          instance.UI.disableTools([]);
        });
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  //receive Memo mutation
  const [receiveMemo, { loading: updating }] = useReceiveMemoMutation({
    onError: onError as any,
    onCompleted: () => {
      refetch();
      message.success('Memo received successfully');
    }
  });

  const handleReceiveMemo = () => {
    if (!isAssignee) {
      message.error('You are not assignee of this memo');
      return;
    }

    confirm({
      title: 'Confirm Receive Memo',
      icon: <ExclamationCircleOutlined />,
      content: (
        <>
          <div>Are you sure want to receive memo?</div>
        </>
      ),
      okText: 'Yes',
      okType: 'danger',
      cancelText: 'No',
      onOk() {
        receiveMemo({
          variables: {
            input: {
              taskId: Number(taskData?.task?.id)
            }
          }
        });
      },
      onCancel() { }
    });
  };

  return (
    <div>
      <div className="webviewer mt-6" ref={viewerDiv} style={{ height: '98vh' }}></div>
      {!loading ? (
        <Space
          style={{
            width: '100%',
            justifyContent: 'space-between',
            position: 'absolute',
            bottom: 10,
            padding: '0 10px 0 18%'
          }}
        >
          {isPreview === 'true' && (
            <div className="absolute right-[110px] bottom-2">
              <Button
                type="primary"
                onClick={() => {
                  return updateTask({
                    variables: {
                      id: documentId,
                      input: {
                        memoUrl: file
                      }
                    }
                  });
                }}
                loading={isGenerating}
              >
                Generate
              </Button>
            </div>
          )}
          {!taskData?.task?.isMemoReceive && isAssignee && isPreview !== 'true' && (
            <div className="absolute right-[100px] bottom-2">
              <Button
                type="primary"
                onClick={() => {
                  if (userData?.getUserMe?.signUrl && userData?.getUserMe?.stampUrl) {
                    handleReceiveMemo();
                  } else {
                    Modal.confirm({
                      title: 'Signature/Stamp not found',
                      content: 'Please upload your signature and stamp before generating memo.',
                      okText: 'Upload Signature And Stamp',
                      cancelText: 'Cancel',
                      onOk: () => {
                        router.push('/settings/profile');
                      }
                    });
                  }
                }}
                loading={updating}
              >
                Receive
              </Button>
            </div>
          )}
          <div className="absolute right-5 bottom-2">
            <Button onClick={() => window.close()}>Close</Button>
          </div>
        </Space>
      ) : null}
    </div>
  );
};
PdfTronTab.displayName = 'PdfTron';
export default PdfTronTab;
