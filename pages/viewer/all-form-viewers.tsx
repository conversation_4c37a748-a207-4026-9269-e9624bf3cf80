import { AppContext } from '@components/context/AppContext';
import * as Gql from '@graphql';
import { onError } from '@utils/error';
import { Button, message, ModalProps, Space, Tabs } from 'antd';
import _ from 'lodash';
import { useRouter } from 'next/router';
import { forwardRef, useContext, useEffect, useRef, useState } from 'react';
import ActivityLogViewer from './activity-log-viewer';
interface Props {
  onSaved?: () => void;
  onSavedXFDF?: (values: any) => void;
  documentId?: string;
}

const PdfTronTab = forwardRef<Props>((props: any) => {
  const router = useRouter();
  const documentId = router.query.documentId as any;
  const activityLogDrawerRef = useRef<any>(null);
  const viewerDiv = useRef<HTMLDivElement>(null);
  const [pdfTron, setPdfTron] = useState<any>();
  const context = useContext(AppContext);
  const room = `project-document-room-${props.documentId}`;
  const [getDoc, { data }] = Gql.useProjectDocumentLazyQuery({ onError: onError as any });

  function timeout(delay: number) {
    return new Promise(res => setTimeout(res, delay));
  }

  //Getting documentId
  useEffect(() => {
    if (documentId) getDoc({ variables: { id: documentId ?? '' } });
  }, [documentId]);

  const file = data?.projectDocument;

  const [updateRequestSignatureXfdf] = Gql.useUpdateOneProjectDocumentMutation({
    onCompleted: () => {
      if (router.query.documentDetails) {
        router.replace({
          query: {
            ..._.omit(router.query, 'documentId'),
            documentDetailsId: router.query.documentDetails
          }
        });
      }
    },
    onError: onError as any
  });

  const saveXFDF = (xfdf: string) => {
    updateRequestSignatureXfdf({
      variables: {
        input: {
          id: _.toString(documentId),
          update: {
            xfdf
          }
        }
      }
    });
    props.onSaved?.();
  };

  // Duplicated the edited file to All form
  const [duplicateEditedStandardForm] = Gql.useDuplicateEditedStandardFormMutation({
    onCompleted: () => {
      if (router.query.documentDetails) {
        router.replace({
          query: {
            ..._.omit(router.query, 'documentId'),
            documentDetailsId: router.query.documentId
          }
        });
      }
      message.success('File saved successfully in all form');
    },
    onError: onError as any
  });

  const duplicateStandardForm = (id: number, xfdf: string) => {
    if (file?.category === Gql.CategoryType.StandardForm) {
      duplicateEditedStandardForm({
        variables: {
          id,
          xfdf
        }
      });
    }
    props?.onSaved?.();
  };

  //Loading the document
  useEffect(() => {
    if (pdfTron && file?.fileUrl) {
      pdfTron?.UI.loadDocument(file?.fileUrl);
      pdfTron?.Core?.documentViewer.addEventListener('documentLoaded', async () => {
        pdfTron.Core?.annotationManager?.importAnnotations(file?.xfdf);
      });
    }
  }, [pdfTron, file?.fileUrl, file?.xfdf]);

  useEffect(() => {
    if (!pdfTron) {
      import('@pdftron/webviewer').then(() => {
        // @ts-ignore
        WebViewer(
          {
            path: '/webviewer',
            licenseKey: process.env.NEXT_PUBLIC_PDFTRON_LICENSE_KEY,
            initialDoc: ''
          },
          viewerDiv.current as HTMLDivElement
        ).then((instance: any) => {
          instance.UI.useEmbeddedPrint(true);
          instance.UI.setPrintQuality(5);
          var Feature = instance.UI.Feature;
          const { docViewer } = instance;
          const { Tools } = instance.Core;
          const panTool = Tools.ToolNames.PAN;
          // instance.UI.disableFeatures([Feature.Measurment, Feature.Copy]);

          docViewer?.addEventListener('documentLoaded', function () {
            instance.UI.setZoomLevel('150%');
            instance.UI.setToolbarGroup(['toolbarGroup-View']);
            instance.UI.setToolMode(panTool);
            instance.UI.openElements(['leftPanel']);
          });

          const style = instance.UI.iframeWindow.document.documentElement.style;

          /*************************************************************** PDFTRON UI CHANGES ********************************************************/
          style.setProperty(`--primary-button`, '#004b75');
          style.setProperty(`--primary-button-hover`, 'yellow');
          style.setProperty(`--view-header-icon-active-fill`, '#1A4971');
          style.setProperty(`--panel-background`, '#E9F0F4');
          style.setProperty(`--tools-header-background`, '#E9F0F4');
          style.setProperty(`--view-header-background`, '#175572');

          /*******************************************************************************************************************************************/

          instance.UI.disableTools([
            Tools.ToolNames.RUBBER_STAMP,
            Tools.ToolNames.FILEATTACHMENT,
            Tools.ToolNames.CALLOUT,
            Tools.ToolNames.CALLOUT2,
            Tools.ToolNames.CALLOUT3,
            Tools.ToolNames.CALLOUT4,
            Tools.ToolNames.FORM_FILL_CROSS,
            Tools.ToolNames.FORM_FILL_DOT,
            Tools.ToolNames.CALENDER
          ]);

          instance.UI.setHeaderItems((header: any) => {
            header.push({
              type: 'actionButton',
              img:
                '<svg width="28" height="26" viewBox="0 0 28 26" fill="none" xmlns="http://www.w3.org/2000/svg">' +
                '<path d="M23.4022 1.71045H4.96485C3.03715 1.71045 1.46875 3.17782 1.46875 4.982V15.5406C1.46875 17.3407 3.03032 18.8057 4.95243 18.8121V23.6035L12.3108 18.8121H23.4022C25.3299 18.8121 26.8983 17.3445 26.8983 15.5406V4.982C26.8983 3.17782 25.3299 1.71045 23.4022 1.71045ZM25.4083 15.5406C25.4083 16.5756 24.5084 17.4178 23.4022 17.4178H11.8433L6.44245 20.9347V17.4178H4.96485C3.85867 17.4178 2.95876 16.5756 2.95876 15.5406V4.982C2.95876 3.94672 3.85867 3.10476 4.96485 3.10476H23.4022C24.5084 3.10476 25.4083 3.94672 25.4083 4.982V15.5406Z" fill="#56798B" />' +
                '<path d="M8.27539 6.63721H20.0928V8.03152H8.27539V6.63721Z" fill="#56798B"/>' +
                '<path d="M8.27539 9.61157H20.0928V11.0059H8.27539V9.61157Z" fill="#56798B"/>' +
                '<path d="M8.27539 12.5862H20.0928V13.9805H8.27539V12.5862Z" fill="#56798B"/>' +
                '</svg>',
              onClick: () => {
                activityLogDrawerRef.current?.pushDrawer();
              }
            });
          });

          // const ToolNames = instance.Core.Tools.ToolNames;
          instance.UI.disableElements(['toolbarGroup-Forms']);
          instance.UI.disableElements(['toolbarGroup-Shapes']);
          instance.UI.disableElements(['toolbarGroup-Edit']);
          instance.UI.disableElements(['toolbarGroup-Annotate']);
          instance.UI.disableElements(['toolbarGroup-Insert']);
          instance.UI.disableElements(['toolbarGroup-Edit']);
          instance.UI.disableElements(['toolbarGroup-FillAndSign']);
          instance.UI.disableElements(['downloadButton']);
          instance.UI.disableElements(['rotatePageCounterClockwise', 'deletePage']);
          instance.UI.disableElements(['annotationCommentButton', 'linkButton']);
          instance.UI.disableElements(['toggleNotesButton']);
          instance.UI.setHeaderItems(function (header: {
            getHeader: (arg0: string) => {
              (): any;
              new(): any;
              push: {
                (arg0: { type: string; toolGroup: string; dataElement: string; title: string }): void;
                new(): any;
              };
              delete: { (arg0: number): void; new(): any };
            };
          }) {
            header.getHeader('toolbarGroup-FillAndSign').push({
              type: 'toolGroupButton',
              toolGroup: 'stampTools',
              dataElement: 'stampToolGroupButton',
              title: 'Image'
            });
            // header.getHeader('toolbarGroup-Insert').push({ type: 'toolGroupButton', toolGroup: 'scaleTools', dataElement: 'scaleToolGroupButton', title: 'annotation.scale' });
            // header.getHeader('toolbarGroup-Shapes').delete(5);
            // header.getHeader('toolbarGroup-Insert').delete(3);
            setPdfTron(instance);
          });
        });
      });
    }
  }, []);

  return (
    <div>
      <div className="webviewer" ref={viewerDiv} style={{ height: '97vh' }}></div>
      <Space style={{ width: '100%', justifyContent: 'end' }}>

        <Button
          className=' flex items-center justify-center h-9 pb-2 rounded-md'
          type="primary"
          style={{ position: 'fixed', bottom: '10px', right: '100px' }}
          onClick={async () => {
            const signatureXfdf = await pdfTron?.docViewer?.getAnnotationManager?.()?.exportAnnotations({
              links: false,
              widgets: false
            });

            saveXFDF(signatureXfdf);

            duplicateStandardForm(Number(router.query.documentId), signatureXfdf);

            // notes for create task
            const newNotes: any[] = pdfTron.Core.annotationManager
              .getAnnotationsList()
              .filter((e: any) => e.IsAdded && e.Subject === 'Note');
            newNotes.forEach((note: any) => {
              props.onSavedXFDF?.(note.getContents());
              props.onSaved?.();
            });
            message.success('File saved successfully');
            await timeout(2000);
            window.close();
          }}
        >
          Save
        </Button>
        <Button
          className=' flex items-center justify-center h-9 pb-2 rounded-md'
          style={{ position: 'fixed', bottom: '10px', right: '10px' }}
          onClick={() => {
            router.replace({
              query: _.omit(router.query, 'documentId')
            });
            window.close();
          }}
        >
          Dismiss
        </Button>

      </Space>
      <ActivityLogViewer ref={activityLogDrawerRef} />
    </div>
  );
});
PdfTronTab.displayName = 'PdfTron';
export default PdfTronTab;
