import { Icon } from '@commons';
import useModal from '@components/hooks/useModal';
import LinkedToPdftron from '@components/LinkedToPdfTron';
import * as Gql from '@graphql';
import { onError } from '@utils/error';
import { Button, Divider, Form, Input, message, Row, Spin, Switch, Tooltip, UploadFile } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import _ from 'lodash';
import moment from 'moment';
import { useRouter } from 'next/router';
import { forwardRef, useCallback, useEffect, useRef, useState } from 'react';
import { SystemService } from 'src/api';
import useTimeFormat from 'src/hooks/useTimeFormat';
import {
  actMessage,
  annotMessage,
  blobToFile,
  buildLogArray,
  flattenPDF,
  getStampImage
} from 'src/utils/pdftron.utils';
import ActivityLogViewer from './activity-log-viewer';
interface Props {
  onSaved?: () => void;
  onSavedXFDF?: (values: any) => void;
  documentId?: string;
}

const PdfTronTab = forwardRef<Props>((props: any) => {
  const router = useRouter();
  const [form] = useForm();
  const documentId = router.query.documentId as string
  const autosavedId = router.query.autosavedId as string
  const dirId = props.projectDocumentId ? props.projectDocumentId : (router.query.dirId as string);
  const [documentAutosavedId, setDocumentAutosavedId] = useState<string | undefined>(undefined);
  const viewerDiv = useRef<HTMLDivElement>(null);
  const [pdfTron, setPdfTron] = useState<any>();
  const [getDoc, { data }] = Gql.useProjectDocumentLazyQuery();
  const [loading, setLoading] = useState(false);
  const dateFreeText = ['DATE_FREETEXT', 'DATEFREETEXT2', 'DATEFREETEXT3', 'DATEFREETEXT4'];
  const freeText = ['FREETEXT', 'FREETEXT2', 'FREETEXT3', 'FREETEXT4'];
  const [logArray, setLogArray] = useState<any[]>([]);
  const [recoveryNotification, setRecoveryNotification] = useState(false);
  const { data: userMeData } = Gql.useGetUserMeQuery({});
  const { data: textSize } = Gql.useGetFontSizeQuery({});
  const fontSize = textSize?.getUserMe?.fontSize;
  const activityLogDrawerRef = useRef<any>(null);
  const [pageChanged, setPageChanged] = useState(false);
  const [blockAutosave, setBlockAutosave] = useState(false);

  const linkedToPdfTronRef = useRef<any>(null);
  const formatTime = useTimeFormat;

  const timeout = (delay: number) => {
    return new Promise(res => setTimeout(res, delay));
  };

  const [createAuditLog] = Gql.useCreateManyAuditLogsMutation({
    onCompleted: async () => {
      setLogArray([]); // reset log array once saved
    },
    onError: onError as any
  });

  const [linkPdftron, { loading: loadingMerge }] = Gql.useLinkPdftronDocumentMutation({
    onCompleted: async data => {
      const xfdfString = await pdfTron.Core.annotationManager.exportAnnotations();

      setTimeout(async () => {
        await pdfTron?.UI.loadDocument(data?.linkPdftronDocument?.url);
        pdfTron.Core.documentViewer.addEventListener('documentLoaded', async () => {
          await pdfTron.Core.annotationManager.importAnnotations(xfdfString);
          pdfTron.UI.openElements(['leftPanel']);
        });
      }, 1000);
    },
    onError: onError as any
  });

  async function generateAsDraft(values: any) {
    setLoading(true);

    const { fileBlob, xfdfString } = await flattenPDF(values, pdfTron);

    if (values.flatten === true) {
      await onUploadChange(fileBlob, '');
    } else {
      await onUploadChange(fileBlob, xfdfString);
    }
  }

  async function saveAsTemplate(input: any) {
    setLoading(true);
    if (input.name && !input.name.endsWith('.pdf')) {
      input.name = input.name + '.pdf';
    }

    const annotations = await pdfTron?.Core.annotationManager.getAnnotationsList();
    if (annotations && annotations.length > 0) {
      await pdfTron.UI.setToolbarGroup(['toolbarGroup-Forms']);
      const styleData = {
        FillColor: new pdfTron.Core.Annotations.Color(222, 227, 255),
        StrokeColor: new pdfTron.Core.Annotations.Color(255, 255, 255),
        // TextColor: new pdfTron.Core.Annotations.Color(0, 0, 0),
        StrokeThickness: 0
      };
      for (const annot of annotations) {
        if (
          annot?.ToolName === 'SignatureFormFieldCreateTool' ||
          annot?.ToolName === 'TextFormFieldCreateTool' ||
          annot?.ToolName === 'AnnotationCreateFreeText' ||
          annot?.ToolName === 'CheckBoxFormFieldCreateTool' ||
          annot?.ToolName === 'RadioButtonFormFieldCreateTool' ||
          annot?.ToolName === 'ListBoxFormFieldCreateTool' ||
          annot?.ToolName === 'ComboBoxFormFieldCreateTool' ||
          annot instanceof pdfTron.Core.Annotations.WidgetAnnotation ||
          annot instanceof pdfTron.Core.Annotations.TextWidgetAnnotation
        ) {
          await pdfTron?.Core.annotationManager.setAnnotationStyles(annot, styleData);

          if (
            annot instanceof pdfTron.Core.Annotations.WidgetAnnotation ||
            annot instanceof pdfTron.Core.Annotations.TextWidgetAnnotation
          ) {
            annot.backgroundColor = new pdfTron.Core.Annotations.Color(0, 0, 0, 0); // RGBA for transparent
            annot.strokeColor = new pdfTron.Core.Annotations.Color(255, 255, 255);
            await pdfTron.Core.annotationManager.redrawAnnotation(annot);
          }
        }
      }
      await pdfTron.UI.setToolbarGroup(['toolbarGroup-FillAndSign']);
    }
    await pdfTron?.Core.documentViewer.updateView();
    await pdfTron?.Core.documentViewer.getDocument().refreshTextData();
    const doc2 = await pdfTron?.Core?.documentViewer.getDocument();
    const xfdfString = await pdfTron.Core?.annotationManager?.exportAnnotations();
    const data = await doc2.getFileData();
    const arr = new Uint8Array(data);
    const blob = new Blob([arr], { type: 'application/pdf' });
    const fileFromBlob = await blobToFile(blob, input?.name);

    await onSaveTemplate(fileFromBlob, xfdfString);
  }

  const autoSaveTemplate = async (
    pdfTronInstance: any,
    fileName: string,
    autosavedId: number | undefined,
    autosavedAt: string | undefined,
    activityLog: any[],
    dirId: number | undefined,
    routerQuery: any,
    skipAutosave: boolean
  ) => {
    if (skipAutosave) return;

    const xfdfString = await pdfTronInstance.Core?.annotationManager?.exportAnnotations();
    //upload file on first autosave and if page rotated only, to save bandwidth
    let fileFromBlob;
    const doc2 = await pdfTronInstance?.Core?.documentViewer.getDocument();
    const data = await doc2.getFileData();
    const arr = new Uint8Array(data);
    const blob = new Blob([arr], { type: 'application/pdf' });
    fileFromBlob = await blobToFile(blob, fileName);

    const onCompleted = async (data: any) => {
      setDocumentAutosavedId(data.createOrUpdateProjectDocumentAutosave.id);
      await createActivityLog(data.createOrUpdateProjectDocumentAutosave.id, activityLog);
      message.success('File autosaved.');
      if (!autosavedId) {
        const query = { ...routerQuery, autosavedId: data.createOrUpdateProjectDocumentAutosave.id };
        const urlParams = new URLSearchParams(query);
        const urlParamsString = '?' + urlParams.toString();
        window.history.pushState({}, '', urlParamsString);
      }
    };

    await onAutoSaveTemplate(fileFromBlob, xfdfString, autosavedId, autosavedAt, dirId, onCompleted);
  };

  const onAutoSaveTemplate = async (
    file: UploadFile | undefined,
    xfdf: string,
    autosavedId: number | undefined,
    autosavedAt: string | undefined,
    dirId: number | undefined,
    onCompleted: any
  ) => {
    autosaveTemplateGql({
      variables: {
        input: {
          id: autosavedId,
          category: Gql.CategoryType.StandardForm,
          fileSystemType: Gql.FileSystemType.Document,
          fileUrl: file as any,
          projectDocumentId: dirId,
          autosavedAt: autosavedAt,
          uploadAddress: 'Uploaded From Web',
          xfdf
        }
      },
      onCompleted
    });
  };

  const [autosaveTemplateGql] = Gql.useCreateOrUpdateProjectDocumentAutosaveMutation({
    onError: (err: any) => {
      message.error(err.message);
      setLoading(false);
    }
  });

  const uploadUpdatedFile = async () => {
    const doc2 = await pdfTron?.Core?.documentViewer.getDocument();
    const data = await doc2.getFileData();
    const arr = new Uint8Array(data);
    const blob = new Blob([arr], { type: 'application/pdf' });
    const fileBlob = await blobToFile(blob, file?.name);

    const tmpFileName = 'tmp/' + _.replace(file?.name as string, new RegExp(' ', 'g'), '-');

    const res = await SystemService.presignedUpload({
      key: tmpFileName,
      size: file?.fileSize as number,
      mimeType: 'pdf'
    });

    await upload('PUT', res.uploadUrl.SignedUrl, fileBlob, res.uploadUrl.ActualSignedRequestHeaders);

    return tmpFileName;
  };

  const upload = async (method: any, _url: any, content: any, headers: any) => {
    const axios = require('axios');
    var req = {
      method: method,
      url: _url,
      withCredentials: false,
      headers: headers || {},
      validateStatus: function (status: any) {
        return status >= 200;
      },
      data: content
    };

    await axios.request({ ...req }); // Spread the existing request configuration
  };

  const handleMergePdfs = async (urls: string[]): Promise<any> => {
    // if file itself is changed, upload first
    if (pageChanged) {
      // upload the file first
      if (!file) {
        return message.error('Document not found');
      }

      const uploadedFileKey = await uploadUpdatedFile();
      urls.unshift(uploadedFileKey);
    } else {
      if (!data?.projectDocument?.fileUrl) {
        return message.error('Document not found');
      }

      urls.unshift(data?.projectDocument?.fileUrl as string);
    }

    return await linkPdftron({
      variables: {
        input: {
          title: data?.projectDocument?.name ?? '',
          urls: urls
        }
      }
    });
  };

  //Getting documentId
  useEffect(() => {
    if (documentId && userMeData) {
      getDoc({ variables: { id: autosavedId || documentId }, fetchPolicy: 'no-cache' }).then(doc => {
        if (!doc.data?.projectDocument) {
          message.error('Document not found');
        }
        setupPDF(doc.data?.projectDocument?.name);
      });
    }
  }, [userMeData, documentId]);

  const file = data?.projectDocument;

  const onUploadChange = async (file: UploadFile, xfdf: any) => {
    const resourceId = autosavedId || documentAutosavedId;

    const onCompleted = async (data: any) => {
      const createdDocumentId = data.createOneProjectDocument?.id || data.updateOneProjectDocument?.id;
      await createActivityLog(createdDocumentId, logArray);
      message.success({
        content: (
          <text>
            Document Generated! Your document is now available in the drafts section
            <Button
              type="link"
              onClick={() => {
                message.destroy();
                const url = '/digital-form/all-form?radioButton=Draft';
                window.open(url, '_blank');
              }}
            >
              Go To Drafts
            </Button>
          </text>
        ),
        duration: 5
      });
      closeEditFolderNameModal();
      await timeout(4000);
      window.close();
      setLoading(false);
    };

    if (resourceId) {
      updateTemplate({
        variables: {
          input: {
            id: resourceId,
            update: {
              xfdf,
              category: Gql.CategoryType.AllForm,
              fileSystemType: Gql.FileSystemType.Document,
              fileUrl: file as any,
              autosavedAt: null,
              projectDocumentId: null,
              status: Gql.ProjectDocumentStatus.Draft
            }
          }
        },
        onCompleted
      });
    } else {
      uploadFile({
        variables: {
          input: {
            projectDocument: {
              xfdf,
              category: Gql.CategoryType.AllForm,
              fileSystemType: Gql.FileSystemType.Document,
              fileUrl: file as any,
              autosavedAt: null,
              projectDocumentId: null,
              uploadAddress: 'Uploaded From Web'
            }
          }
        },
        onCompleted
      });
    }
  };

  const [uploadFile, { loading: uploadFileLoading }] = Gql.useCreateOneProjectDocumentMutation({
    onError: (err: any) => {
      setLoading(false);
      message.error(err.message);
    }
  });

  const onSaveTemplate = async (file: UploadFile, xfdf: string) => {
    const resourceId = autosavedId || documentAutosavedId;

    const onCompleted = async (data: any) => {
      const createdDocumentId = data.createOneProjectDocument?.id || data.updateOneProjectDocument?.id;
      await createActivityLog(createdDocumentId, logArray);
      closeEditTemplateNameModal();
      message.success('Success! Your file has been saved as a new template available for all users');
      await timeout(2000);
      window.close();
      setLoading(false);
    };

    if (resourceId) {
      updateTemplate({
        variables: {
          input: {
            id: resourceId,
            update: {
              xfdf,
              category: Gql.CategoryType.StandardForm,
              fileSystemType: Gql.FileSystemType.Document,
              fileUrl: file as any,
              autosavedAt: null
            }
          }
        },
        onCompleted
      });
    } else {
      createTemplate({
        variables: {
          input: {
            projectDocument: {
              xfdf,
              category: Gql.CategoryType.StandardForm,
              fileSystemType: Gql.FileSystemType.Document,
              fileUrl: file as any,
              projectDocumentId: dirId == 'null' ? null : dirId
            }
          }
        },
        onCompleted
      });
    }
  };

  const [updateTemplate, { loading: updating }] = Gql.useUpdateOneProjectDocumentMutation({
    onError: (err: any) => {
      message.error(err.message);
      setLoading(false);
    }
  });

  const [createTemplate, { loading: creating }] = Gql.useCreateOneProjectDocumentMutation({
    onError: (err: any) => {
      message.error(err.message);
      setLoading(false);
    }
  });

  const createActivityLog = async (id: any, activityLog: any[]) => {
    const toSend = buildLogArray(id, activityLog);
    if (toSend == undefined || toSend.length === 0) return;

    await createAuditLog({ variables: { input: { auditLogs: toSend } } });
  };

  const debouncedAutoSave = useCallback(_.debounce(autoSaveTemplate, 5000), []);

  useEffect(() => {
    if (logArray.length > 0 && file?.name && pdfTron) {
      const resourceId = autosavedId || documentAutosavedId;

      debouncedAutoSave(
        pdfTron,
        file.name,
        resourceId ? parseInt(resourceId) : undefined,
        new Date().toISOString(),
        [...logArray],
        dirId ? parseInt(dirId) : undefined,
        router.query,
        blockAutosave
      );
    }
  }, [logArray, debouncedAutoSave, blockAutosave]);

  const [deleteDocument] = Gql.useDeleteProjectDocumentMutation({
    onCompleted: async () => {
      message.success('Document discarded');
      closeConfirmDeleteModal();
      await timeout(2000);
      window.close();
    },
    onError: onError as any
  });

  const [editFolderNameModal, showEditFolderNameModal, closeEditFolderNameModal] = useModal({
    title: 'Generate document to Drafts',
    onCancel() {
      closeEditFolderNameModal();
      setBlockAutosave(false);
    },
    onClose: () => {
      setBlockAutosave(false);
    },
    width: '500px',
    content: (
      <Form
        layout="vertical"
        form={form}
        requiredMark={false}
        onFinish={generateAsDraft}
        initialValues={{ name: file?.name, flatten: true }}
        className='mt-3'
      >
        <Form.Item
          label={<div className="font-medium">File name</div>}
          name="name"
          rules={[
            {
              required: true,
              message: 'File name is required'
            },
            {
              max: 249,
              message: 'Maximum 250 characters'
            }
          ]}
          style={{ marginBottom: 0 }}
        >
          <Input maxLength={250} />
        </Form.Item>
        <div className="flex items-center mt-2">
          <Form.Item name="flatten" valuePropName="checked" style={{ marginBottom: 0 }}>
            <Switch className="primary" />
          </Form.Item>
          <div className="ml-2">Flatten document</div>
          <Tooltip
            color="black"
            title={
              <div className="m-2">
                This action permanently locks all editing actions done by you, making it uneditable
              </div>
            }
            mouseEnterDelay={0.1}
            overlayStyle={{ maxWidth: '320px' }}
            className="m-2"
          >
            <Icon name="info" width={15} className="text-gray90 cursor-pointer" />
          </Tooltip>
        </div>

        <div className="flex justify-end mt-4">
          <Button htmlType="submit" type="primary" loading={loading}>
            Generate
          </Button>
        </div>
      </Form>
    )
  });

  const [editTemplateNameModal, showEditTemplateNameModal, closeEditTemplateNameModal] = useModal({
    title: 'Save a New Template',
    onCancel: () => {
      setBlockAutosave(false);
    },
    onClose: () => {
      setBlockAutosave(false);
    },
    content: (
      <Form
        layout="vertical"
        form={form}
        requiredMark={false}
        onFinish={saveAsTemplate}
        initialValues={{ name: file?.name }}
      >
        <Form.Item
          label="File name"
          name="name"
          rules={[
            {
              max: 249,
              message: 'Maximum 250 character'
            }
          ]}
        >
          <Input maxLength={250} />
        </Form.Item>
        <div className="flex justify-end">
          <Button htmlType="submit" type="primary" loading={loading}>
            Save
          </Button>
        </div>
      </Form>
    )
  });

  const [confirmDeleteModal, showConfirmDeleteModal, closeConfirmDeleteModal] = useModal({
    content: (
      <div>
        <div className="flex mb-6">
          <Icon name="bin" width={14} height={14} className="text-gray90 mt-1 mr-4" />
          <div className="font-xl">Discard Auto-Recovered Document?</div>
        </div>
        <div className="mb-6">
          Are you sure you want to discard this auto-recovered document? All unsaved changes will be lost.
        </div>
        <div className="flex justify-center">
          <Button
            type="primary"
            className="mr-2 text-gray90 bg-white border-gray90"
            onClick={() => closeConfirmDeleteModal()}
          >
            Cancel
          </Button>
          <Button
            type="primary"
            danger
            className="ml-2 bg-red-500 text-white"
            onClick={() => {
              deleteDocument({
                variables: {
                  id: parseInt(autosavedId as string)
                }
              });
            }}
          >
            Discard
          </Button>
        </div>
      </div>
    )
  });

  //Loading the document
  useEffect(() => {
    if (pdfTron && file?.fileUrl && userMeData) {
      const role = localStorage.getItem('ProjectUserRole');
      pdfTron?.UI.loadDocument(file?.fileUrl);
      pdfTron?.Core?.documentViewer.addEventListener('documentLoaded', async () => {
        freeText.forEach(element => {
          const tool = pdfTron?.Core?.documentViewer.getTool(pdfTron?.Core?.Tools.ToolNames[element]);
          if (tool) {
            tool.defaults.FontSize = `${fontSize}pt`;
          }
        });

        const companyStampUrl = await getStampImage(userMeData?.getUserMe?.company?.stampUrl as string);
        const stampUrl = await getStampImage(userMeData?.getUserMe?.stampUrl as string);
        const stampAndSignUrl = await getStampImage(userMeData?.getUserMe?.stampAndSignUrl as string);
        const signature = await getStampImage(userMeData?.getUserMe?.signUrl as string);

        pdfTron?.Core?.documentViewer
          .getTool(pdfTron?.Core?.Tools?.ToolNames?.RUBBER_STAMP)
          .setStandardStamps([
            role === 'ProjectOwner' || role === 'CloudCoordinator' ? companyStampUrl : null,
            stampUrl,
            stampAndSignUrl
          ]);

        pdfTron?.Core.documentViewer.getTool('AnnotationCreateSignature').importSignatures([signature ?? '']);
        pdfTron?.Core?.documentViewer.getTool('AnnotationCreateSignature').setDefaultSignatureOptions({
          maximumDimensionSize: 75
        });
        await pdfTron.Core?.annotationManager?.importAnnotations(file?.xfdf).then(async () => {
          setLogArray([]); //reset log after annotations are imported, we only want to log new actions
        });
        const annotations = await pdfTron?.Core.annotationManager.getAnnotationsList();
        if (annotations && annotations.length > 0) {
          await pdfTron.UI.setToolbarGroup(['toolbarGroup-Forms']);
          const styleData = {
            FillColor: new pdfTron.Core.Annotations.Color(222, 227, 255),
            StrokeColor: new pdfTron.Core.Annotations.Color(255, 255, 255),
            StrokeThickness: 0
          };
          const filteredAnnotsBox = await annotations.filter(
            (annot: any) => annot.Subject === 'Widget' || annot.Subject === 'Rectangle'
          );
          await filteredAnnotsBox.forEach(async (annot: any) => {
            if (annot instanceof pdfTron.Core.Annotations.SignatureWidgetAnnotation) {
              return;
            }
            await pdfTron?.Core.annotationManager.setAnnotationStyles(annot, styleData);

            if (annot instanceof pdfTron.Core.Annotations.WidgetAnnotation) {
              annot.backgroundColor = new pdfTron.Core.Annotations.Color(222, 227, 255); // RGBA for transparent
              await pdfTron.Core.annotationManager.redrawAnnotation(annot);
            }
          });
          // await pdfTron.UI.setToolbarGroup(['toolbarGroup-FillAndSign']);

          await pdfTron?.Core.documentViewer.refreshAll();
          await pdfTron?.Core.documentViewer.updateView();
          await pdfTron?.Core.documentViewer.getDocument().refreshTextData();
          await pdfTron.UI.setToolbarGroup(['toolbarGroup-View']);
        }
      });
      setLoading(false);
    }
  }, [pdfTron, file?.fileUrl, file?.xfdf, userMeData]);

  const setupPDF = (fileName?: string) => {
    if (!pdfTron) {
      import('@pdftron/webviewer').then(() => {
        // @ts-ignore
        WebViewer(
          {
            path: '/webviewer',
            licenseKey: process.env.NEXT_PUBLIC_PDFTRON_LICENSE_KEY,
            initialDoc: '',
            fullAPI: true,
            disableVirtualDisplayMode: true
          },
          viewerDiv.current as HTMLDivElement
        ).then(async (instance: any) => {
          const { Tools, Annotations, documentViewer, annotationManager } = instance.Core;
          instance.UI.useEmbeddedPrint(true);
          instance.UI.setPrintQuality(5);

          const panTool = Tools.ToolNames.PAN;
          instance.UI.setToolbarGroup('toolbarGroup-View');

          instance.UI.pageManipulationOverlay.add([
            {
              type: 'customPageOperation',
              header: 'Page Attachment',
              dataElement: 'customPageOperations',
              operations: [
                {
                  title: 'Import from Bina Cloud',
                  img: '/assets/Link.svg',
                  onClick: () => linkedToPdfTronRef?.current?.openModal(),
                  dataElement: 'customPageOperationButton',
                  style: {
                    backgroundColor: '#1CE',
                    color: 'white'
                  }
                }
              ]
            },
            { type: 'divider' }
          ]);

          const textCreateTool1 = documentViewer.getTool('AnnotationCreateFreeText');
          const textCreateTool2 = documentViewer.getTool('AnnotationCreateFreeText2');
          const dateCreateTool = documentViewer.getTool(Tools.ToolNames.DATE_FREETEXT);
          dateCreateTool.defaults.TextColor = new Annotations.Color(0, 0, 0);
          textCreateTool1.defaults.TextColor = new Annotations.Color(0, 0, 0);
          textCreateTool2.defaults.TextColor = new Annotations.Color(255, 0, 0);

          const style = instance.UI.iframeWindow.document.documentElement.style;

          instance.UI.setHeaderItems((header: any) => {
            header.push({
              type: 'actionButton',
              img:
                '<svg width="28" height="26" viewBox="0 0 28 26" fill="none" xmlns="http://www.w3.org/2000/svg">' +
                '<path d="M23.4022 1.71045H4.96485C3.03715 1.71045 1.46875 3.17782 1.46875 4.982V15.5406C1.46875 17.3407 3.03032 18.8057 4.95243 18.8121V23.6035L12.3108 18.8121H23.4022C25.3299 18.8121 26.8983 17.3445 26.8983 15.5406V4.982C26.8983 3.17782 25.3299 1.71045 23.4022 1.71045ZM25.4083 15.5406C25.4083 16.5756 24.5084 17.4178 23.4022 17.4178H11.8433L6.44245 20.9347V17.4178H4.96485C3.85867 17.4178 2.95876 16.5756 2.95876 15.5406V4.982C2.95876 3.94672 3.85867 3.10476 4.96485 3.10476H23.4022C24.5084 3.10476 25.4083 3.94672 25.4083 4.982V15.5406Z" fill="#56798B" />' +
                '<path d="M8.27539 6.63721H20.0928V8.03152H8.27539V6.63721Z" fill="#56798B"/>' +
                '<path d="M8.27539 9.61157H20.0928V11.0059H8.27539V9.61157Z" fill="#56798B"/>' +
                '<path d="M8.27539 12.5862H20.0928V13.9805H8.27539V12.5862Z" fill="#56798B"/>' +
                '</svg>',
              onClick: () => {
                activityLogDrawerRef.current?.pushDrawer();
              }
            });
          });

          /*************************************************************** PDFTRON UI CHANGES ********************************************************/
          style.setProperty(`--primary-button`, '#004b75');
          style.setProperty(`--primary-button-hover`, 'yellow');
          style.setProperty(`--view-header-icon-active-fill`, '#1A4971');
          style.setProperty(`--panel-background`, '#E9F0F4');
          style.setProperty(`--tools-header-background`, '#E9F0F4');
          style.setProperty(`--view-header-background`, '#175572');

          /*******************************************************************************************************************************************/

          // instance.UI.disableFeatures([Feature.Measurment, Feature.Copy]);

          // instance.UI.toggleFullScreen();
          instance.UI.setZoomLevel('150%');
          // instance.UI.setToolbarGroup(['toolbarGroup-View']);
          instance.UI.setToolMode(panTool);
          instance.UI.openElements(['leftPanel']);
          // textCreateTool.defaults.FontSize = `10pt`;
          const pageRotations: Record<number, number> = {};

          documentViewer.addEventListener('pagesUpdated', (changes: any) => {
            // set page added to true if page is added
            if (changes.added.length > 0 || changes.removed.length > 0) {
              setPageChanged(true);
            }
            changes.contentChanged.forEach((pageNumber: number) => {
              const newRotation = documentViewer.getDocument().getPageRotation(pageNumber);
              const oldRotation = pageRotations[pageNumber];
              // have to check if newRotation is 90 because of
              // some hard to explain bug when page loading for the first time
              if (newRotation === 90 || (oldRotation && newRotation !== oldRotation)) {
                const newLogObject = {
                  action: Gql.AuditLogActionType.Update,
                  module: Gql.AuditLogModuleType.DocumentEditor,
                  resourceId: documentId,
                  userId: userMeData?.getUserMe.id as any,
                  projectId: localStorage.getItem('ProjectId') as any,
                  content: `${userMeData?.getUserMe.name} ROTATED at ${fileName} | Page: ${pageNumber}`
                };
                setLogArray(prevLogArray => {
                  return [...prevLogArray, newLogObject];
                });
              }
              pageRotations[pageNumber] = newRotation;
            });
            if (changes?.added?.length > 0 || changes?.removed?.length > 0) {
              const isInsert = changes?.added?.length > 0;
              const action = isInsert ? changes?.added[0] : changes?.removed[0];

              const newLogObject = {
                action: isInsert ? Gql.AuditLogActionType.Add : Gql.AuditLogActionType.Delete,
                module: Gql.AuditLogModuleType.DocumentEditor,
                resourceId: documentId,
                userId: userMeData?.getUserMe.id as any,
                projectId: localStorage.getItem('ProjectId') as any,
                content: `${userMeData?.getUserMe.name} ${isInsert ? 'INSERTED' : 'DELETED'
                  } page in ${fileName} | Page: ${action}`
              };
              setLogArray(prevLogArray => {
                return [...prevLogArray, newLogObject];
              });
            }
          });

          annotationManager.addEventListener('annotationChanged', async (annotations: any, action: any) => {
            if (
              (annotations?.[0]?.Subject === 'Rectangle' &&
                annotations?.[0]?.ToolName !== 'AnnotationCreateRectangle') ||
              annotations?.[0]?.Subject === 'Widget'
            )
              return;
            const momentDateModified = moment(annotations[0]?.DateModified);
            if (
              action !== 'delete' &&
              (annotations.length === 0 ||
                annotations[0].Dya === 'Insert text here' ||
                momentDateModified.isBefore(moment().subtract(10, 'seconds')))
            ) {
              return;
            }
            const [annotationMessage, textMessage] = annotMessage(annotations);
            const actionMessage = actMessage(action);
            let actionLog;
            if (action === 'delete') actionLog = Gql.AuditLogActionType.Delete;
            else if (action === 'modify' && textMessage !== '') actionLog = Gql.AuditLogActionType.Update;
            else if (action === 'add') actionLog = Gql.AuditLogActionType.Add;
            else return;

            const newLogObject = {
              action: actionLog,
              module: Gql.AuditLogModuleType.DocumentEditor,
              resourceId: documentId,
              userId: userMeData?.getUserMe.id as any,
              projectId: localStorage.getItem('ProjectId') as any,
              content: `${userMeData?.getUserMe.name
                } ${actionMessage} ${annotationMessage?.toUpperCase()} at ${fileName} | Page: ${annotations[0].PageNumber
                }`,
              text: textMessage
            };

            setLogArray(prevLogArray => {
              if (action === 'modify' && prevLogArray.some(log => log.text === newLogObject.text)) {
                return prevLogArray;
              }
              return [...prevLogArray, newLogObject];
            });
          });

          const origSetRubberStamp = instance.Core.Tools.RubberStampCreateTool.prototype.setRubberStamp;

          dateFreeText.forEach(element => {
            documentViewer.getTool(instance.Core.Tools.ToolNames[element]).setDateFormat('DD-MM-YYYY');
          });

          instance.Core.Tools.RubberStampCreateTool.prototype.setRubberStamp = async function (annot: any, text: any) {
            if (annot.image.width >= 650) {
              annot.image.width /= 11;
              annot.image.height /= 11;
              annot.image.resized = true;
            } else if (annot.image.width >= 450) {
              annot.image.width /= 4;
              annot.image.height /= 4;
              annot.image.resized = true;
            }
            await origSetRubberStamp.call(this, annot, text);
          };

          instance.UI.disableTools([
            // Tools.ToolNames.RUBBER_STAMP,
            Tools.ToolNames.FILEATTACHMENT,
            Tools.ToolNames.CALLOUT,
            Tools.ToolNames.CALLOUT2,
            Tools.ToolNames.CALLOUT3,
            Tools.ToolNames.CALLOUT4,
            Tools.ToolNames.FORM_FILL_CROSS,
            Tools.ToolNames.FORM_FILL_DOT,
            Tools.ToolNames.CALENDER
          ]);

          /* ANNOTATE TOOLBAR DISABLING */
          instance.UI.disableElements(['stickyToolGroupButton']);

          // instance.UI.disableElements(['toolbarGroup-Forms']);
          instance.UI.disableElements(['toolbarGroup-Shapes']);
          // instance.UI.disableElements(['toolbarGroup-Annotate']);
          instance.UI.disableElements(['toolbarGroup-Insert']);
          instance.UI.disableElements(['toolbarGroup-Edit']);
          instance.UI.disableElements(['downloadButton']);
          instance.UI.disableElements(['richTextPopup']);
          instance.UI.disableElements([
            'annotationCommentButton',
            'linkButton',
            'crossStampToolButton',
            'dotStampToolButton'
          ]);
          instance.UI.disableElement('customStampPanelButton');
          instance.UI.disableElements(['toggleNotesButton', 'radioButtonFieldToolGroupButton']);
          instance.UI.disableElements(['defaultSignatureDeleteButton']);
          // instance.UI.disableElements(['imageSignaturePanelButton', 'textSignaturePanelButton']);
          
          //Hide add signature button
          instance.UI.setCreateSignatureButton(false);

          instance.UI.setHeaderItems(function (header: {
            getHeader: (arg0: string) => {
              (): any;
              new(): any;
              push: {
                (arg0: { type: string; toolGroup: string; dataElement: string; title: string }): void;
                new(): any;
              };
              delete: { (arg0: number): void; new(): any };
            };
          }) {
            header.getHeader('toolbarGroup-FillAndSign').push({
              type: 'toolGroupButton',
              toolGroup: 'stampTools',
              dataElement: 'stampToolGroupButton',
              title: 'Image'
            });
            header.getHeader('toolbarGroup-Annotate').push({
              type: 'toolGroupButton',
              toolGroup: 'cloudTools',
              dataElement: 'cloudToolGroupButton',
              title: 'Cloud'
            });
            header.getHeader('toolbarGroup-Annotate').push({
              type: 'toolGroupButton',
              toolGroup: 'arrowTools',
              dataElement: 'arrowToolGroupButton',
              title: 'Arrow'
            });
          });
          setPdfTron(instance);
        });
      });
    }
  };

  return (
    <div>
      {editFolderNameModal}
      {editTemplateNameModal}
      {confirmDeleteModal}
      {pdfTron && data?.projectDocument?.projectId && (
        <LinkedToPdftron
          ref={linkedToPdfTronRef}
          defaultFileList={[]}
          onChange={async urls => handleMergePdfs(urls)}
          projectId={data?.projectDocument?.projectId}
          loadingMerge={loadingMerge}
        />
      )}
      <Spin tip={'Loading...'} spinning={uploadFileLoading || loading}>
        <div className="webviewer" ref={viewerDiv} style={{ height: '100vh' }}></div>
        <Row style={{ width: '100%', justifyContent: 'end' }}>
          {autosavedId && file && (
            <div className="fixed" style={{ bottom: '65px', right: '15px' }}>
              <div className="border border-gray-300 rounded-md p-2 bg-poloBlue shadow-md w-80">
                <div className="flex justify-between">
                  <div className="font-bold m-2">Document Recovery</div>
                  <div
                    className="text-white mr-3 mt-2 cursor-pointer"
                    onClick={() => setRecoveryNotification(!recoveryNotification)}
                  >
                    <Icon name="minus" width={20} height={20} />
                  </div>
                </div>
                <div
                  className="transition-opacity ease-in-out "
                  style={{ display: recoveryNotification ? 'block' : 'none' }}
                >
                  <p className="font-light m-2 text-sm">
                    You are viewing the latest autosaved version of this document form your last session
                  </p>
                  <div className="bg-white rounded-md p-2 m-2">
                    <div className="">{file?.name} [AutoRecovered]</div>
                    <p className="font-light text-sm">
                      Recovered at {formatTime({ dateTime: file?.autosavedAt, timeFormat: 'DD/MMM/YYYY h:mma' })}
                    </p>
                    <div
                      className="border border-gray90 border-solid border-1 rounded-md p-1 my-2 text-red-600 flex w-fit text-sm cursor-pointer"
                      onClick={showConfirmDeleteModal}
                    >
                      <Icon name="bin" width={15} height={15} /> Discard
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
          <Tooltip
            title="Begin document processing workflow"
            placement="topRight"
            overlayStyle={{ maxWidth: 'none', whiteSpace: 'nowrap' }}
          >
            <Button
              type="primary"
              className='h-[40px]'
              style={{ position: 'fixed', bottom: '10px', right: '10px' }}
              onClick={() => {
                setBlockAutosave(true);
                showEditFolderNameModal();
              }}
            >
              Generate
            </Button>
          </Tooltip>
          <Tooltip title="Save current document formatting as a new reusable template" placement="topLeft">
            <Button
              type="primary"
              className="bg-white text-binaBlue border-0 h-[40px]"
              style={{ position: 'fixed', bottom: '10px', right: '110px ' }}
              onClick={() => {
                setBlockAutosave(true);
                showEditTemplateNameModal();
              }}
            >
              Save as new template
            </Button>
          </Tooltip>
        </Row>
      </Spin>
      <ActivityLogViewer ref={activityLogDrawerRef} resourceId={autosavedId || documentAutosavedId} />
    </div>
  );
});

PdfTronTab.displayName = 'PdfTron';
export default PdfTronTab;
