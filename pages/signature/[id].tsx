import { Icon } from '@commons';
import * as Gql from '@graphql';
import { onError } from '@utils/error';
import { Button, Tag, message } from 'antd';
import { useRouter } from 'next/router';
import React, { useRef } from 'react';
import SignatureCanvas from 'react-signature-canvas';

const SignSignature = () => {
  const signatureCanvas = useRef<any>();
  const router = useRouter();
  const [isSaved, setIsSaved] = React.useState(false);
  const { id: changeSignatureToken } = router.query;


  const [updateUserSignature, { loading }] = Gql.useUpdateUserSignatureMutation({
    onError: onError as any,
    onCompleted: () => {
      setIsSaved(true);
      message.success('Signature updated');
      setTimeout(() => {
        window.close();
      }, 1000);
    }
  });

  const onApply = () => {
    const signatureData = signatureCanvas?.current?.toDataURL();    

    updateUserSignature({
      variables: {
        input: { changeSignatureToken: changeSignatureToken as string, signature: signatureData }
      }
    });
  };

  return (
    <div className="flex flex-col justify-center items-center h-screen gap-4 bg-slate-200">
      {isSaved && (
        <Tag color="green" className="text-normal">
          Your signature is finalized. You may now close this window.
        </Tag>
      )}
      {/* <div className="flex justify-center items-center"> */}
      <SignatureCanvas
        penColor="black"
        canvasProps={{ width: 330, height: 250, className: 'sigCanvas', style: { backgroundColor: 'white' } }}
        ref={signatureCanvas}
      />
      <div className="flex w-[360px]">
        <Button loading={loading} type="primary" className="ml-4 w-full flex-1" onClick={onApply}>
          Apply
        </Button>
        <Button type="link" icon={<Icon name="bin" />} onClick={() => signatureCanvas?.current?.clear()} danger />
      </div>      
    </div>
  );
};

export default SignSignature;
