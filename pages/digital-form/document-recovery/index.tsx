import { ExclamationCircleOutlined } from '@ant-design/icons';
import { Icon } from '@commons';
import useModal from '@components/hooks/useModal';
import useQueries from '@components/hooks/useQueries';
import ManageModal from '@components/ManageModal';
import ThreeDotsDropDown from '@components/ThreeDotsDropDown';
import * as Gql from '@graphql';
import { onError } from '@utils/error';
import { getFileName } from '@utils/filename';
import {
  Button,
  Form,
  Input,
  message,
  Modal,
  Row,
  Space,
  Spin,
  Table,
  Tooltip
} from 'antd';
import { useForm } from 'antd/lib/form/Form';
import Paragraph from 'antd/lib/typography/Paragraph';
import _, { isArray } from 'lodash';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { AppLayoutContext } from 'pages/_app';
import { useContext, useEffect, useRef, useState } from 'react';
import { getFileIcon } from 'src/commons/FileIcon';
import useHasFocus from 'src/hooks/useHasFocus';
import useTimeFormat from 'src/hooks/useTimeFormat';

const StandardForm = () => {
  const { userData: userMeData } = useContext(AppLayoutContext);
  const hasFocus = useHasFocus();
  const router = useRouter();
  const [form] = useForm();
  const { confirm } = Modal;
  const [selectedFormId, setSelectedFormId] = useState<string>();
  const [dataSource, setDataSource] = useState<any>(false);
  const dirId = router.query.dir && router.query.dir[0];
  const [loading, setLoading] = useState(false);
  const [hoveredRowIndex, setHoveredRowIndex] = useState<any>(null);

  const tableRef = useRef<any>(null);
  const inputRef = useRef<any>(null);

  const [formName, setFormName] = useState<string>('');
  // For multiple selection
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const timeFormatter = useTimeFormat

  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const onRowHover = (id: any) => {
    setHoveredRowIndex(id);
  };

  const onRowLeave = () => {
    setHoveredRowIndex(null);
  };

  const handleTableScroll = (e: { target: any }) => {
    const { target } = e;
    if (target.scrollHeight - target.scrollTop <= target.clientHeight + 1) {
      onLoadMore();
    }
  };

  const { queries, setFilter } = useQueries<Gql.ProjectDocumentFilter, Gql.ProjectDocumentSort>({
    usePagingParam: false,
    paging: { offset: 0, limit: 20 }
  });

  const {
    data,
    refetch,
    loading: loadingProjectDocument
  } = Gql.useGetProjectDocumentsQuery({
    variables: {
      filter: {
        ...queries.filter,
        autosavedAt: {
          isNot: null
        },
        addedBy: {eq: userMeData?.getUserMe.id},
        category: { eq: Gql.CategoryType.StandardForm }
      }
    },
    errorPolicy: 'all',
    onError: onError
  });

  //Setting Data Source
  useEffect(() => {
    setDataSource(data?.getProjectDocuments?.nodes ?? []);
  }, [data]);

  // refetch the data when dirId is null
  useEffect(() => {
    if (!dirId) {
      refetch();
    }
  }, [dirId]);

  // refetch the data when the tab is focused, because maybe the document has been updated
  useEffect(() => {
    refetch();
  }, [hasFocus]);

  // update dataSource state onLoadMore function is fire
  useEffect(() => {
    setDataSource(data?.getProjectDocuments?.nodes ?? []);

    if (tableRef.current) {
      const tableBody = tableRef.current.querySelector('.ant-table-body');
      tableBody.addEventListener('scroll', handleTableScroll);
    }
    return () => {
      if (tableRef.current) {
        const tableBody = tableRef.current.querySelector('.ant-table-body');
        tableBody.removeEventListener('scroll', handleTableScroll);
      }
    };
  }, [handleTableScroll]);

  const onLoadMore = async () => {
    if (!data?.getProjectDocuments.pageInfo.hasNextPage) return;
    setLoading(true);
    await refetch({
      paging: {
        offset: 0,
        limit: data.getProjectDocuments.nodes.length + 20
      }
    }).then(res => {
      setDataSource([...dataSource, res.data?.getProjectDocuments?.nodes ?? []]);
      setLoading(false);
    });
  };

  //Routing new tab
  const openInNewTab = (url: any) => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  // mass delete
  const [deleteDocuments, { loading: documentsDeleting }] = Gql.useDeleteProjectDocumentsMutation({
      onCompleted: () => {
        message.success('Deleted successfully');
        setSelectedRowKeys([]);
        refetch();
      },
      onError: onError as any
    });

  const [deleteFolder] = Gql.useDeleteProjectDocumentMutation({
    onCompleted: () => {
      message.success('Deleted successfully');
      refetch();
    },
    onError: onError as any
  });

  const showDeleteConfirm = (id: string) => {
    const deletedDocumentName = dataSource.find((doc: any) => id === doc.id.toString()).name

    confirm({
      title: 'Are you sure?',
      icon: <ExclamationCircleOutlined />,
      content: (
        <div>
          Do you really want to delete the{' '}
          <span className="font-semibold">
            {deletedDocumentName} <span className="font-normal">?</span>
          </span>
        </div>
      ),
      okText: 'Yes',
      okType: 'danger',
      cancelText: 'No',
      onOk() {
        deleteFolder({
          variables: {
            id: parseInt(id)
          }
        });
        setSelectedRowKeys([]);
      },
      onCancel() {}
    });
  };

  const [updateName] = Gql.useUpdateOneProjectDocumentMutation({
    onCompleted: () => {
      message.success('Saved successfully');
      closeEditFolderNameModal();
      refetch();
    },
    onError: onError as any
  });

  const onUpdate = (values: Gql.UpdateProjectDocumentInputDto) => {
    const selectedDocument = dataSource?.find((item: any) => item.id === selectedFormId) ?? null;
    const updatedName = getFileName(selectedDocument.type, values.name ?? '', formName);
    if (!values.name) {
      return message.error('Folder name is required');
    }

    updateName({
      variables: {
        input: {
          id: selectedFormId?.toString() ?? '',
          update: {
            name: updatedName
          }
        }
      }
    });
  };

  // Edit Form Category Modal
  const [editFolderNameModal, showEditFolderNameModal, closeEditFolderNameModal] = useModal({
    title: 'Edit name',
    content:
      (setTimeout(() => {
        inputRef?.current?.focus();
      }, 100),
      (
        <Form layout="vertical" form={form} requiredMark={false} onFinish={onUpdate} initialValues={{}}>
          <Form.Item
            rules={[
              {
                max: 249,
                min: 1,
                message: 'Maximum 250 character'
              }
            ]}
            label="File name"
            name="name"
          >
            <Input maxLength={250} defaultValue={formName} ref={inputRef} />
          </Form.Item>
          <div className="flex justify-end">
            <Button htmlType="submit" type="primary">
              Save
            </Button>
          </div>
        </Form>
      ))
  });

  useEffect(() => {
    form.setFieldsValue({ name: formName });
  }, [formName]);

  const columns = [
    {
      title: 'Name',
      key: 'name',
      render: (data: any, _record: any, index: number) => {
        return (
          <Space className="p-1" align="start">
            {getFileIcon(data.type)}
            <div
              className="cursor-pointer"
              onClick={() => {
                const queryParams = {
                  documentId: data.id,
                  dirId: data.projectDocumentId,
                  autosavedId: data.id
                };

                openInNewTab(
                  '/viewer/standard-form-viewer?' + new URLSearchParams(queryParams).toString()
                );
              }}
            >
              <Tooltip
                title={data?.name}
                mouseEnterDelay={0.5}
                placement="topLeft"
                overlayStyle={{ maxWidth: '450px' }}
              >
                <Paragraph
                  className="font-medium"
                  style={{ margin: 0, width: 450 }}
                  ellipsis={{ rows: 2, expandable: false }}
                >
                  {data?.name}
                </Paragraph>
              </Tooltip>
            </div>
          </Space>
        );
      }
    },
    {
      title: 'Folder',
      dataIndex: 'parentFolder',
      key: 'folder',
      width: 300,
      render: (data: any, _record: any, index: number) => {
        return (
          <Row>
            {data?.name}
          </Row>
        );
      }
    },
    {
      title: 'Last Recovery',
      dataIndex: 'autosavedAt',
      key: 'autosavedAt',
      width: 200,
      render: (data: any, _record: any, index: number) => {
        return (
          <Row>
            {timeFormatter({dateTime: data})}
          </Row>
        );
      }
    },
    {
      title: '',
      key: 'action',
      width: 100,
      render: (data: any, _record: any, index: number) => {
        const items: any = [];
        items.push({ label: <p>Rename</p>, key: 'Rename' });

        return (
          <div className='flex justify-end'>
            <div onClick={() => showDeleteConfirm(data.id)} className='mr-6 cursor-pointer'>
              <Icon name="bin" className='mr-6 text-gray500 my-auto relative top-[5px]'/>
            </div>
            <ThreeDotsDropDown
              onClick={(e: any) => {
                if (e.key === 'Rename') {
                  setSelectedFormId(data.id);
                  setFormName(data.name);
                  showEditFolderNameModal();
                }
              }}
              items={items}
              className='m-auto'
            />
          </div>
        );
      }
    }
  ];

  const showDeletesConfirm = () => {
    const deletedDocumentNames = dataSource
      .filter((doc: any) => selectedRowKeys.includes(doc.id.toString()))
      .map((doc: any) => doc.name)
      .slice(0, 10);

    confirm({
      title: 'Are you sure?',
      icon: <ExclamationCircleOutlined />,
      content: (
        <>
          <div>
            Do you really want to delete the{' '}
            {selectedRowKeys.length < 2 && (
              <span className="font-semibold">
                {deletedDocumentNames[0]} <span className="font-normal">?</span>
              </span>
            )}
          </div>
          {selectedRowKeys.length > 1 && (
            <ol className="list-decimal mt-2">
              {deletedDocumentNames.map((name: string, index: number) => (
                <li className="my-2 font-semibold" key={index}>
                  {name}
                </li>
              ))}
            </ol>
          )}
        </>
      ),
      okText: 'Yes',
      okType: 'danger',
      cancelText: 'No',
      onOk() {
        deleteDocuments({
          variables: {
            input: {
              ids: selectedRowKeys.map(id => parseInt(id as string))
            }
          }
        });
        setSelectedRowKeys([]);
      },
      onCancel() {}
    });
  };

  return (
    <div className="absolute h-full w-full">
      {editFolderNameModal}

      <Spin tip={'Loading...'} spinning={loading}>
        <div className="px-5 pt-[20px] pb-[12px]">
          <Link href="/digital-form/standard-form" className='' >
            <div className='cursor-pointer w-fit'>
              {"<"} Back
            </div>
          </Link>
          <h3 className='mt-3 mb-2 flex'><Icon name="auto-recovery-document" className="mr-2"/>Document Recovery</h3>
          <p>BINA has recovered some files while you were away. Review & save the ones you wish to keep.</p>
        </div>

        <div className='px-5 flex'>
        {/* // custom positioning to overlap table */}
          <div className='p-2 bg-tableHeader rounded-t font-bold pb-3' style={{position: "relative", top: '8px'}}>
            Templates
          </div>
        </div>
        <Table
          rowSelection={{
            type: 'checkbox',
            selectedRowKeys,
            onChange: onSelectChange,
            hideSelectAll: selectedRowKeys.length === 0,
            getCheckboxProps: e => {
              return {
                style: {
                  display: selectedRowKeys.includes(e.id) || e.id === hoveredRowIndex ? 'flex' : 'none',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginBottom: 10
                }
              };
            }
          }}
          className="dashboard-table mb-[30px] px-5"
          dataSource={
            isArray(dataSource)
              ? dataSource?.map((item: any) => ({
                  ...item,
                  key: item.id
                }))
              : dataSource
          }
          columns={columns}
          size="small"
          // components={components}
          pagination={false}
          tableLayout="auto"
          onRow={(data, index) => {
            const attr = {
              index,
              onMouseEnter: () => onRowHover(data.id), // when mouse enters the row
              onMouseLeave: () => onRowLeave() // when mouse leaves the row
            };
            return attr as React.HTMLAttributes<any>;
          }}
          ref={tableRef}
          scroll={{
            scrollToFirstRowOnChange: false,
            y: window.innerHeight - 230
          }}
          loading={loading || loadingProjectDocument}
        />
      </Spin>
      <ManageModal
        canMove={false}
        onMove={()=>{}}
        selectedRowKeys={selectedRowKeys}
        showDeletesConfirm={showDeletesConfirm}
        canDelete={true}
        data={dataSource}
        setSelectedRowKeys={setSelectedRowKeys}
      />
    </div>
  );
};

StandardForm.auth = true;
export default StandardForm;
