import { ExclamationCircleOutlined, FilterOutlined, HomeOutlined, UserOutlined } from '@ant-design/icons';
import { Icon } from '@commons';
import DynamicWorkspaceDrawer from '@components/digital-form/all-form/DynamicWorkspaceDrawer';
import FilterDrawer from '@components/digital-form/all-form/FilterDrawer';
import LinearWorkspaceDrawer from '@components/digital-form/all-form/LinearWorkspaceDrawer';
import WorkflowDropdownOption from '@components/digital-form/all-form/WorkflowDropdown';
import DigitalFormTabLayout from '@components/digital-form/DigitalFormTabLayout';
import useModal from '@components/hooks/useModal';
import useQueries from '@components/hooks/useQueries';
import { useColumns } from '@components/hooks/useScheduleColumn';
import ManageModal from '@components/ManageModal';
import StoringModal from '@components/tasks/StoringModal';
import ThreeDotsDropDown from '@components/ThreeDotsDropDown';
import UserAvatar from '@components/UserAvatar';
import WorkspaceUpload from '@components/WorkspaceUpload';
import * as Gql from '@graphql';
import apolloClient from '@lib/apollo';
import { concatenateAssignees, handleRowClick, nameAlias, view } from '@utils/app.utils';
import { manageDocuments } from '@utils/authority';
import { onError } from '@utils/error';
import { tz } from '@utils/timezone';
import {
  Avatar,
  Badge,
  Breadcrumb,
  Button,
  Card,
  Cascader,
  Col,
  message,
  Modal,
  ModalProps,
  Popover,
  Radio,
  RadioChangeEvent,
  Row,
  Select,
  Space,
  Spin,
  Switch,
  Table,
  Tooltip,
  Typography
} from 'antd';
import Paragraph from 'antd/lib/typography/Paragraph';
import axios from 'axios';
import FileSaver from 'file-saver';
import _, { isArray } from 'lodash';
import moment from 'moment';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { AppLayoutContext } from 'pages/_app';
import ShowHideColumn from 'pages/schedules/ShowHideColumn';
import { useCallback, useContext, useEffect, useMemo, useRef, useState } from 'react';
import { ProjectDocumentApiService } from 'src/api';
import { useShareModal } from 'src/components/digital-form/overview/ShareModal';
import useDropzone from 'src/hooks/useDropzone';

const { Text } = Typography;

export interface ModalRef extends ModalProps {
  openModal: (v?: any) => void;
}

const AllForm = () => {
  const router = useRouter();
  const [directory, setDirectory] = useState<Gql.ProjectDocument>(null as any);
  const [isOnlyMeFilter, setIsOnlyMeFilter] = useState(false);
  const LinearWorkspaceDrawerRef = useRef<any>(null);
  const DynamicWorkspaceDrawerRef = useRef<any>(null);
  const currentDirs = (router.query.dir as string[]) ?? [];
  const dirId = router.query.dirId;
  const radioButton = router.query.radioButton;
  const documentId = router.query.documentId as string;
  const workflowType = router.query.workflowType as string;
  const { confirm } = Modal;
  const [dataSource, setDataSource] = useState<any>(false);
  const [projectUserRole, setProjectUserRole] = useState<string>('');
  const uploadWorkspaceDocModalRef = useRef<any>(null);
  const filterDrawerRef = useRef<any>(null);
  const [requestSignLoading, setRequestSignLoading] = useState<any>(false);
  const [loading, setLoading] = useState<any>(false);
  const [tableColumns, toggleColumn] = useColumns('workspace-document');
  const [assigneeOptions, setAssigneeOptions] = useState<any>([]);
  const [groups, setGroups] = useState<any>([]);
  const [filterData, setFilterData] = useState({
    assignee: [],
    keyword: '',
    id: '',
    status: '',
    group: '',
    parentCode: '',
    groupCode: ''
  });
  const [hoveredRowIndex, setHoveredRowIndex] = useState(null);
  const [viewSharedModal, setViewSharedModal] = useState<boolean>(false);
  const [assigneeFilter, setAssigneeFilter] = useState<any>('');
  const [storeDocumentId, setStoreDocumentId] = useState<string | undefined>();

  const [getWorkspaceGroup] = Gql.useWorkspaceGroupLazyQuery({
    fetchPolicy: 'network-only',
    nextFetchPolicy: 'network-only',
    onError: error => {
      onError(error);
    }
  });

  const StoringModalRef = useRef<ModalRef>(null);
  const StoringModalOnClickRef = useRef<ModalRef>(null);

  const fixturesGroups = ['Ungroup Documents', 'Site Diary', 'Request For Information', 'Non Conformance Report'];
  const noChildrenGroups = ['ungroup documents', 'site diary'];

  const dropzoneRef = useRef<any>(null);
  useDropzone({ dropzoneRef, pushModal: uploadWorkspaceDocModalRef?.current?.openModal });

  // For multiple selection
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };
  const [driveType, setDriveType] = useState<Gql.ProjectDocumentStatus>('' as any);

  useEffect(() => {
    setProjectUserRole(localStorage.getItem('ProjectUserRole') || '');
    if (viewOnly) {
      setDriveType('' as any);
    }
  }, [projectUserRole]);

  useEffect(() => {
    if (radioButton) {
      setDriveType(radioButton as Gql.ProjectDocumentStatus);
    } else {
      setDriveType('' as Gql.ProjectDocumentStatus);
    }
  }, []);

  const viewOnly = projectUserRole === 'CanView';
  const { canDelete, canCreate } = manageDocuments(projectUserRole, '', driveType);
  const params = router.query;

  useEffect(() => {
    if (params?.parentCode) {
      setFilterData({
        ...filterData,
        parentCode: params?.parentCode as string,
        status: params?.status as string
      });
    }

    if (params?.groupId) {
      setFilterData({
        ...filterData,
        group: params?.groupId as string
      });
    }

    if (params?.status && params?.groupId && params?.filter) {
      setFilterData({
        ...filterData,
        group: params?.groupId as string,
        status: params?.status as string
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [params.filter, params?.groupId, params?.status, driveType]);

  //get status from query
  const status = router.query?.status as string;
  useEffect(() => {
    if (typeof status === 'string') {
      if (status !== Gql.ProjectDocumentStatus.Draft) {
        setDriveType('' as any);
      }
    }
  }, [status]);

  const { userData: userMeData } = useContext(AppLayoutContext);

  useEffect(() => {
    if (directory) {
      router.push({
        pathname: [router.asPath.split('?')[0], directory.name].join('/'),
        query: { dirId: directory.id }
      });
    }
  }, [directory]);

  Gql.useProjectUsersQuery({
    variables: {
      paging: {
        limit: 100,
        offset: 0
      },
      filter: {
        role: { neq: Gql.ProjectUserRoleType.CanView },
        user: { name: { like: `%${assigneeFilter}%` }, removedAt: { is: null } }
      }
    },
    onError: onError,
    onCompleted: data => {
      const options = data?.projectUsers?.nodes?.map((user: any) => {
        return {
          key: user?.userId,
          title: user?.user?.name,
          label: (
            <div className="flex gap-2">
              <UserAvatar
                username={user?.user?.name}
                src={user?.user?.avatar}
                tooltip={user?.user?.name}
                style={{
                  backgroundColor: user?.user?.avatar ? "transparent" : user?.user?.color || "", color: "#ffffff",
                }}
              />
              <Text>{user?.user?.name}</Text>
            </div>
          ),
          value: user?.userId
        };
      });

      setAssigneeOptions(options);
    }
  });

  Gql.useGetWorkspaceGroupsQuery({
    fetchPolicy: 'no-cache',
    variables: {
      sorting: [
        {
          direction: Gql.SortDirection.Desc,
          field: Gql.WorkspaceGroupWithChildrenSortFields.CreatedAt
        }
      ],
      paging: {
        offset: 0,
        limit: 999999
      }
    },
    onError: onError,
    onCompleted: data => {
      setGroups(data?.getWorkspaceGroups?.nodes);
    }
  });

  useEffect(() => {
    if (router.query.documentDetailsId) {
      if (workflowType === Gql.WorkflowType.Linear) {
        LinearWorkspaceDrawerRef?.current?.pushDrawer();
      } else {
        DynamicWorkspaceDrawerRef?.current?.pushDrawer();
      }
    }
  }, [router.query.documentDetailsId, workflowType]);

  const { queries, setPaging } = useQueries<Gql.ProjectDocumentFilter, Gql.ProjectDocumentSort>({
    paging: { limit: 10, offset: 0 },
    usePagingParam: false
  });

  const companySubscriptions = Gql.useCompanySubscriptionsQuery({
    variables: {
      sorting: {
        direction: Gql.SortDirection.Desc,
        field: Gql.CompanySubscriptionSortFields.SubscriptionEndDate
      },
      paging: {
        limit: 1
      }
    },
    onError: onError
  });
  const isViewer = projectUserRole === 'CanView';

  const {
    data,
    refetch,
    loading: documentLoading
  } = Gql.useGetWorkspaceDocumentsQuery({
    variables: {
      ...queries,
      filter: {
        ...queries.filter,
        ...{
          category: { eq: Gql.CategoryType.AllForm },
          status: isViewer
            ? { eq: Gql.ProjectDocumentStatus.Approved }
            : driveType === Gql.ProjectDocumentStatus.Draft
            ? { eq: Gql.ProjectDocumentStatus.Draft }
            : { neq: Gql.ProjectDocumentStatus.Draft },
          addedBy: {
            ...(driveType === Gql.ProjectDocumentStatus.Draft ? { eq: userMeData?.getUserMe.id } : {})
          },
          ...(isOnlyMeFilter
            ? {
                requestForSignatures: {
                  signById: {
                    eq: userMeData?.getUserMe.id
                  }
                }
              }
            : {}),
          ...(filterData.group && {
            workspaceGroup: {
              id: {
                eq: filterData.group as string
              }
            }
          }),
          ...(filterData.status &&
            driveType !== Gql.ProjectDocumentStatus.Draft && {
              status: {
                eq: filterData.status as Gql.ProjectDocumentStatus
              }
            }),
          ...(filterData.keyword && {
            name: {
              like: `%${filterData.keyword}%`
            }
          }),
          ...(filterData.id && {
            allFormCode: {
              eq: +filterData.id
            }
          }),
          ...(filterData.assignee && filterData.assignee.length > 0
            ? {
                requestForSignatures: {
                  signById: {
                    in: filterData.assignee ?? null
                  }
                }
              }
            : {}),
          ...(filterData?.parentCode
            ? {
                workspaceGroup: {
                  code: {
                    eq: filterData?.parentCode
                  }
                }
              }
            : {}),
          ...(filterData?.groupCode
            ? {
                groupCode: { eq: parseInt(filterData?.groupCode) }
              }
            : {})
        }
      },
      sorting: [
        {
          direction: Gql.SortDirection.Desc,
          field:
            driveType === Gql.ProjectDocumentStatus.Draft
              ? Gql.ProjectDocumentSortFields.CreatedAt
              : (Gql.ProjectDocumentSortFields.SubmittedAt as any)
        }
      ]
    },

    fetchPolicy: 'cache-and-network'
  });

  //Setting Data Source
  useMemo(() => {
    setDataSource(data?.getWorkSpaceAllForm?.nodes ?? []);
  }, [data]);

  const [updateProjectDocument] = Gql.useUpdateProjectDocumentParentMutation({
    onCompleted: () => {
      message.success('Moved document successfully');
      refetch();
    },
    onError: onError as any
  });

  const moveDoc = useCallback(
    (dragIndex: number, hoverIndex: number) => {
      const dragObject = dataSource[dragIndex];
      const dropObject = dataSource[hoverIndex];

      if (dragObject?.fileSystemType === Gql.FileSystemType.Folder) {
        message.error('Cannot Move Folder');
      } else {
        updateProjectDocument({
          variables: {
            input: {
              id: dragObject.id,
              parentId: dropObject.id
            }
          }
        });
      }
    },
    [dataSource]
  );

  const [deleteFolder] = Gql.useDeleteProjectDocumentMutation({
    onCompleted: () => {
      message.success('Deleted successfully');
      refetch();
    },
    onError: onError as any
  });

  const [deleteDocuments, { loading: documentsDeleting }] = Gql.useDeleteProjectDocumentsMutation({
    onCompleted: () => {
      message.success('Deleted successfully');
      setSelectedRowKeys([]);
      refetch();
    },
    onError: onError as any
  });

  const showDeletesConfirm = () => {
    const deletedDocumentNames = dataSource
      .filter((doc: any) => selectedRowKeys.includes(doc.id.toString()))
      .map((doc: any) => doc.name)
      .slice(0, 10);

    confirm({
      title: 'Are you sure?',
      icon: <ExclamationCircleOutlined />,
      content: (
        <>
          <div>
            Do you really want to delete the{' '}
            {selectedRowKeys.length < 2 && (
              <span className="font-semibold">
                {deletedDocumentNames[0]} <span className="font-normal">?</span>
              </span>
            )}
          </div>
          {selectedRowKeys.length > 1 && (
            <ol className="list-decimal mt-2">
              {deletedDocumentNames.map((name: string, index: number) => (
                <li className="my-2 font-semibold" key={index}>
                  {name}
                </li>
              ))}
            </ol>
          )}
        </>
      ),
      okText: 'Yes',
      okType: 'danger',
      cancelText: 'No',
      onOk: async () => {
        await deleteDocuments({
          variables: {
            input: {
              ids: selectedRowKeys.map(id => parseInt(id as string))
            }
          }
        });
        setSelectedRowKeys([]);
      },
      onCancel() {}
    });
  };

  const onBulkDownload = async () => {
    setLoading(true);
    try {
      const nodeBuffer = await ProjectDocumentApiService.downloadBulkZip(
        {
          body: {
            ids: selectedRowKeys as string[]
          }
        },
        {
          responseType: 'arraybuffer'
        }
      ).catch(e => {
        message.error(e?.message ?? 'Something went wrong');
      });

      const file = new Blob([nodeBuffer], { type: 'application/zip' });
      const fileName = 'bulkdownload.zip';
      setSelectedRowKeys([]);
      setLoading(false);
      FileSaver(file, fileName);
    } catch (e) {
      onError(e);
    }
  };

  const showDeleteConfirm = (id: string) => {
    confirm({
      width: 700,
      title: (
        <div className="flex items-center">
          <Icon name="bin" fill="#C94C4F" width={21} height={21} className="mr-2" />
          Delete file?
        </div>
      ),
      icon: null,
      content: (
        <div>
          <p className="ml-7">Are you sure you want to delete this file? This action cannot be undone.</p>
        </div>
      ),
      okText: 'Delete',
      okType: 'danger',
      okButtonProps: {
        className: 'bg-[#C94C4F] !text-white !border-none !shadow-none hover:!bg-[#a8373a]'
      },
      cancelText: 'Cancel',
      onOk() {
        onDelete(id);
      },
      onCancel() {},
      className: 'custom-confirm-modal'
    });
  };

  const onDelete = (id: string) => {
    deleteFolder({ variables: { id: parseInt(id) } });
  };

  const [updateWorkflow] = Gql.useUpdateOneProjectDocumentMutation({
    onError: onError as any
  });

  // Choose workflow workspace drawer
  const [chooseWorkflowModal, showChooseWorkflowModal, closeChooseWorkflowModal] = useModal({
    title: '',
    closable: false,
    className: 'workspace-workflow-modal',
    onCancel: () => {
      router.replace({ query: _.omit(router.query, ['documentId']) });
      closeChooseWorkflowModal();
    },
    content: (
      <div className="flex flex-col gap-4 justify-start mb-32 items-center">
        <h3 className="text-2xl text-white mb-10">Choose Your Workflow</h3>
        <div className="flex gap-4">
          <Card
            className="cursor-pointer"
            onClick={() =>
              updateWorkflow({
                variables: {
                  input: {
                    id: documentId,
                    update: { workflow: Gql.WorkflowType.Linear }
                  }
                },
                onCompleted: async data => {
                  await refetch();
                  closeChooseWorkflowModal();

                  router.replace({
                    query: {
                      ...router.query,
                      documentDetailsId: data?.updateOneProjectDocument?.id,
                      workflowType: data?.updateOneProjectDocument?.workflow
                    }
                  });
                }
              })
            }
          >
            <Icon name="linear-workspace" />
          </Card>
          <Card
            className="cursor-pointer"
            onClick={() =>
              updateWorkflow({
                variables: {
                  input: {
                    id: documentId,
                    update: { workflow: Gql.WorkflowType.Dynamic }
                  }
                },
                onCompleted: async data => {
                  await refetch();
                  closeChooseWorkflowModal();

                  router.replace({
                    query: {
                      ...router.query,
                      documentDetailsId: data?.updateOneProjectDocument?.id,
                      workflowType: data?.updateOneProjectDocument?.workflow
                    }
                  });
                }
              })
            }
          >
            <Icon name="dynamic-workspace" />
          </Card>
        </div>
      </div>
    )
  });

  // Download File
  const onDownload = async (id: string) => {
    try {
      const doc = await apolloClient.query<Gql.ProjectDocumentQuery, Gql.ProjectDocumentQueryVariables>({
        query: Gql.ProjectDocumentDocument,
        variables: { id }
      });
      const file = doc.data.projectDocument;
      const { data: fileBlob } = await axios.get(file?.fileUrl ?? '', {
        responseType: 'blob'
      });
      let fileName = file?.name ?? '';
      if (!fileName.endsWith('.pdf')) {
        fileName += '.pdf';
      }
      FileSaver(fileBlob, fileName);
    } catch (error) {
      message.error('Download failed');
    }
  };

  const pushRouter = (directory: any) => {
    router.push({
      pathname: [router.asPath.split('?')[0], directory?.name].join('/'),
      query: { dirId: directory?.id }
    });
  };

  const [updateDocument] = Gql.useUpdateOneProjectDocumentMutation({
    onCompleted: () => {
      message.success('Updated successfully');
      refetch();
    },
    onError: onError as any
  });

  const [deleteAssignee] = Gql.useDeleteOneRequestForSignatureMutation({
    onCompleted: () => {
      message.success('Asssignee deleted successfully');
      refetch();
    },
    onError: onError as any
  });

  const [addAssignee] = Gql.useCreateOneRequestForSignatureMutation({
    onCompleted: () => {
      message.success('Assignee added successfully');
      refetch();
    },
    onError: onError as any
  });

  const showRequestConfirm = (data: any) => {
    confirm({
      title: 'Requesting approval from: ',
      icon: null,
      width: '550px',
      content: (
        <div>
          {data?.requestForSignatures?.length > 0 &&
            data?.requestForSignatures?.map((obj: any, index: any) => {
              return (
                <div key={index} className="flex items-center gap-2 my-2">
                  <UserAvatar
                  username={obj?.signBy?.name}
                  src={obj?.signBy?.avatar}
                  tooltip={obj?.signBy?.name}
                  style={{
                    backgroundColor: obj?.signBy?.avatar ? "transparent" : obj?.signBy?.color || "",
                    color: "#ffffff" }}
                  />
                  <div>{obj?.signBy?.name + '\n'}</div>
                </div>
              );
            })}
          {data?.requestForSignatures?.length == 0 && (
            <div style={{ color: 'red' }}>Alert: No assignees set for this document</div>
          )}
          {data?.requestForSignatures?.length > 0 && (
            <div className="text-base">
              <br></br>
              Note: <br></br>
              All assignees must approve this document for it to be marked as ‘Approved’. If any assignee rejects it,
              the status will be ‘Rejected’.
            </div>
          )}
        </div>
      ),
      okButtonProps: { style: { background: '#1EA8E0' } },
      okText: 'Confirm request',
      type: 'confirm',
      cancelText: 'Cancel',
      onOk() {
        if (data?.requestForSignatures?.length > 0) onRequest(data.id);
        else message.error('Alert: No assignees set for this document');
      },
      onCancel() {}
    });
  };

  const onRequest = async (id: any) => {
    setRequestSignLoading(true);
    try {
      await apolloClient.mutate<Gql.UpdateOneProjectDocumentMutation, Gql.UpdateOneProjectDocumentMutationVariables>({
        mutation: Gql.UpdateOneProjectDocumentDocument,
        variables: {
          input: {
            id: id,
            update: {
              status: Gql.ProjectDocumentStatus.Submitted
            }
          }
        }
      });
      refetch();
      message.success('Your request for approval has been submitted successfully.');
      setRequestSignLoading(false);
    } catch (e: any) {
      onError(e);
      setRequestSignLoading(false);
    }
  };

  const onAssigneeSelect = async (
    vals: any,
    data: Gql.ProjectDocument,
    assignees: Gql.Maybe<Gql.RequestForSignature[]> | undefined
  ) => {
    await addAssignee({
      variables: {
        input: {
          requestForSignature: {
            signById: Number(vals?.value),
            ownerId: Number(data?.addedBy),
            projectDocumentId: data?.id
          }
        }
      }
    });
  };

  const onAssigneeDeselect = async (
    vals: any,
    data: Gql.ProjectDocument,
    assignees: Gql.Maybe<Gql.RequestForSignature[]> | undefined
  ) => {
    const assignee: any = assignees?.find((assignee: any) => {
      return assignee.signBy.id === vals?.value;
    });
    await deleteAssignee({
      variables: {
        id: assignee?.id
      }
    });
  };

  const filteredColumns = useMemo(() => {
    // Draft Columns
    const draftColumns = [
      {
        title: 'TITLE',
        key: 'name',

        render: (data: any) => {
          const { fileUrl } = data;

          return (
            <Space className="p-1" align="start">
              {data.isDocsStored === true ? <Icon name="store-document"></Icon> : <Icon name="pdf"></Icon>}

              <div
                className="cursor-pointer"
                style={{ wordWrap: 'break-word', wordBreak: 'break-word' }}
                onClick={() => {
                  const { group, status, parentCode } = filterData;

                  if (!group) delete router.query.groupId;
                  if (!status) delete router.query.status;
                  if (!parentCode) delete router.query.parentCode;

                  if (!group && !status && !parentCode) {
                    delete router.query.filter;
                  }

                  if (data?.workflow === null) {
                    showChooseWorkflowModal();
                    router.replace({
                      query: {
                        ...router.query,
                        documentId: data.id
                      }
                    });
                    return;
                  }

                  if (fileUrl) {
                    if (router.query && data.fileSystemType === 'Document') {
                      router.replace({
                        query: {
                          ...router.query,
                          documentDetailsId: data.id,
                          workflowType: data?.workflow
                        }
                      });
                    }
                  }
                  if (data?.fileSystemType === 'Folder') {
                    setDirectory(data);
                    pushRouter(data);
                  }
                }}
              >
                <Tooltip
                  title={data?.name}
                  mouseEnterDelay={0.5}
                  placement="topLeft"
                  overlayStyle={{ maxWidth: '380px' }}
                >
                  {' '}
                  <div>
                    <div className="flex justify-start max-w-[380]">
                      <Paragraph
                        className="font-medium flex"
                        style={{ margin: 0, maxWidth: 380, fontSize: 16 }}
                        ellipsis={{ rows: 1, expandable: false }}
                        id="document-name"
                      >
                        {data?.name}
                      </Paragraph>
                      {data?.workspaceGroup?.parent?.workspaceGroupUsers.length > 0 && (
                        <div
                          onClick={e => {
                            e.stopPropagation();
                            onShare(data);
                          }}
                          className=" cursor-pointer ml-2 pt-1 border-[#9B111E] font-light rounded-xl px-2 bg-[#9B111E] text-white text-sm flex m-auto"
                        >
                          <Icon name="lock-white" />
                          <span className="text-sm relative ml-[2px] -top-[2px] ">
                            {data?.workspaceGroup?.parent?.workspaceGroupUsers.length}
                          </span>
                        </div>
                      )}
                    </div>
                    <Paragraph
                      className="text-base"
                      style={{ margin: 0, width: 380, color: '#8c8c8c' }}
                      ellipsis={{ rows: 1, expandable: false }}
                    >
                      {data?.description}
                    </Paragraph>
                  </div>
                </Tooltip>
              </div>
            </Space>
          );
        }
      },
      {
        title: 'LAST MODIFIED',
        key: 'updatedAt',
        width: 130,
        fixed: 'right',
        render: (data: any) => {
          return <p>{moment(tz(data.updatedAt)).format('D MMM YYYY')}</p>;
        }
      },
      {
        title: 'WORKFLOW',
        key: 'workflow',
        width: 130,
        fixed: 'right',
        render: (data: Gql.ProjectDocument) => {
          const options = [
            {
              label: 'Dynamic',
              value: Gql.WorkflowType.Dynamic,
              component: (
                <WorkflowDropdownOption
                  icon={'dynamic-workflow'}
                  label={'Dynamic'}
                  firstRow={'Assign a single user first. Each approver'}
                  secondRow=" assigns the next until final approval."
                />
              )
            },
            {
              label: 'Linear',
              value: Gql.WorkflowType.Linear,
              component: (
                <WorkflowDropdownOption
                  icon={'linear-workflow'}
                  label={'Linear'}
                  firstRow={'Assign multiple users directly. All must '}
                  secondRow="approve to proceed."
                />
              )
            }
          ];

          return (
            <div>
              <Select
                suffixIcon={<Icon name="chevron-down" />}
                className={data?.workflow ? 'custom-select-multiple' : 'custom-select-multiple-border'}
                dropdownStyle={{ width: 330 }}
                style={{ width: 100, borderRadius: 4 }}
                value={data?.workflow ? { label: data.workflow, value: data.workflow } : undefined}
                placeholder="Select"
                dropdownMatchSelectWidth={false}
                labelInValue
                optionLabelProp="label"
                onChange={val => {
                  updateWorkflow({
                    variables: {
                      input: {
                        id: data.id,
                        update: { workflow: val.value }
                      }
                    },
                    onCompleted: () => {
                      refetch();
                    }
                  });
                }}
              >
                {options.map(option => (
                  <Select.Option key={option.value} value={option.value} label={option.label}>
                    {option.component}
                  </Select.Option>
                ))}
              </Select>
            </div>
          );
        }
      },
      {
        title: 'ASSIGNEE',
        key: 'assignee',
        width: 130,
        fixed: 'right',
        render: (data: Gql.ProjectDocument) => {
          const assignees = data?.requestForSignatures;
          const names = assignees
            ?.map(obj => obj?.signBy?.name)
            .filter(name => name)
            .join(', ');

          // Assignee Options
          // if dynamic workflow, cannot assign self
          const allowedAssignees = () => {
            if (data?.workflow === Gql.WorkflowType.Dynamic) {
              return assigneeOptions?.filter((assignee: any) => {
                return assignee?.value !== userMeData?.getUserMe?.id;
              });
            } else {
              return assigneeOptions;
            }
          };

          return (
            <Tooltip
              title={
                data?.workflow ? (
                  names
                ) : (
                  <Text className="text-white">Please choose workflow before assigning users</Text>
                )
              }
              placement="top"
              overlayInnerStyle={{ background: '#71787E', textAlign: 'center' }}
              overlayClassName="tooltip-grey-arrow"
            >
              <Select
                className={assignees?.length ? 'custom-select-multiple' : 'custom-select-multiple-border'}
                suffixIcon={!data?.workflow ? <Icon name="chevron-down-disabled" /> : <Icon name="chevron-down" />}
                style={{
                  width: 150,
                  textAlign: 'left'
                }}
                showSearch={true}
                placeholder="Select"
                value={assignees?.map((obj: any) => ({
                  label: (
                    <UserAvatar
                      username={obj?.signBy?.name}
                      src={obj?.signBy?.avatar}
                      tooltip={obj?.signBy?.name}
                      align={{
                        offset: [-12, -9], 
                        }}
                      style={{ backgroundColor: obj?.signBy?.avatar ? "transparent" : obj?.signBy?.color || "", color: "#ffffff" }}
                    />
                  ),
                  value: obj?.signBy?.id
                }))}
                onSearch={async value => {
                  setAssigneeFilter(value);
                }}
                onSelect={vals => {
                  if (data.workflow === Gql.WorkflowType.Dynamic && (data?.requestForSignatures?.length as any) >= 1) {
                    message.error('Dynamic workflow can only have 1 assignee');
                    return;
                  }
                  onAssigneeSelect(vals, data, assignees);
                }}
                optionFilterProp="children"
                filterOption={(input, option) => {
                  return (
                    option?.key?.toLowerCase()?.indexOf(input.toLowerCase()) >= 0 ||
                    (option?.title?.toLowerCase() ?? '').indexOf(input.toLowerCase()) >= 0
                  );
                }}
                onDeselect={vals => onAssigneeDeselect(vals, data, assignees)}
                dropdownStyle={{ scrollbarColor: 'grey white' }}
                mode={'multiple'}
                dropdownMatchSelectWidth={false}
                labelInValue
                optionLabelProp="label"
                disabled={data?.workflow === null}
                tagRender={(props: any) => {
                  const { label } = props;

                  return (
                    <div>
                      {label}
                    </div>
                  );
                }}
                options={allowedAssignees()}
              />
            </Tooltip>
          );
        }
      },
      {
        title: 'SUB GROUP',
        key: 'subGroup',
        width: 130,
        fixed: 'right',
        render: (data: Gql.ProjectDocument) => {
          const options = groups?.map((group: any) => {
            //disable fixtures group
            if (fixturesGroups.includes(group?.name)) {
              return {
                label: group?.name,
                value: group?.id,
                disabled: false,
                children:
                  group?.name === 'Request For Information' || group?.name === 'Non Conformance Report'
                    ? group?.children?.map((child: any) => {
                        return {
                          label: child?.name,
                          value: child?.id
                        };
                      })
                    : null
              };
            }

            // if have children make it as a group
            if (group?.children?.length > 0) {
              return {
                label: <Tooltip title={group?.name}>{group?.name}</Tooltip>,
                value: group?.id,
                children: group?.children?.map((child: any) => {
                  return {
                    label: child?.name,
                    value: child?.id
                  };
                })
              };
            } else {
              return {
                label: <Tooltip title={group?.name}>{group?.name}</Tooltip>,
                value: group?.id,
                disabled: !noChildrenGroups.includes(group?.name.toLowerCase())
              };
            }
          });

          return (
            <div onClick={e => e.stopPropagation()}>
              <Cascader
                suffixIcon={<Icon name="chevron-down" />}
                options={options}
                popupClassName="workspace-subgroup-popup"
                value={[data?.workspaceGroup?.name as string]}
                className={'custom-select-multiple'}
                dropdownStyle={{ scrollbarColor: 'grey white' }}
                onChange={value => {
                  if (!value) return;

                  const getLastValue = value[value.length - 1];

                  updateDocument({
                    variables: {
                      input: {
                        id: data.id,
                        update: { workspaceGroupId: getLastValue as string }
                      }
                    }
                  });
                }}
                placeholder="Please select"
              />
            </div>
          );
        }
      },
      {
        title: '',
        key: 'action',
        width: 130,
        fixed: 'right',
        render: (data: Gql.ProjectDocument) => {
          const hasWorkflowAndAssignee = data?.workflow && (data?.requestForSignatures?.length ?? 0) > 0;

          return (
            <div className="flex gap-4">
              <Tooltip
                title="View Document"
                placement="top"
                overlayInnerStyle={{ background: '#71787E', textAlign: 'center' }}
                overlayClassName="tooltip-grey-arrow"
              >
                <Button
                  onClick={() => view(data?.requestForSignatures, data?.status, userMeData, data?.id)}
                  className="!font-normal"
                >
                  View
                </Button>
              </Tooltip>

              <Tooltip
                overlayInnerStyle={{ background: '#71787E', textAlign: 'center' }}
                overlayClassName="max-w-[200px] tooltip-grey-arrow"
                title={
                  hasWorkflowAndAssignee
                    ? 'Submit this document for approval'
                    : 'Workflow and assignee are required to proceed'
                }
              >
                <Button
                  className={`!font-normal border-none ${
                    hasWorkflowAndAssignee ? 'bg-[#CCEAFC] shadow text-[#378DDC]' : 'custom-disabled-overview-btn'
                  }`}
                  disabled={!hasWorkflowAndAssignee}
                  onClick={() => {
                    if (data.xfdf) {
                      Modal.confirm({
                        title: 'Warning > Request Approval',
                        content: 'Your document is in Draft Mode. Please save the document before requesting approval.',
                        onOk: async () => {
                          router.replace({
                            query: {
                              ...router.query,
                              documentDetailsId: data.id,
                              workflowType: data?.workflow
                            }
                          });
                        },
                        okText: 'View Document',
                        onCancel: () => {},
                        cancelText: 'Cancel'
                      });
                    } else {
                      showRequestConfirm(data);
                    }
                  }}
                >
                  Request Approval
                </Button>
              </Tooltip>

              <Tooltip
                title="Download"
                placement="top"
                overlayInnerStyle={{ background: '#71787E', textAlign: 'center' }}
                overlayClassName="tooltip-grey-arrow"
              >
                <Button type="link" className="p-0" onClick={() => onDownload(data?.id)}>
                  <Icon name="download" fill="#7C8085" />
                </Button>
              </Tooltip>

              <Tooltip
                title="Delete"
                placement="top"
                overlayInnerStyle={{ background: '#71787E', textAlign: 'center' }}
                overlayClassName="tooltip-grey-arrow"
              >
                <Button type="link" className="p-0" onClick={() => showDeleteConfirm(data?.id)}>
                  <Icon name="bin" fill="#C94C4F" width={18} height={18} />
                </Button>
              </Tooltip>
            </div>
          );
        }
      }
    ];

    // Process Columns
    const processColumns = [
      {
        title: <div className="relative left-[-5px]">TITLE</div>,
        key: 'name',
        render: (data: any) => {
          const { fileUrl } = data;

          return (
            <Space className="py-1 relative left-[-5px]" align="start">
              {data.isDocsStored === true ? <Icon name="store-document"></Icon> : <Icon name="pdf"></Icon>}

              <div
                className="cursor-pointer"
                style={{ wordWrap: 'break-word', wordBreak: 'break-word' }}
                onClick={() => {
                  const { group, status, parentCode } = filterData;

                  if (!group) delete router.query.groupId;
                  if (!status) delete router.query.status;
                  if (!parentCode) delete router.query.parentCode;

                  if (!group && !status && !parentCode) {
                    delete router.query.filter;
                  }

                  if (data?.workflow === null) {
                    showChooseWorkflowModal();
                    router.replace({
                      query: {
                        ...router.query,
                        documentId: data.id
                      }
                    });
                    return;
                  }

                  if (fileUrl) {
                    if (router.query && data.fileSystemType === 'Document') {
                      router.replace({
                        query: {
                          ...router.query,
                          documentDetailsId: data.id,
                          workflowType: data?.workflow
                        }
                      });
                    }
                  }
                  if (data?.fileSystemType === 'Folder') {
                    setDirectory(data);
                    pushRouter(data);
                  }
                }}
              >
                <Tooltip
                  title={data?.name}
                  mouseEnterDelay={0.5}
                  placement="topLeft"
                  overlayStyle={{ maxWidth: '380px' }}
                >
                  <div>
                    <div className="flex justify-start max-w-[380]">
                      <Paragraph
                        className="font-medium flex"
                        style={{ margin: 0, fontSize: 16, overflow: 'hidden' }}
                        id="document-name"
                      >
                        {data?.name}
                      </Paragraph>
                      {data?.workspaceGroup?.parent?.workspaceGroupUsers.length > 0 && (
                        <div
                          onClick={e => {
                            e.stopPropagation();
                            onShare(data);
                          }}
                          className=" cursor-pointer ml-2 pt-1 border-[#9B111E] font-light rounded-xl px-2 bg-[#9B111E] text-white text-sm flex m-auto"
                        >
                          <Icon name="lock-white" />
                          <span className="text-sm relative ml-[2px] -top-[2px] ">
                            {data?.workspaceGroup?.parent?.workspaceGroupUsers.length}
                          </span>
                        </div>
                      )}
                    </div>
                    <Paragraph
                      className="text-base"
                      style={{ margin: 0, width: 380, color: '#8c8c8c' }}
                      ellipsis={{ rows: 1, expandable: false }}
                    >
                      {data?.description}
                    </Paragraph>
                  </div>
                </Tooltip>
              </div>
            </Space>
          );
        }
      },
      {
        title: 'CODE',
        key: 'groupCode',
        fixed: 'right',
        width: 'auto',
        render: (data: any) => {
          return data?.groupCode ? (
            <p className="cursor-pointer">
              {`${
                fixturesGroups?.includes(data?.workspaceGroup?.name)
                  ? data?.workspaceGroup?.code
                  : data?.workspaceGroup?.parent?.code
              }-${data?.groupCode}`}
            </p>
          ) : (
            ''
          );
        }
      },
      {
        title: 'ID',
        key: 'code',
        fixed: 'right',
        render: (data: any) => {
          return data.allFormCode ? <p> {'#' + data.allFormCode}</p> : '';
        }
      },
      {
        title: 'STATUS',
        key: 'status',
        fixed: 'right',
        width: 'auto',
        render: (data: any) => getStatus(data?.status as string)
      },
      {
        title: 'COMMENTS',
        key: 'comment',
        fixed: 'right',
        width: 'auto',
        render: (data: any) => {
          const isHasComment = data?.comments && data?.comments?.length > 0;

          return (
            <div className={`flex gap-2 items-center ${isHasComment ? 'text-[#1EA8E0]' : ''}`}>
              {isHasComment ? (
                <Icon name="comment-active" width={16} height={16} />
              ) : (
                <Icon name="comment" width={16} height={16} />
              )}
              {isHasComment ? data?.comments?.length : null}
            </div>
          );
        }
      },
      {
        title: 'SUBMITTED ON',
        key: 'submittedOn',
        width: 'auto',
        fixed: 'right',
        render: (data: any) => {
          return <p className="m-auto">{data.submittedAt ? moment(tz(data.submittedAt)).format('D MMM YYYY') : '-'}</p>;
        }
      },
      {
        title: 'LAST MODIFIED',
        key: 'updatedAt',
        fixed: 'right',
        width: 'auto',
        render: (data: any) => {
          return <p>{moment(tz(data.updatedAt)).format('D MMM YYYY')}</p>;
        }
      },
      {
        title: 'ASSIGNED TO',
        key: 'requestForSignatures',
        fixed: 'right',
        width: 110,
        render: (data: any) => {
          const listedAssignees = concatenateAssignees(data?.requestForSignatures, data?.workflow);
      
          return (
            <div className="pr-4">
              <Avatar.Group className="grid grid-cols-4 px-[8px] evenly-spaced">
                {listedAssignees?.map((assignee: any, index: any) => {
                  const isHidden = assignee?.signBy?.name?.startsWith('+ ') || assignee?.isHidden;
      
                  // Set background color and font weight
                  const backgroundColor = isHidden
                    ? '#F5F5F5'
                    : assignee?.signBy?.color || '#E8EFFF';
                  const fontWeight = isHidden ? 'bold' : 'normal';
                  const fontColor = isHidden ? '#565656' : '#FFFFFF';
      
                  const tooltipContent = isHidden
                  ? (() => {
                      const namesString = assignee?.title || assignee?.signBy?.name || '';

                      const names = namesString.includes(',')
                        ? namesString.split(',') 
                        : namesString.split(' ').filter(Boolean); 
                      return (
                        <div style={{ display: 'flex', flexDirection: 'column', gap: '6px', lineHeight: '1.2' }}>
                          {names.map((name: string, idx: number) => (
                            <span key={idx}>{name.trim()}</span> // One name per line
                          ))}
                        </div>
                      );
                    })()
                  : `${assignee?.signBy?.name}${assignee?.status ? ` - ${assignee?.status}` : ''}`;
    
                return (
                  <UserAvatar
                      key={index}
                      username={assignee?.signBy?.name} // Still shows "+ 2" on avatar
                      src={assignee?.signBy?.avatar}
                      style={{
                        backgroundColor,
                        color: fontColor,
                        fontWeight,
                      }}
                      tooltip={tooltipContent} // Pass string for non-hidden, JSX for hidden
                    />
                  );
                })}
              </Avatar.Group>
            </div>
          );
        },
      },
      {
        title: 'GROUP',
        dataIndex: 'workspaceGroup',
        key: 'workspaceGroup',
        fixed: 'right',
        width: 110,
        align: 'left' as const,
        render: (group: any) => {
          return group ? (
            <Tooltip key={group.id} title={group?.name}>
              <div className="w-[110px]">
                <div className="truncate">{group?.name}</div>
              </div>
            </Tooltip>
          ) : (
            'N/A'
          );
        }
      },
      {
        title: '',
        key: 'action',
        fixed: 'right',
        width: 'auto',
        render: (data: any) => {
          const items: any = [
            {
              label: <p>Download</p>,
              key: 'DownloadDocument'
            }
          ];

          if (data?.status === Gql.ProjectDocumentStatus.Approved) {
            items.push({
              label: <p>Store</p>,
              key: 'Store'
            });
          }

          if (data?.fileSystemType === 'Document' && data?.status === Gql.ProjectDocumentStatus.Draft) {
            items.push({
              label: <p>Request Approval</p>,
              key: 'RequestApproval'
            });
          }

          if (canDelete) {
            items.push({
              label: <p className="text-crimsonRed">Delete</p>,
              key: 'Delete'
            });
          }

          const itemsWithDivider: any = [];
          items.forEach((item: any, index: number) => {
            itemsWithDivider.push(item); // Add the original item
            if (index < items.length - 1) {
              // Check if this is not the last item
              itemsWithDivider.push({ type: 'divider', disabled: viewOnly }); // Add the divider
            }
          });

          return (
            <ThreeDotsDropDown
              onClick={(e: any) => {
                e.domEvent.stopPropagation();
                if (e.key === 'DownloadDocument') {
                  onDownload(data.id);
                }
                if (e.key === 'RequestApproval') {
                  if (data.xfdf) {
                    Modal.confirm({
                      title: 'Warning > Request Approval',
                      content: 'Your document is in Draft Mode. Please save the document before requesting approval.',
                      onOk: async () => {
                        router.replace({
                          query: {
                            ...router.query,
                            documentDetailsId: data.id,
                            workflowType: data?.workflow
                          }
                        });
                      },
                      okText: 'View Document',
                      onCancel: () => {},
                      cancelText: 'Cancel'
                    });
                  } else {
                    showRequestConfirm(data);
                  }
                }
                if (e.key === 'Store') {
                  setStoreDocumentId(data.id.toString());
                }
                if (e.key === 'Delete') {
                  showDeleteConfirm(data.id);
                }
              }}
              items={itemsWithDivider}
            />
          );
        }
      }
    ];

    const columns = driveType === Gql.ProjectDocumentStatus.Draft ? draftColumns : processColumns;

    if (driveType === Gql.ProjectDocumentStatus.Draft) {
      return columns;
    }

    const selectedColumns = columns.filter(column => {
      const columnConfig = tableColumns.find((col: any) => col.key === column.key);
      return columnConfig ? columnConfig.show : false;
    });

    return selectedColumns;
  }, [driveType, tableColumns, documentLoading]);

  const handleDriveChange = (e: RadioChangeEvent) => {
    const temp = e.target?.value?.toString() ?? null;
    if (temp === 'Draft') {
      setFilterData({
        assignee: [],
        keyword: '',
        id: '',
        status: '',
        group: '',
        parentCode: '',
        groupCode: ''
      });
    }
    setDriveType(temp);
  };

  const isFilterDataExist = Object.values(filterData).some(value => {
    if (Array.isArray(value)) {
      return value.length > 0;
    }
    return !!value;
  });

  const {
    shareModal,
    showShareModal,
    setShareModalTitle,
    setWorkspaceGroupId,
    listOfUsersWithAccess,
    setShareFileOwner
  } = useShareModal({ viewOnly: viewSharedModal, refetch });

  const onShare = async (document: any) => {
    await getWorkspaceGroup({
      variables: {
        id: document.workspaceGroup.parent.id
      },
      onCompleted: data => {
        setViewSharedModal(true);
        setShareFileOwner(data?.workspaceGroup?.creator as any);
        setShareModalTitle(('Member with access for ' + data?.workspaceGroup?.name) as string);
        setWorkspaceGroupId(document.workspaceGroup.parent.id);
        showShareModal();
        listOfUsersWithAccess({
          variables: {
            workspaceGroupId: parseInt(document.workspaceGroup.parent.id)
          }
        });
      }
    });
  };

  const onStore = async () => {
    // if more than 10 show message
    if (selectedRowKeys.length > 10) {
      message.error('Only 10 documents can be stored at a time');
      return;
    }
    // get all the document that is selected, and check if the status
    // if the document is not approved, then show the error message
    const selectedDocuments = dataSource.filter((doc: Gql.ProjectDocument) =>
      selectedRowKeys.includes(doc.id.toString())
    );
    const notApprovedDocuments = selectedDocuments.filter((doc: Gql.ProjectDocument) => doc.status !== 'Approved');

    if (notApprovedDocuments.length > 0) {
      message.error('Please select only approved documents');
      return;
    }

    if (selectedDocuments.length > 10) {
      message.error('Only 10 documents can be stored at a time');
      return;
    }

    StoringModalRef?.current?.openModal();
  };

  useEffect(() => {
    if (storeDocumentId) {
      StoringModalOnClickRef?.current?.openModal();
    }
  }, [storeDocumentId]);

  return (
    <div className="absolute overflow-auto h-full w-full" ref={dropzoneRef}>
      {chooseWorkflowModal}
      {shareModal}

      <Spin tip={'Loading...'} spinning={requestSignLoading || loading || documentsDeleting}>
        <div className="flex flex-nowrap items-center justify-between px-5 py-[12px]">
          <div>
            <Radio.Group value={driveType} onChange={handleDriveChange} className="allFormRadio my-[20px]">
              <Radio.Button key={1} value={''} className="font-semibold">
                Processed
              </Radio.Button>
              {!viewOnly && (
                <Radio.Button key={0} value={'Draft'} className="font-semibold">
                  Draft
                </Radio.Button>
              )}
            </Radio.Group>
            {dirId && (
              <Breadcrumb>
                <Breadcrumb.Item className="text-gray90 text-md">
                  <Link href="/digital-form/all-form">
                    <HomeOutlined />
                  </Link>
                </Breadcrumb.Item>
                {currentDirs.map((value: any, index) => (
                  <Breadcrumb.Item className="text-gray90 text-md" key={index}>
                    <Link href={`/digital-form/all-form/${currentDirs.slice(0, index + 1).join('/')}`}>{value}</Link>
                  </Breadcrumb.Item>
                ))}
              </Breadcrumb>
            )}
          </div>
          <Row className="items-center gap-0.5 my-[20px]">
            {driveType !== Gql.ProjectDocumentStatus.Draft && (
              <Row className="items-center gap-3">
                <Switch
                  checkedChildren="Assigned To Me"
                  unCheckedChildren="Assigned To Me"
                  onChange={async checked => {
                    await setIsOnlyMeFilter(checked);
                  }}
                />
              </Row>
            )}

            {driveType !== Gql.ProjectDocumentStatus.Draft && (
              <Popover
                content={<ShowHideColumn tableColumns={tableColumns} onColumnToggle={name => toggleColumn(name)} />}
                trigger="hover"
              >
                <Button
                  icon={<Icon name="columns" className="mr-1" />}
                  className="mx-3 flex items-center justify-center  h-[40px]"
                >
                  Column
                </Button>
              </Popover>
            )}

            <Col>
              {driveType !== Gql.ProjectDocumentStatus.Draft && (
                <Badge dot={isFilterDataExist} offset={[-3, 3]}>
                  <Button
                    className="h-[40px] rounded-lg border-gray40 bg-white"
                    onClick={() => {
                      filterDrawerRef?.current?.pushDrawer();
                    }}
                    icon={<FilterOutlined />}
                  >
                    Filters
                  </Button>
                </Badge>
              )}
              {canCreate && driveType === Gql.ProjectDocumentStatus.Draft && (
                <Button
                  type="primary"
                  className="ml-2 rounded-lg text-white border-gray40 h-[40px]"
                  onClick={() => {
                    uploadWorkspaceDocModalRef?.current?.openModal();
                  }}
                >
                  <Space>Create Document</Space>
                </Button>
              )}
            </Col>
          </Row>
        </div>

        <Table
          scroll={{ x: 'max-content' }}
          rowSelection={{
            type: 'checkbox',
            selectedRowKeys,
            onChange: onSelectChange,
            hideSelectAll: selectedRowKeys.length === 0,
            getCheckboxProps: e => {
              return {
                style: {
                  display: selectedRowKeys.includes(e.id) || e.id === hoveredRowIndex ? 'flex' : 'none',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginBottom: 10
                }
              };
            }
          }}
          className="px-5 dashboard-table mb-[30px]"
          dataSource={
            isArray(dataSource)
              ? dataSource?.map((item: any) => ({
                  ...item,
                  key: item.id
                }))
              : dataSource
          }
          columns={filteredColumns as any}
          size="small"
          onRow={(record, index) => {
            const attr = {
              index,
              moveDoc,
              className: 'cursor-pointer',
              onSelect: () => driveType !== 'Draft' && handleRowClick(record),
              onMouseEnter: () => setHoveredRowIndex(record?.id), // when mouse enters the row
              onMouseLeave: () => setHoveredRowIndex(null) // when mouse leaves the row
            };

            return attr as React.HTMLAttributes<any>;
          }}
          pagination={{
            defaultPageSize: 8,
            pageSize: queries.paging.limit,
            current: queries.paging.offset / queries.paging.limit + 1,
            total: data?.getWorkSpaceAllForm.totalCount ?? 0
          }}
          onChange={paginate => {
            const { current, pageSize } = paginate;
            if (pageSize !== undefined && current !== undefined) {
              setPaging({
                offset: (current - 1) * pageSize,
                limit: pageSize
              });
            }
          }}
          loading={documentLoading}
        />

        <LinearWorkspaceDrawer
          ref={LinearWorkspaceDrawerRef}
          onSavedDocs={() => {
            refetch();
          }}
          status={driveType}
          onShare={onShare}
        />

        <DynamicWorkspaceDrawer
          ref={DynamicWorkspaceDrawerRef}
          onShare={onShare}
          onSavedDocs={() => {
            refetch();
          }}
          status={driveType}
        />

        <WorkspaceUpload
          dirId={dirId as any}
          onSaved={() => refetch()}
          type={Gql.CategoryType.AllForm}
          ref={uploadWorkspaceDocModalRef}
        />
      </Spin>
      <FilterDrawer
        ref={filterDrawerRef}
        driveType={driveType}
        onSubmit={async (values: any) => {
          setFilterData(values);
          setPaging({
            limit: 10,
            offset: 0
          });
        }}
      />
      <StoringModal
        ref={StoringModalRef}
        subs={companySubscriptions}
        documentIds={selectedRowKeys?.map(id => parseInt(id as string))}
        onSuccessStore={() => {
          setSelectedRowKeys([]);
        }}
      />

      <StoringModal
        ref={StoringModalOnClickRef}
        subs={companySubscriptions}
        documentIds={storeDocumentId ? [parseInt(storeDocumentId)] : []}
        onSuccessStore={() => {
          setSelectedRowKeys([]);
        }}
      />

      <ManageModal
        onStore={onStore}
        selectedRowKeys={selectedRowKeys}
        onBulkDownload={onBulkDownload}
        showDeletesConfirm={showDeletesConfirm}
        canDelete={canDelete ?? false}
        setSelectedRowKeys={setSelectedRowKeys}
        data={dataSource}
      />
    </div>
  );
};

// Status of documents
const getStatus = (type: string) => _.get(status, type) ?? '';
const status = {
  Draft: (
    <Space align="center">
      <Icon name="status-draft" />
      <p className="mb-1" id="status">
        Draft
      </p>
    </Space>
  ),
  Submitted: (
    <Space align="center">
      <Icon name="status-submitted" />
      <p className="mb-1" id="status">
        Submitted
      </p>
    </Space>
  ),
  Pending: (
    <Space align="center">
      <Icon name="status-pending" />
      <p className="mb-1" id="status">
        Pending
      </p>
    </Space>
  ),
  InReview: (
    <Space align="center">
      <Icon name="status-inReview" />
      <p className="mb-1" id="status">
        In Review
      </p>
    </Space>
  ),
  Amend: (
    <Space align="center">
      <Icon name="status-amend" />
      <p className="mb-1" id="status">
        Amend
      </p>
    </Space>
  ),
  InProgress: (
    <Space align="center">
      <Icon name="status-inProgress" />
      <p className="mb-1" id="status">
        In Progress
      </p>
    </Space>
  ),
  Approved: (
    <Space align="center">
      <Icon name="status-approved" />
      <p className="mb-1" id="status">
        Approved
      </p>
    </Space>
  ),
  Rejected: (
    <Space align="center">
      <Icon name="status-rejected" />
      <p className="mb-1" id="status">
        Rejected
      </p>
    </Space>
  )
};

AllForm.auth = true;
AllForm.Layout = DigitalFormTabLayout;
export default AllForm;
