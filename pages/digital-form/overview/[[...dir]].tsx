import { DownOutlined, ExclamationCircleOutlined, RightOutlined } from '@ant-design/icons';
import { Icon } from '@commons';
import DigitalFormTabLayout from '@components/digital-form/DigitalFormTabLayout';
import CardOverview from '@components/digital-form/overview/CardOverview';
import SearchInput from '@components/forms/FormsInput/SearchInputs/OverviewSearch';
import useModal from '@components/hooks/useModal';
import useQueries from '@components/hooks/useQueries';
import { useColumns } from '@components/hooks/useScheduleColumn';
import ThreeDotsDropDown from '@components/ThreeDotsDropDown';
import * as Gql from '@graphql';
import { manageFiles } from '@utils/authority';
import { onError } from '@utils/error';
import { Breadcrumb, Button, Form, Input, message, Modal, Popover, Radio, Row, Space, Spin, Table, Tooltip } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import Paragraph from 'antd/lib/typography/Paragraph';
import _, { debounce } from 'lodash';
import Link from 'next/link';
import { useRouter } from 'next/router';
import ShowHideColumn from 'pages/schedules/ShowHideColumn';
import { AlignType } from 'rc-table/lib/interface';
import { ReactNode, useEffect, useMemo, useRef, useState } from 'react';
import { useShareModal } from 'src/components/digital-form/overview/ShareModal';
import { getWorkspaceThreeDotsItems, onFilterStatus } from 'src/components/digital-form/overview/Utils';
import useTimeFormat from 'src/hooks/useTimeFormat';

const protectedGroups = ['Ungroup Documents', 'Site Diary'];

const Overview = () => {
  const today = new Date();
  const todayDate = useTimeFormat({ dateTime: today.toISOString(), timeFormat: 'DD-MMM-YYYY' });
  const [projectUserRole, setProjectUserRole] = useState<string>('');
  const [viewSharedModal, setViewSharedModal] = useState<boolean>(false);

  const [form] = useForm();
  const [searchForm] = useForm();
  const { confirm } = Modal;

  const nameInputRef = useRef<any>(null);
  const codeInputRef = useRef<any>(null);

  const { canDelete, canShare, canCreate } = manageFiles(localStorage.getItem('ProjectUserRole') || '');
  const preferredPageSize = localStorage.getItem('preferredPageSize')
  const router = useRouter();
  const [dataSources, setDataSources] = useState<any[]>([]);
  const [tableColumns, toggleColumn] = useColumns('workspace');

  const [defaultExpandedRowKeys, setDefaultExpandedRowKeys] = useState<string[]>([]);

  const isUpdatingGroup = form?.getFieldValue('key');
  const viewOnly = projectUserRole === 'CanView';
  const { queries, setPaging, setFilter } = useQueries<Gql.WorkspaceGroupFilter, Gql.WorkspaceGroupSort>();
  const [documentRecovery, setDocumentRecovery] = useState<boolean>(false);
  const [overviewType, setOverviewType] = useState<string>('List');

    // set default page size
    useEffect(() => {
      if (preferredPageSize) {
        setPaging({
          offset: queries.paging.offset,
          limit: parseInt(preferredPageSize)
        })
      }
    },[])

  const overviewOptions = [
    { label: <Icon name="list-overview" className='m-auto absolute -top-0.5 left-0.5' />, value: 'List' },
    { label: <Icon name="card-overview" className='m-auto absolute top-1 left-2.5' />, value: 'Card' },
  ];

  const sanitizedData = (group: Gql.WorkspaceGroup) => {

    return {
      key: group.id,
      workspaceGroupName: `${group.name} (${group.totalCount ?? 0})`,
      submittedCount: group.submittedCount ?? 0,
      inReviewCount: group.inReviewCount ?? 0,
      approvedCount: group.approvedCount ?? 0,
      rejectedCount: group.rejectedCount ?? 0,
      inProgressCount: group.inProgressCount ?? 0,
      pendingCount: group.pendingCount ?? 0,
      amendCount: group.amendCount ?? 0,
      name: group.name,
      id: group.id,
      isChildren: false,
      code: group.code,
      workspaceGroupId: group.workspaceGroupId,
      workspaceGroupUsersCount: group.workspaceGroupUsers?.length,
      workspaceGroupUsers: group.workspaceGroupUsers,
      creator: group.creator,
      fixtures: protectedGroups.includes(group?.name),
      totalCount: group?.totalCount,
      children: group.children?.map?.(c => {
        return sanitizedChildren(c);
      })
    };
  };

  const sanitizedChildren = (child: Gql.WorkspaceGroup) => {
    return {
      key: child?.id,
      workspaceGroupName: `${child.name} (${child.totalCount ?? 0})`,
      submittedCount: child.submittedCount ?? 0,
      inReviewCount: child.inReviewCount ?? 0,
      approvedCount: child.approvedCount ?? 0,
      rejectedCount: child.rejectedCount ?? 0,
      inProgressCount: child.inProgressCount ?? 0,
      pendingCount: child.pendingCount ?? 0,
      amendCount: child.amendCount ?? 0,
      name: child.name,
      isChildren: true,
      id: child.id,
      code: child.code,
      workspaceGroupId: child.workspaceGroupId
    };
  };

  const sortGroupByAlphanumeric = (a: Gql.WorkspaceGroup, b: Gql.WorkspaceGroup) => {
    if (a.name === 'Ungroup Documents' || a.name === 'Site Diary') return -1;
    if (b.name === 'Ungroup Documents' || b.name === 'Site Diary') return 1;

    return a.name.localeCompare(b.name, undefined, { numeric: true, sensitivity: 'base' });
  };

  const [getAutosavedCount] = Gql.useGetAutosavedProjectDocumentCountLazyQuery({
    fetchPolicy: 'network-only',
    variables: {
      filter: {
        category: {
          eq: Gql.CategoryType.StandardForm
        }
      }
    },
    onCompleted: data => {
      const totalCount = data?.getAutosavedProjectDocumentCount?.totalCount ?? 0;
      if (totalCount === 0) {
        return;
      }

      setDocumentRecovery(true);
    }
  });

  const [createGroup] = Gql.useCreateWorkspaceGroupMutation({
    onCompleted(data) {
      setPaging({
        offset: 0,
        limit: 10
      });

      if (!data?.createWorkspaceGroup?.workspaceGroupId) {
        refetch();
      } else {
        // if the data have the workspace group id, then find the index of the workspace group
        const index = dataSources.findIndex(d => d?.id === data?.createWorkspaceGroup?.workspaceGroupId);
        // if the index is not -1, then set the children of the workspace group
        if (index !== -1) {
          const newGroup = sanitizedChildren(data?.createWorkspaceGroup as any);
          dataSources[index].children = [...dataSources[index].children, newGroup];

          dataSources?.sort?.(sortGroupByAlphanumeric);

          setDataSources([...dataSources]);
        }
      }
      message.success('Group created successfully');
      form.resetFields();
      closeCreateGroupModal();

      return setDefaultExpandedRowKeys([...defaultExpandedRowKeys, data?.createWorkspaceGroup?.id]);
    },
    onError: onError as any
  });

  const [updateGroup] = Gql.useUpdateWorkspaceGroupMutation({
    onCompleted(data) {
      if (!data?.updateWorkspaceGroup?.workspaceGroupId) {
        const index = dataSources.findIndex(d => d?.id === data?.updateWorkspaceGroup?.id);
        if (index !== -1) {
          dataSources[index] = sanitizedData(data?.updateWorkspaceGroup as any);
          dataSources?.sort?.(sortGroupByAlphanumeric);
          setDataSources([...dataSources]);
        }
      } else {
        const index = dataSources.findIndex(d => d?.id === data?.updateWorkspaceGroup?.workspaceGroupId);
        if (index !== -1) {
          const childIndex = dataSources[index].children?.findIndex(
            (d: Gql.WorkspaceGroup) => d?.id === data?.updateWorkspaceGroup?.id
          );
          if (childIndex !== -1) {
            dataSources[index].children[childIndex] = sanitizedChildren(data?.updateWorkspaceGroup as any);
            dataSources[index].children?.sort?.(sortGroupByAlphanumeric);
            setDataSources([...dataSources]);
          }
        }
      }
      message.success(`Successfully updated the ${data?.updateWorkspaceGroup?.workspaceGroupId ? 'sub ' : ''}group`);
      closeCreateGroupModal();

      return data?.updateWorkspaceGroup?.workspaceGroupId
        ? setDefaultExpandedRowKeys([...defaultExpandedRowKeys, data?.updateWorkspaceGroup?.id])
        : null;
    },
    onError: onError as any
  });

  const [deleteGroup] = Gql.useDeleteOneWorkspaceGroupMutation({
    onCompleted(data) {
      if (!data?.deleteOneWorkspaceGroup?.id) return;

      let group = null;
      const index = dataSources.findIndex(d => d?.id === data?.deleteOneWorkspaceGroup?.id);
      if (index !== -1) {
        group = dataSources[index];
        dataSources.splice(index, 1);
        setDataSources([...dataSources]);
      } else {
        const parentIndex = dataSources.findIndex(
          d => d?.children?.findIndex((c: Gql.WorkspaceGroup) => c?.id === data?.deleteOneWorkspaceGroup?.id) !== -1
        );
        if (parentIndex !== -1) {
          const childIndex = dataSources[parentIndex].children?.findIndex(
            (c: Gql.WorkspaceGroup) => c?.id === data?.deleteOneWorkspaceGroup?.id
          );
          if (childIndex !== -1) {
            group = dataSources[parentIndex].children[childIndex];
            dataSources[parentIndex].children?.splice(childIndex, 1);
            setDataSources([...dataSources]);
          }
        }
      }

      message.success(`Successfully deleted the ${group?.workspaceGroupId ? 'sub ' : ''}group ${group?.name}`);
      form.resetFields();
    },
    onError(error) {
      message.error('Failed to delete group');
    }
  });

  const [getWorkspaceGroups, { data, refetch, fetchMore: fetchMoreWorkspaceGroups, loading }] =
    Gql.useGetWorkspaceGroupsLazyQuery({
      variables: {
        ...queries,

        sorting: [
          {
            direction: Gql.SortDirection.Desc,
            field: Gql.WorkspaceGroupWithChildrenSortFields.CreatedAt
          }
        ],
        paging: {
          limit: queries?.paging?.limit,
          offset: queries?.paging?.offset
        }
      },
      onError: onError,
      fetchPolicy: 'cache-and-network',
      onCompleted(data) {

        // card overview data
        if (overviewType === 'Card') {

          // append the new data to the existing data
          setDataSources([..._.uniqBy([...dataSources, ...(data?.getWorkspaceGroups?.nodes?.map?.(group => {
            return sanitizedData(group as any);
          }) ?? [])], 'id')]);
          
        } else {

          const groups = data?.getWorkspaceGroups?.nodes?.map?.(group => {
            return sanitizedData(group as any);
          });

          setDataSources(groups ?? []);
          if (queries?.filter?.name?.like?.length && queries?.filter?.name?.like?.length > 0) {
            setDefaultExpandedRowKeys(data?.getWorkspaceGroups?.nodes?.map?.(d => d?.id));
          } else {
            setDefaultExpandedRowKeys([]);
          }
        }
      }
    });

  const onFetchMoreWorkspaceGroups = () => {
    const { offset, limit } = queries.paging;
    setPaging({
      offset: offset + limit,
      limit
    });
  };

  useEffect(() => {
    const fetch = async () => {
      setProjectUserRole(localStorage.getItem('ProjectUserRole') || '');
      await getWorkspaceGroups();
      const reviewDocumentDate = window.localStorage.getItem('documentRecoveryReview');
      // if no review document date or the review document date is not today, then get the autosaved count
      if (!reviewDocumentDate || reviewDocumentDate != todayDate) {
        getAutosavedCount();
      }
    };
    fetch();
  }, [getWorkspaceGroups]);

  const onFinish = async (values: Gql.CreateWorkspaceGroupInputDto) => {
    const { name, code } = values;
    const workspaceGroupId = form?.getFieldValue('id');

    try {
      if (name === 'Ungroup Documents' || name === 'Site Diary')
        return message.error('This group name cannot be used.');

      await createGroup({
        variables: {
          input: {
            name,
            projectId: localStorage.getItem('projectId') ?? '',
            code: code?.toUpperCase?.() ?? null,
            workspaceGroupId: parseFloat(workspaceGroupId)
          }
        }
      });
    } catch (e: any) {
      onError(e);
    }
  };

  // Search Function
  const onSearch = (values: any) => {
    setFilter({
      name: {
        like: values.keyword
      }
    });
  };

  // Rename Function
  const onRename = async (values: Gql.UpdateWorkspaceGroupInputDto) => {
    if (values.name === 'Ungroup Documents') return message.error('This group name cannot be used.');
    try {
      await updateGroup({
        variables: {
          input: {
            id: form.getFieldValue('id'),
            name: values.name,
            code: form?.getFieldValue('key') === 'Rename' ? form.getFieldValue('code').toUpperCase?.() : '',
            ...(form?.getFieldValue('workspaceGroupId') && {
              workspaceGroupId: parseFloat(form.getFieldValue('workspaceGroupId'))
            })
          }
        }
      });
    } catch (e: any) {
      message.error(e.message);
    }
  };

  // Delete Function
  const onDelete = async (id: string) => {
    try {
      await deleteGroup({
        variables: {
          id
        }
      });
    } catch (e: any) {
      onError(e);
    }
  };

  // Show confirm delete group
  const showDeleteConfirm = (id: string, title: string) => {
    confirm({
      title: 'Are you sure?',
      icon: <ExclamationCircleOutlined />,
      content: (
        <p>
          Do you really want to delete the <span className="font-semibold">{title}</span>?
        </p>
      ),
      okText: 'Yes',
      okType: 'danger',
      cancelText: 'No',
      onOk() {
        if (title === 'Site Diary') return message.error('This group cannot be deleted.');
        else {
          onDelete(id);
        }
      },
      onCancel() { }
    });
  };

  // Create and edit group modal
  const [modal, showCreateGroupModal, closeCreateGroupModal] = useModal({
    onCancel: () => {
      form.resetFields();
    },
    title:
      isUpdatingGroup === 'AddGroup'
        ? 'Create Group'
        : isUpdatingGroup === 'AddSubGroup'
          ? 'Add Sub Group'
          : isUpdatingGroup === 'Rename'
            ? 'Rename Group'
            : 'Rename Sub Group',
    content:
      (setTimeout(() => {
        nameInputRef?.current?.focus();
      }, 100),
        (
          <>
            {isUpdatingGroup === 'Rename' || isUpdatingGroup === 'RenameSubGroup' ? (
              <Form layout="vertical" form={form} onFinish={onRename} requiredMark={false} initialValues={{}}>
                <Form.Item
                  label="Group name"
                  name="name"
                  rules={[{ required: true, message: 'Group name is required!' }]}
                >
                  <Input defaultValue={form?.getFieldValue('name')} ref={nameInputRef} />
                </Form.Item>
                {form?.getFieldValue('key') !== 'RenameSubGroup' && (
                  <Form.Item label="Code" name="code" rules={[{ required: true, message: 'Code is required!' }]}>
                    <Input
                      ref={codeInputRef}
                      maxLength={3}
                      className="uppercase"
                      defaultValue={form?.getFieldValue('code')}
                    />
                  </Form.Item>
                )}
                <div style={{ marginTop: 30 }} className="flex justify-end">
                  <Button id="create" htmlType="submit" type="primary" disabled={viewOnly}>
                    Rename
                  </Button>
                </div>
              </Form>
            ) : (
              <Form layout="vertical" form={form} onFinish={onFinish} requiredMark={false}>
                <Form.Item
                  label={isUpdatingGroup === 'AddGroup' ? 'Group name' : 'Sub Group name'}
                  name="name"
                  rules={[{ required: true, message: 'Group name is required!' }]}
                >
                  <Input ref={nameInputRef} />
                </Form.Item>
                {isUpdatingGroup === 'AddGroup' && (
                  <Form.Item
                    label="Code"
                    name="code"
                    rules={
                      form?.getFieldValue('key') !== 'AddSubGroup'
                        ? [{ required: true, message: 'Code is required!' }]
                        : []
                    }
                  >
                    <Input ref={codeInputRef} maxLength={3} minLength={3} className="uppercase" />
                  </Form.Item>
                )}
                <div style={{ marginTop: 30 }} className="flex justify-end">
                  <Button htmlType="submit" type="primary" disabled={viewOnly}>
                    Create
                  </Button>
                </div>
              </Form>
            )}
          </>
        ))
  });

  const handleOnCellClick = (record: any, status: Gql.ProjectDocumentStatus) => {
    if (record?.isChildren || record?.fixtures) {
      onFilterStatus(status, router, 'children', record?.id ?? 'Ungroup Documents');
    } else {
      onFilterStatus(status, router, 'parent', record?.code);
    }
  };

  const isProtectedGroup = (data: any) => protectedGroups.includes(data?.name);

  const columns = [
    {
      title: <div style={{ paddingLeft: '12px' }}>GROUPS</div>,
      key: 'groups',
      align: 'left' as AlignType,
      render: (data: any) => {
        if (data?.isChildren || isProtectedGroup(data)) {
          return (
            <Paragraph
              onClick={() => {
                if (isProtectedGroup(data)) {
                  router.push({
                    pathname: '/digital-form/all-form',
                    query: {
                      groupId: data?.id,
                      filter: true
                    }
                  });
                }

                if (data?.isChildren) {
                  router.push({
                    pathname: '/digital-form/all-form',
                    query: {
                      groupId: data?.id,
                      filter: true
                    }
                  });
                }
              }}
              className={` flex pl-4 gap-2 items-center ${!data?.isChildren && 'font-medium'} cursor-pointer `}
              style={{ color: 'black', margin: '0.5em', marginLeft: data?.isChildren ? '1em' : '0' }}
              ellipsis={{ rows: 1, expandable: false, tooltip: data?.workspaceGroupName, suffix: '' }}
            >
              {data?.workspaceGroupName?.includes('Ungroup Documents') && <Icon name="ungroup-documents" />}
              {data?.workspaceGroupName?.includes('Site Diary') && <Icon name="site-diary" />}
              {data?.workspaceGroupName}
            </Paragraph>
          );
        }
      }
    },
    {
      title: 'CODE',
      key: 'code',
      dataIndex: 'code',
      width: 100,
      align: 'center' as AlignType
    },
    {
      title: 'SUBMITTED',
      key: 'submitted',
      width: 100,
      align: 'center' as AlignType,
      onCell: (record: any, rowIndex: any) => {
        return {
          onClick: () => handleOnCellClick(record, Gql.ProjectDocumentStatus.Submitted)
        };
      },
      render: (data: any) => {
        return <span className={`cursor-pointer ${!data?.isChildren && 'font-bold'}`}>{data?.submittedCount}</span>;
      }
    },
    {
      title: 'PENDING',
      key: 'pending',
      width: 100,
      align: 'center' as AlignType,
      onCell: (record: any, rowIndex: any) => {
        return {
          onClick: () => handleOnCellClick(record, Gql.ProjectDocumentStatus.Pending)
        };
      },
      render: (data: any) => {
        return <span className={` cursor-pointer ${!data?.isChildren && 'font-bold'}`}>{data?.pendingCount}</span>;
      }
    },
    {
      title: 'IN REVIEW',
      key: 'in_review',
      width: 100,
      align: 'center' as AlignType,
      onCell: (record: any, rowIndex: any) => {
        return {
          onClick: () => handleOnCellClick(record, Gql.ProjectDocumentStatus.InReview)
        };
      },
      render: (data: any) => {
        return <span className={` cursor-pointer ${!data?.isChildren && 'font-bold'}`}>{data?.inReviewCount}</span>;
      }
    },
    {
      title: 'TO AMEND',
      key: 'to_amend',
      width: 100,
      align: 'center' as AlignType,
      onCell: (record: any, rowIndex: any) => {
        return {
          onClick: () => handleOnCellClick(record, Gql.ProjectDocumentStatus.Amend)
        };
      },
      render: (data: any) => {
        return <span className={` cursor-pointer ${!data?.isChildren && 'font-bold'}`}>{data?.amendCount}</span>;
      }
    },
    {
      title: 'IN PROGRESS',
      key: 'in_progress',
      width: 130,
      align: 'center' as AlignType,
      onCell: (record: any, rowIndex: any) => {
        return {
          onClick: () => handleOnCellClick(record, Gql.ProjectDocumentStatus.InProgress)
        };
      },
      render: (data: any) => {
        return <span className={` cursor-pointer ${!data?.isChildren && 'font-bold'}`}>{data?.inProgressCount}</span>;
      }
    },
    {
      title: 'APPROVED',
      key: 'approved',
      width: 100,
      align: 'center' as AlignType,
      onCell: (record: any, rowIndex: any) => {
        return {
          onClick: () => handleOnCellClick(record, Gql.ProjectDocumentStatus.Approved)
        };
      },
      render: (data: any) => {
        return <span className={` cursor-pointer ${!data?.isChildren && 'font-bold'}`}>{data?.approvedCount}</span>;
      }
    },
    {
      title: 'REJECTED',
      key: 'rejected',
      width: 100,
      align: 'center' as AlignType,
      onCell: (record: any, rowIndex: any) => {
        return {
          onClick: () => handleOnCellClick(record, Gql.ProjectDocumentStatus.Rejected)
        };
      },
      render: (data: any) => {
        return <span className={` cursor-pointer ${!data?.isChildren && 'font-bold'}`}>{data?.rejectedCount}</span>;
      }
    },
    {
      title: '',
      key: 'action',
      width: 80,
      align: 'center' as AlignType,
      render: (data: any) => {
        const items = getWorkspaceThreeDotsItems(data, viewOnly, canDelete, canShare, canCreate as boolean);
        if (items.length === 0) return;

        if (protectedGroups?.includes(data?.name)) return;

        return Object.prototype.hasOwnProperty.call(data, 'key') ? (
          <div id="three-dots" className={`${viewOnly && 'invisible'}`}>
            <ThreeDotsDropDown
              onClick={(e: any) => {
                if (e.key === 'Rename' || e.key === 'RenameSubGroup') {
                  form.setFieldsValue({
                    name: data.name,
                    code: data.code,
                    id: data.id,
                    workspaceGroupId: data.workspaceGroupId,
                    key: e.key
                  });

                  return showCreateGroupModal();
                } else if (e.key === 'AddSubGroup') {
                  form?.setFieldsValue({
                    id: data.id,
                    key: e.key
                  });

                  return showCreateGroupModal();
                } else if (e.key === 'Share') {
                  onShare(data, false);
                } else {
                  showDeleteConfirm(data.id, data.name);
                }
              }}
              items={items}
            />
          </div>
        ) : (
          <></>
        );
      }
    }
  ];

  const filteredColumns = useMemo(() => {
    return columns.filter(column => {
      const columnConfig = tableColumns.find((col: any) => col.key === column.key);
      return columnConfig ? columnConfig.show : false;
    });
  }, [tableColumns]);

  const remindReviewDocumentLater = () => {
    setDocumentRecovery(false);
    window.localStorage.setItem('documentRecoveryReview', todayDate);
  };

  const {
    shareModal,
    showShareModal,
    setShareModalTitle,
    setWorkspaceGroupId,
    setShareFileOwner,
    listOfUsersWithAccess
  } = useShareModal({ viewOnly: viewSharedModal, refetch });

  const onShare = (group: any, viewShareModal: boolean) => {
    setShareFileOwner(group.creator);
    setViewSharedModal(viewShareModal);
    setShareModalTitle((viewShareModal ? 'Member with access for ' : 'Manage access for ') + group.name);
    setWorkspaceGroupId(group.id);
    showShareModal();
    listOfUsersWithAccess({
      variables: {
        workspaceGroupId: parseInt(group.id)
      }
    });
  };

  const privateShare = (record: any) => {
    if (record?.workspaceGroupUsersCount > 0) {
      return (
        <div
          onClick={e => {
            e.stopPropagation();
            onShare(record, true);
          }}
          className=" cursor-pointer ml-2 border-[#9B111E] font-light rounded bg-[#9B111E] text-white text-sm pl-2 pr-0 flex py-[2px] m-auto"
        >
          PRIVATE
          <div className='ml-2 bg-white rounded text-[#9B111E] px-1 mr-1 h-4 flex'>
            <Icon name='lock' className='relative top-[2px]' />
            <span className="text-sm">{record?.workspaceGroupUsersCount}</span>
          </div>
        </div>
      );
    }
    return null;
  }

  const groupIcon = (record: any, defaultIcon: ReactNode) => {
    if (record?.workspaceGroupName?.toLowerCase()?.includes('request for information')) {
      return <Icon name="callout" className='relative top-[11px] mr-2' />
    } else if (record?.workspaceGroupName?.toLowerCase()?.includes('non conformance report')) {
      return <Icon name="ncr" className='relative top-[11px] mr-2' />
    } else {
      return defaultIcon
    }
  }

  return (
    <div className="absolute overflow h-full w-full">
      {modal}
      {shareModal}
      <div className="flex flex-nowrap items-center justify-between px-[11px] pt-[20px] pb-[12px]">
        <Breadcrumb>
          <Breadcrumb.Item className="text-black text-md">
            <h3>Overview</h3>
          </Breadcrumb.Item>
        </Breadcrumb>
        <Row className='gap-2 '>
          {!viewOnly && overviewType === 'List' && (
            <Popover
              content={<ShowHideColumn tableColumns={tableColumns} onColumnToggle={name => toggleColumn(name)} />}
              trigger="hover"
            >
              <Button
                icon={<Icon name="columns" className='mr-1.5' />}
                className="flex items-center justify-center  h-[40px] mx-1"
              >
                Column
              </Button>
            </Popover>
          )}

          <Form
            form={searchForm}
            onFinish={onSearch}
            onValuesChange={debounce(data => {
              onSearch(data);
            }, 300)}
          >
            <Space>
              <SearchInput onPressEnter={() => { }} placeholder={'Search'} />
            </Space>
          </Form>

          {canCreate && (
            <Button
              type="primary"
              id="create-group"
              className="rounded-lg w-[125px] h-[45px] mx-2"
              onClick={() => {
                form?.setFieldValue('key', 'AddGroup');
                showCreateGroupModal();
              }}
            >
              Create Group
            </Button>
          )}
        </Row>
      </div>

      <div className="pb-9">
        <Spin spinning={loading}>
          {dataSources.length > 0 &&
            !loading &&
            (overviewType === 'Card' ? (
              <CardOverview
                dataSources={dataSources}
                hasMore={(data?.getWorkspaceGroups?.totalCount || 0) > dataSources?.length}
                fetchMore={onFetchMoreWorkspaceGroups}
                viewOnly={viewOnly}
                canDelete={canDelete}
                canShare={canShare}
                canCreate={canCreate as boolean}
                form={form}
                projectUserRole={projectUserRole}
                showCreateGroupModal={showCreateGroupModal}
                showDeleteConfirm={showDeleteConfirm}
                onShare={onShare}
              />
            ) : (
              <Table
                className="pt-2 px-5 dashboard-table"
                size="small"
                dataSource={dataSources.length > 0 ? dataSources : []}
                columns={filteredColumns}
                key={dataSources.length}
                expandable={{
                  expandIcon: ({ expanded, onExpand, record }) =>
                    //? check if the record is the first child or not
                    record?.fixtures ? null : !record?.workspaceGroupId ? (
                      expanded ? (
                        <div onClick={e => onExpand(record, e)} className="flex w-full pl-4 cursor-pointer">
                          {groupIcon(record, <DownOutlined className="flex self-center mt-1 mr-2" />)}
                          <div
                            onClick={() => {
                              if (isProtectedGroup(record)) {
                                router.push({
                                  pathname: '/digital-form/all-form',
                                  query: {
                                    groupId: record?.id,
                                    filter: true
                                  }
                                });
                              }
                            }}
                            className={` flex ${!record?.isChildren && 'font-medium'}`}
                            style={{
                              color: 'black',

                              margin: '0.5em',
                              marginLeft: record?.isChildren ? '1em' : '0'
                            }}
                          >
                            <Tooltip placement='top' title={record?.workspaceGroupName}>
                              <span className="flex">
                                <div className='max-w-[300px] truncate'>
                                  {record?.workspaceGroupName}
                                </div>
                              </span>
                            </Tooltip>
                          </div>
                          {privateShare(record)}
                        </div>
                      ) : (
                        <div onClick={e => onExpand(record, e)} className="flex w-full pl-4  cursor-pointer">
                          {groupIcon(record, <RightOutlined className="flex self-center mt-1 mr-2 " />)}
                          <div
                            onClick={() => {
                              if (isProtectedGroup(record)) {
                                router.push({
                                  pathname: '/digital-form/all-form',
                                  query: {
                                    groupId: record?.id,
                                    filter: true
                                  }
                                });
                              }
                            }}
                            className={` flex ${!record?.isChildren && 'font-medium'}`}
                            style={{
                              color: 'black',
                              margin: '0.5em',
                              marginLeft: record?.isChildren ? '1em' : '0'
                            }}
                          >
                            <Tooltip placement='top' title={record?.workspaceGroupName}>
                              <span className="flex">
                                <div className='max-w-[300px]  truncate'>
                                  {record?.workspaceGroupName}
                                </div>
                              </span>
                            </Tooltip>
                          </div>
                          {privateShare(record)}
                        </div>
                      )
                    ) : // ? check if the record does not have children or not
                      record?.children === undefined ? null : expanded ? (
                        <>
                          <DownOutlined onClick={e => onExpand(record, e)} className="flex self-center mt-1 mr-2" />
                        </>
                      ) : (
                        <>
                          <RightOutlined onClick={e => onExpand(record, e)} className="flex self-center mt-1 mr-2" />
                        </>
                      ),
                  defaultExpandedRowKeys: defaultExpandedRowKeys
                  // use antd table function to expand/collapse the row
                  // expandedRowKeys: defaultExpandedRowKeys
                }}
                pagination={{
                  showSizeChanger: true,
                  defaultPageSize: 8,
                  pageSize: queries.paging.limit,
                  current: queries.paging.offset / queries.paging.limit + 1,
                  total: data?.getWorkspaceGroups?.totalCount ?? 0,
                  onChange: (_page, pageSize) => {
                    if (preferredPageSize != pageSize.toString()) {
                      localStorage.setItem('preferredPageSize', pageSize.toString())
                    }
                  }
                }}
                onChange={paginate => {
                  const { current, pageSize } = paginate;
                  if (pageSize !== undefined && current !== undefined) {
                    setPaging({
                      offset: (current - 1) * pageSize,
                      limit: pageSize
                    });
                  }
                }}
                rowKey={(record: any) => record.key}
                loading={loading}
              />
            ))}
        </Spin>
      </div>
      {documentRecovery && (
        <div className="fixed bottom-8 right-10">
          <div
            className="border border-gray-200 rounded-lg p-4 bg-white shadow-md w-96"
            style={{ backgroundColor: '#E3F2FD' }}
          >
            <div className="flex">
              <Icon name="auto-recovery-document" className="text-blue-500" />
              <div className="font-semibold ml-2">Document Recovery Available!</div>
            </div>

            <div className="mt-3">
              BINA has recovered some files while you were away. Review & save the ones you wish to keep.
            </div>
            <div className="mt-3">
              <Link href="/digital-form/document-recovery">
                <Button
                  type="primary"
                  className="rounded-lg"
                  style={{ backgroundColor: '#4CAF50', border: '1px solid #4CAF50' }}
                >
                  Review Now
                </Button>
              </Link>
              <Button
                type="primary"
                className="rounded-lg ml-2"
                style={{ backgroundColor: '#FFC107', border: '1px solid #FFC107' }}
                onClick={remindReviewDocumentLater}
              >
                Remind Me Later
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
Overview.auth = true;
Overview.Layout = DigitalFormTabLayout;
export default Overview;
