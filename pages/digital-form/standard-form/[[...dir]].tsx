import { ExclamationCircleOutlined, HomeOutlined } from '@ant-design/icons';
import { Icon } from '@commons';
import SyncModal from '@components/cloud-docs/SyncModal';
import DigitalFormTabLayout from '@components/digital-form/DigitalFormTabLayout';
import StandardFormDocumentDetailsDrawer from '@components/digital-form/standard-form/StandardFormDocumentDetailsDrawer';
import SearchInput from '@components/forms/FormsInput/SearchInputs/StandardFormSearch';
import useModal from '@components/hooks/useModal';
import useQueries from '@components/hooks/useQueries';
import ManageModal from '@components/ManageModal';
import MoveBulkCloudDocsModal from '@components/MoveBulkCloudDocsModal';
import ThreeDotsDropDown from '@components/ThreeDotsDropDown';
import WorkspaceUpload from '@components/WorkspaceUpload';
import * as Gql from '@graphql';
import apolloClient from '@lib/apollo';
import { nameA<PERSON><PERSON> } from '@utils/app.utils';
import { manageDocuments } from '@utils/authority';
import { onError } from '@utils/error';
import { getFileName } from '@utils/filename';
import {
  Avatar,
  Breadcrumb,
  Button,
  Col,
  Dropdown,
  Form,
  Input,
  MenuProps,
  message,
  Modal,
  Row,
  Space,
  Spin,
  Table,
  Tooltip
} from 'antd';
import UserAvatar from 'src/components/UserAvatar';
import { useForm } from 'antd/lib/form/Form';
import Paragraph from 'antd/lib/typography/Paragraph';
import axios from 'axios';
import FileSaver from 'file-saver';
import _, { isArray } from 'lodash';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useEffect, useRef, useState } from 'react';
import { ProjectDocumentApiService } from 'src/api';
import { getFileIcon } from 'src/commons/FileIcon';
import useDropzone from 'src/hooks/useDropzone';
import useHasFocus from 'src/hooks/useHasFocus';

const StandardForm = () => {
  const hasFocus = useHasFocus();
  const router = useRouter();
  const [form] = useForm();
  const standardFormDocumentDetailsDrawerRef = useRef<any>(null);
  const { confirm } = Modal;
  const [selectedFormId, setSelectedFormId] = useState<string>();
  const [dataSource, setDataSource] = useState<any>([]);
  const [projectUserRole, setProjectUserRole] = useState<string>('');
  const moveDocumentsModal = useRef<any>(null);
  const uploadWorkspaceDocModalRef = useRef<any>(null);
  const [breadcrumbs, setBreadcrumbs] = useState<
    Gql.GetProjectDocumentsBreadcrumbQuery['getProjectDocumentsBreadcrumb']
  >([]);
  const dirId = router.query.dir && router.query.dir[0];
  const [getBreadcrumbs] = Gql.useGetProjectDocumentsBreadcrumbLazyQuery();
  const [loading, setLoading] = useState(false);
  const tableRef = useRef<any>(null);
  const [formName, setFormName] = useState<string>('');
  const [hoveredRowIndex, setHoveredRowIndex] = useState<any>(null);
  const moveFileModal = useRef<any>(null);
  const inputRef = useRef<any>(null);

  const [searchForm] = useForm();

  // For multiple selection
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const { canCreate, canDelete, canMove } = manageDocuments(projectUserRole, dirId);

  const dropzoneRef = useRef<any>(null);
  useDropzone({ dropzoneRef, pushModal: uploadWorkspaceDocModalRef?.current?.openModal });

  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  useEffect(() => {
    refetch();

    if (!dirId) {
      setBreadcrumbs([]);
      return;
    }

    getBreadcrumbs({
      variables: { input: { id: parseInt(dirId as string) } }
    }).then(res => {
      setBreadcrumbs(res.data?.getProjectDocumentsBreadcrumb ?? []);
    });
  }, [dirId]);

  useEffect(() => {
    setProjectUserRole(localStorage.getItem('ProjectUserRole') || '');
  }, []);

  const viewOnly = projectUserRole === 'CanView';

  // open document details drawer
  useEffect(() => {
    if (router.query.documentDetailsId) {
      standardFormDocumentDetailsDrawerRef?.current?.pushDrawer();
    }
  }, [router.query.documentDetailsId]);

  const handleTableScroll = (e: { target: any }) => {
    const { target } = e;
    if (target.scrollHeight - target.scrollTop <= target.clientHeight + 1) {
      onLoadMore();
    }
  };

  const { queries, setFilter } = useQueries<Gql.ProjectDocumentFilter, Gql.ProjectDocumentSort>({
    usePagingParam: false,
    paging: { offset: 0, limit: 20 }
  });

  const {
    data,
    refetch,
    loading: loadingProjectDocument
  } = Gql.useGetProjectDocumentsQuery({
    variables: {
      ...queries,
      filter: {
        ...queries.filter,
        ...{
          projectDocumentId: {
            ...(dirId ? { eq: dirId?.toString() } : { eq: null })
          },
          category: { eq: Gql.CategoryType.StandardForm }
        }
      },
      sorting: []
    },
    errorPolicy: 'all',
    onError: onError,
    onCompleted: data => {
      setDataSource(data?.getProjectDocuments?.nodes ?? []);
      if (!dirId) {
        getAutosavedCount();
      }
    }
  });

  useEffect(() => {
    if (hasFocus) {
      getAutosavedCount();
    }
  }, [hasFocus]);

  const [getAutosavedCount] = Gql.useGetAutosavedProjectDocumentCountLazyQuery({
    fetchPolicy: 'network-only',
    variables: {
      filter: {
        category: {
          eq: Gql.CategoryType.StandardForm
        }
      }
    },
    onCompleted: data => {
      const totalCount = data?.getAutosavedProjectDocumentCount?.totalCount ?? 0;
      if (totalCount === 0) {
        return;
      }

      const allData = [...dataSource];
      if (allData[0].id === 'autosaved') {
        allData[0].count = totalCount;
      } else {
        allData.unshift({
          id: 'autosaved',
          count: totalCount
        });
      }
      setDataSource([...allData]);
    }
  });

  // refetch the data when dirId is null
  useEffect(() => {
    if (!dirId) {
      refetch();
    }
  }, [dirId]);

  // update dataSource state onLoadMore function is fire
  useEffect(() => {
    // setDataSource(data?.getProjectDocuments?.nodes ?? []);

    if (tableRef.current) {
      const tableBody = tableRef.current.querySelector('.ant-table-body');
      tableBody.addEventListener('scroll', handleTableScroll);
    }
    return () => {
      if (tableRef.current) {
        const tableBody = tableRef.current.querySelector('.ant-table-body');
        tableBody.removeEventListener('scroll', handleTableScroll);
      }
    };
  }, [handleTableScroll]);

  const onLoadMore = async () => {
    if (!data?.getProjectDocuments.pageInfo.hasNextPage) return;
    setLoading(true);
    await refetch({
      paging: {
        offset: 0,
        limit: data.getProjectDocuments.nodes.length + 20
      }
    }).then(res => {
      setDataSource([...(res.data?.getProjectDocuments?.nodes ?? [])]);
      setLoading(false);
    });
  };

  //Routing new tab
  const openInNewTab = (url: any) => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  const [createFolder, { loading: createFolderLoading }] = Gql.useCreateOneProjectDocumentMutation({
    onCompleted: () => {
      form.resetFields();
      message.success('Folder created successfully');
      refetch();
      closeAddFolderModal();
    },
    onError: onError as any
  });

  const [deleteFolder] = Gql.useDeleteProjectDocumentMutation({
    onCompleted: () => {
      message.success('Deleted successfully');
      refetch();
    },
    onError: onError as any
  });

  const onFinish = (values: Gql.CreateProjectDocumentInputDto) => {
    const { name } = values;
    const category = Gql.CategoryType.StandardForm;
    const fileSystemType = Gql.FileSystemType.Folder;
    const type = 'folder';
    const updatedName = getFileName(type, name ?? '', '');
    const projectDocumentId = dirId as string;
    createFolder({
      variables: {
        input: {
          projectDocument: {
            name: updatedName ?? '',
            category,
            fileSystemType,
            type,
            projectDocumentId
          }
        }
      }
    });
  };

  const onDelete = (id: string) => {
    deleteFolder({ variables: { id: parseInt(id) } });
  };

  const [deleteDocuments, { loading: documentsDeleting }] = Gql.useDeleteProjectDocumentsMutation({
    onCompleted: () => {
      message.success('Deleted successfully');
      setSelectedRowKeys([]);
      refetch();
    },
    onError: onError as any
  });

  const showDeletesConfirm = () => {
    const deletedDocumentNames = dataSource
      .filter((doc: any) => selectedRowKeys.includes(doc.id.toString()))
      .map((doc: any) => doc.name)
      .slice(0, 10);

    confirm({
      title: 'Are you sure?',
      icon: <ExclamationCircleOutlined />,
      content: (
        <>
          <div>
            Do you really want to delete the{' '}
            {selectedRowKeys.length < 2 && (
              <span className="font-semibold">
                {deletedDocumentNames[0]} <span className="font-normal">?</span>
              </span>
            )}
          </div>
          {selectedRowKeys.length > 1 && (
            <ol className="list-decimal mt-2">
              {deletedDocumentNames.map((name: string, index: number) => (
                <li className="my-2 font-semibold" key={index}>
                  {name}
                </li>
              ))}
            </ol>
          )}
        </>
      ),
      okText: 'Yes',
      okType: 'danger',
      cancelText: 'No',
      onOk() {
        deleteDocuments({
          variables: {
            input: {
              ids: selectedRowKeys.map(id => parseInt(id as string))
            }
          }
        });
        setSelectedRowKeys([]);
      },
      onCancel() {}
    });
  };

  const onBulkDownload = async () => {
    setLoading(true);
    try {
      const nodeBuffer = await ProjectDocumentApiService.downloadBulkZip(
        {
          body: {
            ids: selectedRowKeys as string[]
          }
        },
        {
          responseType: 'arraybuffer'
        }
      ).catch(e => {
        message.error(e?.message ?? 'Something went wrong');
      });

      const file = new Blob([nodeBuffer], { type: 'application/zip' });
      const fileName = 'bulkdownload.zip';
      setSelectedRowKeys([]);
      setLoading(false);
      FileSaver(file, fileName);
    } catch (e) {
      onError(e);
    } finally {
      setLoading(false);
    }
  };

  const [updateName] = Gql.useUpdateOneProjectDocumentMutation({
    onCompleted: () => {
      message.success('Saved successfully');
      closeEditFolderNameModal();
      refetch();
    },
    onError: onError as any
  });

  const onUpdate = (values: Gql.UpdateProjectDocumentInputDto) => {
    const selectedDocument = dataSource?.find((item: any) => item.id === selectedFormId) ?? null;
    const updatedName = getFileName(selectedDocument.type, values.name ?? '', formName);
    if (!values.name) {
      return message.error('Folder name is required');
    }

    updateName({
      variables: {
        input: {
          id: selectedFormId?.toString() ?? '',
          update: {
            name: updatedName ?? ''
          }
        }
      }
    });
  };

  const [modal, showAddFolderModal, closeAddFolderModal] = useModal({
    onCancel: () => form.resetFields(),
    title: 'Create folder',
    content:
      (setTimeout(() => {
        inputRef?.current?.focus();
      }, 100),
      (
        <Form layout="vertical" form={form} onFinish={onFinish} requiredMark={false}>
          <Form.Item label="Folder name" name="name" rules={[{ required: true, message: 'Folder name is required!' }]}>
            <Input ref={inputRef} />
          </Form.Item>
          <div className="flex justify-end">
            <Button htmlType="submit" type="primary" disabled={viewOnly}>
              Create
            </Button>
          </div>
        </Form>
      ))
  });

  // Edit Form Category Modal
  const [editFolderNameModal, showEditFolderNameModal, closeEditFolderNameModal] = useModal({
    title: 'Edit name',
    content:
      (setTimeout(() => {
        inputRef?.current?.focus();
      }, 100),
      (
        <Form layout="vertical" form={form} requiredMark={false} onFinish={onUpdate} initialValues={{}}>
          <Form.Item
            rules={[
              {
                max: 249,
                message: 'Maximum 250 character'
              }
            ]}
            label="Folder/File name"
            name="name"
          >
            <Input maxLength={250} defaultValue={formName} ref={inputRef} />
          </Form.Item>
          <div className="flex justify-end">
            <Button htmlType="submit" type="primary">
              Save
            </Button>
          </div>
        </Form>
      ))
  });

  useEffect(() => {
    form.setFieldsValue({ name: formName });
  }, [formName]);

  const showDeleteConfirm = (id: string) => {
    confirm({
      title: 'Are you sure?',
      icon: <ExclamationCircleOutlined />,
      content: 'Do you really want to delete this document?',
      okText: 'Yes',
      okType: 'danger',
      cancelText: 'No',
      onOk() {
        onDelete(id);
      },
      onCancel() {}
    });
  };

  // Download File
  const onDownload = async (id: string) => {
    try {
      const doc = await apolloClient.query<Gql.ProjectDocumentQuery, Gql.ProjectDocumentQueryVariables>({
        query: Gql.ProjectDocumentDocument,
        variables: { id }
      });
      const file = doc.data.projectDocument;
      const { data: fileBlob } = await axios.get(file?.fileUrl ?? '', {
        responseType: 'blob'
      });
      const fileName = file?.name ?? '';
      FileSaver(fileBlob, fileName);
    } catch (e) {
      onError(e);
    }
  };

  // Download Folder
  const onDownloadFolder = async (id: number, name: string) => {
    try {
      const nodeBuffer = await ProjectDocumentApiService.downloadZip(
        {
          body: {
            id
          }
        },
        {
          responseType: 'arraybuffer'
        }
      );

      const file = new Blob([nodeBuffer], { type: 'application/zip' });
      const fileName = name + '.zip';
      FileSaver(file, fileName);
    } catch (e) {
      onError(e);
    }
  };

  const columns = [
    {
      title: 'NAME',
      key: 'name',
      render: (data: any, _record: any, index: number) => {
        if (data.id == 'autosaved') {
          return (
            <div className="flex">
              <p className="m-auto flex">
                <Icon name="auto-recovery-document" className="mr-1" />
                We&apos;ve recovered {data.count} unsaved documents
                <Link href="/digital-form/document-recovery">
                  <div className="ml-10 text-blue-600 cursor-pointer">Review Now</div>
                </Link>
              </p>
            </div>
          );
        }

        return (
          <Space className="p-1" align="start">
            {getFileIcon(data.type)}
            <div
              className="cursor-pointer"
              onClick={() => {
                if (data?.fileSystemType === Gql.FileSystemType.Folder) {
                  setFilter({ name: { like: '' } });
                  searchForm.resetFields();
                }
                if (data?.fileSystemType === Gql.FileSystemType.Document) {
                  router.replace({
                    query: {
                      ...router.query,
                      documentId: data.id
                    }
                  });
                  if (router.query && data.fileSystemType === 'Document') {
                    openInNewTab(
                      '/viewer/standard-form-viewer?documentId=' + data.id + '&dirId=' + data.projectDocumentId
                    );
                  }
                }
                if (data?.fileSystemType === 'Folder') {
                  setFilter({ name: { like: '' } });
                  router.push(`/digital-form/standard-form/${data.id}`);
                }
              }}
            >
              <Tooltip
                title={data?.name}
                mouseEnterDelay={0.5}
                placement="topLeft"
                overlayStyle={{ maxWidth: '450px' }}
              >
                <Paragraph
                  className="font-medium"
                  style={{ margin: 0, width: 450 }}
                  ellipsis={{ rows: 2, expandable: false }}
                >
                  {data?.name}
                </Paragraph>
              </Tooltip>
            </div>
          </Space>
        );
      }
    },
    {
      title: 'FILE SIZE',
      key: 'fileSize',
      width: 120,
      render: (data: any, _record: any, index: number) => {
        if (data.id === 'autosaved') {
          return;
        }

        return data?.fileSystemType === Gql.FileSystemType.Document ? (
          <p> {data?.fileSize ? `${data?.fileSize} MB` : '-'}</p>
        ) : (
          '-'
        );
      }
    },
    {
      title: 'ADDED BY',
      dataIndex: 'owner',
      key: 'owner',
      width: 120,
      render: (owner: any, record: any, index: any) => {
        if (record.id === 'autosaved') {
          return;
        }
        return (
          <Row>
            <UserAvatar
              key={index}
              username={owner?.name}
              src={owner?.avatar}
              style={{ backgroundColor: !owner?.avatar ? owner?.color || "" : "transparent", // Apply only if no avatar
                color: "#ffffff"
              }}
              tooltip={owner?.name}
              align={{
                offset: [-12, -2], 
                }}
            />
          </Row>
        );
      }
    },
    {
      title: '',
      key: 'action',
      width: 40,
      render: (data: any, _record: any, index: number) => {
        if (data.id === 'autosaved') {
          return;
        }

        const items: any = [];

        if (data?.fileSystemType === 'Folder') {
          items.push({
            label: <p>Download Folder</p>,
            key: 'DownloadFolder'
          });
          items.push({ type: 'divider' });
        }
        if (data?.fileSystemType === 'Document') {
          items.push({
            label: <p>Download</p>,
            key: 'DownloadDocument'
          });
          items.push({ type: 'divider' });
        }

        if (viewOnly) {
          items.push({ label: <p>Manage</p>, key: 'Manage' });
        } else {
          items.push({ label: <p>Rename</p>, key: 'Rename' });
        }

        return (
          <ThreeDotsDropDown
            onClick={(e: any) => {
              if (e.key === 'DownloadDocument') {
                onDownload(data.id);
              } else if (e.key === 'DownloadFolder') {
                onDownloadFolder(data.id, data.name);
              } else if (e.key === 'Rename') {
                setSelectedFormId(data.id);
                setFormName(data.name);
                showEditFolderNameModal();
              } else if (e.key === 'DocumentDetails') {
                router.replace({
                  query: {
                    ...router.query,
                    documentDetailsId: data.id
                  }
                });
              } else if (e.key === 'Delete') {
                showDeleteConfirm(data.id);
              } else if (e.key === 'MoveFile') {
                setFormName(data.name);
                setSelectedFormId('' + data.id);
                moveFileModal?.current?.openModal();
              }
            }}
            items={items}
          />
        );
      }
    }
  ];

  const handleNewMenuClick: MenuProps['onClick'] = e => {
    switch (e.key) {
      case '1': {
        showAddFolderModal();
        break;
      }
      case '2': {
        uploadWorkspaceDocModalRef?.current?.openModal();
        break;
      }
      case '3': {
        showSyncModal();
        break;
      }
    }
  };

  // sync modal
  const [syncmodal, showSyncModal, closeSyncModal] = useModal({
    closable: false,

    content: (
      <>
        <SyncModal
          mimeType="pdf"
          dirId={dirId as string}
          category={Gql.CategoryType.StandardForm}
          onComplete={() => {
            refetch();
            closeSyncModal();
          }}
        />
      </>
    )
  });

  const handleRowClick = (record: any) => {
    if (router.query && record.fileSystemType === 'Document' && record.workflow !== null) {
      router.replace({
        query: {
          ...router.query,
          documentDetailsId: record.id,
          workflowType: record?.workflow
        }
      });
    }
  };

  const onRowHover = (id: any) => {
    setHoveredRowIndex(id);
  };

  const onRowLeave = () => {
    setHoveredRowIndex(null);
  };

  const newMenuProps = {
    items: [
      {
        icon: <Icon name="new-folder" />,
        label: <> Add Folder</>,
        key: '1'
      },
      dirId
        ? {
            icon: <Icon name="file-upload" />,
            label: <>Upload Files</>,
            key: '2'
          }
        : null,
      dirId
        ? {
            icon: <Icon name="import" />,
            label: <>Import</>,
            key: '3'
          }
        : null
    ],
    onClick: handleNewMenuClick
  };

  const onMove = () => {
    // if query.filter has a value, then clear it
    if (queries?.filter?.name) {
      setFilter({ name: { like: '' } });
    }
    moveDocumentsModal?.current?.openModal();
  };

  return (
    <div className="absolute h-full w-full" ref={dropzoneRef}>
      {modal}
      {editFolderNameModal}
      {syncmodal}

      <Spin tip={'Loading...'} spinning={createFolderLoading || loading || documentsDeleting}>
        <div className="flex flex-nowrap items-center justify-between px-5 pt-[20px] pb-[12px]">
          <div>
            <h3>Templates</h3>
            {dirId && (
              <Breadcrumb>
                <Breadcrumb.Item className="text-gray90 text-md">
                  <Link href="/digital-form/standard-form">
                    <HomeOutlined />
                  </Link>
                </Breadcrumb.Item>
                {breadcrumbs.map(value => (
                  <Breadcrumb.Item className="text-gray90 text-md" key={value.id}>
                    <Link href={`/digital-form/standard-form/${value.id}`}>{value.name}</Link>
                  </Breadcrumb.Item>
                ))}
              </Breadcrumb>
            )}
          </div>
          <Row>
            <Col>
              <Form
                form={searchForm}
                onValuesChange={_.debounce(data => {
                  setFilter({ name: { like: data.keyword } });
                }, 300)}
              >
                <Space>
                  <SearchInput placeholder="Search by name" onPressEnter={() => {}} />
                </Space>
              </Form>
            </Col>
            <Col>
              {canCreate && (
                <Dropdown menu={newMenuProps} disabled={viewOnly}>
                  <Button
                    type="primary"
                    disabled={viewOnly}
                    icon={<Icon name="plus-white" className="pt-1 mr-1" />}
                    className="ml-2 h-[40px] rounded-lg border-gray40"
                  >
                    <Space>New</Space>
                  </Button>
                </Dropdown>
              )}
            </Col>
          </Row>
        </div>

        <Table
          rowSelection={{
            type: 'checkbox',
            selectedRowKeys,
            onChange: onSelectChange,
            hideSelectAll: selectedRowKeys.length === 0,
            getCheckboxProps: e => {
              if (e.id === 'autosaved') return { style: { display: 'none' } };
              return {
                style: {
                  display: selectedRowKeys.includes(e.id) || e.id === hoveredRowIndex ? 'flex' : 'none',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginBottom: 10
                }
              };
            }
          }}
          className="dashboard-table mb-[30px] px-5"
          dataSource={
            isArray(dataSource)
              ? dataSource?.map((item: any) => ({
                  ...item,
                  key: item.id
                }))
              : dataSource
          }
          columns={columns}
          size="small"
          // components={components}
          pagination={false}
          tableLayout="auto"
          onRow={(data, index) => {
            const attr = {
              index,
              style: data.id === 'autosaved' ? { backgroundColor: 'aliceblue' } : undefined,
              onSelect: () => handleRowClick(data),
              onMouseEnter: () => onRowHover(data.id), // when mouse enters the row
              onMouseLeave: () => onRowLeave() // when mouse leaves the row
            };
            return attr as React.HTMLAttributes<any>;
          }}
          ref={tableRef}
          scroll={{
            scrollToFirstRowOnChange: false,
            y: window.innerHeight - 230
          }}
          loading={loading || loadingProjectDocument}
        />
        {/* </DndProvider> */}

        <StandardFormDocumentDetailsDrawer
          ref={standardFormDocumentDetailsDrawerRef}
          onSaved={() => {
            refetch();
          }}
        />

        <WorkspaceUpload
          ref={uploadWorkspaceDocModalRef}
          dirId={dirId as any}
          onSaved={refetch}
          type={Gql.CategoryType.StandardForm}
        />
        <MoveBulkCloudDocsModal
          data={dataSource}
          ids={selectedRowKeys}
          dirId={dirId as any}
          ref={moveDocumentsModal}
          onSaved={() => {
            refetch();
            setSelectedRowKeys([]);
          }}
          category={Gql.CategoryType.StandardForm}
          setSelectedRowKeys={setSelectedRowKeys}
          onLoadMore={onLoadMore}
        />
      </Spin>

      <ManageModal
        canMove={canMove}
        onMove={onMove}
        selectedRowKeys={selectedRowKeys}
        onBulkDownload={onBulkDownload}
        showDeletesConfirm={showDeletesConfirm}
        canDelete={canDelete ?? false}
        data={dataSource}
        setSelectedRowKeys={setSelectedRowKeys}
      />
    </div>
  );
};

StandardForm.auth = true;
StandardForm.Layout = DigitalFormTabLayout;
export default StandardForm;
