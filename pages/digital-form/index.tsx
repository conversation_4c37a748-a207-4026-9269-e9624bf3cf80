import { ProjectAccess } from '@constants/subscription';
import * as Gql from '@graphql';
import { isAllowed } from '@lib/helper';
import { onError } from '@utils/error';
import { useRouter } from 'next/router';

const DigitalForm = () => {
  const route = useRouter();

  /** START ACL */
  const companySubscriptions = Gql.useCompanySubscriptionsQuery({
    variables: {
      sorting: {
        direction: Gql.SortDirection.Desc,
        field: Gql.CompanySubscriptionSortFields.SubscriptionEndDate
      },
      paging: {
        limit: 1
      }
    },
    onError: onError,
    nextFetchPolicy: 'cache-first'
  });
  /** END ACL */

  if (isAllowed(companySubscriptions, [ProjectAccess.WORKSPACE_DOCUMENT, ProjectAccess.WORKSPACE_TEMPLATE], true))
    route.push('/digital-form/overview');
  else if (isAllowed(companySubscriptions, ProjectAccess.WORKSPACE_DOCUMENT)) route.push('/digital-form/all-form');
  else if (isAllowed(companySubscriptions, ProjectAccess.WORKSPACE_TEMPLATE)) route.push('/digital-form/standard-form');

  return null;
};

DigitalForm.auth = true;
export default DigitalForm;
