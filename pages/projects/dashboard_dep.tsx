import { Icon } from '@commons';
import PieChart from '@components/charts/pie-chart';
import useQueries from '@components/hooks/useQueries';
import DashboardTable from '@components/project/DashboardTable';
import DashboardTaskTable, { DashboardTableRef } from '@components/project/DashboardTaskTable';
import EventCalendarList from '@components/project/EventCalendarList';
import ProjectsTabLayout from '@components/project/ProjectsTabLayout';
import * as Gql from '@graphql';
import { onError } from '@utils/error';
import { Card, Col, Row, Space, Switch } from 'antd';
import _ from 'lodash';
import { useEffect, useRef, useState } from 'react';
import { DashboardApiService } from 'src/api';

const Dashboard = () => {
  const [isCompleteTask, setIsCompleteTask] = useState(false);
  const [pieChartData, setPieChartData] = useState<any>({});
  const { queries: docQueries } = useQueries<Gql.ProjectDocumentFilter, Gql.ProjectDocumentSort>();

  const DashboardRef = useRef<DashboardTableRef>(null);

  const { data: docData } = Gql.useProjectDocumentsQuery({
    variables: {
      ...docQueries,
      filter: {
        category: { eq: Gql.CategoryType.AllForm },
        fileSystemType: { eq: Gql.FileSystemType.Document }
      },
      sorting: {
        direction: Gql.SortDirection.Desc,
        field: Gql.ProjectDocumentSortFields.UpdatedAt
      }
    },
    onError: onError
  });

  const { data: corrInData } = Gql.useGetCorrespondenseInQuery();
  const { data: corrOutData } = Gql.useGetCorrespondenseOutQuery();

  const docDataSource = docData?.projectDocuments.nodes;

  const approvedDocs = _.filter(docDataSource, data => data.status === Gql.ProjectDocumentStatus.Approved);

  const pendingDocs = _.filter(docDataSource, data => data.status === Gql.ProjectDocumentStatus.Pending);

  const corrInDataSource = corrInData?.getCorrespondenceIn ?? [];
  const corrOutDataSource = corrOutData?.getCorrespondenceOut ?? [];

  useEffect(() => {
    DashboardRef.current?.filterTask(isCompleteTask);
  }, [isCompleteTask]);

  useEffect(() => {
    getPieData();
  }, []);

  const getPieData = async () => {
    const pieChartData = await DashboardApiService.overview({});
    setPieChartData([
      {
        type: 'Completed',
        value: pieChartData.countOfCompletedTasks
      },
      {
        type: 'Not Completed',
        value: pieChartData.countOfInProgressTasks
      },
      {
        type: 'Overdue',
        value: pieChartData.countOfOverdueTasks
      }
    ]);
  };

  const corrInColumns = [
    {
      title: 'CORR IN',
      key: 'corrIn',
      width: '25px',
      render: (data: any) => {
        const { fileUrl } = data;
        // get the file type to show the icons
        let extension = fileUrl?.split('.').pop();
        return (
          <>
            <Space className="flex justify-start">
              <Icon name={extension} />
              <div className="font-bold"> {data.name} </div>
            </Space>
          </>
        );
      }
    }
  ];

  const corrOutColumns = [
    {
      title: 'CORR OUT',
      width: '25px',
      key: 'corrOut',
      render: (data: any) => {
        const { fileUrl } = data;
        // get the file type to show the icons
        let extension = fileUrl?.split('.').pop();
        return (
          <>
            <Space className="flex justify-start">
              <Icon name={extension} />
              <div className="font-bold"> {data.name} </div>
            </Space>
          </>
        );
      }
    }
  ];

  const pendingApprovalColumns = [
    {
      title: 'PENDING APPROVAL',
      width: '25px',
      key: 'pendingApproval',
      render: (data: any) => {
        const { fileUrl } = data;
        // get the file type to show the icons
        let extension = fileUrl?.split('.').pop();
        return (
          <>
            <Space className="flex justify-start">
              <Icon name={extension} />
              <div className="font-bold"> {data.name} </div>
            </Space>
          </>
        );
      }
    }
  ];

  const approvedColumns = [
    {
      title: 'APPROVED',
      width: '25px',
      key: 'approved',
      render: (data: any) => {
        const { fileUrl } = data;
        // get the file type to show the icons
        let extension = fileUrl?.split('.').pop();
        return (
          <>
            <Space className="flex justify-start">
              <Icon name={extension} />
              <div className="font-bold"> {data.name} </div>
            </Space>
          </>
        );
      }
    }
  ];

  return (
    <div className="flex flex-wrap flex-row justify-between pt-4 pl-5 pr-3">
      <div className="flex relative w-7/12">
        <div className="w-full">
          <div className="flex justify-between items-center">
            <h1 className="text-xl">Tasks</h1>
            <Space className="flex items-center">
              <p className="font-bold">Hide completed tasks </p>
              <Switch
                onChange={checked => {
                  setIsCompleteTask(checked);
                }}
              />
            </Space>
          </div>

          <div>
            <DashboardTaskTable ref={DashboardRef} />
          </div>

          <div className="mb-[80px]">
            <h1 className="text-xl mt-6">Document Status</h1>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <DashboardTable columns={pendingApprovalColumns} dataSource={_.take(pendingDocs, 10)} />
              </div>
              <div>
                <DashboardTable columns={approvedColumns} dataSource={_.take(approvedDocs, 10)} />
              </div>
            </div>

            <div>
              <h1 className="text-xl mt-6">Correspondence</h1>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <DashboardTable columns={corrInColumns} dataSource={corrInDataSource} />
                </div>
                <div>
                  <DashboardTable columns={corrOutColumns} dataSource={corrOutDataSource} />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex w-5/12 pl-3 pr-3">
        <div className="w-full">
          <Card className="w-full h-165">
            <Row>
              <Col span={12}>
                <h1 className="mb-2">Task Progress</h1>
                <Space>
                  <div className="mb-1">
                    <Icon name="ellipse-48" />
                  </div>

                  <p>Completed</p>
                  <p className="font-bold pl-3">{pieChartData[0]?.value}</p>
                </Space>

                <Space>
                  <div className="mb-1">
                    <Icon name="ellipse-49" />
                  </div>
                  <p>Not Completed</p>
                  <p className="font-bold pl-3">{pieChartData[1]?.value}</p>
                </Space>

                <Space>
                  <div className="mb-1">
                    <Icon name="ellipse-50" />
                  </div>
                  <p>Overdue</p>
                  <p className="font-bold pl-3">{pieChartData[2]?.value}</p>
                </Space>
              </Col>
              <Col span={12}>
                <div>
                  <PieChart pieChartSpan={24} data={pieChartData} taskChart={true} />
                </div>
              </Col>
            </Row>
          </Card>

          <div className="mt-4 mb-14">
            <EventCalendarList />
          </div>
        </div>
      </div>
    </div>
  );
};

Dashboard.auth = true;
Dashboard.Layout = ProjectsTabLayout;
export default Dashboard;
