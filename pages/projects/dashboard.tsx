import { Line } from '@ant-design/plots';
import { Icon } from '@commons';
import Center from '@components/Center';
import <PERSON><PERSON><PERSON> from '@components/charts/pie-chart';
import MemoList from '@components/dashboard/memo-list';
import useModal from '@components/hooks/useModal';
import useQueries from '@components/hooks/useQueries';
import PdfTronModal from '@components/pdf-tron/PdfTronModal';
import EventModal from '@components/project/dashboard/EventModal';
import GraphUploadModal from '@components/project/dashboard/GraphUploadModal';
import WeatherAddressModal from '@components/project/dashboard/WeatherAddressModal';
import { DashboardTableRef } from '@components/project/DashboardTaskTable';
import WorkspaceGroupPicker from '@components/project/GroupPicker';
import ProjectsTabLayout from '@components/project/ProjectsTabLayout';
import TaskGroupPicker from '@components/tasks/GroupPicker';
import ThreeDotsDropDown from '@components/ThreeDotsDropDown';
import { IconNames } from '@constants';
import { ProjectAccess } from '@constants/subscription';
import * as Gql from '@graphql';
import { isAllowed } from '@lib/helper';
import { onError } from '@utils/error';
import { tz } from '@utils/timezone';
import {
  Alert,
  Button,
  Card,
  Carousel,
  Col,
  Collapse,
  Divider,
  Empty,
  Image,
  List,
  Menu,
  message,
  Row,
  Select,
  Space,
  Spin,
  Table,
  Tag,
  Timeline,
  Tooltip,
  Typography
} from 'antd';
import type { ColumnsType } from 'antd/es/table';
import Paragraph from 'antd/lib/typography/Paragraph';
import axios from 'axios';
import { saveAs } from 'file-saver';
import _, { trim } from 'lodash';
import moment from 'moment';
import Link from 'next/link';
import router from 'next/router';
import { useCallback, useEffect, useRef, useState } from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';
import { Document, Page, pdfjs } from 'react-pdf';
import 'react-pdf/dist/Page/AnnotationLayer.css';
import 'react-pdf/dist/Page/TextLayer.css';
import { DashboardApiService } from 'src/api';
import { read, utils } from 'xlsx';

const { Text } = Typography;
const { Panel } = Collapse;

const Dashboard = () => {
  const [projectId, setProjectId] = useState<any>(null);

  useEffect(() => {
    const projectId = localStorage.getItem('ProjectId');
    if (isNaN(Number(projectId))) {
      localStorage.removeItem('ProjectId');
      router.push('/');
    } else {
      setProjectId(projectId);
    }
  }, []);

  const [upcomingLoading, setUpcomingLoading] = useState(false);
  const [pieChartData, setPieChartData] = useState<any>({});
  const [pieChartWorkspaceData, setPieChartWorkspaceData] = useState<any>({});
  const [lineChartData, setLineChartData] = useState<any>([]);
  const [lineChartDataFinance, setLineChartDataFinance] = useState<any>([]);
  const [rowData, setRowData] = useState<any>([]);
  const [rowDataFinance, setRowDataFinance] = useState<any>([]);
  const [upcomingEventData, setUpcomingEventData] = useState<any>([]);
  const [columnData, setColumnData] = useState<any>([]);
  const [columnDataFinance, setColumnDataFinance] = useState<any>([]);
  const [selectedTaskGroup, setSelectedTaskGroup] = useState<string>();
  const [selectedWorkspaceGroup, setSelectedWorkspaceGroup] = useState<string>();
  const [showTaskGroupInput, setShowTaskGroupInput] = useState(false);
  const [showWorkspaceGroupInput, setShowWorkspaceGroupInput] = useState(false);
  const [docId, setDocId] = useState('');
  const pdfTronTabRef = useRef<any>(null);

  const { Option } = Select;
  const [selectedActivityArray, setSelectedActivityArray] = useState([{}]);
  const eventModalRef = useRef<any>(null);
  const weatherAddressModalRef = useRef<any>(null);
  const graphUploadModalRef = useRef<any>(null);

  const [weatherData, setWeatherData] = useState<any>([]);
  const [projectUserRole, setProjectUserRole] = useState<any>('');
  const [graphyType, setGraphType] = useState<any>('Physical');
  const tableRef = useRef<any>(null);
  const [selectedWorkspaceGroupName, setSelectedWorkspaceGroupName] = useState<string | null>('');
  const [selectedTaskGroupName, setSelectedTaskGroupName] = useState<string | null>('');
  const isViewer = projectUserRole === Gql.ProjectUserRoleType.CanView;

  var data: any = [];

  const { data: userMeData } = Gql.useGetUserMeQuery({
    fetchPolicy: 'no-cache'
  });

  // GET PROJECT USER ROLE FROM LOCAL STORAGE
  useEffect(() => {
    setProjectUserRole(localStorage.getItem('ProjectUserRole') || '');
    pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.js`;
  }, []);

  const companySubscriptions = Gql.useCompanySubscriptionsQuery({
    variables: {
      sorting: {
        direction: Gql.SortDirection.Desc,
        field: Gql.CompanySubscriptionSortFields.SubscriptionEndDate
      },
      paging: {
        limit: 1
      }
    },
    onError: onError
  });

  const isGracePeriod = companySubscriptions?.data?.companySubscriptions?.nodes[0]?.isSubscriptionInGracePeriod;
  const subscriptionEndDate = moment(companySubscriptions?.data?.companySubscriptions?.nodes[0]?.subscriptionEndDate).format('DD MMMM, YYYY');
  const remainingDays = 15 - moment().diff(subscriptionEndDate, 'days');
  const showAlertBanner = isGracePeriod;

  const ProjectOwner = projectUserRole === Gql.ProjectUserRoleType.ProjectOwner;
  const CloudCoordinator = projectUserRole === Gql.ProjectUserRoleType.CloudCoordinator;

  const { queries: activityQueries, setPaging: setActivityPaging } = useQueries<Gql.AuditLogFilter, Gql.AuditLogSort>({
    paging: { offset: 0, limit: 10 }
  });

  const me = userMeData?.getUserMe;
  const isReadChangeLog = me?.isReadChangeLog;

  // getChangeLog lazy query
  const [getChangeLogs, { data: changeLogData }] = Gql.useGetChangeLogsLazyQuery({
    onError: onError
  });

  const {
    data: activityData,
    loading: activityLoading,
    refetch: refetchActivity,
    fetchMore
  } = Gql.useGetActivityLogsQuery({
    variables: {
      ...activityQueries,
      filter: {
        ...activityQueries.filter,
        // ...selectedActivityFilter,
        projectId: { eq: _.toString(projectId) },
        module: { notIn: [Gql.AuditLogModuleType.TaskComment, Gql.AuditLogModuleType.WorkspaceComment] }
      },
      sorting: [
        {
          direction: Gql.SortDirection.Desc,
          field: Gql.AuditLogSortFields.CreatedAt
        }
      ]
    },
    // notifyOnNetworkStatusChange: true,
    fetchPolicy: 'cache-and-network',
    onError: onError,
    skip: !projectId
  });
  const activityDatasource = (activityData?.auditLogs?.nodes as Gql.AuditLog[]) ?? [];

  const {
    data: projectData,
    loading: projectLoading,
    refetch: refetchProject
  } = Gql.useGetProjectQuery({
    variables: { id: _.toString(projectId) },
    onError: onError,
    skip: !projectId
  });
  const project = projectData?.project;

  const { data: diaryData } = Gql.useGetSideDiariesQuery({
    variables: {
      filter: {
        projectId: { eq: _.toString(projectId) },
        workspaceGroup: {
          name: { eq: 'Site Diary' }
        },
        status: {
          neq: Gql.ProjectDocumentStatus.Draft || Gql.ProjectDocumentStatus.Rejected
        }
      },
      sorting: [
        {
          direction: Gql.SortDirection.Desc,
          field: Gql.ProjectDocumentSortFields.SubmittedAt
        }
      ],
      paging: {
        limit: 6
      }
    },
    skip: !projectId,
    onError: onError,
    fetchPolicy: 'cache-and-network'
  });

  const columns: ColumnsType<any> = [
    {
      // title: 'Name',
      dataIndex: 'name',
      key: 'name',
      // width: 200,
      render: (text: string, record: any) => {
        return (
          <Tooltip title="Click to preview">
            <Space
              className="cursor-pointer"
              onClick={() => {
                setDocId(record.id);
                pdfTronTabRef.current.openModal();
              }}
            >
              <Icon name="pdf"></Icon>
              <Paragraph>
                <p>{text}</p>
              </Paragraph>
            </Space>{' '}
          </Tooltip>
        );
      }
    }
  ];

  useEffect(() => {
    if (isAllowed(companySubscriptions, ProjectAccess.SCHEDULE_CHART)) {
      loadPhysicalGraph();
      loadFinancialGraph()
    }
  }, [project]);

  useEffect(() => {
    if (router.query.showEvent) {
      eventModalRef.current.openModal();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [router.query.showEvent]);

  const getFile = (save = false) => {
    if (project && project.fileUrlProgress) {
      axios({
        method: 'GET',
        url: project?.fileUrlProgress,
        responseType: 'blob',
        headers: {
          'Content-Type': 'application/octet-stream'
        }
      })
        .then((result: any) => {
          let blob = new Blob([result.data], { type: 'application/octet-stream' });
          var file = new File([blob], project?.title + '_Physical.xlsx');
          const files: any[] = [];
          files.push(file);
          if (save && file) saveAs(file);
          else performNewFileData(files);
        })
        .catch(error => {
          onError(error);
        });
    } else if (project && !project.fileUrlProgress) message.error('No physical file details found.');
  };

  const getFinanceFile = (save = false) => {
    if (project && project.fileUrlProgressFinance) {
      axios({
        method: 'GET',
        url: project?.fileUrlProgressFinance,
        responseType: 'blob',
        headers: {
          'Content-Type': 'application/octet-stream'
        }
      })
        .then((result: any) => {
          let blob = new Blob([result.data], { type: 'application/octet-stream' });
          var file = new File([blob], project?.title + '_Finance.xlsx');
          const files: any[] = [];
          files.push(file);
          if (save && file) saveAs(file);
          else performNewFileDataFinance(files);
        })
        .catch(error => {
          onError(error);
        });
    } else if (project && !project.fileUrlProgressFinance) message.error('No financial file details found.');
  };

  useEffect(() => {
    if (project?.metTownId) getMETWeather();
  }, [projectData]);

  const loadMoreActivity = () => {
    fetchMore({
      variables: {
        paging: {
          offset: activityDatasource.length,
          limit: 10
        }
      },
      updateQuery: (prev, { fetchMoreResult }) => {
        if (!fetchMoreResult) return prev;
        return {
          ...prev,
          auditLogs: {
            ...prev.auditLogs,
            nodes: [...prev.auditLogs.nodes, ...fetchMoreResult.auditLogs.nodes]
          }
        };
      }
    });
  };

  useEffect(() => {
    const groups = activityDatasource.reduce((groups: any, game: any) => {
      const date = game.createdAt.split('T')[0];
      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(game);
      return groups;
    }, {});


    const groupArrays = Object.keys(groups).map(date => {
      return {
        date,
        activities: groups[date]
      };
    });

    setSelectedActivityArray(groupArrays as any);
  }, [activityData]);

  useEffect(() => {
    if (!projectId) return;

    getPieData();
    getUpcomingEvent();
  }, [projectData, projectId]);

  const getPieData = async (taskGroup?: string, workspaceGroup?: string) => {
    try {
      const pieChartData = await DashboardApiService.overview({
        body: {
          TaskGroupId: taskGroup ?? '',
          workspaceGroupId: workspaceGroup ?? ''
        }
      });

      setPieChartData([
        {
          type: 'OPEN',
          value: pieChartData?.tasks?.countOfOpenTasks
        },
        {
          type: 'IN PROGRESS',
          value: pieChartData?.tasks?.countOfInProgressTasks
        },
        {
          type: 'HOLD',
          value: pieChartData.tasks?.countOfHoldTasks
        },
        {
          type: 'CLOSED',
          value: pieChartData?.tasks?.countOfCompletedTasks
        }
      ]);
      setPieChartWorkspaceData([
        {
          type: 'SUBMITTED',
          value: pieChartData?.workspace?.submittedWorkspaceTasks
        },
        {
          type: 'IN REVIEW',
          value: pieChartData?.workspace?.inReviewWorkspaceTasks
        },
        {
          type: 'PENDING',
          value: pieChartData?.workspace?.pendingWorkspaceTasks
        },
        // {
        //   type: 'IN PROGRESS',
        //   value: pieChartData?.workspace?.inProgressWorkspaceTasks
        // },
        {
          type: 'APPROVED',
          value: pieChartData?.workspace?.approvedWorkspaceTasks
        },
        {
          type: 'REJECTED',
          value: pieChartData?.workspace?.rejectedWorkspaceTasks
        }
      ]);
      setSelectedTaskGroupName(pieChartData?.tasks?.name);
      setSelectedWorkspaceGroupName(pieChartData?.workspace?.name);
    } catch (e) {
      onError(e);
    }
  };

  function getMETWeather() {
    const URL = 'https://api.met.gov.my/v2.1/data';

    const param = {
      datasetid: 'FORECAST',
      datacategoryid: 'GENERAL',
      locationid: project?.metTownId,
      start_date: moment().format('YYYY-MM-DD'),
      end_date: moment().format('YYYY-MM-DD')
    };

    axios
      .get(URL, { params: param, headers: { Authorization: 'METToken 3f8f42b5cf0a0e5db2dec513ef78ea171eb4af2e' } })
      .then(response => {
        setWeatherData(response.data.results);
      });
  }

  const handleChange = (event: any) => {
    if (event !== 'all') {
      refetchActivity({
        ...activityQueries,
        filter: {
          ...activityQueries.filter,
          projectId: { eq: _.toString(projectId) },
          module: { eq: event }
        },
        sorting: [
          {
            direction: Gql.SortDirection.Desc,
            field: Gql.AuditLogSortFields.CreatedAt
          }
        ]
      });
    } else {
      // setSelectedActivityFilter({})
      refetchActivity({
        ...activityQueries,
        filter: {
          ...activityQueries.filter,
          projectId: { eq: _.toString(projectId) },
          module: { notIn: [Gql.AuditLogModuleType.TaskComment, Gql.AuditLogModuleType.WorkspaceComment] }
        },
        sorting: [
          {
            direction: Gql.SortDirection.Desc,
            field: Gql.AuditLogSortFields.CreatedAt
          }
        ]
      });
    }
  };

  const [calendarMonth, setCalendarMonth] = useState(moment());

  const getUpcomingEvent = async () => {
    setUpcomingLoading(true);
    const month = await moment(calendarMonth).format('YYYY-MM');
    const data = await DashboardApiService.tasksEventsList({
      body: { month: month }
    });

    const newData = data.filter(
      (data: any) => moment(data[0]).format('YYYY-MM-DD hh:m') >= moment().format('YYYY-MM-DD hh:m')
    );
    setUpcomingEventData(newData);
    setUpcomingLoading(false);
  };

  function ExcelDateToJSDate(date: any) {
    return moment(new Date(Math.round((date - 25569) * 86400 * 1000))).format('MMM-YY');
  }

  const loadPhysicalGraph = useCallback(() => {
    if (project && project.fileUrlProgress && project.fileUrlProgressJson) {
      setLineChartData(JSON.parse(project.fileUrlProgressJson));
      performGraphTableData(JSON.parse(project.fileUrlProgressJson));
    }
  }, [project]);

  const loadFinancialGraph = useCallback(() => {
    if (project && project.fileUrlProgressFinance && project.fileUrlProgressFinanceJson) {
      setLineChartDataFinance(JSON.parse(project.fileUrlProgressFinanceJson));
      performGraphTableDataFinance(JSON.parse(project.fileUrlProgressFinanceJson));
    }
  }, [project]);

  function performNewFileData(files: any[]) {
    // const reader = new FileReader();

    // reader.onload = event => {
    //   const wb = read(event?.target?.result);
    //   const sheets = wb.SheetNames;

    //   let temp: any = [];

    //   if (sheets.length) {
    //     const rows = utils.sheet_to_json(wb.Sheets[sheets[2]], { header: 1, defval: '' }) as any[];

    //     const keys = Object.keys(rows[1]);
    //     if (keys.length > 0) {
    //       keys.forEach((element: string) => {
    //         temp.push({
    //           year: ExcelDateToJSDate((rows[2].slice(1) as any)[+element]),
    //           value:
    //             (rows[3].slice(1) as any)[+element] !== ''
    //               ? +((rows[3].slice(1) as any)[+element] * 100)?.toFixed(2)
    //               : null,
    //           category: 'PLANNED'
    //         });
    //         temp.push({
    //           year: ExcelDateToJSDate((rows[2].slice(1) as any)[+element]),
    //           value:
    //             (rows[4].slice(1) as any)[+element] !== ''
    //               ? +((rows[4].slice(1) as any)[+element] * 100)?.toFixed(2)
    //               : null,
    //           category: 'ACTUAL',
    //           progressDays: (rows[10]?.slice(1) as any)[+element] !== '' ? +(rows[10]?.slice(1) as any)[+element] : null
    //         });
    //         // temp.push({
    //         //   year: ExcelDateToJSDate((rows[2].slice(1) as any)[+element]),
    //         //   value:
    //         //     (rows[17].slice(1) as any)[+element] !== ''
    //         //       ? +((rows[17].slice(1) as any)[+element] * 100)?.toFixed(2)
    //         //       : null,
    //         //   category: 'EOT',
    //         //   progressDays: (rows[10]?.slice(1) as any)[+element] !== '' ? +(rows[10]?.slice(1) as any)[+element] : null
    //         // });
    //       });
    //       data = temp;
    //       console.log(JSON.stringify(temp));
    //       setLineChartData(temp);
    //       performGraphTableData(temp);
    //     }
    //   }
    // };
    // reader.readAsArrayBuffer(files[0] as File);
  }

  function performNewFileDataFinance(files: any[]) {
    const reader = new FileReader();

    reader.onload = event => {
      const wb = read(event?.target?.result);
      const sheets = wb.SheetNames;

      let temp: any = [];

      if (sheets.length) {
        const rows = utils.sheet_to_json(wb.Sheets[sheets[0]], { header: 1, defval: '' }) as any[];

        const keys = Object.keys(rows[1]);
        if (keys.length > 0) {
          keys.forEach((element: string) => {
            temp.push({
              year: ExcelDateToJSDate((rows[7].slice(1) as any)[+element]),
              value: (rows[8].slice(1) as any)[+element] !== '' ? (rows[8].slice(1) as any)[+element] / 1000 : null,
              category: 'SCHEDULE ( x RM1000 )',
              plannedClaim:
                (rows[12].slice(1) as any)[+element] !== '' ? (rows[12].slice(1) as any)[+element] / 1000 : null,
              percentValue: (rows[2].slice(1) as any)[+element] !== '' ? (rows[2].slice(1) as any)[+element] : null
            });
            temp.push({
              year: ExcelDateToJSDate((rows[7].slice(1) as any)[+element]),
              value:
                (rows[9].slice(1) as any)[+element] !== ''
                  ? ((rows[9].slice(1) as any)[+element] / 1000)?.toFixed(5)
                  : null,
              category: 'ACTUAL ( x RM1000 )',
              currentClaim:
                (rows[13].slice(1) as any)[+element] !== ''
                  ? ((rows[13].slice(1) as any)[+element] / 1000)?.toFixed(5)
                  : null,
              percentValue: (rows[3].slice(1) as any)[+element] !== '' ? (rows[3].slice(1) as any)[+element] : null,
              progressDays: (rows[16]?.slice(1) as any)[+element] !== '' ? +(rows[16]?.slice(1) as any)[+element] : null
            });
          });
          data = temp;
          setLineChartDataFinance(temp);
          performGraphTableDataFinance(temp);
        }
      }
    };
    reader.readAsArrayBuffer(files[0] as File);
  }

  useEffect(() => {
    performTableData();
  }, [columnData]);

  useEffect(() => {
    performTableDataFinance();
  }, [columnDataFinance]);

  const performGraphTableData = (data: any) => {
    const years: any = [];
    const columnData: any = [];

    columnData.push({
      title: 'Year',
      fixed: 'left',
      width: 300,
      children: [
        {
          title: 'Month',
          key: 'bulan',
          dataIndex: 'year',
          width: 300
        }
      ]
    });

    data.map((data2: any) => {
      if (years.indexOf(moment(data2.year, 'MMM-YY').format('yyyy')) === -1) {
        years.push(moment(data2.year, 'MMM-YY').format('yyyy'));
      }
    });

    years.forEach((element: any) => {
      let years = {
        title: element,
        key: element,
        width: 60,
        children: data
          .filter((obj: any, index: any) => {
            if (moment(obj.year, 'MMM-YY').format('YYYY') === element && index % 2 !== 1) return obj;
          })
          .map((obj: any, index: any) => {
            if (moment(obj.year, 'MMM-YY').format('YYYY') === element)
              return {
                title: moment(obj.year, 'MMM-YY').format('MMM').toString(),
                key: moment(obj.year, 'MMM-YY').format('MMM').toString(),
                width: 60,
                dataIndex: obj.year
              };
            return null;
          })
      };
      if (element !== 'Invalid date') columnData.push(years);
    });

    setColumnData(columnData);
  };

  const performGraphTableDataFinance = (data: any) => {
    const years: any = [];
    const columnData: any = [];

    columnData.push({
      title: 'Year',
      fixed: 'left',
      width: 300,
      children: [
        {
          title: 'Month',
          key: 'bulan',
          dataIndex: 'year',
          width: 300
        }
      ]
    });

    data.map((data2: any) => {
      if (years.indexOf(moment(data2.year, 'MMM-YY').format('yyyy')) === -1) {
        years.push(moment(data2.year, 'MMM-YY').format('yyyy'));
      }
    });

    years.forEach((element: any) => {
      let years = {
        title: element,
        key: element,
        width: 60,
        children: data
          .filter((obj: any, index: any) => {
            if (moment(obj.year, 'MMM-YY').format('YYYY') === element && index % 2 !== 1) return obj;
          })
          .map((obj: any, index: any) => {
            if (moment(obj.year, 'MMM-YY').format('YYYY') === element)
              return {
                title: moment(obj.year, 'MMM-YY').format('MMM').toString(),
                key: moment(obj.year, 'MMM-YY').format('MMM').toString(),
                width: 60,
                dataIndex: obj.year
              };
            return null;
          })
      };
      if (element !== 'Invalid date') columnData.push(years);
    });
    setColumnDataFinance(columnData);
  };

  const performTableData = () => {
    const rowData: any[] = [];

    // BIL BULAN ROW
    let obj = {
      key: 'year',
      year: 'MONTH NO.'
    } as Object;

    let uniques = lineChartData.filter((obj: any, index: any) => {
      if (index % 2 !== 1) return obj;
    });

    uniques.forEach((element: any, index: any) => {
      obj = { ...obj, [element.year]: index };
    });

    rowData.push(obj);

    // % JADUAL ROW
    obj = {
      key: 'year',
      year: '% SCHEDULE'
    } as Object;

    uniques = lineChartData.filter((obj: any, index: any) => {
      if (obj.category == 'PLANNED' || obj.category == 'SCHEDULE') return obj;
    });

    uniques.forEach((element: any, index: any) => {
      obj = { ...obj, [element.year]: element?.value?.toFixed(2) };
    });

    rowData.push(obj);

    // // % EOT ROW
    // obj = {
    //   key: 'year',
    //   year: '% PLANNED EOT'
    // } as Object;

    // uniques = lineChartData.filter((obj: any, index: any) => {
    //   if (obj.category == 'EOT') return obj;
    // });

    // uniques.forEach((element: any, index: any) => {
    //   obj = { ...obj, [element.year]: element?.value?.toFixed(2) };
    // });

    // rowData.push(obj);

    // % SEBENAR ROW
    obj = {
      key: 'year',
      year: '% ACTUAL'
    } as Object;

    uniques = lineChartData.filter((obj: any, index: any) => {
      if (obj.category == 'ACTUAL') return obj;
    });

    uniques.forEach((element: any, index: any) => {
      obj = { ...obj, [element.year]: element?.value?.toFixed(2) ?? '' };
    });

    rowData.push(obj);
    setRowData(rowData);
  };

  const performTableDataFinance = () => {
    const rowData: any[] = [];

    // BIL BULAN ROW
    let obj = {
      key: 'year',
      year: 'MONTH NO.'
    } as Object;

    let uniques = lineChartDataFinance.filter((obj: any, index: any) => {
      if (index % 2 !== 1) return obj;
    });

    uniques.forEach((element: any, index: any) => {
      obj = { ...obj, [element.year]: index };
    });

    rowData.push(obj);

    // PLANNED - CUMULATIVE (RM)
    obj = {
      key: 'year',
      year: 'SCHEDULE - CUMULATIVE (RM)'
    } as Object;

    uniques = lineChartDataFinance.filter((obj: any, index: any) => {
      if (obj.category == 'PLANNED ( x RM1000 )' || obj.category === 'SCHEDULE ( x RM1000 )') return obj;
    });

    uniques.forEach((element: any, index: any) => {
      obj = { ...obj, [element.year]: (+element.value * 1000)?.toFixed(2)?.replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,') };
    });

    rowData.push(obj);

    // PLANNED - CUMULATIVE (%)
    obj = {
      key: 'year',
      year: 'SCHEDULE - CUMULATIVE (%)'
    } as Object;

    uniques = lineChartDataFinance.filter((obj: any, index: any) => {
      if (obj.category == 'PLANNED ( x RM1000 )' || obj.category === 'SCHEDULE ( x RM1000 )') return obj;
    });

    uniques.forEach((element: any, index: any) => {
      obj = {
        ...obj,
        [element.year]:
          element.percentValue != null
            ? (+element.percentValue * 100)?.toFixed(2)?.replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
            : ''
      };
    });

    rowData.push(obj);

    // ACTUAL - CUMULATIVE (RM)
    obj = {
      key: 'year',
      year: 'ACTUAL - CUMULATIVE (RM)'
    } as Object;

    uniques = lineChartDataFinance.filter((obj: any, index: any) => {
      if (obj.category == 'ACTUAL ( x RM1000 )') return obj;
    });

    uniques.forEach((element: any, index: any) => {
      obj = {
        ...obj,
        [element.year]: element.value
          ? (+element.value * 1000)?.toFixed(2)?.replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
          : ''
      };
    });

    rowData.push(obj);

    // ACTUAL - CUMULATIVE (%)
    obj = {
      key: 'year',
      year: 'ACTUAL - CUMULATIVE (%)'
    } as Object;

    uniques = lineChartDataFinance.filter((obj: any, index: any) => {
      if (obj.category == 'ACTUAL ( x RM1000 )') return obj;
    });

    uniques.forEach((element: any, index: any) => {
      obj = {
        ...obj,
        [element.year]:
          element.percentValue != null
            ? (+element.percentValue * 100)?.toFixed(2)?.replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
            : ''
      };
    });

    rowData.push(obj);

    obj = {
      key: 'year',
      year: 'MONTHLY CLAIM (RM)'
    } as Object;

    uniques.forEach((element: any, index: any) => {
      obj = {
        ...obj,
        [element.year]: Number(element.monthlyClaim)?.toFixed(2)?.replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
          ?? ''
      };
    });

    rowData.push(obj);

    setRowDataFinance(rowData);
  };

  const getProgress = (type: string, graphType: string) => {
    // CALCULATION FOR PHYSICAL GRAPH
    if (graphType == 'physical') {
      const filtered = lineChartData.filter((data: any) => {
        return data.category === 'ACTUAL' && data.value !== null && data.year !== 'Invalid date';
      });

      const sebenar = filtered[filtered.length - 1];

      const jadual = lineChartData.filter((data: any) => {
        return (trim(data.category) === 'PLANNED' || trim(data.category) === 'SCHEDULE') && data.year === sebenar?.year;
      })[0];

      if (type == 'jadual') return jadual?.value?.toFixed(2) ?? 0;
      else if (type == 'sebenar') return sebenar?.value?.toFixed(2) ?? 0;
      else if (type == 'progress') return (sebenar?.value?.toFixed(2) - jadual?.value?.toFixed(2)).toFixed(2);
      else if (type == 'days') return sebenar?.progressDays ?? 0;

    } else if (graphType == 'financial') {
      // CALCULATION FOR FINANCIAL GRAPH
      const filtered = lineChartDataFinance.filter((data: any) => {
        return data.category === 'ACTUAL ( x RM1000 )' && data.value !== null && data.year !== 'Invalid date';
      });

      const sebenar = filtered[filtered.length - 1];

      const jadual = lineChartDataFinance.filter((data: any) => {
        return (trim(data.category) === 'PLANNED ( x RM1000 )' || trim(data.category) === 'SCHEDULE ( x RM1000 )') && data.year === sebenar?.year;
      })[0];

      if (type == 'jadual') return jadual?.value ?? 0;
      else if (type == 'sebenar') return sebenar?.value ?? 0;
      else if (type == 'progress') return sebenar?.value - jadual?.value;
      else if (type == 'plannedClaim') return jadual?.plannedClaim ?? 0;
      else if (type == 'currentClaim') return sebenar?.currentClaim ?? 0;
      else if (type == 'jadualPercent') return jadual?.percentValue;
      else if (type == 'sebenarPercent') return sebenar?.percentValue;
      else if (type == 'days') return sebenar?.progressDays ?? 0;
    }
  };

  // function to get full date with suffix month and year
  const getFullDate = (date: Date) => {
    const month = date.toLocaleString('default', { month: 'long' });
    const year = date.getFullYear();
    const day = date.getDate();
    const suffix = (day: number) => {
      if (day > 3 && day < 21) return 'th';
      switch (day % 10) {
        case 1:
          return 'st';
        case 2:
          return 'nd';
        case 3:
          return 'rd';
        default:
          return 'th';
      }
    };
    return `${day}${suffix(day)} ${month} ${year}`;
  };

  const [ellipsis, setEllipsis] = useState(true);

  const [weatherModal, showWeatherModal] = useModal({
    title: `${getFullDate(new Date())}`,
    content: (
      <>
        {weatherData?.length > 0 && (
          <Row justify={'space-between'} className="">
            <Col>
              {weatherData
                ?.sort((a: any, b: any) => {
                  const order = ['FGM', 'FGA', 'FGN'];
                  return order.indexOf(a?.datatype) - order.indexOf(b?.datatype);
                })
                .map((data: any, index: number) => {
                  if (data?.datatype === 'FGM' || data?.datatype === 'FGA' || data?.datatype === 'FGN') {
                    return (
                      <Row className="py-3 flex items-center" key={index}>
                        <p>
                          {data?.datatype === 'FGM'
                            ? 'Morning: '
                            : data?.datatype === 'FGA'
                              ? 'Afternoon: '
                              : 'Night: '}
                        </p>
                        <p className="ml-3">
                          {data?.value?.toLowerCase?.() === 'no rain' && data?.datatype !== 'FGN'
                            ? 'Sunny'
                            : data?.value}
                        </p>
                        <span className="ml-5">
                          {data?.datatype !== 'FGN' ? (
                            <Icon
                              name={
                                data?.value === 'Rain'
                                  ? 'rainy'
                                  : data?.value === 'Thunderstorms'
                                    ? 'thunderstorm'
                                    : 'sunny'
                              }
                              width={50}
                              height={50}
                            />
                          ) : (
                            <Icon name={data.value === 'Rain' ? 'rainy' : 'night'} width={50} height={50} />
                          )}
                        </span>
                      </Row>
                    );
                  }
                })}
            </Col>
            <Divider type="vertical" className=" h-[289px] " />
            <Col className=" flex flex-col justify-center mr-6">
              <Row className="py-3 flex items-center" justify={'center'}>
                <p>Temperature: </p>
                <p className="ml-3">
                  {' '}
                  {
                    (weatherData?.find((data: any) => {
                      return data?.datatype === 'FMINT';
                    })).value
                  }
                  °C
                </p>
              </Row>
              <Row className="py-3 flex items-center" justify={'center'}>
                <p>Location: </p>
                <p className="ml-3">
                  {
                    (weatherData?.find((data: any) => {
                      return data?.datatype === 'FSIGW';
                    })).locationname
                  }
                </p>
              </Row>
            </Col>
          </Row>
        )}
      </>
    )
  });

  // Change Log Modal
  const [buttonName, setButtonName] = useState<string | undefined | null>('');
  const [buttonUrl, setButtonUrl] = useState<string | undefined | null>('');
  const [currentIndex, setCurrentIndex] = useState<number>(0);

  const carouselRef = useRef<any>(null);

  useEffect(() => {
    if (changeLogData?.changeLogs?.nodes) {
      const data = changeLogData?.changeLogs?.nodes?.[currentIndex];

      if (data) {
        setButtonName(data?.buttonName);
        setButtonUrl(data?.buttonUrl);
      }
    }
  }, [currentIndex, changeLogData?.changeLogs?.nodes, data?.buttonName, data?.buttonUrl]);

  const handleButtonClick = () => {
    if (buttonUrl) {
      window.open(buttonUrl, '_blank');
    }
  };

  const [changeLogModal, showChangeLogModal, closeChangeLogModal] = useModal({
    width: '1200px',
    bodyStyle: { padding: '0px', height: '600px' },
    className: 'rounded-lg',
    closeIcon: (
      <div className="w-10 h-10 rounded-full bg-white pl-1.5 absolute right-5 top-5">
        <Icon name="cross" width={20} height={20} className="bg-red" />
      </div>
    ),
    content: (
      <div>
        <Carousel
          className="w-full h-full mx-auto relative"
          ref={carouselRef}
          afterChange={(current: number) => setCurrentIndex(current)}
          arrows={true}
          prevArrow={<Icon name="left-arrow" width={150} height={150} className="" />}
          nextArrow={
            <Icon name="right-arrow" width={150} height={150} className="w-20 h-20 bg-white rounded-full shadow-lg" />
          }
        >
          {changeLogData?.changeLogs?.nodes?.map?.((data: any) => {
            return (
              <>
                <Image key={data?.id} src={data?.imageUrl} className="object-cover rounded-lg" alt="change log image" />
              </>
            );
          })}
        </Carousel>
        <div className="mt-5 flex justify-center items-center">
          <Button type="primary" className="absolute bottom-0 transform -translate-x-1/2" onClick={handleButtonClick}>
            {buttonName}
          </Button>
        </div>
      </div>
    ),
    onCancel: async () => {
      await updateUser({ variables: { input: { isReadChangeLog: true } } });
      closeChangeLogModal();
    }
  });

  const [updateUser, { loading: updating }] = Gql.useUpdateUserMeMutation({
    onError: onError as any,
    onCompleted: () => closeChangeLogModal()
  });

  // TODO: ENABLE BACK FOR PRODUCTION AFTER SUTRACOM TRIAL
  useEffect(() => {
    if (me && !isReadChangeLog) {
      getChangeLogs();
      showChangeLogModal();
    }
  }, [me, isReadChangeLog]);

  return (
    <div className={`flex-wrap flex-row justify-between ${showAlertBanner ? 'mt-8 mb-14' : 'my-14'} pl-5 pr-3 `}>
      {showAlertBanner && (
        <div
          className="custom-alert-div p-4 mb-4 rounded-lg flex justify-between items-center"
          style={{
            backgroundColor: '#47819C',
            color: '#FFF',
          }}
        >
          <span>
            <Icon name='exclamation' className='mr-4' />
            <strong>Attention:</strong> Subscription expired on {subscriptionEndDate}. Renew now to ensure continued
            service. You have {remainingDays} days left of the grace period.
          </span>
          <div className='flex gap-5 items-center'>
            <button
              className="bg-white text-black px-4 py-2 rounded-md border-none"
              onClick={() => window.open('https://www.bina.cloud/contacts', '_blank')}
            >
              Renew Now
            </button>
            <p>{remainingDays} days left</p>
          </div>
        </div>
      )}
      {weatherModal}
      {changeLogModal}
      <div className='flex w-full bg-[url("../assets/hero_bg.png")] bg-opacity-0 bg-right bg-cover h-60 pt-14 pb-16 px-16 mb-2'>
        <div className="leading-10">
          <p className="w-full text-[26px] font-medium text-slate-800">Welcome, {userMeData?.getUserMe?.name}</p>
          <p className="w-full text-2xl text-gray-200 text-slate-500">
            Here&#39;s what going on with your project today
          </p>
        </div>
      </div>

      <Row>
        <div className="w-8/12 mt-2">
          <Row className="w-full">
            {isAllowed(companySubscriptions, ProjectAccess.DASHBOARD) && (
              <Col className="flex flex-column w-full mb-5">
                <Card className="w-full mx-auto" bodyStyle={{ padding: '12px' }}>
                  <Spin spinning={projectLoading}>
                    <Col className="mb-1">
                      <Row className="w-full justify-between mb-4">
                        {/* TAB MENU */}
                        <Menu
                          mode="horizontal"
                          defaultSelectedKeys={['physical']}
                          className="pl-0"
                          items={[
                            {
                              key: 'physical',
                              label: (
                                <div className="font-semibold">
                                  <Link href="">
                                    <a
                                      onClick={() => {
                                        setGraphType('Physical');
                                      }}
                                    >
                                      Physical
                                    </a>
                                  </Link>
                                </div>
                              )
                            },
                            {
                              key: 'financial',
                              label: (
                                <div className="font-semibold">
                                  <Link href="">
                                    <a
                                      onClick={() => {
                                        setGraphType('Financial');
                                      }}
                                    >
                                      Financial
                                    </a>
                                  </Link>
                                </div>
                              )
                            }
                          ]}
                        />
                        <div className="text-right">
                          {(ProjectOwner || CloudCoordinator) && (
                            <ThreeDotsDropDown
                              onClick={(e: any) => {
                                if (e.key === 'Export') {
                                  graphyType === 'Physical' ? getFile(true) : getFinanceFile(true);
                                } else if (e.key === 'Upload') {
                                  graphUploadModalRef?.current?.openModal();
                                } else if (e.key === 'Sample') {
                                  graphyType === 'Physical'
                                    ? saveAs(
                                      '../assets/S-CURVE_TEMPLATE-PH-REV_1.xlsx',
                                      'S-CURVE_TEMPLATE-PH-REV_1.xlsx'
                                    )
                                    : saveAs(
                                      '../assets/S-CURVE_TEMPLATE-FI-REV_1.xlsx',
                                      'S-CURVE_TEMPLATE-FI-REV_1.xlsx'
                                    );
                                }
                              }}
                              items={[
                                { label: <p>Upload File</p>, key: 'Upload' },
                                { label: <p>Export Excel</p>, key: 'Export' },
                                { label: <p>Download Sample</p>, key: 'Sample' }
                              ]}
                            />
                          )}
                        </div>
                      </Row>
                    </Col>

                    {/* PHYSICAL GRAPH */}
                    {graphyType === 'Physical' && (
                      <Col>
                        {project?.fileUrlProgress && (
                          <>
                            <Line
                              {...{
                                data: lineChartData,
                                xField: 'year',
                                yField: 'value',
                                seriesField: 'category',
                                point: {
                                  size: 3,
                                  shape: 'circle'
                                },
                                responsive: true,
                                connectNulls: false
                              }}
                              localRefresh
                            />
                            <Row className="w-full justify-between mt-5">
                              <Col className="w-1/3 justify-between text-center">
                                <p className="font-medium">
                                  SCHEDULE: <strong className="text-md">{getProgress('jadual', 'physical')}%</strong>
                                </p>
                              </Col>
                              <Col className="w-1/3 justify-between text-center">
                                <p className="font-medium">
                                  ACTUAL:{' '}
                                  <strong className="text-md" style={{ color: '' }}>
                                    {getProgress('sebenar', 'physical')}%
                                  </strong>
                                </p>
                              </Col>
                              <Col className="w-1/3 justify-between text-center">
                                <p className="font-medium">
                                  DELAY/AHEAD:{' '}
                                  <strong
                                    className="text-md"
                                    style={{
                                      color:
                                        getProgress('progress', 'physical') > 0
                                          ? '#16a34a'
                                          : getProgress('progress', 'physical') < 0
                                            ? '#dc2626'
                                            : ''
                                    }}
                                  >
                                    {getProgress('progress', 'physical')}% ({getProgress('days', 'physical')} days)
                                  </strong>
                                </p>
                              </Col>

                              <Col className="w-full mt-2">
                                <Collapse collapsible="header" defaultActiveKey={['0']}>
                                  <Panel header="Click to view raw data" key="1">
                                    <Col className="justify-between text-center">
                                      <Table
                                        className=" overflow-x-scroll"
                                        columns={columnData}
                                        dataSource={rowData}
                                        bordered
                                        size="small"
                                        pagination={false}
                                      // scrollbar={false}
                                      // scroll={{ x: 0, y: 40 }}
                                      />
                                    </Col>
                                  </Panel>
                                </Collapse>
                              </Col>
                            </Row>
                          </>
                        )}
                        {!project?.fileUrlProgress && <Empty />}
                      </Col>
                    )}

                    {/* FINANCIAL GRAPH */}
                    {graphyType === 'Financial' && (
                      <Col>
                        {project?.fileUrlProgressFinance && (
                          <>
                            <Line
                              {...{
                                data: lineChartDataFinance,
                                xField: 'year',
                                yField: 'value',
                                seriesField: 'category',
                                point: {
                                  size: 3,
                                  shape: 'circle'
                                },
                                responsive: true,
                                connectNulls: false
                              }}
                              localRefresh
                            />
                            <Row className="w-full justify-between mt-5">
                              <Col className="w-1/2 justify-between text-center">
                                <p className="font-medium">
                                  SCHEDULE CUMULATIVE:{' '}
                                  <strong className="text-md">
                                    {'RM' +
                                      (+getProgress('jadual', 'financial') * 1000)
                                        ?.toFixed(2)
                                        .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                                  </strong>
                                  <span>
                                    {'(' +
                                      (+getProgress('jadualPercent', 'financial') * 100)
                                        ?.toFixed(2)
                                        .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,') +
                                      '%)'}
                                  </span>
                                </p>
                              </Col>
                              <Col className="w-1/2 justify-between text-center">
                                <p className="font-medium">
                                  ACTUAL CUMULATIVE:{' '}
                                  <strong className="text-md">
                                    {'RM' +
                                      (+getProgress('sebenar', 'financial') * 1000)
                                        ?.toFixed(2)
                                        .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                                  </strong>
                                  <span>
                                    {'(' +
                                      (+getProgress('sebenarPercent', 'financial') * 100)
                                        ?.toFixed(2)
                                        .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,') +
                                      '%)'}
                                  </span>
                                </p>
                              </Col>

                              <Col className="w-1/2 justify-between text-center mt-2">
                                <p className="font-medium">
                                  SCHEDULE THIS MONTH:{' '}
                                  <strong className="text-md">
                                    {'RM' +
                                      (+getProgress('plannedClaim', 'financial') * 1000)
                                        ?.toFixed(2)
                                        .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                                  </strong>
                                </p>
                              </Col>
                              <Col className="w-1/2 justify-between text-center mt-2">
                                <p className="font-medium">
                                  CLAIMED THIS MONTH:{' '}
                                  <strong className="text-md">
                                    {'RM' +
                                      (+getProgress('currentClaim', 'financial') * 1000)
                                        ?.toFixed(2)
                                        .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                                  </strong>
                                </p>
                              </Col>
                              <Col className="w-full justify-between text-center mt-1">
                                <p className="font-medium">
                                  DELAY/AHEAD:{' '}
                                  <strong
                                    style={{
                                      color: '#16a34a'
                                    }}
                                  >
                                    {' '}
                                    {'RM' +
                                      (+getProgress('progress', 'financial') * 1000)
                                        ?.toFixed(2)
                                        .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}{' '}
                                    ({getProgress('days', 'financial') + ' days'})
                                  </strong>
                                </p>
                              </Col>

                              <Col className="w-full mt-2">
                                <Collapse collapsible="header" defaultActiveKey={['0']}>
                                  <Panel header="Click to view raw data" key="1">
                                    <Col className="justify-between text-center">
                                      <Table
                                        className=" overflow-x-scroll"
                                        columns={columnDataFinance}
                                        dataSource={rowDataFinance}
                                        bordered
                                        size="small"
                                        pagination={false}
                                      // scrollbar={false}
                                      // scroll={{ x: 0, y: 40 }}
                                      />
                                    </Col>
                                  </Panel>
                                </Collapse>
                              </Col>
                            </Row>
                          </>
                        )}
                        {!project?.fileUrlProgressFinance && <Empty />}
                      </Col>
                    )}
                  </Spin>
                </Card>
              </Col>
            )}

            {isAllowed(companySubscriptions, ProjectAccess.TASK) && (
              <Col span={isViewer ? 24 : 12} className="pr-2" id="tasks-pie-container">
                <Card className="w-full" bodyStyle={{ padding: '12px' }}>
                  <Col className="w-full mb-1 flex flex-row justify-between items-center ">
                    <p className="font-medium text-medium text-slate-800">TASKS</p>
                    <Image
                      src="/assets/dashboardFilter.svg"
                      id="task-pie-filter"
                      alt="logo"
                      width={25}
                      height={25}
                      onClick={e => {
                        setShowTaskGroupInput(!showTaskGroupInput);
                      }}
                      preview={false}
                    />
                  </Col>
                  <Col className="py-3">
                    {showTaskGroupInput && (
                      <TaskGroupPicker
                        value={selectedTaskGroup as any}
                        projectOwner={true}
                        onChange={(value: Gql.ProjectGroup | any, title: string | undefined) => {
                          setSelectedTaskGroup(value);
                          getPieData(value, selectedWorkspaceGroup);
                          setShowTaskGroupInput(false);
                        }}
                      />
                    )}
                  </Col>
                  <Col className="mx-auto mb-10">
                    <div
                      style={{ height: '200px' }}
                      onClick={async () => {
                        window.location.replace('/tasks-overview');
                      }}
                    >
                      <PieChart pieChartSpan={90} data={pieChartData} taskChart={true} />
                    </div>
                  </Col>
                  <Col className="text-center">
                    <h1 className="left-0 right-0 absolute">{selectedTaskGroupName}</h1>
                  </Col>
                  <Col className="flex flex-column mt-10">
                    <div className="mx-auto">
                      <Tag color="#E00D0D" style={{ fontSize: '10px', borderRadius: '3px' }}>
                        OPEN
                      </Tag>
                      <Tag color="#2B87E3" style={{ fontSize: '10px', borderRadius: '3px' }}>
                        IN PROGRESS
                      </Tag>
                      <Tag color="#EBA10F" style={{ fontSize: '10px', borderRadius: '3px' }}>
                        HOLD
                      </Tag>
                      <Tag color="#0CA85D" style={{ fontSize: '10px', borderRadius: '3px' }}>
                        CLOSED
                      </Tag>
                    </div>
                  </Col>
                </Card>
              </Col>
            )}
            {isAllowed(companySubscriptions, ProjectAccess.WORKSPACE_DOCUMENT) && !isViewer && (
              <Col className="w-1/2 pl-2 relative" id="workspace-pie-container">
                <Card bodyStyle={{ padding: '12px' }}>
                  <Col className="w-full mb-1 flex flex-row justify-between items-center ">
                    <p className="font-medium text-medium text-slate-800">WORKSPACE</p>
                    <Image
                      id="workspace-pie-filter"
                      src="/assets/dashboardFilter.svg"
                      alt="logo"
                      width={25}
                      height={25}
                      onClick={() => setShowWorkspaceGroupInput(!showWorkspaceGroupInput)}
                      preview={false}
                    />
                  </Col>
                  <Col className="py-3">
                    {showWorkspaceGroupInput && (
                      <WorkspaceGroupPicker
                        currentAssigneesIds={[]}
                        value={selectedWorkspaceGroup}
                        onChange={(value: string) => {
                          setSelectedWorkspaceGroup(value);
                          getPieData(selectedTaskGroup, value);
                          setShowWorkspaceGroupInput(false);
                        }}
                      />
                    )}
                  </Col>
                  <Col className="mx-auto mb-10">
                    <div
                      style={{ height: '200px' }}
                      onClick={async () => {
                        window.location.replace('/digital-form');
                      }}
                    >
                      <PieChart pieChartSpan={48} data={pieChartWorkspaceData} taskChart={false} />
                    </div>
                  </Col>
                  <Col className="text-center">
                    <h1 className="absolute left-0 right-0">{selectedWorkspaceGroupName}</h1>
                  </Col>
                  <Col className="flex flex-column mt-10">
                    <div className="mx-auto">
                      <Tag color="#2B87E3" style={{ fontSize: '10px', borderRadius: '3px' }}>
                        SUBMITTED
                      </Tag>
                      <Tag color="#EBA10F" style={{ fontSize: '10px', borderRadius: '3px' }}>
                        IN REVIEW
                      </Tag>
                      <Tag color="#74caec" style={{ fontSize: '10px', borderRadius: '3px' }}>
                        PENDING
                      </Tag>
                      {/* <Tag color="#9ce255" style={{ fontSize: '10px', borderRadius: '3px' }}>
                        IN PROGRESS
                      </Tag> */}
                      <Tag color="#0CA85D" style={{ fontSize: '10px', borderRadius: '3px' }}>
                        APPROVED
                      </Tag>
                      <Tag color="#E00D0D" style={{ fontSize: '10px', borderRadius: '3px' }}>
                        REJECTED
                      </Tag>
                    </div>
                  </Col>
                </Card>
              </Col>
            )}
            <Col className="w-full mt-4 min-h-160">
              <Card
                className="w-full min-h-150"
                style={{ paddingLeft: 16 }}
                bodyStyle={{ padding: '12px', minHeight: '150px' }}
              >
                <div className="p-0">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <p className="font-medium text-slate-800">RECENT ACTIVITY</p>
                    </div>

                    <div className="w-44">
                      <Select defaultValue="all" className="mt-2" style={{ width: '100%' }} onChange={handleChange}>
                        <Option key="all" value="all">
                          View All
                        </Option>
                        {/* <Option key="project" value="Project">Project</Option> */}
                        <Option key="project" value="Project">
                          Project
                        </Option>
                        <Option key="calendar" value="Calendar">
                          Calendar
                        </Option>
                        <Option key="scurve" value="SCurve">
                          S-Curve
                        </Option>
                        <Option key="task" value="Task">
                          Task
                        </Option>
                        <Option key="cloudDocs" value="CloudDocument">
                          Cloud Documents
                        </Option>
                        <Option key="workspace" value="Workspace">
                          Workspace
                        </Option>
                        <Option key="drawing" value="Drawing">
                          Drawing
                        </Option>
                        <Option key="photo" value="Photo">
                          Photo
                        </Option>
                        <Option key="member" value="Member">
                          Members
                        </Option>
                        <Option key="schedule" value="Schedule">
                          Schedule
                        </Option>
                      </Select>
                    </div>
                  </div>

                  <Col className="mt-2 w-full h-full" id="activityScroll">                    
                    <Spin spinning={activityLoading}>
                      <InfiniteScroll
                        dataLength={activityData?.auditLogs?.nodes.length ?? 0}
                        next={loadMoreActivity} // function to fetch more data
                        hasMore={activityData?.auditLogs.pageInfo.hasNextPage || false} // boolean indicating whether there is more data to fetch
                        loader={<Spin spinning={activityLoading} />} // loader component to show while fetching data                        
                        scrollableTarget="activityScroll"
                        style={{ overflow: 'auto', paddingRight: '16px', maxHeight: '400px' }}
                        height={400}
                      >
                        {selectedActivityArray?.map((obj: any, index) => {
                          return (
                            <>
                              <div className={`pb-3 text-[#50555C] text-[15px] ${index >= 1 && 'mt-[0px]'} `}>
                                {' '}
                                {moment(tz(obj.date)).format('dddd, D MMMM YYYY')}
                              </div>
                              {obj?.activities?.map((act: any, actIndex: number) => {
                                const isLastItem = actIndex === obj.activities.length - 1;
                                let icon: IconNames = 'dashboard-al';
                                if (act.module === 'Workspace') {
                                  icon = 'digital-form-al';
                                } else if (act.module === 'Drawing') {
                                  icon = 'drawing-al';
                                } else if (act.module === 'Task') {
                                  icon = 'tasks-al';
                                } else if (act.module === 'Photo') {
                                  icon = 'photos-al';
                                } else if (act.module === 'CloudDocument') {
                                  icon = 'cloud-doc-al';
                                } else if (act.module === 'Member') {
                                  icon = 'members-al';
                                } else if (act.module === 'Schedule') {
                                  icon = 'schedule-al';
                                } else if (act.module === 'Project') {
                                  icon = 'dashboard-al';
                                } else if (act.module === 'Pdftron') {
                                  icon = 'digital-form-al';
                                }

                                return (
                                  <Timeline key={act.id} className={`${isLastItem && 'last-al-item'}`}>
                                    <div className="w-full py-2  rounded-[8px] pl-1 my-1 text-base list-no-style">
                                      <Timeline.Item dot={<Icon name={icon} />} color="red">
                                        <Text className="text-[#7C8085] text-[12px]">
                                          {moment(tz(act.createdAt)).format('LT')}
                                        </Text>
                                        <div className="w-full text-[14px]">{act.content ?? '-'}</div>
                                        <div className=" text-slate-500 text-[12px]">in {_.startCase(act.module)}</div>
                                      </Timeline.Item>
                                    </div>
                                  </Timeline>
                                );
                              })}
                            </>
                          );
                        })}
                        <Timeline.Item className="text-slate-400" key="end-of-activity">
                          End of activity
                        </Timeline.Item>
                      </InfiniteScroll>
                    </Spin>
                  </Col>
                </div>
              </Card>
            </Col>
          </Row>
        </div>

        <div className="w-4/12 px-3 mt-2">
          <Row>
            <Col className="w-full">
              <Card
                className="w-full cursor-pointer"
                style={{ paddingLeft: 16 }}
                bodyStyle={{ padding: '12px' }}
                onClick={() => {
                  weatherData.length > 0 && showWeatherModal();
                }}
              >
                <Spin spinning={projectLoading}>
                  <Row className="p-0">
                    <Col span={12} className="mb-1">
                      <p className="font-medium text-medium text-slate-800">WEATHER</p>
                    </Col>
                    <Col span={12} className="mb-1  text-right">              
                    </Col>
                    {weatherData.length == 0 && (
                      <>
                        <Col span={5} className="my-auto">
                          <div className='bg-[url("../assets/weather.png")] bg-contain h-14 w-14 mx-auto'></div>
                        </Col>

                        <Col>
                          <p className="text-lg font-medium text-slate-500">See the weather on site </p>
                          <p className="text-base text-slate-500">Add worksite address</p>
                          <Button
                            className="text-primary mt-4 mb-0 z-50"
                            onClick={() => {
                              weatherAddressModalRef?.current?.openModal();
                            }}
                          >
                            Add Address
                          </Button>
                        </Col>
                      </>
                    )}
                    {weatherData.length > 0 && (
                      <>
                        <Col span={5} className="my-auto">
                          <div className='bg-[url("../assets/weather.png")] bg-contain h-14 w-14 mx-auto'></div>
                        </Col>

                        <Col span={11} className="my-auto">
                          <p className="text-lg">
                            {' '}
                            {
                              (weatherData?.find((data: any) => {
                                return data?.datatype === 'FSIGW';
                              })).value
                            }
                          </p>
                          <p className="text-base text-slate-500 mt-1">
                            High:{' '}
                            {
                              (weatherData?.find((data: any) => {
                                return data?.datatype === 'FMAXT';
                              })).value
                            }
                            &#8451; Low:{' '}
                            {
                              (weatherData?.find((data: any) => {
                                return data?.datatype === 'FMINT';
                              })).value
                            }
                            &#8451;
                          </p>
                          <p className="text-sm text-slate-500 mt-1">
                            {
                              (weatherData?.find((data: any) => {
                                return data?.datatype === 'FSIGW';
                              })).locationname
                            }
                            ,
                            {
                              (weatherData?.find((data: any) => {
                                return data?.datatype === 'FSIGW';
                              })).locationrootname
                            }
                          </p>
                        </Col>

                        <Col span={8} className="my-auto">
                          {weatherData
                            ?.sort((a: any, b: any) => {
                              const order = ['FGM', 'FGA', 'FGN'];
                              return order.indexOf(a?.datatype) - order.indexOf(b?.datatype);
                            })
                            .map((data: any, index: number) => {
                              if (data?.datatype === 'FGM' || data?.datatype === 'FGA' || data?.datatype === 'FGN') {
                                return (
                                  <Row key={index} className="items-center">
                                    <p className="text-base text-slate-500" title="Morning">
                                      {data?.datatype === 'FGM'
                                        ? 'Morning'
                                        : data?.datatype === 'FGA'
                                          ? 'Afternoon'
                                          : 'Night'}
                                      :{' '}
                                    </p>
                                    <span className="ml-2">
                                      {data?.datatype !== 'FGN' && (
                                        <Icon
                                          name={
                                            data?.value === 'Rain'
                                              ? 'rainy'
                                              : data?.value === 'Thunderstorms'
                                                ? 'thunderstorm'
                                                : 'sunny'
                                          }
                                          width={20}
                                          height={20}
                                        />
                                      )}
                                      {data?.datatype === 'FGN' && (
                                        <Icon
                                          name={data?.value === 'Rain' ? 'rainy' : 'night'}
                                          width={20}
                                          height={20}
                                        />
                                      )}
                                    </span>
                                  </Row>
                                );
                              }
                            })}
                        </Col>
                      </>
                    )}
                  </Row>
                </Spin>
              </Card>
            </Col>

            <Col className="w-full mt-4">
              <Card className="w-full" style={{ paddingLeft: 16 }} bodyStyle={{ padding: '12px' }}>
                <Spin spinning={upcomingLoading}>
                  <Row className="p-0">
                    <Col className="w-full mb-1">
                      <p className="font-medium text-slate-800">CALENDAR</p>
                    </Col>
                    <Col span={5} className="my-auto">
                      <div className='bg-[url("../assets/event.png")] bg-contain h-12 w-12 mx-auto'></div>
                    </Col>

                    {upcomingEventData.length == 0 && (
                      <Col span={19}>
                        <p className="text-lg font-medium text-slate-500">Easy Scheduling Ahead </p>
                        <p className="text-base text-slate-500">Add event & reminder ahead</p>
                        <Button
                          className="text-primary mt-4 mb-0"
                          onClick={() => {
                            eventModalRef?.current?.openModal();
                          }}
                        >
                          Manage Event
                        </Button>
                      </Col>
                    )}
                    {upcomingEventData.length > 0 && (
                      <Col span={19}>
                        <p className="w-full text-base text-slate-600">Upcoming </p>
                        <div style={{ height: '50px', overflow: 'auto' }}>
                          {upcomingEventData.map((data: any, index: number) => (
                            <List key={index} className="mb-2">
                              <p className="w-full font-semibold text-slate-600">
                                {moment(data[0]).format('MMM D, yyyy') === moment().format('MMM D, yyyy')
                                  ? 'TODAY - ' + moment(data[0]).format('MMM D, yyyy')
                                  : moment(data[0]).format('MMM D, yyyy')}
                              </p>

                              {data[1].event?.map((event: any, index: number) => (
                                <Row key={index} className="ml-1">
                                  <Paragraph
                                    className="w-8/12 text-md font-medium font-semibold"
                                    style={{ margin: 0 }}
                                    ellipsis={ellipsis ? { rows: 1, expandable: false } : false}
                                  >
                                    {event.title}
                                  </Paragraph>
                                  <p className="w-4/12 text-md">{moment(event.startAt).format('h:mm A')}</p>
                                </Row>
                              ))}
                              {data[1].task?.map((event: any, index: number) => (
                                <Row key={index} className="ml-1">
                                  <Paragraph
                                    className="w-8/12 text-md font-medium font-semibold"
                                    style={{ margin: 0 }}
                                    ellipsis={ellipsis ? { rows: 1, expandable: false } : false}
                                  >
                                    {event.title}
                                  </Paragraph>
                                  <p className="w-4/12 text-md">{moment(event.dueDate).format('h:mm A')}</p>
                                </Row>
                              ))}
                            </List>
                          ))}
                        </div>

                        <Button
                          className="text-primary mt-4 mb-0"
                          onClick={() => {
                            router.replace({
                              query: {
                                ...router.query,
                                showEvent: 'true'
                              }
                            });
                          }}
                        >
                          Manage Event
                        </Button>
                      </Col>
                    )}
                  </Row>
                </Spin>
              </Card>
              {isAllowed(companySubscriptions, ProjectAccess.WORKSPACE_DOCUMENT) && (
                <Col span={25} className="w-full mt-4">
                  <Card className="w-full " style={{ paddingLeft: 16 }} bodyStyle={{ padding: '12px' }}>
                    <p className="font-medium text-medium text-slate-800 ">CURRENT SITE DIARY</p>

                    {diaryData?.projectDocuments.nodes.length != 0 &&
                      moment(diaryData?.projectDocuments?.nodes[0]?.submittedAt).isAfter(
                        moment().subtract(20, 'hours')
                      ) ? (
                      <>
                        <p className="font-light text-small text-slate-500 mt-4">Today's site diary is here:</p>
                        <Paragraph>
                          <p id="site-diary-name">{diaryData?.projectDocuments?.nodes[0]?.name}</p>
                        </Paragraph>
                        <Tooltip title="Click to preview">
                          <Document
                            className={'cursor-pointer'}
                            onClick={() => {
                              setDocId(diaryData?.projectDocuments?.nodes[0]?.id as string);
                            }}
                            file={diaryData?.projectDocuments.nodes[0].fileUrl}
                          >
                            <Page
                              width={300}
                              pageNumber={1}
                              onClick={() => {
                                pdfTronTabRef.current.openModal();
                              }}
                            />
                          </Document>
                        </Tooltip>
                      </>
                    ) : (
                      <>
                        <p className="font-light text-small text-slate-800 mt-4 mb-2">
                          Today's site diary has not been uploaded yet
                        </p>
                        <Empty className="mb-4" />
                      </>
                    )}
                    <p className="font-medium text-medium text-slate-800">PREVIOUS SITE DIARY</p>
 
                    <Table
                      ref={tableRef}
                      className="dashboard-table"
                      dataSource={
                        moment(diaryData?.projectDocuments?.nodes[0]?.submittedAt).isAfter(
                          moment().subtract(20, 'hours')
                        )
                          ? diaryData?.projectDocuments.nodes.slice(1)
                          : diaryData?.projectDocuments.nodes
                      }
                      columns={columns}
                      size="small"
                      pagination={false}
                      tableLayout="auto"
                      onRow={(_, index) => {
                        const attr = {
                          index
                          // moveDoc
                        };
                        return attr as React.HTMLAttributes<any>;
                      }}
                      scroll={{
                        scrollToFirstRowOnChange: false,
                        y: window.innerHeight - 230
                      }}
                    />
                    {/* </DndProvider> */}
                  </Card>
                </Col>
              )}
              <Col className="mt-4">
                <MemoList projectId={projectId} />
              </Col>
            </Col>
          </Row>
        </div>
      </Row>
      <EventModal
        ref={eventModalRef}
      />
      <WeatherAddressModal
        ref={weatherAddressModalRef}
        onSaved={() => refetchProject()}
      />
      <GraphUploadModal
        ref={graphUploadModalRef}
        onSaved={() => refetchProject()}
        type={Gql.CategoryType.SCurveGraph}
        graphType={graphyType}
      />
      <PdfTronModal ref={pdfTronTabRef} documentId={docId} />
    </div>
  );
};

Dashboard.auth = true;
Dashboard.Layout = ProjectsTabLayout;
export default Dashboard;
