import OverviewProjectDetailsList from '@components/project/OverviewProjectDetailsList';
import ProjectsTabLayout from '@components/project/ProjectsTabLayout';
import * as Gql from '@graphql';
import { Button, Form, Space } from 'antd';
import { useEffect, useState } from 'react';

interface IProjectStatus {
  issuesAndProblem: string;
  solution: string;
}


const Overview = () => {
  const [projectUserRole, setProjectUserRole] = useState<any>('');

  const owner = projectUserRole === Gql.ProjectUserRoleType.ProjectOwner;
  const CloudCoordinator = projectUserRole === Gql.ProjectUserRoleType.CloudCoordinator;
  const canEdit = owner || CloudCoordinator;
  const [isEdit, setIsEdit] = useState<any>(false);
  const [projectId, setProjectId] = useState<null | string>(null);

  useEffect(() => {
    setProjectId(localStorage.getItem('ProjectId'));
    setProjectUserRole(localStorage.getItem('ProjectUserRole'));
  }, []);

  // const { data: projectStatus } = Gql.useGetProjectStatusQuery({
  //   variables: {
  //     id: projectId as string,
  //   },
  //   skip: !projectId,
  //   onCompleted(data) {
  //     form.setFieldsValue({
  //       issuesAndProblem: data?.project?.issuesAndProblem,
  //       solution: data?.project?.solution
  //     })
  //   },
  // });

  const [form] = Form.useForm();

  // const [updateProject] = Gql.useUpdateProjectMutation({
  //   onCompleted() {
  //     return message.success('Project updated successfully');
  //   },
  //   onError() {
  //     return message.error('Something went wrong');
  //   }
  // });

  const onFinish = async (values: IProjectStatus) => {
    const { issuesAndProblem, solution } = values;

    // console.log(issuesAndProblem, solution)

    // const newIssuesAndProblem = issuesAndProblem !== projectStatus?.project?.issuesAndProblem;
    // const newSolution = solution !== projectStatus?.project?.solution;

    // if (newIssuesAndProblem || newSolution) {
    //   return await updateProject({
    //     variables: {
    //       id: projectId as string,
    //       input: {
    //         issuesAndProblem,
    //         solution
    //       }
    //     }
    //   });
    // }
  }

  return (
    <div className="flex flex-wrap flex-row justify-between pt-4 pl-5 mb-6 pr-5">
      {/* <div className="relative w-7/12">
        <div className="mb-3">
          <div className="flex grid-cols-2 gap-8">
            <div>
              <h1
                className={`text-xl cursor-pointer select-none ${
                  selectedNumber === 1 ? "text-gray100" : "text-gray50"
                }`}
                onClick={() => {
                  setSelectedNumber(1);
                }}
              >
                Comments
              </h1>
            </div>
            <div>
              {selectedNumber ? (
                <h1
                  className={`text-xl cursor-pointer select-none ${
                    selectedNumber === 2 ? "text-gray100" : "text-gray50"
                  }`}
                  onClick={() => {
                    setSelectedNumber(2);
                  }}
                >
                  Activity Log
                </h1>
              ) : (
                <h1
                  className="text-xl cursor-pointer select-none"
                  onClick={() => {
                    setSelectedNumber(2);
                  }}
                >
                  Activity Log
                </h1>
              )}
            </div>
          </div>
        </div>

        {selectedNumber === 1 && (
          <Card>
            <OverviewActivityList />
          </Card>
        )}

        {selectedNumber === 2 && <ActivityLog />}
      </div> */}

      {/* <div className="flex w-5/12 pl-3 pr-3"> */}
      <div className="flex w-full pl-3 pr-3 flex-col">
        <div className="w-full">
          <div className="flex justify-between items-center">
            <h1 className="mb-4 text-xl">Project Details</h1>
            {canEdit &&
              (!isEdit ? (
                <Button type="default" className="rounded-[8px] mb-2" onClick={() => setIsEdit(true)}>
                  <Space>Edit Details</Space>
                </Button>
              ) : (
                <Button type="primary" className="rounded-[8px] mb-2" onClick={() => setIsEdit(false)}>
                  <Space>Done</Space>
                </Button>
              ))}
          </div>
          <div className="mb-14">
            <OverviewProjectDetailsList canEdit={canEdit} isEdit={isEdit} />
          </div>
        </div>

        {/* <Form layout='vertical' form={form} requiredMark={false} onFinish={onFinish}>
          <div className="w-full mb-14">
            <div className="flex justify-between items-center">
              <h1 className="mb-4 text-xl">Issues And Problem</h1>
            </div>
            <Form.Item name={'issuesAndProblem'}>
              <TextArea
                rows={10}
              />
            </Form.Item>
            <div className='flex justify-end mt-3'>
              {canEdit ? (
                <Button
                  type='primary'
                  htmlType='submit'
                >
                  Save
                </Button>
              ) : null}
            </div>
          </div>

          <div className="w-full">
            <div className="flex justify-between items-center">
              <h1 className="mb-4 text-xl">Solution</h1>
            </div>
            <Form.Item name={'solution'}>
              <TextArea
                rows={10}
              />
            </Form.Item>
            <div className='flex justify-end mt-3 mb-10'>
              {canEdit ? (
                <Button
                  type='primary'
                  htmlType='submit'
                >
                  Save
                </Button>
              ) : null}
            </div>
          </div>
        </Form> */}

      </div>
    </div>
  );
};

Overview.auth = true;
Overview.Layout = ProjectsTabLayout;
export default Overview;
