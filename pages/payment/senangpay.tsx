import EmptyLayout from '@components/Layout/EmptyLayout';
import { Button, Result, message } from 'antd';
import { useRouter } from 'next/router';
import { useEffect } from 'react';

import * as Gql from '@graphql';
import { onError } from '@utils/error';

const Senangpay = () => {
  const router = useRouter();
  const { order_id, transaction_id, hash, msg } = router.query;

  const paymentStatus = Gql.useCheckSenangPayPaymentStatusQuery({
    variables: {
      orderId: order_id as string,
      transactionId: transaction_id as string,
      hash: hash as string,
      msg: msg as string
    },
    onError: onError
  });

  const status = (arg: string = '') => {
    switch (arg) {
      case 'paid':
        return 'success';
      case 'failed':
        return 'error';
      case 'declined':
        return 'error';
      case 'inprogress':
        return 'warning';
      default:
        return 'info';
    }
  };

  useEffect(() => {
    if (paymentStatus.data?.checkSenangPayPaymentStatus.status === 'failed') {
      router.push('/settings/account');
      message.error(paymentStatus.data?.checkSenangPayPaymentStatus.message);
      return;
    }
    if (paymentStatus.error) {
      router.push('/settings/account');
      message.error(paymentStatus.error.message);
      return;
    }
  }, [paymentStatus, router]);

  if (paymentStatus.loading) {
    return <div className="flex justify-center mt-8">Processing, please do not exit or click back button...</div>;
  }

  return (
    <Result
      status={status(paymentStatus.data?.checkSenangPayPaymentStatus.status)}
      title={
        paymentStatus.data?.checkSenangPayPaymentStatus.name +
        ' - ' +
        paymentStatus.data?.checkSenangPayPaymentStatus.message
      }
      subTitle={'Order number: ' + paymentStatus.data?.checkSenangPayPaymentStatus.orderId}
      extra={[
        <Button type="primary" key="console" onClick={() => router.push('/settings/billing')}>
          Back to Billing
        </Button>
      ]}
    />
  );
};

Senangpay.Layout = EmptyLayout;
export default Senangpay;
