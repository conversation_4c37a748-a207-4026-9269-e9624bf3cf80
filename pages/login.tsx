import { Icon } from '@commons';
import LoginForm from '@components/forms/LoginForm';
import { useGenerateOtpMutation, useVerifyOtpMutation } from '@graphql';
import { onError } from '@utils/error';
import { Button, Card, message, Row, Spin, Typography } from 'antd';
import Form, { useForm } from 'antd/lib/form/Form';
import { signIn, useSession } from 'next-auth/react';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';
import OtpInput from 'react-otp-input';
import { SystemService } from 'src/api';
import { UAParser } from 'ua-parser-js';
import SocialMediaButton from '../src/components/Buttons/SocialMediaButton';
import AuthenticationLayout from '../src/components/Layout/AuthenticationLayout';
const { Text } = Typography;

type FormValues = {
  email: string;
  password: string;
  rememberMe: boolean;
};

const Login: React.FC = () => {
  const router = useRouter();
  const [form] = useForm();
  const [isSubmitting, setSubmitting] = useState(false);
  const { status } = useSession();
  const [hasError, setHasError] = useState(false);
  const [intervalId, setIntervalId] = useState<NodeJS.Timer>();
  const [otpVisibility, setOtpVisibility] = useState(false);
  const [isOtpVerified, setIsOtpVerified] = useState(false);
  const [otp, setOtp] = useState('');

  // generate otp mutation
  const [generateOtp, { loading: generateOtpLoading }] = useGenerateOtpMutation({ onError: onError as any, });

  const [verifyOtp, { loading: verifyOtpLoading }] = useVerifyOtpMutation({
    onError: onError as any,
  });

  useEffect(() => {
    const projectId = localStorage.getItem('projectId');    
    if (router.isReady) {
      if (status === 'authenticated' && isOtpVerified) {
        // router.push((router.query?.redirect as string) ?? "/");
        router.push('/');
      }      
    }
  }, [router.isReady, status, isOtpVerified]);

  const checkServerHealth = async () => {
    try {
      // Check if the page is visible (not in sleep or background)
      if (document.visibilityState !== 'visible' || !navigator.onLine) return;

      await SystemService.alive();
    } catch (error) {
      const err = { response: { status: 500, message: 'Server is down', data: { message: 'Server is down' } } };

      !hasError && onError(err);
      setHasError(true);
    }
  };

  useEffect(() => {
    checkServerHealth();

    const intervalId = setInterval(checkServerHealth, 120000);
    setIntervalId(intervalId);

    return () => clearInterval(intervalId);
  }, [hasError]);

  // SystemService

  const onFinishFailed = (errorInfo: any) => { };

  const onFinish = async (values: FormValues) => {
    const projectDocumentReminder = localStorage.getItem('projectDocumentReminder') || 'false';
    const correspondenceReminder = localStorage.getItem('correspondenceReminder') || 'false';
    const savedColumns = localStorage.getItem('scheduleActivityColumns');

    localStorage.clear();
    sessionStorage.clear();

    localStorage.setItem('projectDocumentReminder', projectDocumentReminder);
    localStorage.setItem('correspondenceReminder', correspondenceReminder);

    if (savedColumns !== null && savedColumns !== undefined && savedColumns !== '') {
      localStorage.setItem('scheduleActivityColumns', savedColumns);
    }

    setSubmitting(true);

    try {
      //@ts-ignore
      const { error } = await signIn('credentials', {
        redirect: false,
        email: values.email,
        password: values.password,
        rememberMe: values.rememberMe,
        clientDeviceInfo: JSON.stringify(UAParser()),
        callbackUrl: '/'
      });

      if (error && hasError) {
        clearInterval(intervalId as NodeJS.Timer);
      } else if (error) {
        message.error(error);
      } else if (!error) {
        const res = await generateOtp({ variables: { input: { email: values.email } } });

        if (res.data?.generateOtp === 'Two Factor Authentication is not enabled') {
          setIsOtpVerified(true);
        }else{
          setOtpVisibility(true);
        }

      }

    } catch (e) {
      throw e;
    } finally {
      setSubmitting(false);
    }
  };

  const onVerifyOtp = async (otp: string) => {
    const res = await verifyOtp({ variables: { input: { email: form?.getFieldValue('email'), otp } } });

    if (res.data?.verifyOtp) {
      setIsOtpVerified(true);
    }
  };

  const loading = isSubmitting || generateOtpLoading || verifyOtpLoading;

  return (
    <AuthenticationLayout>
      <div className="flex-auto space-y-4 p-0 ">
        <div className="flex justify-center border-none">

          <Card className="flex w-96 justify-center border-0 " bordered={!otpVisibility}>

            <Row className="justify-center">
              <Image alt="logo" src="/assets/bina_cloud.png" className="mt-5 mb-10" width={220} height={91.5} />

            </Row>

            {!otpVisibility ? <><h1 className="mt-10 text-3xl font-bold mb-4 text-center">Welcome back!</h1>

              <Form
                layout="vertical"
                onFinish={onFinish}
                onFinishFailed={onFinishFailed}
                autoComplete="on"
                requiredMark={false}
                initialValues={{ rememberMe: false }}
                form={form}
              >
                <LoginForm />
                <Button
                  htmlType="submit"
                  type="primary"
                  className="rounded-lg h-10 w-72 font-medium -mt-4"
                  loading={isSubmitting}
                >
                  Login
                </Button>
              </Form>

              <p className="text-gray90 text-center mt-2">
                <span>
                  {'Not registered?'}
                  <Link href="/sign-up"> Sign up</Link>
                </span>
              </p>
              <p className="text-gray90 text-center mt-2">
                <span>or continue with</span>
              </p>

              <div className="flex justify-center items-center gap-4 mt-3">
                <SocialMediaButton icon={<Icon name="google" />} onClick={() => signIn('google')} />
                {/* <SocialMediaButton icon={<Icon name="facebook" />} onClick={() => signIn('facebook')} /> */}
              </div> </> :
              <div className='flex flex-col gap-4'>
                {loading && <Spin className='mt-4' />}
                <Text className="text-center mt-4 text-xl" strong>Verify your identity</Text>
                <Text className="text-center text-[#50555C] text-normal" >Please enter the 6-character code sent to {form.getFieldValue('email')} to continue.</Text>
                <OtpInput
                  value={otp}
                  onChange={async (val) => {
                    setOtp(val)
                    if (val.length === 6) {
                      onVerifyOtp(val)
                    }
                  }}
                  numInputs={6}
                  renderSeparator={<span className='mx-2'> </span>}
                  renderInput={(props) => {
                    const { style } = props

                    style.border = '1px solid #D9D9D9'
                    style.width = '3rem'
                    style.height = '3rem'
                    style.borderRadius = '0.5rem'

                    return <input {...props} />
                  }}
                />
                <div className='flex items-center justify-center h-full'>
                  <Text className='text-[#50555C]'>
                    Didn’t receive a code?
                    <Button
                      onClick={async () => {
                        await generateOtp({ variables: { input: { email: form.getFieldValue('email') } } });
                        message.success('OTP sent successfully')
                      }}
                      className='m-0 pl-2 p-0'
                      type='link'
                    >
                      Resend
                    </Button>
                  </Text>
                </div>

              </div>}
          </Card>
        </div>
      </div>
    </AuthenticationLayout>
  );
};

export default Login;
