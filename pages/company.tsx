import { HomeOutlined } from '@ant-design/icons';
import ImageUploadInput from '@components/forms/FormsInput/ImageUploadInput';
import useModal from '@components/hooks/useModal';
import * as Gql from '@graphql';
import { onError } from '@utils/error';
import { Button, Card, Form, Input, Spin, Tag } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import Image from 'next/image';
import { useRouter } from 'next/router';
import React, { useRef } from 'react';

const OnBoarding: React.FC = () => {
  const router = useRouter();
  const [form] = useForm();
  const inputRef = useRef<any>(null);

  const { data, loading: fetching } = Gql.useGetUserMeQuery({
    fetchPolicy: 'no-cache'
  });

  const [createCompanyMutation, { loading, error }] = Gql.useCreateCompanyMutation({
    onCompleted: () => {
      form.resetFields();
      window.location.href = '/';
    },
    onError: onError as any
  });

  const sortedCompanies =
    data?.getUserMe.companies?.edges?.sort((a, b) => {
      if (!a.node.name || !b.node.name) return -1;
      if (a.node.id === data.getUserMe.companyId) return -1;
      if (b.node.id === data.getUserMe.companyId) return 1;
      return a.node.name.localeCompare(b.node.name);
    }) || [];

  const [switchCompany, { loading: switchingCompany }] = Gql.useSwitchCompanyMutation({
    onCompleted: () => {
      localStorage.removeItem('ProjectId');
      localStorage.removeItem('ProjectUserRole');
      window.location.href = '/';
    },
    onError: onError as any
  });

  const onFinish = (values: any) => {
    createCompanyMutation({
      variables: {
        input: { name: values.companyName, logoUrl: values.logoUrl }
      }
    });
  };

  const [modal, showAddCompanyModal] = useModal({
    onCancel: () => form.resetFields(),
    title: 'Create New Company',
    content:
      (setTimeout(() => {
        inputRef?.current?.focus();
      }, 100),
        (
          <Form layout="vertical" form={form} onFinish={onFinish} requiredMark={false}>
            <Form.Item className="text-gray90" label="Company Logo" name="logoUrl" rules={[{ required: true }]}>
              <ImageUploadInput />
            </Form.Item>

            <Form.Item className="text-gray90" label="Company Name" name="companyName" rules={[{ required: true }]}>
              <Input className="rounded-lg h-10 w-70" />
            </Form.Item>

            <Button
              htmlType="submit"
              type="primary"
              className="rounded-lg h-10 w-full font-medium"
              loading={loading}
              disabled={loading}
            >
              Create
            </Button>
          </Form>
        ))
  });

  if (fetching) return null;

  return (
    <div
      className="h-screen flex flex-col justify-center items-center bg-no-repeat bg-center bg-fixed bg-cover"
      style={{
        backgroundImage: sortedCompanies?.length > 0 ? 'url(/assets/onboarding-bg.png)' : ''
      }}
    >
      <div className=" cursor-pointer" onClick={() => router.push('/')}>
        <div className="flex items-center">
          <Image src="/assets/new-logo.svg" width={200} height={70} alt="Logo" />
          {/* <div className="ml-3 tracking-widest text-xl text-binaBlue">BINA</div> */}
        </div>
      </div>

      <div className="flex flex-col gap-y-3 mx-5">
        {sortedCompanies?.length > 0 && (
          <Spin spinning={fetching || switchingCompany}>
            <Card
              title="Select Company"
              // style={{ width: 400 }}
              className="overflow-hidden max-h-full"
              bodyStyle={{
                height: 350,
                width: 350,
                overflowY: 'auto'
              }}
            >
              <div className="flex flex-col gap-y-6">
                {sortedCompanies?.map(company => (
                  <div className="flex items-center gap-6 justify-between" key={company.node.id}>
                    <div>
                      {company.node.name}
                      {company.node.ownerId === data?.getUserMe?.id && (
                        <Tag color="green" className="ml-2">
                          Owner
                        </Tag>
                      )}
                    </div>
                    <div></div>
                    <Button
                      type="primary"
                      size="small"
                      disabled={company.node.id === data?.getUserMe?.companyId || switchingCompany}
                      onClick={() => {
                        switchCompany({
                          variables: {
                            companyId: parseInt(company.node.id)
                          }
                        });
                      }}
                      loading={switchingCompany}
                    >
                      {company.node.id !== data?.getUserMe.companyId ? 'Select' : 'Selected'}
                    </Button>
                  </div>
                ))}
              </div>
            </Card>
          </Spin>
        )}
        <Button type="primary" onClick={showAddCompanyModal} className="mt-4">
          Create New Company
        </Button>
        <Button
          type="default"
          className="bg-white"
          onClick={() => {
            router.push('/');
          }}
        >
          <HomeOutlined />
          Back To Main
        </Button>
      </div>

      {modal}
    </div>
  );
};

export default OnBoarding;
