import * as Gql from '@graphql';
import React, { useState } from 'react';

type Props = {}

const Query = (props: Props) => {

  //? this files contains all the queries and mutations for the schedule page

  const [status, setStatus] = useState<Gql.ScheduleStatus>(Gql.ScheduleStatus.Pending)
  const [id, setId] = useState<string>('1')

  //? get all schedules by status
  const [getScheduleByStatus, { data, loading, error }] = Gql.useGetSchedulesLazyQuery({
    variables: {
      filter: {
        status: {
          eq: status
        }
      }
    }
  });

  //? get schedule by id
  const [getScheduleById, { data: schedule, loading: scheduleLoading, error: scheduleError }] = Gql.useGetScheduleLazyQuery({
    variables: {
      id
    }
  });

  //? update schedule by id
  const [updateScheduleById, { data: updateSchedule, loading: updateScheduleLoading, error: updateScheduleError }] = Gql.useUpdateOneScheduleMutation({
    variables: {
      input: {
        id,
        update: {
          //? update the proposed status
          proposedStatus: Gql.ScheduleStatus.Pending,
          //? update the linked document
          documents: [{ id: 1 }, { id: 2 }],
          //? the propose action
          proposeAction: Gql.ProposeAction.Validate
        }
      }
    }
  });

  const [getScheduleComment, { }] = Gql.useGetScheduleCommentsLazyQuery({
    variables: {
      filter: {
        scheduleId: {
          eq: '1'
        }
      }
    }
  });

  const [createScheduleComment, { }] = Gql.useCreateScheduleCommentMutation({
    variables: {
      input: {
        message: 'test',
        scheduleId: '1',
        mentions: ['1', '2']
      }
    }
  });

  return (
    <></>
  )
}

export default Query