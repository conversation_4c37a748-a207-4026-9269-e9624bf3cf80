import { ImportOutlined, UsergroupAddOutlined } from '@ant-design/icons';
import { Icon } from '@commons/Icon';
import ScheduleCreateModal from '@components/schedule/ScheduleCreateModal';
import ThreeDotsDropDown from '@components/ThreeDotsDropDown';
import * as Gql from '@graphql';
import { nameAlias } from '@utils/app.utils';
import { Avatar, Badge, Button, Card, Col, Empty, message, Modal, Row, Space, Switch } from 'antd';
import _ from 'lodash';
import moment from 'moment';
import router from 'next/router';
import React, { useEffect, useRef, useState } from 'react';

const Schedules = () => {
  const createScheduleRef = useRef<any>(null);
  const [projectUserRole, setProjectUserRole] = useState<Gql.ProjectUserRoleType>();
  const [selectedSchedule, setSelectedSchedule] = useState<Gql.ProjectSchedule>();
  const [selectedAction, setSelectedAction] = useState('');

  const [projectSchedules, { data: schedulesData, loading: schedulesLoading, refetch, error }] =
    Gql.useGetProjectSchedulesLazyQuery({
      fetchPolicy: 'cache-and-network'
    });

  const schedule = schedulesData?.getProjectSchedules;

  useEffect(() => {
    projectSchedules();
    setProjectUserRole(localStorage.getItem('ProjectUserRole') as Gql.ProjectUserRoleType);
  }, []);

  const [updateTracking] = Gql.useUpdateOneProjectScheduleMutation({
    onCompleted: () => { refetch(); },
    onError: (err: any) => {
      message.error(err.message);

    }
  });

  const [deleteProjectSchedule] = Gql.useDeleteOneProjectScheduleMutation({
    onCompleted: () => {
      message.success('Deleted a schedule successfully');
      refetch();
    },
    onError: (err: any) => {
      message.error(err.message);
    }
  });

  return (
    <>
      <div className="absolute overflow-auto h-full w-full">
        <Col className="pt-5 pl-5 pr-5 pb-2">
          <Row className="flex justify-between mb-6">
            <h2 className="my-4">Schedules</h2>
            {(projectUserRole === Gql.ProjectUserRoleType.ProjectOwner ||
              projectUserRole === Gql.ProjectUserRoleType.CloudCoordinator) && (
                <Button
                  className="ml-2 my-4 h-[40px]"
                  type="primary"
                  icon={<ImportOutlined />}
                  onClick={() => {
                    setSelectedAction('create');
                    createScheduleRef?.current?.openModal();
                  }}
                >
                  <Space className="ml-2"> Create Schedule</Space>
                </Button>
              )}
          </Row>
          {!schedulesLoading && schedule?.length == 0 ? (
            <Empty />
          ) : (
            <Row>
              {' '}
              {schedule?.map((sched: any, index: number) => {
                return (
                  <Card key={index} style={{ width: 400, cursor: 'pointer', margin: 15 }} bodyStyle={{ padding: '0' }}>
                    <Row>
                      <div className="w-1/2 text-md p-4" style={borderStyle}>
                        {/* {getStatus('Delay')} */}
                        Revision {sched?.revision || 0}
                      </div>
                      <div className="w-1/2 text-md p-4" style={borderStyle}>
                        {getStatus('OnHold')}
                        <Badge
                          count={sched.holdStatusCount == 0 ? '-' : sched.holdStatusCount}
                          color="#1EA8E0"
                          overflowCount={9999}
                          className="flex-end"
                        />
                      </div>
                    </Row>
                    <Row>
                      <div className="w-1/2 text-md p-4" style={borderStyle}>
                        {getStatus('InProgress')}
                        <Badge
                          count={sched.startVarianceCount == 0 ? '-' : sched.startVarianceCount}
                          color="#F29100"
                          overflowCount={9999}
                          className="flex-end"
                        />
                      </div>
                      <div className="w-1/2 text-md p-4" style={borderStyle}>
                        {getStatus('Completed')}
                        <Badge
                          count={sched.finishVariance == 0 ? '-' : sched.finishVariance}
                          color="#D20101"
                          overflowCount={9999}
                          className="flex-end"
                        />
                      </div>
                    </Row>
                    <Col
                      className="p-4"
                      style={{ border: '1px solid', borderColor: '#DCDCDC', borderRadius: '0px' }}
                      onClick={e => {
                        router.push({
                          ...router.query,
                          pathname: '/schedules/schedule',
                          query: { projectScheduleId: sched?.id }
                        });
                      }}
                    >
                      <Row justify={'space-between'}>
                        <Col className="w-5/6">
                          <p className="text-md">{sched?.name}</p>
                          <div className="flex gap-2 items-center">
                            <p className="text-sm text-gray70">
                              Updated on{' '}
                              {moment(sched?.updatedAt).format('DD-MMMM-YYYY').replace(/-/g, ' ') +
                                ' at ' +
                                moment(sched?.updatedAt).format('hh:mm A') +
                                (sched?.updatedByUser ? ' by ' : '.')}
                            </p>
                            {sched?.updatedByUser ? (
                              <div>
                                <Avatar src={sched?.updatedByUser?.avatar} className="mr-1 avatar-default" >{nameAlias(sched?.updatedByUser?.name)}</Avatar>
                              </div>
                            ) : (
                              <div style={{ height: '32px', width: '32px' }} /> // to keep the same height as the avatar
                            )}
                          </div>
                        </Col>

                        {(projectUserRole === Gql.ProjectUserRoleType.ProjectOwner ||
                          projectUserRole === Gql.ProjectUserRoleType.CloudCoordinator) && (
                            <Col
                              className="absolute right-5"
                              style={{ textAlign: 'end' }}
                              onClick={e => {
                                e.stopPropagation();
                              }}
                            >
                              <ThreeDotsDropDown
                                onClick={(e: any) => {
                                  if (e.key === 'Rename') {
                                    setSelectedAction('update');
                                    setSelectedSchedule(sched);
                                    createScheduleRef?.current?.openModal();
                                  }
                                  if (e.key === 'Delete') {
                                    Modal.confirm({
                                      title: `Delete Schedule ${sched.name}?`,
                                      content: `Are you sure you want to delete this Schedule? Deleted Schedules cannot be restored.`,
                                      okText: 'Delete',
                                      cancelText: 'Cancel',
                                      onOk: async () => {
                                        try {
                                          deleteProjectSchedule({ variables: { id: sched.id } });
                                        } catch (error) {
                                          console.log(error);
                                        }
                                      }
                                    });
                                  }
                                }}
                                items={newMenuProps(projectUserRole)}
                              />
                            </Col>
                          )}
                      </Row>

                      <Row
                        className="mt-6"
                        style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}
                      >
                        <div className='flex-1'>
                          <UsergroupAddOutlined style={{ marginRight: '2px' }} />
                          <span>{sched.userCount ?? 0}</span>
                        </div>
                        <div onClick={(e) => e.stopPropagation()}>
                          <Switch checked={sched.isNotify} onClick={(val) => {
                            updateTracking({
                              variables: {
                                input: {
                                  id: _.toString(sched.id),
                                  update: {
                                    isNotify: val
                                  }
                                }
                              }
                            });
                          }} />
                        </div>
                      </Row>
                    </Col>
                  </Card>
                );
              })}
            </Row>
          )}
        </Col>
        <ScheduleCreateModal
          ref={createScheduleRef}
          onSaved={() => {
            refetch();
            setSelectedSchedule(undefined);
          }}
          schedule={selectedSchedule}
          action={selectedAction}
        />
      </div>
    </>
  );
};

const newMenuProps = (projectUserRole: Gql.ProjectUserRoleType) => {
  let items = [];

  items.push({
    label: 'Rename Schedule',
    key: 'Rename'
  });

  if (
    projectUserRole === Gql.ProjectUserRoleType.ProjectOwner ||
    projectUserRole === Gql.ProjectUserRoleType.CloudCoordinator
  ) {
    items.push({
      label: 'Delete Schedule',
      key: 'Delete'
    });
  }

  return items;
};

const borderStyle = {
  border: '1px solid',
  borderColor: '#D8D8D8',
  backgroundColor: '#E9E9E9',
  justifyContent: 'space-between',
  display: 'flex',
  alignItems: 'center'
};

const getStatus = (type: string) => _.get(status, type) ?? '';

const status = {
  Delay: (
    <Space>
      <Icon name="schedule-delay" className="mt-1 mr-1" />
      <span id="status">Delay</span>
    </Space>
  ),
  OnHold: (
    <Space style={{ alignItems: 'middle' }}>
      <Icon name="schedule-inProgress" className="mt-1 mr-1" />
      <p id="status">On Hold</p>
    </Space>
  ),
  InProgress: (
    <Space style={{ alignItems: 'middle' }}>
      <Icon name="schedule-onHold" className="mt-1 mr-1" />
      <p id="status">Start Delay</p>
    </Space>
  ),
  Completed: (
    <Space style={{ alignItems: 'middle' }}>
      <Icon name="schedule-delay" className="mt-1 mr-1" />
      <p id="status">Finish Delay</p>
    </Space>
  )
};

Schedules.auth = true;

export default Schedules;
