import { ImportOutlined, SearchOutlined } from '@ant-design/icons';
import { Icon } from '@commons';
import ChatWidget from '@components/ChatWidget';
import { useColumns } from '@components/hooks/useScheduleColumn';
import GanttUploadModal from '@components/schedule/GanttUploadModal';
import ScheduleHeader from '@components/schedule/Header';
import ScheduleTabLayout from '@components/schedule/ScheduleTabLayout';
import { ProjectAccess } from '@constants/subscription';
import * as Gql from '@graphql';
import { isAllowed } from '@lib/helper';
import { onError } from '@utils/error';
import { Button, Col, Input, message, Modal, Popover, Radio, Row, Space, Spin, Switch, Typography } from 'antd';
import _, { set } from 'lodash';
import moment from 'moment';
import { useRouter } from 'next/router';
import { AppLayoutContext } from 'pages/_app';
import { useContext, useEffect, useRef, useState } from 'react';
import ShowHideColumn from './ShowHideColumn';

const Schedule = () => {
  const Gantt = typeof window !== 'undefined' ? require('@components/Gantt/Gantt').default : null;
  const collapseAll = typeof window !== 'undefined' ? require('@components/Gantt/Gantt').collapseAll : null;
  const expandAll = typeof window !== 'undefined' ? require('@components/Gantt/Gantt').expandAll : null;
  const exportToPDF = typeof window !== 'undefined' ? require('@components/Gantt/Gantt').exportToPDF : null;
  const filterGantt = typeof window !== 'undefined' ? require('@components/Gantt/Gantt').filterGantt : null;
  const allowActivity = typeof window !== 'undefined' ? require('@components/Gantt/Gantt').allowActivity : null;
  const allowTracking = typeof window !== 'undefined' ? require('@components/Gantt/Gantt').allowTracking : null;
  const setTimelineScale = typeof window !== 'undefined' ? require('@components/Gantt/Gantt').setTimelineScale : null;
  const showCriticalPath = typeof window !== 'undefined' ? require('@components/Gantt/Gantt').showCriticalPath : null;
  const upload = typeof window !== 'undefined' ? require('@components/Gantt/Gantt').upload : null;
  const renderGantt = typeof window !== 'undefined' ? require('@components/Gantt/Gantt').renderGantt : null;
  const parse = typeof window !== 'undefined' ? require('@components/Gantt/Gantt').parse : null;
  const parseDB = typeof window !== 'undefined' ? require('@components/Gantt/Gantt').parseDB : null;
  const columnState = typeof window !== 'undefined' ? require('@components/Gantt/Gantt').handleColumnChange : null;
  const dateState = typeof window !== 'undefined' ? require('@components/Gantt/Gantt').handleDateChange : null;
  const selectTask = typeof window !== 'undefined' ? require('@components/Gantt/Gantt').selectTask : null;
  const isTaskExist = typeof window !== 'undefined' ? require('@components/Gantt/Gantt').isTaskExist : null;
  const getChildren = typeof window !== 'undefined' ? require('@components/Gantt/Gantt').getChildren : null;
  const holidayCount = typeof window !== 'undefined' ? require('@components/Gantt/Gantt').holidayCount : null;
  const showHideColumns = typeof window !== 'undefined' ? require('@components/Gantt/Gantt').showHideColumns : null;
  const getProjectSchedulesID =
    typeof window !== 'undefined' ? require('@components/Gantt/Gantt').getProjectSchedulesID : null;
  const router = useRouter();
  const projectScheduleId = router.query.projectScheduleId as string;
  const suid = router.query.suid;
  const pushId = router.query.pushId;
  const folderId = router.query.folderId as string;
  const [projectId, setProjectId] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const ganttUploadModalRef = useRef<any>(null);
  const [isCreatingNew, setIsCreatingNew] = useState<boolean>(false);
  const [hideDates, setHideDates] = useState(false);
  const [hideColumns, setHideColumns] = useState(false);
  const [projectUserRole, setProjectUserRole] = useState<Gql.ProjectUserRoleType>();
  const [folderIds, setFolderIds] = useState(['0']);
  const [hasFetched, setHasFetched] = useState(false);
  const [tableColumns, toggleColumn] = useColumns('schedule');
  const [scheduleRole, setScheduleRole] = useState<any>('');
  const ganttContainerRef = useRef<HTMLDivElement>(null);

  const { userData: userMeData } = useContext(AppLayoutContext);

  useEffect(() => {
    showHideColumns(tableColumns);
  }, [tableColumns]);

  const companySubscriptions = Gql.useCompanySubscriptionsQuery({
    variables: {
      sorting: {
        direction: Gql.SortDirection.Desc,
        field: Gql.CompanySubscriptionSortFields.SubscriptionEndDate
      },
      paging: {
        limit: 1
      }
    }
  });

  const { data: proSchedData, refetch: refetchProSched } = Gql.useProjectScheduleQuery({
    variables: {
      id: projectScheduleId ?? 0
    }
  });

  const { data: projectUser, refetch: refetchProjectUser } = Gql.useGetProjectUserMeQuery({
    onError: onError,
    onCompleted: res => {
      setScheduleRole(res?.getProjectUserMe?.scheduleRole);
    }
  });

  const [processVectorization] = Gql.useProcessVectorizationScheduleLazyQuery();

  useEffect(() => {
    allowActivity(isAllowed(companySubscriptions, ProjectAccess.SCHEDULE_ACTIVITY));
  }, [companySubscriptions]);

  useEffect(() => {
    allowTracking(proSchedData?.projectSchedule?.isScheduleTracked);
  }, [proSchedData]);

  const handleDateSwitchChange = (checked: any) => {
    setHideDates(checked);
    if (!checked) {
      setHideColumns(false); // Enable "Hide Columns" when "Hide Dates" is unchecked
    }
  };

  const handleColumnSwitchChange = (checked: any) => {
    setHideColumns(checked);
    if (!checked) {
      setHideDates(false); // Enable "Hide Dates" when "Hide Columns" is unchecked
    }
  };

  const [createSchedulesTasks, { loading: creatingSchedules }] = Gql.useCreateManySchedulesMutation({
    onCompleted: () => {
      message.success('Added tasks with new CPM file contents.');
    },
    onError: onError as any
  });

  const [useUpdateRetainValue, { loading: updateRetainValueLoading }] = Gql.useUpdateRetainValueMutation({
    onCompleted: () => {},
    onError: onError as any
  });

  const [createSchedulesLinks, { loading: creatingSchedulesLink }] = Gql.useCreateManySchedulesLinksMutation({
    onCompleted: () => {
      console.log('Added links with new CPM file contents.');
      setLoading(false);
    },
    onError: onError as any
  });

  const [getDynamicSchedules, { data }] = Gql.useSchedulesLazyQuery({
    variables: {
      filter: {
        projectId: {
          eq: projectId
        },
        projectScheduleId: {
          eq: projectScheduleId
        },
        ...(suid ? {} : { predecessors: { in: folderIds } })
      },
      paging: {
        limit: 99999
      }
    },
    fetchPolicy: 'network-only',
    onError: onError as any
  });

  const [getHolidays, { data: holiday }] = Gql.useProjectScheduleLazyQuery({
    variables: {
      id: projectScheduleId
    },
    fetchPolicy: 'network-only',
    onError: onError as any
  });

  const [getAllSchedules] = Gql.useGetCpmSchedulesLazyQuery({
    variables: {
      filter: {
        projectId: {
          eq: projectId
        },
        projectScheduleId: {
          eq: projectScheduleId
        }
      },
      paging: {
        limit: 99999
      }
    },
    fetchPolicy: 'cache-and-network',
    onError: onError as any
  });

  const [getRetainValueSchedule] = Gql.useGetRetainValueScheduleLazyQuery();

  const [getSchedulesLinks] = Gql.useGetSchedulesLinksLazyQuery({
    variables: {
      filter: {
        projectId: {
          eq: projectId
        },
        projectScheduleId: {
          eq: projectScheduleId
        }
      },
      paging: {
        limit: 99999
      }
    },
    fetchPolicy: 'cache-and-network',
    onError: onError as any
  });

  const [deleteAllSchedulesTasks] = Gql.useDeleteManySchedulesMutation({
    onError: onError as any
  });

  const [deleteAllSchedulesLinks] = Gql.useDeleteManySchedulesLinksMutation({
    onCompleted: () => {
      console.log('Removed all previous scheduled links.');
    },
    onError: onError as any
  });

  const [updateOneSchedule] = Gql.useUpdateScheduleBySuidMutation({
    onCompleted: () => {
      message.success('Task is now added to Activity Tab.');
    },
    onError: onError as any
  });

  const [updateTracking] = Gql.useUpdateOneProjectScheduleMutation({
    onCompleted: () => {},
    onError: (err: any) => {
      message.error(err.message);
    }
  });

  // updateOneProjectSchedule mutation
  const [updateOneProjectSchedule] = Gql.useUpdateOneProjectScheduleMutation({
    onError: onError as any
  });

  useEffect(() => {
    getHolidays();
    setProjectId(localStorage.getItem('ProjectId'));
    setTimelineScale('Year');
    setProjectUserRole(localStorage.getItem('ProjectUserRole') as Gql.ProjectUserRoleType);
  }, []);

  useEffect(() => {
    if (!folderIds.includes(folderId) && folderId !== undefined) {
      setFolderIds([...folderIds, folderId]);
    }
  }, [folderId]);

  useEffect(() => {
    fetchDynamicDBData().then(dynamicData => {
      parseDB(dynamicData.tasks, dynamicData.links, true);
    });
  }, [folderIds]);

  useEffect(() => {
    if (pushId) {
      Modal.confirm({
        title: 'Pushing Task Ahead',
        content: 'Are you sure you want to push this task ahead?',
        okText: 'Yes',
        cancelText: 'No',
        onOk: async () => {
          await router.replace({
            query: _.omit(router.query, 'pushId')
          });
          await updateOneSchedule({
            variables: {
              input: {
                suid: pushId as string,
                isTaskPushed: true,
                status: Gql.ScheduleStatus.Upcoming,
                projectScheduleId: projectScheduleId
              }
            }
          });
        },
        onCancel: () => {
          router.replace({
            query: _.omit(router.query, 'pushId')
          });
        }
      });
    }
  }, [pushId]);

  const { data: projectData, refetch } = Gql.useGetProjectQuery({
    variables: { id: _.toString(projectId) }
  });
  const project = projectData?.project;

  async function fetchDynamicDBData() {
    const [getSchedulesResult, getSchedulesLinksResult] = await Promise.all([
      getDynamicSchedules(),
      getSchedulesLinks()
    ]);
    const tasks = getSchedulesResult.data?.schedules.nodes;
    const links = getSchedulesLinksResult.data?.schedulesLinks.nodes;

    const data = { tasks, links };
    return data;
  }

  async function fetchAllDBData() {
    const [getSchedulesResult, getSchedulesLinksResult] = await Promise.all([getAllSchedules(), getSchedulesLinks()]);
    const tasks = getSchedulesResult.data?.schedules.nodes;
    const links = getSchedulesLinksResult.data?.schedulesLinks.nodes;

    const data = { tasks, links };
    return data;
  }

  useEffect(() => {
    isTaskExist().then((res: any) => {
      if (res === false) {
        fetchDynamicDBData()
          .then(dynamicData => {
            if (dynamicData.tasks && dynamicData.tasks.length === 0) {
              message.warning('No schedule found for this project. Please import a new schedule.');
              setLoading(false);
            } else if (dynamicData.tasks && dynamicData.tasks.length > 0) {
              parseDB(dynamicData.tasks, dynamicData.links, false);
            }
          })
          .finally(() => {
            setLoading(false);
            if (suid != undefined || suid != null) {
              expandAll();
              selectTask(suid);
              setHasFetched(true);
            }
          });
      } else {
        setLoading(false);
        return;
      }
    });
  }, []);

  const saveFiles = async (data: any, holiday: any, errorLog: any) => {
    if (data && data.data) {
      const { data: ganttDatas } = data;
      const { links: ganttLinks } = data;

      const ganttDataList = ganttDatas.map((ganttData: any) => {
        const childrenArray = getChildren(ganttData.id);
        const children = JSON.stringify(childrenArray);
        let type;
        if (childrenArray.length > 0) {
          type = 'Project';
        } else if (ganttData?.$raw.Duration.toString() === '0') {
          type = 'Milestone';
        } else {
          type = 'Task';
        }
        return {
          suid: ganttData.id ?? '',
          projectScheduleId: projectScheduleId,
          taskMode: ganttData.Task_Mode ?? 'Auto Scheduled',
          name: ganttData.text,
          isCritical: ganttData?.$custom_data?.Critical != null && ganttData.$custom_data.Critical.toString() === '1' ? true : false,
          baselineDuration:
            ganttData?.$custom_data?.Baseline[0]?.Duration?.toString?.() ?? ganttData?.$raw?.Duration.toString() ?? '',
          baselineStart:
            ganttData?.$custom_data?.Baseline[0]?.Start?.toString?.() ?? ganttData?.$raw?.Start.toString() ?? '',
          baselineFinish:
            ganttData?.$custom_data?.Baseline[0]?.Finish?.toString?.() ?? ganttData?.$raw?.Finish.toString() ?? '',
          percentComplete: ganttData?.progress?.toString?.(),
          actualStart: ganttData?.$raw?.ActualStart?.toString?.() ?? '',
          actualFinish: ganttData?.$raw?.ActualFinish?.toString?.() ?? '',
          outlineLevel: _.toString?.(ganttData?.$level) ?? '',
          outlineNumber: ganttData?.outline_number ?? '',
          predecessors: ganttData?.parent ?? '',
          proposedPercentComplete: '',
          percentWorkComplete: ganttData?.percent_work_complete ?? '',
          wbs: ganttData.$wbs ?? '',
          children: children,
          type: type,
          startVariance: parseFloat(ganttData?.$custom_data?.StartVariance) ?? 0,
          finishVariance: parseFloat(ganttData?.$custom_data?.FinishVariance) ?? 0
        };
      });

      const ganttDataLinks = ganttLinks.map((ganttLink: any) => {
        return {
          suid: ganttLink.id,
          projectScheduleId: projectScheduleId,
          lag_unit: _.toString(ganttLink.lag_unit),
          lag: _.toNumber(ganttLink.lag),
          source: _.toNumber(ganttLink.source),
          target: _.toNumber(ganttLink.target),
          type: _.toNumber(ganttLink.type)
        };
      });

      if (ganttDataList.length === 0) return;

      const onHold = await getRetainValueSchedule({
        variables: {
          filter: {
            projectScheduleId: { eq: projectScheduleId }
          }
        }
      });

      const onHoldTask = onHold.data?.getRetainValueSchedule?.nodes.map((x: any) => ({
        id: parseInt(x.id),
        suid: x.suid,
        notes: x.notes,
        comments: JSON.stringify(x.comments.nodes),
        isPriority: x.isPriority,
        status: x.status,
        proposedStatus: x.proposedStatus
      }));

      return await deleteAllSchedulesTasks({
        variables: {
          input: {
            filter: {
              projectId: { eq: _.toString(projectId) },
              projectScheduleId: { eq: projectScheduleId }
            }
          }
        }
      })
        .then(async () => {
          await deleteAllSchedulesLinks({
            variables: {
              input: {
                filter: {
                  projectId: { eq: _.toString(projectId) },
                  projectScheduleId: { eq: projectScheduleId }
                }
              }
            }
          });
        })
        .then(async () => {
          await createSchedulesTasks({
            variables: {
              input: {
                schedules: ganttDataList
              }
            }
          });
        })
        .then(async () => {
          
          if (ganttDataLinks?.length > 0) {
            await createSchedulesLinks({
              variables: {
                input: {
                  schedulesLinks: ganttDataLinks
                }
              }
            });
          } else {
            setLoading(false);
          }
        })
        .then(async () => {
          await updateOneProjectSchedule({
            variables: {
              input: {
                id: _.toString(projectScheduleId),
                update: {
                  revision: (proSchedData?.projectSchedule?.revision ?? 0) + 1
                }
              }
            }
          });
        })

        // await updateOneProjectSchedule({
        //   variables: {
        //     input: {
        //       id: _.toString(projectScheduleId),
        //       update: {
        //         revision: proSchedData?.projectSchedule?.revision ?? 0 + 1
        //       }
        //     }
        //   }
        // });
        // })
        // .then(async () => {
        //   onHoldTask && onHoldTask?.length > 0 && await updateManySchedules({
        //     variables: {
        //       input: {
        //         filter: {
        //           suid: {
        //             in: onHoldTask
        //           },
        //           projectId: {
        //             eq: _.toString(projectId)
        //           },
        //           projectScheduleId: {
        //             eq: projectScheduleId
        //           }
        //         },
        //         update: {
        //           status: Gql.ScheduleStatus.Hold,
        //           isPriority: true,
        //         }
        //       }
        //     }
        //   });
        // })
        .then(async () => {
          await updateTracking({
            variables: {
              input: {
                id: _.toString(projectScheduleId),
                update: {
                  holidays: JSON.stringify(holiday),
                  errorLog: JSON.stringify(errorLog)
                }
              }
            }
          });
        })
        .finally(async () => {
          onHoldTask &&
            onHoldTask?.length > 0 &&
            onHoldTask.forEach(async (task: any) => {
              await useUpdateRetainValue({
                variables: {
                  input: {
                    id: task.id,
                    suid: task.suid,
                    isPriority: task?.isPriority,
                    notes: task.notes,
                    status: task?.status,
                    projectScheduleId: parseInt(projectScheduleId),
                    comments: task.comments,
                    proposedStatus: task.proposedStatus
                  }
                }
              });
            });
            
          await processVectorization({
            variables: {
              input: {
                scheduleId: parseInt(projectScheduleId)
              }
            }
          })
          
          setTimeout(() => {
            window.location.reload();
          }, 2000);
        })
        .catch(err => {
          onError(err);
        });
    }
    return setIsCreatingNew(false);
  };

  function getFormattedDate(date: any, type: any) {
    if (type === 'baseline') {
      const parsedDate = moment(date);

      if (!parsedDate.isValid()) {
        // Handle invalid date format
        return date;
      }

      const isoStringInLocalTimezone = parsedDate.format('YYYY-MM-DDTHH:mm:ss.SSS');
      return isoStringInLocalTimezone;
    }
    return date;
  }

  return (
    <>
      <Col className="pt-5 pl-5 pr-5 pb-2">
        <Row className="flex justify-between">
          <ScheduleHeader title={proSchedData?.projectSchedule?.name} />
          {loading ? (
            <>
              <Spin style={{ marginTop: 'auto', marginBottom: 'auto', marginRight: '10px' }} size="large" />
              <Space className="ml-2"> </Space>
            </>
          ) : null}
          {(projectUserRole === Gql.ProjectUserRoleType.ProjectOwner ||
            projectUserRole === Gql.ProjectUserRoleType.CloudCoordinator ||
            scheduleRole === Gql.ScheduleUserRole.Collaborator) && (
              <Button
                className="ml-2"
                type="primary"
                icon={<ImportOutlined />}
                onClick={() => {
                  ganttUploadModalRef?.current?.openModal();
                  setIsCreatingNew(true);
                }}
                loading={loading}
              >
                <Space className="ml-2"> Import Schedule</Space>
              </Button>
            )}
        </Row>
        <Row className="flex justify-end">
          {/* {errorLog && errorLog.length > 0 && (
            <Button>
              {`${errorLog.join(', ')} ${
                errorLog.length > 1 ? 'are' : 'is'
              } not included in the Microsoft Project file`}
            </Button>
          )} */}

          {/* <div className="Switch" style={{ marginTop: 'auto', marginBottom: 'auto', marginRight: 4, marginLeft: 4 }}>
            <Switch
              checkedChildren="Hide Dates"
              unCheckedChildren="Hide Dates"
              style={{ marginTop: 'auto', marginBottom: 'auto' }}
              onChange={res => {
                handleDateSwitchChange(res);
                dateState(res);
              }}
              disabled={loading || hideColumns}
            />
          </div>
          <div className="Switch" style={{ marginTop: 'auto', marginBottom: 'auto', marginRight: 16, marginLeft: 4 }}>
            <Switch
              checkedChildren="Hide Columns"
              unCheckedChildren="Hide Columns"
              style={{ marginTop: 'auto', marginBottom: 'auto' }}
              onChange={res => {
                handleColumnSwitchChange(res);
                columnState(res);
              }}
              disabled={loading || hideDates}
            />
          </div> */}

          <Popover
            content={<ShowHideColumn tableColumns={tableColumns} onColumnToggle={name => toggleColumn(name)} />}
            trigger="hover"
          >
            <Button icon={<Icon name="columns" className="mr-1.5" />} className="mx-3 flex items-center justify-center">
              Column
            </Button>
          </Popover>
          {/* <div className="Switch" style={{ marginTop: 'auto', marginBottom: 'auto', marginRight: 4, marginLeft: 4 }}>
            <Switch
              checkedChildren="Expand Tasks"
              unCheckedChildren="Expand Tasks"
              style={{ marginTop: 'auto', marginBottom: 'auto' }}
              onChange={res => {
                res ? expandAll() : collapseAll();
              }}
              disabled={loading}
            />
          </div> */}
          {/* <div className="Switch" style={{ marginTop: 'auto', marginBottom: 'auto', marginRight: 16, marginLeft: 8 }}>
            <Switch
              checkedChildren="Critical Line"
              unCheckedChildren="Critical Line"
              style={{ marginTop: 'auto', marginBottom: 'auto' }}
              onChange={res => showCriticalPath(res)}
              disabled={getLoading}
            />
          </div> */}
          <Space className="mr-2">
            <Input
              className="group w-48 duration-500"
              prefix={<SearchOutlined />}
              placeholder={'Search Activity'}
              allowClear
              onChange={_.debounce(data => {
                if (data.target.value === '') {
                  filterGantt(data.target.value);
                  collapseAll();
                } else {
                  if (hasFetched === false) {
                    fetchAllDBData().then(async allData => {
                      await parseDB(allData.tasks, allData.links, true);
                      await filterGantt(data.target.value);
                      expandAll();
                      setHasFetched(true);
                    });
                  } else {
                    filterGantt(data.target.value);
                    expandAll();
                  }
                }
              }, 200)}
              style={{ height: '40px', lineHeight: '30px', padding: '0 10px' }}
              disabled={loading}
            />
          </Space>

          <Radio.Group
            value={''}
            onChange={e => {
              setTimelineScale(e.target.value);
              renderGantt();
            }}
            disabled={loading}
          >
            <Radio.Button value={'ZoomOut'} className="font-semibold">
              -
            </Radio.Button>
            <Radio.Button value={'Today'} className="font-semibold">
              Today
            </Radio.Button>
            <Radio.Button value={'ZoomIn'} className="font-semibold">
              +
            </Radio.Button>
          </Radio.Group>

          {/* <Button
            className="ml-2"
            icon={<ImportOutlined rotate={180} />}
            onClick={() => exportToPDF()}
            disabled={loading}
            loading={loading}
            color={'#000'}
          >
            <Space className="ml-2"> Export Schedule</Space>
          </Button> */}
        </Row>
      </Col>
      <div className="pl-2 pr-2 h-full">
        <div className="gantt-container bg-primary h-full " ref={ganttContainerRef}>
          <Gantt />
        </div>
      </div>
      <GanttUploadModal
        projectScheduleId={projectScheduleId}
        loading={loading}
        ref={ganttUploadModalRef}
        onSaved={async file => {
          message.info('Retrieving latest data...');
          await upload(file, handleLoadingStatus);
          await updateTracking({
            variables: {
              input: {
                id: _.toString(projectScheduleId),
                update: {
                  isScheduleTracked: true
                }
              }
            }
          });
        }}
        onCancel={() => {
          setIsCreatingNew(false);
        }}
        type={Gql.CategoryType.GanttChart}
      />
    </>
  );
  function handleLoadingStatus(status: string, data: any, holiday: any, errorLog: any) {
    // Handle the loading status update here
    if (status === 'loading') {
      setLoading(true);
    } else if (status === 'loaded' && project) {
      refetch();
      if (isCreatingNew === true) {
        saveFiles(data, holiday, errorLog);
      } else {
        setLoading(false);
      }
    }
  }
};

Schedule.auth = true;
Schedule.Layout = ScheduleTabLayout;

export default Schedule;
