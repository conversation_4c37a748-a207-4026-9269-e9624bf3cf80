import { CheckOutlined } from '@ant-design/icons';
import { Menu, Row } from 'antd';
import React from 'react';

type Props = {
  tableColumns: [];
  onColumnToggle: (name: string) => void;
};

type Column = {
  name: string;
  show: boolean;
  hideable?: boolean;
};

const ShowHideColumn = (props: Props) => {
  return (
    <div className="py-2 px-2 w-[230px]">
      <h3>Show/Hide</h3>
      {props.tableColumns
        ?.filter?.((column: Column) => column.hideable !== false)
        ?.map?.((column: Column) => (
          <div
            key={column.name}
            className="hover:bg-slate-50 hover:cursor-pointer flex justify-between items-center px-1"
            onClick={() => props.onColumnToggle(column.name)}
          >
            <p key={column.name} className="py-1.5">
              {column.name}
            </p>
            {column.show ? <CheckOutlined /> : null}
          </div>
        ))}
    </div>
  );
};

export default ShowHideColumn;
