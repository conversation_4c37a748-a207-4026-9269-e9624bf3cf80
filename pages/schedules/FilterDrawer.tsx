import { Icon } from '@commons';
import { Drawer } from '@commons/Drawer';
import useQueries from '@components/hooks/useQueries';
import GroupPicker from '@components/project/GroupPicker';
import * as Gql from '@graphql';
import { Button, DatePicker, Divider, DrawerProps, Form, Input, Row, Select, Space, message } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import _ from 'lodash';
import { useRouter } from 'next/router';
import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { ProjectGroupApiService } from 'src/api';

interface Props {
  onSubmit?: (name: string) => void;
  onClear?: () => void;
}

export interface DrawerRef extends DrawerProps {
  pushDrawer: (v?: any) => void;
}

const FilterDrawer = forwardRef<DrawerRef, Props>((props, ref) => {
  const drawerRef = useRef<any>(null);
  const [form] = Form.useForm();

  useImperativeHandle(ref, () => ({
    pushDrawer: () => {
      drawerRef?.current?.pushDrawer();
    }
  }));

  return (
    <Drawer
      // title="Filter"
      ref={drawerRef}
      {...props}
      width={500}
      extra={
        <div
          onClick={() => {
            drawerRef.current.closeDrawer();
          }}
          className="cursor-pointer"
        >
          {/* <Space>
            <Icon name="cross" />
          </Space> */}
        </div>
      }
      closable={false}
      className="scrollbar"
      onClose={() => { }}
    >
      <Form
        form={form}
        onFinish={values => {
          props.onSubmit?.(values);
        }}
      >
        <div>
          <div className="flex flex-nowrap items-center justify-between">
            <h3>Filter Tasks</h3>
            <Button
              htmlType="submit"
              onClick={() => {
                drawerRef?.current.closeDrawer();
              }}
            >
              Apply Filters
            </Button>
          </div>
          <Divider style={{ marginTop: '8px', marginBottom: '8px' }} />
          <div className="flex flex-nowrap items-center justify-end">
            <Button
              type="text"
              className="text-zinc-500"
              htmlType="submit"
              onClick={() => {
                form.resetFields();
                drawerRef?.current.closeDrawer();
                props.onClear?.();
              }}
            >
              Clear Filters
            </Button>
          </div>
        </div>
      </Form>
    </Drawer>
  );
});



FilterDrawer.displayName = 'Filter';
export default FilterDrawer;
