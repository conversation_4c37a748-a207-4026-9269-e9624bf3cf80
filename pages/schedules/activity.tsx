import { FilterOutlined } from '@ant-design/icons';
import { Icon } from '@commons';
import ActivityDrawerFilter, { ActivityDrawerFilterDrawerRef } from '@components/activity/activityDrawerFilter';
import useModal from '@components/hooks/useModal';
import useQueries from '@components/hooks/useQueries';
import PdfTronModal from '@components/pdf-tron/PdfTronModal';
import ScheduleHeader from '@components/schedule/Header';
import ScheduleTabLayout from '@components/schedule/ScheduleTabLayout';
import * as Gql from '@graphql';
import { manageDocuments } from '@utils/authority';
import {
  Badge,
  Button,
  Card,
  Col,
  Divider,
  Form,
  Input,
  Modal,
  Popover,
  Radio,
  Row,
  Select,
  Space,
  Spin,
  Table,
  Tooltip,
  message
} from 'antd';
import { useForm } from 'antd/lib/form/Form';
import _, { debounce } from 'lodash';
import router from 'next/router';

import ChatWidget from '@components/ChatWidget';
import { useColumns } from '@components/hooks/useScheduleColumn';
import ManagerPicker from '@components/schedule/ManagerPicker';
import DrawerActivityList from '@components/schedule/ScheduleCommentList';
import LinkedToModal from '@components/tasks/LinkedToModal';
import { getErrorMessage, onError } from '@utils/error';
import Paragraph from 'antd/lib/typography/Paragraph';
import moment from 'moment';
import { AppLayoutContext } from 'pages/_app';
import React, { useCallback, useContext, useEffect, useMemo, useRef, useState } from 'react';
import ShowHideColumn from './ShowHideColumn';

const Activity = () => {
  const [isOnlyMeFilter, setIsOnlyMeFilter] = useState(false);
  const [form] = useForm();
  const [noteForm] = useForm();
  const { purchaseId, scheduleId, projectScheduleId } = router.query as any;
  const { confirm } = Modal;
  const tableRef = useRef(null);
  const scrollRef = useRef(null);
  const scheduleActivityDrawerRef = useRef<any>(null);
  const [variant, setVariant] = useState<String>('StartDelay');
  const [percentSort, setPercentSort] = useState<any>({});
  const [openComment, setOpenComment] = useState(false);
  const [scheduleRole, setScheduleRole] = useState<any>('');
  const [getLoading, setGetLoading] = useState(false);

  const projectUserRole: any = localStorage.getItem('ProjectUserRole');
  const scheduleActivityDrawerFilterRef = useRef<ActivityDrawerFilterDrawerRef>(null);
  const { userData: userMeData, isFetchingUser: fetching } = useContext(AppLayoutContext);
  const { canAssign } = manageDocuments(projectUserRole);

  const [selectedSchedule, setSelectedSchedule] = useState<string | null>(null);

  const linkedToModalRef = useRef<any>(null);
  const pdfTronModalRef = useRef<any>(null);
  const [searchForm] = useForm();

  const [LinkedToDocumentId, setLinkedToDocumentId] = useState<string | null>(null);
  const binaAITabVisible = localStorage.getItem('showBinaAI') === 'true';

  const { queries, setFilter, setSorter } = useQueries<Gql.ScheduleFilter, Gql.ScheduleSort>({
    paging: { limit: 10, offset: 0 },
    usePagingParam: false
  });

  const { data: projectUser, refetch: refetchProjectUser } = Gql.useGetProjectUserMeQuery({
    onError: onError,
    onCompleted: res => {
      setScheduleRole(res?.getProjectUserMe?.scheduleRole);
    }
  });

  useEffect(() => {
    if (router?.query?.suid && projectScheduleId) {
      getOneSchedule();
      // setStatus(router?.query?.status as Gql.ScheduleStatus);
      setTimeout(() => {
        scheduleActivityDrawerRef?.current?.pushDrawer();
      }, 500);
    }

    if (router?.query?.status) {
      setVariant(router?.query?.status as string)
    }
  }, [router?.query]);

  const getOneSchedule = async () => {
    const res = await getSchedBySuid({
      variables: {
        suid: router.query?.suid as string,
        projectScheduleId: Number(projectScheduleId)
      }
    });
    setFilter({ ...queries.filter, wbs: { eq: res?.data?.getScheduleBySuid?.wbs as string } });

    if (scheduleActivityDrawerFilterRef?.current) {
      scheduleActivityDrawerFilterRef.current?.setFilter?.({ wbs: res?.data?.getScheduleBySuid?.wbs as string });
    }
  };

  // getScheduleBySuid query
  const [getSchedBySuid, { }] = Gql.useGetScheduleBySuidLazyQuery({
    onError: onError
  });

  const { data: proSchedData, refetch: refetchProSched } = Gql.useProjectScheduleQuery({
    variables: {
      id: projectScheduleId ?? 0
    }
  });

  useEffect(() => {
    if (proSchedData && !proSchedData?.projectSchedule?.isScheduleTracked) window.location.replace('/schedules');
    const errorLog = JSON.parse(proSchedData?.projectSchedule?.errorLog ?? '[]');
    if (errorLog.length > 0) {
      Modal.warning({
        title: 'Warning',
        content: 'Start and finish variances within your schedule file have not been detected.'
      });
    }
  }, [proSchedData]);

  const [getScheduleByStatus, { loading: fetchingSchedule, error, refetch, data, fetchMore }] =
    Gql.useGetSchedulesLazyQuery({
      variables: {
        ...queries,
        filter: {
          ...queries.filter,
          ...(router.query?.suid && ({ suid: { eq: router.query?.suid } } as any)),
          projectScheduleId: { eq: projectScheduleId },
          type: {
            neq: Gql.ScheduleType.Milestone
          },
          percentComplete: { lt: '1' },
          ...{
            status: {
              eq:
                variant === 'StartDelay'
                  ? Gql.ScheduleStatus.StartDelay
                  : variant === 'FinishDelay'
                  ? Gql.ScheduleStatus.FinishDelay
                  : variant === 'Hold'
                  ? Gql.ScheduleStatus.Hold
                  : Gql.ScheduleStatus.Hold
            }
          },
          // ...(status === Gql.ScheduleStatus.Pending && {
          //   baselineStart: queries?.filter?.baselineStart
          //     ? {
          //         gte: queries?.filter?.baselineStart?.gte
          //           ? queries?.filter?.baselineStart?.gte
          //           : moment().format('YYYY-MM-DD'),
          //         lte: queries?.filter?.baselineStart?.lte
          //           ? queries?.filter?.baselineStart?.lte
          //           : moment().add(7, 'days').format('YYYY-MM-DD')
          //       }
          //     : {
          //         gte: moment().format('YYYY-MM-DD'),
          //         lte: moment().add(7, 'days').format('YYYY-MM-DD')
          //       }
          // }),
          ...(isOnlyMeFilter && {
            assignees: { userId: { eq: userMeData?.getUserMe?.id } }
          })
        },
        sorting: [
          {
            direction:
              queries?.sorting?.direction === Gql.SortDirection.Desc ? Gql.SortDirection.Desc : Gql.SortDirection.Asc,
            field: queries?.sorting?.field ? queries?.sorting?.field : Gql.ScheduleSortFields.BaselineStart
          }
          // ...(percentSort !== null && percentSort)
        ]
      },
      fetchPolicy: 'cache-and-network',
      onError: onError
    });

  const [getScheduleSummary, { data: scheduleSummary, refetch: refetchSummary }] =
    Gql.useGetScheduleSummaryDataLazyQuery();

  const [assignMedia] = Gql.useAssignSchedulesMediaMutation({
    onCompleted: () => {
      refetch();
    },
    onError: onError as any
  });

  useEffect(() => {
    if (router?.query?.wbs && router?.query?.id) {
      setFilter({ wbs: { eq: router?.query?.wbs as string } });
    }
  }, [tableRef]);

  const [updateManyScheduleStatus] = Gql.useUpdateManyScheduleStatusMutation({
    onCompleted: async () => {
      message.success('Status updated');
      await refetch();
    },
    onError: onError as any
  });

  // updateOneSchedule mutation
  const [updateOneSchedule, { loading: updating }] = Gql.useUpdateOneScheduleMutation({
    onCompleted: async () => {
      // message.success('Note updated.');
      noteForm.resetFields();
      closeEditNoteModal();
      router.replace({ query: { ..._.omit(router.query, 'scheduleId') } });
      await refetch();
      await refetchSummary();
    },
    onError: onError as any
  });

  const sanitizedData = useCallback(() => {
    if (!data) return [];
    return _.map(data?.getSchedules?.nodes, (schedule: any) => {
      return {
        baselineStart: moment(schedule?.baselineStart).format('ddd D/M/YYYY'),
        actualStart: schedule.actualStart ? moment(schedule?.actualStart).format('ddd D/M/YYYY') : '-',
        baselineFinish: moment(schedule?.baselineFinish).format('ddd D/M/YYYY'),
        actualFinish: schedule.actualFinish ? moment(schedule?.actualFinish).format('ddd D/M/YYYY') : '-',
        baselineDuration: schedule?.baselineDuration ? `${schedule?.baselineDuration} days` : '-',
        completionStatus: schedule ? `${(schedule.percentComplete * 100).toFixed(2)}%` : '-',
        daysDelayed: schedule?.daysDelayed ?? '-',
        completedDelay: schedule?.completedDelay ?? '-',
        startVariance: schedule?.startVariance ?? 0,
        finishVariance: schedule?.finishVariance ?? 0,
        activity: {
          name: schedule?.name,
          predecessorName: schedule?.predecessorName
        },
        wbs: schedule?.wbs,
        medias: schedule?.medias?.nodes?.map((media: Gql.SchedulesMedia) => ({
          id: media?.id,
          name: media?.name,
          status: 'done',
          url: media?.fileUrl
        })),
        suid: schedule?.suid,
        id: schedule?.id,
        status: schedule?.status,
        notes: schedule?.notes,
        updatedAt: moment(schedule?.updatedAt).format('D/M/YYYY h:mm A'),
        documents: schedule?.documents?.[0] ?? [],
        proposeStatus: schedule.proposedStatus,
        assignees: schedule.assignees,
        copies: schedule.copies,
        isPriority: schedule.isPriority,
        percentComplete: schedule.percentComplete,
        commentCount: schedule.commentCount,
        isCritical: schedule.isCritical
      };
    });
  }, [data]);

  const schedulesData = useMemo(() => sanitizedData(), [sanitizedData]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        await Promise.all([
          getScheduleByStatus(),
          getScheduleSummary({
            variables: {
              projectScheduleId: parseInt(projectScheduleId)
            }
          })
        ]);
      } catch (error) {
        console.error('Failed to fetch data:', error);
      }
    };

    fetchData();
  }, [projectScheduleId]);

  useEffect(() => {
    schedulesData?.forEach(schedule => {
      form.setFieldsValue({
        [`medias_${schedule.wbs}`]: schedule.medias,
        [`status_${schedule.suid}`]: schedule?.status
      });
    });
  }, [form, schedulesData]);

  const onFinish = async (values: any) => {
    const schedulesStatus = _.map(values, (value, key: string) => {
      if (key.includes('status')) {
        return {
          suids: key.split('_')[1],
          status: Gql.ScheduleStatus[value as Gql.ScheduleStatus]
        };
      }
    }).filter(value => value?.status !== undefined && value.status !== status);
    if (schedulesStatus.length === 0) return;

    //? check if the status is not the same as the current status, then update

    const currentStatus = data?.getSchedules?.nodes?.map((schedule: any) => ({
      suids: schedule.suid,
      proposedStatus: schedule.proposedStatus
    }));

    const filteredStatus = schedulesStatus.filter(
      (status: any) =>
        currentStatus?.find((current: any) => current.suids === status.suids)?.proposedStatus !== status.status
    );

    if (filteredStatus.length === 0) return;

    await updateManyScheduleStatus({
      variables: {
        input: {
          status: filteredStatus as any,
          projectScheduleId
        }
      }
    });
  };

  // trigger when done edit note
  const onEditNoteFinish = async (values: any) => {

    try {
      const res = await updateOneSchedule({ variables: { input: { id: scheduleId as string, update: values } } });
      if (res?.errors) getErrorMessage(res?.errors)
      else { message.success('Note updated.'); }

    } catch (error: any) {
      message.error(error?.message);
    }
  };

  // Create and edit modal
  const [modal, showCreateGroupModal, closeCreateGroupModal] = useModal({
    onCancel: () => {
      closeCreateGroupModal();
    },
    title: 'Add Collaborator',
    content: (
      <>
        <Form layout="vertical" form={form} onFinish={onFinish} requiredMark={false} initialValues={{}}>
          <ScheduleValidator refetch={refetchProjectUser} type="collaborator" />
          <Divider />
        </Form>
      </>
    )
  });

  // Edit Note Modal
  const [editNoteModal, showEditNoteModal, closeEditNoteModal] = useModal({
    onCancel: () => {
      router.replace({ query: { ..._.omit(router.query, 'scheduleId') } });
      noteForm.resetFields();
    },
    title: scheduleRole !== Gql.ScheduleUserRole.Collaborator ? 'Note' : noteForm.getFieldsValue().notes ? 'Edit Note' : 'Add Note',
    content: (
      <>
        <Form layout="vertical" form={noteForm} onFinish={onEditNoteFinish} requiredMark={false} initialValues={{}}>
          <Form.Item label="Note" name="notes">
            <Input.TextArea disabled={scheduleRole !== Gql.ScheduleUserRole.Collaborator} rows={4} />
          </Form.Item>

          {scheduleRole === Gql.ScheduleUserRole.Collaborator ? (
            <Button loading={updating} type="primary" onClick={() => noteForm.submit()}>
              Save
            </Button>
          ) : null}
        </Form>
      </>
    )
  });

  const handleCellClick = (record: any, rowIndex: any, dataIndex: any) => {
    //disable push drawers when click on these columns
    const selectedColumn = ['medias', 'activity', 'responseNote', 'status', 'documents'];
    if (selectedColumn.includes(dataIndex)) return;
    else {
      const suid = record.suid;
      const id = record.id;
      router.push({
        pathname: router.pathname,
        query: {
          projectScheduleId,
          suid,
          id
        }
      });

      scheduleActivityDrawerRef?.current?.pushDrawer();
    }
  };

  const openInNewTab = (url: any) => {
    const redirectNewTab = window.open(url, '_blank', 'noopener,noreferrer');

    if (!redirectNewTab || redirectNewTab.closed || typeof redirectNewTab.closed === 'undefined') {
      setTimeout(() => {
        message.info('Please allow popups for viewing the document.');
      }, 1000);
    } else {
      redirectNewTab.focus();
    }
  };

  const view = (data: any) => {
    if (data?.category === Gql.CategoryType.AllForm && data?.requestForSignatures) {
      const currentUser = data?.requestForSignatures.find((req: any) => req.signById === userMeData?.getUserMe?.id);
      let assigneesId = '' as any;
      for (let i = 0; i < data?.requestForSignatures?.length; i++) {
        assigneesId += data?.requestForSignatures[i].signById + ',';
      }
      assigneesId = assigneesId.slice(0, -1);
      assigneesId = assigneesId.split(',');

      if (
        (assigneesId.find((id: any) => userMeData?.getUserMe?.id.includes(id)) &&
          data?.status != 'Approved' &&
          data?.status != 'Submitted' &&
          currentUser?.status != 'Approved') ||
        data?.status == 'Draft'
      ) {
        openInNewTab('/viewer/all-form-viewer?documentId=' + data.id);
      } else {
        openInNewTab('/viewer/all-form-viewers?documentId=' + data.id);
      }
    } else {
      openLinkedDocument(data?.id);
    }
  };

  const linkedDocument = async (document: Gql.ProjectDocument) => {
    if (!selectedSchedule) return message.error('Please select a schedule');

    const schedule = data?.getSchedules?.nodes?.filter((schedule: any) => schedule.id === selectedSchedule);
    const documents = schedule?.[0]?.documents?.map((document: any) => ({ id: +document?.id }));
    try {
      await updateOneSchedule({
        variables: {
          input: {
            id: selectedSchedule?.toString() ?? '',
            update: {
              documents: [...(documents ?? []), { id: +document.id }]
            }
          }
        }
      });
      return message.success('Successful linked document');
    } catch (error) {
      message.error('Failed to link document');
    }
  };

  const onSelectStatus = async (value: Gql.ScheduleStatus, id: string, proposedStatus: Gql.ScheduleStatus) => {
    const res = await updateOneSchedule({
      variables: {
        input: {
          id,
          update: {
            status: value,
            ...(value === Gql.ScheduleStatus.Hold && { proposedStatus })
          }
        }
      }
    });

    if (res.data?.updateOneSchedule) {
      message.success('Status updated');
      await refetch();
    }
  };

  const openLinkedDocument = (documentId: string) => {
    setLinkedToDocumentId(documentId);
    return pdfTronModalRef?.current?.openModal?.();
  };

  const updatedStatus = async (id: string, status: Gql.ScheduleStatus) => {
    try {
      return await updateOneSchedule({
        variables: {
          input: {
            id: id,
            update: {
              status: status
            }
          }
        }
      });
    } catch (error) {
      return message.error('Failed to update status');
    }
  };

  const radioStatus = useMemo(
    () => (
      <Radio.Group
        className="flex-1"
        defaultValue={router?.query?.status ? variant : 'StartDelay'}
        onChange={e => {
          setFilter({});
          setVariant(e.target.value);
        }}
      >
        <Radio.Button key={0} value={'StartDelay'} className="font-semibold">
          Start Delay
        </Radio.Button>

        <Radio.Button key={1} value={'FinishDelay'} className="font-semibold">
          Finish Delay
        </Radio.Button>

        <Radio.Button key={2} value={'Hold'} className="font-semibold">
          On Hold
        </Radio.Button>
      </Radio.Group>
    ),
    [setFilter]
  );

  const columns = useMemo(() => {
    return [
      {
        title: 'WBS',
        key: 'wbs',
        dataIndex: 'wbs',
        align: 'left',
        sorter: true,
        sortDirections: ['descend']
      },
      {
        title: 'Activity Name',
        key: 'activity',
        dataIndex: 'activity',
        width: 350,
        render: (activity: ActivityProps, record: any) => {
          if (!activity) return;

          const { name, predecessorName } = activity;

          return (
            <div>
              <div className="flex items-center align-center">
                <Paragraph className="mt-3" ellipsis={{ rows: 1, expandable: false }}>
                  {name}
                </Paragraph>
                <a
                  onClick={() => {
                    scheduleActivityDrawerRef?.current?.pushDrawer();
                    redirectTask(record.suid, projectScheduleId);
                  }}
                >
                  <Icon name="square-with-arrow" className="ml-2" />
                </a>
              </div>

              <p className="text-base text-[#666]">{predecessorName}</p>
            </div>
          );
        }
      },
      {
        title: 'Duration',
        key: 'baselineDuration',
        dataIndex: 'baselineDuration',
        width: 165
      },
      {
        title: 'Priority',
        key: 'isPriority',
        width: 165,
        render: (data: Gql.Schedule) => {
          const onClick = async (e: any) => {
            e.stopPropagation();
            await updateOneSchedule({
              variables: {
                input: {
                  id: data.id,
                  update: {
                    isPriority: !data.isPriority
                  }
                }
              }
            });
          };
          return (
            <div onClick={onClick} className="cursor-pointer">
              {data?.isPriority ? (
                <Icon name="flag" fill="#ff0000" width={20} height={20} />
              ) : (
                <Icon name="flag" width={20} height={20} />
              )}
            </div>
          );
        }
      },
      {
        title: 'Baseline Start',
        key: 'baselineStart',
        dataIndex: 'baselineStart',
        width: 165,
        sorter: true,
        sortDirections: ['descend']
      },
      {
        title: 'Actual Start',
        key: 'actualStart',
        dataIndex: 'actualStart',
        width: 165,
        sorter: true,
        sortDirections: ['descend']
      },
      {
        title: 'Start Variance',
        // (
        //   <span>
        //     Start Variance{' '}
        //     <Tooltip title="The difference between a baseline start date of a task and its actual start date">
        //       <Icon name="info" className="ml-2 mt-2" />
        //     </Tooltip>
        //   </span>
        // ),
        key: 'startVariance',
        dataIndex: 'startVariance',
        width: 165,
        render: (data: any) => {
          return <div>{data} days</div>;
        }
      },
      {
        title: 'Baseline Finish',
        key: 'baselineFinish',
        dataIndex: 'baselineFinish',
        width: 165
      },
      {
        title: 'Actual Finish',
        key: 'actualFinish',
        dataIndex: 'actualFinish',
        width: 165
      },
      {
        title: 'Finish Variance',
        // (
        //   <span>
        //     Finish Variance{' '}
        //     <Tooltip title="The difference between a baseline finish date of a task and its current finish date">
        //       <Icon name="info" className="ml-2 mt-2" />
        //     </Tooltip>
        //   </span>
        // ),
        key: 'finishVariance',
        dataIndex: 'finishVariance',
        width: 165,
        render: (data: any) => {
          return <div>{data} days</div>;
        }
      },
      {
        title: '% Complete',
        key: 'percentComplete',
        dataIndex: 'percentComplete',
        width: 165,
        sorter: true,
        sortDirections: ['descend'],
        render: (data: any) => {
          return <div>{data * 100}%</div>;
        }
      },
      {
        title: 'Is Critical',
        key: 'isCritical',
        dataIndex: 'isCritical',
        width: 80,
        render: (data: any) => <div className="text-center">{data ? 'Yes' : 'No'}</div>
      },
      // {
      //   title: 'Linked To',
      //   width: 250,
      //   dataIndex: 'documents',
      //   render: (document: any) => {
      //     return (
      //       <>
      //         {document.id && (
      //           <div className="flex items-center justify-start gap-2 mt-1" onClick={() => view(document)}>
      //             <p>{getFileIcon(document?.type)}</p>
      //             <Paragraph className="w-[150px] mt-1.5 cursor-pointer" ellipsis={{ rows: 1, expandable: false }}>
      //               {document?.name}
      //             </Paragraph>
      //           </div>
      //         )}
      //         <div className="flex items-center cursor-pointer" onClick={() => linkedToModalRef?.current?.openModal?.()}>
      //           <p className="text-binaBlue">
      //             <Icon name={'plus-outlined-primary'} />
      //             <span className="ml-2">Add Docs</span>
      //           </p>
      //         </div>
      //       </>
      //     );
      //   }
      // },
      {
        title: 'Description',
        key: 'notes',
        width: '170px',
        render: (record: any) => {
          const { notes } = record;

          return (
            <div
              onClick={e => {
                e.stopPropagation();
                noteForm.setFieldsValue({ notes });
                showEditNoteModal();

                router.replace({ query: { ...router.query, scheduleId: record.id } });
              }}
            >
              {notes && (
                <Paragraph
                  className="mt-1 cursor-pointer"
                  style={{ width: 100 }}
                  ellipsis={{ rows: 1, expandable: false }}
                  onClick={() => {
                    noteForm.setFieldsValue({ notes });
                    showEditNoteModal();
                  }}
                >
                  {notes}
                </Paragraph>
              )}
              {scheduleRole === Gql.ScheduleUserRole.Collaborator ? (
                <p
                  className=" text-binaBlue cursor-pointer"
                >
                  {notes ? 'Edit note' : 'Add note'} <Icon name="edit-blue-pen" />
                </p>
              ) : null}
            </div>
          );
        }
      },
      {
        title: 'Comments',
        key: 'comment',
        width: '170px',
        align: 'center',
        render: (record: any) => {
          const { notes, id } = record; // Assuming 'id' is a unique identifier for each row

          return (
            <Popover
              placement="topRight"
              className="cursor-pointer"
              content={
                <div className="w-[400px]" onClick={e => e.stopPropagation()}>
                  <DrawerActivityList scheduleId={id} />
                </div>
              }
              title={false}
              trigger="click"
              open={openComment === id} // Check if 'openComment' state matches the current row id
              onOpenChange={visible => setOpenComment(visible ? id : undefined)} // Open the Popover for the current row only
            >
              <Row justify={'center'} className="w-full items-center gap-3">
                <Button className="mt-5" icon={<Icon name="comment" width={25} height={25} />} />
                {record?.commentCount > 0 && <p className="mt-4">{record?.commentCount}</p>}{' '}
              </Row>
            </Popover>
          );
        }
      },
      {
        title: 'Status',
        key: 'status',
        dataIndex: 'status',
        align: 'center',
        fixed: 'right',
        render: (data: any, record: any) => {
          const scheduleStatus = getStatus(record.status, record.proposeStatus);

          return {
            props: {
              style: {
                backgroundColor:
                  record.status === Gql.ScheduleStatus.InProgress &&
                    (record?.daysDelayed > 0 || record?.completedDelay > 0)
                    ? '#FFD6D6'
                    : '',
                hover: {
                  backgroundColor: '#FFFFFF'
                }
              }
            },
            children: (
              <div className="flex justify-center h-12">
                <Form.Item name={`status_${record.suid}`} className="m-auto">
                  <Select
                    style={{ width: 210 }}
                    disabled={
                      scheduleRole === null ||
                      record?.status === Gql.ScheduleStatus.Completed ||
                      scheduleRole !== 'Collaborator'
                    }
                    onSelect={(value: any) => onSelectStatus(value, record.id, record.status)}
                  >
                    {_.map(scheduleStatus, obj => {
                      return (
                        <Select.Option key={obj.label} value={obj.nextVal}>
                          <div className="flex items-center ">
                            <div className="h-4 w-1 mr-2" style={{ backgroundColor: obj.color }} />
                            <div>{_.startCase(obj.nextVal)}</div>
                          </div>
                        </Select.Option>
                      );
                    })}
                  </Select>
                </Form.Item>
              </div>
            )
          };
        }
      }
    ];
  }, [noteForm, onSelectStatus, openComment, projectScheduleId, scheduleRole, showEditNoteModal, updateOneSchedule]);

  const [tableColumns, toggleColumn] = useColumns('activity');

  const filteredColumns = useMemo(() => {
    return columns.filter(column => {
      const columnConfig = tableColumns.find((col: any) => col.key === column.key);
      return columnConfig ? columnConfig.show : false;
    });
  }, [columns, tableColumns]);

  function getFullAssigneesNames(assignees: any) {
    let name = '';

    assignees.forEach((element: any, index: any) => {
      name += element.user.name;

      if (assignees.length - 1 != index) name += ', ';
    });
    return name;
  }

  const tableColumnsWithOnClick = columns.map(column => {
    return {
      ...column,
      onCell: (record: any) => ({
        onClick: (event: any) => {
          setSelectedSchedule(record.id);
          handleCellClick(record, record.key, column.dataIndex);
        }
      })
    };
  });

  // when scroll reach bottom this will be trigger
  const handleScroll = useCallback(async () => {
    const { scrollTop, clientHeight, scrollHeight } = scrollRef.current as any;

    if (scrollHeight - scrollTop <= clientHeight + 5) {
      if (!data?.getSchedules?.pageInfo?.hasNextPage) return;

      setGetLoading(true);
      try {
        await fetchMore({
          variables: {
            ...queries,
            filter: {
              ...queries.filter,
              ...(variant === 'StartDelay'
                ? {
                    status: {
                      eq: Gql.ScheduleStatus.StartDelay
                    }
                  }
                : variant === 'FinishDelay'
                ? {
                    status: {
                      eq: Gql.ScheduleStatus.FinishDelay
                    }
                  }
                : {
                    status: {
                      eq: Gql.ScheduleStatus.Hold
                    }
                  }),
              projectScheduleId: { eq: projectScheduleId },
              percentComplete: { lt: '1' }
            },
            paging: {
              offset: 0,
              limit: data?.getSchedules?.nodes?.length + 20
            },
            sorting: {
              direction:
                queries?.sorting?.direction === Gql.SortDirection.Desc ? Gql.SortDirection.Desc : Gql.SortDirection.Asc,
              field: queries?.sorting?.field ? queries?.sorting?.field : Gql.ScheduleSortFields.BaselineStart
            }
          },
          updateQuery: (prev, { fetchMoreResult }) => {
            if (!fetchMoreResult) return prev;
            return {
              getSchedules: {
                ...prev.getSchedules,
                nodes: _.uniqBy([...prev.getSchedules.nodes, ...fetchMoreResult.getSchedules.nodes], 'id'),
                pageInfo: fetchMoreResult.getSchedules.pageInfo
              }
            };
          }
        });
      } catch (error) {
        console.error('Error fetching more data:', error);
      }

      setGetLoading(false);
    }
  }, [data?.getSchedules?.pageInfo?.hasNextPage, data?.getSchedules?.nodes?.length]);



  // const radioStatus = useMemo(() => (
  //   <Radio.Group
  //     className="flex-1"
  //     defaultValue={router?.query?.status ?? Gql.ScheduleStatus.Upcoming}
  //     onChange={e => {
  //       setFilter({});
  //       setStatus(e.target.value);
  //     }}
  //   >
  //     <Radio.Button key={0} value={Gql.ScheduleStatus.Upcoming} className="font-semibold">
  //       Start
  //     </Radio.Button>

  //     <Radio.Button key={1} value={Gql.ScheduleStatus.InProgress} className="font-semibold">
  //       Finish
  //     </Radio.Button>
  //   </Radio.Group>
  // ), [setFilter]);

  const loading = updating || fetching || fetchingSchedule || getLoading;

  const getStatus = (status: Gql.ScheduleStatus, proposedStatus: Gql.ScheduleStatus) => {
    if (status === Gql.ScheduleStatus.StartDelay) {
      return [
        { label: 'StartDelay', values: '', nextVal: Gql.ScheduleStatus.StartDelay, color: '#FFAF00' },
        { label: 'Hold', values: '', nextVal: Gql.ScheduleStatus.Hold, color: '#378DDC' }
      ];
    } else if (status === Gql.ScheduleStatus.FinishDelay) {
      return [
        { label: 'FinishDelay', values: '', nextVal: Gql.ScheduleStatus.FinishDelay, color: '#FA5E55' },
        { label: 'Hold', values: '', nextVal: Gql.ScheduleStatus.Hold, color: '#378DDC' }
      ];
    } else {
      let color = '';
      let label = '';

      switch (proposedStatus) {
        case Gql.ScheduleStatus.StartDelay:
          color = '#FFAF00';
          label = 'Start Delay';
          break;
        case Gql.ScheduleStatus.FinishDelay:
          color = '#FA5E55';
          label = 'Finish Delay';
          break;
      }

      return [
        { label: 'Hold', values: '', nextVal: Gql.ScheduleStatus.Hold, color: '#378DDC' },
        { label: label, values: '', nextVal: proposedStatus, color: color }
      ];
    }
  };

  return (
    <div className="m-4 ">
      {proSchedData?.projectSchedule?.isScheduleTracked && (
        <Form layout="vertical" form={form} onFinish={onFinish} requiredMark={false} initialValues={{}}>
          <ScheduleHeader title={proSchedData?.projectSchedule?.name} />
          {/* value={driveType} onChange={handleDriveChange} */}
          <Row className="mb-5">
            <div className="flex gap-4 justify-end ml-8">
              {/* <div className='text-[40px] w-[300px]'>
                <h1 className='text-[#A7ACAF]' >Task&apos;s Delay Detected</h1>
              </div> */}

              <SummaryCard
                label="Start Delay"
                value={scheduleSummary?.getScheduleSummaryData?.startVarianceCount || 0}
                bgColor={'#FFAF00'}
              />
              <SummaryCard
                label="Finish Delay"
                value={scheduleSummary?.getScheduleSummaryData?.finishVariance || 0}
                bgColor={'#FA5E55'}
              />
              <SummaryCard
                label="On Hold"
                value={scheduleSummary?.getScheduleSummaryData?.holdStatusCount || 0}
                bgColor={'#378DDC'}
              />
            </div>
          </Row>
          <div className="flex items-center mb-2 ">
            {radioStatus}
            <Space className="flex-1">
              <Form
                form={searchForm}
                onValuesChange={debounce(data => {
                  // onSearch(data);
                  setFilter({ name: { like: data.keyword } });
                }, 500)}
              ></Form>

              {/* <Switch
                checkedChildren="Assigned To Me"
                unCheckedChildren="Assigned To Me"
                onChange={checked => {
                  setIsOnlyMeFilter(checked);
                  setTimeout(() => {
                    getScheduleByStatus();
                  }, 100);
                }}
              /> */}
            </Space>

            {canAssign && (
              <Button
                loading={loading}
                onClick={() => {
                  showCreateGroupModal();
                }}
                type="link"
                className="text-binaBlue"
                icon={<Icon name="user-plus" />}
                // disabled={!canAssign}
              >
                Add Collaborator
              </Button>
            )}
            <Popover
              content={<ShowHideColumn tableColumns={tableColumns} onColumnToggle={name => toggleColumn(name)} />}
              trigger="hover"
            >
              <Button
                icon={<Icon name="columns" className="mr-1.5" />}
                className="mx-3 flex items-center justify-center"
              >
                Column
              </Button>
            </Popover>
            <Badge dot={_.size(queries.filter) > 0}>
              <Button icon={<FilterOutlined />} onClick={() => scheduleActivityDrawerFilterRef.current?.pushDrawer()}>
                Filters
              </Button>
            </Badge>
          </div>

          <Spin spinning={loading} tip="Loading...">
            <div
              style={{ overflowY: 'scroll', height: window.innerHeight - 400 }} // Set the desired height and enable vertical scrolling
              onScroll={handleScroll}
              ref={scrollRef}
            >
              <Table
                className="my-4"
                rowClassName={''}
                onChange={async (pagination, filters, sorter) => {
                  setSorter({
                    //@ts-ignore
                    direction: sorter?.order === 'descend' ? Gql.SortDirection.Desc : Gql.SortDirection.Asc,
                    //@ts-ignore
                    field: sorter?.field
                  });
                }}
                sticky={true}
                ref={tableRef}
                dataSource={schedulesData}
                columns={filteredColumns as any}
                size="small"
                pagination={false}
                tableLayout="auto"
                loading={loading}
                scroll={{
                  x: 'max-content'
                }}
                style={{ whiteSpace: 'pre' }}
                onRow={(record, rowIndex) => ({
                  style: {
                    backgroundColor:
                      status === Gql.ScheduleStatus.InProgress &&
                      (record?.daysDelayed > 0 || record?.completedDelay > 0)
                        ? '#FFD6D6'
                        : ''
                  }
                })}
              />
            </div>
          </Spin>
          {modal}
          {editNoteModal}

          <ActivityDrawerFilter
            setFilter={setFilter}
            queries={queries}
            setDirection={setSorter}
            ref={scheduleActivityDrawerFilterRef}
          />

          <LinkedToModal ref={linkedToModalRef} onChange={async document => await linkedDocument(document)} />
          <PdfTronModal ref={pdfTronModalRef} documentId={LinkedToDocumentId ?? ''} onSaved={() => refetch()} />
          { binaAITabVisible && <ChatWidget collectionName={`schedule_${projectScheduleId}`} />}
        </Form>
      )}
    </div>
  );
};

type ScheduleProps = {
  type: 'validator' | 'manager' | 'collaborator';
  refetch?: () => void;
};

const ScheduleValidator: React.FC<ScheduleProps> = ({ type, refetch }) => {
  return (
    <>
      <p className="mb-4">Schedule Collaborator</p>
      <Form.Item name="name" rules={[{ required: false, message: _.upperFirst(type) + ' is required!' }]}>
        <ManagerPicker onSaved={refetch} type={type}></ManagerPicker>
      </Form.Item>
    </>
  );
};

function redirectTask(suid: any, projectScheduleId: any) {
  const url = `/schedules/schedule?projectScheduleId=${projectScheduleId}&suid=${suid}`;
  window.open(url, '_blank');
}

type ActivityProps = {
  name: string;
  predecessorName: string;
};

type SummaryProps = {
  label: string;
  value: number;
  bgColor: string;
};
const SummaryCard: React.FC<SummaryProps> = ({ value, bgColor, label }) => {
  return (
    <Card className="shadow-scheduleSummaryCard rounded-lg">
      <Row gutter={20}>
        <Col span={4}>
          <div style={{ backgroundColor: bgColor }} className={`w-[17px] h-[100px] rounded-lg`} />
        </Col>
        <Col className="w-[150px]" span={20}>
          <h1 className="text-[48px]">{value ?? 0}</h1>
          <h3 className="font-[32px]">{label}</h3>
        </Col>
      </Row>
    </Card>
  );
};

Activity.auth = true;
Activity.Layout = ScheduleTabLayout;

export default Activity;
