import { ApolloError } from '@apollo/client';
import { Icon } from '@commons';
import Center from '@components/Center';
import ComposeEmail, { ComposeEmailRef } from '@components/correspondence/ComposeEmail';
import ReplyCorrespondence, { ReplyCorrespondenceRef } from '@components/correspondence/ReplyCorrespondence';
import SearchInput from '@components/forms/FormsInput/SearchInput';
import useModal from '@components/hooks/useModal';
import useQueries from '@components/hooks/useQueries';
import DocViewer, { DocViewerRenderers } from '@cyntler/react-doc-viewer';
import { concatenateRecipients, downloadAllAttachments, nameAlias } from '@utils/app.utils';
import { onError } from '@utils/error';


import { Avatar, Button, Card, Checkbox, DatePicker, Descriptions, Form, Input, message, Popover, Table, Tag, Tooltip, Typography, Modal } from 'antd';

import UserAvatar from '@components/UserAvatar';
import { useForm } from 'antd/lib/form/Form';
import AttachmentTag from 'components/correspondence/AttachmentTag';
import { Dayjs } from 'dayjs';
import parse, { domToReact } from 'html-react-parser';
import debounce from 'lodash/debounce';
import moment from 'moment';
import { useRouter } from 'next/router';
import { AppLayoutContext } from 'pages/_app';
import React, { useContext, useEffect, useState } from 'react';
import * as Gql from 'src/graphql';
const { Text } = Typography;
const { confirm } = Modal

interface QueryFilter {
  sentToMe: boolean;
  sentByMe: boolean;
  status: string[];
  date: Dayjs | null;
  search: string | null;
  clearFilter: boolean;
}

interface FilterOptions {
  key: string;
  label: string | React.ReactElement;
  onClick?: () => void;
  onCancel: () => void;
  active: () => boolean;
  clearFilter?: boolean;
}

const ProjectCorrespondence = () => {
  const router = useRouter();
  const id = router.query.id;
  const { userData: user } = useContext(AppLayoutContext);
  const composeRef = React.useRef<ComposeEmailRef>(null);
  const [searchForm] = useForm();
  const [referenceForm] = useForm();
  const [queryFilter, setQueryFilter] = useState<QueryFilter>({
    sentToMe: false,
    sentByMe: false,
    status: [] as Gql.EmailDeliveryStatus[],
    date: null,
    search: null,
    clearFilter: false
  });
  const [showComposeEmail, setShowComposeEmail] = useState(false);
  const [email, setEmail] = useState<Gql.Email | null>(null);
  const [projectId, setProjectId] = useState<string>();
  const [referenceVisibility, setReferenceVisibility] = useState(false)
  const [fileUrl, setFileUrl] = useState<any>([]);
  const [userRole, setUserRole] = useState<string>('');
  const [showAllAttachments, setShowAllAttachments] = useState(false);
  // Add new ref in component
  const replyRef = React.useRef<ReplyCorrespondenceRef>(null);
  const [showReply, setShowReply] = useState(false);

  const [form] = useForm();

  /** START ACL */
  const companySubscriptions = Gql.useCompanySubscriptionsQuery({
    variables: {
      sorting: {
        direction: Gql.SortDirection.Desc,
        field: Gql.CompanySubscriptionSortFields.SubscriptionEndDate
      },
      paging: {
        limit: 1
      }
    },
    onError: onError,
    nextFetchPolicy: 'cache-first'
  });
  /** END ACL */

  const { queries, setPaging } = useQueries<Gql.EmailFilter, Gql.EmailSortFields>({
    paging: { limit: 10, offset: 0 },
    usePagingParam: false
  });

  useEffect(() => {
    setUserRole(localStorage.getItem('ProjectUserRole') || '');

    const initialFilter = {
      ...(id && { id: { eq: id.toString() } }),
      projectId: { eq: localStorage.getItem('ProjectId') },
      ...(userRole === Gql.ProjectUserRoleType.CanEdit || userRole === Gql.ProjectUserRoleType.CanView ? {
        or: [
          { recipientId: { eq: user?.getUserMe?.id } },
          { senderId: { eq: user?.getUserMe?.id } },
          { receivers: { userId: { eq: Number(user?.getUserMe?.id) } } }
        ]
      } : {})
    };

    refetch({
      filter: initialFilter
    });
  }, [userRole, user?.getUserMe?.id]);

  const setStatus = (status: string) => {
    let statuses = queryFilter.status;

    if (statuses.includes(status)) {
      statuses = statuses.filter(element => element !== status);
    } else {
      statuses.push(status);
    }

    setQueryFilter({
      ...queryFilter,
      status: [...statuses]
    });

    setPaging({
      ...queries.paging,
      offset: 0
    });
  };

  const statusOptions = [
    { key: 'Unread', label: 'Unread' },
    { key: 'Read', label: 'Read' },
    { key: 'Replied', label: 'Replied' }
  ];
  const statusContent = (
    <div className="flex flex-col">
      {statusOptions.map(status => (
        <div key={status.key}>
          <Checkbox
            onChange={() => {
              setStatus(status.key);
            }}
            checked={queryFilter.status?.includes(status.key)}
          >
            {status.label}
          </Checkbox>
        </div>
      ))}
    </div>
  );

  const dateContent = (
    <DatePicker
      onChange={val => {
        setQueryFilter({
          ...queryFilter,
          date: val || null
        });
      }}
    />
  );

  const filters: FilterOptions[] = [
    {
      label: 'Clear filters',
      key: 'clearFilter',
      onClick: () => {
        setQueryFilter({
          sentToMe: false,
          sentByMe: false,
          status: [] as Gql.EmailDeliveryStatus[],
          date: null,
          search: null,
          clearFilter: false
        });
        router.replace('/correspondence');
      },
      onCancel: () => { },
      active: () => queryFilter.clearFilter === true
    },
    {
      label: 'Sent to Me',
      key: 'sentToMe',
      onClick: () => {
        setQueryFilter({
          ...queryFilter,
          sentToMe: true
        });
      },
      onCancel: () => {
        setQueryFilter({
          ...queryFilter,
          sentToMe: false
        });
      },
      active: () => queryFilter.sentToMe === true
    },
    {
      label: 'Sent by Me',
      key: 'sentByMe',
      onClick: () => {
        setQueryFilter({
          ...queryFilter,
          sentByMe: true
        });
      },
      onCancel: () => {
        setQueryFilter({
          ...queryFilter,
          sentByMe: false
        });
      },
      active: () => queryFilter.sentByMe === true
    },
    {
      label: (
        <Popover content={statusContent} trigger="click" placement="bottomLeft" arrow={false} overlayClassName="mt-1">
          Status
        </Popover>
      ),
      key: 'status',
      onCancel: () => {
        setQueryFilter({
          ...queryFilter,
          status: []
        });
      },
      active: () => queryFilter.status.length > 0
    },
    {
      label: (
        <Popover content={dateContent} trigger="click" placement="bottomLeft" arrow={false} overlayClassName="mt-1">
          Date
        </Popover>
      ),
      key: 'date',
      onCancel: () => {
        setQueryFilter({
          ...queryFilter,
          date: null
        });
      },
      active: () => queryFilter.date != null
    }
  ];

  // Email Query
  const { data, loading, refetch } = Gql.useGetEmailsQuery({
    variables: {
      ...queries,
      filter: {
        projectId: { eq: projectId },
        ...(id && { id: { eq: id.toString() } }),
        ...(userRole === Gql.ProjectUserRoleType.CanEdit || userRole === Gql.ProjectUserRoleType.CanView ? {
          or: [
            { recipientId: { eq: user?.getUserMe?.id } },
            { senderId: { eq: user?.getUserMe?.id } }
          ]
        } : {}),

      },
      sorting: [
        {
          direction: Gql.SortDirection.Desc,
          field: Gql.EmailSortFields.CreatedAt
        }
      ],
    },
    onError: onError
  });


  useEffect(() => {
    let deliveryStatuses = [] as Gql.EmailDeliveryStatus[];

    let replied = undefined;
    let hasRepliedStatus = queryFilter.status.includes('Replied');
    let hasReadStatus = queryFilter.status.includes('Read');
    let hasUnreadStatus = queryFilter.status.includes('Unread');

    if (queryFilter.status.length > 0) {
      queryFilter.status.forEach((status: string) => {
        switch (status) {
          case 'Replied':
            replied = true;
            break;
          case 'Unread':
            deliveryStatuses.push(Gql.EmailDeliveryStatus.Accepted, Gql.EmailDeliveryStatus.Delivered);
            if (!hasRepliedStatus) replied = false;
            break;
          case 'Read':
            deliveryStatuses.push(Gql.EmailDeliveryStatus.Clicked, Gql.EmailDeliveryStatus.Opened);
            if (!hasRepliedStatus) replied = false;
            break;
        }
      });
    }

    // If both Replied and Read/Unread are selected, we need to handle differently
    if (hasRepliedStatus && (hasReadStatus || hasUnreadStatus)) {
      replied = undefined; // Don't filter by reply status

      // We'll handle the combined filter in the GraphQL query with OR conditions
      const filterConditions = [];

      // Add replied condition
      filterConditions.push({ replyAt: { isNot: null } });

      // Add delivery status conditions
      if (deliveryStatuses.length > 0) {
        filterConditions.push({ deliveryStatus: { in: deliveryStatuses } });
      }

      // Update the filter data with the OR conditions
      const dateRange = {
        lower: queryFilter.date?.startOf('day').toISOString(),
        upper: queryFilter.date?.endOf('day').toISOString()
      };

      const filterData = {
        projectId: { eq: localStorage.getItem('ProjectId') },
        ...(queryFilter.sentToMe && user?.getUserMe?.id && { receivers: { userId: { eq: parseInt(user?.getUserMe?.id) } } }),
        ...(queryFilter.sentByMe && user?.getUserMe?.id && { createdBy: { eq: parseInt(user?.getUserMe?.id) } }),
        ...(queryFilter.date && { createdAt: { between: dateRange } }),
        ...(id && { id: { eq: id.toString() } }),
        ...(queryFilter.search && {
          or: [
            { subject: { like: `%${queryFilter.search}%` } },
            { body: { like: `%${queryFilter.search}%` } },
            { reference: { like: `%${queryFilter.search}%` } }
          ]
        }),
        or: filterConditions
      };

      refetch({
        filter: {
          projectId: filterData.projectId,
          createdBy: filterData.createdBy,
          createdAt: filterData.createdAt,
          id: filterData.id,
          receivers: filterData.receivers,
          ...(userRole === Gql.ProjectUserRoleType.CanEdit || userRole === Gql.ProjectUserRoleType.CanView ? {
            or: [
              { recipientId: { eq: user?.getUserMe?.id } },
              { senderId: { eq: user?.getUserMe?.id } }
            ]
          } : {}),
          ...(queryFilter.search && {
            or: [
              { subject: { like: `%${queryFilter.search}%` } },
              { body: { like: `%${queryFilter.search}%` } },
              { reference: { like: `%${queryFilter.search}%` } }
            ]
          }),
          or: filterConditions
        }
      });
    } else {
      // Original logic for single status filters
      const dateRange = {
        lower: queryFilter.date?.startOf('day').toISOString(),
        upper: queryFilter.date?.endOf('day').toISOString()
      };

      const filterData = {
        projectId: { eq: localStorage.getItem('ProjectId') },
        ...(queryFilter.sentToMe && user?.getUserMe?.id && { receivers: { userId: { eq: parseInt(user?.getUserMe?.id) } } }),
        ...(queryFilter.sentByMe && user?.getUserMe?.id && { createdBy: { eq: parseInt(user?.getUserMe?.id) } }),
        ...(deliveryStatuses.length > 0 && { deliveryStatus: { in: deliveryStatuses } }),
        ...(replied !== undefined && { replyAt: replied ? { isNot: null } : { is: null } }),
        ...(queryFilter.date && { createdAt: { between: dateRange } }),
        ...(id && { id: { eq: id.toString() } }),
        ...(queryFilter.search && {
          or: [
            { subject: { like: `%${queryFilter.search}%` } },
            { body: { like: `%${queryFilter.search}%` } },
            { reference: { like: `%${queryFilter.search}%` } }
          ]
        })
      };

      refetch({
        filter: {
          projectId: filterData.projectId,
          createdBy: filterData.createdBy,
          deliveryStatus: filterData.deliveryStatus,
          createdAt: filterData.createdAt,
          id: filterData.id,
          replyAt: filterData.replyAt,
          receivers: filterData.receivers,
          ...(userRole === Gql.ProjectUserRoleType.CanEdit || userRole === Gql.ProjectUserRoleType.CanView ? {
            or: [
              { recipientId: { eq: user?.getUserMe?.id } },
              { senderId: { eq: user?.getUserMe?.id } }
            ]
          } : {}),
          ...(queryFilter.search && {
            or: [
              { subject: { like: `%${queryFilter.search}%` } },
              { body: { like: `%${queryFilter.search}%` } },
              { reference: { like: `%${queryFilter.search}%` } }
            ]
          })
        }
      });
    }

    setProjectId(localStorage.getItem('ProjectId') || '')
  }, [queryFilter]);

  const getEmails = data?.emails?.nodes || [];  

  //get project
  const { data: projectData } = Gql.useProjectQuery({
    variables: {
      id: projectId as string
    },
    skip: !projectId,
    onError: onError
  });

  // Create email mutation
  const [createEmailContact, { loading: creatingEmail }] = Gql.useCreateOneContactsEmailMutation({
    onError: (error: ApolloError) => {
      onError(error);
    },
    onCompleted: () => {
      message.success('Non member created successfully');
      closeNonMemberModal();
      composeRef?.current?.refetch()
    }
  });

  const onFinish = async (val: any) => {

    await createEmailContact({
      variables: {
        input: {
          contactsEmail: {
            email: val?.email,
            name: val?.name,
            position: val?.position,
            company: val?.company,
            projectId: projectId as string
          }
        }
      }
    });
  };

  const onCancel = () => {
    closeNonMemberModal();
    form?.resetFields();
  }

  const [viewDocumentModal, showViewDocumentModal, closeViewDocumentModal] = useModal({
    title: '',
    width: '80vw',
    content: (
      <div>
        <DocViewer
          documents={fileUrl}
          pluginRenderers={DocViewerRenderers}
          config={{
            header: {
              disableHeader: true
            }
          }}
        />
      </div>
    )
  });

  // updateOneEmail mutation
  const [updateOneEmail, { loading: updatingEmail }] = Gql.useUpdateOneEmailMutation({
    onError: onError as any,
    onCompleted: (res) => {
      refetch();
      referenceVisibility && message.success('Email reference updated successfully');
      setEmail(email ? { ...email, reference: res.updateOneEmail.reference } as Gql.Email : null);
      setReferenceVisibility(false)
    }
  });

  const showConfirmReference = () => {
    confirm({
      title: 'Set Reference Code',
      icon: null,
      closable: true,
      content: (
        <div>
          <p className='mb-4'>
            You are about to set the reference code for this letter. This code can only be set once and cannot be changed later. Please confirm the code is correct.
          </p>

          <Text strong>
            Reference Code: {referenceForm?.getFieldValue('reference')}
          </Text>
        </div>
      ),
      okText: 'Confirm',
      cancelText: 'Cancel',
      onOk() {
        referenceForm.submit()
      },
      onCancel() { }
    });
  };

  // modal non-member
  const [nonMemberModal, showNonMemberModal, closeNonMemberModal] = useModal({
    title: '',
    content:
      <div className='flex flex-col gap-4' >
        <Center className='justify-between'>
          <Text className='text-2xl' strong>Add non-member</Text>
          <Icon name='close'
            fill='#C94C4F'
            width={20}
            onClick={onCancel} />
        </Center>

        <Text className='text-'>Non-members can receive and respond via email. They will not have access to the project in the app.</Text>

        <Form
          form={form}
          onFinish={onFinish}
          layout="vertical"
          requiredMark={false}
        >
          {/* email, name , company, position */}
          <Form.Item name="email"
            label="Email"
            className='font-semibold'
            rules={[{ required: true, message: 'Email is required!' }]}>
            <Input placeholder="Enter email" />
          </Form.Item>

          <Form.Item
            name="name"
            label="Name"
            className='font-semibold'
            rules={[{ required: true, message: 'Name is required!' }]}>
            <Input placeholder="Enter name" />
          </Form.Item>

          <Form.Item
            name="position"
            label="Position"
            className='font-semibold'
          >
            <Input placeholder="Enter position" />
          </Form.Item>

          <Form.Item
            name="company"
            label="Company"
            className='font-semibold'
          >
            <Input placeholder="Enter company" />
          </Form.Item>
        </Form>

        <div className='flex justify-end'>
          <Button
            type="primary"
            onClick={form?.submit}
            loading={loading}
          >
            Save
          </Button>
          <Button
            loading={loading}
            type="default"
            onClick={onCancel}
            className="ml-3"
          >
            Cancel
          </Button>
        </div>
      </div>
  });

  const columns = [
    !email && {
      title: 'Reference',
      dataIndex: 'reference',
      key: 'reference',
      width: 110,
      render: (reference: string) => {
        return (
          <div className="flex ">
            <div className="text-gray600">{reference}</div>
          </div>
        );
      }
    },
    {
      title: <p className={email ? 'text-[#535862]' : 'black'}>Letter</p>,
      key: 'letter',
      render: (data: Gql.EmailFieldsFragment) => {
        const attachments = data?.assets
        return (
          <div className="cursor-pointer">
            <div className="font-bold">{data?.subject}</div>

            <div
              className="truncate-text"
              style={{
                display: "-webkit-box",
                WebkitBoxOrient: "vertical",
                overflow: "hidden",
                WebkitLineClamp: 2,
                maxWidth: "100%",
                wordBreak: "break-word",
              }}
            >
              {parse((data?.body as string) || "")}
            </div>

            <div className='flex items-center gap-2'>
              <div className={`mt-1 grid grid-cols-${email ? '2' : '3'} gap-2 max-w-[40em]`}>
                {attachments?.slice(0, showAllAttachments ? undefined : 3).map(({ id, name, url, fileExtension }) => (
                  <AttachmentTag
                    key={id}
                    id={id}
                    name={name}
                    url={url}
                    fileExtension={fileExtension}
                    onClick={() => {
                      setFileUrl([{ uri: url, fileType: fileExtension }]);
                      showViewDocumentModal();
                    }}
                  />
                ))}
              </div>
              {!showAllAttachments && attachments && attachments.length > 3 && (
                <div>
                  <Button
                    shape="circle"
                    className="cursor-pointer bg-[#E9F3FB] text-[#535862] border-none shadow-md !min-w-[35px]"
                    onClick={(e) => {
                      e.stopPropagation();
                      setShowAllAttachments(true)
                    }
                    }>
                    +{attachments.length - 3}
                  </Button>
                </div>
              )}
            </div>

          </div>
        );
      }
    },
    !email && {
      title: 'Status',
      key: 'deliveryStatus',
      width: 110,
      render: (record: Gql.EmailFieldsFragment) => {
        const { deliveryStatus, replyAt } = record;

        let status;
        let icon: any;

        if (replyAt) {
          status = 'Replied';
          icon = 'replied';
        } else if (
          deliveryStatus === Gql.EmailDeliveryStatus.Opened ||
          deliveryStatus === Gql.EmailDeliveryStatus.Clicked
        ) {
          status = 'Read';
          icon = 'read';
        } else if (deliveryStatus === Gql.EmailDeliveryStatus.Sending) {
          status = 'Sending';
        } else if (
          deliveryStatus === Gql.EmailDeliveryStatus.TemporaryFailures ||
          deliveryStatus === Gql.EmailDeliveryStatus.PermanentFailures
        ) {
          status = 'Failed';
          icon = 'failed';
        } else if (
          deliveryStatus === Gql.EmailDeliveryStatus.Delivered ||
          deliveryStatus === Gql.EmailDeliveryStatus.Accepted
        ) {
          status = 'Unread';
          icon = 'unread';
        }

        return (
          <Tag className="bg-white">
            <Icon name={icon} /> {status as any}
          </Tag>
        );
      }
    },
    !email && {
      title: 'From',
      key: 'sender',
      width: 166,
      render: (record: Gql.EmailFieldsFragment) => {
        const name = record?.sender?.name?.replace('(Non member)', '').trim();
        return (
          <div>
            <Tooltip title={record?.sender?.name} placement="bottom">
              {record?.sender?.avatar ? (
                <div className='flex'>
                  <UserAvatar
                    src={record?.sender?.avatar}
                    username={nameAlias(record?.sender?.name)}
                  />
                  <div className="overflow-hidden text-ellipsis w-[80px] text-nowrap mt-1 ml-1" style={{ whiteSpace: 'nowrap' }}>
                    {record?.sender?.name}
                  </div>
                </div>
              ) : (
                <div className="flex">
                  <UserAvatar
                    style={{ backgroundColor: record?.sender?.color || '', color: '#ffffff' }}
                    username={nameAlias(record?.sender?.name)}
                  />
                  <div className="overflow-hidden text-ellipsis w-[80px] text-nowrap mt-1 ml-1" style={{ whiteSpace: 'nowrap' }}>
                    {record?.sender?.name}
                  </div>
                </div>
              )}
            </Tooltip>
          </div>
        );
      }
    },
    !email && {
      title: 'Recipients',
      key: 'receivers',
      width: 166,
      render: (record: Gql.EmailFieldsFragment) => {
        const listedRecipients = concatenateRecipients(record?.receivers?.nodes);
        return (
          <div className="pr-4">
            <Avatar.Group className="grid grid-cols-8 pr-[8px] evenly-spaced">

              {listedRecipients?.length === 0 &&
                <div className="flex">
                  <Tooltip title={projectData?.project?.projectEmail} placement="bottom" className='text-[#6092a9]' >
                    <span style={{ whiteSpace: 'nowrap' }}>Project Email</span>
                  </Tooltip>
                </div>
              }

              {listedRecipients.length === 1 ? (
                <div className="flex">
                  <Tooltip title={listedRecipients[0]?.name} placement="bottom" className="flex">
                    {listedRecipients[0]?.avatar ? (
                     <UserAvatar
                     src={listedRecipients[0]?.avatar}
                     username={nameAlias(listedRecipients[0]?.name)}
                   />
                 ) : (
                   <UserAvatar
                     style={{
                       backgroundColor: listedRecipients[0]?.color || '', // Use color from GraphQL or fallback
                       color: '#ffffff',
                     }}
                     username={nameAlias(listedRecipients[0]?.name)}
                   />
                 )}
                 <div
                   className="overflow-hidden text-ellipsis w-16 text-nowrap mt-1 ml-1"
                   style={{ whiteSpace: 'nowrap' }}
                 >
                   {listedRecipients[0]?.name}
                 </div>
               </Tooltip>
             </div>
           ) : (
                <>
                  {listedRecipients.map((receiver: any, index: any) => {
                    return (
                      <Tooltip key={index} title={receiver?.title || receiver?.name} placement="bottom">
                      {receiver?.avatar ? (
                        <UserAvatar
                          src={receiver?.avatar}
                          username={nameAlias(receiver?.name)}
                        />
                      ) : (
                        <UserAvatar
                          style={{
                            backgroundColor: receiver?.color || '', // Use color from GraphQL or fallback
                            color: '#ffffff',
                          }}
                          username={nameAlias(receiver?.name)}
                        />
                      )}
                    </Tooltip>
                    );
                  })}
                </>
              )}
            </Avatar.Group>
          </div>
        );
      }
    },
    !email && {
      title: 'Sent date',
      dataIndex: 'createdAt',
      key: 'sentDate',
      width: 140,
      render: (createdAt: string) => (createdAt ? moment(createdAt).format('DD MMM YYYY') : '-')
    }
  ].filter(Boolean);

  const copyToClipboard = () => {
    if (projectData?.project?.projectEmail)
      navigator.clipboard.writeText(projectData?.project?.projectEmail).then(() => message.info('copied'));
  };

  const popoverContent = (
    <div className=" w-[450px]">
      <h3 className="text-lg font-semibold text-gray600">
        Store project correspondence by sending or forwarding emails to:
      </h3>

      <div className="flex mt-4">
        <div className="border-[1px] border-solid border-gray500 rounded-lg p-2 w-96">
          {projectData?.project?.projectEmail}
        </div>
        <Icon name="copy" className="cursor-pointer ml-2 mt-2" onClick={copyToClipboard} />
      </div>
    </div>
  );

  const saveReference = async (val: any) => {
    const { reference } = val;

    await updateOneEmail({
      variables: {
        input: {
          id: email?.id as string,
          update: {
            reference
          }
        }
      }
    });
  };

  const onRow = (record: any) => {
    return {
      onClick: () => {
        setEmail(record);
        // if unread update to read
        if ((record?.deliveryStatus === Gql.EmailDeliveryStatus.Delivered || record?.deliveryStatus === Gql.EmailDeliveryStatus.Accepted) && !record.createdBy) {

          updateOneEmail({
            variables: {
              input: {
                id: record.id,
                update: {
                  deliveryStatus: Gql.EmailDeliveryStatus.Opened
                }
              }
            }
          });
        }
      },
      className: `${email?.id === record.id ? 'hover:bg-[#D1E5F4] bg-[#D1E5F4]' : ''}`,
    };
  }

  const options = {
    replace: (domNode: any) => {
      if (domNode.type === 'tag' && domNode.name === 'p') {
        const children = domToReact(domNode.children);
        const content = children.toString().replace(/\u00A0|\s/g, '');

        if (!content) return null;

        return (
          <p style={{ marginBottom: '1em' }}>
            {children}
          </p>
        );
      }
    }
  };

  return (
    <div className="mx-4 text-gray600 px-5 dashboard-table mb-[100px] overflow-auto min-h-[85%] ">
      {nonMemberModal}
      {viewDocumentModal}
      <Center className=' justify-between mt-2 mb-4'>
        <h3 className='text-2xl text-black'>Correspondence</h3>
        <div className="flex">
          <Popover content={popoverContent} trigger="click" arrow={false} placement="bottomRight">
            <Button type="default" className="mr-3 mt-[2px] flex">
              Project email address <Icon name="info" className="w-3  ml-2" />
            </Button>
          </Popover>
          <Button
            type="primary"
            className="h-[35px] rounded-lg border-gray40"
            onClick={() => setShowComposeEmail(!showComposeEmail)}
          >
            <Center>
              <Icon name="compose-email" className="mr-2" /> Compose
            </Center>
          </Button>
        </div>
      </Center>

      <div className="flex">
        <div className="flex justify-between w-full">
          <div className="flex h-[35px]">
            {filters.map(filter => (
              <div
                key={filter.key}
                className={`mr-2 rounded-full border shadow-md border-solid px-4 py-2  cursor-pointer hover:bg-slate-100 flex ${filter.active() ? 'text-blue-500 bg-blue-100 border-blue-100' : 'text-gray600 bg-white border-gray500'
                  }`}
                onClick={filter?.onClick}
              >
                {filter.label}
                {filter.active() && (
                  <Icon
                    name="close"
                    className="ml-3 text-sm relative top-[2px] text-blue-500"
                    onClick={(e: Event) => {
                      e.stopPropagation();
                      filter.onCancel();
                    }}
                  />
                )}
              </div>
            ))}
          </div>

          <Form form={searchForm}>
            <div>
              <SearchInput
                onChange={debounce((val: any) => {
                  setQueryFilter({
                    ...queryFilter,
                    search: val.target.value
                  });
                }, 1000)}
                placeholder={'Search'}
              />
            </div>
          </Form>
        </div>
      </div>

      <div className='flex h-[calc(100vh-230px)]'>
        {getEmails && <Table
          dataSource={getEmails}
          columns={columns as any}
          pagination={{
            defaultPageSize: 10,
            pageSize: queries.paging.limit,
            current: queries.paging.offset / queries.paging.limit + 1,
            total: data?.emails.totalCount ?? 0,
            position: ['bottomRight']
          }}
          className={`correspondence-table ${!email ? 'w-full' : 'w-[25%]'}`}
          onRow={onRow}
          size="small"
          tableLayout="fixed"
          scroll={{ y: 'calc(100vh-300px)' }}
          onChange={paginate => {
            const { current, pageSize } = paginate;

            if (pageSize !== undefined && current !== undefined) {
              setPaging({
                offset: (current - 1) * pageSize,
                limit: pageSize
              });
              setQueryFilter({
                ...queryFilter
              });
            }
          }}
        />}

        {email &&
          <div
            className={`${id ? 'w-full' : 'w-3/4'} p-4 bg-[#FAFAFA] h-full overflow-y-auto`}
            style={{ maxHeight: 'calc(100vh-250px)', overflowY: 'auto' }}>
            <Center className="justify-between">
              <p className="text-2xl font-semibold">{email?.subject}</p>
              <Button
                type='default'
                className='bg-transparent border-none'
                onClick={() => {
                  setEmail(null)
                  setShowAllAttachments(false)
                  // remove id from url
                  router.replace(router.pathname)
                }}
              >
                <Icon name='close' />
              </Button>
            </Center>


            {email?.replies && email.replies.length > 0 && email.replies.map((reply) => (
              <Card
                key={reply.id}
                className='my-2'
                size='small'
              >
                <div className="flex justify-between items-center">

                  <Text
                    className='text-binaBlue cursor-pointer'
                    strong
                    onClick={() => {
                      router.replace({ ...router.query, query: { id: reply.id } })
                      setEmail(null)
                    }}
                  >
                    View reply
                  </Text>
                  <Text className="text-gray-500">
                    {moment(reply.createdAt).format('DD MMM YYYY, h:mm A')}
                  </Text>
                </div>
              </Card>
            ))}

            <Card className="rounded-t-lg rounded-b-none" loading={loading}>
              <Descriptions column={1} labelStyle={{ width: "250px" }} colon={false} size="small">
                <Descriptions.Item label="From">{email?.sender?.name} &lt;{email?.sender?.email}&gt;</Descriptions.Item>
                <Descriptions.Item label="To">
                  <div className='grid grid-cols-2 gap-2'>
                    {email?.receivers?.nodes?.length > 0 ? (
                      email.receivers.nodes.map((receiver: any) => (
                        <Tag key={receiver.id}> {receiver.name} &lt;{receiver.email}&gt;</Tag>
                      ))
                    ) : (
                      <Tag>{projectData?.project?.projectEmail}</Tag>
                    )}
                  </div>
                </Descriptions.Item>
                <Descriptions.Item label="Subject">{email?.subject}</Descriptions.Item>
                <Descriptions.Item label="Sender Reference">{email?.reference ? email?.reference :
                  referenceVisibility ?
                    <Form form={referenceForm} onFinish={saveReference}>
                      <div className='flex gap-2'>
                        <Form.Item name='reference' className='m-0 p-0' rules={[{ required: true }]} >
                          <Input placeholder="Enter reference" className='h-[32px]' />
                        </Form.Item>
                        <Button
                          onClick={() => showConfirmReference()}
                          loading={updatingEmail}
                          className='set-ref-button py-0'
                          type='primary'
                        >Set Code</Button>
                      </div>
                    </Form> :
                    <Button
                      className='bg-[#FEE9E7] '
                      type='link'
                      size='small'
                      onClick={() => setReferenceVisibility(true)}
                    >
                      <Center className='gap-2'>
                        <Text
                          className=' text-[#C00F0C] hover:text-[#C00F0C]'>
                          Set reference code
                        </Text>
                        <Icon name='set-ref-code' />
                      </Center>
                    </Button>
                }
                </Descriptions.Item>
                {email?.replyToId &&
                  <Descriptions.Item label="Receiver Reference">
                    <Button
                      type='link'
                      className='p-0 m-0'
                      onClick={() => router.replace({ ...router.query, query: { id: email?.parents?.[0]?.id } })}>
                      {email?.parents?.[0]?.reference}
                    </Button>
                  </Descriptions.Item>}
                <Descriptions.Item label="Date">
                  {moment(email?.createdAt).format('DD MMM YYYY [at] hh:mm A')}
                </Descriptions.Item>
              </Descriptions>

              {/* show attachments */}
              {(email?.assets && email.assets.length > 0) && <div className="mt-4 max-w-[55em]">
                <Text strong className="mb-2 block">{email?.assets?.length || 0} Attachments</Text>
                <div className="grid grid-cols-4 gap-2">
                  {(showAllAttachments ? email?.assets : email?.assets?.slice(0, 4))?.map(({ id, name, url, fileExtension }) => {

                    return (
                      <AttachmentTag
                        key={id}
                        id={id}
                        name={name}
                        url={url}
                        fileExtension={fileExtension}
                        onClick={(url, fileExtension) => {
                          setFileUrl([{ uri: url, fileType: fileExtension }]);
                          showViewDocumentModal();
                        }}
                      />
                    )
                  })}
                </div>

                <div className='flex items-center'>
                  {!showAllAttachments && email?.assets && email.assets.length > 4 && (
                    <div
                      className="mt-2 p-0 text-gray70 text-md cursor-pointer"
                      onClick={() => setShowAllAttachments(true)}
                    >
                      <Icon name='chevron-down' className='mr-2' /> Show all {email.assets.length - 4} attachment{email.assets.length - 4 > 1 ? 's' : ''}
                    </div>
                  )}

                  <Button
                    type="link"
                    className="mt-2 text-gray70 !font-normal items-center"
                    onClick={async (e) => {
                      e.stopPropagation();
                      await downloadAllAttachments(email?.assets)
                    }}
                  >
                    <Icon name='download' className='mr-2' /> Download All
                  </Button>
                </div>
              </div>}

            </Card>

            <Card className="rounded-t-none rounded-b-lg" loading={loading}>
              <div
                className="min-h-[300px] overflow-auto break-words"
                style={{ overflowY: "auto" }}
              >
                {parse(
                  (email?.parsedBody as string || "")
                    .replace(/&nbsp;/g, ' ')
                    .replace(/(\w)\n(\w)/g, '$1 $2')
                    .replace(/\n/g, ''),
                  options
                )}

              </div>
            </Card>

            {!showReply && <Button className="mt-5" type="primary" onClick={() => {
              setShowReply(true)
            }}>
              <div className="flex items-center gap-2">
                <Icon name="reply" />
                Reply
              </div>
            </Button>}

            <ReplyCorrespondence
              ref={replyRef}
              show={showReply}
              email={email}
              onClose={() => setShowReply(false)}
              onNonMemberModalClick={showNonMemberModal}
              onRefetch={refetch}
            />
          </div>}
      </div>

      <ComposeEmail
        ref={composeRef}
        show={showComposeEmail}
        onClose={() => setShowComposeEmail(false)}
        onNonMemberModalClick={showNonMemberModal}
        onRefetch={refetch}
      />
    </div>
  )
};

ProjectCorrespondence.auth = true;
export default ProjectCorrespondence;