import { ExclamationCircleOutlined, TeamOutlined } from '@ant-design/icons';
import { Icon } from '@commons';
import CreateProjectModal from '@components/CreateProjectModal';
import EditProjectModal from '@components/EditProjectModal';
import ThreeDotsDropDown from '@components/ThreeDotsDropDown';
import SearchInput from '@components/forms/FormsInput/SearchInput';
import useModal from '@components/hooks/useModal';
import useQueries from '@components/hooks/useQueries';
import { ProjectAccess } from '@constants/subscription';
import * as Gql from '@graphql';
import { isAllowed } from '@lib/helper';
import { getErrorMessage, onError } from '@utils/error';
import { Alert, Button, Card, Carousel, Col, Form, Input, message, Modal, Pagination, Row, Space, Spin, Typography } from 'antd';
import 'antd/dist/reset.css';
import { useForm } from 'antd/lib/form/Form';
import Title from 'antd/lib/typography/Title';
import _ from 'lodash';
import moment from 'moment';
import { useRouter } from 'next/router';
import { useContext, useEffect, useMemo, useRef, useState } from 'react';
import { SystemService, UserAuthService } from 'src/api';
import { AppLayoutContext } from './_app';

const { Text } = Typography;


const Dashboard = () => {
  const router = useRouter();
  const { confirm } = Modal;
  const createProjectModalRef = useRef<any>(null);
  const editProjectModalRef = useRef<any>(null);
  const [searchForm] = useForm();

  const [pageIndex, setPageIndex] = useState(0);
  const [pageSize, setPageSize] = useState(10);

  const [project, setProject] = useState<any>({});

  const [newGroupForm] = useForm();

  const { headerBanner, userData: user, setHeaderBanner } = useContext(AppLayoutContext);
  const { graceBanner, setGraceBanner } = useContext(AppLayoutContext);

  const showBinaAI = useMemo(() => {
    if (router.query.ai === 'true') {
      localStorage.setItem('showBinaAI', 'true');
      return true;
    } else if (router.query.ai === 'false') {
      localStorage.setItem('showBinaAI', 'false');
      return false;
    }
  }, [router.query.ai]);

  //If previously accessed a project, will redirect inside
  useEffect(() => {
    if (!localStorage?.getItem('ProjectId')) return;
    window.location.replace(getFirstAllowedLocation(companySubscriptions));
  }, []);

  const setUserRole = async () => {
    try {
      if (!localStorage?.getItem('ProjectId')) return;

      const role = await UserAuthService.currentRole({});

      localStorage.setItem('ProjectUserRole', role);
    } catch (err) {
      getErrorMessage(err);
    }
  };

  const { queries, setPaging, setFilter } = useQueries<Gql.ProjectFilter, Gql.ProjectSort>();

  const [getProjects, { data, loading, refetch, error }] = Gql.useGetProjectListsLazyQuery({
    variables: {
      ...queries,
      sorting: [
        {
          direction: Gql.SortDirection.Desc,
          field: Gql.ProjectSortFields.CreatedAt
        }
      ]
    }
  });

  const checkServerHealth = async () => {
    try {
      // Check if the page is visible (not in sleep or background)
      if (document.visibilityState !== 'visible' || !navigator.onLine) return;
      const response = await SystemService.alive();

      if (response === 'System is alive') {
      }
    } catch (error) {
      const err = { response: { status: 500, message: 'Server is down', data: { message: 'Server is down' } } };

      onError(err);
    }
  };

  const [getCompany, { data: companyData }] = Gql.useCompanyLazyQuery({
    variables: {
      id: user?.getUserMe?.company?.id ?? ''
    }
  });

  useEffect(() => {
    if (!user?.getUserMe?.company?.id) return;
    Promise.all([checkServerHealth(), getProjects(), getCompany()]);
    const intervalId = setInterval(checkServerHealth, 120000);
    return () => clearInterval(intervalId);
  }, [getCompany, getProjects, user]);

  const companySubscriptions = Gql.useCompanySubscriptionsQuery({
    variables: {
      sorting: {
        direction: Gql.SortDirection.Desc,
        field: Gql.CompanySubscriptionSortFields.SubscriptionEndDate
      },
      paging: {
        limit: 1
      }
    }
  });

  // useEffect(() => {
  //   // NotificationApiService.getNotifications();
  //   if (isGracePeriod) {
  //     const msg = (
  //       <>
  //         Your company subscription has ended on{' '}
  //         {moment(companySubscriptions?.data?.companySubscriptions?.nodes[0]?.subscriptionEndDate).format('DD-MM-YYYY')}
  //         . <a href='https://www.bina.cloud/contacts' target='_blank' rel='noreferrer' style={{ textDecoration: 'underline' }}>Please renew promptly</a> to continue enjoying our services. A 15-day grace period is now in effect.
  //       </>
  //     );
  //     setGraceBanner(
  //       <Alert
  //         type="warning"
  //         message={msg}
  //         banner
  //       // onClick={() => {
  //       //   router.push('/settings/account');
  //       // }}
  //       />
  //     );
  //   }
  // }, [isGracePeriod, setGraceBanner]);

  useEffect(() => {
    if (user?.getUserMe?.company?.id) {
    }
  }, [user]);

  const companyOwner = companyData?.company?.ownerId === user?.getUserMe?.id;

  useEffect(() => {
    if (error?.message) {
      setHeaderBanner(<Alert type="error" message={error?.message} banner />);
    }
  }, [error, setHeaderBanner]);

  const dataSource = data?.projects.nodes;
  const totalCount = data?.projects.totalCount;
  const [ellipsis, setEllipsis] = useState(true);
  const [projectName, setProjectName] = useState('');

  const isGracePeriod = companySubscriptions?.data?.companySubscriptions?.nodes[0]?.isSubscriptionInGracePeriod;
  const subscriptionEndDate = moment(companySubscriptions?.data?.companySubscriptions?.nodes[0]?.subscriptionEndDate).format('DD MMMM, YYYY');
  const remainingDays = 15 - moment().diff(subscriptionEndDate, 'days');
  const showAlertBanner = isGracePeriod;

  const [deleteProject] = Gql.useDeleteOneProjectMutation({
    onCompleted: () => {
      message.success('Deleted successfully');
      refetch();
    },
    onError: (err: any) => {
      message.error(err.message);
    }
  });

  const onDelete = (id: string) => {
    deleteProject({ variables: { id } });
  };

  const showDeleteConfirm = (id: string, title: string) => {
    confirm({
      title: 'Are you sure?',
      icon: <ExclamationCircleOutlined />,
      content: (
        <p>
          Do you really want to delete <span className="font-semibold">{title}</span>? All deleted contents cannot be
          retrieved.
        </p>
      ),
      okText: 'Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk() {
        onDelete(id);
      },
      onCancel() { }
    });
  };

  const [projectNameModal, showProjectNameModal, closeDescModal] = useModal({
    title: 'Project title',
    content: <Input.TextArea autoSize={{ minRows: 1, maxRows: 10 }} value={projectName} readOnly={true} />
  });

  const contentStyle: React.CSSProperties = {
    height: '250px',
    color: '#fff',
    lineHeight: '200px',
    textAlign: 'center',
    width: '100%',
    objectFit: 'cover'
  };

  const onFilter = (values: any) => {
    setFilter({ title: { like: '%' + values.keyword + '%' } });
  };

  const handleCreateProject = () => {
    const isActive = companySubscriptions?.data?.companySubscriptions?.nodes[0]?.isSubscriptionActive;
    if (isActive) {
      createProjectModalRef?.current?.openModal();
    } else {
      message.info({
        content: (
          <>
            You do not have any active subscription now. Please <u>subscribe to a new plan</u>.
          </>
        ),
        onClick: () => {
          router.push('/settings/account');
        }
      });
    }
  };

  return (
    <>
      {projectNameModal}

      <div className="absolute w-full h-full p-5 overflow-auto">
        {showAlertBanner && (
          <div
            className="custom-alert-div p-4 mb-4 rounded-lg flex justify-between items-center"
            style={{
              backgroundColor: '#47819C',
              color: '#FFF',
            }}
          >
            <span>
              <Icon name='exclamation' className='mr-4' />
              <strong>Attention:</strong> Subscription expired on {subscriptionEndDate}. Renew now to ensure continued
              service. You have {remainingDays} days left of the grace period.
            </span>
            <div className='flex gap-5 items-center'>
              <button
                className="bg-white text-black px-4 py-2 rounded-md border-none"
                onClick={() => window.open('https://www.bina.cloud/contacts', '_blank')}
              >
                Renew Now
              </button>
              <p>{remainingDays} days left</p>
            </div>
          </div>
        )}
        <div className="pt-10">
          <div className="flex justify-between mb-[20px] items-center">
            <h2 className="text-[28px] font-medium text-slate-800 lg:w-full md:w-full pl-1">Welcome to All Projects</h2>
            <div className="flex items-center flex-nowrap">
              <div>
                <Form
                  form={searchForm}
                  onFinish={onFilter}
                  className=""
                  onValuesChange={_.debounce(() => {
                    searchForm.submit();
                  }, 300)}
                >
                  <Space>
                    <SearchInput onPressEnter={() => { }} placeholder="Search by title" />
                  </Space>
                </Form>
              </div>
              {companyOwner && (
                <Button
                  type="primary"
                  className="rounded-[8px] ml-[25.33px] w-[158px] h-[48px]"
                  onClick={handleCreateProject}
                >
                  Create Project
                </Button>
              )}
            </div>
          </div>
        </div>
        <Spin tip={'Loading...'} spinning={loading}>
          {!_.isEmpty(dataSource) ? (
            <Row>
              {' '}
              {dataSource?.map((project, index: number) => {
                return (
                  <Col key={index} className="w-1/3 lg:w-1/2 md:w-full sm:w-full">
                    <Card
                      id={project?.title}
                      cover={
                        <Carousel autoplay>
                          {project?.carousels?.nodes.map(option => {
                            return (
                              <div
                                key={option.id}
                                className="justify-center"
                                style={contentStyle}
                                onClick={async () => {
                                  localStorage.setItem('ProjectId', project.id);
                                  localStorage.setItem('ProjectName', project.title);

                                  await setUserRole();
                                  // window.location.replace('/projects/dashboard');
                                  window.location.replace(getFirstAllowedLocation(companySubscriptions));
                                  //
                                }}
                              >
                                <img
                                  style={contentStyle}
                                  className="justify-center"
                                  src={option?.fileUrl ? option?.fileUrl : undefined}
                                  alt={option?.name ? option?.name : undefined}
                                />
                              </div>
                            );
                          })}
                        </Carousel>
                      }
                      style={{
                        borderRadius: 16,
                        margin: 10
                      }}
                      bodyStyle={{ padding: '8px' }}
                      className="cursor-pointer"
                    >
                      <div>
                        <div
                          className="flex items-center text-gray90 mt-[0px]"
                          onClick={async () => {
                            // localStorage.setItem("ProjectId", project.id);
                            // await setUserRole();
                            // window.location.replace("/projects/dashboard");
                          }}
                        >
                          <div className="flex items-center justify-center w-9 h-9">
                            <TeamOutlined />
                            <span className="ml-[5px]">{project.projectUsers?.totalCount}</span>
                          </div>
                          <div className="ml-2 w-full">
                            <div className='flex justify-between'>
                              <Title
                                className="w-[250px]"
                                style={{ margin: 0 }}
                                level={5}
                                ellipsis={ellipsis ? { rows: 1, expandable: false } : false}
                                onClick={e => {
                                  setProjectName(e.currentTarget.innerHTML);
                                  showProjectNameModal();
                                }}
                              >
                                {project.title}
                              </Title>
                              <div className='h-[32px]'>
                                {project.projectUsers?.nodes?.map((projectUser: any, projectUserIndex: number) => {
                                  if (
                                    projectUser?.userId === user?.getUserMe?.id && index !== 1 &&
                                    (projectUser.role === Gql.ProjectUserRoleType.CloudCoordinator ||
                                      projectUser.role === Gql.ProjectUserRoleType.ProjectOwner)
                                  ) {
                                    const items = [{ label: <p>Edit</p>, key: 'Edit' }];
                                    if (projectUser.role === Gql.ProjectUserRoleType.ProjectOwner) {
                                      items.push({
                                        label: <p>Delete</p>,
                                        key: 'Delete'
                                      });
                                    }
                                    return (
                                      <ThreeDotsDropDown
                                        key={projectUserIndex}
                                        onClick={(e: any) => {
                                          localStorage.setItem('ProjectId', project.id);
                                          setUserRole();
                                          if (e.key === 'Delete') {
                                            showDeleteConfirm(project.id, project.title);
                                          }
                                          if (e.key === 'Edit') {
                                            newGroupForm.setFieldsValue(
                                              {
                                                id: project.id,
                                                title: project.title
                                              }
                                            );
                                            setProject(project);
                                            editProjectModalRef?.current?.openModal();
                                          }
                                        }}
                                        items={items}
                                      />
                                    );
                                  }
                                })}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </Card>
                  </Col>
                );
              })}
            </Row>
          ) : (
            <Card size="default" className="flex justify-center h-96">
              <div className="pt-20">
                <div className="flex justify-center">
                  <Icon name="create-project" />
                </div>

                <p className="mt-2 font-bold text-center">No Projects here yet</p>
                <p className="mb-3 text-center text-gray90">Start by creating a project below</p>
                <div className="flex justify-center">
                  <Button
                    type="primary"
                    className="rounded-[8px] w-[158px] h-[48px] justify-center"
                    onClick={handleCreateProject}
                  >
                    Create Project
                  </Button>
                </div>
                
            
              </div>
            </Card>
          )}
          <Pagination
            className="flex justify-end mt-10"
            current={pageIndex}
            total={totalCount}
            onChange={(page, size) => {
              const offset = (page - 1) * size < 0 ? 0 : (page - 1) * size;

              setPageIndex(page);
              setPageSize(size);
              setPaging({
                offset,
                limit: size
              });
            }}
          />
        </Spin>
        <CreateProjectModal ref={createProjectModalRef} onSaved={() => refetch()} />
        <EditProjectModal
          ref={editProjectModalRef}
          onSaved={() => {
            //clear local storage
            localStorage.removeItem('ProjectId');
            refetch();
          }}
          project={project}
        />
      </div>
    </>
  );
};

const getFirstAllowedLocation = (companySubscriptions: any) => {
  if (isAllowed(companySubscriptions, [ProjectAccess.DASHBOARD])) return '/projects/dashboard';

  if (isAllowed(companySubscriptions, [ProjectAccess.TASK])) return '/tasks-overview';

  if (
    isAllowed(
      companySubscriptions,
      [ProjectAccess.PROJECT_DOCUMENT, ProjectAccess.CORRESPONDENCE, ProjectAccess.WORK_PROGRAMME],
      true
    )
  )
    return '/cloud-docs';

  if (isAllowed(companySubscriptions, [ProjectAccess.WORKSPACE_DOCUMENT, ProjectAccess.WORKSPACE_TEMPLATE], true))
    return '/digital-form';

  if (isAllowed(companySubscriptions, [ProjectAccess.DRAWING, ProjectAccess.BIM_MODEL], true)) return '/drawings';

  if (isAllowed(companySubscriptions, ProjectAccess.PHOTO)) return '/photos/recent';

  if (isAllowed(companySubscriptions, ProjectAccess.SCHEDULE_CHART)) return '/schedules';

  return '/projects/dashboard';
};

Dashboard.auth = true;

export default Dashboard;
