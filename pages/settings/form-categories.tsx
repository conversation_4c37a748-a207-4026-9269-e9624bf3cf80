import { ExclamationCircleOutlined } from '@ant-design/icons';
import { useHeader } from '@components/HeaderProvider';
import ThreeDotsDropDown from '@components/ThreeDotsDropDown';
import useModal from '@components/hooks/useModal';
import useQueries from '@components/hooks/useQueries';
import SettingsTabLayout from '@components/settings/SettingsTabLayout';
import * as Gql from '@graphql';
import { onError } from '@utils/error';
import { tz } from '@utils/timezone';
import { Button, Form, Input, Modal, Table, message } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import _ from 'lodash';
import moment from 'moment';
import { useEffect, useState } from 'react';

const FormCategories = () => {
  const { setTitle } = useHeader();
  const [selectedFormId, setSelectedFormId] = useState<string>();
  const [form] = useForm();
  const { confirm } = Modal;

  useEffect(() => {
    setTitle('Settings');
  }, []);

  useEffect(() => {
    const selectedForm = dataSource?.find((form: any) => {
      return form.id === selectedFormId;
    });
    form.setFieldsValue(selectedForm ?? {});
  }, [selectedFormId]);

  const { queries, setPaging } = useQueries<Gql.FormCategoryFilter, Gql.FormCategorySort>();

  const { data, refetch } = Gql.useFormCategoriesQuery({
    variables: {
      ...queries,
      sorting: [
        {
          direction: Gql.SortDirection.Desc,
          field: Gql.FormCategorySortFields.CreatedAt
        }
      ]
    },
    onError: onError
  });

  const dataSource = data?.formCategories.nodes ?? [];
  const totalCount = data?.formCategories.totalCount;

  const [createForm] = Gql.useCreateOneFormCategoryMutation({
    onCompleted: () => {
      form.resetFields();
      message.success('Form category created successfully');
      refetch();
      closeAddModal();
    },
    onError: onError as any
  });

  const [updateForm] = Gql.useUpdateOneFormCategoryMutation({
    onCompleted: () => {
      message.success('Form category updated successfully');
      form.resetFields();
      refetch();
      closeModal();
    },
    onError: onError as any
  });

  const [deleteForm] = Gql.useDeleteOneFormCategoryMutation({
    onCompleted: () => {
      message.success('Deleted successfully');
      refetch();
    },
    onError: onError as any
  });

  const onFinish = (values: Gql.CreateFormCategoryInputDto) => {
    const { name } = values;
    createForm({
      variables: {
        input: {
          formCategory: { name }
        }
      }
    });
    form.resetFields();
  };

  const onUpdate = (values: Gql.UpdateFormCategoryInputDto) => {
    const { name } = values;
    updateForm({
      variables: {
        input: {
          id: _.toString(selectedFormId),
          update: {
            name
          }
        }
      }
    });
  };

  const onDelete = (id: string) => {
    deleteForm({ variables: { id } });
  };

  const showDeleteConfirm = (id: string) => {
    confirm({
      title: 'Are you sure?',
      icon: <ExclamationCircleOutlined />,
      content: 'Do you really want to delete this form categories?',
      okText: 'Yes',
      okType: 'danger',
      cancelText: 'No',
      onOk() {
        onDelete(id);
      },
      onCancel() { }
    });
  };

  // Edit Form Category Modal
  const [modal, showModal, closeModal] = useModal({
    title: 'Edit category',
    content: (
      <Form layout="vertical" form={form} requiredMark={false} onFinish={onUpdate}>
        <Form.Item
          label="Category name"
          name="name"
          rules={[{ required: true, message: 'Category name is required!' }]}
        >
          <Input />
        </Form.Item>
        <div className="flex justify-between">
          <Button
            htmlType="submit"
            className="text-danger"
            onClick={(data: any) => {
              showDeleteConfirm(data.id);
              closeModal();
            }}
          >
            Delete
          </Button>
          <Button htmlType="submit" type="primary">
            Save
          </Button>
        </div>
      </Form>
    )
  });

  // Add Form Category Modal
  const [AddModal, showAddModal, closeAddModal] = useModal({
    title: 'Add category',
    content: (
      <Form layout="vertical" requiredMark={false} onFinish={onFinish}>
        <Form.Item
          label="Category name"
          name="name"
          rules={[{ required: true, message: 'Category name is required!' }]}
        >
          <Input />
        </Form.Item>
        <div className="flex justify-end">
          <Button htmlType="submit" type="primary">
            Create
          </Button>
        </div>
      </Form>
    )
  });

  const columns: any = [
    {
      title: 'CATEGORY NAME',
      key: 'name',
      render: (data: any) => {
        return (
          <div
            className="font-bold cursor-pointer"
            onClick={() => {
              setSelectedFormId(data.id);
              showModal();
            }}
          >
            {data.name}
          </div>
        );
      }
    },
    {
      title: 'CREATED BY',
      dataIndex: 'owner',
      key: 'owner',
      render: (owner: any) => {
        return <p>{owner.name}</p>;
      }
    },
    {
      title: 'LAST UPDATED',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      render: (updatedAt: any) => {
        return <p>{moment(tz(updatedAt)).format('D MMM YYYY')}</p>;
      }
    },
    {
      title: 'CREATED ON',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (createdAt: any) => {
        return <p>{moment(tz(createdAt)).format('D MMM YYYY')}</p>;
      }
    },
    {
      title: '',
      key: 'action',
      render: (data: any) => (
        <ThreeDotsDropDown
          onClick={(e: any) => {
            onDelete(data.id);
          }}
          items={[
            {
              key: '1',
              label: 'Delete'
            }
          ]}
        />
      )
    }
  ];

  return (
    <>
      {modal}
      {AddModal}
      <div className="p-5 absolute overflow-auto h-full w-full">
        <div className="flex justify-between">
          <div>
            <h1 className="text-xl">Form Categories</h1>
          </div>

          <div>
            <Button
              type="primary"
              className="rounded-[8px] bg-primary w-[158px] h-[48px]"
              onClick={() => {
                showAddModal();
              }}
            >
              Add a category
            </Button>
          </div>
        </div>

        <div>
          <Table
            className="dashboard-table mt-4"
            dataSource={dataSource}
            columns={columns}
            size="small"
            pagination={{
              pageSize: queries.paging.limit,
              current: queries.paging.offset / queries.paging.limit + 1,
              total: totalCount ?? 0
            }}
            onChange={paginate => {
              const { current, pageSize } = paginate;
              if (pageSize !== undefined && current !== undefined) {
                setPaging({
                  offset: (current - 1) * pageSize,
                  limit: pageSize
                });
              }
            }}
          />
        </div>
      </div>
    </>
  );
};

FormCategories.auth = true;
FormCategories.Layout = SettingsTabLayout;

export default FormCategories;
