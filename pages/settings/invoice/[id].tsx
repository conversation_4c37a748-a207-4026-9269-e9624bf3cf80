import EmptyLayout from '@components/Layout/EmptyLayout';
import * as Gql from '@graphql';
import { onError } from '@utils/error';
import { tz } from '@utils/timezone';
import moment from 'moment';
import Image from 'next/image';
import { useRouter } from 'next/router';
import { useState } from 'react';

const InvoiceID = () => {
  const router = useRouter();
  const [invoice, setInvoice] = useState<Gql.GetInvoiceQuery['getInvoice']>();

  const fetchInvoice = Gql.useGetInvoiceQuery({
    variables: {
      cuid: router.query.id as string
    },
    onCompleted: data => {
      setInvoice(data.getInvoice);
    },
    onError: err => {
      router.push('/settings/billing');
      onError(err);
    }
  });

  if (fetchInvoice.loading) {
    return <div className="flex justify-center mt-8">Loading...</div>;
  }

  return (
    invoice?.cuid && (
      <div className="mt-24">
        <div className="invoice-box bg-white">
          <table cellPadding="0" cellSpacing="0">
            <tr className="top">
              <td colSpan={2}>
                <table>
                  <tr>
                    <td className="title">
                      <Image src="/assets/new-logo.svg" width={300} height={150} alt="logo" />
                    </td>
                    <td>
                      <small>ID: {invoice?.cuid}</small>
                      <br />
                      Invoice #: {invoice?.invoiceNumber}
                      <br />
                      Paid: {moment(tz(invoice?.updatedAt)).format('DD MMMM YYYY')}
                      {/* <br />
                      Due: February 1, 2015 */}
                    </td>
                  </tr>
                </table>
              </td>
            </tr>

            <tr className="information">
              <td colSpan={2}>
                <table>
                  <tr>
                    <td>
                      Bina Cloudtech Sdn Bhd
                      <br />
                      C-1, 2, Jalan Prima Saujana 2/A,
                      <br />
                      Prima Saujana,
                      <br />
                      43000 Kajang, Selangor
                    </td>

                    <td>
                      {invoice?.company?.name}
                      <br />
                      {invoice?.user?.name}
                      <br />
                      {invoice?.user?.email}
                    </td>
                  </tr>
                </table>
              </td>
            </tr>

            <tr className="heading">
              <td>Payment Method</td>
              <td></td>
            </tr>

            <tr className="details">
              <td>{invoice?.paymentMethod}</td>
              <td></td>
            </tr>

            <tr className="heading">
              <td>Item</td>

              <td>Price</td>
            </tr>

            <tr className="item">
              <td>
                <b>{invoice.subscriptionPackage?.title}</b>
                {'  '}
                <small>
                  {moment(tz(invoice?.updatedAt)).format('DD MMMM YYYY')} {' - '}
                  {moment(tz(invoice?.updatedAt))
                    .add(invoice.subscriptionPackage?.availableDuration, 'month')
                    .format('DD MMMM YYYY')}
                </small>{' '}
                <br />
                <small>{invoice.subscriptionPackage?.description}</small>
              </td>

              <td>RM{invoice.subscriptionPackage?.amount}</td>
            </tr>

            <tr className="total">
              <td></td>

              <td>Total: RM{invoice.subscriptionPackage?.amount}</td>
            </tr>
          </table>
        </div>
      </div>
    )
  );
};

InvoiceID.Layout = EmptyLayout;
export default InvoiceID;
