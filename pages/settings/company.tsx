import { ExclamationCircleFilled } from '@ant-design/icons';
import { useHeader } from '@components/HeaderProvider';
import ImageUploadInput from '@components/forms/FormsInput/ImageUploadInput';
import useModal from '@components/hooks/useModal';
import SettingsTabLayout from '@components/settings/SettingsTabLayout';
import * as Gql from '@graphql';
import { nameAlias } from '@utils/app.utils';
import { onError } from '@utils/error';
import { Avatar, Button, Card, Form, Image, Input, Modal, Spin, Tag, message } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import _ from 'lodash';
// import  from 'next/image';
import { AppLayoutContext } from 'pages/_app';
import { useContext, useEffect, useRef, useState } from 'react';

const Company = () => {
  // const { confirm } = Modal;
  const { setTitle } = useHeader();
  const [form] = useForm();
  const inputRef = useRef<any>(null);
  const [logo, setLogo] = useState<any>(null);
  // const { userData: me, isFetchingUser: fetching, setUserData } = useContext(AppLayoutContext);
  const {
    data: meData,
    loading: fetching,
    refetch
  } = Gql.useGetUserMeQuery({
    onError: onError,
    onCompleted: res => {
      const companyLogo = res?.getUserMe?.company?.logoUrl;
      if (companyLogo !== undefined && companyLogo !== null) {
        setLogo(companyLogo);
      }
    }
  });

  const {
    data,
    loading: fetchingCompany,
    error,
    refetch: refetchCompany
  } = Gql.useGetCompanyUsersQuery({
    fetchPolicy: 'no-cache'
  });

  const [editCompanyMutation, { loading: renaming }] = Gql.useEditCompanyMutation({
    onCompleted: async res => {
      await closeRenameCompanyModal();
      await closeCompanyLogoModal();
      await refetchCompany();
      // await refetch();
      await setLogo(res?.editCompany?.logoUrl);

      message.success('Company edited successfully.');
    },
    onError: e => {
      message.error(e.message);
    }
  });

  // const [removeUserMutation, { loading: removing }] =
  //   Gql.useRemoveCompanyUserMutation({
  //     onCompleted: () => {
  //       message.success("User removed");
  //       refetch();
  //       refetchCompany();
  //     },
  //     onError: (e) => {
  //       message.error(e.message);
  //     },
  //   });

  const onFinish = async (values: any) => {
    if (typeof values.logoUrl === 'string') return;

    await editCompanyMutation({
      variables: {
        input: {
          name: values.companyName,
          logoUrl: values.logoUrl
        }
      }
    });
  };

  const [modal, showRenameCompanyModal, closeRenameCompanyModal] = useModal({
    onCancel: () => form.resetFields(),
    title: 'Rename Company',
    content:
      (setTimeout(() => {
        inputRef?.current?.focus();
      }, 100),
      (
        <Form layout="vertical" form={form} onFinish={onFinish} requiredMark={false}>
          <Form.Item className="text-gray90" label="Company Name" name="companyName">
            <Input className="rounded-lg h-10 w-70" defaultValue={meData?.getUserMe.company?.name || ''} />
          </Form.Item>
          <Button
            htmlType="submit"
            type="primary"
            className="rounded-lg h-10 w-full font-medium"
            loading={renaming}
            disabled={renaming}
          >
            Rename
          </Button>
        </Form>
      ))
  });

  const [logoModal, showCompanyLogoModal, closeCompanyLogoModal] = useModal({
    onCancel: () => form.resetFields(),
    title: 'Change Company Logo',
    content:
      (setTimeout(() => {
        inputRef?.current?.focus();
      }, 100),
      (
        <Form layout="vertical" form={form} onFinish={onFinish} requiredMark={false}>
          <Form.Item className="text-gray90" label="Company Logo" name="logoUrl">
            <ImageUploadInput />
          </Form.Item>

          <Button
            htmlType="submit"
            type="primary"
            className="rounded-lg h-10 w-full font-medium"
            loading={renaming}
            disabled={renaming}
          >
            Edit
          </Button>
        </Form>
      ))
  });

  useEffect(() => {
    setTitle('Settings');
  }, [setTitle]);

  const sortedUsers = data?.getCompanyUsers.sort((a, b) => {
    if (a.id === meData?.getUserMe.id) {
      return -1;
    }
    if (b.id === meData?.getUserMe.id) {
      return 1;
    }
    return 0;
  });

  const loading = fetching || fetchingCompany || renaming;
  // if (loading) return;

  return (
    <div className="p-5 mb-16">
      {modal}
      {logoModal}
      {/* <Spin spinning={loading}> */}
      <div className="flex gap-4">
        <Card
          loading={loading}
          title={meData?.getUserMe.company?.name}
          style={{ width: 640 }}
          className="overflow-hidden max-h-full"
          bodyStyle={{
            height: 400,
            overflowY: 'auto'
          }}
          extra={<a onClick={showRenameCompanyModal}>Rename</a>}
        >
          <div className="flex flex-col gap-y-6">
            {sortedUsers?.map(user => (
              <div className="flex items-center gap-6 justify-between" key={user.id}>
                <div className="flex items-center gap-2">
                  <Avatar src={user.avatar} className='avatar-default' >{nameAlias(user.name)}</Avatar>
                  {user.name}
                  {user.id === meData?.getUserMe?.id && <Tag color="green">Owner</Tag>}
                </div>
                <div></div>
              </div>
            ))}
          </div>
        </Card>

        <Card
          loading={loading}
          style={{ width: 640 }}
          className="overflow-hidden max-h-full "
          extra={
            <a
              onClick={async () => {
                const logoUrl = logo;
                if (logoUrl !== undefined && logoUrl !== null) {
                  await form.setFieldsValue({
                    logoUrl: logoUrl
                  });
                }
                showCompanyLogoModal();
              }}
            >
              {logo ? 'Edit' : 'Add'} Logo
            </a>
          }
        >
          <div className="flex justify-center items-center">
            {logo !== null ? (
              <Image src={logo} alt="company logo" className="w-full" />
            ) : (
              <p className="text-center">No Logo provided</p>
            )}
          </div>
        </Card>
      </div>
      {/* </Spin> */}
      <div className="px-5 pt-[20px] pb-[12px] text-[0.8rem] text-slate-500 italic space-y-2">
        <p className="font-bold">How to add users?</p>
        <p>Users can be added to your company by sending them an project invitation.</p>
      </div>
    </div>
  );
};

Company.auth = true;
Company.Layout = SettingsTabLayout;

export default Company;
