import { useHeader } from '@components/HeaderProvider';
import SecurityForm from '@components/settings/SecurityForm';
import SettingsTabLayout from '@components/settings/SettingsTabLayout';
import SubscriptionCard from '@components/settings/SubscriptionCard';
import * as Gql from '@graphql';
import { onError } from '@utils/error';
import { Button, Card, Divider, Row, Space, Switch, Typography } from 'antd';
import Link from 'next/link';
import { AppLayoutContext } from 'pages/_app';
import { useContext, useEffect, useState } from 'react';
const { Text } = Typography;

const Account = () => {
  const [passwordVisibility, setChangePasswordVisibility] = useState(false);
  const { setTitle } = useHeader();
  const { version } = require('../../package.json');
  const { data: me } = Gql.useGetUserMeQuery({});  

  useEffect(() => {
    setTitle('Settings');
  }, [setTitle]);

  // Check that user logged in with social media accounts do not show secure forms
  const { userData: data } = useContext(AppLayoutContext);
  const companySubscriptions = Gql.useCompanySubscriptionsQuery({
    variables: {
      sorting: {
        direction: Gql.SortDirection.Desc,
        field: Gql.CompanySubscriptionSortFields.SubscriptionEndDate
      },
      paging: {
        limit: 1
      }
    }
  });
  const isSocialMediaAccount = data?.getUserMe?.googleId || data?.getUserMe?.facebookId || data?.getUserMe?.appleId;

  const [updateUserMe] = Gql.useUpdateUserMeMutation({ onError: onError as any });

  return (
    <div className="p-5">
      <Row className="mb-6">
        {/* {data?.getUserMe?.myCompany && (
          <SubscriptionCard
            companySubscriptions={companySubscriptions}
            fetching={fetching || companySubscriptions.loading}
          />
        )} */}
        {!data?.getUserMe?.myCompany &&
          companySubscriptions.data?.companySubscriptions?.nodes[0]?.isSubscriptionActive && (
            <>
              <Space className="w-full text-green">
                You are subscribed under company <b>{data?.getUserMe?.company?.name}</b>
              </Space>
            </>
          )}
        {!data?.getUserMe?.myCompany &&
          !companySubscriptions.data?.companySubscriptions?.nodes[0]?.isSubscriptionActive && (
            <>
              <Space className="w-full text-red-500">
                You company <b>{data?.getUserMe?.company?.name}</b> subscription is expired.
              </Space>
            </>
          )}


      </Row>

      {/* {!isSocialMediaAccount && ( */}
      <Row className="mb-6">
        {!passwordVisibility ? <Card className='w-full'>
          <div className='flex flex-col'>
            <Text strong>Account Security</Text>
            <Divider className='mt-1' />

            <div className='flex flex-col w-full my-4'>
              <Text strong>Email</Text>
              <Text color='#4E616D' >{me?.getUserMe?.email}</Text>
            </div>

            <div className='flex w-full my-4'>
              <div className='flex flex-col flex-1'>
                <Text strong>Password</Text>
                <Text color='#4E616D' >*********</Text>
              </div>
              <Button type='primary' onClick={() => setChangePasswordVisibility(!passwordVisibility)}>Change Password</Button>
            </div>

            <div className='flex'>
              <div className='flex flex-col  w-full'>
                <Text strong>2-Factor Authentication</Text>
                <Text color='#4E616D' >Enhance your account security by requiring a verification code sent to your email when logging in.</Text>
              </div>
              {me && <Switch defaultChecked={me?.getUserMe?.enableTwoFA || false} onChange={async (val) => {
                await updateUserMe({
                  variables: {
                    input: {
                      enableTwoFA: val
                    }
                  }
                })
              }} />}
            </div>


          </div>
        </Card> :
          <SecurityForm />}

      </Row>
      {/* )} */}

      <Row className="mb-6">
        <Card style={{ width: 640 }}>
          <h1 className="font-bold mb-6 text-lg">BINA CLOUDTECH SDN BHD</h1>
          <div className="grid grid-rows-3 grid-flow-col gap-2">
            <Link href="https://bina.cloud/terms-and-conditions" target="_blank">
              Terms & Conditions
            </Link>
            <Link href="https://bina.cloud/privacy_policy" target="_blank">
              Privacy Policy
            </Link>
            {/* <Link href="/Outlet">Outlet and Branch</Link> */}
          </div>
          <div className="mt-2 w-full text-slate-500 text-xs">{`Build: v${version}`}</div>
        </Card>
      </Row>
    </div>
  );
};

Account.auth = true;
Account.Layout = SettingsTabLayout;
export default Account;
