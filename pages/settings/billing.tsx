import useQueries from '@components/hooks/useQueries';
import SettingsTabLayout from '@components/settings/SettingsTabLayout';
import * as Gql from '@graphql';
import { onError } from '@utils/error';
import { Spin, Table, Tag, message } from 'antd';

const Billing = () => {
  const { queries, setPaging } = useQueries<Gql.SalesOrderFilter, Gql.SalesOrderSort>({
    paging: { offset: 0, limit: 10 }
  });

  const { data, loading, refetch } = Gql.useSalesOrdersQuery({
    variables: {
      ...queries,
      sorting: [
        {
          direction: Gql.SortDirection.Desc,
          field: Gql.SalesOrderSortFields.CreatedAt
        }
      ]
    },
    onError: onError
  });

  const statusTagColor = (arg: string = '') => {
    switch (arg) {
      case 'paid':
        return 'green';
      case 'failed':
        return 'red';
      case 'declined':
        return 'red';
      case 'cancelled':
        return 'volcano';
      case 'inprogress':
        return 'orange';
      case 'pending':
        return 'yellow';
      default:
        return 'blue';
    }
  };

  const [getSenangpayPaymentForm] = Gql.useCreateSenangPayPaymentFormLazyQuery();

  const handleMakePayment = async (orderId: string) => {
    const response = await getSenangpayPaymentForm({
      variables: {
        orderId
      }
    });

    if (response.error) {
      message.error(response.error.message);
      return;
    }

    const form = document.createElement('form');
    form.method = 'POST';
    form.action = `${process.env.NEXT_PUBLIC_SENANGPAY_URI}/payment/${process.env.NEXT_PUBLIC_SENANGPAY_MERCHANT_ID}`;

    const data = response.data?.createSenangPayPaymentForm;

    data &&
      data.forEach(item => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = item.name;
        input.value = item.value;

        form.appendChild(input);
      });

    document.body.appendChild(form);
    form.submit();
  };

  return (
    <div className="p-6">
      <Spin spinning={loading} tip="Loading...">
        <Table
          loading={loading}
          dataSource={data?.salesOrders.nodes.map(node => ({
            ...node,
            subscriptionPackage: node.subscriptionPackage?.title
          }))}
          columns={[
            {
              title: 'Invoice #',
              dataIndex: 'invoiceNumber',
              key: 'invoiceNumber',
              render: text => {
                if (!text) return '-';
                return text;
              }
            },
            {
              title: 'Subscription Package',
              dataIndex: 'subscriptionPackage',
              key: 'subscriptionPackage'
            },
            {
              title: 'Date',
              dataIndex: 'updatedAt',
              key: 'updatedAt',
              render: text => {
                return new Date(text).toLocaleDateString();
              }
            },
            {
              title: 'Total',
              dataIndex: 'total',
              key: 'total',
              render: text => {
                return `RM ${text}`;
              }
            },
            {
              title: 'Payment Method',
              dataIndex: 'paymentMethod',
              key: 'paymentMethod',
              render: text => {
                if (!text) return '-';
                return text;
              }
            },
            {
              title: 'Status',
              dataIndex: 'status',
              key: 'status',
              render: text => {
                return <Tag color={statusTagColor(text)}>{text}</Tag>;
              }
            },

            {
              title: '',
              render: (_, record) => {
                switch (record.status) {
                  case 'paid':
                    return (
                      <a href={`/settings/invoice/${record.cuid}`} target="_blank" rel="noreferrer">
                        View Invoice
                      </a>
                    );
                  case 'pending':
                    return (
                      <a
                        onClick={e => {
                          e.preventDefault();
                          handleMakePayment(record.id);
                        }}
                      >
                        Make Payment
                      </a>
                    );
                  default:
                    return <></>;
                }
              }
            }
          ]}
          pagination={{
            current: queries.paging.offset + 1,
            pageSize: queries.paging.limit,
            total: data?.salesOrders.totalCount,
            onChange: (page, pageSize) => {
              const offset = (page - 1) * pageSize;
              setPaging({ offset, limit: pageSize });
            }
          }}
        />
      </Spin>
    </div>
  );
};

Billing.auth = true;
Billing.Layout = SettingsTabLayout;
export default Billing;
