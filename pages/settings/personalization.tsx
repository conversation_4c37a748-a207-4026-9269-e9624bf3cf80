import { useHeader } from '@components/HeaderProvider';
import SettingsTabLayout from '@components/settings/SettingsTabLayout';
import * as Gql from '@graphql';
import { onError } from '@utils/error';
import { Button, Card, Checkbox, Form, Input, InputNumber, message, Select, Skeleton, Switch } from 'antd';
import { useEffect, useState } from 'react';

const { Option } = Select;

const Notifications = () => {
  const [notiForm] = Form.useForm();
  const [pdfForm] = Form.useForm();
  const [preferences] = Form.useForm();
  const [projectId, setProjectId] = useState<string>((localStorage.getItem('ProjectId') as string) || '');
  const projectUserRole = localStorage.getItem('ProjectUserRole');
  const { data: fontSize } = Gql.useGetFontSizeQuery({
    onCompleted(data) {
      pdfForm?.setFieldsValue({
        textSize: data?.getUserMe?.fontSize
      });
    }
  });

  const { data: userPreferences, loading: preferencesLoading } = Gql.useGetUserPreferencesQuery({
    fetchPolicy: 'network-only',
    onCompleted(data) {
      preferences?.setFieldsValue({
        email: data?.getUserPreferences?.channels?.email
      });
    }
  });

  useEffect(() => {
    if (projectId) {
      getProject({ variables: { id: projectId } });
    }
  }, [projectId]);

  const [getProject, { data }] = Gql.useGetProjectLazyQuery({
    onCompleted(data) {
      notiForm?.setFieldsValue({
        isRemindSiteDiary: data?.project?.isRemindSiteDiary,
        isNotify2DUploaded: data?.project?.isNotify2DUploaded
      });
    },
    onError: onError
  });

  const [updateProject] = Gql.useUpdateProjectMutation({
    onCompleted() {
      message.success('Notification settings updated successfully');
    },
    onError: onError as any
  });

  const [updatePreferences] = Gql.useUpdateUserPreferencesMutation({
    onCompleted() {
      message.success('Notification settings updated successfully');
    },
    onError: onError as any
  });

  const [updateUser] = Gql.useUpdateUserMeMutation({
    onCompleted() {
      message.success('PDF Editor settings updated successfully');
    },
    onError: onError as any
  });

  async function onFinishNoti(values: any) {
    return await updateProject({
      variables: {
        id: projectId,
        input: {
          isRemindSiteDiary: values.isRemindSiteDiary,
          isNotify2DUploaded: values.isNotify2DUploaded
        }
      }
    });
  }

  async function onFinishPreferences(values: any) {
    return await updatePreferences({
      variables: {
        input: values.email
      }
    });
  }

  async function onFinishPDF(values: any) {
    return await updateUser({
      variables: {
        input: {
          fontSize: values.textSize
        }
      }
    });
  }

  return (
    <div className="p-9">
      {(projectUserRole === Gql.ProjectUserRoleType.CloudCoordinator ||
        projectUserRole === Gql.ProjectUserRoleType.ProjectOwner) && (
        <Form form={notiForm} onFinish={onFinishNoti}>
          <Card style={{ width: '640px', height: '244px', marginBottom: '24px' }}>
            <div className=" grid grid-rows-3 gap-6 mb-3">
              <div>
                <h1>Notifications</h1>
                <div className="flex flex-col gap-3 mt-10">
                  <div className="flex justify-between">
                    <p>Site Diary Daily Reminder</p>
                    <Form.Item name="isRemindSiteDiary" valuePropName="checked" style={{ marginBottom: '2px' }}>
                      <Switch />
                    </Form.Item>
                  </div>
                </div>
                <div className="flex flex-col gap-3 mt-4">
                  <div className="flex justify-between">
                    <p>2D Drawing New Upload</p>
                    <Form.Item name="isNotify2DUploaded" valuePropName="checked" style={{ marginBottom: '2px' }}>
                      <Switch />
                    </Form.Item>
                  </div>
                </div>
                <Button htmlType="submit" type="primary" className='mt-4'>
                  Save
                </Button>
              </div>
            </div>
          </Card>
        </Form>
      )}

      <Form form={preferences} onFinish={onFinishPreferences}>
        <Card style={{ width: '640px', height: '260px', marginBottom: '24px' }}>
          <div>
            {preferencesLoading ? (
              <>
                <Skeleton active />
                <Skeleton.Button active className="mt-5" />
              </>
            ) : (
              <div>
                <h1>Your notification settings</h1>
                <p>Select where you want to receive notifications</p>

                <div className="flex flex-col gap-3 my-10">
                  <div className="flex justify-between preferences-checkbox">
                    <Form.Item name="email" valuePropName="checked" style={{ marginBottom: '2px' }}>
                      <Checkbox className="">
                        <div className="ml-5">
                          <h3>Email</h3>
                          <p>Receive new updates in your email inbox</p>
                        </div>
                      </Checkbox>
                    </Form.Item>
                  </div>
                </div>
                <Button htmlType="submit" type="primary">
                  Save
                </Button>
              </div>
            )}
          </div>
        </Card>
      </Form>

      <Form form={pdfForm} onFinish={onFinishPDF}>
        <Card style={{ width: '640px', height: '200px' }}>
          <div className=" grid grid-rows-3 gap-6 mb-3">
            <div>
              <h1>PDF Editor</h1>
              <div className="flex flex-col gap-3 mt-10">
                <div className="flex justify-between">
                  <p>Text Size</p>
                  <div className="flex justify-between items-center gap-3">
                    <Form.Item name="textSize" style={{ marginBottom: '2px' }}>
                      <Select style={{ width: '100px' }} placeholder="Select a size" defaultValue={4}>
                        {[...Array(25)].map((_, i) => (
                          <Option key={i} value={4 + i}>
                            {4 + i}
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                    <p>pt</p>
                  </div>
                </div>
              </div>
              <Button htmlType="submit" type="primary">
                Save
              </Button>
            </div>
          </div>
        </Card>
      </Form>
    </div>
  );
};

Notifications.auth = true;
Notifications.Layout = SettingsTabLayout;

export default Notifications;
