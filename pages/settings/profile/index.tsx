import ImageUploadInput from '@components/forms/FormsInput/ImageUploadInput';
import PhoneNumberInput from '@components/forms/FormsInput/PhoneNumberInput';
import { useHeader } from '@components/HeaderProvider';
import SettingsTabLayout from '@components/settings/SettingsTabLayout';
import { USER_SESSION_KEY } from '@constants/auth';
import * as Gql from '@graphql';
import { onError } from '@utils/error';
import { setSessionStorage } from '@utils/sessionStorage';
import { Button, Card, Form, Input, message, Modal, Select, Spin } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import axios from 'axios';
import { isValidPhoneNumber } from 'libphonenumber-js';
import _ from 'lodash';
import { AppLayoutContext } from 'pages/_app';
import { useContext, useEffect } from 'react';
import CompanyStampGenerator from './CompanyStampGenerator';
import DigitalSignature from './DigitalSignature';
import PersonalStampGenerator from './PersonalStampGenerator';
import UserAvatar from '@components/UserAvatar';
const { confirm } = Modal;

const Profile = () => {
  const { setTitle } = useHeader();
  const [form] = useForm();
  const { data: timezones, loading: timezoneLoading } = Gql.useTimezonesQuery({});


  useEffect(() => {
    setTitle('Settings');
  }, [setTitle]);

  const projectUserRole = (localStorage.getItem('ProjectUserRole') as Gql.ProjectUserRoleType) || '';

  // RETRIEVE INFO IF THE USER SIGN IN WITH SOCIAL MEDIA ACCOUNT
  const { data, loading: fetching, refetch } = Gql.useGetUserMeQuery({});

  const [updateUser, { loading: updating }] = Gql.useUpdateUserMeMutation({
    onCompleted: res => {
      refetch();
      message.success('Updated Successfully');
    },
    onError: onError as any
  });

  const onFinish = (values: Gql.UpdateUserInputDto) => {
    const { name, phoneNo, position, avatar, timezoneId, companyOrigin } = values;
    const tz = timezoneId?.toString();
    let input: Gql.UpdateUserInputDto = {
      name,
      phoneNo,
      position,
      ...(_.isString(avatar) ? {} : { avatar }),
      companyOrigin
    };

    if (tz) input.timezoneId = parseInt(tz);

    updateUser({
      variables: {
        input
      }
    }).then(() => {
      refetch().then(result => {
        setSessionStorage(USER_SESSION_KEY, JSON.stringify(result?.data?.getUserMe));
      });
    });
  };

  const initialValues = {
    ...data?.getUserMe,
    companyName: data?.getUserMe?.myCompany?.name,
    companyStampUrl: data?.getUserMe?.company?.stampUrl
  };

  const onResetStamp = async (type: 'Company' | 'Personal' | 'Signature') => {
    confirm({
      title: `${type} Stamp Removal Notice`,
      width: '40%',
      content: (
        <>
          <div>
            Please note, stamp can only be create/upload once due to legal restrictions. To proceed with removal, click{' '}
            <strong>&quot;Confirm&quot; below.</strong> Our team will process your request and notify you upon
            completion.
          </div>
        </>
      ),
      okText: 'Confirm',
      okType: 'danger',
      cancelText: 'Cancel',
      async onOk() {
        let getRequestType = '';
        let msg = '';
        switch (type) {
          case 'Company':
            getRequestType = 'CompanyStampRequest';
            msg = `User: ${data?.getUserMe.name}\nEmail: ${data?.getUserMe.email}\nCompany: ${data?.getUserMe.company?.name}\nRequested to change Company Stamp`;
            break;
          case 'Personal':
            getRequestType = 'PersonalStampRequest';
            msg = `User: ${data?.getUserMe.name}\nEmail: ${data?.getUserMe.email}\nCompany: ${data?.getUserMe.company?.name}\nRequested to change Personal Stamp`;
            break;
          case 'Signature':
            getRequestType = 'SignatureStampRequest';
            msg = `User: ${data?.getUserMe.name}\nEmail: ${data?.getUserMe.email}\nCompany: ${data?.getUserMe.company?.name}\nRequested to change Personal Signature`;
            break;
        }

        if (localStorage.getItem(getRequestType) === 'true') {
          message.warning('You can only submit for approval once. Please wait for the approval.');
          return;
        }

        await axios.post(`/api/slack?mes=${encodeURIComponent(msg ?? '')}`).then(async () => {
          await message.success('Stamp removal request has been submitted');
          await localStorage.setItem(getRequestType, 'true');
        });
      },
      onCancel() {}
    });
  };

  if (fetching) return null;

  return (
    <div className="gap-4 p-5 mb-16 m-4">
      <Card className="w-full mb-2">
        <h1 className="text-xl font-bold mb-4">Profile</h1>
        <Form
          layout="vertical"
          form={form}
          onFinish={onFinish}
          initialValues={initialValues ?? {}}
          requiredMark={false}
        >
            <Form.Item className="text-gray90 mt-2" name="avatar">
              <ImageUploadInput 
              className="flex flex-row mt-2"
              maxSize={7}
              name={form.getFieldValue('name')}
              avatar={true} 
              backgroundColor={data?.getUserMe?.color || ""} // Pass the color from user data
              />
            </Form.Item>
          <div className="flex">
            <div className="col-6 w-full">
              <Form.Item className="text-gray90" label="Full Name" name="name">
                <Input className="h-10 rounded-lg" />
              </Form.Item>

              <Form.Item
                className="text-gray90"
                label="Phone Number"
                name="phoneNo"
                rules={[
                  { required: true, message: 'Phone Number is required!' },
                  {
                    message: 'Invalid Phone Number!',
                    validator: (_, value) => (isValidPhoneNumber(value, 'MY') ? Promise.resolve() : Promise.reject())
                  }
                ]}
              >
                <PhoneNumberInput style={{ width: '100%' }} />
              </Form.Item>

              <Form.Item className="text-gray90" label="Company" name="companyOrigin">
                <Input className="h-10 rounded-lg" />
              </Form.Item>
            </div>
            <div className="col-6 ml-12 w-full">
              <Form.Item className="text-gray90" label="Position" name="position">
                <Input className="h-10 rounded-lg" />
              </Form.Item>

              <Form.Item className="text-gray90" label="Timezone" name="timezoneId">
                <Select
                  defaultValue={
                    data?.getUserMe?.timezone?.id ||
                    timezones?.timezones?.nodes?.find(timezone => timezone.value === 'Asia/Kuala_Lumpur')?.id
                  }
                  className="h-10 rounded-lg"
                  loading={timezoneLoading}
                  disabled={timezoneLoading}
                  showSearch
                  options={timezones?.timezones?.nodes?.map(timezone => ({
                    value: timezone.id,
                    label: `${timezone.name} - ${timezone.value}`
                  }))}
                  placeholder="Search to Select"
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    (option?.label?.toLowerCase?.() ?? '').includes(input?.toLowerCase?.())
                  }
                  filterSort={(optionA, optionB) =>
                    (optionA?.label ?? '')?.toLowerCase?.().localeCompare((optionB?.label ?? '')?.toLowerCase?.())
                  }
                />
              </Form.Item>
            </div>
          </div>
          <div className="flex justify-end w-full">
            <Button
              htmlType="submit"
              type="primary"
              className="w-24 h-10 mr-1 font-medium rounded-lg"
              disabled={updating}
            >
              Save
            </Button>
          </div>
          <Spin spinning={updating}></Spin>
        </Form>
      </Card>

      <div className="flex w-full mt-6">
        <div className="flex flex-col w-1/2 mr-3">
          {(projectUserRole === 'ProjectOwner' ||
            (projectUserRole === 'CloudCoordinator' && initialValues.companyStampUrl)) && (
            <div className="mb-6">
              <CompanyStampGenerator
                stampUrl={initialValues.companyStampUrl as string}
                onResetStamp={
                  projectUserRole === 'ProjectOwner'
                    ? () => {
                        onResetStamp('Company');
                      }
                    : undefined
                }
                companyName={data?.getUserMe?.company?.name}
                onRefetch={refetch}
              />
            </div>
          )}
          <div className="">
            <PersonalStampGenerator
              stampUrl={data?.getUserMe?.stampUrl}
              user={{
                id: data?.getUserMe?.id,
                name: data?.getUserMe?.name,
                position: data?.getUserMe?.position,
                company: data?.getUserMe?.company?.name
              }}
              onRefetch={refetch}
              onResetStamp={() => {
                onResetStamp('Personal');
              }}
            />
          </div>
        </div>
        <div className="ml-3 w-1/2">
          <DigitalSignature
            projectUserRole={projectUserRole}
            signatureUrl={data?.getUserMe?.signUrl}
            onResetStamp={() => {
              onResetStamp('Signature');
            }}
            onRefetch={() => {
              refetch();
            }}
            userId={data?.getUserMe?.id}
          />
        </div>
      </div>
    </div>
  );
};

Profile.auth = true;
Profile.Layout = SettingsTabLayout;

export default Profile;
