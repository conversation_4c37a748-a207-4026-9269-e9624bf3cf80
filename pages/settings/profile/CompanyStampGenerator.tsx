import { ExclamationCircleOutlined } from '@ant-design/icons';
import { Icon } from '@commons';
import FileUpload from '@components/FileUpload';
import useModal from '@components/hooks/useModal';
import UploadUI from '@components/settings/UploadStampUI';
import * as Gql from '@graphql';
import { onError } from '@utils/error';
import { Button, Form, Input, Modal, message } from 'antd';
import html2canvas from 'html2canvas';
import _ from 'lodash';
import React, { useRef, useState } from 'react';
import ReactCurvedText from 'react-curved-text';
import SignatureStampCard from './SignatureStampCard';

type CompanyStampGeneratorProps = {
  stampUrl: string | null | undefined;
  onResetStamp?: () => void;
  companyName: string | null | undefined;
  onRefetch: () => void;
};

const CompanyStampGenerator: React.FC<CompanyStampGeneratorProps> = ({ stampUrl, onResetStamp, companyName, onRefetch }) => {
  const curvedTextRef = useRef<any>(null);
  const companyStampRef = useRef<any>(null);
  const insideTextRef = useRef<any>(null);
  const [form] = Form.useForm();
  const { confirm } = Modal;

  const [previewCompanyName, setPreviewCompanyName] = useState<string>(companyName || '');
  const [previewRegisterNo, setPreviewRegisterNo] = useState<string>('');
  const uploadPhotoModalRef = useRef<any>(null);

  const [updateCompanyStamp, { loading: updatingCompStamp }] = Gql.useUpdateCompanyStampMutation({
    onError: onError as any,
    onCompleted: async (res) => {
      onRefetch();
      await localStorage.setItem('CompanyStampRequest', 'false');
      closeCompanyStampGeneratorModal()
      message.success('Company stamp added successfully.');
    }
  });

  const onCompanyStampFinish = async (values: any) => {
    try {
      await form.resetFields();

      const companyStamp = companyStampRef?.current;
      companyStamp.style.backgroundColor = 'transparent';

      const svgCanvas = curvedTextRef?.current;
      svgCanvas.style.marginLeft = '33px';
      svgCanvas.style.marginTop = '59px';
      svgCanvas.style.transform = 'rotate(70deg)';
      svgCanvas.style.backgroundColor = 'transparent';

      insideTextRef.current.style.marginLeft = '10px';

      // Use html2canvas to capture the HTML content
      const canvas = await html2canvas(companyStamp, { backgroundColor: null });

      // Convert base64 data URL to Blob
      const base64Data = canvas.toDataURL().split(',')[1];
      const buffer = Buffer.from(base64Data, 'base64');
      const blob = new Blob([buffer], { type: 'image/png' });

      // Create a File object from the Blob
      const stampFile = new File([blob], `${companyName}_stamp`, { type: 'image/png' });

      updateCompanyStamp({
        variables: {
          input: {
            stampFile: stampFile
          }
        }
      });

    } catch (e) {
      onError(e);
    }
  };

  const loading = updatingCompStamp;

  const [companyStampGeneratorModal, showCompanyStampGeneratorModal, closeCompanyStampGeneratorModal] = useModal({
    title: 'Company Stamp',
    content:
      (
        <>
          <div className="flex flex-col gap-4 justify-center items-center">
            <div className="m-auto bg-transparent" ref={companyStampRef}>
              <div className=" rounded-full h-[200px] w-[200px] border-[4px] border-solid border-[#2E69C2] align-middle flex justify-center">
                <div className="text-[#2E69C2] self-center  absolute text-xl" ref={insideTextRef}>{_.toUpper(previewRegisterNo)}</div>

                <div className=" absolute rounded-full h-[140px] w-[140px] border-[4px] border-solid border-[#2E69C2]" style={{top: '101px'}}/>

                <div ref={curvedTextRef}>
                  <ReactCurvedText width={200}
                    height={200}
                    cx={103}
                    cy={98}
                    rx={50}
                    ry={50}
                    startOffset='0'
                    reversed={true}
                    text={previewCompanyName.toLocaleUpperCase()}
                    textProps={{ "style": { "fontSize": 18, fontWeight: 700 }, "lengthAdjust": "spacing", "textLength": "280px" }}
                    textPathProps={{ fill: '#2E69C2' }}
                    tspanProps={{ dy: '-27' }}
                    svgProps={{ "style": { "transform": "rotate(-70deg)" } }}
                  />
                </div>
                <div className="absolute bottom-[24.4em]">
                  <Icon name="star" width={20} height={20} />
                </div>
              </div>
            </div>
          </div>

          <div className="flex justify-center my-4 items-center">
            <p className=''>Company Stamp Preview</p>
          </div>

          <Form layout="vertical" className="" form={form} onFinish={onCompanyStampFinish} requiredMark={false}>
            <Form.Item className="text-gray90" label="Company Name" name="name" rules={[{ required: true }]}>
              <Input
                className="rounded-lg h-10 w-70"
                maxLength={50}
                defaultValue={companyName || ''}
                onChange={val => {
                  setPreviewCompanyName(val?.target?.value);
                  form.setFieldValue('name', val?.target?.value);
                }}
              />
            </Form.Item>

            <Form.Item
              className="text-gray90"
              label="Registration no."
              name="registrationNo"
              rules={[{ required: true }]}
            >
              <Input
                className="rounded-lg h-10 w-70"
                maxLength={20}
                onChange={val => {
                  setPreviewRegisterNo(val?.target?.value);
                }}
              />
            </Form.Item>
          </Form>
          <div className="flex justify-center items-center">
            <Button
              onClick={() => confirm({
                title: 'Generate Company Stamp',
                okText: 'Confirm',
                icon: <ExclamationCircleOutlined />,
                content: (
                  <div>
                    <p>Are you sure you want to generate a new company stamp?</p>
                    <p>Click &quot;Confirm&quot; to proceed.</p>
                  </div>
                ),
                onOk: () => form.submit(),
              })}
              type="primary"
              className="rounded-lg my-4 w-1/4 font-medium"
              loading={loading}
            >
              Generate
            </Button>
          </div>
        </>
      )
  });

  const [uploadModal, showUploadModal, closeUploadModal] = useModal({
    title: 'Company Stamp',
    content:
      (
        <UploadUI onRefetch={() => {
          onRefetch()
          closeUploadModal()
        }} type='company' />
      )
  });

  return (
    <>
      {companyStampGeneratorModal}
      {uploadModal}
      <FileUpload
        type={'company-stamp'}
        isSignatureOrStamp={true}
        onSaved={() => {
          onRefetch();
          uploadPhotoModalRef?.current?.closeModal();
        }}
        ref={uploadPhotoModalRef}
        accept={['image/PNG', 'image/png']}
        maxFiles={1}
      />

      <SignatureStampCard
        assetUrl={stampUrl}
        label={"Company Stamp"}
        onCreate={() => {
          showCompanyStampGeneratorModal();
        }}
        onUpload={() => {
          uploadPhotoModalRef?.current?.openModal()
        }}
        onReset={onResetStamp}
      />
    </>
  )
}

export default CompanyStampGenerator
