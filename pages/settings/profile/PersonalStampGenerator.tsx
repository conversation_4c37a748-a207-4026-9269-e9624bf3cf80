import FileUpload from '@components/FileUpload';
import useModal from '@components/hooks/useModal';
import UploadUI from '@components/settings/UploadStampUI';
import * as Gql from '@graphql';
import { Button, Col, Form, Input, ModalProps, Row, Select, message } from 'antd';
import html2canvas from 'html2canvas';
import { AppLayoutContext } from 'pages/_app';
import React, { useContext, useRef, useState } from 'react';
import SignatureStampCard from './SignatureStampCard';
const { Option } = Select;

export interface ModalRef extends ModalProps {
  showForm: (v?: any) => void;
}

type Props = {
  onRefetch: () => void;
  stampUrl: string | null | undefined;
  onResetStamp: () => void;
  user?: {
    id?: string | null | undefined;
    name?: string | null | undefined;
    position?: string | null | undefined;
    company?: string | null | undefined;
  };
};

const PersonalStampGenerator: React.FC<Props> = ({ stampUrl, user, onRefetch, onResetStamp }) => {
  const modalRef = useRef<any>(null);
  const [form] = Form.useForm();
  const { userData: userMeData, setUserData } = useContext(AppLayoutContext);

  const [name, setName] = useState(user?.name || '');
  const [occupation, setOccupation] = useState(user?.position || '');
  const [company, setCompany] = useState(user?.company || '');
  const [professionalNumber, setProfessionalNumber] = useState('');
  const [loading, setLoading] = useState(false);
  const [fontSize, setFontSize] = useState(18);

  const uploadPhotoModalRef = useRef<any>(null);

  const [updateUser] = Gql.useUpdateUserMeMutation({
    onCompleted(data) {
      localStorage.setItem('PersonalStampRequest', 'false');
      message.destroy();
      onRefetch();
      setUserData({
        getUserMe: {
          ...userMeData?.getUserMe,
          stampUrl: `${data.updateUserMe.stampUrl}?${new Date().getTime()}` // to refresh the image
        }
      } as any);
      modalRef?.current?.closeModal();
      message.success('Personal stamp generated successfully');
    }
  });

  const generatePng = async () => {
    const resultDiv = document.getElementById('result');
    if (resultDiv) {
      try {
        const canvas = await html2canvas(resultDiv, { scale: 4, backgroundColor: null });

        const blob = await new Promise<Blob | null>(resolve => {
          canvas.toBlob((blob: any) => {
            resolve(blob);
          }, 'image/png');
        });

        if (!userMeData?.getUserMe?.id) return message.error('User not found');
        if (!blob) return message.error('Failed to generate personal stamp');
        const file = new File([blob], 'image.png', { type: 'image/png' });

        await updateUser({
          variables: {
            input: {
              stampUrl: file
            }
          },
          onCompleted: () => {
            closeGeneratePersonalStamp();
          }
        });
      } catch (error) {
        message.error('Failed to generate personal stamp');
      }
    }
  };

  const onFinish = async (values: any) => {
    setLoading(true);
    message.open({ type: 'loading', content: 'Generating personal stamp', duration: 0 });
    setTimeout(async () => {
      await generatePng();
      setLoading(false);
    }, 3000);
  };

  const [uploadModal, showUploadModal, closeUploadModal] = useModal({
    title: 'Personal Stamp',
    content: (
      <>
        sds

      </>
    )
  });

  const [generatePersonalStamp, showGeneratePersonalStamp, closeGeneratePersonalStamp] = useModal({
    title: 'Personal Stamp',
    content: (
      <div>
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            name: user?.name,
            occupation: user?.position,
            company: user?.company,
            fontSize: fontSize
          }}
          onValuesChange={() => {
            const values = form.getFieldsValue();
            setName(values.name || '');
            setOccupation(values.occupation || '');
            setCompany(values.company || '');
            setProfessionalNumber(values.professionalNumber || '');
            setFontSize(values.fontSize || 18);
          }}
          onFinish={onFinish}
        >
          <Row gutter={24}>
            <Col span={24} className="flex flex-col items-center justify-center">
              <div
                className="flex flex-col items-center justify-center w-[250px] h-[100px] text-center m-0 p-2"
                id="result"
              >
                <h1 style={{ color: '#2E69C2', fontSize: `${fontSize}px`, lineHeight: '1.2', marginTop: '10px' }}>
                  {name || 'Please enter your name'}
                </h1>
                <h2
                  className={'pt-[0.1em]'}
                  style={{ color: '#2E69C2', fontSize: `${fontSize}px`, marginBottom: '-3px', lineHeight: '1.2' }}
                >
                  {occupation || 'Please enter your occupation/Position'}
                </h2>
                <h2
                  className="pt-1"
                  style={{
                    color: '#2E69C2',
                    fontSize: `${fontSize}px`,
                    marginTop: '0px',
                    marginBottom: '0px',
                    lineHeight: '1.2'
                  }}
                >
                  {company || 'Please enter your company'}
                </h2>
                <h3
                  style={{
                    color: '#2E69C2',
                    fontSize: `${fontSize}px`,
                    lineHeight: '1.2',
                    marginTop: '3px',
                    marginBottom: '10px'
                  }}
                >
                  {professionalNumber || ''}
                </h3>
              </div>
              <p className="">Personal Stamp Preview</p>
            </Col>
            <Col span={24}>
              <Form.Item label="Name" name="name" rules={[{ required: true, message: 'Please enter your name' }]}>
                <Input className="rounded-lg h-10 w-70" />
              </Form.Item>
              <Form.Item
                label="Occupation/Position"
                name="occupation"
                rules={[{ required: true, message: 'Please enter your occupation/Position' }]}
              >
                <Input className="rounded-lg h-10 w-70" />
              </Form.Item>
              <Form.Item
                label="Company"
                name="company"
                rules={[{ required: true, message: 'Please enter your company name' }]}
              >
                <Input className="rounded-lg h-10 w-70" />
              </Form.Item>
              <Row className="flex gap-4">
                <Form.Item label="Professional Number" name="professionalNumber" className="flex-grow">
                  <Input className="rounded-lg h-10 w-full" />
                </Form.Item>
              </Row>

              <Row className="flex gap-4">
                <Form.Item label="Font Size" name="fontSize" style={{ marginBottom: '2px' }}>
                  <Select placeholder="Select a size" defaultValue={fontSize}>
                    {[...Array(25)].map((_, i) => (
                      <Option key={i} value={4 + i}>
                        {4 + i}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Row>
            </Col>
          </Row>
          <Row className="mt-10">
            <Col span={24}>
              <div className="flex justify-center">
                <Button type="primary" htmlType="submit" loading={loading}>
                  Generate
                </Button>
              </div>
            </Col>
          </Row>
        </Form>
      </div>
    )
  });

  return (
    <>
      {uploadModal}
      {generatePersonalStamp}
      <FileUpload
        type={'personal-stamp'}
        isSignatureOrStamp={true}
        onSaved={() => {
          onRefetch();
          uploadPhotoModalRef?.current?.closeModal();          
          
        }}
        ref={uploadPhotoModalRef}
        accept={['image/PNG', 'image/png']}
        maxFiles={1}
      />

      <SignatureStampCard
        assetUrl={stampUrl}
        label={'Personal Stamp'}
        onCreate={() => {
          showGeneratePersonalStamp();
        }}
        onUpload={() => {
          uploadPhotoModalRef?.current?.openModal();          
        }}
        onReset={onResetStamp}
      />
    </>
  );
};

export default PersonalStampGenerator;
