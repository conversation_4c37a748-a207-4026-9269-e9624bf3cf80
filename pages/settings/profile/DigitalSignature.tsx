import FileUpload from '@components/FileUpload';
import useModal from '@components/hooks/useModal';
import * as Gql from '@graphql';
import { onError } from '@utils/error';
import _ from 'lodash';
import { QRCodeCanvas } from 'qrcode.react';
import { useRef, useState } from 'react';
import { UserAuthService } from 'src/api';
import SignatureStampCard from './SignatureStampCard';

type DigitalSignatureProp = {
  projectUserRole: string | null | undefined;
  signatureUrl: string | null | undefined;
  onResetStamp: () => void;
  onRefetch: () => void;
  userId: string | null | undefined;
};

const DigitalSignature: React.FC<DigitalSignatureProp> = ({
  signatureUrl,
  onResetStamp,
  onRefetch,
  userId
}) => {
  const uploadPhotoModalRef = useRef<any>(null);
  const [token, setToken] = useState<string | null>(null);

  const [updateUser, { loading: updating }] = Gql.useUpdateUserMeMutation({
    onError: onError as any,
  });

  const getUserChangeSignatureToken = async () => {
    const res = await UserAuthService.changeSignatureToken();    
    
    return res;
  }

  const [signatureQrCode, showSignatureQrCode, closeSignatureQrCode] = useModal({
    onClose() {
      
      onRefetch();
    },
    title: 'Set up your personal signature',
    content: (
      <div className="flex flex-col items-center">
        <QRCodeCanvas
          id={`qr-code`}
          style={{ width: '30%', height: '30%' }}
          value={`${process.env.NEXT_PUBLIC_APP_URL}/signature/${token}`}
        />
        <p className="mt-4 text-lg">Scan the QR code to sign</p>
        <p className='font-bold'>This QR code will expire in 30 minutes.</p>
      </div>
    )
  });



  return (
    <>
      {signatureQrCode}
      <FileUpload
        type={Gql.CategoryType.Photo}
        isSignatureOrStamp={true}
        onSaved={() => {
          onRefetch();
          // uploadPhotoModalRef?.current?.closeModal();
        }}
        ref={uploadPhotoModalRef}
        accept={['image/PNG', 'image/png']}
        maxFiles={1}
      />
      <SignatureStampCard
        assetUrl={signatureUrl}
        label={'Digital Signature'}
        onCreate={async () => {
          const res = await getUserChangeSignatureToken();
          setToken(res);

          await updateUser({
            variables: {
              input: {
                changeSignatureToken: res
              }
            }
          });
    
          showSignatureQrCode();
        }}
        onUpload={() => {
          uploadPhotoModalRef?.current?.openModal();
        }}
        onReset={onResetStamp}
      />
    </>
  );
};

export default DigitalSignature;
