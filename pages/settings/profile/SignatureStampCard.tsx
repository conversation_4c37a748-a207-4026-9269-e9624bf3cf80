import { But<PERSON>, Card, Image, Row } from 'antd';
import { FC } from 'react';

type Props = {
  assetUrl?: string | null;
  label: string;
  onCreate: () => void;
  onUpload: () => void;
  onReset?: () => void;
};

const SignatureStampCard: FC<Props> = ({ assetUrl, label, onCreate, onUpload, onReset }) => {
  return (
    <Card className="flex flex-col ">
      <div className="flex">
        <h1 className="text-xl flex-1 font-bold">{label}</h1>
        {!assetUrl && (
          <Row>
            <Button type="primary" onClick={onCreate}>
              Create
            </Button>
            <Button type="primary" onClick={onUpload} className="ml-3">
              Upload
            </Button>
          </Row>
        )}

        {assetUrl && onReset && (
          <div
            onClick={onReset}
            className="cursor-pointer border border-solid rounded-xl text-blue-400 border-blue-400 px-4 py-1"
          >
            {`Request ${label} Change`}
          </div>
        )}
      </div>

      <div className="flex justify-center mt-8 mb-6">
        {assetUrl ? (
          <Image
            src={assetUrl as string}
            alt={label}
            rootClassName="flex"
            className="m-auto object-contain"
            height={'180px'}
          />
        ) : (
          <div className="flex" style={{ height: '180px' }}>
            <div className="m-auto">{`No ${label} provided`}</div>
          </div>
        )}
      </div>
    </Card>
  );
};

export default SignatureStampCard;
