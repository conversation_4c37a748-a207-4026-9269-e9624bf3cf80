import { useHeader } from '@components/HeaderProvider';
import useQueries from '@components/hooks/useQueries';
import * as Gql from '@graphql';
import { onError } from '@utils/error';
import { tz } from '@utils/timezone';
import { Col, List, Row, Spin } from 'antd';
import _ from 'lodash';
import moment from 'moment';
import { useEffect, useState } from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';

const ActivityLog = () => {
  const [data, setData] = useState<any[]>([]);
  const { setTitle } = useHeader();

  useEffect(() => {
    setTitle('Activity log');
  }, []);

  const { queries, setPaging } = useQueries<Gql.AuditLogFilter, Gql.AuditLogSort>({
    paging: { offset: 0, limit: 20 },
    usePagingParam: false
  });

  const { data: newData, loading } = Gql.useGetActivityLogsQuery({
    variables: {
      ...queries,
      sorting: [
        {
          direction: Gql.SortDirection.Desc,
          field: Gql.AuditLogSortFields.CreatedAt
        }
      ]
    },
    onError: onError
  });

  useEffect(() => {
    if (newData) {
      const data = newData?.auditLogs?.nodes ?? [];
      setData(prev => [...prev, ...data]);
    }
  }, [newData]);

  const loadMoreData = () => {
    const { offset, limit } = queries.paging;
    setPaging({
      offset: offset + limit,
      limit
    });
  };

  const structuredArr = _.orderBy(
    _.toPairs(_.groupBy(data, data => moment(tz(data.createdAt)).startOf('day').toISOString())),
    data => moment(data[0]).startOf('day').toISOString()
  );

  const projectUI = (data: Gql.AuditLog, index: number) => {
    return (
      <Row className="py-[16px]" key={index}>
        <Col span={3} className="text-gray90 font-normal text-md">
          {moment(tz(data.createdAt)).format('h:mma')}
        </Col>
        <Col span={20}>
          <div className="grid grid-rows-2 ml-[28px]">
            <span className="text-gray100 font-semibold text-md">
              Project{' '}
              {data.action === Gql.AuditLogActionType.Create
                ? 'Created'
                : data.action === Gql.AuditLogActionType.Update
                ? 'Updated'
                : data.action === Gql.AuditLogActionType.Delete
                ? 'Deleted'
                : ''}
            </span>
            <span className="text-gray90 font-normal text-base"> {data?.user?.name}</span>
          </div>
        </Col>
      </Row>
    );
  };

  const taskUI = (data: Gql.AuditLog, index: number) => {
    const title = data.content?.slice(0, data.content.indexOf('\n'));
    const description = data.content?.slice(data.content.indexOf('\n') + 1);
    return (
      <Row className="py-[16px]" key={index}>
        <Col span={3} className="text-gray90 font-normal text-md">
          {moment(tz(data.createdAt)).format('h:mma')}
        </Col>
        <Col span={20}>
          <div className="grid grid-rows-2 ml-[28px]">
            <span className="text-gray100 font-semibold text-md">
              Task{' '}
              {data.action === Gql.AuditLogActionType.Create
                ? 'Created'
                : data.action === Gql.AuditLogActionType.Update
                ? 'Updated'
                : data.action === Gql.AuditLogActionType.Delete
                ? 'Deleted'
                : ''}
            </span>
            <span className="text-gray90 font-normal text-base"> {data?.user?.name}</span>
            {data.action === Gql.AuditLogActionType.Create ? (
              <div className="bg-[#F0F4FF] rounded-[8px] w-full p-2 mt-2">
                <span className="text-primary font-base font-semibold"> {title}</span>
                <br />
                <span className="font-base font-normal text-gray90"> {description}</span>
              </div>
            ) : (
              <div className="bg-[#F0F4FF] rounded-[8px] w-full p-2 mt-2">
                <span className="text-gray90 text-base font-normal"> {data.content}</span>
              </div>
            )}
          </div>
        </Col>
      </Row>
    );
  };

  const commentUI = (data: Gql.AuditLog, index: number) => {
    return (
      <Row className="py-[16px]" key={index}>
        <Col span={3} className="text-gray90 font-normal text-md">
          {moment(tz(data.createdAt)).format('h:mma')}
        </Col>
        <Col span={20}>
          <div className="grid grid-rows-2 ml-[28px]">
            <span className="text-gray100 font-semibold text-md">
              Left a comment in <span className="text-primary font-base font-semibold">{data.task?.title}</span>
            </span>
            <span className="text-gray90 font-normal text-base"> {data?.user?.name}</span>
            <div className="bg-[#F0F4FF] rounded-[8px] w-full p-2 mt-2">
              <span className="text-gray90 text-base font-normal"> {data.content}</span>
            </div>
          </div>
        </Col>
      </Row>
    );
  };

  return (
    <Spin tip={'Loading...'} spinning={loading}>
      <div id="scrollableDiv" className="overflow-y-scroll mt-[20px] scrollbar h-screen pb-[80px]">
        <InfiniteScroll
          dataLength={data.length}
          next={loadMoreData}
          hasMore={data.length < (newData?.auditLogs?.totalCount ?? 0)}
          loader={<Spin />}
          scrollableTarget="scrollableDiv"
        >
          {structuredArr.map((data, index: number) => (
            <List
              key={index}
              itemLayout="horizontal"
              dataSource={data[1]}
              className="overflow-auto activity-list mb-[20px]"
              header={moment(data[0]).format('DD MMMM')}
              renderItem={(o, i) => {
                if (o.module === Gql.AuditLogModuleType.Project) {
                  return projectUI(o, i);
                } else if (o.module === Gql.AuditLogModuleType.Task) {
                  return taskUI(o, i);
                } else if (o.module === Gql.AuditLogModuleType.TaskComment) {
                  return commentUI(o, i);
                } else {
                  return;
                }
              }}
            />
          ))}
        </InfiniteScroll>
      </div>
    </Spin>
  );
};

ActivityLog.auth = true;

export default ActivityLog;
