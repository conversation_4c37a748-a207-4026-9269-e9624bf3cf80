"use client";
import { message, Spin, Typography } from 'antd';
import { useRouter } from 'next/router';
import { useEffect, useRef, useState } from 'react';

const AutoDeskBim = () => {
  const router = useRouter();
  const token = router.query.token as string;
  const urn = router.query.urn as string;
  const [loading, setLoading] = useState(false)

  const viewer3DRef = useRef<any>(null);  
  const forgeViewer2Ref = useRef(null);

  const options = {
    env: 'AutodeskProduction',
    api: 'derivativeV2',
    getAccessToken: async function (onTokenReady: any) {     
      var timeInSeconds = 3599;
      onTokenReady(token, timeInSeconds);      
    },
    compass: true
  };

  useEffect(() => {
    executeBim();
  }, [urn])

  const urns = urn ? urn.split(',') : []  
  
  const executeBim = async () => {    
    if (urns.length > 0) {      
      const config = {
        extensions: [
          'Autodesk.DocumentBrowser',
        ],
      };

      //@ts-ignore
      Autodesk.Viewing.Initializer(options, async function async() {
        const htmlDiv3D = document.getElementById('forgeViewer2');        
        setLoading(true)
        //@ts-ignore
        const viewer3D = new Autodesk.Viewing.GuiViewer3D(htmlDiv3D as any, config);

        viewer3DRef.current = viewer3D;
        await viewer3D.start();                     

          urns.map((m: any, index: number) => {            
            //@ts-ignore
            Autodesk.Viewing.Document.load(`urn:${m}`, (doc) => {                            
              var viewables = doc.getRoot().getDefaultGeometry();

              viewer3D.loadDocumentNode(doc, viewables, {
                keepCurrentModels: true,
                modelSpace: true,    // 2D drawings
                applyRefPoint: true, // 3D shared coordinates
                applyScaling: 'm',   // force all models to same scale
                globalOffset: { x: 0, y: 0, z: 0 }
              })
                .then(onLoadFinish);
            });
          });

        function onLoadFinish() {
          setLoading(false)          
        }

        function onDocumentLoadFailure() {
          setLoading(false)
          message.error('Document failed to load');
        }

        //@ts-ignore
        viewer3D.addEventListener(Autodesk.Viewing.GEOMETRY_LOADED_EVENT, (x) => {
          const explodeExtension = viewer3D.getExtension('Autodesk.Explode');
          const measure = viewer3D.getExtension('Autodesk.Measure');
          const section = viewer3D.getExtension('Autodesk.Section');

          explodeExtension?.unload();
          measure?.unload();
          section?.unload();
        });

        //@ts-ignore
        viewer3D.addEventListener(Autodesk.Viewing.TOOLBAR_CREATED_EVENT, async (e: any) => {
          const getToolbar = viewer3D.toolbar;

          const toolbarDiv = document.querySelector('.adsk-viewing-viewer .adsk-toolbar') as HTMLElement;
          const controlGroups = document.querySelectorAll('.adsk-viewing-viewer.dark-theme .adsk-control-group') as NodeListOf<HTMLElement>;

          controlGroups.forEach((controlGroup) => {
            controlGroup.style.background = 'black';
          });

          if (toolbarDiv) {
            toolbarDiv.style.background = 'transparent';
            toolbarDiv.style.border = 'none';
          }

          // Function to remove unwanted toolbar controls
          const removeControls = () => {
            const settingsTools = getToolbar.getControl('settingsTools');
            const navTools = getToolbar.getControl('navTools');

            if (settingsTools) {
              settingsTools.removeControl('toolbar-modelStructureTool');
              settingsTools.removeControl('toolbar-fullscreenTool');
            }

            if (navTools) {
              navTools.removeControl('toolbar-zoomTool');
              navTools.removeControl('toolbar-panTool');
              navTools.removeControl('toolbar-cameraSubmenuTool');
            }
          };

          // Listen for the event that all extensions are loaded
          //@ts-ignore
          viewer3D.addEventListener(Autodesk.Viewing.EXTENSION_LOADED_EVENT, (extEvent) => {
            if (extEvent.extensionId === 'Autodesk.DocumentBrowser' || extEvent.extensionId === 'Autodesk.PropertiesManager') {
              // Call the function to remove controls
              removeControls();
            }
          });
        });
        ;
      });
    }
  };

  return (
    <>
      <div>        
        <div style={{ height: '100vh' }} className="relative">
          <div id="forgeViewer2" ref={forgeViewer2Ref} style={{ width: '100%', height: '100%' }} />
        </div>
      </div>
    </>
  )
};

export default AutoDeskBim;
