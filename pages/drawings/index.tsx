import { ProjectAccess } from '@constants/subscription';
import * as Gql from '@graphql';
import { isAllowed } from '@lib/helper';
import { onError } from '@utils/error';
import { useRouter } from 'next/router';

const Drawings = () => {
  const route = useRouter();

  /** START ACL */
  const companySubscriptions = Gql.useCompanySubscriptionsQuery({
    variables: {
      sorting: {
        direction: Gql.SortDirection.Desc,
        field: Gql.CompanySubscriptionSortFields.SubscriptionEndDate
      },
      paging: {
        limit: 1
      }
    },
    onError: onError,
    nextFetchPolicy: 'cache-first'
  });
  /** END ACL */

  if (isAllowed(companySubscriptions, ProjectAccess.DRAWING)) route.push('/drawings/2D-drawing');
  else if (isAllowed(companySubscriptions, ProjectAccess.BIM_MODEL)) route.push('/drawings/BIM-drawing');

  return null;
};

Drawings.auth = true;
export default Drawings;
