import { ExclamationCircleOutlined, HomeOutlined, UserOutlined } from '@ant-design/icons';
import { Icon } from '@commons';
import ManageModal from '@components/ManageModal';
import MoveBulkCloudDocsModal from '@components/MoveBulkCloudDocsModal';
import MoveDrawingsModal from '@components/MoveDrawingsModal';
import { default as ThreeDotsDropDown } from '@components/ThreeDotsDropDown';
import TwoDDrawingsUpload from '@components/TwoDDrawingsUpload';
import UserAvatar from '@components/UserAvatar';
import SyncModal from '@components/cloud-docs/SyncModal';
import AddRevisionModal from '@components/drawings/2D-Drawings/AddRevisionModal';
import DrawingFormTabLayout from '@components/drawings/DrawingFormTabLayout';
import SearchInput from '@components/forms/FormsInput/SearchInputs/2dDrawingsSearch';
import useModal from '@components/hooks/useModal';
import useQueries from '@components/hooks/useQueries';
import PdfTronModal from '@components/pdf-tron/DrawingsPdfTronModal';
import * as Gql from '@graphql';
import apolloClient from '@lib/apollo';
import { handleRowClick, nameAlias } from '@utils/app.utils';
import { manageFiles } from '@utils/authority';
import { onError } from '@utils/error';
import { getFileName } from '@utils/filename';
import { tz } from '@utils/timezone';
import {
  Avatar,
  Breadcrumb,
  Button,
  Dropdown,
  Form,
  Input,
  MenuProps,
  message,
  Modal,
  Row,
  Space,
  Spin,
  Table,
  Tooltip
} from 'antd';
import { useForm } from 'antd/lib/form/Form';
import { ColumnsType } from 'antd/lib/table';
import Paragraph from 'antd/lib/typography/Paragraph';
import axios from 'axios';
import FileSaver from 'file-saver';
import { debounce, isArray } from 'lodash';
import moment from 'moment';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { AppLayoutContext } from 'pages/_app';
import { useContext, useEffect, useRef, useState } from 'react';
import { ProjectDocumentApiService } from 'src/api';
import { getFileIcon } from 'src/commons/FileIcon';
import useDropzone from 'src/hooks/useDropzone';
import VersionControlDrawer from '../../../src/components/drawings/2D-Drawings/versionControlDrawer';


const TwoDDrawing = () => {
  const router = useRouter();
  const [form] = useForm();
  const pdfTronModalRef = useRef<any>(null);
  const versionControlDrawerRef = useRef<any>(null);
  const documentId = router.query.documentId as string;
  const { confirm } = Modal;
  const [selectedFormId, setSelectedFormId] = useState<string>();
  const [dataSource, setDataSource] = useState<any>(false);
  const uploadDocModalRef = useRef<any>(null);
  const [breadcrumbs, setBreadcrumbs] = useState<
    Gql.GetProjectDocumentsBreadcrumbQuery['getProjectDocumentsBreadcrumb']
  >([]);
  const dirId = router.query.dir && router.query.dir[0];
  const [getBreadcrumbs, { data: breadcrumbData }] = Gql.useGetProjectDocumentsBreadcrumbLazyQuery();
  const [fileType, setFileType] = useState<string>('');
  const [formName, setFormName] = useState<string>('');
  const [category, setCategory] = useState<Gql.CategoryType>();
  const moveFileModal = useRef<any>(null);
  const moveDocumentsModal = useRef<any>(null);
  const [loading, setLoading] = useState(false);
  const tableRef = useRef<any>(null);
  const inputRef = useRef<any>(null);
  const addRevisionModalRef = useRef<any>(null);

  const [searchForm] = useForm();
  // For multiple selection
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [isManage, setIsManage] = useState(false);
  const [bimStatusLoad, setBimStatusLoad] = useState<boolean>(false);
  const [fileId, setFileId] = useState<string>('');
  const [hoveredRowIndex, setHoveredRowIndex] = useState(null);

  const { userRole } = useContext(AppLayoutContext);

  const dropzoneRef = useRef<any>(null);
  useDropzone({ dropzoneRef, pushModal: uploadDocModalRef?.current?.openModal });

  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  useEffect(() => {
    if (!dirId) {
      setBreadcrumbs([]);
      return;
    }

    getBreadcrumbs({
      variables: { input: { id: parseInt(dirId as string) } }
    }).then(res => {
      setBreadcrumbs(res.data?.getProjectDocumentsBreadcrumb ?? []);
    });
  }, [dirId, getBreadcrumbs, breadcrumbData]);

  const viewOnly = userRole === 'CanView';
  const { canCreate, canDelete, canMove } = manageFiles(userRole as Gql.ProjectUserRoleType, dirId ?? '');

  const handleTableScroll = (e: { target: any }) => {
    const { target } = e;
    if (target.scrollHeight - target.scrollTop <= target.clientHeight + 1) {
      onLoadMore();
    }
  };

  useEffect(() => {
    if (router.query.documentId) {
    }
  }, [router.query.documentId]);

  const onRowHover = (id: any) => {
    setHoveredRowIndex(id);
  };

  const onRowLeave = () => {
    setHoveredRowIndex(null);
  };


  const { queries, setFilter } = useQueries<Gql.ProjectDocumentFilter, Gql.ProjectDocumentSort>({
    usePagingParam: false,
    paging: { offset: 0, limit: 20 }
  });

  const {
    data,
    refetch,
    loading: loadingProjectocument
  } = Gql.useGetProjectDocumentsQuery({
    variables: {
      
      ...queries,
      filter: {
        ...queries.filter,
        ...{
          projectDocumentId: {
            ...(dirId ? { eq: dirId?.toString() } : { eq: null })
          },
          category: { eq: Gql.CategoryType.TwoDDrawings }
        }
      }
    },
    onError: onError,
    fetchPolicy: 'cache-and-network'
  });

  useEffect(() => {
    if (router.query.drawingId) {
      versionControlDrawerRef?.current?.pushDrawer();
    }
  }, [router.query.drawingId]);

  useEffect(() => {
    setDataSource(data?.getProjectDocuments?.nodes ?? []);
  }, [data]);

  // refetch the data when dirId is null
  useEffect(() => {
    if (!dirId) {
      refetch();
    }
  }, [dirId, refetch]);

  // update dataSource state onLoadMore function is fire
  useEffect(() => {
    setDataSource(data?.getProjectDocuments?.nodes ?? []);

    if (tableRef.current) {
      const tableBody = tableRef.current.querySelector('.ant-table-body');
      tableBody.addEventListener('scroll', handleTableScroll);
    }
    return () => {
      if (tableRef.current) {
        const tableBody = tableRef.current.querySelector('.ant-table-body');
        tableBody.removeEventListener('scroll', handleTableScroll);
      }
    };
  }, [handleTableScroll]);

  const onLoadMore = async () => {
    if (!data?.getProjectDocuments.pageInfo.hasNextPage) return;
    setLoading(true);
    await refetch({
      paging: {
        offset: 0,
        limit: data.getProjectDocuments.nodes.length + 20
      }
    }).then(res => {
      setDataSource([...dataSource, res.data?.getProjectDocuments?.nodes ?? []]);
      setLoading(false);
    });
  };

  const [deleteDocuments, { loading: documentsDeleting }] = Gql.useDeleteProjectDocumentsMutation({
    onCompleted: () => {
      message.success('Deleted successfully');
      setSelectedRowKeys([]);
      refetch();
    },
    onError: onError as any
  });

  const showDeletesConfirm = () => {
    const deletedDocumentNames = dataSource
      .filter((doc: any) => selectedRowKeys.includes(doc.id.toString()))
      .map((doc: any) => doc.name)
      .slice(0, 10);

    confirm({
      title: 'Are you sure?',
      icon: <ExclamationCircleOutlined />,
      content: (
        <>
          <div>
            Do you really want to delete the{' '}
            {selectedRowKeys.length < 2 && (
              <span className="font-semibold">
                {deletedDocumentNames[0]} <span className="font-normal">?</span>
              </span>
            )}
          </div>
          {selectedRowKeys.length > 1 && (
            <ol className="list-decimal mt-2">
              {deletedDocumentNames.map((name: string, index: number) => (
                <li className="my-2 font-semibold" key={index}>
                  {name}
                </li>
              ))}
            </ol>
          )}
        </>
      ),
      okText: 'Yes',
      okType: 'danger',
      cancelText: 'No',
      onOk() {
        deleteDocuments({
          variables: {
            input: {
              ids: selectedRowKeys.map(id => parseInt(id as string))
            }
          }
        });
        setSelectedRowKeys([]);
        setIsManage(false);
      },
      onCancel() {}
    });
  };

  const onBulkDownload = async () => {
    setLoading(true);
    try {
      message.open({
        type: 'loading',
        content: `Downloading...`,
        duration: 0
      });
      const nodeBuffer = await ProjectDocumentApiService.downloadBulkZip(
        {
          body: {
            ids: selectedRowKeys as string[]
          }
        },
        {
          responseType: 'arraybuffer'
        }
      ).catch(e => {
        message.error(e?.message ?? 'Something went wrong');
      });

      const file = new Blob([nodeBuffer], { type: 'application/zip' });
      const fileName = 'bulkdownload.zip';
      setSelectedRowKeys([]);
      setLoading(false);
      FileSaver(file, fileName);
    } catch (e) {
      onError(e);
    } finally {
      message.destroy();
      setLoading(false);
    }
  };

  //Routing new tab
  const openInNewTab = (url: any) => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  const [createFolder, { loading: createFolderLoading }] = Gql.useCreateOneProjectDocumentMutation({
    onCompleted: () => {
      form.resetFields();
      message.success('Folder created successfully');
      refetch();
      closeAddFolderModal();
    },
    onError: onError as any
  });

  const [createTask] = Gql.useCreateTaskMutation({
    onCompleted: ({}) => {
      message.success('Task created successfully');
      refetch();
    },
    onError: onError as any
  });

  const onSavedXfdf = (xfdf: any) => {
    createTask({
      variables: {
        input: {
          title: xfdf,
          documents: [{ id: Number(documentId) }]
        }
      }
    });
  };

  const [deleteFolder] = Gql.useDeleteProjectDocumentMutation({
    onCompleted: () => {
      message.success('Deleted successfully');
      refetch();
    },
    onError: onError as any
  });

  const onFinish = (values: Gql.CreateProjectDocumentInputDto) => {
    const { name } = values;
    const category = Gql.CategoryType.TwoDDrawings;
    const fileSystemType = Gql.FileSystemType.Folder;
    const type = 'folder';
    const updatedName = getFileName(type, name ?? '', '');
    const projectDocumentId = dirId as string;
    createFolder({
      variables: {
        input: {
          projectDocument: {
            name: updatedName,
            category,
            fileSystemType,
            type,
            projectDocumentId
          }
        }
      }
    });
  };

  const onDelete = (id: string) => {
    deleteFolder({ variables: { id: parseInt(id) } });
  };

  const showDeleteConfirm = (id: string) => {
    confirm({
      title: 'Are you sure?',
      icon: <ExclamationCircleOutlined />,
      content: 'Do you really want to delete this document?',
      okText: 'Yes',
      okType: 'danger',
      cancelText: 'No',
      onOk() {
        onDelete(id);
      },
      onCancel() {}
    });
  };

  const [updateName] = Gql.useUpdateOneProjectDocumentMutation({
    onCompleted: () => {
      message.success('Saved successfully');
      closeEditFolderNameModal();
      refetch();
      form.setFieldValue('name', '');
    },
    onError: (err: any) => {
      message.error(err.message);
    }
  });

  const onUpdate = (values: Gql.UpdateProjectDocumentInputDto) => {
    const selectedDocument = dataSource?.find((item: any) => item.id === selectedFormId) ?? null;
    const updatedName = getFileName(selectedDocument.type, values.name ?? '', formName);
    const fileType = selectedDocument.type;
    if (!values.name) {
      return message.error('Folder name is required');
    }

    updateName({
      variables: {
        input: {
          id: selectedFormId?.toString() ?? '',
          update: {
            name: updatedName ?? ''
          }
        }
      }
    });
  };

  const [modal, showAddFolderModal, closeAddFolderModal] = useModal({
    onCancel: () => form.resetFields(),
    title: 'Create folder',
    content:
      (setTimeout(() => {
        inputRef?.current?.focus();
      }, 100),
      (
        <Form layout="vertical" form={form} onFinish={onFinish} requiredMark={false}>
          <Form.Item label="Folder name" name="name" rules={[{ required: true, message: 'Folder name is required!' }]}>
            <Input ref={inputRef} />
          </Form.Item>
          <div className="flex justify-end">
            <Button htmlType="submit" type="primary" disabled={viewOnly}>
              Create
            </Button>
          </div>
        </Form>
      ))
  });

  // Edit Form Category Modal
  const [editFolderNameModal, showEditFolderNameModal, closeEditFolderNameModal] = useModal({
    title: 'Edit name',
    content:
      (setTimeout(() => {
        inputRef?.current?.focus();
      }, 100),
      (
        <Form layout="vertical" form={form} requiredMark={false} onFinish={onUpdate} initialValues={{}}>
          <Form.Item label="Folder/File name" name="name">
            <Input defaultValue={formName} ref={inputRef} />
          </Form.Item>
          <div className="flex justify-end">
            <Button htmlType="submit" type="primary">
              Save
            </Button>
          </div>
        </Form>
      ))
  });

  const onDownload = async (id: string) => {
    try {
      const doc = await apolloClient.query<Gql.ProjectDocumentQuery, Gql.ProjectDocumentQueryVariables>({
        query: Gql.ProjectDocumentDocument,
        variables: { id }
      });
      const file = doc.data.projectDocument;
      const { data: fileBlob } = await axios.get(file?.fileUrl ?? '', {
        responseType: 'blob'
      });
      const fileName = file?.name ?? '';
      FileSaver(fileBlob, fileName);
    } catch (e) {
      onError(e);
    }
  };

  const onDownloadFolder = async (id: number, name: string) => {
    try {
      message.open({
        type: 'loading',
        content: `Downloading ${name}...`,
        duration: 0
      });
      const nodeBuffer = await ProjectDocumentApiService.downloadZip(
        {
          body: {
            id
          }
        },
        {
          responseType: 'arraybuffer'
        }
      );

      const file = new Blob([nodeBuffer], { type: 'application/zip' });
      const fileName = name + '.zip';
      FileSaver(file, fileName);
    } catch (e) {
      onError(e);
    } finally {
      message.destroy();
    }
  };

  const [ellipsis, setEllipsis] = useState(true);

  const columns: ColumnsType<any> = [
    {
      title: 'NAME',
      key: 'name',
      render: (data: any) => {
        const { fileUrl } = data;

        return (
          <Space className="p-1" align="start">
            {getFileIcon(data.type)}
            <div
              className="cursor-pointer"
              onClick={async () => {
                if (fileUrl) {
                  if (data?.fileSystemType === Gql.FileSystemType.Folder) {
                    setFilter({ name: { like: '' } });
                    searchForm.resetFields();
                  }
                  if (router.query && data.fileSystemType === 'Document') {
                    if (viewOnly) {
                      openInNewTab(
                        `/viewer/2D-drawings-viewers?documentId=${data.id}${dirId ? `&dirId=${dirId}` : ''}`
                      );
                    } else {
                      openInNewTab(`/viewer/2D-drawings-viewer?documentId=${data.id}${dirId ? `&dirId=${dirId}` : ''}`);
                    }
                  }
                }

                if (data?.fileSystemType === 'Folder') {
                  setFilter({ name: { like: '' } });
                  setSelectedRowKeys([]);
                  router.push(`/drawings/2D-drawing/${data.id}`);
                }
              }}
            >
              <Tooltip
                title={data?.name}
                mouseEnterDelay={0.5}
                placement="topLeft"
                overlayStyle={{ maxWidth: '550px' }}
              >
                <Paragraph
                  className="font-medium"
                  id="name"
                  style={{ margin: 0, width: 550 }}
                  ellipsis={ellipsis ? { rows: 2, expandable: false } : false}
                >
                  {data?.name}
                  {bimStatusLoad && data?.id === fileId && <Spin className="ml-3" size="small" />}
                </Paragraph>
              </Tooltip>
            </div>
          </Space>
        );
      }
    },
    {
      title: 'REVISION',
      key: 'revision',
      width: 120,
      render: (data: any) => {
        return data?.drawingRevisions?.length > 1 ? <p>REVISION-{data?.drawingRevisions?.length - 1}</p> : '-';
      }
    },
    {
      title: 'FILE SIZE',
      key: 'fileSize',
      width: 120,
      render: (data: any) => {
        return data?.fileSystemType === Gql.FileSystemType.Document ? (
          <p> {data?.fileSize ? `${data?.fileSize} MB` : '-'}</p>
        ) : (
          '-'
        );
      }
    },
    {
      title: 'ADDED BY',
      dataIndex: 'owner',
      key: 'owner',
      width: 160,
      render: (owner: any, index: any) => {
        return (
          <UserAvatar
            key={index}
            username={owner?.name}
            src={owner?.avatar}
            style={{ backgroundColor: !owner?.avatar ? owner?.color || "" : "transparent", // Apply only if no avatar
              color: "#ffffff"
            }}
            tooltip={owner?.name}
            align={{
              offset: [-12, -9], 
              }}
          />
        );
      }
    },
    {
      title: 'LAST MODIFIED',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 160,
      render: (updatedAt: any) => {
        return <p>{moment(tz(updatedAt)).format('D MMM YYYY')}</p>;
      }
    },
    {
      title: '',
      key: 'action',
      width: 40,
      align: 'center' as const,
      render: (data: any  ) => {
        const items: any = [];

        if (data?.fileSystemType === 'Folder') {
          items.push({
            label: <p>Download Folder</p>,
            key: 'DownloadFolder'
          });
          items.push({ type: 'divider' });
        }
        if (data?.fileSystemType === 'Document') {
          items.push({
            label: <p>Download</p>,
            key: 'DownloadDocument'
          });
          items.push({ type: 'divider' });

          items.push({
            label: <p>View Revision</p>,
            key: 'viewRevision'
          });
          items.push({ type: 'divider' });
        }

        if (!viewOnly) {

          if (data?.fileSystemType === 'Document') {
            items.push({
              label: <Button className='resetButton' >Add Revision</Button>,
              key: 'addRevision'
            });
          }

          if (data?.fileSystemType === 'Folder') {
            items.push({ label: <p>Rename</p>, key: 'Rename' });
          }
        }

        return (
          <ThreeDotsDropDown
            refButton={undefined}
            onClick={(e: any) => {
              if (e.key === 'DownloadDocument') {
                onDownload(data.id);
              }
              if (e.key === 'DownloadFolder') {
                onDownloadFolder(data.id, data.name);
              }
              if (e.key === 'Rename') {
                setSelectedFormId(data.id);
                setFormName(data.name);
                form.setFieldValue('name', data.name);
                showEditFolderNameModal();
              }
              if (e.key === 'Delete') {
                showDeleteConfirm(data.id);
              }
              if (e.key === 'MoveFile') {
                setCategory(data.category);
                setFormName(data.name);
                setFileType(data.type);
                setSelectedFormId('' + data.id);
                moveFileModal?.current?.openModal();
              } else if (e.key === 'Manage') {
                setSelectedRowKeys([data.id]);
                setIsManage(true);
              } else if (e.key === 'viewRevision') {
                router.replace({
                  query: {
                    ...router.query,
                    drawingId: data.id
                  }
                });
              } else if (e.key === 'addRevision') {
                setFileId(data.id);
                addRevisionModalRef?.current?.openModal();
              }
            }}
            items={items}
          />
        );
      }
    }
  ];

  // sync modal
  const [syncmodal, showSyncModal, closeSyncModal] = useModal({
    closable: false,
    content: (
      <>
        <SyncModal
          mimeType="pdf"
          isPersonal={false}
          dirId={dirId as string}
          category={Gql.CategoryType.TwoDDrawings}
          onComplete={() => {
            refetch();
            closeSyncModal();
          }}
        />
      </>
    )
  });

  const handleNewMenuClick: MenuProps['onClick'] = e => {
    switch (e.key) {
      case '1': {
        showAddFolderModal();
        break;
      }
      case '2': {
        uploadDocModalRef?.current?.openModal();
        break;
      }
      case '3': {
        showSyncModal();
        break;
      }
    }
  };

  const newMenuProps = {
    items: [
      {
        icon: <Icon name="new-folder" />,
        label: <> Add Folder</>,
        key: '1'
      },
      dirId
        ? {
            icon: <Icon name="file-upload" />,
            label: <>Upload Files</>,
            key: '2'
          }
        : null,
      dirId
        ? {
            icon: <Icon name="import" />,
            label: <>Import</>,
            key: '3'
          }
        : null
    ],
    onClick: handleNewMenuClick
  };

  const onMove = () => {
    // if query.filter has a value, then clear it
    if (queries?.filter?.name) {
      setFilter({ name: { like: '' } });
    }
    moveDocumentsModal?.current?.openModal();
  }

  return (
    <div className="absolute overflow h-full w-full" ref={dropzoneRef}>
      {modal}
      {editFolderNameModal}
      {syncmodal}
      <Spin tip={'Loading...'} spinning={createFolderLoading || documentsDeleting || loading}>
        <div className="flex flex-nowrap items-center justify-between px-5 pt-[20px] pb-[12px]">
          <div>
            <h3>Drawings</h3>
            {dirId && (
              <Breadcrumb>
                <Breadcrumb.Item className="text-gray90 text-md">
                  <Link href="/drawings/2D-drawing">
                    <HomeOutlined />
                  </Link>
                </Breadcrumb.Item>
                {breadcrumbs.map(value => (
                  <Breadcrumb.Item className="text-gray90 text-md" key={value.id}>
                    <a
                      onClick={() => {
                        setSelectedRowKeys([]);
                        router.push(`/drawings/2D-drawing/${value.id}`);
                      }}
                    >
                      {value.name}
                    </a>
                  </Breadcrumb.Item>
                ))}
              </Breadcrumb>
            )}
          </div>
          <Row>
            <Space>
              <Form
                form={searchForm}
                onValuesChange={debounce(data => {
                  setFilter({ name: { like: data.keyword } });
                }, 300)}
              >
                <div style={{paddingTop: '10px'}}>
                  <SearchInput
                    onPressEnter={() => {
                    }}
                    placeholder={'Search'}
                  />
                </div>
              </Form>
              {canCreate && (
                <Dropdown menu={newMenuProps}>
                  <Button
                    type="primary"
                    disabled={viewOnly}
                    icon={<Icon name="plus-white" className="pt-1 mr-1" />}
                    className="h-[41px] mt-[2px] rounded-lg border-gray40"
                  >
                    <Space>New</Space>
                  </Button>
                </Dropdown>
              )}
            </Space>
          </Row>
        </div>

        <Table
          rowSelection={{
            type: 'checkbox',
            selectedRowKeys,
            onChange: onSelectChange,
            hideSelectAll: selectedRowKeys.length === 0,
            getCheckboxProps: (e) => ({
              id: 'manage-checkbox-2d-drawing',
              style: {
                display:  selectedRowKeys.includes(e.id) || e.id === hoveredRowIndex
                  ? 'flex'
                  : 'none',
                justifyContent: 'center',
                alignItems: 'center',
                marginBottom: 10,
              },
            })
          }}
          onRow={(record, index) => {
            const attr = {
              index,            
              className: 'cursor-pointer',
              onSelect: () => handleRowClick(record),
              onMouseEnter: () => onRowHover(record.id), // when mouse enters the row
              onMouseLeave: () => onRowLeave(), // when mouse leaves the row
            };

            return attr as React.HTMLAttributes<any>;
          }}
          className="dashboard-table mb-[30px] px-5"
          dataSource={
            isArray(dataSource)
            ? dataSource?.map((item: any) => ({
                ...item,
                key: item.id
              }))
            : dataSource
          }
          ref={tableRef}
          columns={columns}
          size="small"
          pagination={false}
          tableLayout="auto"
          scroll={{
            scrollToFirstRowOnChange: false,
            y: window.innerHeight - 230
          }}
          loading={loading}
        />

        <PdfTronModal
          ref={pdfTronModalRef}
          documentId={documentId}
          onSavedXFDF={(xfdf: any) => {
            onSavedXfdf(xfdf);
          }}
          onSaved={() => refetch()}
        />
        <TwoDDrawingsUpload
          title='Upload Drawings'
          dirId={dirId as any}
          onSaved={() => refetch()}
          type={Gql.CategoryType.TwoDDrawings}
          ref={uploadDocModalRef}
        />
        {/* TODO: REMOVE THIS BLOCK */}
        <MoveDrawingsModal
          dirId={dirId as any}
          ref={moveFileModal}
          onSaved={() => refetch()}
          selectedFormId={selectedFormId}
          formName={formName}
          fileType={fileType}
          category={category as Gql.CategoryType.TwoDDrawings}
        />

        <MoveBulkCloudDocsModal
          data={dataSource}
          ids={selectedRowKeys}
          dirId={dirId as any}
          ref={moveDocumentsModal}
          onSaved={() => {
            refetch();
            setSelectedRowKeys([]);
            setIsManage(false);
          }}
          category={Gql.CategoryType.TwoDDrawings}
          setSelectedRowKeys={setSelectedRowKeys}
          onLoadMore={onLoadMore}
        />
        <VersionControlDrawer ref={versionControlDrawerRef} />
        <AddRevisionModal ref={addRevisionModalRef} dirId={fileId} />
      </Spin>

      <ManageModal
        data={dataSource}
        selectedRowKeys={selectedRowKeys}
        onBulkDownload={onBulkDownload}
        onMove={onMove}
        canMove={typeof canMove === 'boolean' ? canMove : false}
        showDeletesConfirm={showDeletesConfirm}
        canDelete={canDelete ?? false}
        setSelectedRowKeys={setSelectedRowKeys}
        setIsManage={setIsManage}
      />
    </div>
  );
};

TwoDDrawing.auth = true;
TwoDDrawing.Layout = DrawingFormTabLayout;
export default TwoDDrawing;
