import { ExclamationCircleOutlined, HomeOutlined } from '@ant-design/icons';
import { Icon } from '@commons';
import ManageModal from '@components/ManageModal';
import MoveBulkCloudDocsModal from '@components/MoveBulkCloudDocsModal';
import MoveDrawingsModal from '@components/MoveDrawingsModal';
import { default as ThreeDotsDropDown } from '@components/ThreeDotsDropDown';
import UserAvatar from '@components/UserAvatar';
import AutoDeskForgeModal from '@components/autoDesk-forge/AutoDeskForgeModal';
import BIMDrawingUploadModal from '@components/drawings/BIM-drawing-upload';
import DrawingFormTabLayout from '@components/drawings/DrawingFormTabLayout';
import SearchInput from '@components/forms/FormsInput/SearchInput';
import useModal from '@components/hooks/useModal';
import useQueries from '@components/hooks/useQueries';
import * as Gql from '@graphql';
import apolloClient from '@lib/apollo';
import { nameAlias } from '@utils/app.utils';
import { manageFiles } from '@utils/authority';
import { onError } from '@utils/error';
import { getFileName } from '@utils/filename';
import { tz } from '@utils/timezone';
import {
  Avatar,
  Breadcrumb,
  Button,
  Dropdown,
  Form,
  Input,
  MenuProps,
  message,
  Modal,
  Row,
  Space,
  Spin,
  Table,
  Tooltip,
  Typography
} from 'antd';
import { useForm } from 'antd/lib/form/Form';
import { ColumnsType } from 'antd/lib/table';
import Paragraph from 'antd/lib/typography/Paragraph';
import FileSaver from 'file-saver';
import { debounce, isArray } from 'lodash';
import moment from 'moment';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useEffect, useRef, useState } from 'react';
import { ProjectDocumentApiService } from 'src/api';
import { getFileIcon } from 'src/commons/FileIcon';
import useDropzone from 'src/hooks/useDropzone';

const { Text } = Typography;

const BIMDrawing = () => {
  const router = useRouter();
  const [form] = useForm();
  const AutoDeskForgeModalRef = useRef<any>(null);
  const { confirm } = Modal;
  const documentId = router.query.documentId as string;
  const [selectedFormId, setSelectedFormId] = useState<string>();
  const [projectUserRole, setProjectUserRole] = useState<string>('');
  const [dataSource, setDataSource] = useState<Gql.GetProjectDocumentsQuery['getProjectDocuments']['nodes']>([]);
  const uploadDocModalRef = useRef<any>(null);
  const [breadcrumbs, setBreadcrumbs] = useState<
    Gql.GetProjectDocumentsBreadcrumbQuery['getProjectDocumentsBreadcrumb']
  >([]);
  const dirId = router.query.dir && router.query.dir[0];
  const [getBreadcrumbs, { data: breadcrumbData }] = Gql.useGetProjectDocumentsBreadcrumbLazyQuery();
  const [fileType, setFileType] = useState<string>('');
  const [formName, setFormName] = useState<string>('');
  const [category, setCategory] = useState<string>('');
  const moveFileModal = useRef<any>(null);
  const moveDocumentsModal = useRef<any>(null);
  const [loading, setLoading] = useState(false);
  const [searchForm] = useForm();
  const [bimStatusLoad, setBimStatusLoad] = useState(false);
  const [fileId, setFileId] = useState<string>('');
  const [hoveredRowIndex, setHoveredRowIndex] = useState<any>(null);

  const tableRef = useRef<any>(null);
  const inputRef = useRef<any>(null);

  // For multiple selection
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  const dropzoneRef = useRef<any>(null);
  useDropzone({ dropzoneRef, pushModal: uploadDocModalRef?.current?.openModal });

  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  useEffect(() => {
    setFilter({ name: { like: '' } });

    if (!dirId) {
      setBreadcrumbs([]);
      return;
    }

    getBreadcrumbs({
      variables: { input: { id: parseInt(dirId as string) } }
    }).then(res => {
      setBreadcrumbs(res.data?.getProjectDocumentsBreadcrumb ?? []);
    });
  }, [dirId, getBreadcrumbs, breadcrumbData]);

  useEffect(() => {
    setProjectUserRole(localStorage.getItem('ProjectUserRole') || '');
  }, []);

  const viewOnly = projectUserRole === 'CanView';
  const { canCreate, canDelete, canMove } = manageFiles(projectUserRole, dirId);

  const handleTableScroll = (e: { target: any }) => {
    const { target } = e;
    if (target.scrollHeight - target.scrollTop <= target.clientHeight + 1) {
      onLoadMore();
    }
  };

  const { queries, setFilter } = useQueries<Gql.ProjectDocumentFilter, Gql.ProjectDocumentSort>({
    usePagingParam: false,
    paging: { offset: 0, limit: 20 }
  });

  const {
    data,
    refetch,
    loading: loadingProjectDocument
  } = Gql.useGetProjectDocumentsQuery({
    variables: {
      ...queries,
      filter: {
        ...queries.filter,
        ...{
          projectDocumentId: {
            ...(dirId ? { eq: dirId?.toString() } : { eq: null })
          },
          category: { eq: Gql.CategoryType.BimDrawings }
        }
      }
    },
    onError: onError
  });

  useEffect(() => {
    setDataSource(data?.getProjectDocuments?.nodes ?? []);
  }, [data]);

  // refetch the data when dirId is null
  useEffect(() => {
    if (!dirId) {
      refetch();
    }
  }, [dirId]);

  // update dataSource state onLoadMore function is fire
  useEffect(() => {
    setDataSource(data?.getProjectDocuments?.nodes ?? []);

    if (tableRef.current) {
      const tableBody = tableRef.current.querySelector('.ant-table-body');
      tableBody.addEventListener('scroll', handleTableScroll);
    }
    return () => {
      if (tableRef.current) {
        const tableBody = tableRef.current.querySelector('.ant-table-body');
        tableBody.removeEventListener('scroll', handleTableScroll);
      }
    };
  }, [handleTableScroll]);

  const onLoadMore = async () => {
    try {
      if (!data?.getProjectDocuments.pageInfo.hasNextPage) return;
      setLoading(true);
      await refetch({
        paging: {
          offset: 0,
          limit: data.getProjectDocuments.nodes.length + 20
        }
      }).then(res => {
        setDataSource([...dataSource, ...(res.data?.getProjectDocuments?.nodes ?? [])]);

        setLoading(false);
      });
    } catch (e) {
      onError(e);
    }
  };

  const [deleteDocuments, { loading: documentsDeleting }] = Gql.useDeleteProjectDocumentsMutation({
    onCompleted: () => {
      message.success('Deleted successfully');
      setSelectedRowKeys([]);
      refetch();
    },
    onError: onError as any
  });

  const showDeletesConfirm = () => {
    const deletedDocumentNames = dataSource
      .filter((doc: any) => selectedRowKeys.includes(doc.id.toString()))
      .map((doc: any) => doc.name)
      .slice(0, 10);

    confirm({
      title: 'Are you sure?',
      icon: <ExclamationCircleOutlined />,
      content: (
        <>
          <div>
            Do you really want to delete the{' '}
            {selectedRowKeys.length < 2 && (
              <span className="font-semibold">
                {deletedDocumentNames[0]} <span className="font-normal">?</span>
              </span>
            )}
          </div>
          {selectedRowKeys.length > 1 && (
            <ol className="list-decimal mt-2">
              {deletedDocumentNames.map((name: string, index: number) => (
                <li className="my-2 font-semibold" key={index}>
                  {name}
                </li>
              ))}
            </ol>
          )}
        </>
      ),
      okText: 'Yes',
      okType: 'danger',
      cancelText: 'No',
      onOk() {
        deleteDocuments({
          variables: {
            input: {
              ids: selectedRowKeys.map(id => parseInt(id as string))
            }
          }
        });
        setSelectedRowKeys([]);
      },
      onCancel() {}
    });
  };

  // check file type and show modal
  const checkType = async (id: string) => {
    const res = await apolloClient.query<Gql.ProjectDocumentQuery, Gql.ProjectDocumentQueryVariables>({
      query: Gql.ProjectDocumentDocument,
      variables: { id }
    });

    if (res.data.projectDocument?.type === 'pdf') {
      // pdfTronModalRef?.current?.openModal();
    }
    if (res.data.projectDocument?.type !== 'pdf') {
      AutoDeskForgeModalRef?.current?.openModal();
    }
  };

  useEffect(() => {
    if (router.query.documentId) {
      checkType(router.query.documentId as string);
    }
  }, [router.query.documentId]);

  const [createFolder, { loading: createFolderLoading }] = Gql.useCreateOneProjectDocumentMutation({
    onCompleted: () => {
      form.resetFields();
      message.success('Folder created successfully');
      refetch();
      closeAddFolderModal();
    },
    onError: onError as any
  });

  const [deleteFolder] = Gql.useDeleteProjectDocumentMutation({
    onCompleted: () => {
      message.success('Deleted successfully');
      refetch();
    },
    onError: onError as any
  });

  const onFinish = (values: Gql.CreateProjectDocumentInputDto) => {
    const { name } = values;
    const category = Gql.CategoryType.BimDrawings;
    const fileSystemType = Gql.FileSystemType.Folder;
    const type = 'folder';
    const updatedName = getFileName(type, name ?? '', '');
    const projectDocumentId = dirId as string;
    createFolder({
      variables: {
        input: {
          projectDocument: {
            name: updatedName,
            category,
            fileSystemType,
            type,
            projectDocumentId
          }
        }
      }
    });
  };

  const onDelete = (id: string) => {
    deleteFolder({ variables: { id: parseInt(id) } });
  };

  const showDeleteConfirm = (id: string) => {
    confirm({
      title: 'Are you sure?',
      icon: <ExclamationCircleOutlined />,
      content: 'Do you really want to delete this project?',
      okText: 'Yes',
      okType: 'danger',
      cancelText: 'No',
      onOk() {
        onDelete(id);
      },
      onCancel() {}
    });
  };

  const [modal, showAddFolderModal, closeAddFolderModal] = useModal({
    title: 'Create folder',
    content:
      (setTimeout(() => {
        inputRef?.current?.focus();
      }, 100),
      (
        <Form layout="vertical" form={form} onFinish={onFinish} requiredMark={false}>
          <Form.Item label="Folder name" name="name" rules={[{ required: true, message: 'Folder name is required!' }]}>
            <Input ref={inputRef} />
          </Form.Item>
          <div className="flex justify-end">
            <Button htmlType="submit" type="primary" disabled={viewOnly}>
              Create
            </Button>
          </div>
        </Form>
      ))
  });

  const [updateName] = Gql.useUpdateOneProjectDocumentMutation({
    onCompleted: () => {
      message.success('Saved successfully');
      closeEditFolderNameModal();
      refetch();
      form.setFieldValue('name', '');
    },
    onError: onError as any
  });

  const onUpdate = (values: Gql.UpdateProjectDocumentInputDto) => {
    const selectedDocument = dataSource?.find(data => data.id === selectedFormId) ?? null;
    const type = selectedDocument?.type ?? '';
    const updatedName = getFileName(type, values.name ?? '', formName);
    if (!values.name) {
      return message.error('Folder name is required');
    }

    updateName({
      variables: {
        input: {
          id: selectedFormId?.toString() ?? '',
          update: {
            name: updatedName ?? ''
          }
        }
      }
    });
  };

  // Edit Form Category Modal
  const [editFolderNameModal, showEditFolderNameModal, closeEditFolderNameModal] = useModal({
    title: 'Edit name',
    content:
      (setTimeout(() => {
        inputRef?.current?.focus();
      }, 100),
      (
        <Form layout="vertical" form={form} requiredMark={false} onFinish={onUpdate} initialValues={{}}>
          <Form.Item label="Folder/File name" name="name">
            <Input defaultValue={formName} ref={inputRef} />
          </Form.Item>
          <div className="flex justify-end">
            <Button htmlType="submit" type="primary">
              Save
            </Button>
          </div>
        </Form>
      ))
  });

  const checkBIMStatus = async (urn: string) => {
    setBimStatusLoad(true);
    try {
      const status = await ProjectDocumentApiService.bimStatus({
        body: {
          urn
        }
      })
        .catch(err => {
          message.error('Something went wrong');
        })
        .finally(() => {
          setBimStatusLoad(false);
        });
      return status;
    } catch (e) {
      onError(e);
    }
  };

  const onRowHover = (id: any) => {
    setHoveredRowIndex(id);
  };

  const onRowLeave = () => {
    setHoveredRowIndex(null);
  };

  const columns: ColumnsType<any> = [
    {
      title: 'NAME',
      key: 'name',
      render: (data: any) => {
        const autoDeskMetadata = data?.autoDeskMetadata;
        const getMetadata = autoDeskMetadata && JSON?.parse(autoDeskMetadata || '');
        return (
          <Space className="p-1" align="start" key={data.id}>
            {getFileIcon(data.type as string)}
            <div
              className={`cursor-pointer ${data?.fileSystemType !== 'Folder' && 'mt-1'}`}
              onClick={async () => {
                if (data?.fileUrl && data?.fileSystemType != 'Folder') {
                  setFileId(data?.id);

                  const res = await checkBIMStatus(data?.fileUrl);

                  if (res.progress === 'inprogress') {
                    Modal.confirm({
                      title: 'BIM File Processing',
                      content:
                        'Your BIM file is currently being processed. Please wait a few moments while we prepare it for viewing.',
                      okButtonProps: {
                        disabled: true
                      }
                    });
                  } else if (res.status === 'failed') {
                    // show the warning message
                    Modal.confirm({
                      title: 'BIM File Upload Failed',
                      content: (
                        <div>
                          We couldn’t process your BIM file. Please try uploading it again. <br /> <br />{' '}
                          {res.failedDerivative && (
                            <>
                              <Text strong> Effected file: </Text>${res.failedDerivative.name}
                            </>
                          )}
                        </div>
                      ),
                      okButtonProps: {
                        disabled: true
                      }
                    });
                  } else if (res.status === 'success' && res.progress === 'complete') {
                    router.replace({
                      query: {
                        ...router.query,
                        documentId: data.id
                      }
                    });
                  }
                }

                if (data?.fileSystemType === 'Folder') {
                  setFilter({ name: { like: '' } });
                  setSelectedRowKeys([]);
                  router.push(`/drawings/BIM-drawing/${data.id}`);
                }
              }}
            >
              <Tooltip
                title={data?.name}
                mouseEnterDelay={0.5}
                placement="topLeft"
                overlayStyle={{ maxWidth: '550px' }}
              >
                <Paragraph
                  className="font-medium"
                  style={{ margin: 0, width: 550 }}
                  ellipsis={{ rows: 2, expandable: false }}
                >
                  {data?.name} {getMetadata?.processingLog && <Icon className="ml-2" name="pl-error" />}
                  {bimStatusLoad && data?.id === fileId && <Spin className="ml-3" size="small" />}
                </Paragraph>
              </Tooltip>
            </div>
          </Space>
        );
      }
    },
    {
      title: 'FILE SIZE',
      key: 'fileSize',
      width: 120,
      render: (data: any) => {
        return data.fileSystemType === Gql.FileSystemType.Document ? (
          <p> {data?.fileSize ? `${data?.fileSize} MB` : '-'}</p>
        ) : (
          '-'
        );
      }
    },
    {
      title: 'FILE TYPE',
      dataIndex: 'type',
      key: 'type',
      width: 120,
      render: (type: any) => {
        return <p>{type.toUpperCase()}</p>;
      }
    },
    {
      title: 'ADDED BY',
      dataIndex: 'owner',
      key: 'owner',
      width: 160,
      render: (owner: any, index: any) => {
        return (
          <UserAvatar
            key={index}
            username={owner?.name}
            src={owner?.avatar}
            style={{ backgroundColor: !owner?.avatar ? owner?.color || "" : "transparent", // Apply only if no avatar
              color: "#ffffff"
            }}
            tooltip={owner?.name}
            align={{
              offset: [-12, -9], 
              }}
          />
        );
      }
    },
    {
      title: 'LAST MODIFIED',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 160,
      render: (updatedAt: any) => {
        return <p>{moment(tz(updatedAt)).format('D MMM YYYY')}</p>;
      }
    },
    {
      title: '',
      key: 'action',
      align: 'center' as const,
      render: (data: any) => {
        const items: any = [];

        if (viewOnly) {
          items.push({ label: <p>Manage</p>, key: 'Manage' });
        } else {
          items.push({ label: <p>Rename</p>, key: 'Rename' });
          items.push({ type: 'divider' });
          items.push({ label: <p>Manage</p>, key: 'Manage' });
        }

        return (
          <ThreeDotsDropDown
            onClick={(e: any) => {
              if (e.key === 'Rename') {
                setFormName(data.name);
                form.setFieldValue('name', data.name);
                setSelectedFormId(data.id);
                showEditFolderNameModal();
              }
              if (e.key === 'Delete') {
                showDeleteConfirm(data.id);
              }
              if (e.key === 'MoveFile') {
                setCategory(data.category);
                setFormName(data.name);
                setFileType(data.type);
                setSelectedFormId('' + data.id);
                moveFileModal?.current?.openModal();
              }
              if (e.key === 'Manage') {
                setSelectedRowKeys([data.id]);
              }
            }}
            items={items}
          />
        );
      }
    }
  ];

  const handleNewMenuClick: MenuProps['onClick'] = e => {
    switch (e.key) {
      case '1': {
        showAddFolderModal();
        break;
      }
      case '2': {
        uploadDocModalRef?.current?.openModal();
        break;
      }
    }
  };

  const newMenuProps = {
    items: [
      {
        label: 'Add Folder',
        key: '1'
      },
      {
        label: 'Upload Files',
        key: '2'
      }
    ],
    onClick: handleNewMenuClick
  };

  return (
    <div className="absolute overflow w-full" ref={dropzoneRef}>
      {modal}
      {editFolderNameModal}

      <Spin tip={'Loading...'} spinning={createFolderLoading || documentsDeleting || loading}>
        <div className="flex flex-nowrap items-center justify-between px-5 pt-[20px] pb-[12px]">
          <div>
            <h3>BIM Models</h3>
            {dirId && (
              <Breadcrumb>
                <Breadcrumb.Item className="text-gray90 text-md">
                  <Link href="/drawings/BIM-drawing">
                    <HomeOutlined />
                  </Link>
                </Breadcrumb.Item>
                {breadcrumbs.map(value => (
                  <Breadcrumb.Item className="text-gray90 text-md" key={value.id}>
                    <a
                      onClick={() => {
                        setSelectedRowKeys([]);
                        router.push(`/drawings/BIM-drawing/${value.id}`);
                      }}
                    >
                      {value.name}
                    </a>
                  </Breadcrumb.Item>
                ))}
              </Breadcrumb>
            )}
          </div>
          <Row className="items-center">
            <Space>
              <Form
                form={searchForm}
                onValuesChange={debounce(data => {
                  setFilter({ name: { like: data.keyword } });
                }, 300)}
              >
                <div style={{ paddingTop: '10px' }}>
                  <SearchInput onPressEnter={() => {}} placeholder={'Search'} />
                </div>
              </Form>
              {canCreate && (
                <Dropdown menu={newMenuProps}>
                  <Button
                    type="primary"
                    disabled={viewOnly}
                    className="h-[40px] rounded-lg border-gray40"
                    icon={<Icon name="plus-white" className="pt-1 mr-1" />}
                  >
                    <Space>New</Space>
                  </Button>
                </Dropdown>
              )}
            </Space>
          </Row>
        </div>

        <Table
          rowSelection={{
            type: 'checkbox',
            selectedRowKeys,
            onChange: onSelectChange,
            hideSelectAll: selectedRowKeys.length === 0,
            getCheckboxProps: e => {
              return {
                style: {
                  display: selectedRowKeys.includes(e.id) || e.id === hoveredRowIndex ? 'flex' : 'none',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginBottom: 10
                }
              };
            }
          }}
          className="dashboard-table mb-[30px] px-5"
          dataSource={
            isArray(dataSource)
              ? dataSource?.map((item: any) => ({
                  ...item,
                  key: item.id
                }))
              : dataSource
          }
          onRow={(data, index) => {
            const attr = {
              index,
              onMouseEnter: () => onRowHover(data.id), // when mouse enters the row
              onMouseLeave: () => onRowLeave() // when mouse leaves the row
            };
            return attr as React.HTMLAttributes<any>;
          }}
          columns={columns}
          size="small"
          tableLayout="auto"
          pagination={false}
          scroll={{
            scrollToFirstRowOnChange: false,
            y: window.innerHeight - 220
          }}
          loading={loading || loadingProjectDocument}
          ref={tableRef}
        />

        <AutoDeskForgeModal
          ref={AutoDeskForgeModalRef}
          documentId={documentId}
          onSaved={() => refetch()}
          onClose={() => refetch()}
        />
        <MoveDrawingsModal
          dirId={dirId as any}
          ref={moveFileModal}
          onSaved={() => refetch()}
          selectedFormId={selectedFormId}
          formName={formName}
          fileType={fileType}
          category={category}
        />

        <MoveBulkCloudDocsModal
          data={dataSource}
          ids={selectedRowKeys}
          dirId={dirId as any}
          ref={moveDocumentsModal}
          onSaved={() => {
            refetch();
            setSelectedRowKeys([]);
          }}
          category={Gql.CategoryType.BimDrawings}
          setSelectedRowKeys={setSelectedRowKeys}
          onLoadMore={onLoadMore}
        />
        <BIMDrawingUploadModal
          ref={uploadDocModalRef}
          onCompleted={async id => {
            await router.replace({ query: { ...router.query, documentId: id } });
          }}
        />
      </Spin>

      <ManageModal
        canMove={canMove as boolean}
        onMove={() => {
          moveDocumentsModal?.current?.openModal();
        }}
        selectedRowKeys={selectedRowKeys}
        showDeletesConfirm={showDeletesConfirm}
        canDelete={canDelete ?? false}
        data={dataSource}
        setSelectedRowKeys={setSelectedRowKeys}
      />
    </div>
  );
};

BIMDrawing.auth = true;
BIMDrawing.Layout = DrawingFormTabLayout;
export default BIMDrawing;
