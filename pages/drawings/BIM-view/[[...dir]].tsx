import { Icon } from '@commons';
import { Drawer } from '@commons/Drawer';
import AssetsModal from '@components/autoDesk-forge/AssetsModal';
import { initializeGanttChart } from '@components/autoDesk-forge/extension/Phasing';
import { initializeSpriteExtension, loadSprite, refetchingSprite } from '@components/autoDesk-forge/extension/Sprite';
import Center from '@components/Center';
import BimComments from '@components/cloud-docs/drawer/bim-comments';
import TaskAddDocsAndRfiDrawer from '@components/tasks/TaskAddDocsAndRfiDrawer';
import TaskAddDrawer from '@components/tasks/TaskAddDrawer';
import TaskAddPhotosDrawer from '@components/tasks/TaskAddPhotosDrawer';
import TaskEditDocsAndRfiDrawer from '@components/tasks/TaskEditDocsAndRfiDrawer';
import TaskEditDrawer from '@components/tasks/TaskEditDrawer';
import TaskEditDrawingsDrawer from '@components/tasks/TaskEditDrawingsDrawer';
import TaskEditPhotosDrawer from '@components/tasks/TaskEditPhotosDrawer';
import * as Gql from '@graphql';
import { addLabelToToolbarButton } from '@utils/app.utils';
import { onError } from '@utils/error';
import { Avatar, Button, Checkbox, Form, Spin, Typography, message } from 'antd';
import TextArea from 'antd/lib/input/TextArea';
import _ from 'lodash';
import { useRouter } from 'next/router';
import { useEffect, useRef, useState } from 'react';
import { useReactToPrint } from 'react-to-print';
import { IntegrationApiService } from 'src/api';
const { Text } = Typography;

const AutoDeskBim = () => {
  const router = useRouter();
  const docId = router.query.documentId as string;
  const projectId = router.query.projectId as string;
  const { documentId, elementId, spriteId } = router.query as any;
  const viewer3DRef = useRef<any>(null);
  const taskAddDrawerRef = useRef<any>(null);
  const taskAddPhotosDrawerRef = useRef<any>(null);
  const taskAddDocsAndRfiDrawerRef = useRef<any>(null);
  const taskEditDrawerRef = useRef<any>(null);
  const taskEditPhotosDrawerRef = useRef<any>(null);
  const taskEditDrawingsDrawerRef = useRef<any>(null);
  const taskEditDocsAndRfiDrawerRef = useRef<any>(null);
  const [showProcessingLog, setShowProcessingLog] = useState(false);
  const drawerRef = useRef<any>(null);
  const [screenshot, setScreenshot] = useState(null);
  const [drawerType, setDrawerType] = useState('');
  const [isResolveClick, setIsResolveClick] = useState(false);
  const [filter, setFilter] = useState<any>({ isCommentResolve: { is: false } });
  const [takingScreenshot, setTakingScreeshot] = useState(false);
  const [asset, setAsset] = useState<boolean>(false);
  const [view, setView] = useState<boolean>(false);
  const [assets, setAssets] = useState<{ assetId: number; name: string }>({ assetId: 0, name: '' });
  const assetRef = useRef(asset);

  useEffect(() => {
    assetRef.current = asset;
  }, [asset]);
  const [groupedTasks, setGroupedTasks] = useState<any[]>([]);
  const [ganttLoading, setGanttLoading] = useState(true);

  const forgeViewer2Ref = useRef(null);
  const imgRef = useRef(null);
  const handlePrint = useReactToPrint({
    content: () => imgRef.current,
    pageStyle: `
          @page {
            size: landscape;
            margin: 0;
          }
          @media print {
            body {
              -webkit-print-color-adjust: exact;
              margin: 0;
              padding: 0;
              display: flex;
              align-items: center;
              justify-content: center;
              height: 100vh;
              background-color: #fff;
            }
          }
        `
  });
  const [form] = Form.useForm();

  const spriteImage = {
    task: '/assets/TaskSprite.png',
    drawing: '/assets/DrawingSprite.png',
    document: '/assets/DocumentSprite.png',
    photos: '/assets/PhotosSprite.png'
  };

  const { data: userData } = Gql.useGetUserMeQuery({ onError: onError as any });
  const [getBimAsset, { data: bimAssets }] = Gql.useGetBimAssetsLazyQuery({
    variables: {
      filter: {
        projectDocumentId: {
          eq: documentId
        }
      }
    }
  });

  const { data: task, refetch } = Gql.useGetTasksQuery({
    variables: {
      filter: {
        bimId: {
          eq: documentId
        }
      },
      paging: {
        limit: 1000
      }
    }
  });

  const takeScreenshot = async (viewer: any) => {
    setTakingScreeshot(true); // Set loading state to true

    try {
      await new Promise<void>((resolve, reject) => {
        viewer?.getScreenShot(1920, 1080, (blobURL: any) => {
          if (blobURL) {
            setScreenshot(blobURL);
            resolve(); // Resolve when screenshot is successful
          } else {
            reject('Screenshot failed'); // Reject if something goes wrong
          }
        });
      });
    } catch (error) {
    } finally {
      setTakingScreeshot(false); // Set loading state to false after completion
    }
  };

  const [fetchedTask, { data: fetchedData }] = Gql.useGetTaskLazyQuery();

  const [updateProjectDocument] = Gql.useUpdateOneProjectDocumentMutation({
    onCompleted: () => {
      projectDocumentRefetch();
      message.success('Comment updated successfully.');
    },
    onError: onError as any
  });

  const tasks = task?.tasks?.nodes ?? [];

  const deleteSprite = async (id: string) => {
    const dataVizExt = viewer3DRef.current.getExtension('Autodesk.DataVisualization');
    dataVizExt.removeAllViewables();
    const newTasks = tasks.filter(task => task.id !== id);
    loadSprite(viewer3DRef.current, newTasks);
  };

  const refetchAddSprite = async (id: any) => {
    const arrayTask = [...tasks];
    fetchedTask({
      variables: {
        id: id
      }
    }).then((res: any) => {
      arrayTask.push(res.data.task);
      refetchingSprite(
        viewer3DRef.current,
        arrayTask,
        taskEditDrawerRef,
        taskEditPhotosDrawerRef,
        taskEditDocsAndRfiDrawerRef,
        taskEditDrawingsDrawerRef
      );
    });
  };

  const options = {
    env: 'AutodeskProduction2',
    api: 'streamingV2',
    getAccessToken: function (onTokenReady: any) {
      IntegrationApiService.autodeskAuth().then((accessToken: string) => {
        var token = accessToken;
        var timeInSeconds = 3599;
        onTokenReady(token, timeInSeconds);
      });
    },
    compass: true
  };

  const [getDoc, { data }] = Gql.useProjectDocumentLazyQuery({
    onCompleted: data => {
      const metadata = data?.projectDocument?.autoDeskMetadata;
      const metadataVal = JSON.parse(metadata);
      if (metadataVal?.processingLog) setShowProcessingLog(true);
    }
  });

  const { data: projectDocuments, refetch: projectDocumentRefetch } = Gql.useGetProjectDocumentsQuery({
    variables: {
      filter: {
        projectDocumentId: {
          eq: documentId
        },
        projectId: {
          eq: projectId
        },
        category: {
          eq: Gql.CategoryType.Photo
        }
      },
      paging: {
        limit: 50,
        offset: 0
      }
    }
  });

  const photos = projectDocuments?.getProjectDocuments?.nodes ?? [];
  const resolvedPhoto = photos.filter((photo: any) => photo.isCommentResolve);
  const unresolvedPhoto = photos.filter((photo: any) => !photo.isCommentResolve);
  const getCommentData = isResolveClick ? resolvedPhoto : unresolvedPhoto;

  const [createPost, { loading: creatingPost }] = Gql.useCreateOneProjectDocumentMutation({
    onCompleted: () => {
      refetch();
      projectDocumentRefetch();
    },
    onError: onError as any
  });

  useEffect(() => {
    if (docId) getDoc({ variables: { id: docId } });
  }, [docId]);

  useEffect(() => {
    executeBim();
  }, [data?.projectDocument?.fileUrl, docId]);

  const executeBim = async () => {
    if (data?.projectDocument?.fileUrl || showProcessingLog) {
      //@ts-ignore
      const viewer = Autodesk.Viewing;
      const metadata = data?.projectDocument?.autoDeskMetadata;
      const metadataVal = JSON.parse(metadata);
      const urn = data?.projectDocument?.fileUrl;

      const config = {
        extensions: [
          'Autodesk.AEC.LevelsExtension',
          'Autodesk.AEC.Minimap3DExtension',
          'Autodesk.DocumentBrowser',
          'Autodesk.Explode'
        ]
      };

      //@ts-ignore
      Autodesk.Viewing.Initializer(options, async function async() {
        const htmlDiv3D = document.getElementById('forgeViewer2');
        const htmlDivDrawer = document.getElementById('bimDrawerView');
        //@ts-ignore
        const viewer3D = new Autodesk.Viewing.GuiViewer3D(htmlDiv3D as any, config);
        //@ts-ignore

        viewer3DRef.current = viewer3D;
        await viewer3D.start();
        const properties = {};
        getBimAsset().then((data: any) => {
          const bimAssets = data.data.bimAssets.nodes;
          bimAssets.forEach((asset: any) => {
            //@ts-ignore
            if (!properties[asset.dbId]) {
              //@ts-ignore
              properties[asset.dbId] = {
                Assets: {
                  [asset.assetKey]: asset.assetValue
                }
              };
            } else {
              //@ts-ignore
              properties[asset.dbId]['Assets'][asset.assetKey] = asset.assetValue;
            }
          });
        });

        if (metadataVal) {
          metadataVal?.urns.map((m: any, index: number) => {
            //@ts-ignore
            Autodesk.Viewing.Document.load(`urn:${m.urnInBase64}`, doc => {
              var viewables = doc.getRoot().getDefaultGeometry();

              viewer3D
                .loadDocumentNode(doc, viewables, {
                  keepCurrentModels: true,
                  modelSpace: true, // 2D drawings
                  applyRefPoint: true, // 3D shared coordinates
                  applyScaling: 'm', // force all models to same scale
                  globalOffset: { x: 0, y: 0, z: 0 }
                })
                .then(onLoadFinish);
            });
          });
        } else {
          //@ts-ignore
          Autodesk.Viewing.Document.load(`urn:${urn}`, onDocumentLoadSuccess, onDocumentLoadFailure);
        }

        function onDocumentLoadSuccess(viewerDocument: any) {
          var nodes = viewerDocument.getRoot().search({ type: 'geometry' });       

          if (data?.projectDocument?.type?.toLowerCase?.() === 'nwd') {
            viewer3D?.loadDocumentNode(viewerDocument, nodes[0]);
          } else if (data?.projectDocument?.type?.toLowerCase?.() === 'fbx') {
            viewer3D?.loadDocumentNode(viewerDocument, nodes[0]);
          } else if (data?.projectDocument?.type?.toLowerCase?.() === 'dwf') {
            viewer3D?.loadDocumentNode(viewerDocument, nodes[0]);
          } else {
            viewer3D?.loadDocumentNode(viewerDocument, nodes[0]);
          }

          initializeSpriteExtension(viewer3DRef.current);
          refetchingSprite(
            viewer3DRef.current,
            tasks,
            taskEditDrawerRef,
            taskEditPhotosDrawerRef,
            taskEditDocsAndRfiDrawerRef,
            taskEditDrawingsDrawerRef
          );
        }

        function onDocumentLoadFailure(...a: any) {
          
          message.error('Failed fetching Forge manifest');
        }

        async function onLoadFinish() {
          takeScreenshot(viewer3DRef.current);
          viewer3D.loadExtension('CustomPropertiesExtension', { properties });
        }

        viewer3D.addEventListener(viewer.AGGREGATE_SELECTION_CHANGED_EVENT, (event: any) => {
          if (assetRef.current && event?.selections?.[0]?.dbIdArray) {
            viewer3D.getProperties(event?.selections?.[0]?.dbIdArray[0], (data: any) => {
              setView(true);
              setAssets({
                assetId: event?.selections?.[0]?.dbIdArray?.[0],
                name: data.name
              });
            });
          }
        });
        viewer3D.addEventListener(viewer.GEOMETRY_LOADED_EVENT, async () => {
          const type = data?.projectDocument?.type?.toLowerCase();
          if (type && (type === 'nwd' || type === 'nwc' || type === 'nwf')) {
            await getLeafNodesWithTimeliner(viewer3D);
          }
        });

        viewer3D.addEventListener(viewer.TOOLBAR_CREATED_EVENT, async (e: any) => {
          let toolbar = e.target.toolbar;
          let group = new viewer.UI.ControlGroup('my-custom-view-toolbar');

          // Assets Button
          const customButton = new viewer.UI.Button('Assets');
          customButton.icon.classList.add('adsk-icon-custom');
          customButton.setToolTip('Assets');
          customButton.icon.style.backgroundImage = "url('/assets/asset-management-icon.png')";
          customButton.icon.style.backgroundSize = 'contain';
          customButton.icon.style.width = '26px';
          customButton.icon.style.height = '26px';

          // Create a wrapper div for icon and label
          const wrapper = document.createElement('div');
          wrapper.style.display = 'flex';
          wrapper.style.flexDirection = 'column';
          wrapper.style.alignItems = 'center';

          // Move icon into the wrapper
          wrapper.appendChild(customButton.icon);

          // Create a label element and add it below the icon
          const label = document.createElement('div');
          label.innerText = 'Assets';
          label.style.fontSize = '11px'; // Adjust the size to fit your needs
          label.style.marginTop = '8px'; // Spacing between icon and label
          wrapper.appendChild(label);

          // Replace icon with the wrapper (icon is now contained in the wrapper)
          customButton.container.innerHTML = '';
          customButton.container.appendChild(wrapper);

          group.addControl(customButton);
          toolbar.addControl(group);

          customButton.onClick = function () {
            if (customButton.container.classList.contains('inactive')) {
              customButton.container.classList.remove('inactive');
              customButton.container.classList.add('active');
              setAsset(true);
            } else {
              customButton.container.classList.remove('active');
              customButton.container.classList.add('inactive');
              setAsset(false);
            }
          };

          let getToolbar = viewer3D.toolbar;
          let settingsTools = getToolbar.getControl('settingsTools');
          const toolbarContainer = document.querySelector('.adsk-viewing-viewer .adsk-toolbar') as HTMLElement;

          if (toolbarContainer) {
            toolbarContainer.style.marginBottom = '0px';
          }

          if (settingsTools) {
            settingsTools.removeControl('toolbar-fullscreenTool');
          }

          const modelTools = document.getElementById('modelTools');
          const settingTools = document.getElementById('settingsTools');
          const parent = document.querySelector('.adsk-viewing-viewer');

          const changeTooltipPosition = (id: string) => {
            const tooltip = document.getElementById(id) as HTMLElement;
            if (tooltip) {
              tooltip.style.left = '50px';
              tooltip.style.bottom = '10px';
            }
          };

          if (modelTools && settingTools && parent) {
            await setTimeout(() => {
              modelTools.classList.add('model-tools-class');
              settingTools.classList.add('model-tools-class');

              const newParentDiv = document.createElement('div');
              newParentDiv.id = 'modelToolsParent';
              newParentDiv.className = 'model-tools-parent';

              modelTools.parentNode?.insertBefore(newParentDiv, modelTools);
              newParentDiv.appendChild(modelTools);
              newParentDiv.appendChild(settingTools);
              parent.appendChild(newParentDiv);

              const sectionTool = document.getElementById('toolbar-sectionTool');
              const explodeTool = document.getElementById('toolbar-explodeTool');
              const navTools = document.getElementById('navTools');
              const exploadesubtool = document.querySelector('.explode-submenu');
              const measureTool = document.getElementById('toolbar-measurementSubmenuTool');

              const newHomeWrapperDiv = document.createElement('div');

              changeTooltipPosition('toolbar-documentModels-tooltip');
              changeTooltipPosition('toolbar-levelsTool-tooltip');
              changeTooltipPosition('toolbar-modelStructureTool-tooltip');
              changeTooltipPosition('toolbar-propertiesTool-tooltip');
              changeTooltipPosition('toolbar-settingsTool-tooltip');

              if (navTools && explodeTool && sectionTool && exploadesubtool && measureTool) {
                navTools.appendChild(measureTool);
                navTools.appendChild(sectionTool);
                navTools.appendChild(explodeTool);
                navTools.appendChild(exploadesubtool);
                navTools.appendChild(newHomeWrapperDiv);
              }

              const canvas = document.querySelector('canvas[data-viewer-canvas="true"]') as HTMLCanvasElement;

              if (canvas) {
                canvas.style.minWidth = `${window.innerWidth}px`;
                canvas.style.minHeight = `${window.innerHeight}px`;
              }
            }, 3000);
          }

          // createToolbarButton(
          //   'dashboard-phases-button ',
          //   '/assets/gantt.svg',
          //   'Show Gantt Chart',
          //   group,
          //   () => {
          //     //TODO: full screen
          //     initializeGanttChart(viewer3DRef, ganttDataset);
          //   },
          //   'Gantt'
          // );

          const init = () => {
            Promise.all([
              addLabelToToolbarButton('toolbar-levelsTool', 'Layers'),
              addLabelToToolbarButton('toolbar-documentModels', 'Browse'),
              addLabelToToolbarButton('toolbar-panTool', 'Pan'),
              addLabelToToolbarButton('toolbar-zoomTool', 'Zoom'),
              addLabelToToolbarButton('toolbar-explodeTool', 'Explode'),
              addLabelToToolbarButton('toolbar-orbitTools', 'Orbit'),
              addLabelToToolbarButton('toolbar-cameraSubmenuTool', 'Camera'),
              addLabelToToolbarButton('toolbar-settingsTool', 'Settings'),
              addLabelToToolbarButton('toolbar-modelStructureTool', 'Model'),
              addLabelToToolbarButton('toolbar-propertiesTool', 'Properties'),
              addLabelToToolbarButton('toolbar-sectionTool', 'Section'),
              addLabelToToolbarButton('toolbar-bimWalkTool', 'Person'),
              addLabelToToolbarButton('toolbar-measurementSubmenuTool', 'Measure'),
              addLabelToToolbarButton('homeViewWrapperParent', 'Home')
            ]).then(() => {
              console.log('All labels added');
            });
          };

          if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', init);
          } else {
            init();
          }

          loadSprite(viewer3DRef.current, tasks);
          refetchingSprite(
            viewer3DRef.current,
            tasks,
            taskEditDrawerRef,
            taskEditPhotosDrawerRef,
            taskEditDocsAndRfiDrawerRef,
            taskEditDrawingsDrawerRef
          );
        });      

        let cameraChangeTimeout: any; 

        viewer3DRef.current.addEventListener(viewer.CAMERA_CHANGE_EVENT, () => {
          // Skip if drawer not open
          const drawerDiv = document.querySelector('.ant-drawer-open');
          if (!drawerDiv) return;

          // Clear the previous timeout
          clearTimeout(cameraChangeTimeout);

          // Set a new timeout to execute the screenshot function
          cameraChangeTimeout = setTimeout(() => {
            takeScreenshot(viewer3DRef.current);
          }, 500);
        });
        
      });
    }
  };

  useEffect(() => {
    const type = data?.projectDocument?.type?.toLowerCase();
    
    if (!ganttLoading && groupedTasks.length > 0 && type && (type === 'nwd' || type === 'nwc' || type === 'nwf')) {
      const toolbar = viewer3DRef.current?.toolbar;

      if (toolbar) {
        const existingGroup = toolbar.getControl('settingsTools') || toolbar.getControl('modelTools');

        if (existingGroup) {
          if (!existingGroup.getControl('dashboard-phases-button')) {
            createToolbarButton(
              'dashboard-phases-button',
              '/assets/gantt.svg',
              'Show Gantt Chart',
              existingGroup,
              () => {
                if (groupedTasks.length > 0) {
                  initializeGanttChart(viewer3DRef, groupedTasks);
                } else {
                  message.warning('Gantt data is still loading, please wait.');
                }
              },
              'Gantt'
            );
          }
        } else {
          console.warn('Existing toolbar group not found');
        }
      }
    }
  }, [ganttLoading, groupedTasks, data?.projectDocument?.type]);

  const getAllLeafNodes = (viewer: any) => {
    const instanceTree = viewer.model.getInstanceTree();
    const leafDbIds: number[] = [];

    function recurseThroughNodes(nodeId: number) {
      let childCount = 0;
      instanceTree.enumNodeChildren(nodeId, (childId: number) => {
        recurseThroughNodes(childId);
        childCount++;
      });

      // If the node has no children, it's a leaf node
      if (childCount === 0) {
        leafDbIds.push(nodeId);
      }
    }

    recurseThroughNodes(instanceTree.getRootId()); // Start recursion from root
    return leafDbIds;
  };

  // Function to check if the node has Timeliner properties
  const hasTimelinerProperties = (viewer: any, dbId: number): Promise<boolean> => {
    return new Promise(resolve => {
      viewer.getProperties(dbId, (props: any) => {
        
        const timelinerProps = props.properties.filter((prop: any) => prop.displayCategory.includes('TimeLiner'));

        resolve(timelinerProps.length > 0); // Return true if Timeliner properties exist
      });
    });
  };

  // Get all leaf nodes that have Timeliner properties
  const getLeafNodesWithTimeliner = async (viewer: any) => {
    const leafDbIds = getAllLeafNodes(viewer);
    const taskGroups = new Map();

    for (const dbId of leafDbIds) {
      await new Promise<void>(resolve => {
        viewer.getProperties(dbId, (props: any) => {
          const timelinerData = extractTimelinerProperties(props);
          if (timelinerData) {
            const { taskName, taskStart, taskEnd } = timelinerData;
            const key = `${taskName}-${taskStart}-${taskEnd}`;

            if (!taskGroups.has(key)) {
              taskGroups.set(key, {
                dbIds: [],
                name: taskName,
                start: taskStart,
                end: taskEnd
              });
            }

            const group = taskGroups.get(key);
            group.dbIds.push(timelinerData.dbId);
          }
          resolve();
        });
      });
    }

    const groupedTasksArray = Array.from(taskGroups.values());
    
    setGroupedTasks(groupedTasksArray);
    setGanttLoading(false);
  };

  const extractTimelinerProperties = (props: any) => {
    const timelinerProperties = props.properties.filter((prop: any) => prop.displayCategory === 'TimeLiner');

    if (timelinerProperties.length > 0) {
      const taskName = timelinerProperties.find((prop: any) => prop.displayName.startsWith('Contained in Task:'));
      const taskStart = timelinerProperties.find((prop: any) =>
        prop.displayName.startsWith('Contained in Task Start (Planned):')
      );
      const taskEnd = timelinerProperties.find((prop: any) =>
        prop.displayName.startsWith('Contained in Task End (Planned):')
      );

      return {
        dbId: props.dbId,
        taskName: taskName?.displayValue ?? 'N/A',
        taskStart: taskStart?.displayValue ?? 'N/A',
        taskEnd: taskEnd?.displayValue ?? 'N/A'
      };
    }

    return null;
  };

  const createToolbarButton = (
    buttonId: string,
    buttonIconUrl: string,
    tooltip: string,
    group: any,
    action: any,
    buttonLabel: string
  ) => {
    //@ts-ignore
    const button = new Autodesk.Viewing.UI.Button(buttonId);
    button.setToolTip(tooltip);
    button.onClick = action;

    const icon = button.container.querySelector('.adsk-button-icon');
    if (icon) {
      icon.style.backgroundImage = `url(${buttonIconUrl})`;
      icon.style.backgroundSize = `24px`;
      icon.style.backgroundRepeat = `no-repeat`;
      icon.style.backgroundPosition = `center`;
    }

    const textLabel = document.createElement('div');
    textLabel.className = 'adsk-button-label';
    textLabel.innerText = buttonLabel;
    textLabel.style.fontSize = '10px';
    textLabel.style.fontWeight = 'bold';
    textLabel.style.marginTop = '5px';
    textLabel.style.textAlign = 'center';
    textLabel.style.color = '#868e96';

    button.container.appendChild(textLabel);

    button.container.addEventListener('mouseover', () => {
      textLabel.style.color = '#1CE';
      icon.style.stroke = '#1CE';
    });

    button.container.addEventListener('mouseout', () => {
      textLabel.style.color = '#868e96';
      icon.style.stroke = '#868e96';
    });

    group.addControl(button);
  };

  const downloadScreenshot = () => {
    if (screenshot && drawerType === 'screenshot') {
      const a = document.createElement('a');
      a.href = screenshot;
      a.download = 'screenshot.png';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    }
  };

  const [createComment] = Gql.useCreateProjectDocumentCommentMutation({
    onCompleted: () => {
      form.resetFields();
    },
    onError: onError as any
  });

  const onPost = async (val: any) => {
    if (!screenshot) return;

    // Fetch the blob from the URL
    const response = await fetch(screenshot);
    const blob = await response.blob();

    // Convert the blob into a File
    const file = new File([blob], 'bim-screenshot.png', { type: 'image/png' });

    const res = await createPost({
      variables: {
        input: {
          projectDocument: {
            category: Gql.CategoryType.Photo,
            projectId: projectId,
            fileSystemType: Gql.FileSystemType.Document,
            isBimPhoto: true,
            fileUrl: file,
            projectDocumentId: documentId
            // fileUrl: fileUrl
          }
        }
      }
    });

    createComment({
      variables: {
        input: {
          commentType: Gql.ProjectDocumentCommentType.Bim,
          projectDocumentId: res?.data?.createOneProjectDocument?.id ?? '',
          message: val?.message,
          // mentions: mentions,
          userId: parseInt(userData?.getUserMe?.id ?? '')
        }
      }
    });
  };

  const autoDeskMetadata = data?.projectDocument?.autoDeskMetadata;
  const getMetadata = autoDeskMetadata && JSON?.parse(autoDeskMetadata || '');

  let drawerBtn = null;
  if (drawerType === 'screenshot') {
    drawerBtn = (
      <Button type="primary" onClick={() => downloadScreenshot()}>
        Download
      </Button>
    );
  } else if (drawerType === 'print') {
    drawerBtn = (
      <Button onClick={() => handlePrint()} type="primary">
        Print
      </Button>
    );
  }

  const onResolve = async (id: string, isResolved: boolean) => {
    await updateProjectDocument({
      variables: {
        input: {
          id: id,
          update: {
            isCommentResolve: isResolved
          }
        }
      }
    });
  };

  const loading = creatingPost || takingScreenshot;

  return (
    <>
      <div>
        <div className="bg-white p-2 border-black " style={{ borderBottom: '0.2em solid #E9F0F4' }}>
          <Icon name="logo-transparent" />
        </div>

        <div className="p-2 bg-white flex items-center space-x-2">
          <div className="flex flex-1 space-x-2 items-center">
            <Icon name="bim" />
            <Text strong>BIM Models</Text>
            <Icon name="chevron-right" />
            <Text>{data?.projectDocument?.name}</Text>
            {getMetadata?.processingLog && <Icon name="pl-error" className="ml-2" />}
          </div>
          <Center className="space-x-4 text-sm">
            <Center
              className="flex-col cursor-pointer"
              onClick={() => {
                setDrawerType('comments');
                drawerRef?.current?.pushDrawer();
              }}
            >
              <Icon name="bim-comment" />
              <Text color="#50555C">Comments</Text>
            </Center>

            <Center
              className="flex-col cursor-pointer"
              onClick={() => {
                setDrawerType('screenshot');
                drawerRef?.current?.pushDrawer();
              }}
            >
              <Icon name="screenshot" />
              <Text color="#50555C">Screenshot</Text>
            </Center>

            <Center
              className="flex-col cursor-pointer mx-2"
              onClick={() => {
                setDrawerType('print');
                drawerRef?.current?.pushDrawer();
              }}
            >
              <Icon name="print" />
              <Text color="#50555C">Print</Text>
            </Center>
          </Center>
        </div>

        <div style={{ height: '84vh' }} className="relative">
          <div id="forgeViewer2" ref={forgeViewer2Ref} style={{ width: '100%', height: '100%' }} />
        </div>
      </div>

      <TaskAddDrawer
        ref={taskAddDrawerRef}
        elementId={elementId}
        spriteId={spriteId}
        bimId={documentId}
        spriteImage={spriteImage.task}
        refetch={refetch}
        refetchAddSprite={(id: string) => refetchAddSprite(id)}
      />
      <TaskEditDrawer ref={taskEditDrawerRef} refetchSprite={(id: string) => deleteSprite(id)} refetch={refetch} />
      <TaskAddPhotosDrawer
        ref={taskAddPhotosDrawerRef}
        elementId={elementId}
        spriteId={spriteId}
        bimId={documentId}
        spriteImage={spriteImage.photos}
        refetch={refetch}
        refetchAddSprite={(id: string) => refetchAddSprite(id)}
      />
      <TaskEditPhotosDrawer
        ref={taskEditPhotosDrawerRef}
        refetchSprite={(id: string) => deleteSprite(id)}
        refetch={refetch}
      />
      <TaskAddDocsAndRfiDrawer
        ref={taskAddDocsAndRfiDrawerRef}
        elementId={elementId}
        spriteId={spriteId}
        bimId={documentId}
        spriteImage={spriteImage.document}
        refetch={refetch}
        refetchAddSprite={(id: string) => refetchAddSprite(id)}
      />
      <TaskEditDocsAndRfiDrawer
        ref={taskEditDocsAndRfiDrawerRef}
        refetchSprite={(id: string) => deleteSprite(id)}
        refetch={refetch}
      />
      <TaskEditDrawingsDrawer
        ref={taskEditDrawingsDrawerRef}
        refetchSprite={(id: string) => deleteSprite(id)}
        refetch={refetch}
      />
      <AssetsModal isVisible={view} assets={assets} documentId={documentId} onClose={() => setView(false)} />

      <Drawer
        ref={drawerRef}
        mask={false}
        height={10}
        title={
          <div className="flex items-center">
            <Text className="flex-1 text-[#1D6586]">{_.startCase(drawerType)} </Text>
            <Button
              onClick={() => drawerRef?.current?.pushDrawer()}
              type="text"
              icon={<Icon name="close" className="cursor-pointer" />}
            />
          </div>
        }
        closable={false}
      >
        {drawerType === 'screenshot' && (
          <Text className="" strong>
            Picture of your design
          </Text>
        )}
        {drawerType === 'comments' && (
          <Button
            style={
              isResolveClick
                ? {
                    boxShadow: 'inset 14px 14px 55px #c7c7c7, inset -14px -14px 55px #ffffff'
                  }
                : {}
            }
            onClick={() => {
              setFilter({ isCommentResolve: { is: !isResolveClick } });
              setIsResolveClick(!isResolveClick);
            }}
            shape="round"
          >
            Show {isResolveClick ? 'unresolve' : 'resolved'} ({photos?.length - getCommentData?.length})
          </Button>
        )}
        <Center className="flex-col gap-4 mt-2">
          <Spin spinning={loading} tip="Generating image">
            <img
              src={screenshot as any}
              alt="Model Screenshot"
              style={{ width: 'auto', height: 'auto', maxWidth: '100%', maxHeight: '100%' }}
              ref={imgRef}
            />
          </Spin>

          {drawerType === 'comments' && (
            <>
              <div className="flex gap-4 w-full">
                <Avatar src={userData?.getUserMe?.avatar} />

                <Form form={form} onFinish={onPost} className="w-full flex-1">
                  <Form.Item name="message">
                    <TextArea onPressEnter={() => form.submit()} placeholder="Enter a comment" disabled={loading} />
                  </Form.Item>

                  <div className="flex justify-between">
                    <Button type="primary" disabled={loading} onClick={() => form?.submit()}>
                      Send
                    </Button>
                    <Button onClick={() => form?.resetFields()}>Cancel</Button>
                  </div>
                </Form>
              </div>
              <div>
                {' '}
                {getCommentData?.map((photo: any, index: number) => {
                  return (
                    <div className="border-solid px-4 border-[#E8E8E8] rounded my-2" key={photo.id}>
                      <div className="flex justify-between items-center my-4">
                        <Text className="ml-auto">
                          {' '}
                          Resolve{photo.isCommentResolve && 'd'}{' '}
                          <Checkbox
                            checked={photo.isCommentResolve}
                            onClick={() => onResolve(photo.id, !photo?.isCommentResolve)}
                          />{' '}
                        </Text>
                      </div>
                      <Center className="flex-col">
                        <img
                          src={photo.fileUrl}
                          alt="Model Screenshot"
                          style={{ width: 'auto', height: 'auto', maxWidth: '100%', maxHeight: '100%' }}
                        />
                        <Center className="w-full m-0 rounded-b-lg rounded-t-none border-solid border-2 border-[#E8E8E8]">
                          Layout {photos.length - index}
                        </Center>
                        <BimComments documentId={Number(photo.id ?? 0)} type={Gql.ProjectDocumentCommentType.Bim} />
                      </Center>
                    </div>
                  );
                })}
              </div>{' '}
            </>
          )}

          {drawerBtn}
        </Center>
      </Drawer>
    </>
  );
};
async function loadScript(url: any) {
  // Create a new script element
  const script = document.createElement('script');
  script.type = 'text/javascript';
  script.src = url;

  // Set up a callback to run when the script has loaded
  // script.onload = function () {
  //   console.log(`Script loaded: ${url}`);
  //   if (callback) callback();
  // };

  // Handle script load error
  script.onerror = function () {
    console.error(`Error loading script: ${url}`);
  };

  // Append the script to the document
  document.head.appendChild(script);
}

export default AutoDeskBim;
