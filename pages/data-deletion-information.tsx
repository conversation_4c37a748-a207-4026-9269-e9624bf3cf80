import EmptyLayout from '@components/Layout/EmptyLayout';
import { Card } from 'antd';
import React from 'react';

const DataDeletionInformation: React.FC = () => {
  return (
    <EmptyLayout>
      <Card className="flex drop-shadow-lg rounded-lg prose lg:prose-xl" style={{ borderRadius: '16px' }}>
        <h1>Data Deletion Information</h1>
        <p>
          At our company, we take data privacy seriously. If you would like to request the deletion of your personal
          data, please follow the steps outlined below.
        </p>
        <ol>
          <li>Log in to your account on our website.</li>
          <li>Navigate to the &quot;Settings&quot; page.</li>
          <li>Click on the &quot;Delete My Account&quot; button.</li>
          <li>Follow the prompts to confirm your account deletion.</li>
        </ol>
        <p>
          Please note that once your account is deleted, we will not be able to recover any of the data associated with
          it. We strongly recommend that you export any data you wish to keep before proceeding with the deletion.
        </p>
        <p>
          If you have any questions or concerns about data deletion, please contact our customer support team at
          <EMAIL>
        </p>
      </Card>
    </EmptyLayout>
  );
};

export default DataDeletionInformation;
