import { ExclamationCircleOutlined } from '@ant-design/icons';
import { Icon } from '@commons';
import SearchInput from '@components/forms/FormsInput/SearchInput';
import useModal from '@components/hooks/useModal';
import useQueries from '@components/hooks/useQueries';
import ManageModal from '@components/ManageModal';
import FilesCard from '@components/photos/FilesCard';
import PhotoTabLayout from '@components/photos/PhotoTabLayout';
import PhotoUpload from '@components/PhotoUpload';
import * as Gql from '@graphql';
import { manageFiles } from '@utils/authority';
import { onError } from '@utils/error';
import { tz } from '@utils/timezone';
import { Button, Col, Dropdown, Form, Image, Input, MenuProps, message, Modal, Row, Space, Spin } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import FileSaver from 'file-saver';
import _, { isEmpty } from 'lodash';
import { useRouter } from 'next/router';
import { AppLayoutContext } from 'pages/_app';
import { useContext, useEffect, useRef, useState } from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';
import { ProjectDocumentApiService } from 'src/api';
import useDropzone from 'src/hooks/useDropzone';

const Recent = () => {
  const router = useRouter();
  const [form] = useForm();
  const [projectUserRole, setProjectUserRole] = useState<string>('');
  const [dataSourceFiles, setDataSourceFiles] = useState<any>([]);
  const [isDocOwner, setIsDocOwner] = useState<boolean>(false);
  const [previewDetails, setPreviewDetails] = useState<any>({ current: 0, visible: false });
  const uploadPhotoModalRef = useRef<any>(null);
  const inputRef = useRef<any>(null);

  const canEdit = projectUserRole === 'CanEdit';
  const dirId = router.query.dir && router.query.dir[0];
  const { userData: userMeData } = useContext(AppLayoutContext);

  const [loading, setLoading] = useState<any>(false);

  // For multiple selection
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  const { confirm } = Modal;

  const canDelete = projectUserRole === 'ProjectOwner' || projectUserRole === 'CloudCoordinator' || isDocOwner;
  const { canCreateRecent } = manageFiles(projectUserRole, dirId);

  const dropzoneRef = useRef<any>(null);
  useDropzone({ dropzoneRef, pushModal: uploadPhotoModalRef?.current?.openModal });

  // check if user is editor of the project, if yes, then allow to delete the photos that added by him
  useEffect(() => {
    if (selectedRowKeys.length > 0 && canEdit) {
      // find the selected photos and folder that added by the user
      const selectedPhotos = dataSourceFiles.filter((file: any) => selectedRowKeys.includes(file.id));
      // if the selected photos and folders are added by the user, then allow to delete
      if (selectedPhotos.length > 0 ) {
        const addedByUser = selectedPhotos.every((photo: any) => photo.addedBy === userMeData?.getUserMe.id)
        if (addedByUser) {
          setIsDocOwner(true);
        } else {
          setIsDocOwner(false);
        }
      }
    }
  }, [selectedRowKeys]);

  const onSelectChange = (id: string, status: boolean) => {
    if (status) {
      setSelectedRowKeys(prev => [...prev, id]);
    } else {
      setSelectedRowKeys(prev => prev.filter(key => key !== id));
    }
  };

  useEffect(() => {
    setProjectUserRole(localStorage.getItem('ProjectUserRole') || '');
  }, []);

  const { queries: queriesFiles, setFilter: setFilterFiles } = useQueries<
    Gql.ProjectDocumentFilter,
    Gql.ProjectDocumentSort
  >({ paging: { offset: 0, limit: 20 }, usePagingParam: false });

  const {
    data: photoFiles,
    refetch: refetchFiles,
    fetchMore: fetchMoreFiles,
    loading: loadingFiles
  } = Gql.useProjectDocumentsQuery({
    variables: {
      ...queriesFiles,
      filter: {
        ...queriesFiles.filter,
        ...{
          category: { eq: Gql.CategoryType.Photo },
          fileSystemType: { eq: Gql.FileSystemType.Document },
          isBimPhoto: { is: false }
        }
      },
      sorting: [
        {
          direction: Gql.SortDirection.Desc,
          field: Gql.ProjectDocumentSortFields.CreatedAt
        }
      ]
    },
    onError: onError
  });

  useEffect(() => {
    setDataSourceFiles(photoFiles?.projectDocuments?.nodes ?? []);
  }, [photoFiles]);

  const [createFolder, { loading: createFolderLoading }] = Gql.useCreateOneProjectDocumentMutation({
    onCompleted: () => {
      form.resetFields();
      message.success('Folder created successfully');
      closeAddFolderModal();
    },
    onError: onError as any
  });

  const onFinish = (values: Gql.CreateProjectDocumentInputDto) => {
    const { name } = values;
    const category = Gql.CategoryType.Photo;
    const fileSystemType = Gql.FileSystemType.Folder;
    const type = 'folder';
    const projectDocumentId = dirId as string;
    createFolder({
      variables: {
        input: {
          projectDocument: {
            name,
            category,
            fileSystemType,
            type,
            projectDocumentId
          }
        }
      }
    });
  };

  // SEARCH
  const onFilter = (data: any) => {
    setFilterFiles({
      name: { like: `%${data}%` }
    });
  };

  const [modal, showAddFolderModal, closeAddFolderModal] = useModal({
    onCancel: () => form.resetFields(),
    title: 'Create folder',
    content:
      (setTimeout(() => {
        inputRef?.current?.focus();
      }, 100),
      (
        <Form layout="vertical" form={form} onFinish={onFinish} requiredMark={false}>
          <Form.Item label="Folder name" name="name" rules={[{ required: true, message: 'Folder name is required!' }]}>
            <Input ref={inputRef} />
          </Form.Item>
          <div className="flex justify-end">
            <Button htmlType="submit" type="primary">
              Create
            </Button>
          </div>
        </Form>
      ))
  });

  const debouncedOnFilter = _.debounce((data: any) => {
    onFilter(data.keyword);
  }, 500);

  const handleNewMenuClick: MenuProps['onClick'] = e => {
    switch (e.key) {
      case '1': {
        showAddFolderModal();
        break;
      }
      case '2': {
        uploadPhotoModalRef?.current?.openModal();
        break;
      }
    }
  };

  const newMenuProps = {
    items: [
      {
        icon: <Icon name="file-upload" />,
        label: <>Upload Files</>,
        key: '2'
      }
    ],
    onClick: handleNewMenuClick
  };

  const fetchMoreFileData = async () => {
    await fetchMoreFiles({
      variables: {
        paging: {
          offset: photoFiles?.projectDocuments.nodes.length,
          limit: 20
        }
      },

      updateQuery: (prev, { fetchMoreResult }) => {
        if (!fetchMoreResult) return prev;
        return {
          projectDocuments: {
            ...fetchMoreResult.projectDocuments,
            nodes: [...(prev.projectDocuments?.nodes ?? []), ...(fetchMoreResult.projectDocuments?.nodes ?? [])]
          }
        };
      }
    });
  };

  const [deleteDocuments, { loading: documentsDeleting }] = Gql.useDeleteProjectDocumentsMutation({
    onCompleted: () => {
      message.success('Deleted successfully');
      setTimeout(() => {
        refetchFiles();
      }, 500);
      setSelectedRowKeys([]);
    },
    onError: onError as any
  });

  const showDeletesConfirm = () => {
    // select from dataSource and photoDataSources
    const deletedDocumentNames = [
      ...dataSourceFiles.filter((item: any) => selectedRowKeys.includes(item.id.toString()))
    ].map(item => item.name);

    confirm({
      title: 'Are you sure?',
      icon: <ExclamationCircleOutlined />,
      content: (
        <>
          <div>
            Do you really want to delete the{' '}
            {selectedRowKeys.length < 2 && (
              <span className="font-semibold">
                {deletedDocumentNames[0]} <span className="font-normal">?</span>
              </span>
            )}
          </div>
          {selectedRowKeys.length > 1 && (
            <ol className="list-decimal mt-2">
              {deletedDocumentNames.map((name: string, index: number) => (
                <li className="my-2 font-semibold" key={index}>
                  {name}
                </li>
              ))}
            </ol>
          )}
        </>
      ),
      okText: 'Yes',
      okType: 'danger',
      cancelText: 'No',
      onOk() {
        deleteDocuments({
          variables: {
            input: {
              ids: selectedRowKeys.map(id => parseInt(id as string))
            }
          }
        });
        setSelectedRowKeys([]);
      },
      onCancel() {}
    });
  };

  const onBulkDownload = async () => {
    // check if the selected files include video, if yes, then show the warning message
    const selectedFiles = dataSourceFiles.filter((file: any) => selectedRowKeys.includes(file.id));

    const selectedVideos = selectedFiles.filter((file: any) => file.type === 'mp4');
    if (selectedVideos.length > 0) {
      return message.error('Only photos can be downloaded.');
    }

    setLoading(true);
    const nodeBuffer = await ProjectDocumentApiService.downloadBulkZip(
      {
        body: {
          ids: selectedRowKeys as string[]
        }
      },
      {
        responseType: 'arraybuffer'
      }
    ).catch(e => {
      message.error(e?.message ?? 'Something went wrong');
    });

    const file = new Blob([nodeBuffer], { type: 'application/zip' });
    const fileName = 'bulkdownload.zip';
    setSelectedRowKeys([]);
    setLoading(false);
    FileSaver(file, fileName);
  };

  const images = dataSourceFiles.filter((file: any) => !(file.type === 'folder'));

  return (
    <>
      <ManageModal
        data={dataSourceFiles}
        selectedRowKeys={selectedRowKeys}
        onBulkDownload={onBulkDownload}
        showDeletesConfirm={showDeletesConfirm}
        canDelete={canDelete}
        setSelectedRowKeys={setSelectedRowKeys}
      />

    <div className="absolute w-full h-full py-5 px-5 overflow-x-hidden" ref={dropzoneRef}>
      {modal}
      <Spin tip={'Loading...'} spinning={createFolderLoading || loading || documentsDeleting}>
        <div className="flex items-center justify-between mb-3">
          <div>
            <h3>Recent</h3>
          </div>
          <Row>
            <Col>
              <Form
                form={form}
                onValuesChange={(data: any) => {
                  debouncedOnFilter(data);
                }}
              >
                <Space>
                  <div style={{ paddingTop: '7px' }}>
                    <SearchInput onPressEnter={() => { }} placeholder={'Search by name'} />
                  </div>
                  {canCreateRecent && (
                    <Dropdown menu={newMenuProps}>
                      <Button
                        type="primary"
                        icon={<Icon name="plus-white" className="pt-1 mr-1" />}
                        className=" rounded-lg border-gray40 h-[41px] mb-[3px]"
                      >
                        New
                      </Button>
                    </Dropdown>
                  )}
                </Space>
              </Form>
            </Col>
          </Row>
        </div>

          <div className="mb-12" style={{}}>
            <div className="mb-7 bg-white rounded-md" id="scrollableDiv">
              <InfiniteScroll
                dataLength={dataSourceFiles.length}
                next={fetchMoreFileData}
                hasMore={photoFiles?.projectDocuments.pageInfo.hasNextPage || false}
                loader={null}
                height={isEmpty(dataSourceFiles) && !loadingFiles ? 200 : 650}
                style={{ overflowX: 'hidden' }}
              >
                {isEmpty(dataSourceFiles) && !loadingFiles ? (
                  <div className="flex h-full">
                    <div className="m-auto">
                      <div className="flex flex-col items-center">
                        <Icon name="image-placeholder" className="gray-70" />
                        <div className="mt-3 text-gray70">Start uploading your media files to fill in this space.</div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="grid grid-cols-5 lg:grid-cols-4 gap-1">
                    {images.map((file: any, index: number) => {
                      return (
                        <div className="my-4 mx-2 flex-1" key={file.id}>
                          <FilesCard
                            index={index}
                            onClick={() => setPreviewDetails({ current: index, visible: true, file })}
                            refetch={refetchFiles}
                            fileUrl={file.fileUrl}
                            name={file.name}
                            dataSource={dataSourceFiles}
                            fileId={file.id}
                            createdAt={tz(file.createdAt) as any}
                            onSelect={onSelectChange}
                            selectedRowKeys={selectedRowKeys}
                            file={file}
                          />
                        </div>
                      );
                    })}
                  </div>
                )}
              </InfiniteScroll>
            </div>
          </div>
        </Spin>

        <PhotoUpload
          dirId={dirId as any}
          onSaved={() => refetchFiles()}
          type={Gql.CategoryType.Photo}
          ref={uploadPhotoModalRef}
          title="Upload Photos or Videos"
        />
      </div>

      <Image.PreviewGroup
        preview={{
          rootClassName: 'photos-view',
          visible: previewDetails.visible,
          src: `${previewDetails?.file?.fileUrl ?? ''}`,
          getContainer() {
            const leftIcon = document.querySelector('.ant-image-preview-switch-left') as HTMLElement;
            const rightIcon = document.querySelector('.ant-image-preview-switch-right') as HTMLElement;

            if (leftIcon && rightIcon) {
              leftIcon.style.background = 'url(/assets/arrow-left.png) no-repeat center';
              rightIcon.style.background = 'url(/assets/arrow-right.png) no-repeat center';

              leftIcon.style.color = 'transparent';
              rightIcon.style.color = 'transparent';

              leftIcon.style.insetInlineStart = '30px';
              rightIcon.style.insetInlineEnd = '30px';
            }

            return document.getElementById('scrollableDiv') as HTMLElement;
          },
          onVisibleChange: value => {
            setPreviewDetails((prev: any) => ({ ...prev, visible: value }));
          },
          current: previewDetails.current
        }}
      >
        {images.map((file: any, index: number) => (
          <Image src={file.fileUrl} key={index} alt="" style={{ display: 'none' }} />
        ))}
      </Image.PreviewGroup>
    </>
  );
};

Recent.auth = true;
Recent.Layout = PhotoTabLayout;
export default Recent;
