import { ExclamationCircleOutlined, HomeOutlined } from '@ant-design/icons';
import { Icon } from '@commons';
import ManageModal from '@components/ManageModal';
import MoveBulkCloudDocsModal from '@components/MoveBulkCloudDocsModal';
import PhotoUpload from '@components/PhotoUpload';
import SyncModal from '@components/cloud-docs/SyncModal';
import SearchInput from '@components/forms/FormsInput/SearchInput';
import useModal from '@components/hooks/useModal';
import useQueries from '@components/hooks/useQueries';
import FilesCard from '@components/photos/FilesCard';
import FoldersCard from '@components/photos/FoldersCard';
import PhotoTabLayout from '@components/photos/PhotoTabLayout';
import * as Gql from '@graphql';
import { manageFiles } from '@utils/authority';
import { onError } from '@utils/error';
import { tz } from '@utils/timezone';
import {
  <PERSON>readcrumb,
  Button,
  Col,
  Dropdown,
  Form,
  Image,
  Input,
  MenuProps,
  Modal,
  Row,
  Space,
  Spin,
  message
} from 'antd';
import { useForm } from 'antd/lib/form/Form';
import FileSaver from 'file-saver';
import _, { isEmpty } from 'lodash';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { AppLayoutContext } from 'pages/_app';
import { useContext, useEffect, useRef, useState } from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';
import { ProjectDocumentApiService } from 'src/api';
import useDropzone from 'src/hooks/useDropzone';

const Photos = () => {
  const router = useRouter();
  const [form] = useForm();
  const [projectUserRole, setProjectUserRole] = useState<string>('');
  const moveDocumentsModal = useRef<any>(null);
  const [dataSource, setDataSource] = useState<any>([]);
  const [dataSourceFiles, setDataSourceFiles] = useState<any>([]);
  const [isDocOwner, setIsDocOwner] = useState<boolean>(false);
  const [showPhotos, setShowPhotos] = useState<boolean>(true);
  const [showFolders, setShowFolders] = useState<boolean>(true);
  const [previewDetails, setPreviewDetails] = useState<any>({ current: 0, visible: false });

  const [breadcrumbs, setBreadcrumbs] = useState<
    Gql.GetProjectDocumentsBreadcrumbQuery['getProjectDocumentsBreadcrumb']
  >([]);
  const uploadPhotoModalRef = useRef<any>(null);
  const inputRef = useRef<any>(null);
  const viewOnly = projectUserRole === 'CanView';
  const canEdit = projectUserRole === 'CanEdit';
  const dirId = router.query.dir && (router.query.dir as string);
  const { userData: userMeData } = useContext(AppLayoutContext);
  const [loading, setLoading] = useState<any>(false);

  // For multiple selection
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [isManage, setIsManage] = useState(false);
  const { confirm } = Modal;
  const canDelete = projectUserRole === 'ProjectOwner' || projectUserRole === 'CloudCoordinator' || isDocOwner;
  const { canCreate, canMove } = manageFiles(projectUserRole, dirId);
  const dropzoneRef = useRef<any>(null);
  useDropzone({ dropzoneRef, pushModal: uploadPhotoModalRef?.current?.openModal });

  // check if user is editor of the project, if yes, then allow to delete the photos that added by him
  useEffect(() => {
    if (selectedRowKeys.length > 0 && canEdit) {
      // find the selected photos and folder that added by the user
      const selectedPhotos = dataSourceFiles.filter((file: any) => selectedRowKeys.includes(file.id));
      const selectedFolders = dataSource.filter((folder: any) => selectedRowKeys.includes(folder.id));
      // if the selected photos and folders are added by the user, then allow to delete
      if (selectedPhotos.length > 0 || selectedFolders.length > 0) {
        const addedByUser =
          selectedPhotos.every((photo: any) => photo.addedBy === userMeData?.getUserMe.id) &&
          selectedFolders.every((folder: any) => folder.addedBy === userMeData?.getUserMe.id);
        if (addedByUser) {
          setIsDocOwner(true);
        } else {
          setIsDocOwner(false);
        }
      }
    }
  }, [selectedRowKeys]);

  const onSelectChange = (id: string, status: boolean) => {
    if (status) {
      setSelectedRowKeys(prev => [...prev, id]);
    } else {
      setSelectedRowKeys(prev => prev.filter(key => key !== id));
    }
  };

  const [getBreadcrumbs, { data: breadcrumbData }] = Gql.useGetProjectDocumentsBreadcrumbLazyQuery();

  useEffect(() => {
    refetch();
    refetchFiles();

    if (!dirId) {
      setBreadcrumbs([]);
      return;
    }

    if (dirId) {
      getBreadcrumbs({
        variables: { input: { id: parseInt(dirId as string) } }
      }).then(res => {
        setBreadcrumbs(res.data?.getProjectDocumentsBreadcrumb ?? []);
      });
    }
  }, [dirId, breadcrumbData]);

  useEffect(() => {
    setProjectUserRole(localStorage.getItem('ProjectUserRole') || '');
  }, []);

  const { queries, setFilter } = useQueries<Gql.ProjectDocumentFilter, Gql.ProjectDocumentSort>({
    paging: { offset: 0, limit: 20 },
    usePagingParam: false
  });

  const search = queries?.filter?.name?.like;

  const {
    data,
    refetch,
    fetchMore,
    loading: loadingData
  } = Gql.useGetProjectDocumentsQuery({
    variables: {
      ...queries,
      filter: {
        ...queries.filter,
        ...{
          ...(!search || search?.length === 0
            ? {
                projectDocumentId: {
                  ...(dirId ? { eq: dirId?.toString() } : { eq: null })
                }
              }
            : {}),
          category: { eq: Gql.CategoryType.Photo },
          fileSystemType: { eq: Gql.FileSystemType.Folder }
        }
      },
      sorting: [
        {
          direction: Gql.SortDirection.Asc,
          field: Gql.ProjectDocumentSortFields.Name
        }
      ]
    },
    onError: onError
  });

  const { queries: queriesFiles, setFilter: setFilterFiles } = useQueries<
    Gql.ProjectDocumentFilter,
    Gql.ProjectDocumentSort
  >({ paging: { offset: 0, limit: 20 }, usePagingParam: false });

  const {
    data: photoFiles,
    refetch: refetchFiles,
    fetchMore: fetchMoreFiles,
    loading: loadingFiles
  } = Gql.useGetProjectDocumentsQuery({
    variables: {
      ...queriesFiles,
      filter: {
        ...queries.filter,
        ...{
          ...(!search || search?.length === 0
            ? {
                projectDocumentId: {
                  ...(dirId ? { eq: dirId?.toString() } : { eq: null })
                }
              }
            : {}),
          category: { eq: Gql.CategoryType.Photo },
          fileSystemType: { eq: Gql.FileSystemType.Document }
        }
      }
    },
    onError: onError
  });

  useEffect(() => {
    setDataSource(data?.getProjectDocuments?.nodes ?? []);
  }, [data]);

  useEffect(() => {
    setDataSourceFiles(photoFiles?.getProjectDocuments?.nodes ?? []);
  }, [photoFiles]);

  const [createFolder, { loading: createFolderLoading }] = Gql.useCreateOneProjectDocumentMutation({
    onCompleted: () => {
      form.resetFields();
      message.success('Folder created successfully');
      refetch();
      closeAddFolderModal();
    },
    onError: onError as any
  });

  const images = dataSourceFiles.filter((file: any) => !(file.type === 'folder'));

  const onFinish = (values: Gql.CreateProjectDocumentInputDto) => {
    const { name } = values;
    const category = Gql.CategoryType.Photo;
    const fileSystemType = Gql.FileSystemType.Folder;
    const type = 'folder';
    const projectDocumentId = dirId as string;
    createFolder({
      variables: {
        input: {
          projectDocument: {
            name,
            category,
            fileSystemType,
            type,
            projectDocumentId
          }
        }
      }
    });
  };

  // SEARCH
  const onFilter = (data: any) => {
    // search files and folders
    setFilter({
      name: { like: data }
    });
    setFilterFiles({
      name: { like: data }
    });
  };

  const [modal, showAddFolderModal, closeAddFolderModal] = useModal({
    onCancel: () => form.resetFields(),
    title: 'Create folder',
    content:
      (setTimeout(() => {
        inputRef?.current?.focus();
      }, 100),
      (
        <Form layout="vertical" form={form} onFinish={onFinish} requiredMark={false}>
          <Form.Item label="Folder name" name="name" rules={[{ required: true, message: 'Folder name is required!' }]}>
            <Input ref={inputRef} />
          </Form.Item>
          <div className="flex justify-end">
            <Button htmlType="submit" type="primary">
              Create
            </Button>
          </div>
        </Form>
      ))
  });

  const debouncedOnFilter = _.debounce((data: any) => {
    onFilter(data.keyword);
  }, 500);

  // sync modal
  const [syncmodal, showSyncModal, closeSyncModal] = useModal({
    closable: false,

    content: (
      <>
        <SyncModal
          mimeType="images"
          category={Gql.CategoryType.Photo}
          dirId={dirId as string}
          onComplete={() => {
            refetch();
            closeSyncModal();
          }}
        />
      </>
    )
  });

  const handleNewMenuClick: MenuProps['onClick'] = e => {
    switch (e.key) {
      case '1': {
        showAddFolderModal();
        break;
      }
      case '2': {
        uploadPhotoModalRef?.current?.openModal();
        break;
      }
      case '3': {
        showSyncModal();
        break;
      }
    }
  };

  const newMenuProps = {
    items: [
      {
        icon: <Icon name="new-folder" />,
        label: <> Add Folder</>,
        key: '1'
      },
      dirId
        ? {
            icon: <Icon name="file-upload" />,
            label: <>Upload Photos</>,
            key: '2'
          }
        : null
    ],
    onClick: handleNewMenuClick
  };

  //* infinite scroll *//
  const fetchMoreData = async () => {
    fetchMore({
      variables: {
        paging: {
          offset: data?.getProjectDocuments?.nodes?.length ?? 0,
          limit: 20
        }
      },
      updateQuery: (prev, { fetchMoreResult }) => {
        if (!fetchMoreResult) return prev;
        return {
          getProjectDocuments: {
            ...fetchMoreResult.getProjectDocuments,
            nodes: [...(prev.getProjectDocuments?.nodes ?? []), ...(fetchMoreResult.getProjectDocuments?.nodes ?? [])]
          }
        };
      }
    });
  };

  const fetchMoreFileData = async () => {
    fetchMoreFiles({
      variables: {
        paging: {
          offset: photoFiles?.getProjectDocuments.nodes.length,
          limit: 20
        }
      },
      updateQuery: (prev, { fetchMoreResult }) => {
        if (!fetchMoreResult) return prev;
        return {
          getProjectDocuments: {
            ...fetchMoreResult.getProjectDocuments,
            nodes: [...(prev.getProjectDocuments?.nodes ?? []), ...(fetchMoreResult.getProjectDocuments?.nodes ?? [])]
          }
        };
      }
    });
  };

  const [deleteDocuments, { loading: documentsDeleting }] = Gql.useDeleteProjectDocumentsMutation({
    onCompleted: () => {
      message.success('Deleted successfully');
      setTimeout(() => {
        refetch();
        refetchFiles();
      }, 500);
      setSelectedRowKeys([]);
    },
    onError: onError as any
  });

  const showDeletesConfirm = () => {
    // select from dataSource and photoDataSources
    const deletedDocumentNames = [
      ...dataSource.filter((item: any) => selectedRowKeys.includes(item.id.toString())),
      ...dataSourceFiles.filter((item: any) => selectedRowKeys.includes(item.id.toString()))
    ].map(item => item.name);

    confirm({
      title: 'Are you sure?',
      icon: <ExclamationCircleOutlined />,
      content: (
        <>
          <div>
            Do you really want to delete the{' '}
            {selectedRowKeys.length < 2 && (
              <span className="font-semibold">
                {deletedDocumentNames[0]} <span className="font-normal">?</span>
              </span>
            )}
          </div>
          {selectedRowKeys.length > 1 && (
            <ol className="list-decimal mt-2">
              {deletedDocumentNames.map((name: string, index: number) => (
                <li className="my-2 font-semibold" key={index}>
                  {name}
                </li>
              ))}
            </ol>
          )}
        </>
      ),
      okText: 'Yes',
      okType: 'danger',
      cancelText: 'No',
      onOk() {
        deleteDocuments({
          variables: {
            input: {
              ids: selectedRowKeys.map(id => parseInt(id as string))
            }
          }
        });
        setSelectedRowKeys([]);
        setIsManage(false);
      },
      onCancel() {}
    });
  };

  const onBulkDownload = async () => {
    // check if the selected files include video, if yes, then show the warning message
    const selectedFiles = dataSourceFiles.filter((file: any) => selectedRowKeys.includes(file.id));

    const selectedVideos = selectedFiles.filter((file: any) => file.type === 'mp4');
    if (selectedVideos.length > 0) {
      return message.error('Only photos can be downloaded.');
    }

    setLoading(true);
    try {
      const nodeBuffer = await ProjectDocumentApiService.downloadBulkZip(
        {
          body: {
            ids: selectedRowKeys as string[]
          }
        },
        {
          responseType: 'arraybuffer'
        }
      ).catch(e => {
        message.error(e?.message ?? 'Something went wrong');
      });

      const file = new Blob([nodeBuffer], { type: 'application/zip' });
      const fileName = 'bulkdownload.zip';
      setSelectedRowKeys([]);
      setIsManage(false);
      setLoading(false);
      FileSaver(file, fileName);
    } catch (e) {
      onError(e);
    }
  };

  const selectAll = () => {
    const selectedPhotos = dataSourceFiles.map((file: any) => file.id);
    const selectedFolders = dataSource.map((folder: any) => folder.id);
    setSelectedRowKeys([...selectedPhotos, ...selectedFolders]);
  };

  const onMove = () => {
    // if query.filter has a value, then clear it
    if (queries?.filter?.name) {
      setFilter({ name: { like: '' } });
    }
    moveDocumentsModal?.current?.openModal();
  };

  return (
    <>
      <div className="absolute w-full h-full py-5 px-5 overflow-x-hidden" ref={dropzoneRef}>
        {modal}
        {syncmodal}
        <Spin tip={'Loading...'} spinning={createFolderLoading || loadingData || loading || documentsDeleting}>
          <div className="flex items-center justify-between mb-3">
            <div>
              <h3>Photos</h3>
              {dirId && (
                <Breadcrumb>
                  <Breadcrumb.Item className="text-gray90 text-md">
                    <Link href="/photos/folder">
                      <HomeOutlined />
                    </Link>
                  </Breadcrumb.Item>
                  {breadcrumbs.map(value => (
                    <Breadcrumb.Item className="text-gray90 text-md" key={value.id}>
                      <Link href={`/photos/folder?dir=${value.id}`}>{value.name}</Link>
                    </Breadcrumb.Item>
                  ))}
                </Breadcrumb>
              )}
            </div>
            <Row>
              <Col>
                <Form
                  form={form}
                  onValuesChange={(data: any) => {
                    // console.log(data);
                    debouncedOnFilter(data);
                  }}
                >
                  <Space>
                    <div style={{ paddingTop: '7px' }}>
                      <SearchInput onPressEnter={() => {}} placeholder={'Search by name'} />
                    </div>
                    {canCreate && (
                      <Dropdown menu={newMenuProps}>
                        <Button
                          type="primary"
                          icon={<Icon name="plus-white" className="pt-1 mr-1" />}
                          disabled={viewOnly}
                          className="h-[40px] rounded-lg border-gray40"
                        >
                          <Space>New</Space>
                        </Button>
                      </Dropdown>
                    )}
                  </Space>
                </Form>
              </Col>
            </Row>
          </div>

          <div className="mb-12" style={{}}>
            <div className="mb-1 font-semibold bg-white w-fit p-1 rounded flex pl-2">
              Folders
              <div className="relative mx-2 cursor-pointer" onClick={() => setShowFolders(!showFolders)}>
                {showFolders ? <Icon name="chevron-down" /> : <Icon name="chevron-up" />}
              </div>
            </div>
            <div
              className={`mb-7 overflow-x-hidden bg-white rounded-md ${!showFolders && 'hidden'}`}
              id="scrollableDiv"
            >
              <InfiniteScroll
                dataLength={dataSource.length}
                next={fetchMoreData}
                hasMore={data?.getProjectDocuments.pageInfo.hasNextPage || false}
                loader={null}
                height={isEmpty(dataSource) && !loadingData ? 200 : 230}
                style={{ overflowX: 'hidden' }}
              >
                {isEmpty(dataSource) && !loadingData ? (
                  <div className="flex h-full">
                    <div className="m-auto">
                      <div className="flex flex-col items-center">
                        <Icon name="folders" />
                        <div className="mt-3 text-gray70">
                          {dirId
                            ? 'No subfolders found. Click “+ New” to add one.'
                            : 'There are no folders yet. Click “+ New” to get started.'}
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="grid grid-cols-5 lg:grid-cols-4 gap-1">
                    {dataSource
                      .filter((d: any) => d.type === 'folder')
                      .map((folder: any) => {
                        return (
                          <div className="m-2" key={folder.id}>
                            <FoldersCard
                              refetch={refetch}
                              folderId={folder.id}
                              folderName={folder.name}
                              dataSource={dataSource}
                              onClick={() => {
                                setFilter({ name: { like: '' } });
                                router.push(`/photos/folder?dir=${folder.id}`);
                              }}
                              onSelect={onSelectChange}
                              isManage={isManage}
                              setIsManage={setIsManage}
                              selectedRowKeys={selectedRowKeys}
                            />
                          </div>
                        );
                      })}
                  </div>
                )}
              </InfiniteScroll>
            </div>
            <div className="mb-1 font-semibold bg-white w-fit p-1 rounded flex pl-2">
              {!dirId && 'Ungrouped' } Files
              <div className="relative mx-2 cursor-pointer" onClick={() => setShowPhotos(!showPhotos)}>
                {showPhotos ? <Icon name="chevron-down" /> : <Icon name="chevron-up" />}
              </div>
            </div>
            <div
              className={`mb-7 bg-white rounded-md transition-transform translate-y-8 ${!showPhotos && 'hidden'}`}
              id="scrollableDiv"
            >
              <InfiniteScroll
                dataLength={dataSourceFiles.length}
                next={fetchMoreFileData}
                hasMore={photoFiles?.getProjectDocuments.pageInfo.hasNextPage || false}
                loader={null}
                height={isEmpty(dataSourceFiles) && !loadingData ? 200 : 650}
                style={{ overflowX: 'hidden' }}
              >
                {isEmpty(dataSourceFiles) && !loadingFiles ? (
                  <div className="flex h-full">
                    <div className="m-auto">
                      <div className="flex flex-col items-center">
                        <Icon name="image-placeholder" className="gray-70" />
                        <div className="mt-3 text-gray70">Start uploading your media files to fill in this space.</div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="grid grid-cols-5 lg:grid-cols-4 gap-1">
                    {dataSourceFiles
                      .filter((file: any) => !(file.type === 'folder'))
                      ?.sort?.((a: any, b: any) => {
                        const dateA: any = new Date(a.createdAt);
                        const dateB: any = new Date(b.createdAt);
                        return dateB - dateA;
                      })
                      ?.map?.((file: any, index: number) => {
                        return (
                          <div className="my-4 mx-2 flex-1" key={file.id}>
                            <FilesCard
                              index={index}
                              key={file.id}
                              onClick={() => setPreviewDetails({ current: index, visible: true, file })}
                              refetch={refetchFiles}
                              fileUrl={file.fileUrl}
                              name={file.name}
                              dataSource={dataSource}
                              fileId={file.id}
                              createdAt={tz(file.createdAt) as any}
                              onSelect={onSelectChange}
                              selectedRowKeys={selectedRowKeys}
                              file={file}
                            />
                          </div>
                        );
                      })}
                  </div>
                )}
              </InfiniteScroll>
            </div>
          </div>
        </Spin>

        <PhotoUpload
          dirId={dirId as any}
          onSaved={() => refetchFiles()}
          type={Gql.CategoryType.Photo}
          ref={uploadPhotoModalRef}
        />
        <MoveBulkCloudDocsModal
          data={dataSource}
          ids={selectedRowKeys}
          dirId={dirId as any}
          ref={moveDocumentsModal}
          onSaved={() => {
            refetch();
            refetchFiles();
            setSelectedRowKeys([]);
            setIsManage(false);
          }}
          category={Gql.CategoryType.Photo}
          setSelectedRowKeys={setSelectedRowKeys}
          onLoadMore={fetchMoreData}
        />
      </div>

      <Image.PreviewGroup
        preview={{
          visible: previewDetails.visible,
          src: `${previewDetails?.file?.fileUrl ?? ''}`,
          onVisibleChange: value => {
            setPreviewDetails((prev: any) => ({ ...prev, visible: value }));
          },
          getContainer() {
            const leftIcon = document.querySelector('.ant-image-preview-switch-left') as HTMLElement;
            const rightIcon = document.querySelector('.ant-image-preview-switch-right') as HTMLElement;

            if (leftIcon && rightIcon) {
              leftIcon.style.background = 'white';
              rightIcon.style.background = 'white';

              leftIcon.style.color = 'black';
              rightIcon.style.color = 'black';

              leftIcon.style.insetInlineStart = '300px';
              rightIcon.style.insetInlineEnd = '300px';
            }

            return document.getElementById('scrollableDiv') as HTMLElement;
          },
          current: previewDetails.current
        }}
      >
        {images.map((file: any, index: number) => {
          return <Image src={file.fileUrl} key={index} style={{ display: 'none' }} />;
        })}
      </Image.PreviewGroup>

      <ManageModal
        canMove={canMove as boolean}
        onMove={onMove}
        data={dataSource}
        selectedRowKeys={selectedRowKeys}
        onBulkDownload={onBulkDownload}
        showDeletesConfirm={showDeletesConfirm}
        canDelete={canDelete}
        setSelectedRowKeys={setSelectedRowKeys}
        setIsManage={setIsManage}
        selectAll={selectAll}
      />
    </>
  );
};

Photos.auth = true;
Photos.Layout = PhotoTabLayout;
export default Photos;
