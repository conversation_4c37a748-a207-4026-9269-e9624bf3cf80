import { Icon } from '@commons';
import { Button, Form, Input, message } from 'antd';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';
import { isMobile } from 'react-device-detect';
import { UserAuthService } from 'src/api';
import ForgotPasswordLayout from '../src/components/Layout/ForgotPasswordLayout';

type FormData = { newPassword: string };
const ResetPassword: React.FC = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const resetToken = router.query.token as string;

  useEffect(() => {
    if (router.isReady && router.query.token) checkIsMobile();
  }, [router.isReady]);

  const checkIsMobile = () => {
    if (isMobile) {
      window.location.href = `bina://reset-password/?token=${resetToken}`;
      return;
    }
  };

  const onFinish = async (formData: FormData) => {
    try {
      setLoading(true);
      const newPassword = formData.newPassword;
      UserAuthService.resetPassword({ body: { newPassword, resetToken } })
        .then(() => {
          setLoading(false);
          router.push('/reset-password-successful');
        })
        .catch(err => {
          setLoading(false);
          message.error(err?.response?.data?.message || 'Something went wrong');
        });
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <ForgotPasswordLayout>
      <div className="flex justify-center pt-40">
        <div className="pt-20">
          <h1 className="text-2xl font-bold mb-3">Reset your password</h1>
          <Form layout="vertical" onFinish={onFinish} requiredMark={false}>
            <Form.Item
              className="text-gray90"
              label="Enter your new password"
              name="newPassword"
              rules={[
                {
                  required: true,
                  message: 'Password is required'
                },
                {
                  min: 8,
                  message: 'Minimum 8 characters long'
                },
                {
                  pattern: /^(?=.*[a-z])/,
                  message: 'One lowercase letter [a-z]'
                },
                {
                  pattern: /^(?=.*[A-Z])/,
                  message: 'One uppercase letter [A-Z]'
                },
                {
                  pattern: /^(?=.*[0-9])/,
                  message: 'One number [0-9]'
                },
                {
                  pattern: /^[a-zA-Z0-9@$!%*?&]+$/,
                  message: 'Only letters, digits, and @$!%*?& are allowed'
                }
              ]}
            >
              <Input.Password
                className="rounded-lg h-10 w-72"
                iconRender={visible => (
                  <Button type="link" className="text-gray90">
                    <Icon name={visible ? 'eye-visible' : 'eye-invisible'} />
                  </Button>
                )}
              />
            </Form.Item>

            <Form.Item
              className="text-gray90"
              label="Confirm your new password"
              name="confirmPassword"
              dependencies={['newPassword']}
              rules={[
                { required: true, message: 'Repeat New Password is required' },
                ({ getFieldValue }: any) => ({
                  validator(_: any, value: any) {
                    if (!value || getFieldValue('newPassword') === value) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error('The two passwords that you entered do not match!'));
                  }
                })
              ]}
            >
              <Input.Password
                className="rounded-lg h-10 w-72"
                iconRender={visible => (
                  <Button type="link" className="text-gray90">
                    <Icon name={visible ? 'eye-visible' : 'eye-invisible'} />
                  </Button>
                )}
              />
            </Form.Item>

            <Button className="rounded-lg h-10 w-72 font-medium" htmlType="submit" type="primary" loading={loading}>
              Confirm
            </Button>
          </Form>
        </div>
      </div>
    </ForgotPasswordLayout>
  );
};

export default ResetPassword;
