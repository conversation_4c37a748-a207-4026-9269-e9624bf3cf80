import React, { useEffect } from 'react';
import { IntegrationApiService } from 'src/api';

const Test: React.FC = () => {
  const options = {
    env: 'AutodeskProduction',
    api: 'derivativeV2',
    getAccessToken: function (onTokenReady: any) {
      IntegrationApiService.autodeskAuth().then((accessToken: string) => {
        var token = accessToken;

        var timeInSeconds = 3599; // Use value provided by Forge Authentication (OAuth) API
        onTokenReady(token, timeInSeconds);
      });
    }
  };

  useEffect(() => {
    //@ts-ignore
    const urns = [
      {
        urn: 'dXJuOmFkc2sub2JqZWN0czpvcy5vYmplY3Q6YmluYS10ZXN0L2VxTGVSZEFhMEpsdlpXRC1hLmR3Zw==',
        xform: { x: 0, y: 0, z: 0 }
      },
      {
        urn: 'dXJuOmFkc2sub2JqZWN0czpvcy5vYmplY3Q6YmluYS10ZXN0L3NuTTY0TGtvcVNpSUI4QS1iLmR3Zw==',
        xform: { x: 0, y: 0, z: 0 }
      }
    ];

    //@ts-ignore
    Autodesk.Viewing.Initializer(options, function () {
      const htmlDiv = document.getElementById('forgeViewer');
      //@ts-ignore
      const viewer = new Autodesk.Viewing.GuiViewer3D(htmlDiv as any);
      viewer.start();

      //@ts-ignore
      urns.map((m: any) => {
        //@ts-ignore
        Autodesk.Viewing.Document.load(`urn:${m.urn}`, (doc: any) => {
          var nodes = doc.getRoot().search({ type: 'geometry' });

          viewer.loadDocumentNode(doc, nodes[1], {
            preserveView: true,
            keepCurrentModels: true,
            //@ts-ignore
            placementTransform: new THREE.Matrix4().setPosition(m.xform),
            //@ts-ignore
            keepCurrentModels: true,
            globalOffset: { x: 0, y: 0, z: 0 }
          });
        });
      });

      function onDocumentLoadSuccess(viewerDocument: any) {
        // Choose the default viewable - most likely a 3D model, rather than a 2D sheet.
        // console.log(viewerDocument.getGeometryList());
        // var defaultModel = viewerDocument
        //   .getRoot()
        //   .search({ type: "geometry" });
        // viewer.loadDocumentNode(viewerDocument, defaultModel);
      }

      function onDocumentLoadFailure(...a: any) {
        console.log(a);

        console.log('oh no :(');
        console.error('Failed fetching Forge manifest');
      }
    });
  }, []);

  return (
    <>
      <div id="forgeViewer" style={{ height: '70vh' }}></div>
    </>
  );
};

export default Test;
