import * as Gql from '@graphql';
import { onError } from '@utils/error';
import { Button, Card, Form, message, Spin } from 'antd';
import { signIn } from 'next-auth/react';
import Image from 'next/image';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';
import { ProjectInvitationApiService, UserAuthService } from 'src/api';

const Acceptation = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  const email = router.query.email as string;

  const [updateUser, { loading: updating, error: updateError }] = Gql.useUpdateUserByEmailMutation({
    onError: onError as any
  });

  const invitation = async () => {
    await updateUser({
      variables: {
        email,
        input: {
          receiveWeeklyEmails: false
        }
      }
    });
  };

  useEffect(() => {
    if (email) {
      invitation();
    }
  }, [email]);

  if (!email) return;

  return (
    <div
      className="h-screen flex flex-row justify-center items-center bg-no-repeat bg-center bg-fixed bg-cover"
      style={{ backgroundImage: 'url(/assets/onboarding-bg.png)' }}
    >
      <div className="absolute left-10 top-10">
        <div className="flex items-center">
          <Image src="/assets/new-logo.svg" width={200} height={70} />
          {/* <div className="ml-3 tracking-widest text-xl text-binaBlue">BINA</div> */}
        </div>
      </div>

      <Spin spinning={loading}>
        <Card bordered={false} className=" flex justify-center sm:w-[350px]">
          <div className="w-80 text-center">
            <h2 className="font-bold">Successfully unsubscribe from Weekly Summary Emails.</h2>
            <h4 className="mb-6 text-gray90 text-center mt-5 mx-5">
              You will no longer receive weekly summary emails from BINA.
            </h4>
            {/* <Form layout="vertical" form={form} onFinish={onFinish} requiredMark={false}>
                            <div className="flex justify-between">
                                <Button htmlType="submit" className="rounded-lg h-10 w-[80px] font-medium" disabled={!inviterAndTitle}>
                                    Decline
                                </Button>
                                <Button
                                    htmlType="submit"
                                    type="primary"
                                    className="rounded-lg h-10 w-[80px]font-medium"
                                    onClick={async () => {
                                        await setInvitationRef();
                                    }}
                                    disabled={!inviterAndTitle}
                                >
                                    Accept
                                </Button>
                            </div>
                        </Form> */}
          </div>
        </Card>
      </Spin>
    </div>
  );
};

export default Acceptation;
