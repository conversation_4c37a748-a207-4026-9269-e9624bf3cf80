import { Icon } from '@commons';
import { Button } from 'antd';
import { useRouter } from 'next/router';
import React from 'react';
import ForgotPasswordLayout from '../src/components/Layout/ForgotPasswordLayout';

const ResetPasswordSuccessful: React.FC = () => {
  const router = useRouter();
  return (
    <ForgotPasswordLayout>
      <div className="flex justify-center pt-40">
        <div className="pt-20">
          <Icon name="resetPassword" />
          <h1 className="text-2xl font-bold">Your password is reset!</h1>
          <p className="text-gray90 text-base">You can now login with your new password.</p>
          <Button
            htmlType="submit"
            type="primary"
            className="rounded-lg h-10 w-72 font-medium mt-4 sm:hidden md:hidden"
            onClick={() => router.push('/login')}
          >
            Back to login
          </Button>
        </div>
      </div>
    </ForgotPasswordLayout>
  );
};

export default ResetPasswordSuccessful;
