import { ExclamationCircleOutlined, UserOutlined } from '@ant-design/icons';
import SearchInput from '@components/forms/FormsInput/SearchInput';
import useQueries from '@components/hooks/useQueries';
import MemberTabLayout from '@components/members/MemberTabLayout';
import MemberChangeRoleModal from '@components/project/member/MemberChangeRoleModal';
import MemberEditDrawer from '@components/project/member/MemberEditDrawer';
import MemberInviteModal from '@components/project/member/MemberInviteModal';
import RemoveMembersModal from '@components/project/member/RemoveMembersModal';
import MemberTable from '@components/project/MemberTable';
import ThreeDotsDropDown from '@components/ThreeDotsDropDown';
import UserAvatar from '@components/UserAvatar';
import * as Gql from '@graphql';
import { manageFiles } from '@utils/authority';
import { onError } from '@utils/error';
import { Button, Col, Form, message, Modal, Radio, Space } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import { ColumnsType } from 'antd/lib/table';
import _ from 'lodash';
import { useRouter } from 'next/router';
import { useContext, useEffect, useMemo, useRef, useState } from 'react';
import { AppLayoutContext } from '../_app';

const Members = () => {
  const router = useRouter();
  const [memberId, setMemberId] = useState<string | undefined>();
  const [projectUserRole, setProjectUserRole] = useState<string>('');
  const memberEditDrawerRef = useRef<any>(null);
  const memberRemovalRef = useRef<any>(null);
  const { confirm } = Modal;
  const memberInviteModalRef = useRef<any>(null);
  const memberChangeRoleModalRef = useRef<any>(null);
  const [projectId, setProjectId] = useState<any>(null);
  const [membersType, setMembersType] = useState<string>('joined');
  const [searchForm] = useForm();
  const { userData: userMeData } = useContext(AppLayoutContext);

  useEffect(() => {
    setProjectUserRole(localStorage.getItem('ProjectUserRole') ?? '');
    setProjectId(localStorage.getItem('ProjectId'));
    setFilter({ user: { name: '' as any } });
  }, []);

  const viewOnly = projectUserRole === 'CanView';
  const { canCreate } = manageFiles(projectUserRole);
  const canManage = projectUserRole === Gql.ProjectUserRoleType.ProjectOwner || projectUserRole === Gql.ProjectUserRoleType.CloudCoordinator;

  useEffect(() => {
    if (membersType === 'joined') {
      refetchProjectUsers();
    } else {
      refetchPendingUsers();
    }
  }, [membersType]);

  const refetchProjectUsers = () => {
    getProjectOwner();
    getCloudCoordinator();
    getViewers();
    getEditor();
  };

  const refetchPendingUsers = () => {
    getPendingCloudCoordinator();
    getPendingViewer();
    getPendingEditor();
  };

  useEffect(() => {
    if (router.query.memberId) {
      memberEditDrawerRef?.current?.pushDrawer();
    }
  }, [router.query.memberId]);

  // ******* JOINED QUERY ******** //
  const { queries, setPaging, setFilter } = useQueries<Gql.ProjectUserFilter, Gql.ProjectUserSort>({
    usePagingParam: false
  });

  const { queries: ccQueries, setPaging: ccPaging } = useQueries<Gql.ProjectUserFilter, Gql.ProjectUserSort>({
    usePagingParam: false
  });

  const { queries: editorQueries, setPaging: editorPaging } = useQueries<Gql.ProjectUserFilter, Gql.ProjectUserSort>({
    usePagingParam: false
  });

  const { queries: viewerQueries, setPaging: viewerPaging } = useQueries<Gql.ProjectUserFilter, Gql.ProjectUserSort>({
    usePagingParam: false
  });

  const [getProjectOwner, { data: projectOwner, loading: projectOwnerLoading }] = Gql.useProjectUsersLazyQuery({
    variables: {
      filter: {
        role: { eq: Gql.ProjectUserRoleType.ProjectOwner },
        user: { name: { like: `%${queries.filter?.user?.name}%` } }
      }
    },
    onError: onError
  });

  const [getCloudCoordinator, { data: cloudCoordinator, refetch: refetchCloudCoordinator, loading: cloudCoordinatorLoading }] =
    Gql.useProjectUsersLazyQuery({
      variables: {
        ...ccQueries,
        filter: {
          role: { eq: Gql.ProjectUserRoleType.CloudCoordinator },
          user: { 
            name: { like: `%${queries.filter?.user?.name}%` },
            removedAt: { is: null }
          }
        }
      },
      onError: onError,
      fetchPolicy: 'network-only'
    });

  const [getEditor, { data: viewer, refetch: refetchViewer, loading: editorLoading }] = Gql.useProjectUsersLazyQuery({
    variables: {
      ...viewerQueries,
      filter: {
        role: { eq: Gql.ProjectUserRoleType.CanView },
        user: {
          name: { like: `%${queries.filter?.user?.name}%` },
          removedAt: { is: null }
        }
      }
    },
    onError: onError,
    fetchPolicy: 'network-only'
  });  

  const [getViewers, { data: editor, refetch: refetchEditor, loading: viewersLoading }] = Gql.useProjectUsersLazyQuery({
    variables: {
      ...editorQueries,
      filter: {
        role: { eq: Gql.ProjectUserRoleType.CanEdit },
        user: {
          name: { like: `%${queries.filter?.user?.name}%` },
          removedAt: { is: null }
        }
      }
    },
    onError: onError,
    fetchPolicy: 'network-only'
  });

  const dataSourceProjectOwner = projectOwner?.projectUsers.nodes ?? [];
  const totalCountProjectOwner = projectOwner?.projectUsers.totalCount ?? 0;

  const dataSourceCloudCoordinator = cloudCoordinator?.projectUsers.nodes ?? [];
  const totalCountCloudCoordinator = cloudCoordinator?.projectUsers.totalCount ?? 0;

  const dataSourceEditor = editor?.projectUsers.nodes ?? [];
  const totalCountEditor = editor?.projectUsers.totalCount ?? 0;

  const dataSourceViewer = viewer?.projectUsers.nodes ?? [];
  const totalCountViewer = viewer?.projectUsers.totalCount ?? 0;

  // ******* PENDING QUERY ******** //
  const { queries: pendingQueries, setPaging: pendingPaging } = useQueries<
    Gql.ProjectInvitationFilter,
    Gql.ProjectInvitationSort
  >({ usePagingParam: false });

  const { queries: pendingEditorQueries, setPaging: pendingEditorPaging } = useQueries<
    Gql.ProjectInvitationFilter,
    Gql.ProjectInvitationSort
  >({ usePagingParam: false });

  const { queries: pendingViewerQueries, setPaging: pendingViewerPaging } = useQueries<
    Gql.ProjectInvitationFilter,
    Gql.ProjectInvitationSort
  >({ usePagingParam: false });

  const [getPendingCloudCoordinator, { data: pendingCloudCoordinator, refetch: refetchPendingCloudCoordinator, loading: loadingPendingCC }] =
    Gql.useProjectInvitationsLazyQuery({
      variables: {
        ...pendingQueries,
        filter: {
          role: { eq: Gql.ProjectUserRoleType.CloudCoordinator },
          isAccepted: { isNot: true },
          projectId: { eq: projectId }
        }
      },
      onError: onError
    });

  const [getPendingViewer, { data: pendingViewer, refetch: refetchPendingViewer, loading: loadingPendingViewer }] = Gql.useProjectInvitationsLazyQuery(
    {
      variables: {
        ...pendingViewerQueries,
        filter: {
          role: { eq: Gql.ProjectUserRoleType.CanView },
          isAccepted: { isNot: true },
          projectId: { eq: projectId }
        }
      },
      onError: onError
    }
  );

  const [getPendingEditor, { data: pendingEditor, refetch: refetchPendingEditor, loading: loadingPendingEditor }] = Gql.useProjectInvitationsLazyQuery(
    {
      variables: {
        ...pendingEditorQueries,
        filter: {
          role: { eq: Gql.ProjectUserRoleType.CanEdit },
          isAccepted: { isNot: true },
          projectId: { eq: projectId }
        }
      },
      onError: onError
    }
  );

  const dataSourcePendingCloudCoordinator = pendingCloudCoordinator?.projectInvitations.nodes ?? [];
  const totalCountPendingCloudCoordinator = pendingCloudCoordinator?.projectInvitations.totalCount ?? 0;

  const dataSourcePendingEditor = pendingEditor?.projectInvitations.nodes ?? [];
  const totalCountPendingEditor = pendingEditor?.projectInvitations.totalCount ?? 0;

  const dataSourcePendingViewer = pendingViewer?.projectInvitations.nodes ?? [];
  const totalCountPendingViewer = pendingViewer?.projectInvitations.totalCount ?? 0;

  const [deleteMember] = Gql.useDeleteOneProjectUserMutation({
    variables: { id: _.toString(memberId) },
    onCompleted: () => {
      message.success('Deleted successfully');
      refetchCloudCoordinator();
      refetchEditor();
      refetchViewer();
    },
    onError: onError as any
  });

  const threeDotsItems = (user: Gql.User) => {
    const items = [];
    if (user?.id !== projectOwner?.projectUsers.nodes[0]?.userId || !user) {
      items.push({
        key: '1',
        label: 'Remove from project'
      });
    }

    if (user) {
      items.push({
        key: '2',
        label: 'Change user roles'
      });
    }

    return items;
  };

  const onClickRemoveUser = (invitationId: string, user: any) => {
    if (user) {
      memberRemovalRef?.current?.openModal(user);
    } else {
      showDeleteConfirm(invitationId, user);
    }
  };

  const [deleteInvitation] = Gql.useDeleteOneProjectInvitationMutation({
    variables: { id: _.toString(memberId) },
    onCompleted: () => {
      message.success('Deleted successfully');
      refetchPendingCloudCoordinator();
      refetchPendingEditor();
      refetchPendingViewer();
    },
    onError: onError as any
  });

  const onDelete = (id: string, user: object) => {
    if (!user) {
      return deleteInvitation({ variables: { id } });
    }
    deleteMember({ variables: { id } });
  };

  const showDeleteConfirm = (id: string, user: object) => {
    confirm({
      title: 'Are you sure?',
      icon: <ExclamationCircleOutlined />,
      content: 'Do you really want to delete this member?',
      okText: 'Yes',
      okType: 'danger',
      cancelText: 'No',
      onOk() {
        onDelete(id, user);
      },
      onCancel() {}
    });
  };

  // Reusable column generator with simplified color logic
  const generateColumns = (canManage: boolean, router: any, onRemove: (id: string, user: any) => void, onChangeRole?: (userId: string) => void): ColumnsType<any> => [
    {
      title: 'NAME',
      key: 'name',
      width: 600,
      render: (data: any) => {
        console.log('hello', data?.user?.avatar, data?.user?.name )
        return (
          <div className="grid grid-row-2 grid-cols-10 gap-x-[20px] gap-y-0">
            <div className="col-span-1">
              <UserAvatar
                username={data?.user?.name}
                src={data?.user?.avatar}
                style={{ backgroundColor: data?.user?.avatar ? "transparent" : data?.user?.color || "#E8EFFF", color: '#FFFFFF', marginTop: '6px', marginLeft: '2px' }}
                tooltip={data?.user?.name}
                align={{
                  offset: [-12, -10], 
                }}
              />
            </div>
            <div className="col-span-6">
              <h4
                className={`row-span-1 col-span-8 m-0 text-md ${data.user?.name ? 'font-semibold text-gray100' : 'font-normal'} self-start`}
                onClick={() => {
                  if (!data?.user) return;
                  setMemberId(data.id);
                  router.replace({
                    query: {
                      ...router.query,
                      memberId: data.id
                    }
                  });
                }}
              >
                {data.user?.name ?? 'Pending'}
              </h4>
              <p className="m-0"> {data.user?.email ?? data?.email}</p>
            </div>
          </div>
        );
      }
    },
    {
      title: 'PHONE',
      dataIndex: 'user',
      key: 'user',
      width: 200,
      render: (user: any) => <p>{user?.phoneNo ?? ''}</p>
    },
    {
      title: 'COMPANY',
      key: 'originCompany',
      render: (data: Gql.ProjectUserFieldsFragment) => <p>{data?.user?.companyOrigin ?? ''}</p>
    },
    {
      title: '',
      key: 'action',
      width: 80,
      render: (data: any) => {
        const items = threeDotsItems(data?.user);
        return canManage ? (
          <ThreeDotsDropDown
            onClick={(e: any) => {
              if (e.key === '1') onRemove(data.id, data.user);
              if (e.key === '2' && onChangeRole) onChangeRole(data.userId);
            }}
            items={items}
          />
        ) : null;
      }
    }
  ];

  const columnsProjectOwner = useMemo(
    () => generateColumns(canManage, router, onClickRemoveUser),
    [router, canManage]
  );

  const columnsCloudCoordinator = useMemo(
    () => generateColumns(canManage, router, onClickRemoveUser, (userId) => memberChangeRoleModalRef?.current?.openModal(userId)),
    [router, canManage]
  );

  const columnsEditor = useMemo(
    () => generateColumns(canManage, router, onClickRemoveUser, (userId) => memberChangeRoleModalRef?.current?.openModal(userId)),
    [router, canManage]
  );

  const columnsViewer = useMemo(
    () => generateColumns(canManage, router, onClickRemoveUser, (userId) => memberChangeRoleModalRef?.current?.openModal(userId)),
    [router, canManage]
  );

  return (
    <>
      <div className="p-5 absolute overflow-auto h-full w-full">
        <div className="mb-[70px]">
          <div>
            <h2>Members</h2>
            <div className="flex justify-between my-[20px] items-center">
              <div className="flex items-center">
                <Radio.Group
                  value={membersType}
                  onChange={value => setMembersType(value.target.value)}
                >
                  <Radio.Button key={0} value={'joined'} className="font-semibold">
                    Joined
                  </Radio.Button>
                  <Radio.Button key={1} value={'pending'} className="font-semibold">
                    Pending
                  </Radio.Button>
                </Radio.Group>
              </div>
              <div className="flex items-center">
                <Col>
                  <Form
                    form={searchForm}
                    onValuesChange={_.debounce(data => {
                      setFilter({ user: { name: data.keyword } });
                    }, 300)}
                  >
                    <Space>
                      <div style={{paddingTop: '4.5px'}}>
                        <SearchInput placeholder="Search by name" onPressEnter={() => { }}/>
                      </div>
                    </Space>
                  </Form>
                </Col>
                {canCreate && (
                  <Button
                    type="primary"
                    className="rounded-[8px] w-[158px] h-[40px] ml-[10px] mb-[4px]"
                    disabled={viewOnly}
                    onClick={() => memberInviteModalRef?.current?.openModal()}
                  >
                    Invite member
                  </Button>
                )}
              </div>
            </div>
          </div>

          {membersType === 'joined' ? (
            <>
              <MemberTable
                title="Project Owner"
                columns={columnsProjectOwner}
                dataSource={dataSourceProjectOwner}
                setPaging={setPaging}
                offset={queries.paging.offset}
                total={totalCountProjectOwner}
                pageSize={queries.paging.limit}
                loading={projectOwnerLoading}
              />
              <MemberTable
                title="Cloud Coordinator"
                columns={columnsCloudCoordinator}
                dataSource={dataSourceCloudCoordinator}
                setPaging={ccPaging}
                offset={ccQueries.paging.offset}
                total={totalCountCloudCoordinator}
                pageSize={ccQueries.paging.limit}
                loading={cloudCoordinatorLoading}
              />
              <MemberTable
                title="Editor"
                columns={columnsEditor}
                dataSource={dataSourceEditor}
                setPaging={editorPaging}
                offset={editorQueries.paging.offset}
                total={totalCountEditor}
                pageSize={editorQueries.paging.limit}
                loading={editorLoading}
              />
              <MemberTable
                title="Viewer"
                columns={columnsViewer}
                dataSource={dataSourceViewer}
                setPaging={viewerPaging}
                offset={viewerQueries.paging.offset}
                total={totalCountViewer}
                pageSize={viewerQueries.paging.limit}
                loading={viewersLoading}
              />
            </>
          ) : (
            <>
              <MemberTable
                title="Cloud Coordinator"
                columns={columnsCloudCoordinator}
                dataSource={dataSourcePendingCloudCoordinator}
                setPaging={pendingPaging}
                offset={pendingQueries.paging.offset}
                total={totalCountPendingCloudCoordinator}
                pageSize={pendingQueries.paging.limit}
                loading={loadingPendingCC}
              />
              <MemberTable
                title="Editor"
                columns={columnsEditor}
                dataSource={dataSourcePendingEditor}
                setPaging={pendingEditorPaging}
                offset={pendingEditorQueries.paging.offset}
                total={totalCountPendingEditor}
                pageSize={pendingEditorQueries.paging.limit}
                loading={loadingPendingEditor}
              />
              <MemberTable
                title="Viewer"
                columns={columnsViewer}
                dataSource={dataSourcePendingViewer}
                setPaging={pendingViewerPaging}
                offset={pendingViewerQueries.paging.offset}
                total={totalCountPendingViewer}
                pageSize={pendingViewerQueries.paging.limit}
                loading={loadingPendingViewer}
              />
            </>
          )}

          <RemoveMembersModal ref={memberRemovalRef} refetch={refetchProjectUsers} />
          <MemberEditDrawer ref={memberEditDrawerRef} />
          <MemberInviteModal ref={memberInviteModalRef} refetch={refetchPendingUsers} />
          <MemberChangeRoleModal ref={memberChangeRoleModalRef} refetch={refetchProjectUsers} />
        </div>
      </div>
    </>
  );
};

Members.auth = true;
Members.Layout = MemberTabLayout;
export default Members;
