import { ExclamationCircleOutlined } from '@ant-design/icons';
import SearchInput from '@components/forms/FormsInput/SearchInputs/ProjectDocumentsSearch';
import useQueries, { convertToLikeComparison } from '@components/hooks/useQueries';
import MemberTabLayout from '@components/members/MemberTabLayout';
import ContactAddDrawer from '@components/project/contact/ContactAddDrawer';
import ContactEditDrawer from '@components/project/contact/ContactEditDrawer';
import ContactImportDrawer from '@components/project/contact/ContactImportDrawer';
import ThreeDotsDropDown from '@components/ThreeDotsDropDown';
import * as Gql from '@graphql';
import { onError } from '@utils/error';
import { Button, Form, message, Modal, Space, Table } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import { ColumnsType } from 'antd/lib/table';
import _ from 'lodash';
import moment from 'moment';
import { useRouter } from 'next/router';
import { useEffect, useMemo, useRef, useState } from 'react';

const Contacts = () => {
  const router = useRouter();
  const [form] = useForm();
  const contactAddDrawerRef = useRef<any>(null);
  const contactEditDrawerRef = useRef<any>(null);
  const contactImportDrawerRef = useRef<any>(null);
  const { confirm } = Modal;
  const [projectUserRole, setProjectUserRole] = useState<string>('');
  const [projectId, setProjectId] = useState<any>(null);
  
  const canManage = projectUserRole === Gql.ProjectUserRoleType.ProjectOwner || projectUserRole === Gql.ProjectUserRoleType.CloudCoordinator;

  useEffect(() => {
    if (router.query.contactId) {
      contactEditDrawerRef?.current?.pushDrawer();
    }
  }, [router.query.contactId]);

  useEffect(() => {
    setProjectUserRole(localStorage.getItem('ProjectUserRole') ?? '');
    setProjectId(localStorage.getItem('ProjectId'));
  }, []);

  // Get queries from contact
  const { queries, setPaging, setFilter, setSorter } = useQueries<Gql.ContactFilter, Gql.ContactSort>();

  const { data, refetch, loading } = Gql.useContactsQuery({
    notifyOnNetworkStatusChange: true,
    fetchPolicy: 'network-only',
    onError: onError,
    variables: {
      ...queries,
      sorting: [
        {
          direction: Gql.SortDirection.Desc,
          field: Gql.ContactSortFields.UpdatedAt
        }
      ]
    }
  });

  const dataSource = data?.contacts.nodes ?? [];
  const totalCount = data?.contacts.totalCount;

  const [deleteContact] = Gql.useDeleteOneContactMutation({
    onCompleted: () => {
      message.success('Deleted successfully');
      refetch();
    }
  });

  const onDelete = (id: string) => {
    deleteContact({ variables: { id } });
  };

  const onFilter = (values: any) => {
    const search = { name: values.keyword, phoneNo: values.keyword };

    setFilter({ or: convertToLikeComparison(search) });
  };

  const showDeleteConfirm = (id: string) => {
    confirm({
      title: 'Are you sure?',
      icon: <ExclamationCircleOutlined />,
      content: 'Do you really want to delete this contact?',
      okText: 'Yes',
      okType: 'danger',
      cancelText: 'No',
      onOk() {
        onDelete(id);
      },
      onCancel() {}
    });
  };

  const columns = useMemo(
    (): ColumnsType<any> => [
      {
        title: 'CONTACT NAME',
        key: 'name',
        render: (data: any) => {
          return (
            <div
              className="font-bold cursor-pointer"
              onClick={() => {
                router.replace({
                  query: {
                    ...router.query,
                    contactId: data.id
                  }
                });
              }}
            >
              {data.name}
            </div>
          );
        }
      },
      {
        title: 'COMPANY NAME',
        dataIndex: 'contactCompany',
        key: 'contactCompany',
        render: contactCompany => contactCompany.name
      },
      {
        title: 'EMAIL',
        dataIndex: 'email',
        key: 'email'
      },
      {
        title: 'PHONE',
        dataIndex: 'phoneNo',
        key: 'phoneNo'
      },
      // {
      //   title: "CONTACT OWNER",
      //   dataIndex: "owner",
      //   key: "owner",
      //   render: (owner) => owner.name,
      // },
      // {
      //   title: "LAST UPDATED",
      //   dataIndex: "updatedAt",
      //   key: "updatedAt",
      //   render: (updatedAt) => moment(updatedAt).format("DD MMM YYYY"),
      // },
      {
        title: '',
        key: 'action',
        render: (data: any) => (
          <>
          {canManage && 
            <ThreeDotsDropDown
              onClick={(e: any) => {
                showDeleteConfirm(data.id);
              }}
              items={[{ key: '1', label: 'Delete' }]}
            />
          }
          </>
        )
      }
    ],
    [router]
  );

  return (
    <>
      <div className="p-5">
        <div className="flex justify-between mb-[20px]">
          <h2>Contacts</h2>
          <div>
            <Form form={form} onFinish={onFilter}>
              <Space>
                <div style={{paddingTop: '7px'}}>
                  <SearchInput onPressEnter={() => form.submit()} placeholder={'Search by contact name or phone'} />
                </div>
                <Button
                  type="primary"
                  className="rounded-[8px] w-[158px] h-[40px]"
                  onClick={() => contactAddDrawerRef?.current?.pushDrawer()}
                >
                  Add Contact
                </Button>
              </Space>
            </Form>
          </div>
        </div>
        <Table
          dataSource={dataSource}
          columns={columns}
          size="small"
          loading={loading}
          className="dashboard-table"
          pagination={{
            pageSize: queries.paging.limit,
            current: queries.paging.offset / queries.paging.limit + 1,
            total: totalCount ?? 0
          }}
          onChange={paginate => {
            const { current, pageSize } = paginate;
            if (pageSize !== undefined && current !== undefined) {
              setPaging({ offset: (current - 1) * pageSize, limit: pageSize });
            }
          }}
        />

        <ContactAddDrawer onSaved={() => refetch()} ref={contactAddDrawerRef} />
        <ContactEditDrawer ref={contactEditDrawerRef} onSaved={() => refetch()} />

        <ContactImportDrawer onSaved={() => refetch()} ref={contactImportDrawerRef} />
      </div>
    </>
  );
};

Contacts.auth = true;
Contacts.Layout = MemberTabLayout;
export default Contacts;
