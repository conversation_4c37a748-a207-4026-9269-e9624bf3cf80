/* eslint-disable @next/next/no-sync-scripts */
import Document, { Head, Html, Main, NextScript } from 'next/document';

class MyDocument extends Document {
  render() {
    return (
      <Html>
        <Head>
          <link rel="stylesheet" 
            href="https://developer.api.autodesk.com/modelderivative/v2/viewers/7.100/style.min.css?v=v7.*" 
            integrity="sha384-1L94irUehjrBUJO7FZgBui0PJMe5sa6jxDDAH7Kz9esuuWKfUyUa1HGxfC1UJNPm"
            crossOrigin="anonymous"
          />
          <link rel="stylesheet" 
            href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
            integrity="sha384-2HsR+d+xrUsBSTVcOmiX+ZnpmxooiR5a3a7FV7pNRSkfbl+GlbL7fpUVnp/YrhFY"
            crossOrigin="anonymous"
          />
          <link
            rel="stylesheet"
            href="https://cdn.jsdelivr.net/npm/frappe-gantt@0.6.1/dist/frappe-gantt.css"
            integrity="sha384-7m7QARZ+I5i/e3zNLmxAeO3v1vOgtpJBC/b0Aev34b6oIOxdjl7lSXlILQgb8b3Z"
            crossOrigin="anonymous"
          />
          <script 
          src="https://developer.api.autodesk.com/modelderivative/v2/viewers/7.100/viewer3D.min.js" 
          integrity="sha384-I2hEOq9KKDaJSGlgB6ANd9rvxQaAJuFzCCp2F/duMTt0B3WsCgw/83cBbUJlpp0l" 
          crossOrigin="anonymous"/>
          <script
            src="https://cdnjs.cloudflare.com/ajax/libs/frappe-gantt/0.6.1/frappe-gantt.min.js"
            integrity="sha384-vIVXvmnGMKcWl9tl8Erv6xZ+73fWqZSOY0D31kkKw/VZocq7VAG3zqYhuOn0Yzlq"
            crossOrigin="anonymous"
          ></script>
          <script
            src="https://cdn.jsdelivr.net/npm/@dxp-dc/esdk-obs-browserjs@3.20.1/dist/@dxp-dc/esdk-obs-browserjs-es5-3.20.1.min.js"
            integrity="sha384-NzASoZgPOBefgPpMvlnn5/6xEOV7u0KOPaRp5tLPNQ/5R0+IrTZhLlf93I6oIzBS"
            crossOrigin="anonymous"
          ></script>
        </Head>
        <body>
          <Main />
          <NextScript />
        </body>
      </Html>
    );
  }
}

export default MyDocument;
