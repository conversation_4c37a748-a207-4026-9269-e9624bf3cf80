import { Button, Form, Input, message } from 'antd';
import React, { useState } from 'react';

import { UserAuthService } from 'src/api';
import ForgotPasswordLayout from '../src/components/Layout/ForgotPasswordLayout';

type FormData = { email: string };

const ForgotPassword: React.FC = () => {
  const [email, setEmail] = useState<string>();
  const [loading, setLoading] = useState(false);

  const onFinish = async (formData: FormData) => {
    try {
      setLoading(true);
      const email = formData.email ?? '';
      await UserAuthService.forgotPassword({ body: { email } });
      setEmail(formData.email);
    } catch (e: any) {
      message.error(e.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <ForgotPasswordLayout>
      <div className="flex justify-center pt-20">
        <div className=" w-72">
          {!email ? (
            <>
              <h1 className="text-2xl font-bold mb-3">Forgot your password?</h1>
              <p className="text-gray90 text-base mb-3">
                Enter your email address and {"we'll"} send you a password reset link.
              </p>
              <Form layout="vertical" onFinish={onFinish} requiredMark={false}>
                <Form.Item
                  label="Email address"
                  className="text-gray90"
                  name="email"
                  rules={[
                    { required: true, message: 'Email is required' },
                    { type: 'email', message: 'Valid email is required' }
                  ]}
                >
                  <Input className="rounded-lg h-10 w-72" />
                </Form.Item>

                <Button htmlType="submit" type="primary" className="rounded-lg h-10 w-72 font-medium" loading={loading}>
                  Send password reset link
                </Button>
              </Form>
            </>
          ) : (
            <>
              <h1 className="text-2xl font-bold mb-3">Password reset request was sent successfully.</h1>
              <p className="text-gray90 text-base mb-3">
                Please check your email <span className="font-bold">{email}</span> to reset your password.
              </p>
            </>
          )}
        </div>
      </div>
    </ForgotPasswordLayout>
  );
};

export default ForgotPassword;
