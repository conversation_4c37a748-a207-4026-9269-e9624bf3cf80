import { apiCaller } from '@utils/axios';
import axios from 'axios';
import _ from 'lodash';
import NextAuth, { User } from 'next-auth';
import { JWT } from 'next-auth/jwt';
import CredentialsProvider from 'next-auth/providers/credentials';
import FacebookProvider from 'next-auth/providers/facebook';
import GoogleProvider from 'next-auth/providers/google';

interface ISocialLoginResult {
  accessToken: string;
  refreshToken: string;
  accessTokenExpiry: number;
}

interface AuthResponse {
  accessToken: string;
  refreshToken: string;
  [key: string]: any;
}

export default NextAuth({
  providers: [
    CredentialsProvider({
      // The name to display on the sign in form (e.g. 'Sign in with...')
      name: 'Credentials',
      // The credentials property is used to generate a suitable form on the sign in page.
      credentials: {
        email: { label: 'Email', type: 'text', placeholder: 'jsmith' },
        password: { label: 'Password', type: 'password' },
        rememberMe: { label: 'rememberMe', type: 'boolean' }
      },

      async authorize(credentials): Promise<any> {
        try {
          const { refreshToken, accessToken, clientDeviceInfo } = credentials as any;

          if (refreshToken && accessToken) {
            return { refreshToken, accessToken };
          }
          const res = await axios.post<AuthResponse>(
            `${process.env.NEXT_AUTH_PUBLIC_API_URL}/api/auth/user/sign-in`,
            {
              email: credentials?.email,
              password: credentials?.password,
              rememberMe: Boolean(credentials?.rememberMe),
              clientDeviceInfo,
            }
          );
          
          if (res.data.accessToken) {
            return res.data;
          }
        } catch (e: any) {
          throw new Error(e.response.data.message);
        }
      }
    }),
    GoogleProvider({
      clientId: process.env.GOOGLE_APP_ID ?? '',
      clientSecret: process.env.GOOGLE_APP_SECRET ?? ''
    }),
    FacebookProvider({
      clientId: process.env.FACEBOOK_APP_ID ?? '',
      clientSecret: process.env.FACEBOOK_APP_SECRET ?? ''
    })
  ],

  callbacks: {
    async signIn({ account, user }) {
      let token: ISocialLoginResult | null = null;
      if (account && account.provider === 'google') {
        const googleToken = account.access_token as string;
        token = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/auth/user/google-signIn`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ googleToken })
        }).then(res => res.json());
      } else if (account && account.provider === 'facebook') {
        const facebookToken = account.access_token as string;
        token = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/auth/user/facebook-signIn`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ facebookToken })
        }).then(res => res.json());
      }

      if (token) {
        // @ts-ignore
        user.accessToken = token.accessToken;
        // @ts-ignore
        user.refreshToken = token.refreshToken;
        // @ts-ignore
        user.accessTokenExpiry = token.accessTokenExpiry;
      }

      return !_.isEmpty(token) || !_.isEmpty(user);
    },

    // called after successful signin
    jwt: async ({ token, user }: { token: JWT; user?: User }) => {
      if (user) {
        // This will only be executed at login. Each next invocation will skip this part.
        //@ts-ignore
        token.accessToken = user.accessToken;
        //@ts-ignore
        token.accessTokenExpiry = user.accessTokenExpiry;
        //@ts-ignore
        token.refreshToken = user.refreshToken;
      }

      // If accessTokenExpiry is 24 hours, we have to refresh token before 24 hours pass.
      // check if expire in 1 hour
      const oneHourFromNow = (Date.now() + 60 * 60 * 1000)/1000;
      //@ts-ignore
      const shouldRefreshTime = token.accessTokenExpiry < oneHourFromNow;

      // If the token is still valid, just return it.
      if (shouldRefreshTime) {
        const newToken = await refreshAccessToken(token);
        return Promise.resolve(newToken);
      }

      return Promise.resolve(token);
    }, // called whenever session is checked
    session: async ({ session, token }) => {
      // Here we pass accessToken to the client to be used in authentication with your API
      //@ts-ignore
      session.accessToken = token.accessToken;

      const headers = {
        Authorization: `Bearer ${token.accessToken}`
      };

      await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/api/auth/user/session`, { headers }).then(res => {
        // if user is removed from the system, we should remove the session
        if (res.status === 400) {
          return null
        }
      })
      return Promise.resolve(session);
    }
  },
  jwt: {
    secret: process.env.NEXT_AUTH_JWT_SECRET
    // encryption: true,
  },
  session: {
    // jwt: true,
    maxAge: 30 * 24 * 60 * 60
  }
});

const refreshAccessToken = async (tokenObject: JWT) => {
  try {
    // Get a new set of tokens with a refreshToken
    const headers = {
      Authorization: `Bearer ${tokenObject.accessToken}`
    };
    const tokenResponse = await axios.post(
      `${process.env.NEXT_PUBLIC_API_URL}/api/auth/user/revoke-authentication`,
      {
      refreshToken: tokenObject.refreshToken
      },
      { headers }
    );
    return {
      ...tokenObject,
      accessToken: tokenResponse.data.accessToken,
      accessTokenExpiry: tokenResponse.data.accessTokenExpiry,
      refreshToken: tokenResponse.data.refreshToken
    };
  } catch (error) {
    return {
      ...tokenObject,
      error: 'RefreshAccessTokenError'
    };
  }
};
