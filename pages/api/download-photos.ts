import axios from 'axios';
import { NextApiRequest, NextApiResponse } from 'next';
import { Readable } from 'stream';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const imageUrl = req.query.url as string;

  try {
    const response = await axios.get(imageUrl, { responseType: 'stream' });

    if (response.headers['content-type']) {
      res.setHeader('Content-Type', response.headers['content-type']);
    }
    if (response.headers['content-length']) {
      res.setHeader('Content-Length', response.headers['content-length']);
    }

    (response.data as Readable).pipe(res);
  } catch (error) {
    console.error('Error proxying image:', error);
    res.status(404).send('Image not found');
  }
}
