import axios from 'axios';
import type { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const message = decodeURIComponent(req.query.mes as string);
  await axios.post(
    '*********************************************************************************',
    { text: message },
    { headers: { 'Content-type': 'application/json' } }
  );

  res.status(200).send('done');
}
