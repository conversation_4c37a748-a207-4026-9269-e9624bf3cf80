import axios from 'axios';
import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const imageUrl = req.query.url as string;
  try {
    // Make a request to the image URL and get the response data as array buffer
    const response = await axios({
      method: 'get',
      url: imageUrl,
      responseType: 'arraybuffer'
    });

    // Set the appropriate headers
    if (response.headers['content-type']){
      res.setHeader('Content-Type', response.headers['content-type']);
    }

    // Send the response with the base64 encoded image data
    res.status(200).send(response.data);
  } catch (error) {
    // console.error('Error proxying image:', error);
    res.status(404).send('Image not found');
  }
}
