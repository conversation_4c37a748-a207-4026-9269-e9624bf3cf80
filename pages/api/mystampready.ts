import axios from 'axios';
import moment from 'moment';
import { NextApiRequest, NextApiResponse } from 'next';
import { getConfigs, IRequestConfig, IRequestOptions } from 'src/api';
var querystring = require('querystring');
var md5 = require('md5');

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const requestMethod = req.method;

  switch (requestMethod) {
    case 'POST':
      let url = 'https://mystampready.com/const/api/?get-const';

      const configs: IRequestConfig = getConfigs('post', 'application/x-www-form-urlencoded', url, {});

      const date = moment(new Date()).format('YYYYMMDDSS');
      const oid = '' + date + req.body?.company?.id + ('' + req.body?.id);

      configs.data = querystring.stringify({
        oid: '' + oid,
        INU: 'c0edee1372df6073ed600ae2b7677bae',
        oidt: md5('07240953fff11c92c2edf6e3357fa3d6-' + oid + '-c0edee1372df6073ed600ae2b7677bae')
      });
      const result = await axios(configs);
      res.status(200).json({ data: result.data });
  }
}