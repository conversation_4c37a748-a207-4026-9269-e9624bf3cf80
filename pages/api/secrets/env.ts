// Next.js API route support: https://nextjs.org/docs/api-routes/introduction
import type { NextApiRequest, NextApiResponse } from 'next';

// @ys: include all server side env here, obfuscate sensitive data as needed as I havent found the way to limit this to specific session only
export default async function handler(req: NextApiRequest, res: NextApiResponse<any>) {
  res.status(200).json([
    { code: 'APP_ENV', value: process.env.APP_ENV },
    { code: 'NEXTAUTH_URL', value: process.env.NEXTAUTH_URL },
    {
      code: 'NEXT_AUTH_JWT_SECRET',
      value: process.env.NEXT_AUTH_JWT_SECRET?.toString().replaceAll(/.(?=.{5,}$)/g, '*')
    },
    { code: 'NEXT_AUTH_PUBLIC_API_URL', value: process.env.NEXT_AUTH_PUBLIC_API_URL },
    { code: 'API_SCHEMA_PATH', value: process.env.API_SCHEMA_PATH },
    { code: 'GRAPHQL_SCHEMA_PATH', value: process.env.GRAPHQL_SCHEMA_PATH },
    { code: 'FACEBOOK_APP_ID', value: process.env.FACEBOOK_APP_ID?.toString().replaceAll(/.(?=.{5,}$)/g, '*') },
    {
      code: 'FACEBOOK_APP_SECRET',
      value: process.env.FACEBOOK_APP_SECRET?.toString().replaceAll(/.(?=.{5,}$)/g, '*')
    },
    { code: 'GOOGLE_APP_ID', value: process.env.GOOGLE_APP_ID },
    { code: 'GOOGLE_APP_SECRET', value: process.env.GOOGLE_APP_SECRET?.toString().replaceAll(/.(?=.{5,}$)/g, '*') },
    { code: 'NEXT_SHARP_PATH', value: process.env.NEXT_SHARP_PATH }
  ]);
}
