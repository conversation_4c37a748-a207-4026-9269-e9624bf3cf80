pipeline {
    agent any

    environment {
        AZURE_CLIENT_ID     = credentials('AZURE_CLIENT_ID')
        AZURE_CLIENT_SECRET = credentials('AZURE_CLIENT_SECRET')
        AZURE_TENANT_ID     = credentials('AZURE_TENANT_ID')        
        IMAGE_NAME          = 'bina-web'
        IMAGE_TAG           = 'latest'
    }

    stages {
        stage('Checkout Source') {
            steps {
                checkout scm
            }
        }

        stage('Set Dynamic Variables') {
            steps {
                script {
                    env.ACR_NAME = (env.NODE_ENV == 'production') ? 'binaprod' : 'binastg'
                    env.ENV_FILE = (env.NODE_ENV == 'production') ? 'BINA_WEB_PROD_ENV' : 'BINA_WEB_STG_ENV'
                }
            }
        }

        stage('Azure Login') {
            steps {
                sh '''
                    set -e
                    echo "🔐 Logging into Azure..."
                    az login --service-principal -u "$AZURE_CLIENT_ID" -p "$AZURE_CLIENT_SECRET" --tenant "$AZURE_TENANT_ID" --output none
                '''
            }
        }

       stage('Inject .env File') {
          steps {
              script {
                  def credId = env.ENV_FILE
                  withCredentials([file(credentialsId: credId, variable: 'ENV_FILE')]) {
                      sh '''
                          set -e
                          echo "📁 Injecting .env file..."                        
                          cp "$ENV_FILE" .env
                          echo "✅ .env file injected"
                      '''
                  }
              }
          }
      }

        stage('Build Docker Image') {
            steps {
                sh '''
                    set -e
                    echo "🔨 Building Docker image..."                    
                    docker buildx build --platform linux/amd64 -t $ACR_NAME.azurecr.io/$IMAGE_NAME:$IMAGE_TAG .
                '''
            }
        }

        stage('Login to ACR') {
            steps {
                sh '''
                    set -e
                    echo "🔓 Logging into Azure Container Registry..."
                    az acr login --name $ACR_NAME
                '''
            }
        }

        stage('Push Docker Image') {
            steps {
                sh '''
                    set -e
                    echo "🚀 Pushing Docker image to ACR..."
                    docker push $ACR_NAME.azurecr.io/$IMAGE_NAME:$IMAGE_TAG
                '''
            }
        }
    }

    post {
        always {
            cleanWs()
        }
        failure {
            echo "❌ Build failed! Check the logs above."
        }
        success {
            echo "✅ Build and push completed successfully!"
        }
    }
}
