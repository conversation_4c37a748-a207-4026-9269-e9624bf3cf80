{"editor.codeActionsOnSave": {"source.sortImports": "explicit"}, "cSpell.words": ["onprocessfiles"], "conventionalCommits.scopes": ["Enchance error handling"], "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.detectIndentation": false, "editor.insertSpaces": true, "editor.tabSize": 2, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "spellright.language": ["en"], "spellright.documentTypes": ["markdown", "latex", "plaintext", "typescriptreact"], "[jsonc]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "liveServer.settings.port": 5501, "[xml]": {"editor.defaultFormatter": "redhat.vscode-xml"}, "files.associations": {"plyconfig.json": "jsonc"}}