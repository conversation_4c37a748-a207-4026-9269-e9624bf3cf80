import { aliasMutation, aliasQuery } from './graphql';
import { getFixturesDoc } from './utils';

Cypress.Commands.add('login', (username, password) => {
  cy.session(
    [username, password],
    () => {
      cy.visit('/login');
      cy.get('input[id="email"]').type(username);
      cy.get('input[id="password"]').type(password);
      cy.contains('Login').click({ multiple: true });

      cy.intercept('GET', '/api/auth/session').as('session');
      cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'getUserMe')).as('getUserMe');
      cy.intercept('GET', '/api/auth/user/is-first-time-sign-in').as('isFirstTimeSignIn');

      cy.wait(['@session', '@getUserMe', '@isFirstTimeSignIn'], { timeout: 100000 }).spread(
        (session, getUserMe, firstTimeSignIn) => {
          expect(session.response?.statusCode).to.eq(200);
          expect(session.response?.body).to.have.property('accessToken');

          expect(getUserMe.response?.statusCode).to.eq(200);
          expect(getUserMe.response?.statusCode).to.eq(200);
        }
      );

      cy.url().should('include', '/?size=10&page=1');
      cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'getProjects')).as('getProjects');
      cy.intercept('POST', 'https://api.novu.co/v1/widgets/session/initialize').as('initialize');
      cy.intercept('GET', 'https://api.novu.co/v1/widgets/notifications/feed?page=0').as('feed');
      cy.intercept('GET', 'https://api.novu.co/v1/widgets/notifications/unseen').as('unseen');

      cy.wait(['@getUserMe', '@isFirstTimeSignIn'], { timeout: 100000 }).spread((getUserMe, firstTimeSignIn) => {
        expect(getUserMe.response?.statusCode).to.eq(200);
      });

      cy.wait('@getProjects', { timeout: 100000 }).then(projects => {
        expect(projects.response?.statusCode).to.eq(200);

        cy.wait('@initialize', { timeout: 100000 }).then(initialize => {
          expect(initialize.response?.statusCode).to.eq(201);
        });

        cy.get('div[class="ant-card-cover"]').should('be.visible');
        cy.get('div[id="Project 1"]').find('img').click({
          force: true
        });

        cy.url().should('include', '/dashboard', { timeout: 100000 });
        cy.end();
      });
    },
    {
      cacheAcrossSpecs: true
    }
  );
});

Cypress.Commands.add('novuAuth', () => {
  cy.intercept('POST', 'https://api.novu.co/v1/widgets/session/initialize').as('initialize');
  cy.intercept('GET', 'https://api.novu.co/v1/widgets/notifications/feed?page=0').as('feed');

  cy.wait('@initialize', { timeout: 100000 }).then(initialize => {
    expect(initialize.response?.statusCode).to.eq(201);
  });

  cy.wait('@feed', { timeout: 100000 }).then(feed => {
    expect(feed.response?.statusCode).to.eq(201);
  });
});

Cypress.Commands.add('SetProjectId', () => {
  cy.session(['projectId'], () => {
    cy.window().then(win => {
      win?.localStorage?.setItem('projectId', '1');
    });
  });
});

Cypress.Commands.add('multiSelect', (selector, text, label) => {
  cy.get(`input[id='${selector}']`).click({ force: true });
  cy.get('.ant-select-dropdown :not(.ant-select-dropdown-hidden)')
    .find('.ant-select-item-option')
    .each((el, index) => {
      if (el.text() == text) {
        cy.wrap(el).click();
      }
    });

  cy.contains(label).click();
});

Cypress.Commands.add('multi_select', (selector, text, label) => {
  cy.get(`input[id='${selector}']`).click({ force: true });
  cy.get('.ant-select-dropdown :not(.ant-select-dropdown-hidden)')
    .find('.ant-select-item-option')
    .each((el, index) => {
      cy.wait(1000);
      const optionText = el.text();
      text?.forEach(value => {
        if (optionText.includes(value)) {
          cy.wrap(el).click({
            multiple: true,
            force: true
          });
        }
      });
    });

  cy.contains(label).click();
});

Cypress.Commands.add('uploadPhotos', (selector: string, label: string, file: string[]) => {
  cy.contains(label).click({ force: true });
  cy.get(`input[id="${selector}"]`).selectFile(file, {
    action: 'drag-drop',
    force: true
  });

  cy.contains('Finish').click();
});

Cypress.Commands.add('uploadAttachment', (selector, files) => {
  cy.get(`input[id="${selector}"]`).selectFile(files, {
    force: true
  });
});

Cypress.Commands.add('dropdown', (label, mode, index) => {
  if (mode === 'single') {
    cy.get('a[class="ant-dropdown-trigger mb-3"]').first().click();
  } else {
    cy.get('a[class="ant-dropdown-trigger mb-3"]')
      .eq(index ?? 0)
      .click();
  }
  cy.get('li[class="ant-dropdown-menu-item ant-dropdown-menu-item-only-child"]').contains(label).click();
});

Cypress.Commands.add('tableSelection', (selector, targetIndex) => {
  //? check if the targetIndex is in the index, then click the checkbox
  cy.get(`input[id="manage-checkbox-${selector}"`).each((el, index) => {
    if (targetIndex.includes(index)) {
      cy.wrap(el).click({ force: true });
    }
  });
});

Cypress.Commands.add('createTaskGroup', name => {
  cy.visit('/tasks-overview');

  cy.contains('Create Group').click();
  cy.get('input[id="title"]').should('not.have.value');

  cy.intercept('POST', `/api/project-group/create`).as('createWorkspaceGroup');
  cy.get('input[id="title"]').type(name);

  cy.get('button[type="submit"]').click();
  cy.wait('@createWorkspaceGroup').then(createWorkspaceGroup => {
    expect?.(createWorkspaceGroup?.response?.statusCode).to.eq(201);
  });
});

Cypress.Commands.add('createTask', (taskName, groupName) => {
  cy.visit('/tasks');
  cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'projectUsers')).as('projectUsers');

  cy.contains('Create Task').should('be.visible').click();

  cy.get('div[class="ant-drawer-content-wrapper"]').should('be.visible');
  cy.get('textarea[placeholder="Task Name"]').type(taskName);

  cy.wait('@projectUsers').then(projectUsers => {
    expect(projectUsers?.response?.statusCode).to.eq(200);
    cy.multi_select('assignees-picker', ['binaTester'], 'Assignee');
  });

  cy.multi_select('group-picker', groupName, 'Group');

  cy.contains('Save').click();
  cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'createTask')).as('createTask');
  cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'assignTask')).as('assignTask');

  cy.wait(['@createTask', '@assignTask'], { timeout: 10000 }).spread((createTask, assignTask) => {
    expect(createTask.response.statusCode).to.eq(200);
    expect(assignTask.response.statusCode).to.eq(200);

    cy.contains('Testing creating task using cypress').should('be.visible');
    cy.end();
  });
});

Cypress.Commands.add('createWorkspaceGroup', name => {
  cy.visit('/digital-form');

  cy.get('button[id="create-group"]').should('be.visible').click();
  cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'createWorkspaceGroup')).as(
    'createWorkspaceGroup'
  );

  cy.get('input[id="name"]').type(name);
  cy.get('button[type="submit"]').click();

  cy.wait('@createWorkspaceGroup', { timeout: 10000 }).then(createWorkspaceGroup => {
    expect(createWorkspaceGroup?.response?.statusCode).to.eq(200);
  });
});

Cypress.Commands.add('createWorkspaceDocument', groupName => {
  const documentName = getFixturesDoc({ numberOfFiles: 1, type: 'pdf' });

  cy.visit('/digital-form/all-form');

  //? upload document
  cy.contains('Draft').click();
  cy.contains('Create Document').click();

  cy.get('div[class="ant-modal-content"]').should('be.visible');

  cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'createOneProjectDocument')).as(
    'createOneProjectDocument'
  );
  cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'projectDocuments')).as(
    'projectDocuments'
  );

  cy.uploadAttachment('upload-document', documentName);

  cy.wait(['@createOneProjectDocument', '@projectDocuments']).spread((createOneProjectDocument, projectDocuments) => {
    expect(createOneProjectDocument.response.statusCode).to.eq(200);
    expect(projectDocuments.response.statusCode).to.eq(200);
  });

  //? add document detail
  cy.get('div[id="document-name"]').first().click();

  cy.wait('@projectDocuments').then(projectDocuments => {
    expect(projectDocuments?.response?.statusCode).to.eq(200);
    cy.wait(1000);

    //? add assignee and ccs
    cy.multi_select('assignees-picker', ['binaTester', 'binaUser'], 'Assignee');

    cy.multi_select('group-picker', groupName, 'Group');

    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'updateOneProjectDocument')).as(
      'updateOneProjectDocument'
    );
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'assignAttachmentWorkspace')).as(
      'assignAttachmentWorkspace'
    );
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'assignPhotoWorkspace')).as(
      'assignPhotoWorkspace'
    );

    cy.get('button[type="submit"]').contains('Save').click();

    cy.wait(['@updateOneProjectDocument', '@assignAttachmentWorkspace', '@assignPhotoWorkspace']).spread(
      (updateOneProjectDocument, assignAttachmentWorkspace, assignPhotoWorkspace) => {
        expect(updateOneProjectDocument.response.statusCode).to.eq(200);
        expect(assignAttachmentWorkspace.response.statusCode).to.eq(200);
        expect(assignPhotoWorkspace.response.statusCode).to.eq(200);
      }
    );
  });

  cy.dropdown('Request Approval', 'multiple', 0);

  cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'updateOneProjectDocument')).as(
    'updateOneProjectDocument'
  );
  cy.get('button[type="button"]').contains('Request Approval').click();

  cy.wait('@updateOneProjectDocument').then(updateOneProjectDocument => {
    expect(updateOneProjectDocument?.response?.statusCode).to.eq(200);
  });
});

declare global {
  namespace Cypress {
    interface Chainable {
      login(email: string, password: string): Chainable<void>;
      SetProjectId(): Chainable<void>;
      multiSelect: (selector: string, text: string, label: string) => Chainable<void>;
      uploadPhotos: (selector: string, label: string, file: string[]) => Chainable<void>;
      dropdown: (label: string, mode: 'single' | 'multiple', index?: number) => Chainable<void>;
      uploadAttachment: (selector: string, files: string[]) => Chainable<void>;
      novuAuth: () => Chainable<void>;
      multi_select: (selector: string, text: string[], label: string) => Chainable<void>;
      tableSelection: (selector: string, targetIndex: number[]) => Chainable<void>;
      createTaskGroup: (name: string) => Chainable<void>;
      createTask: (name: string, groupName: string[]) => Chainable<void>;
      createWorkspaceGroup: (name: string) => Chainable<void>;
      createWorkspaceDocument: (groupName: string[]) => Chainable<void>;
    }
  }
}

export { };

