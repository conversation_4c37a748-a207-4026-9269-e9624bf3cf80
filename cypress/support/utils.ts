
type IgetFixturesDoc = {
  numberOfFiles: number,
  type: 'images' | 'pdf'
}

export const getFixturesDoc = (props: IgetFixturesDoc) => {
  const path = `cypress/fixtures/${props.type}`;
  const numberOfFiles = props.numberOfFiles;
  const extension = props.type === 'pdf' ? '.pdf' : '.jpeg'

  const fileArray = Array.from({ length: numberOfFiles }, (_, index) => {
    const fileName = `Testing${index + 1}${extension}`;
    return `${path}/${fileName}`;
  });

  return fileArray;
}

export const transformDueDate = (dueDate: string) => {
  const parts = dueDate.split('/');
  const formattedDueDate = `${parts[2]}-${parts[0]}-${parts[1]}`;
  return formattedDueDate;
}