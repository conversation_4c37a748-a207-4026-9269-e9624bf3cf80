import '../../support/commands';
import { aliasMutation, aliasQuery } from '../../support/graphql';
import { getFixturesDoc } from '../../support/utils';

beforeEach(() => {
  cy.login('<EMAIL>', '1234');
  cy.visit('/drawings/2D-drawing');
});

describe('2d-drawing', () => {
  const folderName = 'test folder';
  const editedFolderName = 'Editing folder using cypress';
  const documents = getFixturesDoc({ numberOfFiles: 2, type: 'pdf' });

  it('should visit 2d-drawing page', () => {
    cy.url().should('include', '/drawings/2D-drawing');
  });

  it('should can create a new folder', () => {
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'getProjectDocuments')).as(
      'getProjectDocuments'
    );

    cy.wait('@getProjectDocuments', { timeout: 10000 }).then(({ response }) => {
      expect(response?.statusCode).to.eq(200);

      cy.get('button[type="button"]').contains('Add New').should('be.visible').click();
      cy.contains('Add Folder').should('be.visible').click();

      cy.get('div[class="ant-modal"]')
        .should('be.visible')
        .then(() => {
          cy.get('input[id="name"]').type(folderName);
          cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req =>
            aliasMutation(req, 'createOneProjectDocument')
          ).as('createOneProjectDocument');

          cy.get('button[type="submit"]').contains('Create').click();
          cy.wait('@createOneProjectDocument', { timeout: 10000 }).then(({ response }) => {
            expect(response?.statusCode).to.eq(200);
            cy.get('div[id="name"]').should('have.text', folderName);
            cy.end();
          });
        });
    });
  });

  it('should can upload a new file', () => {
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'getProjectDocuments')).as(
      'getProjectDocuments'
    );

    cy.wait('@getProjectDocuments', { timeout: 10000 }).then(({ response }) => {
      expect(response?.statusCode).to.eq(200);

      cy.get('button[type="button"]').contains('Add New').should('be.visible').click();
      cy.contains('Upload Files').should('be.visible').click();

      cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'createOneProjectDocument')).as(
        'createOneProjectDocument'
      );
      cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'getProjectDocuments')).as(
        'getProjectDocuments'
      );

      cy.uploadAttachment('upload-document', documents);

      cy.wait('@createOneProjectDocument', { timeout: 600000 }).then(createOneProjectDocument => {
        expect(createOneProjectDocument.response?.statusCode).to.eq(200);
        cy.wait(3000);
        cy.contains('Dismiss').click();
        cy.wait(3000);
        
        cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'getProjectDocuments')).as(
          'getProjectDocuments'
        );
        cy.get('div[id="name"]').first().should('have.text', folderName).click();

        cy.wait('@getProjectDocuments', { timeout: 10000 }).then(({ response }) => {
          expect(response?.statusCode).to.eq(200);
          cy.wait(5000);

          cy.get('button[type="button"]').contains('Add New').should('be.visible').click();
          cy.contains('Upload Files').should('be.visible').click();
  
          cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req =>
            aliasMutation(req, 'createOneProjectDocument')
          ).as('createOneProjectDocument');
          cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'getProjectDocuments')).as(
            'getProjectDocuments'
          );
  
          cy.uploadAttachment('upload-document', documents);
  
          cy.wait('@createOneProjectDocument', { timeout: 600000 }).then(createOneProjectDocument => {
            expect(createOneProjectDocument.response?.statusCode).to.eq(200);

            // get the name, the first should have text 'Testing1.pdf' and the second should have text 'Testing2.pdf'
            cy.get('div[id="name"]').first().should('have.text', 'Testing1.pdf');
            cy.get('div[id="name"]').last().should('have.text', 'Testing2.pdf');
            
            cy.end();
          });
        });
      });
    });
  });

  it('should can search a folder by name', () => {
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'getProjectDocuments')).as(
      'getProjectDocuments'
    );
    cy.get('input[placeholder="Search"]').clear().type(folderName);
    cy.wait('@getProjectDocuments', { timeout: 10000 }).then(({ response }) => {
      expect(response?.statusCode).to.eq(200);
      cy.get('div[id="name"]').first().should('have.text', folderName);
      cy.end();
    });

    cy.wait(1000);

    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'getProjectDocuments')).as(
      'getProjectDocuments'
    );
    cy.get('input[placeholder="Search"]').clear().type('Testing1.pdf');
    cy.wait('@getProjectDocuments', { timeout: 10000 }).then(({ response }) => {
      expect(response?.statusCode).to.eq(200);
      cy.get('div[id="name"]').first().should('have.text', 'Testing1.pdf');
      cy.end();
    });

    cy.wait(1000);

    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'getProjectDocuments')).as(
      'getProjectDocuments'
    );
    cy.get('input[placeholder="Search"]').clear().type('Testing2.pdf');
    cy.wait('@getProjectDocuments', { timeout: 10000 }).then(({ response }) => {
      expect(response?.statusCode).to.eq(200);
      cy.get('div[id="name"]').first().should('have.text', 'Testing2.pdf');
      cy.end();
    });
  });

  it('should can download a folder', () => {
    cy.intercept('POST', `/api/cloud-docs/project-document/download-zip`).as('downloadFolder')
    cy.dropdown('Download Folder', 'single');
    cy.wait('@downloadFolder').then((downloadFolder) => {
      expect(downloadFolder?.response?.statusCode).to.eq(201)
    });
  })

  it('should can rename a folder', () => {
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'getProjectDocuments')).as('getProjectDocuments');

    cy.wait('@getProjectDocuments', { timeout: 10000 }).then(({ response }) => {
      expect(response?.statusCode).to.eq(200);
      cy.dropdown('Rename', 'single');

      cy.get('input[id="name"]').clear().type(editedFolderName);
      cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'updateOneProjectDocument')).as('updateOneProjectDocument');
      cy.contains('Save').click();

      cy.wait('@updateOneProjectDocument', { timeout: 10000 }).then(({ response }) => {
        expect(response?.statusCode).to.eq(200);
        cy.get('div[id="name"]').first().should('have.text', editedFolderName);
        cy.end();
      });
    });
  }
  );

  it('should can delete a folder', () => {
    //? create a new folder
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'getProjectDocuments')).as('getProjectDocuments');

    cy.wait('@getProjectDocuments', { timeout: 10000 }).then(({ response }) => {
      expect(response?.statusCode).to.eq(200);

      //? create a new test delete folder
      cy.get('button[type="button"]').contains('Add New').should('be.visible').click();
      cy.contains('Add Folder').should('be.visible').click();

      cy.get('div[class="ant-modal"]')
        .should('be.visible')
        .then(() => {
          cy.get('input[id="name"]').type('Deleted folder');
          cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req =>
            aliasMutation(req, 'createOneProjectDocument')
          ).as('createOneProjectDocument');

          cy.get('button[type="submit"]').contains('Create').click();
          cy.wait('@createOneProjectDocument', { timeout: 10000 }).then(({ response }) => {
            expect(response?.statusCode).to.eq(200);
            cy.get('div[id="name"]').contains('Deleted folder').should('exist');
            cy.end();
          });
      });
      
      cy.dropdown('Manage', 'single');
      cy.get('button[type="button"]').contains('Delete').should('be.visible').click();

      cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'deleteProjectDocuments')).as('deleteProjectDocuments');
      cy.get('button[type="button"]').contains('Yes').should('be.visible').click();

      cy.wait('@deleteProjectDocuments', { timeout: 10000 }).then(({ response }) => {
        expect(response?.statusCode).to.eq(200);
        cy.end();
      });
    });
  });

  it('should can cancel delete a folder', () => {
    //? create a new folder
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'getProjectDocuments')).as('getProjectDocuments');

    cy.wait('@getProjectDocuments', { timeout: 10000 }).then(({ response }) => {
      expect(response?.statusCode).to.eq(200);

      cy.dropdown('Manage', 'single');
      cy.get('button[type="button"]').contains('Delete').should('be.visible').click();

      cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'deleteProjectDocuments')).as('deleteProjectDocuments');
      cy.get('button[type="button"]').contains('No').should('be.visible').click();
    });
  });

  it('should can move files to folder', () => { 
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'getProjectDocuments')).as('getProjectDocuments');

    cy.wait('@getProjectDocuments', { timeout: 10000 }).then(({ response }) => {
      expect(response?.statusCode).to.eq(200);

      cy.dropdown('Manage', 'multiple', 1);
      cy.tableSelection('2d-drawing', [2]);

      cy.get('button[type="button"]').contains('Move').should('be.visible').click();
      cy.get('div[class="ant-modal scrollbar"]').should('be.visible');

      cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'updateProjectDocumentParent')).as('updateProjectDocumentParent');
      cy.get('div[id="move-file"]').contains(editedFolderName).click();

      cy.wait(3000);

      cy.get('button[id="move-here"]').click({ force: true });

      cy.wait('@updateProjectDocumentParent', { timeout: 10000 }).then(({ response }) => {
        expect(response?.statusCode).to.eq(200);
        cy.end();
      });
    });
  });
});
