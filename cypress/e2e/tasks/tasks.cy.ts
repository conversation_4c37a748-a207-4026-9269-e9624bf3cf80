import '../../support/commands';
import { aliasMutation, aliasQuery } from '../../support/graphql';
import { getFixturesDoc, transformDueDate } from '../../support/utils';

beforeEach(() => {
  cy.login('<EMAIL>', '1234');
  cy.visit('/tasks');
});

describe('task', () => {
  
  it('should visit task page', () => {
    cy.url().should('include', '/tasks');
  }
  );

  it('should create a task', () => {
    cy.contains('Create Task').should('be.visible').click();

    cy.get('div[class="ant-drawer-content-wrapper"]').should('be.visible');
    cy.get('textarea[placeholder="Task Name"]').type('Testing creating task using cypress');

    cy.multi_select('assignees-picker', ['binaUser'], 'Assignee');

    cy.uploadAttachment('upload-attachment', getFixturesDoc({ numberOfFiles: 10, type: 'pdf' }))
    cy.uploadPhotos('upload-media', 'Add photos / videos', getFixturesDoc({ numberOfFiles: 10, type: 'images' }));
    
    cy.contains('Save').click();
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'createTask')).as('createTask');
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'assignTask')).as('assignTask');

    cy.wait(['@createTask', '@assignTask'], { timeout: 10000 }).spread((createTask, assignTask) => {
      expect(createTask.response?.statusCode).to.eq(200);
      expect(assignTask.response?.statusCode).to.eq(200);

      cy.contains('Testing creating task using cypress').should('be.visible');
      cy.end();
    });
  });


  it('status should be change to in progress', () => {
    cy.contains('Testing creating task using cypress').should('be.visible').click();

    cy.get('div[class="ant-drawer-content-wrapper"]').should('be.visible');
    cy.contains('In Progress').should('be.visible');
  });

  it('should edit a task', () => {
    cy.contains('Testing creating task using cypress').should('be.visible').click();

    cy.get('div[class="ant-drawer-content-wrapper"]').should('be.visible');
    cy.get('textarea[placeholder="Task Name"]').clear().type('Testing editing task using cypress');

    cy.multi_select('assignees-picker', ['binaTester'], 'Assignee');

    cy.get('button[title="Remove file"]').each(button => {
      cy.wrap(button).click({ timeout: 10000 });
    });

    cy.uploadAttachment('upload-attachment', getFixturesDoc({ numberOfFiles: 5, type: 'pdf' }))
    cy.uploadPhotos('upload-media', 'Add photos / videos', getFixturesDoc({ numberOfFiles: 5, type: 'images' }));

    cy.multi_select('cc-picker', ['binaUser'], 'Cc');
    
    cy.contains('Save').click();

    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'updateOneTask')).as('updateOneTask');

    cy.wait('@updateOneTask', { timeout: 1000000 }).then((updateOneTask) => {
      expect?.(updateOneTask?.response?.statusCode).to.eq(200);
      cy.contains('Testing editing task using cypress').should('be.visible');
      cy.end();
    }
    );
  });
  
  it('should delete a task', () => {
    cy.contains('Testing editing task using cypress').should('be.visible')
    cy.dropdown('Delete', 'single')

    cy.get('button[class="ant-btn ant-btn-default ant-btn-dangerous"]').click();

    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'deleteOneTask')).as('deleteOneTask');
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'getTasks')).as('getTasks');
    cy.end()
  });
});

describe('filter task', () => {
  beforeEach(() => {
    cy.contains('Filters').click();
    cy.get('div[class="ant-drawer-content-wrapper"]').should('be.visible');
  });

  it('should filter the task by name', () => {
    const taskName = 'Task 1';
    // Type the task name into the input field
    cy.get('input[id="keyword"]').type(taskName);

    // Click the "Apply Filters" button
    cy.contains('Apply Filters').click({ force: true });

    // Verify the task badge dot is visible
    cy.get('sup.ant-scroll-number.ant-badge-dot').should('be.visible');

    // Verify the task name is visible and its text matches the expected task name
    cy.get('div[id="task-name"]').should('be.visible').should('have.text', taskName);

    cy.contains('Filters').click();
    cy.contains('Clear Filters').click({ force: true });
  });

  it('should filter the task by name', () => {
    const taskId = '1';
    // Type the task id into the input field
    cy.get('input[id="id"]').type(taskId);

    // Click the "Apply Filters" button
    cy.contains('Apply Filters').click({ force: true });

    // Verify the task badge dot is visible
    cy.get('sup.ant-scroll-number.ant-badge-dot').should('be.visible');

    // Verify the task id is visible and its text matches the expected task name
    cy.get('p[id="id"]').first().should('be.visible').should('have.text', `#${taskId}`);

    cy.contains('Filters').click();
    cy.contains('Clear Filters').click({ force: true });
  });

  it('should filter the status and verify all are Open', () => {
    const expectedStatus = 'Open';

    // Select "Open" as the status filter using your custom multiSelect command
    cy.multiSelect('status', expectedStatus, 'Status');

    // Click the "Apply Filters" button
    cy.contains('Apply Filters').click();

    // Get all status elements and verify that each one has the expected status "Open"
    cy.get('p[id="status"]').each(statusElement => {
      cy.wrap(statusElement).should('have.text', expectedStatus);
    });

    cy.contains('Filters').click();
    cy.contains('Clear Filters').click({ force: true });
  });

  it('should filter the due date task', () => {
    cy.get('#due-date')
    .first()
    .invoke('text')
      .then(dueDate => {
        const date = transformDueDate(dueDate);
      cy.get('input[id=dueDate]').type(dueDate, { force: true })
      cy.contains('Apply Filters').click()
    });
  });

  it('should filter the group', () => {
    const expectedGroup = 'Ungroup Tasks';

    // Select "Ungroup Tasks" as the group filter using your custom multiSelect command
    cy.multiSelect('select-group', expectedGroup, 'Group');

    // Click the "Apply Filters" button
    cy.contains('Apply Filters').click();

    // Get all status elements and verify that each one has the expected status "Open"
    cy.get('div[id="group"]').each(statusElement => {
      cy.wrap(statusElement).should('have.text', expectedGroup);
    });

    cy.contains('Filters').click();
    cy.contains('Clear Filters').click({ force: true });
  });

  it('should filter the task assignee', () => {
    const expectedAssignee = 'binaTester';
    cy.multi_select('assignees-picker', [expectedAssignee], 'Assigned To');
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'getTasks')).as('getTasks');

    cy.contains('Apply Filters').click();

    cy.wait('@getTasks', { timeout: 100000 }).then((getTasks) => {
      expect(getTasks?.response?.statusCode).to.eq(200);
    })

    cy.contains('Filters').click();
    cy.contains('Clear Filters').click({ force: true });
  })

  it('should filter the tasks for assignees', () => {
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'getTasks')).as('getTasks');
    cy.get('button[role="switch"]').click({ force: true });

    cy.wait('@getTasks', { timeout: 100000 }).then((getTasks) => {
      expect(getTasks?.response?.statusCode).to.eq(200);
    });
  })
})
