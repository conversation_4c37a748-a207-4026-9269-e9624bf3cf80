import '../../support/commands';
import { aliasMutation, aliasQuery } from '../../support/graphql';

beforeEach(() => {
  cy.login('<EMAIL>', '1234');
  cy.visit('/tasks-overview');
});

describe('task-overview', () => {
  const groupName = 'Creating group using cypress'
  const editedGroupName = 'Editing group using cypress'
  it('should create a group', () => {
    const groupName = 'Creating group using cypress';

    // find the create group button
    cy.contains('Create Group').click()

    // check the input should empty
    cy.get('input[id="title"]').should('not.have.value');

    // check the api call
    cy.intercept('POST', `/api/project-group/create`).as('createWorkspaceGroup')
    // type the input
    cy.get('input[id="title"]').type(groupName);

    // click the create group button
    cy.get('button[type="submit"]').click();
    cy.wait('@createWorkspaceGroup').then((createWorkspaceGroup) => {
      expect?.(createWorkspaceGroup?.response?.statusCode).to.eq(201)
    })
  })

  it('should catch an error when create group with same name', () => {
    const groupName = 'Creating group using cypress';

    // find the create group button
    cy.contains('Create Group').click()

    // check the input should empty
    cy.get('input[id="title"]').should('not.have.value');

    // check the api call
    cy.intercept('POST', '/api/project-group/create').as('createWorkspaceGroup')
    // type the input
    cy.get('input[id="title"]').type(groupName);

    // click the create group button
    cy.get('button[type="submit"]').click();
    cy.wait('@createWorkspaceGroup').then((createWorkspaceGroup) => {
      expect?.(createWorkspaceGroup?.response?.statusCode).to.eq(400)
    })
  })

  it('should search the created group', () => {
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'getProjectGroups')).as('getProjectGroups')
    cy.get('input[id="keyword"]').type(groupName);

    cy.wait('@getProjectGroups').then((getProjectGroups) => {
      expect(getProjectGroups.response?.statusCode).to.eq(200);
      cy.end();
    })
  });

  it('should rename the group', () => {
    cy.dropdown('Rename', 'single');

    cy.get('input[id="title"]').clear().type('Editing group using cypress');
    cy.contains('Update').click();

    cy.contains(editedGroupName).should('exist');
  });

  it('should delete the updated group', () => {
    cy.dropdown('Delete', 'single');
    cy.get('button[class="ant-btn ant-btn-default ant-btn-dangerous"]').click();
    cy.contains(editedGroupName).should('not.exist');

    cy.end()
  })
})