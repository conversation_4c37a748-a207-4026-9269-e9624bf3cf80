import '../../support/commands';
import { aliasMutation, aliasQuery } from '../../support/graphql';

describe('workspace', () => {
  beforeEach(() => {
    cy.login('<EMAIL>', '1234');
    cy.visit('/digital-form');
  });

  it('should visit workspace page', () => {
    cy.url().should('include', '/digital-form/overview');
    cy.end();
  });

  it('should search on overview', () => {
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'getWorkspaceDocument')).as('getWorkspaceDocument');
    cy.get('input[id="keyword"]').type('Site Diary');
    cy.wait('@getWorkspaceDocument').then(({ response }) => {
      expect(response?.statusCode).to.eq(200)
    });
    cy.end();
  });

  it('should create a workspace group', () => {
    cy.get('button[id="create-group"]').should('be.visible').click();
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'createWorkspaceGroup')).as('createWorkspaceGroup');

    cy.get('input[id="name"]').type('test group');
    cy.get('button[type="submit"]').click();

    cy.wait('@createWorkspaceGroup', { timeout: 10000 }).then((createWorkspaceGroup) => {
      expect(createWorkspaceGroup?.response?.statusCode).to.eq(200);
    });
  });
                                                                                                                                                                                                                    
  it('should check if group with same name gives error', () => {
    cy.get('button[id="create-group"]').should('be.visible').click();
    cy.get('input[id="name"]').type('test group');
    
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'createWorkspaceGroup')).as('createWorkspaceGroup');
    cy.get('button[type="submit"]').click();

    cy.wait('@createWorkspaceGroup', { timeout: 10000 }).then((createWorkspaceGroup) => {
      expect(createWorkspaceGroup?.response?.statusCode).to.eq(400);
    });
  });

  it('should able to edit a workspace group name', () => {
    cy.get('a[class="ant-dropdown-trigger mb-3"]').first().click();
    cy.get('span[class="ant-dropdown-menu-title-content"]').contains('Rename').click();

    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'updateWorkspaceGroup')).as('updateWorkspaceGroup');
    
    cy.get('input[id="name"]').clear().type('(Renamed) test group');
    cy.get('button[type="submit"]').click();
    
    cy.wait('@updateWorkspaceGroup', { timeout: 10000 }).then((updateWorkspaceGroup) => {
      expect(updateWorkspaceGroup?.response?.statusCode).to.eq(200);
    });
  });

  it('should not be able to rename to a workspace group similar to other group name', () => {
    cy.dropdown('Rename', 'single');
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'updateWorkspaceGroup')).as('updateWorkspaceGroup');

    cy.get('input[id="name"]').clear().type('Site Diary');
    cy.get('button[type="submit"]').click();
    
    cy.wait('@updateWorkspaceGroup', { timeout: 10000 }).then((updateWorkspaceGroup) => {
      expect(updateWorkspaceGroup?.response?.statusCode).to.eq(400);
    });
    
    cy.end();
  });

  it('should able to delete a workspace group', () => {
    cy.get('a[class="ant-dropdown-trigger mb-3"]').first().click();
    cy.get('span[class="ant-dropdown-menu-title-content"]').contains('Delete').click();
    cy.get('button[class="ant-btn ant-btn-default ant-btn-dangerous"]').click();
    cy.get('div[class="ant-message-custom-content ant-message-success"')
      .should('be.visible')
      .contains('Successfully deleted group');
  });
});
