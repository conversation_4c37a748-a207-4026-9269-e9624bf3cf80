import '../../support/commands';
import { aliasMutation, aliasQuery } from '../../support/graphql';
import { getFixturesDoc } from '../../support/utils';

const user1 = 'dev123';
const user2 = 'binaTester';
const user3 = 'binaUser';

describe('workspace draft', () => {
  const documentName = getFixturesDoc({ numberOfFiles: 1, type: 'pdf' });
  const renamedFile = 'Testing Edited.pdf';

  beforeEach(() => {
    cy.login('<EMAIL>', '1234');
    cy.visit('/digital-form/all-form');
    cy.contains('Draft').click();
  });

  it('should visit workspace document page', () => {
    cy.url().should('include', '/digital-form/all-form');
  });

  it('should creating workspace ticket', () => {
    cy.contains('Draft').click();
    cy.contains('Create Document').click();

    cy.get('div[class="ant-modal-content"]').should('be.visible');

    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'createOneProjectDocument')).as('createOneProjectDocument');
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'projectDocuments')).as('projectDocuments');

    cy.uploadAttachment('upload-document', documentName);

    cy.wait(['@createOneProjectDocument', '@projectDocuments']).spread((createOneProjectDocument, projectDocuments) => {
      expect(createOneProjectDocument.response?.statusCode).to.eq(200);
      expect(projectDocuments.response?.statusCode).to.eq(200);
    });
  });

  it('should can rename the file', () => {
      cy.get('div[id="document-name"]').contains('Testing1.pdf').click();
      cy.get('div[class="ant-drawer-content-wrapper"]').should('be.visible');

      cy.get('textarea[id="name"]').clear().type(renamedFile, { force: true });
      cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'updateOneProjectDocument')).as('updateOneProjectDocument');

      cy.get('button[type="submit"]').click({ force: true });
      cy.wait('@updateOneProjectDocument', { responseTimeout: 100000 }).then((updateOneProjectDocument) => {
      expect?.(updateOneProjectDocument?.response?.statusCode).to.eq(200);
    });
  });

  it('should can add document detail', () => {
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'projectDocuments')).as(
      'projectDocuments'
    );
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'projectUsers')).as('projectUsers');

    cy.get('div[id="document-name"]').contains(renamedFile).click();
    cy.get('div[class="ant-drawer-content-wrapper"]').should('be.visible');
    cy.wait('@projectDocuments').then(projectDocuments => {
      expect(projectDocuments?.response?.statusCode).to.eq(200);
      cy.wait(1000);

      cy.wait('@projectUsers').then(projectUsers => {
        expect(projectUsers?.response?.statusCode).to.eq(200);
      });

      //? add description
      cy.get('input[id="description"]').click();
      cy.get('textarea[id="description"]').type('add description using cypress');
      cy.get('button[type="submit"][id="save-description"]').click();

      //? add assignee and ccs
      cy.multi_select('assignees-picker', [user1, user3], 'Assignee');
      // cy.multi_select('cc-picker', [user1, user3], 'Cc');

      //? upload attachment and photos
      cy.uploadAttachment('upload-attachment', getFixturesDoc({ numberOfFiles: 3, type: 'pdf' }));
      cy.uploadPhotos('upload-media', 'Add photos / videos', getFixturesDoc({ numberOfFiles: 3, type: 'images' }));

      cy.get('button[id="linked-to"]').click();
      cy.get('div[class="ant-modal"]').should('be.visible');

      cy.get('div[id="linked-to-cloud-docs"]').click();
      cy.get('div[id="Project Documents"]').click();
      cy.contains('Folder A').click();
      cy.contains('Example PDF 2').click();

      cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'updateOneProjectDocument')).as('updateOneProjectDocument');
      cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'assignAttachmentWorkspace')).as('assignAttachmentWorkspace');
      cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'assignPhotoWorkspace')).as('assignPhotoWorkspace');

      cy.get('button[type="submit"]').contains("Save").click();

      cy.wait(['@updateOneProjectDocument', '@assignAttachmentWorkspace', '@assignPhotoWorkspace']).spread((updateOneProjectDocument, assignAttachmentWorkspace, assignPhotoWorkspace) => {
        expect(updateOneProjectDocument.response?.statusCode).to.eq(200);
        expect(assignAttachmentWorkspace.response?.statusCode).to.eq(200);
        expect(assignPhotoWorkspace.response?.statusCode).to.eq(200);
      });
    });
  });

  it('should request for approval', () => {
    cy.dropdown('Request Approval', 'multiple', 0);

    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'updateOneProjectDocument')).as('updateOneProjectDocument');
    cy.get('button[type="button"]').contains('Request Approval').click()

    cy.wait('@updateOneProjectDocument').then((updateOneProjectDocument) => {
      expect(updateOneProjectDocument?.response?.statusCode).to.eq(200);
    });

    cy.contains('Processed').click();

    cy.get('div[id="document-name"]').first().should('be.visible').should('have.text', renamedFile);
    cy.get('p[id="status"]').first().should('be.visible').should('have.text', 'Submitted');
  });

});

describe('workspace process', () => {
  const renamedFile = 'Testing Edited.pdf';
  const testing2File = 'Testing2.pdf';

  beforeEach(() => {
    cy.login('<EMAIL>', '1234');
    cy.visit('/digital-form/all-form');
  });

  it('should can filter assign by me', () => {
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'projectDocuments')).as('projectDocuments');
    cy.get('button[class="ant-switch"]').click();

    cy.wait('@projectDocuments').then((projectDocuments) => {
      expect(projectDocuments?.response?.statusCode).to.eq(200);
    });

    // cy.get(`img[alt="${user2}"]`).should('be.visible')
  });

  it('should can filter by keyword', () => {
    cy.contains('Filters').click();
    cy.get('div[class="ant-drawer-body"]').should('be.visible');
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'projectDocuments')).as('projectDocuments');

    cy.get('input[id="keyword"]').type(renamedFile);
    cy.get('button[type="submit"]').contains('Apply Filters').click()

    cy.wait('@projectDocuments').then((projectDocuments) => {
      expect(projectDocuments?.response?.statusCode).to.eq(200);
    });

    cy.get('sup.ant-scroll-number.ant-badge-dot').should('be.visible');
    cy.get('div[id="document-name"]').first().should('be.visible').should('have.text', renamedFile);

    cy.contains('Filters').click();
    cy.get('div[class="ant-drawer-body"]').should('be.visible');
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'projectDocuments')).as('projectDocuments');

    cy.get('button').contains('Clear Filters').click();
    cy.wait('@projectDocuments').then((projectDocuments) => {
      expect(projectDocuments?.response?.statusCode).to.eq(200);
    });
  });

  it('should can filter by status', () => {
    const expectedStatus = 'Submitted'

    cy.contains('Filters').click();
    cy.get('div[class="ant-drawer-body"]').should('be.visible');
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'projectDocuments')).as('projectDocuments');

    cy.multiSelect('status', expectedStatus, 'Status');
    cy.get('button[type="submit"]').contains('Apply Filters').click()

    cy.wait('@projectDocuments').then((projectDocuments) => {
      expect(projectDocuments?.response?.statusCode).to.eq(200);
    });

    cy.get('sup.ant-scroll-number.ant-badge-dot').should('be.visible');
    cy.get('p[id="status"]').first().should('be.visible').should('have.text', expectedStatus);

    cy.contains('Filters').click();
    cy.get('div[class="ant-drawer-body"]').should('be.visible');
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'projectDocuments')).as('projectDocuments');

    cy.get('button').contains('Clear Filters').click();
    cy.wait('@projectDocuments').then((projectDocuments) => {
      expect(projectDocuments?.response?.statusCode).to.eq(200);
    });
  });

  it('should can filter by Group', () => {
    const expectedGroup = 'Ungroup Documents';

    cy.contains('Filters').click();
    cy.get('div[class="ant-drawer-body"]').should('be.visible');
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'projectDocuments')).as('projectDocuments');

    cy.multiSelect('workspace-pie-group', expectedGroup, 'Group');
    cy.get('button[type="submit"]').contains('Apply Filters').click()

    cy.wait('@projectDocuments').then((projectDocuments) => {
      expect(projectDocuments?.response?.statusCode).to.eq(200);
    });

    cy.get('sup.ant-scroll-number.ant-badge-dot').should('be.visible');
    cy.get('p[id="group"]').first().should('be.visible').should('have.text', expectedGroup);

    cy.contains('Filters').click();
    cy.get('div[class="ant-drawer-body"]').should('be.visible');
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'projectDocuments')).as('projectDocuments');

    cy.get('button').contains('Clear Filters').click();
    cy.wait('@projectDocuments').then((projectDocuments) => {
      expect(projectDocuments?.response?.statusCode).to.eq(200);
    });
  });

  it('should can filter by Assigned To', () => {
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'projectUsers')).as('projectUsers');

    cy.contains('Filters').click();
    cy.get('div[class="ant-drawer-body"]').should('be.visible');

    cy.wait('@projectUsers').then((projectUsers) => {
      expect(projectUsers?.response?.statusCode).to.eq(200);
      cy.multiSelect('assignees-picker', user1, 'Assigned To');

      cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'projectDocuments')).as('projectDocuments');
      cy.get('button[type="submit"]').contains('Apply Filters').click()

      cy.wait('@projectDocuments').then((projectDocuments) => {
        expect(projectDocuments?.response?.statusCode).to.eq(200);
      });

      cy.get('sup.ant-scroll-number.ant-badge-dot').should('be.visible');
      cy.get(`img[alt="${user1}"]`).should('be.visible')

      cy.contains('Filters').click();
      cy.get('div[class="ant-drawer-body"]').should('be.visible');
      cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'projectDocuments')).as('projectDocuments');

      cy.get('button').contains('Clear Filters').click();
      cy.wait('@projectDocuments').then((projectDocuments) => {
        expect(projectDocuments?.response?.statusCode).to.eq(200);
      });
    });
  });

  it('should request approval and the document is In Review', () => {
    cy.contains('Log out').click();
    cy.url().should('contain', '/login');

    cy.login('<EMAIL>', '1234');
    cy.visit('/digital-form/all-form');

    cy.get('div[id="document-name"]').first().should('be.visible').should('have.text', renamedFile).click();
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'projectDocument')).as('projectDocument');

    cy.get('div[class="ant-drawer-body"]').should('be.visible');
    cy.wait('@projectDocument').then((projectDocument) => {
      expect(projectDocument?.response?.statusCode).to.eq(200);
    });

    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'approveFormOrUpdateStatus')).as('approveFormOrUpdateStatus');
    cy.get('button[type="button"]').contains('In Review').should('exist').click({ force: true});
    cy.wait('@approveFormOrUpdateStatus').then((approveFormOrUpdateStatus) => {
      expect(approveFormOrUpdateStatus?.response?.statusCode).to.eq(200);
    });

    cy.get('button[type="button"]').contains('Approve').should('exist').click({ force: true});
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'approveFormOrUpdateStatus')).as('approveFormOrUpdateStatus');
    cy.get('button[type="button"]').contains('OK').click();

    cy.wait('@approveFormOrUpdateStatus').then((approveFormOrUpdateStatus) => {
      expect(approveFormOrUpdateStatus?.response?.statusCode).to.eq(200);
    });

    cy.get('button[type="button"]').contains('Store').should('exist');
    cy.get('div[id="close-drawer"]').click();

    cy.contains('Log out').click();
    cy.url().should('contain', '/login');

    cy.login('<EMAIL>', '1234');
    cy.visit('/digital-form/all-form');

    cy.get('div[id="document-name"]').first().should('be.visible').should('have.text', renamedFile).click();
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'projectDocument')).as('projectDocument');

    // cy.get('div[class="ant-drawer-body"]').should('be.visible');
    cy.wait('@projectDocument').then((projectDocument) => {
      expect(projectDocument?.response?.statusCode).to.eq(200);
    });

    cy.get('button[type="button"]').contains('Approve').should('exist', { timeout: 100000 }).click({ force: true });
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'approveFormOrUpdateStatus')).as('approveFormOrUpdateStatus');
    cy.get('button[type="button"]').contains('OK').click({ force: true });

    cy.wait('@approveFormOrUpdateStatus').then((approveFormOrUpdateStatus) => {
      expect(approveFormOrUpdateStatus?.response?.statusCode).to.eq(200);
    });

    cy.get('button[type="button"]').contains('Store').should('exist');
    cy.get('div[id="close-drawer"]').click();

    cy.get('p[id="status"]').first().should('be.visible').should('have.text', 'Approved');
  });
});

describe('workspace document reject', () => {
  const documentName = getFixturesDoc({ numberOfFiles: 1, type: 'pdf' });

  beforeEach(() => {
    cy.login('<EMAIL>', '1234');
    cy.visit('/digital-form/all-form');
    cy.contains('Draft').click();
  });

  it('should create a document', () => {
    cy.contains('Create Document').click();

    cy.get('div[class="ant-modal-content"]').should('be.visible');

    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'createOneProjectDocument')).as(
      'createOneProjectDocument'
    );
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'projectDocuments')).as(
      'projectDocuments'
    );

    cy.uploadAttachment('upload-document', documentName);

    cy.wait(['@createOneProjectDocument', '@projectDocuments']).spread((createOneProjectDocument, projectDocuments) => {
      expect(createOneProjectDocument.response?.statusCode).to.eq(200);
      expect(projectDocuments.response?.statusCode).to.eq(200);
    });
  });

  it('should can add document detail', () => {
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'projectDocuments')).as(
      'projectDocuments'
    );
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'projectUsers')).as('projectUsers');

    cy.get('div[id="document-name"]').contains('Testing1.pdf').click();
    cy.get('div[class="ant-drawer-content-wrapper"]').should('be.visible');
    cy.wait('@projectDocuments').then(projectDocuments => {
      expect(projectDocuments?.response?.statusCode).to.eq(200);
      cy.wait(1000);

      cy.wait('@projectUsers').then(projectUsers => {
        expect(projectUsers?.response?.statusCode).to.eq(200);
      });

      //? add assignee and ccs
      cy.multi_select('assignees-picker', [user1, user3], 'Assignee');
      // cy.multi_select('cc-picker', [user1, user3], 'Cc');

      cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'updateOneProjectDocument')).as(
        'updateOneProjectDocument'
      );
      cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req =>
        aliasMutation(req, 'assignAttachmentWorkspace')
      ).as('assignAttachmentWorkspace');
      cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'assignPhotoWorkspace')).as(
        'assignPhotoWorkspace'
      );

      cy.get('button[type="submit"]').contains('Save').click();

      cy.wait(['@updateOneProjectDocument', '@assignAttachmentWorkspace', '@assignPhotoWorkspace']).spread(
        (updateOneProjectDocument, assignAttachmentWorkspace, assignPhotoWorkspace) => {
          expect(updateOneProjectDocument.response?.statusCode).to.eq(200);
          expect(assignAttachmentWorkspace.response?.statusCode).to.eq(200);
          expect(assignPhotoWorkspace.response?.statusCode).to.eq(200);
        }
      );
    });
  });

  it('should request for approval', () => {
    cy.dropdown('Request Approval', 'multiple', 0);

    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'updateOneProjectDocument')).as(
      'updateOneProjectDocument'
    );
    cy.get('button[type="button"]').contains('Request Approval').click();

    cy.wait('@updateOneProjectDocument').then(updateOneProjectDocument => {
      expect(updateOneProjectDocument?.response?.statusCode).to.eq(200);
    });

    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'projectDocuments')).as('projectDocuments');
    cy.contains('Processed').click();

    cy.wait('@projectDocuments').then(projectDocuments => {
      expect(projectDocuments?.response?.statusCode).to.eq(200);
      cy.get('div[id="document-name"]').first().should('be.visible').should('have.text', 'Testing1.pdf');
      cy.get('p[id="status"]').first().should('be.visible').should('have.text', 'Submitted');
    });

    cy.end();
  });

    it('should request approval and the document is Rejected', () => {
    cy.contains('Log out').click();
    cy.url().should('contain', '/login');

    cy.login('<EMAIL>', '1234');
    cy.visit('/digital-form/all-form');

    cy.get('div[id="document-name"]').first().should('be.visible').should('have.text', 'Testing1.pdf').click();
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'projectDocument')).as('projectDocument');

    // cy.get('div[class="ant-drawer-body"]').should('be.visible');
    cy.wait('@projectDocument').then((projectDocument) => {
      expect(projectDocument?.response?.statusCode).to.eq(200);
    });

    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'approveFormOrUpdateStatus')).as('approveFormOrUpdateStatus');
    cy.get('button[type="button"]').contains('In Review').should('exist').click();
    cy.wait('@approveFormOrUpdateStatus').then((approveFormOrUpdateStatus) => {
      expect(approveFormOrUpdateStatus?.response?.statusCode).to.eq(200);
    });

    cy.get('button[type="button"]').contains('Approve').should('exist').click();
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'approveFormOrUpdateStatus')).as('approveFormOrUpdateStatus');
    cy.get('button[type="button"]').contains('OK').click();

    cy.wait('@approveFormOrUpdateStatus').then((approveFormOrUpdateStatus) => {
      expect(approveFormOrUpdateStatus?.response?.statusCode).to.eq(200);
    });

    cy.get('button[type="button"]').contains('Store').should('exist');
    cy.get('div[id="close-drawer"]').click();

    cy.contains('Log out').click();
    cy.url().should('contain', '/login');

    cy.login('<EMAIL>', '1234');
    cy.visit('/digital-form/all-form');

    cy.get('div[id="document-name"]').first().should('be.visible').should('have.text', 'Testing1.pdf').click();
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'projectDocument')).as('projectDocument');

    cy.get('div[class="ant-drawer-body"]').should('be.visible');
    cy.wait('@projectDocument').then((projectDocument) => {
      expect(projectDocument?.response?.statusCode).to.eq(200);
    });

    cy.get('button[type="button"]').contains('Reject',  { timeout: 100000 }).should('exist').click();
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'approveFormOrUpdateStatus')).as('approveFormOrUpdateStatus');
    cy.get('button[type="button"]').contains('OK').click({ force: true });

    cy.wait('@approveFormOrUpdateStatus').then((approveFormOrUpdateStatus) => {
      expect(approveFormOrUpdateStatus?.response?.statusCode).to.eq(200);
    });

    cy.get('button[type="button"]').contains('Store').should('exist');
    cy.get('div[id="close-drawer"]').click({ force: true });

    cy.get('p[id="status"]').first().should('be.visible').should('have.text', 'Rejected');
  });
});

//? workspace document store
describe('workspace document store', () => { 
  const documentName = getFixturesDoc({ numberOfFiles: 1, type: 'pdf' });

  beforeEach(() => {
    cy.login('<EMAIL>', '1234');
    cy.visit('/digital-form/all-form');
  });

  it('should visit workspace document page', () => {
    cy.url().should('include', '/digital-form/all-form');
  });

  it('should can store the document', () => { 
    //? intercept the request for project document
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'projectDocuments')).as('projectDocuments');

    //? wait for the request to finish
    cy.wait('@projectDocuments').then(projectDocuments => {
      expect(projectDocuments?.response?.statusCode).to.eq(200);
      //? intercept the request for project document
      
      // find the first document that status is approved
      cy.get('p[id="status"]').contains('Approved').parent().parent().parent().siblings('td[class="ant-table-cell"]').first().click();
      cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'projectDocument')).as('projectDocument');

      //? wait for the request to finish
      cy.wait('@projectDocument', { timeout: 10000 }).then(projectDocument => {
        cy.wait(5000);
        
        expect(projectDocument?.response?.statusCode).to.eq(200);
        cy.get('div[class="ant-drawer-content-wrapper"]').should('be.visible');
  
        //? find the store button and click it
        cy.get('button[type="button"]').contains('Store').should('exist').click({
          force: true
        });
  
        cy.get('div[class="ant-modal"]').should('be.visible');
  
        cy.get('div[id="storing-cloud-docs"]').first().click();
        cy.get('div[id="Project Documents"]').click();
        cy.contains('Folder A').click();
  
        cy.get('div[class="ant-modal-body"]').find('button[type="button"]').contains('Store').click();

        cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'storeProjectDocument')).as('storeProjectDocument');
        cy.get('button[type="button"]').contains('OK').click({ force: true });

        cy.wait('@storeProjectDocument').then((storeProjectDocument) => {
          expect(storeProjectDocument?.response?.statusCode).to.eq(200);
        })
      });
    });
  });
});
