import '../support/commands';
import { aliasMutation, aliasQuery } from '../support/graphql';

beforeEach(() => {
  cy.login('<EMAIL>', '1234');
  cy.visit('/projects/dashboard');
});

describe('Dashboard Graph', () => {

  it('should can upload s-curve graph', () => {
    cy.get('div[class="absolute overflow-auto w-full h-full"]').scrollTo('top');

    const graphPath = 'cypress/fixtures/s_curve.xlsx';

      cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'createOneProjectDocument')).as(
        'createOneProjectDocument'
      );

      cy.get('a[class="ant-dropdown-trigger mb-3"]').should('be.visible');

      cy.dropdown('Upload File', 'single');
      cy.uploadAttachment('upload-graph', [graphPath]);

    cy.wait('@createOneProjectDocument').then(createOneProjectDocument => {
        expect(createOneProjectDocument?.response?.statusCode).to.eq(200);
      });
  });

  it('should can export s-curve graph', () => {
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'projectDocuments')).as('projectDocuments');

    cy.get('div[class="absolute overflow-auto w-full h-full"]').scrollTo('top');

    cy.wait('@projectDocuments').then(projectDocuments => {
      expect(projectDocuments.response?.statusCode).to.eq(200);

      cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'createOneProjectDocument')).as(
        'createOneProjectDocument'
      );

      cy.get('a[class="ant-dropdown-trigger mb-3"]').should('be.visible');

      cy.dropdown('Export Excel', 'single');
    });
  });

  it('should can download sample s-curve graph', () => {
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'projectDocuments')).as('projectDocuments');

    cy.get('div[class="absolute overflow-auto w-full h-full"]').scrollTo('top');

    cy.wait('@projectDocuments').then(projectDocuments => {
      expect(projectDocuments.response?.statusCode).to.eq(200);

      cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'createOneProjectDocument')).as(
        'createOneProjectDocument'
      );

      cy.get('a[class="ant-dropdown-trigger mb-3"]').should('be.visible');

      cy.dropdown('Download Sample', 'single');
    });
  });
});

describe('Task & Workspace Pie Chart', () => {

  it('should display task & workspace pie chart', () => {
    cy.intercept('POST', '/api/dashboard/overview').as('overview');
    cy.wait('@overview').then(overview => {
      expect(overview.response?.statusCode).to.eq(201);
      expect(overview.response?.body).to.have.property('tasks').and.to.have.property('countOfOpenTasks').eq(2);
      expect(overview.response?.body).to.have.property('workspace').and.to.have.property('submittedWorkspaceTasks').eq(2);

      cy.get('div[id="tasks-pie-container"]').find('div[class="g2-html-annotation"]').last().should('be.visible').should('have.text', 2);
      cy.get('div[id="workspace-pie-container"]').find('div[class="g2-html-annotation"]').last().should('be.visible').should('have.text', 2);
    });
  }
  );

  it('tasks pie chart should be clickable', () => {
    cy.intercept('POST', '/api/dashboard/overview').as('overview');
    cy.wait('@overview').then(overview => {
      expect(overview.response?.statusCode).to.eq(201);
      expect(overview.response?.body).to.have.property('tasks').and.to.have.property('countOfOpenTasks').eq(2);
      expect(overview.response?.body).to.have.property('workspace').and.to.have.property('submittedWorkspaceTasks').eq(2);

      cy.get('div[class="ant-col mx-auto mb-10"]').first().click();
      cy.url().should('include', '/tasks-overview');
    });
  });

  it('workspace pie chart should be clickable', () => {
    cy.intercept('POST', '/api/dashboard/overview').as('overview');
    cy.wait('@overview').then(overview => {
      expect(overview.response?.statusCode).to.eq(201);
      expect(overview.response?.body).to.have.property('tasks').and.to.have.property('countOfOpenTasks').eq(2);
      expect(overview.response?.body).to.have.property('workspace').and.to.have.property('submittedWorkspaceTasks').eq(2);

      cy.get('div[class="ant-col mx-auto mb-10"]').last().click();
      cy.url().should('include', '/digital-form/overview');
    });
  });

  it('should increase the number of tasks', () => {
    cy.intercept('POST', '/api/dashboard/overview').as('overview');
    cy.wait('@overview').then(overview => {
      expect(overview.response?.statusCode).to.eq(201);

      cy.createTaskGroup('test task group');
      cy.createTask('Testing creating task using cypress', ['test task group']);

      cy.intercept('POST', '/api/dashboard/overview').as('overview');
      cy.visit('/projects/dashboard');
      cy.wait('@overview').then(overview => {
        expect(overview.response?.statusCode).to.eq(201);

        cy.get('div[id="tasks-pie-container"]')
          .find('div[class="g2-html-annotation"]')
          .last()
          .should('be.visible')
          .should('have.text', 3);
      });
    });
  });

  it('should increase the number of workspace', () => {
    cy.intercept('POST', '/api/dashboard/overview').as('overview');
    cy.wait('@overview').then(overview => {
      expect(overview.response?.statusCode).to.eq(201);

      cy.createWorkspaceGroup('test workspace group');
      cy.createWorkspaceDocument(['test workspace group']);

      cy.intercept('POST', '/api/dashboard/overview').as('overview');
      cy.visit('/projects/dashboard');

      cy.wait('@overview').then(overview => {
        expect(overview.response?.statusCode).to.eq(201);

        cy.get('div[id="workspace-pie-container"]')
          .find('div[class="g2-html-annotation"]')
          .last()
          .should('be.visible')
          .should('have.text', 3);
      }
      );
    });
  });

  it('should filter the task pie chart', () => {
    cy.intercept('POST', '/api/dashboard/overview').as('overview');
    cy.wait('@overview').then(overview => {
      expect(overview.response?.statusCode).to.eq(201);

      cy.get('div[id="task-pie-filter"]').should('be.visible').click();

      cy.intercept('POST', '/api/dashboard/overview').as('overview');
      cy.multi_select('group-picker', ['Ungroup Tasks'], 'TASKS');
      cy.wait('@overview').then(overview => {
        expect(overview.response?.statusCode).to.eq(201);

        cy.get('div[id="tasks-pie-container"]').find('div[class="g2-html-annotation"]').last().should('be.visible').should('have.text', 2);
      });

      cy.wait(1000);

      cy.get('div[id="task-pie-filter"]').should('be.visible').click();
      cy.intercept('POST', '/api/dashboard/overview').as('overview');
      cy.multi_select('group-picker', ['test task group'], 'TASKS');
      cy.wait('@overview').then(overview => {
        expect(overview.response?.statusCode).to.eq(201);

        cy.get('div[id="tasks-pie-container"]').find('div[class="g2-html-annotation"]').last().should('be.visible').should('have.text', 1);
      });
    });
  });

  it('should filter the workspace pie chart', () => {
    cy.intercept('POST', '/api/dashboard/overview').as('overview');
    cy.wait('@overview').then(overview => {
      expect(overview.response?.statusCode).to.eq(201);

      cy.get('div[id="workspace-pie-filter"]').should('be.visible').click();

      cy.intercept('POST', '/api/dashboard/overview').as('overview');
      cy.multi_select('workspace-pie-group', ['Ungroup Documents'], 'WORKSPACE');
      cy.wait('@overview').then(overview => {
        expect(overview.response?.statusCode).to.eq(201);

        cy.get('div[id="workspace-pie-container"]').find('div[class="g2-html-annotation"]').last().should('be.visible').should('have.text', 2);
      });

      cy.wait(1000);

      cy.get('div[id="workspace-pie-filter"]').should('be.visible').click();
      cy.intercept('POST', '/api/dashboard/overview').as('overview');
      cy.multi_select('workspace-pie-group', ['test workspace group'], 'WORKSPACE');
      cy.wait('@overview').then(overview => {
        expect(overview.response?.statusCode).to.eq(201);

        cy.get('div[id="workspace-pie-container"]').find('div[class="g2-html-annotation"]').last().should('be.visible').should('have.text', 1);
      });
    });
  });
});

describe('Weather Forecast', () => {
  it('should can add address', () => {
    cy.contains('Add Address').should('be.visible').click();

    cy.get('div[class="ant-modal-content"]').should('be.visible');
    cy.multi_select('weather-address-input', ['MUAR, JOHOR'], 'Add worksite address to this project');

    cy.intercept('GET', 'https://api.met.gov.my/v2.1/data?datasetid=FORECAST&datacategoryid=GENERAL&locationid=LOCATION:127&start_date=2023-09-05&end_date=2023-09-05').as('getWeatherForecast');
    cy.get('button[type="submit"]').should('be.visible').click();
  });
});

describe('Calendar', () => {
  it('should can add event', () => {
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'project')).as(
      'project'
    );
    cy.intercept('POST', '/api/dashboard/tasks-events-list').as('events');

    cy.wait(['@project', '@events']).spread((project, events) => {
      expect(project.response?.statusCode).to.eq(200);
      expect(events.response?.statusCode).to.eq(201);
      cy.contains('Manage Event').should('be.visible').click({ force: true});

      cy.get('div[class="ant-modal-content"]').should('be.visible');

      cy.get('input[placeholder="Event Title"]').should('be.visible').type('test event');

      cy.get('input[placeholder="Start date"]').should('be.visible').click();
      cy.get('div[class="ant-picker-panel-container"]').should('be.visible').find('div[class="ant-picker-cell-inner"]').each(($el, index) => {
        if ($el.text() === '15') {
          cy.wrap($el).click();
          cy.contains('OK').should('be.visible').click();
        }
      }
      );

      cy.get('input[placeholder="End date"]').click();
      cy.get('div[class="ant-picker-panel-container"]').should('be.visible').find('div[class="ant-picker-cell-inner"]').each(($el, index) => {
        if ($el.text() === '15') {
          cy.wrap($el).click();
          cy.contains('OK').should('be.visible').click();
        }
      }
      );

      cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'createEvent')).as('createEvent');
      cy.get('button[type="submit"]').contains('Save Event').should('be.visible').click();

      cy.wait('@createEvent').then(createEvent => {
        expect(createEvent.response?.statusCode).to.eq(200);
      }
      );
    }
    );
  });

  it('should display created event', () => {
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'project')).as(
      'project'
    );
    cy.intercept('POST', '/api/dashboard/tasks-events-list').as('events');

    cy.wait(['@project', '@events']).spread((project, events) => {
      expect(project.response?.statusCode).to.eq(200);
      expect(events.response?.statusCode).to.eq(201);
      cy.contains('Manage Event').should('be.visible').click({ force: true });

      cy.get('div[class="ant-modal-content"]').should('be.visible');

      cy.get('p[id="event-title"]').should('be.visible').each(($el, index) => {
        if ($el.text() === 'test event') {
          cy.wrap($el).should('be.visible');
        }
      }
      );
    }
    );
  });

  it('should can delete event', () => {
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'project')).as(
      'project'
    );
    cy.intercept('POST', '/api/dashboard/tasks-events-list').as('events');
    cy.intercept('GET', '/api/auth/user/is-first-time-sign-in').as('isFirstTimeSignIn');

    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'deleteOneEvent')).as('deleteEvent');

    cy.wait(['@project', '@events', '@isFirstTimeSignIn']).spread((project, events) => {
      expect(project.response?.statusCode).to.eq(200);
      expect(events.response?.statusCode).to.eq(201);
      cy.contains('Manage Event').should('be.visible').click({ force: true });

      cy.get('div[class="ant-modal-content"]').should('be.visible');

      cy.dropdown('Delete', 'multiple', 1);

      cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'deleteOneEvent')).as('deleteEvent');
      cy.get('button[type="button"]').contains('Yes').should('be.visible').click();

      cy.wait('@deleteEvent').then(deleteEvent => {
        expect(deleteEvent.response?.statusCode).to.eq(200);
      }
      );
    }
    );
  });

  it('should can create a event with all day and schedule for all users', () => {
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'project')).as('project');
    cy.intercept('POST', '/api/dashboard/tasks-events-list').as('events');
    cy.intercept('GET', '/api/auth/user/is-first-time-sign-in').as('isFirstTimeSignIn');

    cy.wait(['@project', '@events', '@isFirstTimeSignIn']).spread((project, events) => {
      expect(project.response?.statusCode).to.eq(200);
      expect(events.response?.statusCode).to.eq(201);
      cy.contains('Manage Event').should('be.visible').click({ force: true });

      cy.get('div[class="ant-modal-content"]').should('be.visible');

      cy.get('input[placeholder="Event Title"]').should('be.visible').type('test event for all users');

      cy.get('input[placeholder="Start date"]').should('be.visible').click();
      cy.get('div[class="ant-picker-panel-container"]').should('be.visible').find('div[class="ant-picker-cell-inner"]').each(($el, index) => {
        if ($el.text() === '15') {
          cy.wrap($el).click();
          cy.contains('OK').should('be.visible').click();
        }
      }
      );

      cy.get('div[class="ant-picker-panel-container"]').should('be.visible').find('div[class="ant-picker-cell-inner"]').each(($el, index) => {
        if ($el.text() === '15') {
          cy.wrap($el).click();
          cy.contains('OK').should('be.visible').click();
        }
      }
      );

      cy.get('button[id="all-day-switch"]').should('be.visible').click();
      cy.get('button[id="schedule-for-all-switch"]').should('be.visible').click();

      cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'createEvent')).as('createEvent');
      cy.get('button[type="submit"]').contains('Save Event').should('be.visible').click();

      cy.wait('@createEvent').then(createEvent => {
        expect(createEvent.response?.statusCode).to.eq(200);
      }
      );

      cy.end();
    }
    );
  });

  it('should can see created event with all day and schedule for all users', () => {
    cy.contains('Log out').click();
    cy.url().should('contain', '/login');

    cy.login('<EMAIL>', '1234');

    cy.visit('/projects/dashboard');

    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'project')).as(
      'project'
    );
    cy.intercept('POST', '/api/dashboard/tasks-events-list').as('events');
    cy.intercept('GET', '/api/auth/user/is-first-time-sign-in').as('isFirstTimeSignIn');

    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'deleteOneEvent')).as('deleteEvent');

    cy.wait(['@project', '@events', '@isFirstTimeSignIn']).spread((project, events, isFirstTimeSignIn) => {
      expect(project.response?.statusCode).to.eq(200);
      expect(events.response?.statusCode).to.eq(201);

      cy.contains('Manage Event').should('be.visible').click({ force: true });

      cy.get('div[class="ant-modal-content"]').should('be.visible');

      cy.get('p[id="event-title"]').should('be.visible').each(($el, index) => {
        if ($el.text() === 'test event for all users') {
          cy.wrap($el).should('be.visible');
        }
      }
      );
    }
    );
  });

  it('should can delete event with all day and schedule for all users', () => {
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'project')).as('project');
    cy.intercept('POST', '/api/dashboard/tasks-events-list').as('events');
    cy.intercept('GET', '/api/auth/user/is-first-time-sign-in').as('isFirstTimeSignIn');

    cy.wait(['@project', '@events', '@isFirstTimeSignIn']).spread((project, events) => {
      expect(project.response?.statusCode).to.eq(200);
      expect(events.response?.statusCode).to.eq(201);

      cy.contains('Manage Event').should('be.visible').click({ force: true });
      cy.get('div[class="ant-modal-content"]').should('be.visible');
      cy.dropdown('Delete', 'multiple', 1);

      cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'deleteOneEvent')).as(
        'deleteEvent'
      );
      cy.get('button[type="button"]').contains('Yes').should('be.visible').click();

      cy.wait('@deleteEvent').then(deleteEvent => {
        expect(deleteEvent.response?.statusCode).to.eq(200);
        cy.contains('Log out').click({ force: true });
        cy.url().should('contain', '/login');
        cy.login('<EMAIL>', '1234');
  
        cy.visit('/projects/dashboard');
  
        cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'project')).as('project');
        cy.intercept('POST', '/api/dashboard/tasks-events-list').as('events');
        cy.intercept('GET', '/api/auth/user/is-first-time-sign-in').as('isFirstTimeSignIn');
  
        cy.wait(['@project', '@events', '@isFirstTimeSignIn']).spread((project, events) => {
          expect(project.response?.statusCode).to.eq(200);
          expect(events.response?.statusCode).to.eq(201);
  
          cy.get('button[type="button"]').contains('Manage Event').should('be.visible').click({ force: true });
          cy.get('div[class="ant-modal-content"]').should('be.visible');
          cy.get('p[id="event-title"]').each(($el, index) => {
            cy.wait(1000);
            if ($el.text() === 'test event for all users') {
              cy.wrap($el).should('not.be.visible');
            }
          }
          );
  
          cy.end();
        });
      });
    });
  });
});

describe('Site Diary', () => {
  it('should can create see site diary', () => { 
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'project')).as('project');
    cy.intercept('POST', '/api/dashboard/tasks-events-list').as('events');
    cy.intercept('GET', '/api/auth/user/is-first-time-sign-in').as('isFirstTimeSignIn');

    cy.wait(['@project', '@events', '@isFirstTimeSignIn']).spread((project, events) => {
      expect(project.response?.statusCode).to.eq(200);
      expect(events.response?.statusCode).to.eq(201);

      cy.createWorkspaceDocument(['Site Diary']);

      cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'projectDocuments')).as('projectDocuments');
      cy.visit('/projects/dashboard');

      cy.wait('@projectDocuments').then(projectDocuments => {
        expect(projectDocuments.response?.statusCode).to.eq(200);

        cy.get('p[id="site-diary-name"]').should('be.visible')
      }
      );
    });
  });
})
