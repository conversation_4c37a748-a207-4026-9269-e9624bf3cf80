import '../../support/commands';
import { aliasMutation, aliasQuery } from '../../support/graphql';
import { getFixturesDoc } from '../../support/utils';

beforeEach(() => {
  cy.login('<EMAIL>', '1234');
  cy.visit('/photos');
});

describe('Photos Recent', () => {
  const photos = getFixturesDoc({ numberOfFiles: 5, type: 'images' });

  it('should visit the photos recent page', () => {
    cy.url().should('include', '/photos');
  });

  it('should can create a folder', () => {
    //? wait for the page to load
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'getProjectDocuments')).as(
      'getProjectDocuments'
    );

    cy.wait('@getProjectDocuments', { timeout: 10000 }).then(({ response }) => {
      expect(response?.statusCode).to.eq(200);

      cy.get('button[type="button"]').contains('Add New').should('be.visible').click();
      cy.contains('Add Folder').should('be.visible').click();

      cy.get('input[id="name"]').clear().type('Testing Folder');

      cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'createOneProjectDocument')).as(
        'createOneProjectDocument'
      );
      cy.get('button[type="submit"]').contains('Create').should('be.visible').click();

      cy.wait('@createOneProjectDocument', { timeout: 10000 }).then(({ response }) => {
        expect(response?.statusCode).to.eq(200);
      });
    });
  });

  it('should can upload files on root', () => {
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'getProjectDocuments')).as(
      'getProjectDocuments'
    );

    cy.wait('@getProjectDocuments', { timeout: 10000 }).then(({ response }) => {
      expect(response?.statusCode).to.eq(200);

      cy.get('button[type="button"]').contains('Add New').should('be.visible').click();
      cy.contains('Upload Files').should('be.visible').click();

      cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'createOneProjectDocument')).as(
        'createOneProjectDocument'
      );
      cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'getProjectDocuments')).as(
        'getProjectDocuments'
      );

      cy.uploadAttachment('upload-document', photos);

      cy.wait('@createOneProjectDocument', { timeout: 10000 }).then(({ response }) => {
        expect(response?.statusCode).to.eq(200);
      });

      cy.get('div[class="ant-card-meta-title"]').should('have.length', 5);
      cy.get('button[type="button"]').contains('Dismiss').should('be.visible').click();
    });
  });

  it('should can upload files inside folder', () => {
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'getProjectDocuments')).as(
      'getProjectDocuments'
    );

    cy.wait('@getProjectDocuments', { timeout: 10000 }).then(({ response }) => {
      expect(response?.statusCode).to.eq(200);

      cy.contains('Testing Folder').should('be.visible').click();

      cy.wait(3000);

      cy.get('div[class="absolute w-full h-full py-5 px-5 overflow-x-hidden"]').scrollTo('top');

      cy.get('button[type="button"]').contains('Add New').should('be.visible').click();
      cy.contains('Upload Files').should('be.visible').click();

      cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'createOneProjectDocument')).as(
        'createOneProjectDocument'
      );
      cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'getProjectDocuments')).as(
        'getProjectDocuments'
      );

      cy.uploadAttachment('upload-document', photos);

      cy.wait('@createOneProjectDocument', { timeout: 10000 }).then(({ response }) => {
        expect(response?.statusCode).to.eq(200);
      });

      cy.get('div[class="ant-card-meta-title"]').should('have.length', 5);
      cy.get('button[type="button"]').contains('Dismiss').should('be.visible').click();
    });
  });

  it('should can search by folder name', () => {
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'getProjectDocuments')).as(
      'getProjectDocuments'
    );

    cy.wait('@getProjectDocuments', { timeout: 10000 }).then(({ response }) => {
      expect(response?.statusCode).to.eq(200);

      cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'getProjectDocuments')).as(
        'getProjectDocuments'
      );

      cy.get('input[placeholder="Search by name"]').clear().type('Testing Folder');

      cy.wait('@getProjectDocuments', { timeout: 10000 }).then(({ response }) => {
        expect(response?.statusCode).to.eq(200);
        cy.get('p[class="font-medium twoLineEllipsis"]').should('have.length', 1).should('contain', 'Testing Folder');

        cy.wait(1000);
      });
    });
  });

  it('should can search by photo name', () => {
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'getProjectDocuments')).as(
      'getProjectDocuments'
    );

    cy.wait('@getProjectDocuments', { timeout: 10000 }).then(({ response }) => {
      expect(response?.statusCode).to.eq(200);

      photos.forEach(photo => {
        const name = photo?.slice(photo.lastIndexOf('/') + 1, photo.lastIndexOf('.'));

        cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'getProjectDocuments')).as(
          'getProjectDocuments'
        );

        cy.get('input[placeholder="Search by name"]').clear().type(name);

        cy.wait('@getProjectDocuments', { timeout: 10000 }).then(({ response }) => {
          expect(response?.statusCode).to.eq(200);
          cy.get('div[class="ant-card-meta-title"]').should('have.length', 2).should('contain', name);

          cy.wait(1000);
        });
      });
    });
  });

  it('should can rename a folder', () => {
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'getProjectDocuments')).as(
      'getProjectDocuments'
    );

    cy.wait('@getProjectDocuments', { timeout: 10000 }).then(({ response }) => {
      expect(response?.statusCode).to.eq(200);

      cy.dropdown('Rename', 'single');
      cy.get('input[id="name"]').clear().type('Testing Edit Folder Name');

      cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'updateOneProjectDocument')).as(
        'updateOneProjectDocument'
      );

      cy.get('button[type="submit"]').contains('Save').should('be.visible').click();

      cy.wait('@updateOneProjectDocument', { timeout: 10000 }).then(({ response }) => {
        expect(response?.statusCode).to.eq(200);
      });
    });
  });

  it('should can rename a photo on root', () => {
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'getProjectDocuments')).as(
      'getProjectDocuments'
    );

    cy.wait('@getProjectDocuments', { timeout: 10000 }).then(({ response }) => {
      expect(response?.statusCode).to.eq(200);

      cy.dropdown('Rename', 'multiple', 1);
      cy.get('input[id="name"]').clear().type('Testing Edit');

      cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'updateOneProjectDocument')).as(
        'updateOneProjectDocument'
      );

      cy.get('button[type="submit"]').contains('Save').should('be.visible').click();

      cy.wait('@updateOneProjectDocument', { timeout: 10000 }).then(({ response }) => {
        expect(response?.statusCode).to.eq(200);
      });
    });
  });
  
    it('should can rename a photo inside folder', () => {
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'getProjectDocuments')).as(
      'getProjectDocuments'
    );

    cy.wait('@getProjectDocuments', { timeout: 10000 }).then(({ response }) => {
      expect(response?.statusCode).to.eq(200);

      cy.contains('Testing Edit Folder Name').should('be.visible').click();
      cy.wait(3000);

      cy.dropdown('Rename', 'multiple', 0);
      cy.get('input[id="name"]').clear().type('Testing Edit Inside Folder');

      cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'updateOneProjectDocument')).as(
        'updateOneProjectDocument'
      );

      cy.get('button[type="submit"]').contains('Save').should('be.visible').click();

      cy.wait('@updateOneProjectDocument', { timeout: 10000 }).then(({ response }) => {
        expect(response?.statusCode).to.eq(200);
      });
    });
  });

  it('should can download a folder', () => {
    //? wait for the page to load
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'getProjectDocuments')).as(
      'getProjectDocuments'
    );
    
    cy.wait('@getProjectDocuments', { timeout: 10000 }).then(({ response }) => {
      expect(response?.statusCode).to.eq(200);
      
      cy.dropdown('Download', 'single');
      
      cy.intercept('OPTIONS', `/api/cloud-docs/project-document/download-bulk-zip`).as('downloadFolder');
      // cy.wait('@downloadFolder', { timeout: 10000 }).then(({ response }) => {
      //   expect(response?.statusCode).to.eq(201);
      // });
    }
    )
  });

    it('should can download a folder', () => {
    //? wait for the page to load
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'getProjectDocuments')).as(
      'getProjectDocuments'
    );
    
    cy.wait('@getProjectDocuments', { timeout: 10000 }).then(({ response }) => {
      expect(response?.statusCode).to.eq(200);
      
      cy.dropdown('Download', 'single');
      
      cy.intercept('OPTIONS', `/api/cloud-docs/project-document/download-bulk-zip`).as('downloadFolder');
      // cy.wait('@downloadFolder', { timeout: 10000 }).then(({ response }) => {
      //   expect(response?.statusCode).to.eq(201);
      // });
    }
    )
    });
  
  it('should can download multiple photos', () => {
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'getProjectDocuments')).as(
      'getProjectDocuments'
    );

    cy.wait('@getProjectDocuments', { timeout: 10000 }).then(({ response }) => {
      expect(response?.statusCode).to.eq(200);

      cy.intercept('POST', `/api/cloud-docs/project-document/download-bulk-zip`).as('downloadFolder');
      cy.dropdown('Manage', 'multiple', 1);

      //? scroll to top
      cy.get('div[class="absolute w-full h-full py-5 px-5 overflow-x-hidden"]').scrollTo('top');

      cy.get('input[type="checkbox"]').each((checkbox, index) => {
        const targetIndex = [2, 3, 4, 5];

        if (targetIndex.includes(index)) {
          cy.wrap(checkbox).click({ force: true });
        }
      });

      cy.get('button[type="button"]').contains('Download').should('be.visible').click();

      cy.wait('@downloadFolder', { timeout: 10000 }).then(({ response }) => {
        expect(response?.statusCode).to.eq(201);
      });
    });
  });

  it('should can download multiple photos inside folder', () => {
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'getProjectDocuments')).as(
      'getProjectDocuments'
    );

    cy.wait('@getProjectDocuments', { timeout: 10000 }).then(({ response }) => {
      expect(response?.statusCode).to.eq(200);

      cy.contains('Testing Edit Folder Name').should('be.visible').click();
      cy.wait(3000);

      cy.intercept('POST', `/api/cloud-docs/project-document/download-bulk-zip`).as('downloadFolder');
      cy.dropdown('Manage', 'single');

      cy.get('input[type="checkbox"]').each((checkbox, index) => {
        const targetIndex = [1, 2, 3, 4];

        if (targetIndex.includes(index)) {
          cy.wrap(checkbox).click({ force: true });
        }
      });

      //? scroll to top
      cy.get('div[class="absolute w-full h-full py-5 px-5 overflow-x-hidden"]').scrollTo('top');

      cy.get('button[type="button"]').contains('Download').should('be.visible').click({ force: true });

      cy.wait('@downloadFolder', { timeout: 10000 }).then(({ response }) => {
        expect(response?.statusCode).to.eq(201);
      });
    });
  });

  it('should can delete multiple photos inside folder', () => { 
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'getProjectDocuments')).as(
      'getProjectDocuments'
    );
    
    cy.wait('@getProjectDocuments', { timeout: 10000 }).then(({ response }) => {
      expect(response?.statusCode).to.eq(200);
      
      cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'deleteOneProjectDocument')).as(
        'deleteOneProjectDocument'
      );

      cy.contains('Testing Edit Folder Name').should('be.visible').click();
      cy.wait(3000);
      
      cy.dropdown('Manage', 'single');
      cy.get('div[class="absolute w-full h-full py-5 px-5 overflow-x-hidden"]').scrollTo('top');

      cy.get('input[type="checkbox"]').each((checkbox, index) => {
        const targetIndex = [1, 2, 3, 4];
        
        if (targetIndex.includes(index)) {
          cy.wrap(checkbox).click({ force: true });
        }
      });
      
      cy.get('button[type="button"]').contains('Delete').should('be.visible').click();
      cy.get('button[type="button"]').contains('Yes').should('be.visible').click();
      
      cy.wait('@deleteOneProjectDocument', { timeout: 10000 }).then(({ response }) => {
        expect(response?.statusCode).to.eq(200);
      });
    });
  });

  it('should can delete a folder', () => {
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'getProjectDocuments')).as(
      'getProjectDocuments'
    );

    cy.wait('@getProjectDocuments', { timeout: 10000 }).then(({ response }) => {
      expect(response?.statusCode).to.eq(200);

      cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'deleteOneProjectDocument')).as(
        'deleteOneProjectDocument'
      );
      
      cy.dropdown('Manage', 'single');
      cy.get('div[class="absolute w-full h-full py-5 px-5 overflow-x-hidden"]').scrollTo('top');

      cy.get('button[type="button"]').contains('Delete').should('be.visible').click();
      cy.get('button[type="button"]').contains('Yes').should('be.visible').click();

      cy.wait('@deleteOneProjectDocument', { timeout: 10000 }).then(({ response }) => {
        expect(response?.statusCode).to.eq(200);
      });
    });
  });

  it('should can delete multiple photos on root', () => { 
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'getProjectDocuments')).as(
      'getProjectDocuments'
    );
    
    cy.wait('@getProjectDocuments', { timeout: 10000 }).then(({ response }) => {
      expect(response?.statusCode).to.eq(200);
      
      cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'deleteOneProjectDocument')).as(
        'deleteOneProjectDocument'
      );
      
      cy.dropdown('Manage', 'single');
      cy.get('div[class="absolute w-full h-full py-5 px-5 overflow-x-hidden"]').scrollTo('top');

      cy.get('input[type="checkbox"]').each((checkbox, index) => {
        const targetIndex = [1, 2, 3, 4];
        
        if (targetIndex.includes(index)) {
          cy.wrap(checkbox).click({ force: true });
        }
      });
      
      cy.get('button[type="button"]').contains('Delete').should('be.visible').click();
      cy.get('button[type="button"]').contains('Yes').should('be.visible').click();
      
      cy.wait('@deleteOneProjectDocument', { timeout: 10000 }).then(({ response }) => {
        expect(response?.statusCode).to.eq(200);
      });
    });
  });
});
