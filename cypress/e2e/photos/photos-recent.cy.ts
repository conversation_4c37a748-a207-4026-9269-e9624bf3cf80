import '../../support/commands';
import { aliasMutation, aliasQuery } from '../../support/graphql';
import { getFixturesDoc } from '../../support/utils';

beforeEach(() => {
  cy.login('<EMAIL>', '1234');
  cy.visit('/photos/recent');
});

describe('Photos Recent', () => {
  const photos = getFixturesDoc({ numberOfFiles: 5, type: 'images' });

  it('should visit the photos recent page', () => {
    cy.url().should('include', '/photos/recent');
  });

  it('should can upload a photo', () => {
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'getProjectDocuments')).as(
      'getProjectDocuments'
    );

    cy.wait('@getProjectDocuments', { timeout: 10000 }).then(({ response }) => {
      expect(response?.statusCode).to.eq(200);

      cy.get('button[type="button"]').contains('Add New').should('be.visible').click();
      cy.contains('Upload Files').should('be.visible').click();

      cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'createOneProjectDocument')).as(
        'createOneProjectDocument'
      );
      cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'getProjectDocuments')).as(
        'getProjectDocuments'
      );

      cy.uploadAttachment('upload-document', photos);

      cy.wait('@createOneProjectDocument', { timeout: 10000 }).then(({ response }) => {
        expect(response?.statusCode).to.eq(200);
      }
      );

      cy.get('div[class="ant-card-meta-title"]').should('have.length', 5);
      cy.get('button[type="button"]').contains('Dismiss').should('be.visible').click();
    });
  });

  it('should can search by photo name', () => {
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'getProjectDocuments')).as(
      'getProjectDocuments'
    );

    cy.wait('@getProjectDocuments', { timeout: 10000 }).then(({ response }) => {
      expect(response?.statusCode).to.eq(200);

      photos.forEach(photo => {
        const name = photo?.slice(photo.lastIndexOf('/') + 1, photo.lastIndexOf('.'));

        cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'getProjectDocuments')).as(
          'getProjectDocuments'
        );

        cy.get('input[placeholder="Search by name"]').clear().type(name);

        cy.wait('@getProjectDocuments', { timeout: 10000 }).then(({ response }) => {
          expect(response?.statusCode).to.eq(200);
          cy.get('div[class="ant-card-meta-title"]').should('have.length', 1).should('contain', name);

          cy.wait(1000);
        });
      });
    });
  });

  it('should can rename a photo', () => {
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'getProjectDocuments')).as(
      'getProjectDocuments'
    );

    cy.wait('@getProjectDocuments', { timeout: 10000 }).then(({ response }) => {
      expect(response?.statusCode).to.eq(200);

      cy.dropdown('Rename', 'single');
      cy.get('input[id="name"]').clear().type('Testing Edit');

      cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'updateOneProjectDocument')).as(
        'updateOneProjectDocument'
      );

      cy.get('button[type="submit"]').contains('Save').should('be.visible').click();

      cy.wait('@updateOneProjectDocument', { timeout: 10000 }).then(({ response }) => {
        expect(response?.statusCode).to.eq(200);
      });
    });
  });

  it('should can download one photos', () => {
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'getProjectDocuments')).as(
      'getProjectDocuments'
    );

    cy.wait('@getProjectDocuments', { timeout: 10000 }).then(({ response }) => {
      expect(response?.statusCode).to.eq(200);

      cy.intercept('POST', `/api/cloud-docs/project-document/download-bulk-zip`).as('downloadFolder');
      cy.dropdown('Download', 'single');

      cy.wait('@downloadFolder', { timeout: 10000 }).then(({ response }) => {
        expect(response?.statusCode).to.eq(201);
      });
    });
  });

  it('should can download multiple photos', () => {
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'getProjectDocuments')).as(
      'getProjectDocuments'
    );

    cy.wait('@getProjectDocuments', { timeout: 10000 }).then(({ response }) => {
      expect(response?.statusCode).to.eq(200);

      cy.intercept('POST', `/api/cloud-docs/project-document/download-bulk-zip`).as('downloadFolder');
      cy.dropdown('Manage', 'single');

      cy.get('input[type="checkbox"]').each((checkbox, index) => {
        const targetIndex = [1, 2, 3, 4];

        if (targetIndex.includes(index)) {
          cy.wrap(checkbox).click({ force: true });
        }
      });

      cy.get('button[type="button"]').contains('Download').should('be.visible').click();

      cy.wait('@downloadFolder', { timeout: 10000 }).then(({ response }) => {
        expect(response?.statusCode).to.eq(201);
      });
    });
  });

  it('should can delete photos', () => {
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'getProjectDocuments')).as(
      'getProjectDocuments'
    );

    cy.wait('@getProjectDocuments', { timeout: 10000 }).then(({ response }) => {
      expect(response?.statusCode).to.eq(200);

      cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasMutation(req, 'deleteOneProjectDocument')).as(
        'deleteOneProjectDocument'
      );

      cy.dropdown('Manage', 'single');

      cy.get('input[type="checkbox"]').each((checkbox, index) => {
        const targetIndex = [1, 2, 3, 4];

        if (targetIndex.includes(index)) {
          cy.wrap(checkbox).click({ force: true });
        }
      });

      cy.get('button[type="button"]').contains('Delete').should('be.visible').click();
      cy.get('button[type="button"]').contains('Yes').should('be.visible').click();

      cy.wait('@deleteOneProjectDocument', { timeout: 10000 }).then(({ response }) => {
        expect(response?.statusCode).to.eq(200);
      });
    });
  });
});
