import '../support/commands';
import { aliasQuery } from '../support/graphql';

describe('Login', () => {
  beforeEach(() => {
    cy.visit('/login');
  });

  it('should display Email is required error message', () => {
    cy.get('input[id="password"]').type('1234');
    cy.contains('Login').click({ multiple: true });

    cy.get('.ant-form-item-explain-error').should('be.visible').contains('Email is required');
  });

  it('should display Password is required error message', () => {
    cy.get('input[id="email"]').type('<EMAIL>');
    cy.contains('Login').click({ multiple: true });

    cy.get('.ant-form-item-explain-error').should('be.visible').contains('Password is required');
  });

  it('should display an error message if the form is submitted with incorrect credentials', () => {
    cy.get('input[id="email"]').type('<EMAIL>');
    cy.get('input[id="password"]').type('incorrect');
    cy.contains('Login').click({ multiple: true });

    cy.get('.ant-btn-loading-icon').should('be.visible');
    cy.get('.ant-message-error').should('be.visible');
  });

  it('should submit the form and redirect to the home page', () => {
    cy.get('input[id="email"]').type('<EMAIL>');
    cy.get('input[id="password"]').type('1234');
    cy.contains('Login').click({ multiple: true });
    cy.get('.ant-btn-loading-icon').should('be.visible');

    cy.intercept('GET', '/api/auth/session').as('session');
    cy.intercept('POST', Cypress.env().GRAPHQL_SCHEMA_PATH, req => aliasQuery(req, 'getUserMe')).as('getUserMe');
    cy.intercept('GET', '/api/auth/user/is-first-time-sign-in').as('isFirstTimeSignIn');

    cy.wait(['@session', '@getUserMe', '@isFirstTimeSignIn']).spread((session, getUserMe, firstTimeSignIn) => {
      expect(session.response?.statusCode).to.eq(200);
      expect(session.response?.body).to.have.property('accessToken');

      expect(getUserMe.response?.statusCode).to.eq(200);
      expect(getUserMe.response?.body).to.have.property('data').to.have.property('getUserMe');

      const isFirstTimeSignIn = firstTimeSignIn.response?.body;

      if (!isFirstTimeSignIn) {
        cy.url().should('include', '/');
        cy.end();
      } else {
        cy.url().should('include', '/onboarding');
        cy.end();
      }
    });
  });
});
