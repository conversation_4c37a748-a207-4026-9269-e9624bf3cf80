{"version": 3, "file": "chunks/2637.chunk.js", "mappings": "6GACQ,EAAQ,MAKhBA,CAAE,CAAEC,OAAQ,SAAUC,MAAM,GAAQ,CAClCC,UALqB,EAAQ,O", "sources": ["webpack://webviewer-ui/./node_modules/core-js/modules/es.number.is-integer.js"], "sourcesContent": ["'use strict';\nvar $ = require('../internals/export');\nvar isIntegralNumber = require('../internals/is-integral-number');\n\n// `Number.isInteger` method\n// https://tc39.es/ecma262/#sec-number.isinteger\n$({ target: 'Number', stat: true }, {\n  isInteger: isIntegralNumber\n});\n"], "names": ["$", "target", "stat", "isInteger"], "sourceRoot": ""}