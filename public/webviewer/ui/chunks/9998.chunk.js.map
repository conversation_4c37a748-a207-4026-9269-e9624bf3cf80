{"version": 3, "file": "chunks/9998.chunk.js", "mappings": "sGAAoEA,EAAOC,QAA6K,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,EAAE,CAAOG,CAAEH,GAAG,SAASI,EAAEJ,GAAG,OAAOA,EAAE,KAAK,CAAC,CAAC,SAASK,EAAEL,GAAG,OAAOA,EAAE,KAAK,GAAGA,EAAE,KAAK,CAAC,CAAC,SAASM,EAAEN,EAAEG,EAAEF,EAAEK,GAAG,IAAIC,EAAEP,EAAE,IAAI,OAAOC,GAAG,IAAI,IAAI,OAAOE,GAAGG,EAAE,eAAe,kBAAkB,IAAI,IAAI,OAAOH,EAAE,aAAa,aAAa,IAAI,KAAK,OAAOC,EAAEJ,GAAGO,GAAGJ,GAAGG,EAAE,SAAS,YAAYD,EAAEL,GAAGO,GAAGJ,GAAGG,EAAE,SAAS,YAAYC,GAAGJ,GAAGG,EAAE,QAAQ,YAAY,IAAI,IAAI,OAAOH,EAAE,UAAU,UAAU,IAAI,KAAK,OAAOC,EAAEJ,GAAGO,GAAGJ,GAAGG,EAAE,MAAM,SAASD,EAAEL,GAAGO,GAAGJ,GAAGG,EAAE,MAAM,SAASC,GAAGJ,GAAGG,EAAE,KAAK,SAAS,IAAI,IAAI,OAAOH,GAAGG,EAAE,SAAS,YAAY,IAAI,KAAK,OAAOF,EAAEJ,GAAGO,GAAGJ,GAAGG,EAAE,QAAQ,WAAWC,GAAGJ,GAAGG,EAAE,MAAM,SAAS,IAAI,IAAI,OAAOH,GAAGG,EAAE,WAAW,eAAe,IAAI,KAAK,OAAOF,EAAEJ,GAAGO,GAAGJ,GAAGG,EAAE,SAAS,YAAYD,EAAEL,GAAGO,GAAGJ,GAAGG,EAAE,SAAS,UAAUC,GAAGJ,GAAGG,EAAE,UAAU,UAAU,IAAI,IAAI,OAAOH,GAAGG,EAAE,WAAW,aAAa,IAAI,KAAK,OAAOF,EAAEJ,GAAGO,GAAGJ,GAAGG,EAAE,OAAO,UAAUD,EAAEL,GAAGO,GAAGJ,GAAGG,EAAE,OAAO,QAAQC,GAAGJ,GAAGG,EAAE,MAAM,QAAQ,CAAC,IAAIC,EAAE,CAACC,KAAK,KAAKC,SAAS,sDAAsDC,MAAM,KAAKC,OAAO,wFAAwFD,MAAM,KAAKE,UAAU,EAAEC,cAAc,qCAAqCH,MAAM,KAAKI,YAAY,8DAA8DJ,MAAM,KAAKK,YAAY,uBAAuBL,MAAM,KAAKM,QAAQ,SAAShB,GAAG,OAAOA,EAAE,GAAG,EAAEiB,QAAQ,CAACC,GAAG,OAAOC,IAAI,UAAUC,EAAE,aAAaC,GAAG,eAAeC,IAAI,oBAAoBC,KAAK,0BAA0BC,EAAE,cAAcC,aAAa,CAACC,OAAO,SAASC,KAAK,UAAUrB,EAAEA,EAAEC,EAAED,EAAEsB,GAAGtB,EAAEuB,EAAEvB,EAAEwB,GAAGxB,EAAEyB,EAAEzB,EAAE0B,GAAG1B,EAAE2B,EAAE3B,EAAE4B,GAAG5B,EAAE6B,EAAE7B,EAAE8B,GAAG9B,IAAI,OAAOL,EAAEC,QAAQmC,OAAO9B,EAAE,MAAK,GAAIA,CAAE,CAAhzDJ,CAAE,EAAQ,O", "sources": ["webpack://webviewer-ui/./node_modules/dayjs/locale/sl.js"], "sourcesContent": ["!function(e,n){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=n(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],n):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_sl=n(e.dayjs)}(this,(function(e){\"use strict\";function n(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var t=n(e);function r(e){return e%100==2}function a(e){return e%100==3||e%100==4}function s(e,n,t,s){var m=e+\" \";switch(t){case\"s\":return n||s?\"nekaj sekund\":\"nekaj sekundami\";case\"m\":return n?\"ena minuta\":\"eno minuto\";case\"mm\":return r(e)?m+(n||s?\"minuti\":\"minutama\"):a(e)?m+(n||s?\"minute\":\"minutami\"):m+(n||s?\"minut\":\"minutami\");case\"h\":return n?\"ena ura\":\"eno uro\";case\"hh\":return r(e)?m+(n||s?\"uri\":\"urama\"):a(e)?m+(n||s?\"ure\":\"urami\"):m+(n||s?\"ur\":\"urami\");case\"d\":return n||s?\"en dan\":\"enim dnem\";case\"dd\":return r(e)?m+(n||s?\"dneva\":\"dnevoma\"):m+(n||s?\"dni\":\"dnevi\");case\"M\":return n||s?\"en mesec\":\"enim mesecem\";case\"MM\":return r(e)?m+(n||s?\"meseca\":\"mesecema\"):a(e)?m+(n||s?\"mesece\":\"meseci\"):m+(n||s?\"mesecev\":\"meseci\");case\"y\":return n||s?\"eno leto\":\"enim letom\";case\"yy\":return r(e)?m+(n||s?\"leti\":\"letoma\"):a(e)?m+(n||s?\"leta\":\"leti\"):m+(n||s?\"let\":\"leti\")}}var m={name:\"sl\",weekdays:\"nedelja_ponedeljek_torek_sreda_četrtek_petek_sobota\".split(\"_\"),months:\"januar_februar_marec_april_maj_junij_julij_avgust_september_oktober_november_december\".split(\"_\"),weekStart:1,weekdaysShort:\"ned._pon._tor._sre._čet._pet._sob.\".split(\"_\"),monthsShort:\"jan._feb._mar._apr._maj._jun._jul._avg._sep._okt._nov._dec.\".split(\"_\"),weekdaysMin:\"ne_po_to_sr_če_pe_so\".split(\"_\"),ordinal:function(e){return e+\".\"},formats:{LT:\"H:mm\",LTS:\"H:mm:ss\",L:\"DD.MM.YYYY\",LL:\"D. MMMM YYYY\",LLL:\"D. MMMM YYYY H:mm\",LLLL:\"dddd, D. MMMM YYYY H:mm\",l:\"D. M. YYYY\"},relativeTime:{future:\"čez %s\",past:\"pred %s\",s:s,m:s,mm:s,h:s,hh:s,d:s,dd:s,M:s,MM:s,y:s,yy:s}};return t.default.locale(m,null,!0),m}));"], "names": ["module", "exports", "e", "t", "default", "n", "r", "a", "s", "m", "name", "weekdays", "split", "months", "weekStart", "weekdaysShort", "monthsShort", "weekdaysMin", "ordinal", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "l", "relativeTime", "future", "past", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "locale"], "sourceRoot": ""}