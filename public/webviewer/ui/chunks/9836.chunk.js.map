{"version": 3, "file": "chunks/9836.chunk.js", "mappings": "sGAAoEA,EAAOC,QAA6K,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,EAAE,CAAOG,CAAEH,GAAGI,EAAE,CAACC,KAAK,KAAKC,SAAS,wDAAwDC,MAAM,KAAKC,OAAO,iGAAiGD,MAAM,KAAKE,YAAY,6DAA6DF,MAAM,KAAKG,UAAU,EAAEC,cAAc,8BAA8BJ,MAAM,KAAKK,YAAY,uBAAuBL,MAAM,KAAKM,QAAQ,SAASb,GAAG,OAAOA,CAAC,EAAEc,QAAQ,CAACC,GAAG,QAAQC,IAAI,WAAWC,EAAE,aAAaC,GAAG,cAAcC,IAAI,oBAAoBC,KAAK,0BAA0BC,aAAa,CAACC,OAAO,SAASC,KAAK,SAASC,EAAE,mBAAmBC,EAAE,YAAYC,GAAG,aAAaC,EAAE,WAAWC,GAAG,WAAWC,EAAE,UAAUC,GAAG,WAAWC,EAAE,aAAaC,GAAG,aAAaC,EAAE,WAAWC,GAAG,eAAe,OAAOjC,EAAEC,QAAQiC,OAAO/B,EAAE,MAAK,GAAIA,CAAE,CAAljCD,CAAE,EAAQ,O", "sources": ["webpack://webviewer-ui/./node_modules/dayjs/locale/fy.js"], "sourcesContent": ["!function(e,n){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=n(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],n):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_fy=n(e.dayjs)}(this,(function(e){\"use strict\";function n(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var i=n(e),t={name:\"fy\",weekdays:\"snein_moandei_tiisdei_woansdei_tongersdei_freed_sneon\".split(\"_\"),months:\"jannewaris_febrewaris_maart_april_maaie_juny_july_augustus_septimber_oktober_novimber_desimber\".split(\"_\"),monthsShort:\"jan._feb._mrt._apr._mai_jun._jul._aug._sep._okt._nov._des.\".split(\"_\"),weekStart:1,weekdaysShort:\"si._mo._ti._wo._to._fr._so.\".split(\"_\"),weekdaysMin:\"Si_Mo_Ti_Wo_To_Fr_So\".split(\"_\"),ordinal:function(e){return e},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD-MM-YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY HH:mm\",LLLL:\"dddd D MMMM YYYY HH:mm\"},relativeTime:{future:\"oer %s\",past:\"%s lyn\",s:\"in pear sekonden\",m:\"ien minút\",mm:\"%d minuten\",h:\"ien oere\",hh:\"%d oeren\",d:\"ien dei\",dd:\"%d dagen\",M:\"ien moanne\",MM:\"%d moannen\",y:\"ien jier\",yy:\"%d jierren\"}};return i.default.locale(t,null,!0),t}));"], "names": ["module", "exports", "e", "i", "default", "n", "t", "name", "weekdays", "split", "months", "monthsShort", "weekStart", "weekdaysShort", "weekdaysMin", "ordinal", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "relativeTime", "future", "past", "s", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "locale"], "sourceRoot": ""}