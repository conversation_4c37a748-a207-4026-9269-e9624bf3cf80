/*! For license information please see 683.chunk.js.LICENSE.txt */
(self.webpackChunkwebviewer_ui=self.webpackChunkwebviewer_ui||[]).push([[683],{683:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>De}),n(52675),n(89463),n(2259),n(45700),n(28706),n(33771),n(2008),n(48980),n(51629),n(23418),n(74423),n(25276),n(64346),n(23792),n(62062),n(34782),n(15086),n(54554),n(89572),n(23288),n(62010),n(2892),n(67945),n(84185),n(83851),n(81278),n(79432),n(26099),n(27495),n(38781),n(21699),n(47764),n(23500),n(62953);var o=n(38221),r=n.n(o),a=n(96540),i=n(61113),l=n(46697),c=n(40367),u=n(46942),s=n.n(u),d=n(90540),m=(n(94170),n(69085),n(75710)),p=n(26247),h=(n(94490),n(59904),n(40875),n(10287),n(58940),n(3362),n(31415),n(76031),n(16096)),f=n(28854),g=n(5556),b=n.n(g),v=n(95651),y=n(51e3),w=(n(30226),n(48822)),x=n(97160);const A=function(e){var t=e.className,n=e.pageIndex,o=(0,i.wA)(),r=(0,i.d4)((function(e){return m.A.getSelectedThumbnailPageIndexes(e)}));return a.createElement("div",{className:t,onClick:function(){-1===r.indexOf(n)&&o(p.A.setSelectedPageThumbnails([n]))}},a.createElement(w.A,{dataElement:x.A.PAGE_MANIPULATION_OVERLAY_BUTTON,element:x.A.PAGE_MANIPULATION_OVERLAY,img:"icon-tool-more",title:"option.thumbnailPanel.moreOptions"}))};var E=n(66481),C=n(18492);function P(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,a,i,l=[],c=!0,u=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=a.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){u=!0,r=e}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(u)throw r}}return l}}(e,t)||function(e,t){if(e){if("string"==typeof e)return T(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?T(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function T(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}var k={index:b().number.isRequired},S="thumbnailControl",I=function(e){var t=e.index,n=(0,C.B)().t,o=P((0,i.d4)((function(e){return[m.A.isElementDisabled(e,S)]})),1)[0],r=P((0,i.d4)((function(e){return[m.A.isElementDisabled(e,x.A.PAGE_MANIPULATION_OVERLAY_BUTTON)]})),1)[0],l=P((0,i.d4)((function(e){return[m.A.pageDeletionConfirmationModalEnabled(e),m.A.getSelectedThumbnailPageIndexes(e)]})),2),c=l[0],u=l[1],d=(0,i.wA)(),p=P((0,i.d4)((function(e){return[m.A.getCurrentPage(e),m.A.getThumbnailControlMenuItems(e),m.A.getFeatureFlags(e)]}),i.bN),3),h=p[0],g=p[1],b=p[2],w=u.length>0?u.map((function(e){return e+1})):[t+1],T=w.includes(h),k=b.customizableUI;T||(w=[h]);var I=f.A.getDocument(),N=null==I?void 0:I.type,O=N===E._.XOD,L=N===E._.OFFICE||N===E._.LEGACY_OFFICE,R={thumbRotateClockwise:a.createElement(y.A,{className:"rotate-button",img:"icon-header-page-manipulation-page-rotation-clockwise-line",onClick:function(){return(0,v.gH)(w)},title:"option.thumbnailPanel.rotatePageClockwise",dataElement:"thumbRotateClockwise"}),thumbRotateCounterClockwise:a.createElement(y.A,{img:"icon-header-page-manipulation-page-rotation-counterclockwise-line",onClick:function(){return(0,v.cV)(w)},title:"option.thumbnailPanel.rotatePageCounterClockwise",dataElement:"thumbRotateCounterClockwise"}),thumbDelete:a.createElement(y.A,{className:"delete-button",img:"icon-delete-line",onClick:function(){return(0,v.Gt)(w,d,c)},title:"option.thumbnailPanel.delete",dataElement:"thumbDelete",onClickAnnouncement:"".concat(n("action.delete")," ").concat(n("action.modal")," ").concat(n("action.isOpen"))})},M=!1,D=[],_=g.map((function(e){var t=e.dataElement,n=t,o=R[t];if(D.indexOf(t)>-1)return null;if(D.push(t),!o){M=!0;var r=e.img,i=e.onClick,l=e.title;o=a.createElement(y.A,{className:"".concat(t,"-button"),img:r,onClick:function(){return i(h)},title:l,dataElement:t})}return o?a.cloneElement(o,{key:n}):null}));return o?null:O||L||null!=I&&I.isWebViewerServerDocument()?a.createElement("div",{className:"thumbnailControls-overlay","data-element":S,style:{display:"flex"}},a.createElement(y.A,{img:"icon-header-page-manipulation-page-rotation-counterclockwise-line",onClick:function(){return(0,v.cV)(w)},title:"option.thumbnailPanel.rotatePageCounterClockwise",dataElement:"thumbRotateCounterClockwise"}),a.createElement(y.A,{img:"icon-header-page-manipulation-page-rotation-clockwise-line",onClick:function(){return(0,v.gH)(w)},title:"option.thumbnailPanel.rotatePageClockwise",dataElement:"thumbRotateClockwise"})):a.createElement("div",{className:s()({"thumbnailControls-overlay":!0,"custom-buttons":M,"modular-ui":k}),"data-element":S},_,r?null:a.createElement(A,{className:"more-options",pageIndex:t}))};I.propTypes=k;const N=I;var O=n(42050),L=(n(66794),n(11768)),R=n(59844);function M(e){return M="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},M(e)}function D(e){return function(e){if(Array.isArray(e))return H(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||B(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _(){_=function(){return t};var e,t={},n=Object.prototype,o=n.hasOwnProperty,r=Object.defineProperty||function(e,t,n){e[t]=n.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",l=a.asyncIterator||"@@asyncIterator",c=a.toStringTag||"@@toStringTag";function u(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,n){return e[t]=n}}function s(e,t,n,o){var a=t&&t.prototype instanceof b?t:b,i=Object.create(a.prototype),l=new N(o||[]);return r(i,"_invoke",{value:T(e,n,l)}),i}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=s;var m="suspendedStart",p="suspendedYield",h="executing",f="completed",g={};function b(){}function v(){}function y(){}var w={};u(w,i,(function(){return this}));var x=Object.getPrototypeOf,A=x&&x(x(O([])));A&&A!==n&&o.call(A,i)&&(w=A);var E=y.prototype=b.prototype=Object.create(w);function C(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function P(e,t){function n(r,a,i,l){var c=d(e[r],e,a);if("throw"!==c.type){var u=c.arg,s=u.value;return s&&"object"==M(s)&&o.call(s,"__await")?t.resolve(s.__await).then((function(e){n("next",e,i,l)}),(function(e){n("throw",e,i,l)})):t.resolve(s).then((function(e){u.value=e,i(u)}),(function(e){return n("throw",e,i,l)}))}l(c.arg)}var a;r(this,"_invoke",{value:function(e,o){function r(){return new t((function(t,r){n(e,o,t,r)}))}return a=a?a.then(r,r):r()}})}function T(t,n,o){var r=m;return function(a,i){if(r===h)throw Error("Generator is already running");if(r===f){if("throw"===a)throw i;return{value:e,done:!0}}for(o.method=a,o.arg=i;;){var l=o.delegate;if(l){var c=k(l,o);if(c){if(c===g)continue;return c}}if("next"===o.method)o.sent=o._sent=o.arg;else if("throw"===o.method){if(r===m)throw r=f,o.arg;o.dispatchException(o.arg)}else"return"===o.method&&o.abrupt("return",o.arg);r=h;var u=d(t,n,o);if("normal"===u.type){if(r=o.done?f:p,u.arg===g)continue;return{value:u.arg,done:o.done}}"throw"===u.type&&(r=f,o.method="throw",o.arg=u.arg)}}}function k(t,n){var o=n.method,r=t.iterator[o];if(r===e)return n.delegate=null,"throw"===o&&t.iterator.return&&(n.method="return",n.arg=e,k(t,n),"throw"===n.method)||"return"!==o&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+o+"' method")),g;var a=d(r,t.iterator,n.arg);if("throw"===a.type)return n.method="throw",n.arg=a.arg,n.delegate=null,g;var i=a.arg;return i?i.done?(n[t.resultName]=i.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,g):i:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,g)}function S(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function I(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function N(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(S,this),this.reset(!0)}function O(t){if(t||""===t){var n=t[i];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,a=function n(){for(;++r<t.length;)if(o.call(t,r))return n.value=t[r],n.done=!1,n;return n.value=e,n.done=!0,n};return a.next=a}}throw new TypeError(M(t)+" is not iterable")}return v.prototype=y,r(E,"constructor",{value:y,configurable:!0}),r(y,"constructor",{value:v,configurable:!0}),v.displayName=u(y,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===v||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,y):(e.__proto__=y,u(e,c,"GeneratorFunction")),e.prototype=Object.create(E),e},t.awrap=function(e){return{__await:e}},C(P.prototype),u(P.prototype,l,(function(){return this})),t.AsyncIterator=P,t.async=function(e,n,o,r,a){void 0===a&&(a=Promise);var i=new P(s(e,n,o,r),a);return t.isGeneratorFunction(n)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},C(E),u(E,c,"Generator"),u(E,i,(function(){return this})),u(E,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var o in t)n.push(o);return n.reverse(),function e(){for(;n.length;){var o=n.pop();if(o in t)return e.value=o,e.done=!1,e}return e.done=!0,e}},t.values=O,N.prototype={constructor:N,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(I),!t)for(var n in this)"t"===n.charAt(0)&&o.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function r(o,r){return l.type="throw",l.arg=t,n.next=o,r&&(n.method="next",n.arg=e),!!r}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],l=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var c=o.call(i,"catchLoc"),u=o.call(i,"finallyLoc");if(c&&u){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&o.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var a=r;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a.finallyLoc,g):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),I(n),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var r=o.arg;I(n)}return r}}throw Error("illegal catch attempt")},delegateYield:function(t,n,o){return this.delegate={iterator:O(t),resultName:n,nextLoc:o},"next"===this.method&&(this.arg=e),g}},t}function j(e,t,n,o,r,a,i){try{var l=e[a](i),c=l.value}catch(e){return void n(e)}l.done?t(c):Promise.resolve(c).then(o,r)}function z(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,a,i,l=[],c=!0,u=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=a.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){u=!0,r=e}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(u)throw r}}return l}}(e,t)||B(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function B(e,t){if(e){if("string"==typeof e)return H(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?H(e,t):void 0}}function H(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}const U=function(e){var t=e.index,n=e.isSelected,o=e.updateAnnotations,r=e.shiftKeyThumbnailPivotIndex,i=e.onFinishLoading,l=e.onLoad,c=e.onRemove,u=void 0===c?function(){}:c,d=e.onDragStart,m=e.onDragOver,p=e.isDraggable,g=e.shouldShowControls,b=e.thumbnailSize,v=e.currentPage,y=e.pageLabels,w=void 0===y?[]:y,x=e.selectedPageIndexes,A=e.isThumbnailMultiselectEnabled,E=e.isReaderModeOrReadOnly,C=e.dispatch,P=e.actions,T=e.isMobile,k=e.canLoad,S=e.onCancel,I=e.isThumbnailSelectingPages,M=e.thumbnailSelectionMode,B=e.activeDocumentViewerKey,H=e.panelSelector,U=b?Number(b):150,F=z((0,a.useState)({width:U,height:U}),2),G=F[0],W=F[1],V=z((0,a.useState)(!1),2),q=V[0],Y=V[1],K=null,$=function(){K=setTimeout((function(){var e,n,r=(0,R.Ay)().querySelector(".ThumbnailsPanel.".concat(H," #pageThumb").concat(t)),a=t+1,c=f.A.getRotation(a),u=f.A.getDocument(B);if(u&&u.getPageInfo(a)){var s=u.loadCanvas({pageNumber:a,width:U,height:U,drawComplete:(e=_().mark((function e(n){var r,a,l,u,s;return _().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:(r=(0,R.Ay)().querySelector(".ThumbnailsPanel.".concat(H," #pageThumb").concat(t)))&&((a=r.querySelector(".page-image"))&&r.removeChild(a),n.className="page-image",l=Math.min(U/n.width,U/n.height),n.style.width="".concat(n.width*l,"px"),n.style.height="".concat(n.height*l,"px"),W({width:Number(n.width),height:Number(n.height)}),Math.abs(c)&&(u="rotate(".concat(90*c,"deg) translate(-50%,-50%)"),s="top left",n.style.transform=u,n.style["transform-origin"]=s,n.style["ms-transform"]=u,n.style["ms-transform-origin"]=s,n.style["-moz-transform"]=u,n.style["-moz-transform-origin"]=s,n.style["-webkit-transform-origin"]=s,n.style["-webkit-transform"]=u,n.style["-o-transform"]=u,n.style["-o-transform-origin"]=s),r.appendChild(n)),o&&o(t),i(t),Y(!0);case 5:case"end":return e.stop()}}),e)})),n=function(){var t=this,n=arguments;return new Promise((function(o,r){var a=e.apply(t,n);function i(e){j(a,o,r,i,l,"next",e)}function l(e){j(a,o,r,i,l,"throw",e)}i(void 0)}))},function(e){return n.apply(this,arguments)}),allowUseOfOptimizedThumbnail:!0});l(t,r,s)}}),50)};(0,a.useEffect)((function(){var e=function(e){var n=e.contentChanged,o=e.moved,r=e.added,a=e.removed,i=t+1,l=r.includes(i),c=n.some((function(e){return i===e})),u=Object.keys(o).some((function(e){return i===parseInt(e)})),s=a.includes(i),d=f.A.getTotalPages();a.length>0&&t+1>d||(l||c||u||s)&&$()},n=function(){Y(!1),$()};return f.A.addEventListener("pagesUpdated",e),f.A.addEventListener("rotationUpdated",n),k&&$(),function(){f.A.removeEventListener("pagesUpdated",e),f.A.removeEventListener("rotationUpdated",n),clearTimeout(K),u(t)}}),[]),(0,h.A)((function(){k?($(),o(t)):S(t)}),[k,B]);var X=v===t+1,J=w[t],Q="default",Z=f.A.getRotation(t+1);return((!Z||2===Z)&&G.width>G.height||(1===Z||3===Z)&&G.width<G.height)&&(Q="rotated"),a.createElement("div",{className:s()({Thumbnail:!0,active:X,selected:n&&I}),onDragOver:function(e){return m(e,t)},id:"Thumbnail-container"},a.createElement("div",{className:"container",style:{width:U,height:U},onDragStart:function(e){return d(e,t)},draggable:p,onClick:function(e){var n=e.target.type&&"checkbox"===e.target.type;if(A&&!E){var o=e.ctrlKey||e.metaKey,a=e.shiftKey,i=D(x);if(a){C(P.setThumbnailSelectingPages(!0));var l=r;null===l&&(l=v-1,C(P.setShiftKeyThumbnailsPivotIndex(l)));var c=Math.min(l,t),u=Math.max(l,t);i=D(new Set(D(Array.from({length:u-c+1},(function(e,t){return t+c})))))}else o||I?(C(P.setThumbnailSelectingPages(!0)),(o||n||M===O.A.THUMBNAIL)&&(0!==x.length||I?x.includes(t)?i=x.filter((function(e){return t!==e})):i.push(t):i.push(v-1)),C(P.setShiftKeyThumbnailsPivotIndex(t))):i=[t];var s=i[i.length-1];!I&&!a&&C(P.setShiftKeyThumbnailsPivotIndex(s)),C(P.setSelectedPageThumbnails(i))}else T()&&C(P.closeElement("leftPanel"));setTimeout((function(){n&&M!==O.A.THUMBNAIL||f.A.setCurrentPage(t+1)}),0)}},a.createElement("div",{id:"pageThumb".concat(t),className:"thumbnail"}),I&&q&&a.createElement(L.G,{className:"checkbox ".concat(Q),checked:x.includes(t)})),a.createElement("div",{className:"page-label"},J),!I&&X&&g&&a.createElement(N,{index:t}))};function F(){return F=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},F.apply(null,arguments)}function G(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}const W=function(e){var t,n,o=(t=(0,i.d4)((function(e){return[m.A.getCurrentPage(e),m.A.getPageLabels(e),m.A.getSelectedThumbnailPageIndexes(e),m.A.isThumbnailMultiselectEnabled(e),m.A.isReaderMode(e),m.A.isDocumentReadOnly(e),m.A.getShiftKeyThumbnailPivotIndex(e),m.A.isThumbnailSelectingPages(e),m.A.getThumbnailSelectionMode(e),m.A.getActiveDocumentViewerKey(e)]}),i.bN),n=11,function(e){if(Array.isArray(e))return e}(t)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,a,i,l=[],c=!0,u=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=a.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){u=!0,r=e}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(u)throw r}}return l}}(t,n)||function(e,t){if(e){if("string"==typeof e)return G(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?G(e,t):void 0}}(t,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),r=o[0],l=o[1],c=o[2],u=o[3],s=o[4],h=o[5],f=o[6],g=o[7],b=o[8],v=o[9],y=o[10],w=(0,i.wA)();return a.createElement(U,F({},e,{currentPage:r,pageLabels:l,selectedPageIndexes:c,isThumbnailMultiselectEnabled:u,isReaderModeOrReadOnly:s||h,dispatch:w,actions:p.A,isMobile:d.Fr,shiftKeyThumbnailPivotIndex:f,isThumbnailSelectingPages:g,thumbnailSelectionMode:b,selectionModes:y,activeDocumentViewerKey:v}))};n(26910),n(25440);var V=n(23983);n(50113),n(55698);const q=function(){return a.createElement("div",{className:"dropdown-menu button-hover"},a.createElement(w.A,{title:"action.more",className:"dropdown-menu",element:x.A.THUMBNAILS_CONTROL_MANIPULATE_POPUP_SMALL,dataElement:x.A.THUMBNAILS_CONTROL_MANIPULATE_POPUP_SMALL_TRIGGER,img:"icon-tool-more"}),a.createElement("div",{className:"indicator"}))},Y=function(){return a.createElement("div",{className:"dropdown-menu"},a.createElement(w.A,{title:"action.PageInsertion",className:"dropdown-menu",element:"thumbnailsControlInsertPopup",dataElement:"thumbnailsControlInsertPopupTrigger",img:"icon-header-page-manipulation-insert-above"}),a.createElement("div",{className:"indicator"}))},K=function(e){var t=e.pageNumbers,n=e.operations;return a.createElement(a.Fragment,null,n.map((function(e,n){return a.createElement(y.A,{key:n,className:"button-hover",dataElement:e.dataElement,img:e.img,onClick:function(){return e.onClick(t)},title:e.title})})))},$=function(e){var t=e.onRotateClockwise,n=e.onRotateCounterClockwise;return a.createElement(a.Fragment,null,a.createElement(y.A,{className:"button-hover",dataElement:"thumbnailsControlRotateCounterClockwise",img:"icon-header-page-manipulation-page-rotation-counterclockwise-line",onClick:n,title:"action.rotateCounterClockwise"}),a.createElement(y.A,{className:"button-hover",dataElement:"thumbnailsControlRotateClockwise",img:"icon-header-page-manipulation-page-rotation-clockwise-line",onClick:t,title:"action.rotateClockwise"}))};function X(){return X=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},X.apply(null,arguments)}function J(e){var t=e.children,n=e.pageNumbers,o=e.multiPageManipulationControlsItems,r=a.Children.toArray(t);return o?o.map((function(e,t){var o=e.dataElement,i=e.type,l=r.find((function(e){return e.props.dataElement===o})),c=o||"".concat(i,"-").concat(t);return l||("divider"===i&&(l=a.createElement("div",{className:"divider"})),"customPageOperation"===i&&(l=a.createElement(K,X({key:o,pageNumbers:n},e)))),l?a.cloneElement(l,{key:c}):null})):r}const Q=function(e){var t=e.pageNumbers,n=e.multiPageManipulationControlsItemsSmall,o=e.onRotateClockwise,r=e.onRotateCounterClockwise;return a.createElement("div",{className:"PageControlContainer root small"},a.createElement(J,{pageNumbers:t,multiPageManipulationControlsItems:n},a.createElement($,{onRotateClockwise:o,onRotateCounterClockwise:r,dataElement:"leftPanelPageTabsRotate"}),a.createElement(Y,{dataElement:"leftPanelPageTabsInsertSmall"}),a.createElement(q,{dataElement:"leftPanelPageTabsMoreSmall"})))},Z=function(){return a.createElement("div",{className:"dropdown-menu button-hover"},a.createElement(w.A,{title:"action.more",className:"dropdown-menu",element:x.A.THUMBNAILS_CONTROL_MANIPULATE_POPUP,dataElement:x.A.THUMBNAILS_CONTROL_MANIPULATE_POPUP_TRIGGER,img:"icon-tool-more"}),a.createElement("div",{className:"indicator"}))},ee=function(e){var t=e.moveToTop,n=e.moveToBottom;return a.createElement(a.Fragment,null,a.createElement(y.A,{className:"button-hover",dataElement:"moveToTop",img:"icon-page-move-up",onClick:t,title:"action.moveToTop"}),a.createElement(y.A,{className:"button-hover",dataElement:"moveToBottom",img:"icon-page-move-down",onClick:n,title:"action.moveToBottom"}))};function te(){return te=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},te.apply(null,arguments)}function ne(e){var t=e.children,n=e.pageNumbers,o=e.multiPageManipulationControlsItems,r=a.Children.toArray(t);return o?o.map((function(e,t){var o=e.dataElement,i=e.type,l=r.find((function(e){return e.props.dataElement===o})),c=o||"".concat(i,"-").concat(t);return l||("divider"===i&&(l=a.createElement("div",{className:"divider"})),"customPageOperation"===i&&(l=a.createElement(K,te({key:o,pageNumbers:n},e)))),l?a.cloneElement(l,{key:c}):null})):r}const oe=function(e){var t=e.pageNumbers,n=e.onRotateClockwise,o=e.onRotateCounterClockwise,r=e.multiPageManipulationControlsItems,i=e.moveToTop,l=e.moveToBottom;return a.createElement("div",{className:"PageControlContainer root"},a.createElement(ne,{pageNumbers:t,multiPageManipulationControlsItems:r},a.createElement($,{onRotateClockwise:n,onRotateCounterClockwise:o,dataElement:"leftPanelPageTabsRotate"}),a.createElement(ee,{moveToTop:i,moveToBottom:l,dataElement:"leftPanelPageTabsMove"}),a.createElement(Z,{dataElement:"leftPanelPageTabsMore"})))},re=function(e){var t=e.onInsert;return a.createElement(a.Fragment,null,a.createElement(y.A,{className:"button-hover",dataElement:"thumbnailsControlInsert",img:"icon-page-insertion-insert",onClick:t,title:"action.insert"}))},ae=function(e){var t=e.onInsert,n=e.onReplace,o=e.onExtractPages,r=e.onDeletePages;return a.createElement(a.Fragment,null,a.createElement(y.A,{className:"button-hover",dataElement:"thumbnailsControlInsert",img:"icon-page-insertion-insert",onClick:t,title:"action.insert"}),a.createElement(y.A,{className:"button-hover",dataElement:"thumbnailsControlReplace",img:"icon-page-replacement",onClick:n,title:"action.replace"}),a.createElement(y.A,{className:"button-hover",dataElement:"thumbnailsControlExtract",img:"icon-page-manipulation-extract",onClick:o,title:"action.extract"}),a.createElement(y.A,{className:"button-hover",dataElement:"thumbnailsControlDelete",img:"icon-delete-line",onClick:r,title:"action.delete"}))};function ie(){return ie=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},ie.apply(null,arguments)}function le(e){var t=e.children,n=e.pageNumbers,o=e.multiPageManipulationControlsItems,r=a.Children.toArray(t);return o?o.map((function(e,t){var o=e.dataElement,i=e.type,l=r.find((function(e){return e.props.dataElement===o})),c=o||"".concat(i,"-").concat(t);return l||("divider"===i&&(l=a.createElement("div",{className:"divider"})),"customPageOperation"===i&&(l=a.createElement(K,ie({key:o,pageNumbers:n},e)))),l?a.cloneElement(l,{key:c}):null})):r}const ce=function(e){var t=e.pageNumbers,n=e.onRotateClockwise,o=e.onRotateCounterClockwise,r=e.onInsert,i=e.onReplace,l=e.onExtractPages,c=e.onDeletePages,u=e.moveToTop,s=e.moveToBottom,d=e.multiPageManipulationControlsItems;return a.createElement("div",{className:"PageControlContainer root"},a.createElement(le,{pageNumbers:t,multiPageManipulationControlsItems:d},a.createElement($,{onRotateClockwise:n,onRotateCounterClockwise:o,dataElement:"leftPanelPageTabsRotate"}),a.createElement(re,{onInsert:r,dataElement:"leftPanelPageTabsInsert"}),a.createElement(ae,{onInsert:r,onReplace:i,onExtractPages:l,onDeletePages:c,dataElement:"leftPanelPageTabsOperations"}),a.createElement(ee,{moveToTop:u,moveToBottom:s,dataElement:"leftPanelPageTabsMove"})))};var ue=n(75914);function se(){return se=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},se.apply(null,arguments)}function de(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}const me=function(e){var t,n,o,r=e.parentElement,l=(0,i.wA)(),c=(0,d.Fr)(),u=(n=(0,i.d4)((function(e){return[m.A.getSelectedThumbnailPageIndexes(e),r&&"leftPanel"!==r?m.A.getPanelWidth(e,r):m.A.getLeftPanelWidth(e),m.A.pageDeletionConfirmationModalEnabled(e),m.A.getMultiPageManipulationControlsItems(e),m.A.getMultiPageManipulationControlsItemsSmall(e),m.A.getMultiPageManipulationControlsItemsLarge(e),m.A.isInDesktopOnlyMode(e)]})),o=7,function(e){if(Array.isArray(e))return e}(n)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,a,i,l=[],c=!0,u=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=a.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){u=!0,r=e}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(u)throw r}}return l}}(n,o)||function(e,t){if(e){if("string"==typeof e)return de(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?de(e,t):void 0}}(n,o)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),s=u[0],h=u[1],g=u[2],b=u[3],y=u[4],w=u[5],A=u[6],C=s.map((function(e){return e+1})),P=function(){return!(0,v.zd)(C,l)&&(0,v.gH)(C)},T=function(){return!(0,v.zd)(C,l)&&(0,v.cV)(C)},k=f.A.getDocument(),S=null==k?void 0:k.type,I=S===E._.XOD,N=S===E._.OFFICE||S===E._.LEGACY_OFFICE;if(I||N||null!=k&&k.isWebViewerServerDocument())return a.createElement("div",{className:"PageControlContainer root small"},a.createElement($,{onRotateClockwise:P,onRotateCounterClockwise:T}));if(!A&&c)try{t=(0,R.Ay)().querySelector(".App").getBoundingClientRect().width-88}catch(o){t=(h||ue.fz)-88}else t=(h||ue.fz)-88;var O=t>290,L={onReplace:function(){return!(0,v.zd)(C,l)&&(0,v.HC)(l)},onExtractPages:function(){return!(0,v.zd)(C,l)&&(0,v.ut)(C,l)},onDeletePages:function(){return!(0,v.zd)(C,l)&&(0,v.Gt)(C,l,g)},onRotateCounterClockwise:T,onRotateClockwise:P,onInsert:function(){return!(0,v.zd)(C,l)&&(l(p.A.closeElement(x.A.PAGE_MANIPULATION_OVERLAY)),void l(p.A.openElement("insertPageModal")))},moveToTop:function(){return!(0,v.zd)(C,l)&&(0,v.gP)(C)},moveToBottom:function(){return!(0,v.zd)(C,l)&&(0,v.Iw)(C)},pageNumbers:C};return t<190?a.createElement(Q,se({},L,{multiPageManipulationControlsItemsSmall:y})):O?a.createElement(ce,se({},L,{multiPageManipulationControlsItems:w})):a.createElement(oe,se({},L,{multiPageManipulationControlsItems:b}))};function pe(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,a,i,l=[],c=!0,u=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=a.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){u=!0,r=e}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(u)throw r}}return l}}(e,t)||function(e,t){if(e){if("string"==typeof e)return he(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?he(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function he(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}function fe(e,t){for(var n="",o=e.sort((function(e,t){return e-t})),r=null,a=0;o.length>a;a++)o[a+1]===o[a]+1?r=null!==r?r:o[a]:null!==r?(n="".concat(n).concat(t[r],"-").concat(t[o[a]],", "),r=null):n="".concat(n).concat(t[o[a]],", ");return n.slice(0,-2)}n(45622);var ge=function(e){var t=e.shouldShowControls,n=e.parentElement,o=pe((0,C.B)(),1)[0],r=(0,i.wA)(),l=pe((0,i.d4)((function(e){return[m.A.getSelectedThumbnailPageIndexes(e),m.A.isElementDisabled(e,"documentControl"),m.A.getPageLabels(e),m.A.isThumbnailSelectingPages(e),m.A.getFeatureFlags(e)]})),5),c=l[0],u=l[1],s=l[2],d=l[3],h=l[4],g=fe(c,s),b=pe((0,a.useState)(g),2),v=b[0],w=b[1],x=pe((0,a.useState)(g),2),A=x[0],E=x[1],P=h.customizableUI;(0,a.useEffect)((function(){w(fe(c,s))}),[w,c,t,s]);var T=function(){r(p.A.setThumbnailSelectingPages(!0))};return u?null:a.createElement("div",{className:"documentControlsContainer","data-element":"documentControl"},t?a.createElement("div",{className:"documentControls"},a.createElement("div",{className:"divider"}),d&&a.createElement(me,{parentElement:n}),P&&a.createElement("label",{className:"documentControlsLabel",htmlFor:"pageNumbersInput"},a.createElement("span",null,o("option.thumbnailPanel.multiSelectPages")," -"),a.createElement("span",{className:"multiSelectExampleLabel"},o("option.thumbnailPanel.multiSelectPagesExample"))),a.createElement("div",{className:"documentControlsInput"},a.createElement("input",{name:"pageNumbersInput",onBlur:function(e){var t=e.target.value.replace(/ /g,""),n=t?(0,V.A)(t,s):[],o=n.map((function(e){return e-1}));if(n.length||!t){r(p.A.setSelectedPageThumbnails(o));var a=fe(c,s);w(a),E(a)}else w(A);c.length>0&&!d&&setTimeout((function(){T()}),100)},onChange:function(e){w(e.target.value)},value:v,placeholder:P?"":"1, 3, 5-10","aria-label":o("option.thumbnailPanel.enterPageNumbers"),className:"pagesInput",type:"text"}),a.createElement("div",{className:"documentControlsButton"},d?a.createElement(y.A,{img:"icon-close",title:"option.documentControls.closeTooltip",onClick:function(){r(p.A.setSelectedPageThumbnails([f.A.getCurrentPage()-1])),r(p.A.setThumbnailSelectingPages(!1))},dataElement:"thumbCloseMultiSelect"}):a.createElement(y.A,{img:"icon-tool-select-pages",title:"option.documentControls.selectTooltip",onClick:T,dataElement:"thumbMultiSelect"})))):null)};ge.propTypes={isDisabled:b().bool,pageLabels:b().arrayOf(b().string),toggleDocumentControl:b().func,shouldShowControls:b().bool};const be=ge;var ve=n(52080),ye=n(84879),we=n(15052),xe=n(33647),Ae=n(96763),Ee=function(e,t){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];return function(o){return o(p.A.openElement(x.A.LOADING_MODAL)),new Promise((function(r,a){f.A.mergeDocument(e,t).then((function(e){o(p.A.closeElement(x.A.LOADING_MODAL)),f.A.setCurrentPage(t),n&&(0,we.A)(xe.A.DOCUMENT_MERGED,e),r(e)})).catch((function(e){a(e),o(p.A.closeElement(x.A.LOADING_MODAL))}))}))}},Ce=n(43287);function Pe(e){return Pe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Pe(e)}function Te(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function ke(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Te(Object(n),!0).forEach((function(t){Se(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Te(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Se(e,t,n){return(t=function(e){var t=function(e){if("object"!=Pe(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=Pe(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==Pe(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ie(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,a,i,l=[],c=!0,u=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=a.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){u=!0,r=e}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(u)throw r}}return l}}(e,t)||function(e,t){if(e){if("string"==typeof e)return Ne(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Ne(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ne(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}n(24478);var Oe="dataTransferWebViewerFrame",Le="100",Re="1000",Me="50";const De=function(e){var t=e.panelSelector,n=e.parentDataElement,o=Ie((0,i.d4)((function(e){return[m.A.isElementOpen(e,"leftPanel"),m.A.isElementDisabled(e,"thumbnailsPanel"),m.A.getTotalPages(e),m.A.getCurrentPage(e),m.A.getSelectedThumbnailPageIndexes(e),m.A.getIsThumbnailMergingEnabled(e),m.A.getIsThumbnailReorderingEnabled(e),m.A.getIsMultipleViewerMerging(e),m.A.isElementDisabled(e,"thumbnailControl"),m.A.isElementDisabled(e,"thumbnailsSizeSlider"),m.A.isReaderMode(e),m.A.isDocumentReadOnly(e),m.A.getTotalPages(e,2),m.A.getActiveDocumentViewerKey(e),m.A.openingPageManipulationOverlayByRightClickEnabled(e),m.A.getFeatureFlags(e)]}),i.bN),16),u=o[0],h=o[1],g=o[2],b=o[3],v=o[4],w=o[5],A=o[6],E=o[7],P=o[8],T=o[9],k=o[10],S=o[11],I=o[12],N=o[13],O=o[14],L=o[15],M=Ie((0,C.B)(),1)[0],D=(0,a.useRef)(),_=(0,a.useRef)([]),j=(0,a.useRef)([]),z=(0,a.useRef)(null),B=Ie((0,a.useState)(!1),2),H=B[0],U=B[1],F=Ie((0,a.useState)(!0),2),G=F[0],V=F[1],q=Ie((0,a.useState)(0),2),Y=q[0],K=q[1],$=Ie((0,a.useState)(0),2),X=$[0],J=$[1],Q=Ie((0,a.useState)(null),2),Z=Q[0],ee=Q[1],te=Ie((0,a.useState)(!1),2),ne=te[0],oe=te[1],re=Ie((0,a.useState)(1),2),ae=re[0],ie=re[1],le=Ie((0,a.useState)(!1),2),ce=le[0],ue=le[1],se=Ie((0,a.useState)(150),2),de=se[0],me=se[1],pe=Ie((0,a.useState)(0),2),he=pe[0],fe=pe[1],ge=Ie((0,a.useState)(0),2),Pe=ge[0],Te=ge[1],Se=2===N?I:g,Ne=null==L?void 0:L.customizableUI,De=(0,i.wA)(),_e={},je=function(e){var t=j.current&&j.current[e]&&j.current[e].element;if(t){var n=e+1,o=f.A.getPageWidth(n),a=function(e,t){var n,o,r;return e>t?(r=e/de,n=de,o=Math.round(t/r)):(r=t/de,n=Math.round(e/r),o=de),{width:n,height:o}}(o,f.A.getPageHeight(n)),i=a.width,l=a.height,c=t.querySelector(".annotation-image")||document.createElement("canvas");c.className="annotation-image",c.role="img",c.ariaLabel="".concat(M("action.page")," ").concat(n),c.style.maxWidth="".concat(de,"px"),c.style.maxHeight="".concat(de,"px");var u=c.getContext("2d"),s=1,d=f.A.getCompleteRotation(n);d<0&&(d+=4);var m=window.Core.getCanvasMultiplier();d%2==0?(c.width=i,c.height=l,s=c.width/o,s/=m):(c.width=l,c.height=i,s=c.height/o,s/=m),t.appendChild(c),f.A.setAnnotationCanvasTransform(u,s,d);var p={pageNumber:n,overrideCanvas:c},h=t.querySelector(".page-image");h&&(p=ke(ke({},p),{},{overridePageRotation:d,overridePageCanvas:h}),_e[n]||(_e[n]=r()(f.A.drawAnnotations,112)),(0,_e[n])(p))}};if((0,a.useEffect)((function(){var e=function(){V(!1)},t=function(e){e||V(!0)},n=function(){var e;"officeEditor"===(null===(e=f.A.getDocument())||void 0===e?void 0:e.getType())?U(!0):U(!1),_e={},De(p.A.setSelectedPageThumbnails([]))},o=function(){z.current&&(f.A.setCurrentPage(z.current),z.current=null)};return f.A.addEventListener("beginRendering",e),f.A.addEventListener("finishedRendering",t),f.A.addEventListener("documentLoaded",n),f.A.addEventListener("pageComplete",o),f.A.getDocument()&&n(),function(){f.A.removeEventListener("beginRendering",e),f.A.removeEventListener("finishedRendering",t),f.A.removeEventListener("documentLoaded",n),f.A.removeEventListener("pageComplete",o)}}),[]),(0,a.useEffect)((function(){var e=function(e){if(e){var t=Array.from(v);e.removed&&(t=t.filter((function(t){return-1===e.removed.indexOf(t+1)}))),e.moved&&(t=t.map((function(t){return e.moved[t+1]?e.moved[t+1]-1:t})));var n=e.added&&e.added[0]-1<=t[0];1===t.length&&n&&(t=e.added.map((function(e){return e-1}))),De(p.A.setSelectedPageThumbnails(t))}};return f.A.addEventListener("pagesUpdated",e),function(){return f.A.removeEventListener("pagesUpdated",e)}}),[v]),(0,a.useEffect)((function(){var e;null===(e=D.current)||void 0===e||e.scrollToRow(Math.floor((b-1)/ae));var t=function(e){var t=[];e.forEach((function(e){var n=e.PageNumber-1;!e.Listable||t.indexOf(n)>-1||(t.push(n),je(n))}))},n=function(e){var t,n=e-1;null===(t=D.current)||void 0===t||t.scrollToRow(Math.floor(n/ae))};return f.A.addEventListener("pageNumberUpdated",n),f.A.addEventListener("annotationChanged",t),f.A.addEventListener("annotationHidden",t),function(){f.A.removeEventListener("pageNumberUpdated",n),f.A.removeEventListener("annotationChanged",t),f.A.removeEventListener("annotationHidden",t)}}),[de,ae]),(0,a.useEffect)((function(){(k||S)&&(De(p.A.setSelectedPageThumbnails([])),De(p.A.setThumbnailSelectingPages(!1)))}),[k,S]),h||H||!u&&!t&&!Ne)return null;var ze=function(){ue(!1),ee(null)},Be=function(e,t,n){var o,r=(new Date).getTime();return e<Se-1&&e>0&&r-he>=n?(null===(o=D.current)||void 0===o||o.scrollToRow(Math.floor((e+t)/ae)),fe(r),e+t):e},He=function(e,t){if(e.preventDefault(),e.stopPropagation(),A||w){var n=e.target.getBoundingClientRect();oe(ae>1?!(e.pageX>n.x+n.width/2):!(e.pageY>n.y+n.height/2)),ee(t);var o=(0,R.Ay)().querySelector("#virtualized-thumbnails-container").getBoundingClientRect(),r=o.y,a=o.bottom;e.pageY<r+100?Te(Be(t,-1,400)):e.pageY>a-100&&Te(Be(t,1,400))}},Ue=function(){Te(Be(Pe,1,200))},Fe=function(){Te(Be(Pe,-1,200))},Ge=function(e,t){Te(t),ue(!0);var n,o=v.some((function(e){return e===t})),r=o?v.map((function(e){return e+1})):[t+1];(0,we.A)(xe.A.THUMBNAIL_DRAGGED),e.dataTransfer.setData("text",""),r.length>1&&e.dataTransfer.setDragImage(new Image,0,0),w&&E&&(e.dataTransfer.dropEffect="move",e.dataTransfer.effectAllowed="all",e.dataTransfer.setData(Oe,window.frameElement.id),n=r,window.extractedDataPromise=(0,ye.A)(n),window.pagesExtracted=n),o||De(p.A.setSelectedPageThumbnails([t])),f.A.setCurrentPage(t+1)},We=function(e){e.preventDefault();var t,n=e.dataTransfer.files,o=ne?Z+1:Z+2;d.lw||(t=e.dataTransfer.getData(Oe));var r,a,i=t&&window.frameElement.id!==t||n.length,l=b-1;if(w&&i)t&&window.frameElement.id!==t?De((r=t,a=o,function(e){return new Promise((function(t,n){var o=window.parent.document.querySelector("#".concat(r));o||(Ae.warn("Could not find other instance of WebViewer"),n());var i=o.contentWindow.extractedDataPromise;i||(Ae.warn("Could not retrieve data from other instance of WebViewer"),n()),e(p.A.openElement(x.A.LOADING_MODAL)),i.then((function(n){e(Ee(n,a,!1)).then((function(n){var r=n.filename,a=n.pages;(0,we.A)(xe.A.DOCUMENT_MERGED,{filename:r,pages:o.contentWindow.pagesExtracted}),e(p.A.closeElement(x.A.LOADING_MODAL)),t({filename:r,pages:a})}))})).catch((function(t){e(p.A.closeElement(x.A.LOADING_MODAL)),n(t)}))}))})):n.length&&Array.from(n).forEach((function(e){De(Ee(e,o))}));else if(A&&!i&&null!==Z){var c=ne?Z+1:Z+2,u=v.some((function(e){return e===l}))?v.map((function(e){return e+1})):[b];z.current=c-u.filter((function(e){return e<c})).length,f.A.movePages(u,c);for(var s=[],m=0;m<u.length;m++)s.push(z.current+m);(0,we.A)(xe.A.THUMBNAIL_DROPPED,{pageNumbersBeforeMove:u,pagesNumbersAfterMove:s,numberOfPagesMoved:s.length})}ee(null),ue(!1)},Ve=function(e,t,n){Ye(e)||Ke(e)||(j.current[e]={element:t,loaded:!1},_.current.push({pageIndex:e,id:n}))},qe=function(e){var t=Xe(e);-1!==t&&_.current.splice(t,1)},Ye=function(e){var t;return null===(t=j.current[e])||void 0===t?void 0:t.loaded},Ke=function(e){return-1!==Xe(e)},$e=function(e){var t=Xe(e);-1!==t&&(f.A.cancelLoadThumbnail(_.current[t].id),_.current.splice(t,1))},Xe=function(e){return _.current.findIndex((function(t){return t.pageIndex===e}))},Je=function(e){var t,n;$e(e);var o=null===(t=j.current[e])||void 0===t||null===(n=t.element)||void 0===n?void 0:n.querySelectorAll("canvas");null!=o&&o.length&&o.forEach((function(e){e.height=0,e.width=0})),_e[e]&&_e[e].cancel(),j.current[e]=null},Qe=function(e){var n=e.index,o=e.key,r=e.style,i=s()({columnsOfThumbnails:ae>1,row:!0}),l=!(k||S);return a.createElement("div",{role:"row","aria-label":"row",className:i,key:o,style:r},new Array(ae).fill().map((function(e,o){var r=n*ae+o,i=l&&(w||A),c=i&&Z===r;return r<Se?a.createElement(a.Fragment,{key:r},(ae>1||0===r)&&c&&ne&&a.createElement("div",{key:"placeholder1-".concat(r),className:"thumbnailPlaceholder"}),a.createElement("div",{key:r,role:"cell",onDragEnd:ze,className:"cellThumbContainer",onContextMenu:function(e){return O&&(n=r,(t=e).preventDefault(),f.A.setCurrentPage(n+1),v.includes(n)||De(p.A.setSelectedPageThumbnails([n])),void(k||S||(De(p.A.setPageManipulationOverlayAlternativePosition({left:t.pageX,right:"auto",top:t.pageY})),De(p.A.openElements([x.A.PAGE_MANIPULATION_OVERLAY])))));var t,n}},a.createElement(W,{isDraggable:i,isSelected:v.includes(r),index:r,canLoad:G,onLoad:Ve,onCancel:$e,onRemove:Je,onDragStart:Ge,onDragOver:He,onFinishLoading:qe,updateAnnotations:je,shouldShowControls:l,thumbnailSize:de,panelSelector:t})),c&&!ne&&a.createElement("div",{key:"placeholder2-".concat(r),className:"thumbnailPlaceholder"})):null})))},Ze=function(){ie(Math.min(16,Math.max(1,Math.floor(X/de))))},et=P?Number(de)+50:Number(de)+80,tt=!(k||S),nt={height:"".concat(25,"px")},ot=Ce.bg,rt=function(e,t){var n=Number(t)*Re;n<100&&(n=100),me(n),Ze()};return a.createElement(a.Fragment,null,!T&&a.createElement("div",{"data-element":"thumbnailsSizeSlider",className:"thumbnail-slider-container"},a.createElement(y.A,{img:"icon-zoom-thumb-out",title:"action.zoomOut",hideTooltipShortcut:!0,onClick:function(){de-Me>Me&&(me(de-Me),Ze())},dataElement:"zoomThumbOutButton"}),Ne&&a.createElement(ve.A,{dataElement:"thumbnailsSizeSlider",property:"zoom",displayProperty:"zoom",min:Number(Le),max:Number(Re),value:de,getDisplayValue:function(){return de},customCircleRadius:8,customLineStrokeWidth:4,getCirclePosition:function(e,t){return t>1&&(t/=1e3),t*e+ot},convertRelativeCirclePositionToValue:function(e){return e},onSliderChange:rt,onStyleChange:rt,step:Number(Me),shouldHideSliderTitle:!0,shouldHideSliderValue:!0}),!Ne&&a.createElement("input",{role:"slider",type:"range","aria-label":"thumbnail size slider",min:Le,max:Re,value:de,"aria-valuemin":Le,"aria-valuemax":Re,"aria-valuenow":de,onChange:function(e){me(Number(e.target.value)),Ze()},step:Me,className:"thumbnail-slider",id:"thumbnailSize"}),a.createElement(y.A,{img:"icon-zoom-thumb-in",title:"action.zoomIn",hideTooltipShortcut:!0,onClick:function(){de+Number(Me)<1001&&(me(de+Number(Me)),Ze())},dataElement:"zoomThumbInButton"})),a.createElement(c.A,{bounds:!0,onResize:function(e){var t=e.bounds;K(t.height),J(t.width),ie(Math.min(16,Math.max(1,Math.floor(t.width/de))))},key:de},(function(e){var n=e.measureRef;return a.createElement("div",{className:"Panel ThumbnailsPanel ".concat(t),id:"virtualized-thumbnails-container","data-element":"thumbnailsPanel",onDrop:We,ref:n},a.createElement("div",{className:"virtualized-thumbnails-container"},ce?a.createElement("div",{className:"thumbnailAutoScrollArea",onDragOver:Fe,style:nt}):"",a.createElement(l.B8,{ref:D,height:Y,width:X,rowHeight:et,rowCount:Math.ceil(Se/ae),rowRenderer:Qe,overscanRowCount:3,className:"thumbnailsList",style:{outline:"none"},scrollToIndex:Math.floor((b-1)/ae),role:"grid","aria-label":M("component.thumbnailsPanel")}),ce?a.createElement("div",{className:"thumbnailAutoScrollArea",onDragOver:Ue,style:ke(ke({},nt),{},{bottom:"70px"})}):""))})),a.createElement(be,{shouldShowControls:tt,parentElement:n||t}))}},14077:(e,t,n)=>{(t=n(76314)(!1)).push([e.id,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .documentControlsContainer{margin-left:16px;margin-right:16px;margin-bottom:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .documentControlsContainer{margin-left:16px;margin-right:16px;margin-bottom:16px}}.documentControlsContainer .PageControlContainer{display:flex;background-color:var(--gray-2);justify-content:center;align-content:center;border-radius:4px}.documentControlsContainer .PageControlContainer .dropdown-menu{position:relative}.documentControlsContainer .PageControlContainer .dropdown-menu .indicator{position:absolute;bottom:1px;right:1px;width:0;height:0;border-left:4px solid transparent;border-right:4px solid transparent;border-top:4px solid #c4c4c4;transform:rotateY(0deg) rotate(315deg)}.documentControlsContainer .PageControlContainer button .Icon{height:21px;width:21px;color:var(--icon-color)}.documentControlsContainer .PageControlContainer .button-hover:hover{background:var(--view-header-button-hover);border-radius:4px}.documentControlsContainer .PageControlContainer .divider{height:20px;width:1px;background:var(--divider);margin:6px}",""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"},e.exports=t},24478:(e,t,n)=>{var o=n(85072),r=n(54391);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.id,r,""]]),o(r,{insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach((e=>o.push(e))),n.querySelectorAll("*").forEach((n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))})),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const r=t[o];if(0===o)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach((t=>{t.innerHTML=e.innerHTML}))};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1}),e.exports=r.locals||{}},30226:(e,t,n)=>{var o=n(85072),r=n(32651);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.id,r,""]]),o(r,{insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach((e=>o.push(e))),n.querySelectorAll("*").forEach((n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))})),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const r=t[o];if(0===o)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach((t=>{t.innerHTML=e.innerHTML}))};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1}),e.exports=r.locals||{}},32651:(e,t,n)=>{(t=n(76314)(!1)).push([e.id,':host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.thumbnailControls-overlay{display:grid;text-align:center;z-index:2;margin-top:5px;grid-template-areas:"rotate delete . more";grid-template-columns:repeat(3,1fr)}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.thumbnailControls-overlay{display:flex}}.thumbnailControls-overlay .Button{height:32px;padding:0;width:32px}.thumbnailControls-overlay .Button .Icon{height:24px;width:24px;color:var(--icon-color)}.thumbnailControls-overlay .Button:hover{background:var(--view-header-button-hover);border-radius:4px}.thumbnailControls-overlay .Button.active{background:var(--view-header-button-active)}.thumbnailControls-overlay .Button.active .Icon{color:var(--selected-icon-color)}.thumbnailControls-overlay.modular-ui .Button:hover{border:1px solid var(--focus-border);background:var(--tools-button-hover)}.thumbnailControls-overlay .rotate-button{grid-area:rotate}.thumbnailControls-overlay .delete-button{grid-area:delete}.thumbnailControls-overlay .more-options{grid-area:more}.thumbnailControls-overlay.custom-buttons .Button{grid-area:auto}.thumbnailControls{display:flex;flex-direction:row;text-align:center;z-index:2;margin-top:5px}.thumbnailControls .Button{height:32px;padding:0;width:32px;margin:0 4px}.thumbnailControls .Button .Icon{height:24px;width:24px;color:var(--icon-color)}.thumbnailControls .Button:hover{background:var(--view-header-button-hover);border-radius:4px}',""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"},e.exports=t},45622:(e,t,n)=>{var o=n(85072),r=n(64279);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.id,r,""]]),o(r,{insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach((e=>o.push(e))),n.querySelectorAll("*").forEach((n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))})),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const r=t[o];if(0===o)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach((t=>{t.innerHTML=e.innerHTML}))};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1}),e.exports=r.locals||{}},54391:(e,t,n)=>{(t=n(76314)(!1)).push([e.id,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.thumbnail-slider-container{display:flex;align-items:center;width:230px;margin:0 auto}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .thumbnail-slider-container{width:inherit;margin:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .thumbnail-slider-container{width:inherit;margin:16px}}.thumbnail-slider-container .thumbnail-slider{width:100%;height:20px;padding:0;color:transparent;background-color:transparent;border:0 transparent}.thumbnail-slider-container input[type=range]{-webkit-appearance:none;margin:10px 0;width:100%}.thumbnail-slider-container input[type=range]:focus{outline:none;box-shadow:0 0 5px var(--focus-border)}.thumbnail-slider-container input[type=range]::-webkit-slider-runnable-track{width:100%;height:3px;cursor:pointer;animate:.2s;box-shadow:0 0 0 var(--gray-12);background:var(--slider-filled);border-radius:5px;border:0 solid var(--gray-12)}.thumbnail-slider-container input[type=range]::-webkit-slider-thumb{box-shadow:0 0 1px var(--gray-12);border:0 solid var(--gray-12);height:15px;width:15px;border-radius:50px;background:var(--slider-filled);cursor:pointer;-webkit-appearance:none;margin-top:-5px}.thumbnail-slider-container input[type=range]:focus::-webkit-slider-runnable-track{background:var(--slider-filled)}.thumbnail-slider-container input[type=range]::-moz-range-track{width:100%;height:3px;cursor:pointer;animate:.2s;box-shadow:0 0 0 var(--gray-12);background:var(--slider-filled);border-radius:5px;border:0 solid var(--gray-12)}.thumbnail-slider-container input[type=range]::-moz-range-thumb{box-shadow:0 0 1px var(--gray-12);border:0 solid var(--gray-12);height:15px;width:15px;border-radius:50px;background:var(--slider-filled);cursor:pointer}.thumbnail-slider-container input[type=range]::-ms-track{padding:5px 0 0;width:100%;height:3px;background:transparent;border-color:transparent;color:transparent;cursor:pointer;border-width:6px 0;animate:.2s}.thumbnail-slider-container input[type=range]::-ms-fill-lower,.thumbnail-slider-container input[type=range]::-ms-fill-upper{background:var(--slider-filled);border:0 solid var(--gray-12);border-radius:10px;box-shadow:0 0 0 var(--gray-12)}.thumbnail-slider-container input[type=range]::-ms-thumb{box-shadow:0 0 1px var(--gray-12);border:0 solid var(--gray-12);height:15px;width:15px;border-radius:50%;background:var(--slider-filled);cursor:pointer}.thumbnail-slider-container input[type=range]:focus::-ms-fill-lower,.thumbnail-slider-container input[type=range]:focus::-ms-fill-upper{background:var(--slider-filled)}.thumbnail-slider-container Button{width:15px;height:15px;margin:2.5px;padding-top:6px}.thumbnail-slider-container Button:hover{background:var(--view-header-button-hover);border-radius:4px}.thumbnail-slider-container .slider{width:100%}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .thumbnail-slider-container .slider{margin-top:0}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .thumbnail-slider-container .slider{margin-top:0}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .thumbnail-slider-container .slider .slider-element-container{width:auto;margin-left:auto}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .thumbnail-slider-container .slider .slider-element-container{width:auto;margin-left:auto}}.ThumbnailsPanel{overflow:hidden!important;display:flex;height:100%}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ThumbnailsPanel{width:inherit;margin:0 16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .ThumbnailsPanel{width:inherit;margin:0 16px}}.ThumbnailsPanel #virtualized-thumbnails-container{flex:1}.ThumbnailsPanel .row{display:flex;justify-content:space-around;align-items:center;flex-direction:column}.ThumbnailsPanel .thumbnailPlaceholder{width:150px;margin:2px;border:1px solid var(--focus-border)}.ThumbnailsPanel .columnsOfThumbnails.row{display:flex;justify-content:left;align-items:center;flex-direction:row}.ThumbnailsPanel .columnsOfThumbnails .cellThumbContainer{display:flex;flex-direction:row}.ThumbnailsPanel .columnsOfThumbnails .Thumbnail{display:inline-flex}.ThumbnailsPanel .columnsOfThumbnails .thumbnailPlaceholder{width:116px;min-width:116px;height:150px;margin-bottom:30px}.thumbnailAutoScrollArea{position:absolute;width:calc(100% - 55px);z-index:10;background:hsla(0,0%,100%,0)}",""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"},e.exports=t},55698:(e,t,n)=>{var o=n(85072),r=n(14077);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.id,r,""]]),o(r,{insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach((e=>o.push(e))),n.querySelectorAll("*").forEach((n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))})),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const r=t[o];if(0===o)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach((t=>{t.innerHTML=e.innerHTML}))};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1}),e.exports=r.locals||{}},64279:(e,t,n)=>{(t=n(76314)(!1)).push([e.id,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.documentControls{display:flex;flex-direction:column}.documentControls .divider{height:1px;background:var(--divider);margin:16px 0 8px}.documentControls .documentControlsInput{display:flex;flex-direction:row;padding-bottom:16px;padding-top:8px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .documentControls .documentControlsInput{padding-bottom:0}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .documentControls .documentControlsInput{padding-bottom:0}}.documentControls .documentControlsInput.customizableUI{padding:8px 0}.documentControls .documentControlsInput .pagesInput{width:100%;height:30px;border:1px solid var(--border);border-radius:4px;color:var(--text-color);padding:4px 8px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .documentControls .documentControlsInput .pagesInput{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .documentControls .documentControlsInput .pagesInput{font-size:13px}}.documentControls .documentControlsInput .pagesInput:focus{outline:none;border:1px solid var(--focus-border)}.documentControls .documentControlsInput .pagesInput::-moz-placeholder{color:var(--placeholder-text)}.documentControls .documentControlsInput .pagesInput::placeholder{color:var(--placeholder-text)}.documentControls .documentControlsInput .documentControlsButton{display:flex;flex-direction:row;padding-left:2px}.documentControls .documentControlsInput .documentControlsButton .Button{height:30px;padding:0;width:30px;margin:0 4px}.documentControls .documentControlsInput .documentControlsButton .Button .Icon{height:24px;width:24px;color:var(--icon-color)}.documentControls .documentControlsInput .documentControlsButton .Button:hover{background:var(--view-header-button-hover);border-radius:4px}.documentControls .documentControlsLabel{margin-top:16px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .documentControls .documentControlsLabel{margin-top:8px;font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .documentControls .documentControlsLabel{margin-top:8px;font-size:13px}}.documentControls .documentControlsLabel .multiSelectExampleLabel{color:var(--faded-text);margin-left:2px}",""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"},e.exports=t},66794:(e,t,n)=>{var o=n(85072),r=n(79339);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.id,r,""]]),o(r,{insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach((e=>o.push(e))),n.querySelectorAll("*").forEach((n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))})),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const r=t[o];if(0===o)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach((t=>{t.innerHTML=e.innerHTML}))};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1}),e.exports=r.locals||{}},79339:(e,t,n)=>{(t=n(76314)(!1)).push([e.id,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.Thumbnail{display:flex;flex-direction:column;justify-content:center;align-items:center;text-align:center;cursor:pointer}.Thumbnail.active .container .page-image{border:2px solid var(--focus-border);box-shadow:none;box-sizing:content-box}.Thumbnail .container{position:relative;display:flex;justify-content:center;align-items:center;cursor:pointer}.Thumbnail .container .page-image{box-shadow:0 0 3px 0 var(--box-shadow)}.Thumbnail .container .annotation-image,.Thumbnail .container .page-image{position:absolute;left:50%;top:50%;transform:translate(-50%,-50%)}.Thumbnail .container .checkbox{position:absolute;border-radius:4px;z-index:4}.Thumbnail .container .default{top:3%;right:15%}.Thumbnail .container .rotated{top:15%;right:3%}.Thumbnail .page-label{margin-top:11px}.Thumbnail.selected .container .thumbnail:before{color:#fff;background:var(--focus-border);width:16px;height:16px;position:absolute;z-index:10}.Thumbnail.selected .container canvas{background:hsla(0,0%,100%,.6)}.Thumbnail.active .page-label{color:var(--focus-border)!important}",""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"},e.exports=t}}]);
//# sourceMappingURL=683.chunk.js.map