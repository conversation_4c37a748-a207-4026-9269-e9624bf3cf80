(self.webpackChunkwebviewer_ui=self.webpackChunkwebviewer_ui||[]).push([[9798],{4283:(e,t,n)=>{(t=n(76314)(!1)).push([e.id,".redaction-search-result{display:flex;align-items:center;padding:12px;background-color:var(--component-background);border:1px solid transparent;border-radius:4px;box-shadow:0 0 3px var(--document-box-shadow);margin:8px 0;cursor:pointer}.redaction-search-result .Icon svg{transform:scale(1.2);padding-top:2px}.redaction-search-result .search-value{word-break:break-all;color:var(--secondary-button-text);font-weight:700}.redaction-search-result.active{background-color:transparent!important;border:1px solid var(--focus-border)}.redaction-search-result-info{font-size:13px;color:var(--text-color)}",""]),e.exports=t},8804:(e,t,n)=>{"use strict";n.d(t,{A:()=>m}),n(52675),n(89463),n(2259),n(45700),n(28706),n(23792),n(89572),n(94170),n(2892),n(59904),n(84185),n(40875),n(10287),n(26099),n(60825),n(47764),n(62953);var o=n(96540),r=n(6251),a=n(41717),i=n(59844);function c(e){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},c(e)}function l(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(l=function(){return!!e})()}function s(e){return s=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},s(e)}function d(e,t){return d=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},d(e,t)}function u(e){var t=function(e){if("object"!=c(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=c(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==c(t)?t:t+""}var p=function(e){function t(){var e,n,o,r;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);for(var i=arguments.length,d=new Array(i),p=0;p<i;p++)d[p]=arguments[p];return e=function(e,t,n){return t=s(t),function(e,t){if(t&&("object"==c(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,l()?Reflect.construct(t,n||[],s(e).constructor):t.apply(e,n))}(this,t,[].concat(d)),n=e,r=function(t){return(0,a.A)({nonce:t,container:e.props.container})},(o=u(o="createEmotionCache"))in n?Object.defineProperty(n,o,{value:r,enumerable:!0,configurable:!0,writable:!0}):n[o]=r,e}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&d(e,t)}(t,e),n=t,Object.defineProperty(n,"prototype",{writable:!1}),n;var n}(r.he);const m=function(e){var t=e.children;return window.isApryseWebViewerWebComponent?o.createElement(p,{container:(0,i.Ay)()},t):t}},12035:(e,t,n)=>{(t=n(76314)(!1)).push([e.id,':host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.redaction-search-counter-controls{display:flex;flex-direction:row;margin-top:36px;font-size:var(--font-size-default);padding:16px;border:1px solid var(--lighter-border);background-color:var(--gray-0);border-radius:4px 4px 0 0;max-height:50px;min-height:50px;grid-column-gap:var(--padding-medium);-moz-column-gap:var(--padding-medium);column-gap:var(--padding-medium)}.redaction-search-counter-controls .redaction-search-results-counter{flex:2 1 auto}.redaction-search-counter-controls .redaction-search-results-counter span{font-weight:700}.redaction-search-counter-controls .spinner{margin:auto;flex:3 1 "25px"}.redaction-search-counter-controls button{padding:0;background-color:transparent;flex:1 1 auto;color:var(--secondary-button-text);border:none;cursor:pointer;height:100%;white-space:nowrap}:host(:not([data-tabbing=true])) .redaction-search-counter-controls button,html:not([data-tabbing=true]) .redaction-search-counter-controls button{outline:none}.redaction-search-counter-controls button:hover:not(:disabled){color:var(--secondary-button-hover)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .redaction-search-counter-controls button{font-size:var(--font-size-default)}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .redaction-search-counter-controls button{font-size:var(--font-size-default)}}.redaction-search-counter-controls button.disabled{opacity:.5}.redaction-search-counter-controls button.disabled span{color:var(--secondary-button-text)}.redaction-search-results-container{flex:1 1 auto;background-color:var(--gray-2);color:var(--faded-text);font-size:13px;border-left:1px solid var(--lighter-border);border-right:1px solid var(--lighter-border);display:flex;flex-direction:column}.redaction-search-no-results,.redaction-search-results-container.emptyList{justify-content:center;align-items:center}.redaction-search-panel-controls{display:flex;flex-direction:row;flex:0 1 52px;padding:12px;background-color:var(--component-background);border:1px solid var(--lighter-border);margin-bottom:16px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .redaction-search-panel-controls{margin-bottom:30px;font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .redaction-search-panel-controls{margin-bottom:30px;font-size:13px}}.redaction-search-panel-controls button{border:none;background-color:transparent;height:28px;padding:0 16px;cursor:pointer}:host(:not([data-tabbing=true])) .redaction-search-panel-controls button,html:not([data-tabbing=true]) .redaction-search-panel-controls button{outline:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .redaction-search-panel-controls button{height:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .redaction-search-panel-controls button{height:32px}}.redaction-search-panel-controls .Button{white-space:nowrap}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .redaction-search-panel-controls .Button{font-size:var(--font-size-default)}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .redaction-search-panel-controls .Button{font-size:var(--font-size-default)}}.redaction-search-panel-controls .Button.cancel{flex:2 1 auto;color:var(--secondary-button-text);border:none;cursor:pointer;margin-right:20px}.redaction-search-panel-controls .Button.cancel:hover:not(.disabled) span{color:var(--secondary-button-hover)}.redaction-search-panel-controls .Button.redact-all-selected{flex:1 1 auto;border:1px solid var(--secondary-button-text);border-radius:4px;margin-right:8px}.redaction-search-panel-controls .Button.redact-all-selected span{color:var(--secondary-button-text)}.redaction-search-panel-controls .Button.redact-all-selected.disabled{opacity:.5}.redaction-search-panel-controls .Button.redact-all-selected:hover:not(.disabled){border-color:var(--secondary-button-hover)}.redaction-search-panel-controls .Button.redact-all-selected:hover:not(.disabled) span{color:var(--secondary-button-hover)}.redaction-search-panel-controls .Button.mark-all-selected{flex:2 1 auto;background-color:var(--primary-button)!important;border:1px solid var(--primary-button);border-radius:4px}.redaction-search-panel-controls .Button.mark-all-selected span{color:var(--primary-button-text)}.redaction-search-panel-controls .Button.mark-all-selected:hover:not(.disabled){border-color:var(--primary-button-hover);background-color:var(--primary-button-hover)!important}.redaction-search-panel-controls .Button.mark-all-selected.disabled{opacity:.5}',""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"},e.exports=t},15493:(e,t,n)=>{(t=n(76314)(!1)).push([e.id,".redaction-item{display:flex;align-items:center;padding:12px 16px}.redaction-item:hover{background-color:var(--view-header-button-hover);cursor:pointer}.redaction-item.focus-visible,.redaction-item:focus-visible{outline:var(--focus-visible-outline)}.redaction-item.modular-ui:hover:not(:disabled):not(.disabled){background-color:transparent;box-shadow:inset 0 0 0 1px var(--hover-border)}.redaction-item-selected{background-color:var(--view-header-button-active)!important}.redaction-item-selected.modular-ui{box-shadow:inset 0 0 0 1px var(--focus-border)}.redaction-item-info{flex:1;padding-left:18px;padding-right:20px}.redaction-item-preview{font-size:13px;color:var(--text-color)}.redaction-item-date-author{font-size:10px;color:var(--faded-text)}.redaction-item-label-text{font-size:10px;margin:2px 0}.redaction-item-delete.customUI:hover{box-shadow:inset 0 0 0 1px var(--hover-border)}",""]),e.exports=t},21424:(e,t,n)=>{var o=n(85072),r=n(82933);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.id,r,""]]),o(r,{insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach((e=>o.push(e))),n.querySelectorAll("*").forEach((n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))})),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const r=t[o];if(0===o)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach((t=>{t.innerHTML=e.innerHTML}))};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1}),e.exports=r.locals||{}},28608:(e,t,n)=>{var o=n(85072),r=n(4283);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.id,r,""]]),o(r,{insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach((e=>o.push(e))),n.querySelectorAll("*").forEach((n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))})),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const r=t[o];if(0===o)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach((t=>{t.innerHTML=e.innerHTML}))};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1}),e.exports=r.locals||{}},35757:(e,t,n)=>{(t=n(76314)(!1)).push([e.id,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .redaction-search-multi-select{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .redaction-search-multi-select{font-size:13px}}.redaction-search-multi-select-search-icon-container{height:28px;align-self:flex-start;display:flex;align-items:center;margin:0 var(--padding-tiny)}.redaction-search-multi-select-search-icon-container .Icon{width:16px;height:16px}",""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"},e.exports=t},49006:(e,t,n)=>{var o=n(85072),r=n(73139);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.id,r,""]]),o(r,{insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach((e=>o.push(e))),n.querySelectorAll("*").forEach((n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))})),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const r=t[o];if(0===o)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach((t=>{t.innerHTML=e.innerHTML}))};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1}),e.exports=r.locals||{}},52890:(e,t,n)=>{var o=n(85072),r=n(35757);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.id,r,""]]),o(r,{insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach((e=>o.push(e))),n.querySelectorAll("*").forEach((n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))})),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const r=t[o];if(0===o)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach((t=>{t.innerHTML=e.innerHTML}))};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1}),e.exports=r.locals||{}},53303:(e,t,n)=>{(t=n(76314)(!1)).push([e.id,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionSearchOverlay{margin-left:16px;margin-right:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionSearchOverlay{margin-left:16px;margin-right:16px}}.RedactionSearchOverlay input{width:100%;padding:6px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionSearchOverlay .creatable-multi-select-label{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionSearchOverlay .creatable-multi-select-label{font-size:13px}}",""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"},e.exports=t},54030:(e,t,n)=>{"use strict";n.d(t,{A:()=>d}),n(26099),n(3362);var o=n(28854),r=n(47127),a=n(26247),i=n(15052),c=n(73086),l=n(96763);function s(){}const d=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:s,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;return function(r){return o.A.isWebViewerServerDocument()?u(e,r,n):p(e,t,r,n)}};var u=function(e,t,n){return o.A.applyRedactions(e,n).then((function(e){if(e&&e.url)return(0,c.A)(t,{filename:"redacted.pdf",includeAnnotations:!0,externalURL:e.url});l.warn("WebViewer Server did not return a valid result")}))},p=function(e,t,n,c){var l={message:r.A.t("warning.redaction.applyMessage"),title:r.A.t("warning.redaction.applyTile"),confirmBtnText:r.A.t("action.apply"),onConfirm:function(){return o.A.applyRedactions(e,c).then((function(){t()})).catch((function(e){return(0,i.R)(e)})),Promise.resolve()}};return n(a.A.showWarningMessage(l))}},54546:(e,t,n)=>{var o=n(85072),r=n(53303);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.id,r,""]]),o(r,{insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach((e=>o.push(e))),n.querySelectorAll("*").forEach((n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))})),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const r=t[o];if(0===o)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach((t=>{t.innerHTML=e.innerHTML}))};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1}),e.exports=r.locals||{}},58375:(e,t,n)=>{(t=n(76314)(!1)).push([e.id,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.RedactionPanel{padding:16px 16px 0;display:flex;flex-direction:column}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel{width:100%;height:100%;min-width:100%;padding:8px 0 0}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .close-container{display:flex;align-items:center;justify-content:flex-end;height:32px;width:100%;padding-right:12px}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .close-container .close-icon-container{padding:0;border:none;background-color:transparent;z-index:3;cursor:pointer}:host(:not([data-tabbing=true])) .App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .close-container .close-icon-container,html:not([data-tabbing=true]) .App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .close-container .close-icon-container{outline:none}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .close-container .close-icon-container .close-icon{width:24px;height:24px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel{width:100%;height:100%;min-width:100%;padding:8px 0 0}.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .close-container{display:flex;align-items:center;justify-content:flex-end;height:32px;width:100%;padding-right:12px}.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .close-container .close-icon-container{padding:0;border:none;background-color:transparent;z-index:3;cursor:pointer}:host(:not([data-tabbing=true])) .App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .close-container .close-icon-container,html:not([data-tabbing=true]) .App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .close-container .close-icon-container{outline:none}.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .close-container .close-icon-container .close-icon{width:24px;height:24px}}.RedactionPanel .marked-redaction-counter{flex:0 1 19px;margin-top:24px;margin-bottom:12px;font-size:16px}.RedactionPanel .marked-redaction-counter span{font-weight:700}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .marked-redaction-counter{margin:16px;font-size:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .marked-redaction-counter{margin:16px;font-size:16px}}.RedactionPanel .no-marked-redactions{display:flex;flex-direction:column;align-items:center;flex:1 1 auto}.RedactionPanel .no-marked-redactions .msg{text-align:center}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .no-marked-redactions .msg{line-height:15px;width:146px}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .no-marked-redactions .msg{line-height:15px;width:146px}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .no-marked-redactions .msg{line-height:15px;width:146px;margin-bottom:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .no-marked-redactions .msg{line-height:15px;width:146px;margin-bottom:32px}}.RedactionPanel .no-marked-redactions .empty-icon,.RedactionPanel .no-marked-redactions .empty-icon svg{width:65px;height:83px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .no-marked-redactions .empty-icon,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .no-marked-redactions .empty-icon svg{width:60px;height:60px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .no-marked-redactions .empty-icon,.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .no-marked-redactions .empty-icon svg{width:60px;height:60px}}.RedactionPanel .no-marked-redactions .empty-icon *{fill:var(--gray-5);color:var(--gray-5)}.RedactionPanel .redaction-panel-controls{flex:0 0 57px;margin-left:-16px;padding-top:13px;border-top:1px solid var(--divider);display:flex;background-color:var(--component-background);width:inherit;justify-content:flex-end;padding-right:16px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .redaction-panel-controls{margin:0 0 16px;padding:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .redaction-panel-controls{margin:0 0 16px;padding:16px}}.RedactionPanel .redaction-panel-controls .Button.redact-all-marked{padding:0;border:none;background-color:transparent;background-color:var(--primary-button);color:var(--primary-button-text);border-radius:4px;height:32px;width:90px}:host(:not([data-tabbing=true])) .RedactionPanel .redaction-panel-controls .Button.redact-all-marked,html:not([data-tabbing=true]) .RedactionPanel .redaction-panel-controls .Button.redact-all-marked{outline:none}.RedactionPanel .redaction-panel-controls .Button.redact-all-marked:hover:not(.disabled){background-color:var(--primary-button-hover)}.RedactionPanel .redaction-panel-controls .Button.redact-all-marked.disabled{opacity:.5}.RedactionPanel .redaction-panel-controls .Button.redact-all-marked.disabled span{color:var(--primary-button-text)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .redaction-panel-controls .Button.redact-all-marked{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .redaction-panel-controls .Button.redact-all-marked{font-size:13px}}.RedactionPanel .redaction-panel-controls .clear-all-marked{padding:0;color:var(--secondary-button-text);background-color:transparent;border:none;height:32px;width:70px;margin-right:8px;cursor:pointer}:host(:not([data-tabbing=true])) .RedactionPanel .redaction-panel-controls .clear-all-marked,html:not([data-tabbing=true]) .RedactionPanel .redaction-panel-controls .clear-all-marked{outline:none}.RedactionPanel .redaction-panel-controls .clear-all-marked:hover:not(.disabled){color:var(--secondary-button-hover)}.RedactionPanel .redaction-panel-controls .clear-all-marked.disabled{opacity:.5;cursor:not-allowed}.RedactionPanel .redaction-panel-controls .clear-all-marked.disabled span{color:var(--secondary-button-text)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .redaction-panel-controls .clear-all-marked{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .redaction-panel-controls .clear-all-marked{font-size:13px}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .redaction-panel-controls{left:0;margin-bottom:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .redaction-panel-controls{left:0;margin-bottom:16px}}.RedactionPanel .redaction-group-container{flex:1 1 auto}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .redaction-group-container{margin-right:4px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .redaction-group-container{margin-right:4px}}.RedactionPanel button.focus-visible,.RedactionPanel button:focus-visible{outline:var(--focus-visible-outline)}.ModularPanel-container .RedactionPanel{height:100%;padding:unset}.ModularPanel-container .RedactionPanel .redaction-panel-controls{margin-right:-16px;padding-bottom:16px}",""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"},e.exports=t},61326:(e,t,n)=>{var o=n(85072),r=n(58375);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.id,r,""]]),o(r,{insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach((e=>o.push(e))),n.querySelectorAll("*").forEach((n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))})),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const r=t[o];if(0===o)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach((t=>{t.innerHTML=e.innerHTML}))};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1}),e.exports=r.locals||{}},67511:(e,t,n)=>{(t=n(76314)(!1)).push([e.id,".redaction-search-results-page-number{display:flex;align-items:start;padding:8px 12px 4px}.redaction-search-results-page-number .redaction-search-results-page-number-checkbox{position:absolute;left:24px}.redaction-search-results-page-number .collapsible-page-group-header button{font-size:13px;font-weight:400;color:var(--faded-text);margin:0 0 0 32px;width:calc(100% - 32px)}",""]),e.exports=t},73139:(e,t,n)=>{(t=n(76314)(!1)).push([e.id,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.redaction-items{margin:8px 2px 1px;background-color:var(--component-background);box-shadow:0 0 3px 0 var(--box-shadow);border-radius:4px}.redaction-items>:first-child{padding-top:16px;border-radius:4px 4px 0 0}.redaction-items>:last-child{padding-bottom:16px;border-radius:0 0 4px 4px}.redaction-items>:only-child{padding-top:16px;padding-bottom:16px;border-radius:4px}.redaction-page-group{padding-top:12px;padding-bottom:12px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .redaction-page-group{padding-top:8px;padding-right:4px;padding-left:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .redaction-page-group{padding-top:8px;padding-right:4px;padding-left:16px}}.redaction-page-group h2{margin:0}.redaction-page-group h2 button{margin:0;font-size:13px;font-weight:400;color:var(--faded-text)}.redaction-page-group-header{display:flex;justify-content:space-between;align-items:baseline}.expand-arrow{height:16px;width:16px;display:flex;align-items:center;cursor:pointer}.expand-arrow .Icon{width:12px;height:12px}.expand-arrow.Button.custom-ui.icon-only:hover{box-shadow:inset 0 0 0 1px var(--hover-border)}",""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"},e.exports=t},79798:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>st}),n(52675),n(89463),n(2259),n(45700),n(2008),n(51629),n(23418),n(64346),n(23792),n(72712),n(34782),n(89572),n(23288),n(62010),n(2892),n(67945),n(84185),n(83851),n(81278),n(79432),n(26099),n(27495),n(38781),n(47764),n(23500),n(62953),n(76031);var o=n(96540),r=n(5556),a=n.n(r),i=(n(28706),n(94170),n(69085),n(18492)),c=n(46942),l=n.n(c),s=n(11623),d=n(38618),u=(n(48980),n(28854));function p(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,a,i,c=[],l=!0,s=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(o=a.call(n)).done)&&(c.push(o.value),c.length!==t);l=!0);}catch(e){s=!0,r=e}finally{try{if(!l&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw r}}return c}}(e,t)||function(e,t){if(e){if("string"==typeof e)return m(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?m(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function m(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}var f=o.createContext(),h=function(e){var t=e.children,n=p((0,o.useState)(null),2),r=n[0],a=n[1],i=p((0,o.useState)(!1),2),c=i[0],l=i[1],s=p((0,o.useState)(-1),2),d=s[0],m=s[1];(0,o.useEffect)((function(){var e=function(e,t){if("selected"===t){var n=e.filter((function(e){return"Redact"===e.Subject})),o=n.length>0?n[0].Id:null;a(o)}else a(null)},t=function(e){if(e){var t=(u.A.getPageSearchResults()||[]).findIndex((function(t){return u.A.isSearchResultEqual(t,e)}));m(t)}};return u.A.addEventListener("annotationSelected",e),u.A.addEventListener("activeSearchResultChanged",t),function(){u.A.removeEventListener("annotationSelected",e),u.A.removeEventListener("activeSearchResultChanged",t)}}),[]);var h={selectedRedactionItemId:r,setSelectedRedactionItemId:a,isRedactionSearchActive:c,setIsRedactionSearchActive:l,activeSearchResultIndex:d};return o.createElement(f.Provider,{value:h},t)},b=n(90776),y=n(51e3),g=(n(61326),n(62062),n(61113)),v=n(75710),x=n(4186),w=n(74353),A=n.n(w),S=(n(98714),n(87440));function E(){return E=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},E.apply(null,arguments)}function R(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}const k=function(e){var t,n,r=(t=(0,g.d4)((function(e){return[v.A.getRedactionPanelWidth(e)]}),g.bN),n=1,function(e){if(Array.isArray(e))return e}(t)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,a,i,c=[],l=!0,s=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(o=a.call(n)).done)&&(c.push(o.value),c.length!==t);l=!0);}catch(e){s=!0,r=e}finally{try{if(!l&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw r}}return c}}(t,n)||function(e,t){if(e){if("string"==typeof e)return R(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?R(e,t):void 0}}(t,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())[0];return o.createElement(S.A,E({},e,{panelWidth:r,comment:!0}))};function O(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}var P=function(e){var t,n,r=(t=(0,g.d4)((function(e){var t;return[null===(t=v.A.getFeatureFlags(e))||void 0===t?void 0:t.customizableUI]})),n=1,function(e){if(Array.isArray(e))return e}(t)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,a,i,c=[],l=!0,s=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(o=a.call(n)).done)&&(c.push(o.value),c.length!==t);l=!0);}catch(e){s=!0,r=e}finally{try{if(!l&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw r}}return c}}(t,n)||function(e,t){if(e){if("string"==typeof e)return O(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?O(e,t):void 0}}(t,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())[0],a=e.iconColor,c=e.annotation,d=e.author,u=e.dateFormat,p=e.language,m=e.onRedactionItemDelete,f=e.onRedactionItemSelection,h=e.textPreview,w=e.isSelected,S=e.timezone,E=(0,i.B)().t,R=(0,x.A)(c);if(S){var P=R.toLocaleString("en-US",{timeZone:S});R=new Date(P)}var j,C=R?A()(R).locale(p).format(u):E("option.notesPanel.noteContent.noDate"),T="".concat(d," - ").concat(C),I=l()("redaction-item",{"redaction-item-selected":w},{"modular-ui":r}),N=c.label,_=c.icon,z=void 0===_?"icon-form-field-text":_,M=c.redactionType;return j=M===b._E.TEXT?o.createElement(k,{linesToBreak:2},h):M===b._E.FULL_PAGE||M===b._E.FULL_VIDEO_FRAME||M===b._E.REGION||M===b._E.AUDIO_REDACTION||M===b._E.FULL_VIDEO_FRAME_AND_AUDIO?E(N):c.getContents(),o.createElement("div",{role:"listitem",className:I,onClick:f,onKeyUp:function(e){"Enter"===e.key&&f()},tabIndex:0},o.createElement("div",{className:"redaction-icon-container"},o.createElement(s.A,{glyph:z,color:a})),o.createElement("div",{className:"redaction-item-info"},o.createElement("div",{className:"redaction-item-preview"},j),c.OverlayText?o.createElement("div",{className:"redaction-item-label-text"},E("option.stylePopup.labelText"),": ",c.OverlayText):null,o.createElement("div",{className:"redaction-item-date-author"},T)),o.createElement(y.A,{className:"redaction-item-delete",style:{marginLeft:"auto"},img:"icon-close",onClick:m,ariaLabel:"".concat(j," ").concat(T," ").concat(E("action.delete")," ")}))};const j=o.memo(P);function C(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}const T=function(e){var t,n,r=e.annotation,a=(0,o.useContext)(f),i=a.selectedRedactionItemId,c=a.setSelectedRedactionItemId,l=(t=(0,g.d4)((function(e){return[v.A.getNoteDateFormat(e),v.A.getCurrentLanguage(e),v.A.getCustomNoteSelectionFunction(e),v.A.getTimezone(e)]}),g.bN),n=4,function(e){if(Array.isArray(e))return e}(t)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,a,i,c=[],l=!0,s=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(o=a.call(n)).done)&&(c.push(o.value),c.length!==t);l=!0);}catch(e){s=!0,r=e}finally{try{if(!l&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw r}}return c}}(t,n)||function(e,t){if(e){if("string"==typeof e)return C(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?C(e,t):void 0}}(t,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),s=l[0],d=l[1],p=l[2],m=l[3],h=r.getCustomData("trn-annot-preview"),b=r.StrokeColor.toString(),y=u.A.getDisplayAuthor(r.Author),x=(0,o.useCallback)((function(){p&&p(r),u.A.deselectAllAnnotations(),u.A.selectAnnotation(r),u.A.jumpToAnnotation(r),c(r.Id)}),[r]),w=(0,o.useCallback)((function(){u.A.deleteAnnotations([r])}),[r]);return o.createElement(j,{dateFormat:s,language:d,timezone:m,author:y,annotation:r,iconColor:b,textPreview:h,onRedactionItemDelete:w,onRedactionItemSelection:x,isSelected:i===r.Id})};var I=n(97902),N=(n(49006),function(e){var t=e.pageNumber,n=e.redactionItems,r=(0,i.B)().t;return o.createElement(I.A,{className:"redaction-page-group",header:function(){return"".concat(r("option.shared.page")," ").concat(t)},expansionDescription:"".concat(r("option.shared.page")," ").concat(t," ").concat(r("redactionPanel.redactionItems")),headingLevel:2},o.createElement("div",{role:"list",className:"redaction-items"},n.map((function(e){return o.createElement(T,{annotation:e,key:"".concat(e.Id,"-").concat(t)})}))))});N.propTypes={pageNumber:a().oneOfType([a().number,a().string]),redactionItems:a().array};const _=N;var z=n(56478);function M(){return M=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},M.apply(null,arguments)}function D(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}const L=function(e){var t,n,r=e.redactionItems,a=(t=(0,o.useState)([]),n=2,function(e){if(Array.isArray(e))return e}(t)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,a,i,c=[],l=!0,s=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(o=a.call(n)).done)&&(c.push(o.value),c.length!==t);l=!0);}catch(e){s=!0,r=e}finally{try{if(!l&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw r}}return c}}(t,n)||function(e,t){if(e){if("string"==typeof e)return D(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?D(e,t):void 0}}(t,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),i=a[0],c=a[1];return(0,o.useEffect)((function(){c((0,z.Qk)().position.getSortedNotes(r))}),[r]),o.createElement(_,M({redactionItems:i},e))};function H(){return H=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},H.apply(null,arguments)}function B(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,a,i,c=[],l=!0,s=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(o=a.call(n)).done)&&(c.push(o.value),c.length!==t);l=!0);}catch(e){s=!0,r=e}finally{try{if(!l&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw r}}return c}}(e,t)||W(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function W(e,t){if(e){if("string"==typeof e)return F(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?F(e,t):void 0}}function F(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}const U=function(e){var t=e.redactionAnnotations,n=e.applyAllRedactions,r=e.deleteAllRedactionAnnotations,a=e.redactionTypesDictionary,c=(0,i.B)().t,u=B((0,o.useState)({}),2),p=u[0],m=u[1],h=B((0,o.useState)([]),2),g=h[0],v=h[1],x=(0,o.useContext)(f).isTestMode;(0,o.useEffect)((function(){var e={};t.forEach((function(t){var n=(0,b.s8)(t),o=a[n]||{icon:"icon-tool-redaction-area",label:"redactionPanel.redactionItem.regionRedaction"},r=o.label,i=o.icon;t.label=r,t.icon=i,t.redactionType=n;var c,l=t.PageNumber;void 0===e[l]?e[l]=[t]:e[l]=[t].concat(function(e){if(Array.isArray(e))return F(e)}(c=e[l])||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(c)||W(c)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())})),m(e),v(Object.keys(e))}),[t]);var w,A=o.createElement("div",{className:"no-marked-redactions"},o.createElement("div",null,o.createElement(s.A,{className:"empty-icon",glyph:"icon-no-marked-redactions"})),o.createElement("div",{className:"msg"},c("redactionPanel.noMarkedRedactions"))),S=l()("redact-all-marked",{disabled:0===t.length}),E=l()("clear-all-marked",{disabled:0===t.length});return o.createElement(o.Fragment,null,o.createElement("div",{className:"marked-redaction-counter"},o.createElement("span",null,c("redactionPanel.redactionCounter"))," ","(".concat(t.length,")")),g.length>0?(w=x?{initialItemCount:g.length}:{},o.createElement("div",{className:"redaction-group-container"},o.createElement(d.aY,H({data:g,itemContent:function(e,t){return o.createElement(L,{key:e,pageNumber:t,redactionItems:p[t]})}},w)))):A,o.createElement("div",{className:"redaction-panel-controls"},o.createElement(y.A,{disabled:0===t.length,className:E,onClick:r,label:c("redactionPanel.clearMarked")},c("redactionPanel.clearMarked")),o.createElement(y.A,{disabled:0===t.length,className:S,onClick:n,label:c("redactionPanel.redactAllMarked")},c("redactionPanel.redactAllMarked"))))};var q=n(26247),V=n(54030),$=n(815),G=n(21012),Y=(n(16034),n(25276),n(23203)),X=n(96729),Q=n(8804),K=(n(21424),["id","label"]);function Z(){return Z=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},Z.apply(null,arguments)}var J=function(e){var t=e.id,n=e.label,r=function(e,t){if(null==e)return{};var n,o,r=function(e,t){if(null==e)return{};var n={};for(var o in e)if({}.hasOwnProperty.call(e,o)){if(-1!==t.indexOf(o))continue;n[o]=e[o]}return n}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)n=a[o],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}(e,K);return o.createElement(Q.A,null,o.createElement("label",{htmlFor:t,className:"creatable-multi-select-label"},n),o.createElement(X.Ay,Z({isMulti:!0},r,{inputId:t})))};J.propTypes={id:a().string,label:a().string},J.defaultProps={id:"",label:""};const ee=J;var te=n(30107),ne=(n(52890),["children"]);function oe(e){return oe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},oe(e)}function re(){return re=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},re.apply(null,arguments)}function ae(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function ie(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ae(Object(n),!0).forEach((function(t){ce(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ae(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ce(e,t,n){return(t=function(e){var t=function(e){if("object"!=oe(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=oe(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==oe(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var le=function(e,t,n){return arguments.length>3&&void 0!==arguments[3]&&!arguments[3]?"transparent":e?t:n},se=function(e){var t=e.data,n=(0,i.B)().t;return o.createElement(Y.F.Option,e,t.icon&&o.createElement(s.A,{glyph:t.icon}),n(t.label))};se.propTypes={data:a().object.isRequired};var de=function(e){var t=e.data,n=(0,i.B)().t;return o.createElement("div",{style:{display:"flex",height:"18px"}},t.icon&&o.createElement(s.A,{glyph:t.icon}),n(t.label))};de.propTypes={data:a().object.isRequired};var ue=function(e){var t=e.children,n=function(e,t){if(null==e)return{};var n,o,r=function(e,t){if(null==e)return{};var n={};for(var o in e)if({}.hasOwnProperty.call(e,o)){if(-1!==t.indexOf(o))continue;n[o]=e[o]}return n}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)n=a[o],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}(e,ne);return o.createElement(Y.F.Control,n,o.createElement("div",{className:"redaction-search-multi-select-search-icon-container"},o.createElement(s.A,{className:"redaction-search-multi-select-search-icon",glyph:"icon-header-search"})),t)};ue.propTypes={children:a().node};var pe=function(e){var t,n=(0,i.B)().t,r=e.activeTheme,a=e.redactionSearchOptions,c=[{label:n("redactionPanel.search.pattern"),options:a}],l=(t="dark"===r,{groupHeading:function(e){return ie(ie({},e),{},{textTransform:"none",fontSize:"13px",fontWeight:"bold",color:le(t,te.zb.white,te.kh["text-color"]),paddingBottom:"8px",paddingLeft:"8px",paddingTop:"10px"})},group:function(e){return ie(ie({},e),{},{padding:"0px"})},menu:function(e){return ie(ie({},e),{},{padding:"0px 0px 0px 0px",borderRadius:"4px",overflowY:"visible",margin:"0"})},menuList:function(e){return ie(ie({},e),{},{padding:"0px",backgroundColor:le(t,te.zb.black,te.zb.gray0),overflowY:"visible",borderRadius:"4px"})},multiValue:function(e){return ie(ie({},e),{},{backgroundColor:le(t,te.zb.blue1DarkMode,te.zb.gray2),padding:"2px 8px",fontSize:"13px",borderRadius:"4px",overflowY:"hidden",whiteSpace:"nowrap",color:le(t,te.zb.white,te.kh["text-color"])})},multiValueRemove:function(e){return ie(ie({},e),{},{color:te.zb.gray6,borderRadius:"4px",marginLeft:"4px",padding:"0px","&:hover":{backgroundColor:te.zb.gray2,boxShadow:"inset 0 0 0 1px ".concat(te.zb.blue6),color:te.zb.gray6},svg:{height:"16px",width:"16px"}})},option:function(e){return ie(ie({},e),{},{display:"flex",fontSize:"13px",padding:"6px 8px 0","&:hover":{backgroundColor:le(t,te.zb.blue1DarkMode,te.kh["primary-button-hover"]),color:te.zb.gray0},backgroundColor:le(t,te.zb.blue1DarkMode,te.zb.gray0),overflowY:"visible",whiteSpace:"normal","&:last-child":{borderRadius:"0 0 4px 4px",paddingBottom:"6px"}})},noOptionsMessage:function(e){return ie(ie({},e),{},{color:te.kh["text-color"]})},valueContainer:function(e){return ie(ie({},e),{},{padding:"1px",maxHeight:(0,$.IS)()?"55px":"70px",overflowY:"scroll"})},control:function(e){return ie(ie({},e),{},{backgroundColor:le(t,te.zb.gray10,te.zb.white),minHeight:"28px",borderColor:le(t,te.zb.gray8,te.zb.gray6),"&:focus-within":{borderColor:le(t,te.zb.gray8,te.zb.blue5)},"&:hover":{borderColor:le(t,te.zb.gray8,te.zb.gray6)},boxShadow:"none !important"})},placeholder:function(e){return ie(ie({},e),{},{fontSize:"13px",color:le(t,te.zb.gray7,te.zb.gray5),paddingLeft:"4px"})},input:function(e){return ie(ie({},e),{},{fontSize:"13px",color:le(t,te.zb.white,te.kh["text-color"]),paddingLeft:"3px"})}});return o.createElement(ee,re({options:c,styles:l,components:{Option:se,MultiValueLabel:de,IndicatorsContainer:function(){return null},Control:ue},placeholder:"",formatCreateLabel:function(e){return"".concat(n("component.searchPanel")," ").concat(e)},id:"redaction-search-multi-select",label:n("redactionPanel.redactionSearchPlaceholder")},e))};pe.propTypes={activeTheme:a().string.isRequired,redactionSearchOptions:a().array.isRequired};const me=pe;function fe(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}n(54546);var he=function(e){var t={textSearch:[],caseSensitive:!0};return e?(e.forEach((function(e){var n=e.type;n===b._E.TEXT?t.textSearch.push(e.label):t[n]=!0,e.regex&&(t.caseSensitive=t.caseSensitive&&!e.regex.ignoreCase)})),t):t};const be=function(e){var t=e.setIsRedactionSearchActive,n=e.searchTerms,r=e.setSearchTerms,a=e.executeRedactionSearch,i=e.activeTheme,c=e.redactionSearchOptions;return o.createElement(G.A,{className:"RedactionSearchOverlay",dataElement:"redactionSearchOverlay"},o.createElement(me,{onFocus:function(){return t(!0)},value:n,onCreateOption:function(e){var t,o={label:e,value:e,type:b._E.TEXT},i=[].concat(function(e){if(Array.isArray(e))return fe(e)}(t=n||[])||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(t)||function(e,t){if(e){if("string"==typeof e)return fe(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?fe(e,t):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[o]);r(i);var c=he(i);c.caseSensitive=!1,a(c)},onChange:function(e){r(e);var t=he(e);a(t)},activeTheme:i,redactionSearchOptions:c}))};n(48598);var ye=n(90826);function ge(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}const ve=function(e){return function(t){var n,o=(0,e.getState)(),r=v.A.getRedactionSearchPatterns(o),a=Object.keys(r).reduce((function(e,t){var n=r[t],o=n.regex;return e[n.type]=o,e}),{}),i={regex:!0,caseSensitive:t.caseSensitive},c=function(e){if(Array.isArray(e))return ge(e)}(n=t.textSearch)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(n)||function(e,t){if(e){if("string"==typeof e)return ge(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?ge(e,t):void 0}}(n)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();Object.keys(t).forEach((function(e){var t=a[e];t&&c.push(t.source)}));var l=c.join("|");""!==l?(0,ye.A)()(l,i):u.A.clearSearchResults()}};function xe(e){return xe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},xe(e)}function we(){return we=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},we.apply(null,arguments)}function Ae(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function Se(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ae(Object(n),!0).forEach((function(t){Ee(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ae(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Ee(e,t,n){return(t=function(e){var t=function(e){if("object"!=xe(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=xe(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==xe(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const Re=function(e){var t=(0,o.useContext)(f).setIsRedactionSearchActive,n=(0,g.Pj)(),r=(0,g.d4)((function(e){return v.A.getActiveTheme(e)})),a=(0,g.d4)((function(e){return v.A.getRedactionSearchPatterns(e)}),g.bN),i=Object.values(a).map((function(e){return Se(Se({},e),{},{value:e.type})}));return o.createElement(be,we({setIsRedactionSearchActive:t,executeRedactionSearch:function(){return function(e,t){ve(t)(e)}(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n)},activeTheme:r,redactionSearchOptions:i},e))};n(74423),n(21699);var ke=n(11768),Oe=(n(28608),function(e){var t=e.isChecked,n=e.onChange,r=e.onClickResult,a=e.isActive,i=e.icon,c=e.ambientStr,d=function(e){var t=e.ambientStr,n=e.resultStrStart,r=e.resultStrEnd,a=e.resultStr;if(e.type===b._E.TEXT){var i=""===t?a:t.slice(n,r),c=t.slice(0,n),l=t.slice(r);return o.createElement(o.Fragment,null,c,o.createElement("span",{className:"search-value"},i),l)}return a}(e),u=l()("redaction-search-result",{active:a});return o.createElement("div",{className:u,role:"listitem",onClick:r},o.createElement("div",{style:{paddingRight:"14px"}},o.createElement(ke.G,{"aria-label":"".concat(c),checked:t,onChange:n})),o.createElement("div",{style:{paddingRight:"14px"}},o.createElement(s.A,{glyph:i})),o.createElement("div",{className:"redaction-search-result-info"},d))});Oe.propTypes={isChecked:a().bool,onChange:a().func,onClickResult:a().func,isActive:a().bool,icon:a().string,ambientStr:a().string};const Pe=o.memo(Oe),je=function(e){var t=e.searchResult,n=e.checked,r=e.checkResult,a=(0,o.useContext)(f).activeSearchResultIndex,i=t.ambientStr,c=t.resultStrStart,l=t.resultStrEnd,s=t.resultStr,d=t.icon,p=t.index,m=t.type,h=(0,o.useCallback)((function(e){r(e,p)}),[p,r]),b=(0,o.useCallback)((function(){u.A.setActiveSearchResult(t)}),[t]);return o.createElement(Pe,{ambientStr:i,resultStrStart:c,resultStrEnd:l,resultStr:s,icon:d,type:m,isChecked:n,onChange:h,onClickResult:b,isActive:a===p})};function Ce(e){return Ce="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ce(e)}function Te(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function Ie(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Te(Object(n),!0).forEach((function(t){Ne(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Te(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Ne(e,t,n){return(t=function(e){var t=function(e){if("object"!=Ce(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=Ce(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==Ce(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _e(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}n(87922);const ze=function(e){var t,n,r=e.pageNumber,a=e.searchResults,c=e.selectedSearchResultIndexes,l=e.setSelectedSearchResultIndexes,s=(0,i.B)().t,d=a.map((function(e){return e.index})),u=(t=(0,o.useState)(!1),n=2,function(e){if(Array.isArray(e))return e}(t)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,a,i,c=[],l=!0,s=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(o=a.call(n)).done)&&(c.push(o.value),c.length!==t);l=!0);}catch(e){s=!0,r=e}finally{try{if(!l&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw r}}return c}}(t,n)||function(e,t){if(e){if("string"==typeof e)return _e(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?_e(e,t):void 0}}(t,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),p=u[0],m=u[1];(0,o.useEffect)((function(){var e=d.reduce((function(e,t){return c[t]&&e}),!0);m(e)}),[c,d]);var f=(0,o.useCallback)((function(e){var t=e.target.checked;d.forEach((function(e){c[e]=t})),m(t),l(Ie({},c))}),[c,d]),h=(0,o.useCallback)((function(e,t){var n=e.target.checked;c[t]=n,l(Ie({},c))}),[c]);return o.createElement("div",{className:"redaction-search-results-page-number"},o.createElement(ke.G,{className:"redaction-search-results-page-number-checkbox","aria-label":"".concat(s("option.shared.page")," ").concat(r),checked:p,onChange:function(e){e.stopPropagation(),f(e)}}),o.createElement(I.A,{header:function(){return"".concat(s("option.shared.page")," ").concat(r)},style:{width:"100%"},expansionDescription:"".concat(s("option.shared.page")," ").concat(r)},o.createElement("div",{role:"list"},a.map((function(e,t){return o.createElement(je,{checked:c[e.index],checkResult:h,searchResult:e,key:"".concat(t,"-").concat(r)})})))))};var Me=n(25637);n(87118);const De="SEARCH_NOT_INITIATED",Le="SEARCH_IN_PROGRESS",He="SEARCH_DONE";function Be(){return Be=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},Be.apply(null,arguments)}function We(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,a,i,c=[],l=!0,s=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(o=a.call(n)).done)&&(c.push(o.value),c.length!==t);l=!0);}catch(e){s=!0,r=e}finally{try{if(!l&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw r}}return c}}(e,t)||Fe(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Fe(e,t){if(e){if("string"==typeof e)return Ue(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Ue(e,t):void 0}}function Ue(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}const qe=function(e){var t=e.redactionSearchResults,n=e.searchStatus,r=e.onCancelSearch,a=e.isProcessingRedactionResults,c=e.markSelectedResultsForRedaction,s=e.redactSelectedResults,u=(0,i.B)().t,p=We((0,o.useState)({}),2),m=p[0],h=p[1],b=We((0,o.useState)({}),2),g=b[0],v=b[1],x=We((0,o.useState)([]),2),w=x[0],A=x[1],S=(0,o.useContext)(f).isTestMode;(0,o.useEffect)((function(){var e={};t.forEach((function(t,n){var o,r=t.pageNum;t.index=n,void 0===e[r]?e[r]=[t]:e[r]=[].concat(function(e){if(Array.isArray(e))return Ue(e)}(o=e[r])||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(o)||Fe(o)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[t])})),h(e);var n={};t.forEach((function(e,t){n[t]=!1})),v(n)}),[t]),(0,o.useEffect)((function(){var e=t.filter((function(e,t){return g[t]}));A(e)}),[g]);var E=o.createElement("div",{"aria-label":u("message.noResults")},o.createElement("p",{"aria-live":"polite",className:"no-margin"},u("message.noResults"))),R=0===t.length,k=l()("redaction-search-results-container",{emptyList:R}),O=l()("redact-all-selected",{disabled:0===w.length}),P=l()("mark-all-selected",{disabled:0===w.length}),j=n===He&&!a||n===De;return o.createElement(o.Fragment,null,o.createElement("div",{className:"redaction-search-counter-controls"},n===Le&&o.createElement("div",{style:{flexGrow:1}},o.createElement(Me.A,{height:"18px",width:"18px"})),j&&o.createElement(o.Fragment,null,o.createElement("div",{className:"redaction-search-results-counter"},o.createElement("p",{"aria-live":"assertive",className:"no-margin"},o.createElement("span",null,u("redactionPanel.searchResults"))," (",t.length,")")),o.createElement(y.A,{className:l()({inactive:w.length<1}),onClick:function(){var e={};t.forEach((function(t,n){e[n]=!0})),v(e)},disabled:R,label:u("action.selectAll")},u("action.selectAll")),o.createElement(y.A,{className:l()({inactive:w.length<1}),disabled:R,onClick:function(){var e={};t.forEach((function(t,n){e[n]=!1})),v(e)},label:u("action.unselect")},u("action.unselect")))),o.createElement("div",{className:k,role:"list"},n===De&&o.createElement("div",{"aria-label":u("redactionPanel.search.start")},u("redactionPanel.search.start")),n===Le&&R&&a&&o.createElement("div",null,o.createElement(Me.A,{height:"25px",width:"25px"})),n===He&&R&&!a&&E,(n===Le||n===He)&&function(){var e=Object.keys(m);if(e.length>0){var t=S?{initialItemCount:e.length}:{};return o.createElement(d.aY,Be({data:e,itemContent:function(e,t){return o.createElement(ze,{key:e,pageNumber:t,searchResults:m[t],selectedSearchResultIndexes:g,setSelectedSearchResultIndexes:v})}},t))}}()),o.createElement("div",{className:"redaction-search-panel-controls"},o.createElement(y.A,{onClick:function(){h({}),r()},label:u("action.cancel"),className:"cancel"},u("action.cancel")),o.createElement(y.A,{disabled:0===w.length,label:u("annotation.redact"),className:O,onClick:function(){s(w)}},u("annotation.redact")),o.createElement(y.A,{disabled:0===w.length,label:u("action.addMark"),className:P,onClick:function(){c(w),r()}},u("action.addMark"))))};function Ve(){return Ve=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},Ve.apply(null,arguments)}function $e(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}var Ge=window.Core.Tools.ToolNames,Ye={OverlayText:"",StrokeColor:new window.Core.Annotations.Color(255,0,0),TextColor:new window.Core.Annotations.Color(255,0,0,1),Font:"Helvetica"};function Xe(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Ye,n=t.StrokeColor,o=t.OverlayText,r=t.FillColor,a=t.Font,i=void 0===a?"Helvetica":a,c=t.TextColor,l=t.FontSize;return e.map((function(e){var t=new window.Core.Annotations.RedactionAnnotation;return t.PageNumber=e.page_num,t.Quads=e.quads.map((function(e){return e.getPoints()})),t.StrokeColor=n,t.OverlayText=o,t.FillColor=r,t.Font=i,t.FontSize=l,t.TextColor=c,t.setContents(e.result_str),t.type=e.type,t.Author=u.A.getCurrentUser(),"text"===e.type&&t.setCustomData("trn-annot-preview",e.result_str),t.setCustomData("trn-redaction-type",e.type),t}))}const Qe=function(e){var t,n,r=e.onCancelSearch,a=(0,g.wA)(),i=(t=(0,g.d4)((function(e){return[v.A.getActiveToolStyles(e),v.A.getActiveToolName(e)]}),g.bN),n=2,function(e){if(Array.isArray(e))return e}(t)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,a,i,c=[],l=!0,s=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(o=a.call(n)).done)&&(c.push(o.value),c.length!==t);l=!0);}catch(e){s=!0,r=e}finally{try{if(!l&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw r}}return c}}(t,n)||function(e,t){if(e){if("string"==typeof e)return $e(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?$e(e,t):void 0}}(t,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),c=i[0],l=i[1],s=(0,o.useCallback)((function(e){var t=u.A.getTool(Ge.REDACTION),n=t&&t.defaults?t.defaults:Ye,o=Xe(e,l.includes("Redaction")?c:n);u.A.getAnnotationManager().addAnnotations(o)}),[c,l]);return o.createElement(qe,Ve({markSelectedResultsForRedaction:s,redactSelectedResults:function(e){var t=Xe(e,Ye);a((0,V.A)(t,r))}},e))};function Ke(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}const Ze=function(e){var t,n,r=(0,g.wA)(),a=(t=(0,o.useState)([]),n=2,function(e){if(Array.isArray(e))return e}(t)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,a,i,c=[],l=!0,s=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(o=a.call(n)).done)&&(c.push(o.value),c.length!==t);l=!0);}catch(e){s=!0,r=e}finally{try{if(!l&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw r}}return c}}(t,n)||function(e,t){if(e){if("string"==typeof e)return Ke(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Ke(e,t):void 0}}(t,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),i=a[0],c=a[1],l=(0,o.useContext)(f),d=l.isRedactionSearchActive,u=l.setIsRedactionSearchActive,p=e.redactionSearchResults,m=e.isProcessingRedactionResults,h=e.clearRedactionSearchResults,b=e.searchStatus,y=(0,$.IS)();return o.createElement(o.Fragment,null,y&&o.createElement("div",{className:"close-container"},o.createElement("button",{className:"close-icon-container",onClick:function(){r(q.A.closeElement("redactionPanel"))}},o.createElement(s.A,{glyph:"ic_close_black_24px",className:"close-icon"}))),o.createElement(Re,{searchTerms:i,setSearchTerms:c}),d&&o.createElement(Qe,{redactionSearchResults:p,onCancelSearch:function(){c([]),h(),u(!1)},searchStatus:b,isProcessingRedactionResults:m}))};function Je(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,a,i,c=[],l=!0,s=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(o=a.call(n)).done)&&(c.push(o.value),c.length!==t);l=!0);}catch(e){s=!0,r=e}finally{try{if(!l&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw r}}return c}}(e,t)||function(e,t){if(e){if("string"==typeof e)return et(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?et(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function et(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}n(50113);const tt=function(){var e=function(){var e=Je((0,o.useState)(De),2),t=e[0],n=e[1],r=Je((0,o.useState)([]),2),a=r[0],i=r[1],c=Je((0,o.useState)(!1),2),l=c[0],s=c[1],d=(0,g.d4)((function(e){return v.A.getRedactionSearchPatterns(e)}),g.bN),p=(0,o.useMemo)((function(){return Object.keys(d).reduce((function(e,t){var n=d[t],o=n.regex,r=n.type,a=n.icon;return e[r]={regex:o,icon:a},e}),{})}),[d]),m=(0,o.useCallback)((function(e){var t=e.resultStr,n=Object.keys(p).find((function(e){return p[e].regex.test(t)}));e.type=void 0===n?b._E.TEXT:n;var o=(p[e.type]||{}).icon,r=void 0===o?"icon-form-field-text":o;return e.icon=r,e}),[p]),f=(0,o.useCallback)((function(){i([]),u.A.clearSearchResults(),s(!1)}));return(0,o.useEffect)((function(){function e(e){var t=e.map(m);s(!0),i(t)}return u.A.addEventListener("searchResultsChanged",e),function(){u.A.removeEventListener("searchResultsChanged",e)}}),[t]),(0,o.useEffect)((function(){function e(e){null==e?n(De):e?n(Le):(n(He),setTimeout((function(){s(!1)}),100))}return u.A.addEventListener("searchInProgress",e),function(){u.A.removeEventListener("searchInProgress",e)}}),[]),{redactionSearchResults:a,isProcessingRedactionResults:l,clearRedactionSearchResults:f,searchStatus:t}}(),t=e.redactionSearchResults,n=e.isProcessingRedactionResults,r=e.clearRedactionSearchResults,a=e.searchStatus;return o.createElement(Ze,{redactionSearchResults:t,isProcessingRedactionResults:n,clearRedactionSearchResults:r,searchStatus:a})};function nt(e){return nt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},nt(e)}function ot(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function rt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ot(Object(n),!0).forEach((function(t){at(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ot(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function at(e,t,n){return(t=function(e){var t=function(e){if("object"!=nt(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=nt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==nt(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function it(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,a,i,c=[],l=!0,s=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(o=a.call(n)).done)&&(c.push(o.value),c.length!==t);l=!0);}catch(e){s=!0,r=e}finally{try{if(!l&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw r}}return c}}(e,t)||function(e,t){if(e){if("string"==typeof e)return ct(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?ct(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ct(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}var lt=function(e){var t=it((0,g.d4)((function(e){return[v.A.isElementOpen(e,"redactionPanel"),v.A.isElementDisabled(e,"redactionPanel"),v.A.getRedactionPanelWidth(e),v.A.isInDesktopOnlyMode(e),v.A.getCustomApplyRedactionsHandler(e),v.A.getRedactionSearchPatterns(e)]}),g.bN),6),n=t[0],r=t[1],a=t[2],i=t[3],c=t[4],l=t[5],d=(0,$.IS)(),p=e.redactionAnnotationsList,m=e.isCustomPanel,h=e.dataElement,y=(0,o.useMemo)((function(){return rt(rt({},Object.keys(l).reduce((function(e,t){var n=l[t],o=n.label,r=n.type,a=n.icon;return e[r]={label:o,icon:a},e}),{})),b.mC)}),[l]),x=(0,g.wA)(),w=function(){var e=m?h:"redactionPanel";x(q.A.closeElement(e))},A=m||!i&&d?{}:{width:"".concat(a,"px"),minWidth:"".concat(a,"px")},S=(0,o.useContext)(f).isRedactionSearchActive,E=it((0,o.useState)(!1),2),R=E[0],k=E[1];if((0,o.useEffect)((function(){var e=setTimeout((function(){k(!n)}),500);return function(){clearTimeout(e)}}),[n]),r||!n&&R&&!m)return null;var O=m?h:"redactionPanel";return o.createElement(G.A,{dataElement:O,className:"Panel RedactionPanel",style:A},!i&&d&&!m&&o.createElement("div",{className:"close-container"},o.createElement("div",{className:"close-icon-container",onClick:w},o.createElement(s.A,{glyph:"ic_close_black_24px",className:"close-icon"}))),o.createElement(tt,null),!S&&o.createElement(U,{redactionAnnotations:p,redactionTypesDictionary:y,applyAllRedactions:function(){var e=function(){var e=m?w:function(){};x((0,V.A)(p,e))};c?c(p,e):e()},deleteAllRedactionAnnotations:function(){u.A.deleteAnnotations(p)}}))};lt.propTypes={redactionAnnotationsList:a().array,isCustomPanel:a().bool,dataElement:a().string},lt.defaultProps={isCustomPanel:!1,dataElement:""};const st=function(e){return o.createElement(h,null,o.createElement(lt,e))}},82933:(e,t,n)=>{(t=n(76314)(!1)).push([e.id,".creatable-multi-select-label{display:inline-block;font-weight:700;margin-bottom:var(--padding-small)}",""]),e.exports=t},87118:(e,t,n)=>{var o=n(85072),r=n(12035);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.id,r,""]]),o(r,{insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach((e=>o.push(e))),n.querySelectorAll("*").forEach((n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))})),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const r=t[o];if(0===o)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach((t=>{t.innerHTML=e.innerHTML}))};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1}),e.exports=r.locals||{}},87922:(e,t,n)=>{var o=n(85072),r=n(67511);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.id,r,""]]),o(r,{insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach((e=>o.push(e))),n.querySelectorAll("*").forEach((n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))})),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const r=t[o];if(0===o)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach((t=>{t.innerHTML=e.innerHTML}))};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1}),e.exports=r.locals||{}},98714:(e,t,n)=>{var o=n(85072),r=n(15493);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.id,r,""]]),o(r,{insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach((e=>o.push(e))),n.querySelectorAll("*").forEach((n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))})),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const r=t[o];if(0===o)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach((t=>{t.innerHTML=e.innerHTML}))};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1}),e.exports=r.locals||{}}}]);
//# sourceMappingURL=9798.chunk.js.map