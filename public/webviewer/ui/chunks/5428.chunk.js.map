{"version": 3, "file": "chunks/5428.chunk.js", "mappings": "6GACQ,EAAQ,MAKhBA,CAAE,CAAEC,OAAQ,SAAUC,MAAM,GAAQ,CAAEC,SAJjB,EAAQ,Q,kBCD7B,IAEIC,EAFa,EAAQ,OAEOD,SAKhCE,EAAOC,QAAUC,OAAOJ,UAAY,SAAkBK,GACpD,MAAoB,iBAANA,GAAkBJ,EAAeI,EACjD,C", "sources": ["webpack://webviewer-ui/./node_modules/core-js/modules/es.number.is-finite.js", "webpack://webviewer-ui/./node_modules/core-js/internals/number-is-finite.js"], "sourcesContent": ["'use strict';\nvar $ = require('../internals/export');\nvar numberIsFinite = require('../internals/number-is-finite');\n\n// `Number.isFinite` method\n// https://tc39.es/ecma262/#sec-number.isfinite\n$({ target: 'Number', stat: true }, { isFinite: numberIsFinite });\n", "'use strict';\nvar globalThis = require('../internals/global-this');\n\nvar globalIsFinite = globalThis.isFinite;\n\n// `Number.isFinite` method\n// https://tc39.es/ecma262/#sec-number.isfinite\n// eslint-disable-next-line es/no-number-isfinite -- safe\nmodule.exports = Number.isFinite || function isFinite(it) {\n  return typeof it == 'number' && globalIsFinite(it);\n};\n"], "names": ["$", "target", "stat", "isFinite", "globalIsFinite", "module", "exports", "Number", "it"], "sourceRoot": ""}