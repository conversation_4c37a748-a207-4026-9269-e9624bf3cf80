"use strict";(self.webpackChunkwebviewer_ui=self.webpackChunkwebviewer_ui||[]).push([[6697],{46697:(e,t,o)=>{o.d(t,{dl:()=>w,jS:()=>x,B8:()=>ue});var i=o(23029),n=o(92901),r=o(56822),l=o(53954),s=o(85501),a=o(64467),c=o(96540),d=o(71345);function h(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,i)}return o}function u(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?h(Object(o),!0).forEach((function(t){(0,a.A)(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):h(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}function f(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(f=function(){return!!e})()}var p=function(e){function t(){var e,o,n,s;(0,i.A)(this,t);for(var c=arguments.length,d=new Array(c),h=0;h<c;h++)d[h]=arguments[h];return o=this,n=t,s=[].concat(d),n=(0,l.A)(n),e=(0,r.A)(o,f()?Reflect.construct(n,s||[],(0,l.A)(o).constructor):n.apply(o,s)),(0,a.A)(e,"state",{scrollToColumn:0,scrollToRow:0,instanceProps:{prevScrollToColumn:0,prevScrollToRow:0}}),(0,a.A)(e,"_columnStartIndex",0),(0,a.A)(e,"_columnStopIndex",0),(0,a.A)(e,"_rowStartIndex",0),(0,a.A)(e,"_rowStopIndex",0),(0,a.A)(e,"_onKeyDown",(function(t){var o=e.props,i=o.columnCount,n=o.disabled,r=o.mode,l=o.rowCount;if(!n){var s=e._getScrollState(),a=s.scrollToColumn,c=s.scrollToRow,d=e._getScrollState(),h=d.scrollToColumn,u=d.scrollToRow;switch(t.key){case"ArrowDown":u="cells"===r?Math.min(u+1,l-1):Math.min(e._rowStopIndex+1,l-1);break;case"ArrowLeft":h="cells"===r?Math.max(h-1,0):Math.max(e._columnStartIndex-1,0);break;case"ArrowRight":h="cells"===r?Math.min(h+1,i-1):Math.min(e._columnStopIndex+1,i-1);break;case"ArrowUp":u="cells"===r?Math.max(u-1,0):Math.max(e._rowStartIndex-1,0)}h===a&&u===c||(t.preventDefault(),e._updateScrollState({scrollToColumn:h,scrollToRow:u}))}})),(0,a.A)(e,"_onSectionRendered",(function(t){var o=t.columnStartIndex,i=t.columnStopIndex,n=t.rowStartIndex,r=t.rowStopIndex;e._columnStartIndex=o,e._columnStopIndex=i,e._rowStartIndex=n,e._rowStopIndex=r})),e}return(0,s.A)(t,e),(0,n.A)(t,[{key:"setScrollIndexes",value:function(e){var t=e.scrollToColumn,o=e.scrollToRow;this.setState({scrollToRow:o,scrollToColumn:t})}},{key:"render",value:function(){var e=this.props,t=e.className,o=e.children,i=this._getScrollState(),n=i.scrollToColumn,r=i.scrollToRow;return c.createElement("div",{className:t,onKeyDown:this._onKeyDown},o({onSectionRendered:this._onSectionRendered,scrollToColumn:n,scrollToRow:r}))}},{key:"_getScrollState",value:function(){return this.props.isControlled?this.props:this.state}},{key:"_updateScrollState",value:function(e){var t=e.scrollToColumn,o=e.scrollToRow,i=this.props,n=i.isControlled,r=i.onScrollToChange;"function"==typeof r&&r({scrollToColumn:t,scrollToRow:o}),n||this.setState({scrollToColumn:t,scrollToRow:o})}}],[{key:"getDerivedStateFromProps",value:function(e,t){return e.isControlled?{}:e.scrollToColumn!==t.instanceProps.prevScrollToColumn||e.scrollToRow!==t.instanceProps.prevScrollToRow?u(u({},t),{},{scrollToColumn:e.scrollToColumn,scrollToRow:e.scrollToRow,instanceProps:{prevScrollToColumn:e.scrollToColumn,prevScrollToRow:e.scrollToRow}}):{}}}])}(c.PureComponent);function _(e,t){var i,n=void 0!==(i=void 0!==t?t:"undefined"!=typeof window?window:"undefined"!=typeof self?self:o.g).document&&i.document.attachEvent;if(!n){var r=function(){var e=i.requestAnimationFrame||i.mozRequestAnimationFrame||i.webkitRequestAnimationFrame||function(e){return i.setTimeout(e,20)};return function(t){return e(t)}}(),l=function(){var e=i.cancelAnimationFrame||i.mozCancelAnimationFrame||i.webkitCancelAnimationFrame||i.clearTimeout;return function(t){return e(t)}}(),s=function(e){var t=e.__resizeTriggers__,o=t.firstElementChild,i=t.lastElementChild,n=o.firstElementChild;i.scrollLeft=i.scrollWidth,i.scrollTop=i.scrollHeight,n.style.width=o.offsetWidth+1+"px",n.style.height=o.offsetHeight+1+"px",o.scrollLeft=o.scrollWidth,o.scrollTop=o.scrollHeight},a=function(e){if(!(e.target.className&&"function"==typeof e.target.className.indexOf&&e.target.className.indexOf("contract-trigger")<0&&e.target.className.indexOf("expand-trigger")<0)){var t=this;s(this),this.__resizeRAF__&&l(this.__resizeRAF__),this.__resizeRAF__=r((function(){(function(e){return e.offsetWidth!=e.__resizeLast__.width||e.offsetHeight!=e.__resizeLast__.height})(t)&&(t.__resizeLast__.width=t.offsetWidth,t.__resizeLast__.height=t.offsetHeight,t.__resizeListeners__.forEach((function(o){o.call(t,e)})))}))}},c=!1,d="",h="animationstart",u="Webkit Moz O ms".split(" "),f="webkitAnimationStart animationstart oAnimationStart MSAnimationStart".split(" "),p=i.document.createElement("fakeelement");if(void 0!==p.style.animationName&&(c=!0),!1===c)for(var _=0;_<u.length;_++)if(void 0!==p.style[u[_]+"AnimationName"]){d="-"+u[_].toLowerCase()+"-",h=f[_],c=!0;break}var g="resizeanim",v="@"+d+"keyframes "+g+" { from { opacity: 0; } to { opacity: 0; } } ",m=d+"animation: 1ms "+g+"; "}return{addResizeListener:function(t,o){if(n)t.attachEvent("onresize",o);else{if(!t.__resizeTriggers__){var r=t.ownerDocument,l=i.getComputedStyle(t);l&&"static"==l.position&&(t.style.position="relative"),function(t){if(!t.getElementById("detectElementResize")){var o=(v||"")+".resize-triggers { "+(m||"")+'visibility: hidden; opacity: 0; } .resize-triggers, .resize-triggers > div, .contract-trigger:before { content: " "; display: block; position: absolute; top: 0; left: 0; height: 100%; width: 100%; overflow: hidden; z-index: -1; } .resize-triggers > div { background: #eee; overflow: auto; } .contract-trigger:before { width: 200%; height: 200%; }',i=t.head||t.getElementsByTagName("head")[0],n=t.createElement("style");n.id="detectElementResize",n.type="text/css",null!=e&&n.setAttribute("nonce",e),n.styleSheet?n.styleSheet.cssText=o:n.appendChild(t.createTextNode(o)),i.appendChild(n)}}(r),t.__resizeLast__={},t.__resizeListeners__=[],(t.__resizeTriggers__=r.createElement("div")).className="resize-triggers";var c=r.createElement("div");c.className="expand-trigger",c.appendChild(r.createElement("div"));var d=r.createElement("div");d.className="contract-trigger",t.__resizeTriggers__.appendChild(c),t.__resizeTriggers__.appendChild(d),t.appendChild(t.__resizeTriggers__),s(t),t.addEventListener("scroll",a,!0),h&&(t.__resizeTriggers__.__animationListener__=function(e){e.animationName==g&&s(t)},t.__resizeTriggers__.addEventListener(h,t.__resizeTriggers__.__animationListener__))}t.__resizeListeners__.push(o)}},removeResizeListener:function(e,t){if(n)e.detachEvent("onresize",t);else if(e.__resizeListeners__.splice(e.__resizeListeners__.indexOf(t),1),!e.__resizeListeners__.length){e.removeEventListener("scroll",a,!0),e.__resizeTriggers__.__animationListener__&&(e.__resizeTriggers__.removeEventListener(h,e.__resizeTriggers__.__animationListener__),e.__resizeTriggers__.__animationListener__=null);try{e.__resizeTriggers__=!e.removeChild(e.__resizeTriggers__)}catch(e){}}}}}function g(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,i)}return o}function v(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?g(Object(o),!0).forEach((function(t){(0,a.A)(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):g(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}function m(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(m=function(){return!!e})()}(0,a.A)(p,"defaultProps",{disabled:!1,isControlled:!1,mode:"edges",scrollToColumn:0,scrollToRow:0}),(0,d.polyfill)(p);var S=function(e){function t(){var e,o,n,s;(0,i.A)(this,t);for(var c=arguments.length,d=new Array(c),h=0;h<c;h++)d[h]=arguments[h];return o=this,n=t,s=[].concat(d),n=(0,l.A)(n),e=(0,r.A)(o,m()?Reflect.construct(n,s||[],(0,l.A)(o).constructor):n.apply(o,s)),(0,a.A)(e,"state",{height:e.props.defaultHeight||0,width:e.props.defaultWidth||0}),(0,a.A)(e,"_parentNode",void 0),(0,a.A)(e,"_autoSizer",void 0),(0,a.A)(e,"_window",void 0),(0,a.A)(e,"_detectElementResize",void 0),(0,a.A)(e,"_onResize",(function(){var t=e.props,o=t.disableHeight,i=t.disableWidth,n=t.onResize;if(e._parentNode){var r=e._parentNode.offsetHeight||0,l=e._parentNode.offsetWidth||0,s=(e._window||window).getComputedStyle(e._parentNode)||{},a=parseInt(s.paddingLeft,10)||0,c=parseInt(s.paddingRight,10)||0,d=parseInt(s.paddingTop,10)||0,h=parseInt(s.paddingBottom,10)||0,u=r-d-h,f=l-a-c;(!o&&e.state.height!==u||!i&&e.state.width!==f)&&(e.setState({height:r-d-h,width:l-a-c}),n({height:r,width:l}))}})),(0,a.A)(e,"_setRef",(function(t){e._autoSizer=t})),e}return(0,s.A)(t,e),(0,n.A)(t,[{key:"componentDidMount",value:function(){var e=this.props.nonce;this._autoSizer&&this._autoSizer.parentNode&&this._autoSizer.parentNode.ownerDocument&&this._autoSizer.parentNode.ownerDocument.defaultView&&this._autoSizer.parentNode instanceof this._autoSizer.parentNode.ownerDocument.defaultView.HTMLElement&&(this._parentNode=this._autoSizer.parentNode,this._window=this._autoSizer.parentNode.ownerDocument.defaultView,this._detectElementResize=_(e,this._window),this._detectElementResize.addResizeListener(this._parentNode,this._onResize),this._onResize())}},{key:"componentWillUnmount",value:function(){this._detectElementResize&&this._parentNode&&this._detectElementResize.removeResizeListener(this._parentNode,this._onResize)}},{key:"render",value:function(){var e=this.props,t=e.children,o=e.className,i=e.disableHeight,n=e.disableWidth,r=e.style,l=this.state,s=l.height,a=l.width,d={overflow:"visible"},h={};return i||(d.height=0,h.height=s),n||(d.width=0,h.width=a),c.createElement("div",{className:o,ref:this._setRef,style:v(v({},d),r)},t(h))}}])}(c.Component);(0,a.A)(S,"defaultProps",{onResize:function(){},disableHeight:!1,disableWidth:!1,style:{}});var C=o(96763);function y(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(y=function(){return!!e})()}var w=function(e){function t(){var e,o,n,s;(0,i.A)(this,t);for(var d=arguments.length,h=new Array(d),u=0;u<d;u++)h[u]=arguments[u];return o=this,n=t,s=[].concat(h),n=(0,l.A)(n),e=(0,r.A)(o,y()?Reflect.construct(n,s||[],(0,l.A)(o).constructor):n.apply(o,s)),(0,a.A)(e,"_child",c.createRef()),(0,a.A)(e,"_measure",(function(){var t=e.props,o=t.cache,i=t.columnIndex,n=void 0===i?0:i,r=t.parent,l=t.rowIndex,s=void 0===l?e.props.index||0:l,a=e._getCellMeasurements(),c=a.height,d=a.width;c===o.getHeight(s,n)&&d===o.getWidth(s,n)||(o.set(s,n,d,c),r&&"function"==typeof r.recomputeGridSize&&r.recomputeGridSize({columnIndex:n,rowIndex:s}))})),(0,a.A)(e,"_registerChild",(function(t){!t||t instanceof Element||C.warn("CellMeasurer registerChild expects to be passed Element or null"),e._child.current=t,t&&e._maybeMeasureCell()})),e}return(0,s.A)(t,e),(0,n.A)(t,[{key:"componentDidMount",value:function(){this._maybeMeasureCell()}},{key:"componentDidUpdate",value:function(){this._maybeMeasureCell()}},{key:"render",value:function(){var e=this,t=this.props.children,o="function"==typeof t?t({measure:this._measure,registerChild:this._registerChild}):t;return null===o?o:(0,c.cloneElement)(o,{ref:function(t){"function"==typeof o.ref?o.ref(t):o.ref&&(o.ref.current=t),e._child.current=t}})}},{key:"_getCellMeasurements",value:function(){var e=this.props.cache,t=this._child.current;if(t&&t.ownerDocument&&t.ownerDocument.defaultView&&t instanceof t.ownerDocument.defaultView.HTMLElement){var o=t.style.width,i=t.style.height;e.hasFixedWidth()||(t.style.width="auto"),e.hasFixedHeight()||(t.style.height="auto");var n=Math.ceil(t.offsetHeight),r=Math.ceil(t.offsetWidth);return o&&(t.style.width=o),i&&(t.style.height=i),{height:n,width:r}}return{height:0,width:0}}},{key:"_maybeMeasureCell",value:function(){var e=this.props,t=e.cache,o=e.columnIndex,i=void 0===o?0:o,n=e.parent,r=e.rowIndex,l=void 0===r?this.props.index||0:r;if(!t.has(l,i)){var s=this._getCellMeasurements(),a=s.height,c=s.width;t.set(l,i,c,a),n&&"function"==typeof n.invalidateCellSizeAfterRender&&n.invalidateCellSizeAfterRender({columnIndex:i,rowIndex:l})}}}])}(c.PureComponent);(0,a.A)(w,"__internalCellMeasurerFlag",!1);var x=function(){return(0,n.A)((function e(){var t=this,o=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};(0,i.A)(this,e),(0,a.A)(this,"_cellHeightCache",{}),(0,a.A)(this,"_cellWidthCache",{}),(0,a.A)(this,"_columnWidthCache",{}),(0,a.A)(this,"_rowHeightCache",{}),(0,a.A)(this,"_defaultHeight",void 0),(0,a.A)(this,"_defaultWidth",void 0),(0,a.A)(this,"_minHeight",void 0),(0,a.A)(this,"_minWidth",void 0),(0,a.A)(this,"_keyMapper",void 0),(0,a.A)(this,"_hasFixedHeight",void 0),(0,a.A)(this,"_hasFixedWidth",void 0),(0,a.A)(this,"_columnCount",0),(0,a.A)(this,"_rowCount",0),(0,a.A)(this,"columnWidth",(function(e){var o=e.index,i=t._keyMapper(0,o);return void 0!==t._columnWidthCache[i]?t._columnWidthCache[i]:t._defaultWidth})),(0,a.A)(this,"rowHeight",(function(e){var o=e.index,i=t._keyMapper(o,0);return void 0!==t._rowHeightCache[i]?t._rowHeightCache[i]:t._defaultHeight}));var n=o.defaultHeight,r=o.defaultWidth,l=o.fixedHeight,s=o.fixedWidth,c=o.keyMapper,d=o.minHeight,h=o.minWidth;this._hasFixedHeight=!0===l,this._hasFixedWidth=!0===s,this._minHeight=d||0,this._minWidth=h||0,this._keyMapper=c||R,this._defaultHeight=Math.max(this._minHeight,"number"==typeof n?n:30),this._defaultWidth=Math.max(this._minWidth,"number"==typeof r?r:100)}),[{key:"clear",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,o=this._keyMapper(e,t);delete this._cellHeightCache[o],delete this._cellWidthCache[o],this._updateCachedColumnAndRowSizes(e,t)}},{key:"clearAll",value:function(){this._cellHeightCache={},this._cellWidthCache={},this._columnWidthCache={},this._rowHeightCache={},this._rowCount=0,this._columnCount=0}},{key:"defaultHeight",get:function(){return this._defaultHeight}},{key:"defaultWidth",get:function(){return this._defaultWidth}},{key:"hasFixedHeight",value:function(){return this._hasFixedHeight}},{key:"hasFixedWidth",value:function(){return this._hasFixedWidth}},{key:"getHeight",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(this._hasFixedHeight)return this._defaultHeight;var o=this._keyMapper(e,t);return void 0!==this._cellHeightCache[o]?Math.max(this._minHeight,this._cellHeightCache[o]):this._defaultHeight}},{key:"getWidth",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(this._hasFixedWidth)return this._defaultWidth;var o=this._keyMapper(e,t);return void 0!==this._cellWidthCache[o]?Math.max(this._minWidth,this._cellWidthCache[o]):this._defaultWidth}},{key:"has",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,o=this._keyMapper(e,t);return void 0!==this._cellHeightCache[o]}},{key:"set",value:function(e,t,o,i){var n=this._keyMapper(e,t);t>=this._columnCount&&(this._columnCount=t+1),e>=this._rowCount&&(this._rowCount=e+1),this._cellHeightCache[n]=i,this._cellWidthCache[n]=o,this._updateCachedColumnAndRowSizes(e,t)}},{key:"_updateCachedColumnAndRowSizes",value:function(e,t){if(!this._hasFixedWidth){for(var o=0,i=0;i<this._rowCount;i++)o=Math.max(o,this.getWidth(i,t));var n=this._keyMapper(0,t);this._columnWidthCache[n]=o}if(!this._hasFixedHeight){for(var r=0,l=0;l<this._columnCount;l++)r=Math.max(r,this.getHeight(e,l));var s=this._keyMapper(e,0);this._rowHeightCache[s]=r}}}])}();function R(e,t){return"".concat(e,"-").concat(t)}var z=o(58168),T=o(20053);function b(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t={};return function(o){var i=o.callback,n=o.indices,r=Object.keys(n),l=!e||r.every((function(e){var t=n[e];return Array.isArray(t)?t.length>0:t>=0})),s=r.length!==Object.keys(t).length||r.some((function(e){var o=t[e],i=n[e];return Array.isArray(i)?o.join(",")!==i.join(","):o!==i}));t=n,l&&s&&i(n)}}var I=o(80669);function A(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,i)}return o}function k(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?A(Object(o),!0).forEach((function(t){(0,a.A)(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):A(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}function M(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(M=function(){return!!e})()}var O="requested",P=function(e){function t(){var e,o,n,s;(0,i.A)(this,t);for(var c=arguments.length,d=new Array(c),h=0;h<c;h++)d[h]=arguments[h];return o=this,n=t,s=[].concat(d),n=(0,l.A)(n),e=(0,r.A)(o,M()?Reflect.construct(n,s||[],(0,l.A)(o).constructor):n.apply(o,s)),(0,a.A)(e,"state",{isScrolling:!1,scrollLeft:0,scrollTop:0}),(0,a.A)(e,"_calculateSizeAndPositionDataOnNextUpdate",!1),(0,a.A)(e,"_onSectionRenderedMemoizer",b()),(0,a.A)(e,"_onScrollMemoizer",b(!1)),(0,a.A)(e,"_invokeOnSectionRenderedHelper",(function(){var t=e.props,o=t.cellLayoutManager,i=t.onSectionRendered;e._onSectionRenderedMemoizer({callback:i,indices:{indices:o.getLastRenderedIndices()}})})),(0,a.A)(e,"_setScrollingContainerRef",(function(t){e._scrollingContainer=t})),(0,a.A)(e,"_updateScrollPositionForScrollToCell",(function(){var t=e.props,o=t.cellLayoutManager,i=t.height,n=t.scrollToAlignment,r=t.scrollToCell,l=t.width,s=e.state,a=s.scrollLeft,c=s.scrollTop;if(r>=0){var d=o.getScrollPositionForCell({align:n,cellIndex:r,height:i,scrollLeft:a,scrollTop:c,width:l});d.scrollLeft===a&&d.scrollTop===c||e._setScrollPosition(d)}})),(0,a.A)(e,"_onScroll",(function(t){if(t.target===e._scrollingContainer){e._enablePointerEventsAfterDelay();var o=e.props,i=o.cellLayoutManager,n=o.height,r=o.isScrollingChange,l=o.width,s=e._scrollbarSize,a=i.getTotalSize(),c=a.height,d=a.width,h=Math.max(0,Math.min(d-l+s,t.target.scrollLeft)),u=Math.max(0,Math.min(c-n+s,t.target.scrollTop));if(e.state.scrollLeft!==h||e.state.scrollTop!==u){var f=t.cancelable?"observed":O;e.state.isScrolling||r(!0),e.setState({isScrolling:!0,scrollLeft:h,scrollPositionChangeReason:f,scrollTop:u})}e._invokeOnScrollMemoizer({scrollLeft:h,scrollTop:u,totalWidth:d,totalHeight:c})}})),e._scrollbarSize=(0,I.default)(),void 0===e._scrollbarSize?(e._scrollbarSizeMeasured=!1,e._scrollbarSize=0):e._scrollbarSizeMeasured=!0,e}return(0,s.A)(t,e),(0,n.A)(t,[{key:"recomputeCellSizesAndPositions",value:function(){this._calculateSizeAndPositionDataOnNextUpdate=!0,this.forceUpdate()}},{key:"componentDidMount",value:function(){var e=this.props,t=e.cellLayoutManager,o=e.scrollLeft,i=e.scrollToCell,n=e.scrollTop;this._scrollbarSizeMeasured||(this._scrollbarSize=(0,I.default)(),this._scrollbarSizeMeasured=!0,this.setState({})),i>=0?this._updateScrollPositionForScrollToCell():(o>=0||n>=0)&&this._setScrollPosition({scrollLeft:o,scrollTop:n}),this._invokeOnSectionRenderedHelper();var r=t.getTotalSize(),l=r.height,s=r.width;this._invokeOnScrollMemoizer({scrollLeft:o||0,scrollTop:n||0,totalHeight:l,totalWidth:s})}},{key:"componentDidUpdate",value:function(e,t){var o=this.props,i=o.height,n=o.scrollToAlignment,r=o.scrollToCell,l=o.width,s=this.state,a=s.scrollLeft,c=s.scrollPositionChangeReason,d=s.scrollTop;c===O&&(a>=0&&a!==t.scrollLeft&&a!==this._scrollingContainer.scrollLeft&&(this._scrollingContainer.scrollLeft=a),d>=0&&d!==t.scrollTop&&d!==this._scrollingContainer.scrollTop&&(this._scrollingContainer.scrollTop=d)),i===e.height&&n===e.scrollToAlignment&&r===e.scrollToCell&&l===e.width||this._updateScrollPositionForScrollToCell(),this._invokeOnSectionRenderedHelper()}},{key:"componentWillUnmount",value:function(){this._disablePointerEventsTimeoutId&&clearTimeout(this._disablePointerEventsTimeoutId)}},{key:"render",value:function(){var e=this.props,t=e.autoHeight,o=e.cellCount,i=e.cellLayoutManager,n=e.className,r=e.height,l=e.horizontalOverscanSize,s=e.id,a=e.noContentRenderer,d=e.style,h=e.verticalOverscanSize,u=e.width,f=this.state,p=f.isScrolling,_=f.scrollLeft,g=f.scrollTop;(this._lastRenderedCellCount!==o||this._lastRenderedCellLayoutManager!==i||this._calculateSizeAndPositionDataOnNextUpdate)&&(this._lastRenderedCellCount=o,this._lastRenderedCellLayoutManager=i,this._calculateSizeAndPositionDataOnNextUpdate=!1,i.calculateSizeAndPositionData());var v=i.getTotalSize(),m=v.height,S=v.width,C=Math.max(0,_-l),y=Math.max(0,g-h),w=Math.min(S,_+u+l),x=Math.min(m,g+r+h),R=r>0&&u>0?i.cellRenderers({height:x-y,isScrolling:p,width:w-C,x:C,y}):[],z={boxSizing:"border-box",direction:"ltr",height:t?"auto":r,position:"relative",WebkitOverflowScrolling:"touch",width:u,willChange:"transform"},b=m>r?this._scrollbarSize:0,I=S>u?this._scrollbarSize:0;return z.overflowX=S+b<=u?"hidden":"auto",z.overflowY=m+I<=r?"hidden":"auto",c.createElement("div",{ref:this._setScrollingContainerRef,"aria-label":this.props["aria-label"],className:(0,T.default)("ReactVirtualized__Collection",n),id:s,onScroll:this._onScroll,role:"grid",style:k(k({},z),d),tabIndex:0},o>0&&c.createElement("div",{className:"ReactVirtualized__Collection__innerScrollContainer",style:{height:m,maxHeight:m,maxWidth:S,overflow:"hidden",pointerEvents:p?"none":"",width:S}},R),0===o&&a())}},{key:"_enablePointerEventsAfterDelay",value:function(){var e=this;this._disablePointerEventsTimeoutId&&clearTimeout(this._disablePointerEventsTimeoutId),this._disablePointerEventsTimeoutId=setTimeout((function(){(0,e.props.isScrollingChange)(!1),e._disablePointerEventsTimeoutId=null,e.setState({isScrolling:!1})}),150)}},{key:"_invokeOnScrollMemoizer",value:function(e){var t=this,o=e.scrollLeft,i=e.scrollTop,n=e.totalHeight,r=e.totalWidth;this._onScrollMemoizer({callback:function(e){var o=e.scrollLeft,i=e.scrollTop,l=t.props,s=l.height;(0,l.onScroll)({clientHeight:s,clientWidth:l.width,scrollHeight:n,scrollLeft:o,scrollTop:i,scrollWidth:r})},indices:{scrollLeft:o,scrollTop:i}})}},{key:"_setScrollPosition",value:function(e){var t=e.scrollLeft,o=e.scrollTop,i={scrollPositionChangeReason:O};t>=0&&(i.scrollLeft=t),o>=0&&(i.scrollTop=o),(t>=0&&t!==this.state.scrollLeft||o>=0&&o!==this.state.scrollTop)&&this.setState(i)}}],[{key:"getDerivedStateFromProps",value:function(e,t){return 0!==e.cellCount||0===t.scrollLeft&&0===t.scrollTop?e.scrollLeft!==t.scrollLeft||e.scrollTop!==t.scrollTop?{scrollLeft:null!=e.scrollLeft?e.scrollLeft:t.scrollLeft,scrollTop:null!=e.scrollTop?e.scrollTop:t.scrollTop,scrollPositionChangeReason:O}:null:{scrollLeft:0,scrollTop:0,scrollPositionChangeReason:O}}}])}(c.PureComponent);(0,a.A)(P,"defaultProps",{"aria-label":"grid",horizontalOverscanSize:0,noContentRenderer:function(){return null},onScroll:function(){return null},onSectionRendered:function(){return null},scrollToAlignment:"auto",scrollToCell:-1,style:{},verticalOverscanSize:0}),P.propTypes={},(0,d.polyfill)(P);const L=P;var G=function(){return(0,n.A)((function e(t){var o=t.height,n=t.width,r=t.x,l=t.y;(0,i.A)(this,e),this.height=o,this.width=n,this.x=r,this.y=l,this._indexMap={},this._indices=[]}),[{key:"addCellIndex",value:function(e){var t=e.index;this._indexMap[t]||(this._indexMap[t]=!0,this._indices.push(t))}},{key:"getCellIndices",value:function(){return this._indices}},{key:"toString",value:function(){return"".concat(this.x,",").concat(this.y," ").concat(this.width,"x").concat(this.height)}}])}(),H=function(){return(0,n.A)((function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:100;(0,i.A)(this,e),this._sectionSize=t,this._cellMetadata=[],this._sections={}}),[{key:"getCellIndices",value:function(e){var t=e.height,o=e.width,i=e.x,n=e.y,r={};return this.getSections({height:t,width:o,x:i,y:n}).forEach((function(e){return e.getCellIndices().forEach((function(e){r[e]=e}))})),Object.keys(r).map((function(e){return r[e]}))}},{key:"getCellMetadata",value:function(e){var t=e.index;return this._cellMetadata[t]}},{key:"getSections",value:function(e){for(var t=e.height,o=e.width,i=e.x,n=e.y,r=Math.floor(i/this._sectionSize),l=Math.floor((i+o-1)/this._sectionSize),s=Math.floor(n/this._sectionSize),a=Math.floor((n+t-1)/this._sectionSize),c=[],d=r;d<=l;d++)for(var h=s;h<=a;h++){var u="".concat(d,".").concat(h);this._sections[u]||(this._sections[u]=new G({height:this._sectionSize,width:this._sectionSize,x:d*this._sectionSize,y:h*this._sectionSize})),c.push(this._sections[u])}return c}},{key:"getTotalSectionCount",value:function(){return Object.keys(this._sections).length}},{key:"toString",value:function(){var e=this;return Object.keys(this._sections).map((function(t){return e._sections[t].toString()}))}},{key:"registerCell",value:function(e){var t=e.cellMetadatum,o=e.index;this._cellMetadata[o]=t,this.getSections(t).forEach((function(e){return e.addCellIndex({index:o})}))}}])}();function W(e){var t=e.align,o=void 0===t?"auto":t,i=e.cellOffset,n=e.cellSize,r=e.containerSize,l=e.currentOffset,s=i,a=s-r+n;switch(o){case"start":return s;case"end":return a;case"center":return s-(r-n)/2;default:return Math.max(a,Math.min(s,l))}}function E(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(E=function(){return!!e})()}var D=function(e){function t(e,o){var n,s,a,c;return(0,i.A)(this,t),s=this,a=t,c=[e,o],a=(0,l.A)(a),(n=(0,r.A)(s,E()?Reflect.construct(a,c||[],(0,l.A)(s).constructor):a.apply(s,c)))._cellMetadata=[],n._lastRenderedCellIndices=[],n._cellCache=[],n._isScrollingChange=n._isScrollingChange.bind(n),n._setCollectionViewRef=n._setCollectionViewRef.bind(n),n}return(0,s.A)(t,e),(0,n.A)(t,[{key:"forceUpdate",value:function(){void 0!==this._collectionView&&this._collectionView.forceUpdate()}},{key:"recomputeCellSizesAndPositions",value:function(){this._cellCache=[],this._collectionView.recomputeCellSizesAndPositions()}},{key:"render",value:function(){var e=(0,z.A)({},(function(e){if(null==e)throw new TypeError("Cannot destructure "+e)}(this.props),this.props));return c.createElement(L,(0,z.A)({cellLayoutManager:this,isScrollingChange:this._isScrollingChange,ref:this._setCollectionViewRef},e))}},{key:"calculateSizeAndPositionData",value:function(){var e=this.props,t=function(e){for(var t=e.cellCount,o=e.cellSizeAndPositionGetter,i=[],n=new H(e.sectionSize),r=0,l=0,s=0;s<t;s++){var a=o({index:s});if(null==a.height||isNaN(a.height)||null==a.width||isNaN(a.width)||null==a.x||isNaN(a.x)||null==a.y||isNaN(a.y))throw Error("Invalid metadata returned for cell ".concat(s,":\n        x:").concat(a.x,", y:").concat(a.y,", width:").concat(a.width,", height:").concat(a.height));r=Math.max(r,a.y+a.height),l=Math.max(l,a.x+a.width),i[s]=a,n.registerCell({cellMetadatum:a,index:s})}return{cellMetadata:i,height:r,sectionManager:n,width:l}}({cellCount:e.cellCount,cellSizeAndPositionGetter:e.cellSizeAndPositionGetter,sectionSize:e.sectionSize});this._cellMetadata=t.cellMetadata,this._sectionManager=t.sectionManager,this._height=t.height,this._width=t.width}},{key:"getLastRenderedIndices",value:function(){return this._lastRenderedCellIndices}},{key:"getScrollPositionForCell",value:function(e){var t=e.align,o=e.cellIndex,i=e.height,n=e.scrollLeft,r=e.scrollTop,l=e.width,s=this.props.cellCount;if(o>=0&&o<s){var a=this._cellMetadata[o];n=W({align:t,cellOffset:a.x,cellSize:a.width,containerSize:l,currentOffset:n,targetIndex:o}),r=W({align:t,cellOffset:a.y,cellSize:a.height,containerSize:i,currentOffset:r,targetIndex:o})}return{scrollLeft:n,scrollTop:r}}},{key:"getTotalSize",value:function(){return{height:this._height,width:this._width}}},{key:"cellRenderers",value:function(e){var t=this,o=e.height,i=e.isScrolling,n=e.width,r=e.x,l=e.y,s=this.props,a=s.cellGroupRenderer,c=s.cellRenderer;return this._lastRenderedCellIndices=this._sectionManager.getCellIndices({height:o,width:n,x:r,y:l}),a({cellCache:this._cellCache,cellRenderer:c,cellSizeAndPositionGetter:function(e){var o=e.index;return t._sectionManager.getCellMetadata({index:o})},indices:this._lastRenderedCellIndices,isScrolling:i})}},{key:"_isScrollingChange",value:function(e){e||(this._cellCache=[])}},{key:"_setCollectionViewRef",value:function(e){this._collectionView=e}}])}(c.PureComponent);function F(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(F=function(){return!!e})()}(0,a.A)(D,"defaultProps",{"aria-label":"grid",cellGroupRenderer:function(e){var t=e.cellCache,o=e.cellRenderer,i=e.cellSizeAndPositionGetter,n=e.indices,r=e.isScrolling;return n.map((function(e){var n=i({index:e}),l={index:e,isScrolling:r,key:e,style:{height:n.height,left:n.x,position:"absolute",top:n.y,width:n.width}};return r?(e in t||(t[e]=o(l)),t[e]):o(l)})).filter((function(e){return!!e}))}}),D.propTypes={},(function(e){function t(e,o){var n,s,a,c;return(0,i.A)(this,t),s=this,a=t,c=[e,o],a=(0,l.A)(a),(n=(0,r.A)(s,F()?Reflect.construct(a,c||[],(0,l.A)(s).constructor):a.apply(s,c)))._registerChild=n._registerChild.bind(n),n}return(0,s.A)(t,e),(0,n.A)(t,[{key:"componentDidUpdate",value:function(e){var t=this.props,o=t.columnMaxWidth,i=t.columnMinWidth,n=t.columnCount,r=t.width;o===e.columnMaxWidth&&i===e.columnMinWidth&&n===e.columnCount&&r===e.width||this._registeredChild&&this._registeredChild.recomputeGridSize()}},{key:"render",value:function(){var e=this.props,t=e.children,o=e.columnMaxWidth,i=e.columnMinWidth,n=e.columnCount,r=e.width,l=i||1,s=o?Math.min(o,r):r,a=r/n;return a=Math.max(l,a),a=Math.min(s,a),a=Math.floor(a),t({adjustedWidth:Math.min(r,a*n),columnWidth:a,getColumnWidth:function(){return a},registerChild:this._registerChild})}},{key:"_registerChild",value:function(e){if(e&&"function"!=typeof e.recomputeGridSize)throw Error("Unexpected child type registered; only Grid/MultiGrid children are supported.");this._registeredChild=e,this._registeredChild&&this._registeredChild.recomputeGridSize()}}])}(c.PureComponent)).propTypes={};var j=o(82284);function N(e){var t=e.cellCount,o=e.cellSize,i=e.computeMetadataCallback,n=e.computeMetadataCallbackProps,r=e.nextCellsCount,l=e.nextCellSize,s=e.nextScrollToIndex,a=e.scrollToIndex,c=e.updateScrollOffsetForScrollToIndex;t===r&&("number"!=typeof o&&"number"!=typeof l||o===l)||(i(n),a>=0&&a===s&&c())}var B,U=o(80045),V=function(){return(0,n.A)((function e(t){var o=t.cellCount,n=t.cellSizeGetter,r=t.estimatedCellSize;(0,i.A)(this,e),(0,a.A)(this,"_cellSizeAndPositionData",{}),(0,a.A)(this,"_lastMeasuredIndex",-1),(0,a.A)(this,"_lastBatchedIndex",-1),(0,a.A)(this,"_cellCount",void 0),(0,a.A)(this,"_cellSizeGetter",void 0),(0,a.A)(this,"_estimatedCellSize",void 0),this._cellSizeGetter=n,this._cellCount=o,this._estimatedCellSize=r}),[{key:"areOffsetsAdjusted",value:function(){return!1}},{key:"configure",value:function(e){var t=e.cellCount,o=e.estimatedCellSize,i=e.cellSizeGetter;this._cellCount=t,this._estimatedCellSize=o,this._cellSizeGetter=i}},{key:"getCellCount",value:function(){return this._cellCount}},{key:"getEstimatedCellSize",value:function(){return this._estimatedCellSize}},{key:"getLastMeasuredIndex",value:function(){return this._lastMeasuredIndex}},{key:"getOffsetAdjustment",value:function(){return 0}},{key:"getSizeAndPositionOfCell",value:function(e){if(e<0||e>=this._cellCount)throw Error("Requested index ".concat(e," is outside of range 0..").concat(this._cellCount));if(e>this._lastMeasuredIndex)for(var t=this.getSizeAndPositionOfLastMeasuredCell(),o=t.offset+t.size,i=this._lastMeasuredIndex+1;i<=e;i++){var n=this._cellSizeGetter({index:i});if(void 0===n||isNaN(n))throw Error("Invalid size returned for cell ".concat(i," of value ").concat(n));null===n?(this._cellSizeAndPositionData[i]={offset:o,size:0},this._lastBatchedIndex=e):(this._cellSizeAndPositionData[i]={offset:o,size:n},o+=n,this._lastMeasuredIndex=e)}return this._cellSizeAndPositionData[e]}},{key:"getSizeAndPositionOfLastMeasuredCell",value:function(){return this._lastMeasuredIndex>=0?this._cellSizeAndPositionData[this._lastMeasuredIndex]:{offset:0,size:0}}},{key:"getTotalSize",value:function(){var e=this.getSizeAndPositionOfLastMeasuredCell();return e.offset+e.size+(this._cellCount-this._lastMeasuredIndex-1)*this._estimatedCellSize}},{key:"getUpdatedOffsetForIndex",value:function(e){var t=e.align,o=void 0===t?"auto":t,i=e.containerSize,n=e.currentOffset,r=e.targetIndex;if(i<=0)return 0;var l,s=this.getSizeAndPositionOfCell(r),a=s.offset,c=a-i+s.size;switch(o){case"start":l=a;break;case"end":l=c;break;case"center":l=a-(i-s.size)/2;break;default:l=Math.max(c,Math.min(a,n))}var d=this.getTotalSize();return Math.max(0,Math.min(d-i,l))}},{key:"getVisibleCellRange",value:function(e){var t=e.containerSize,o=e.offset;if(0===this.getTotalSize())return{};var i=o+t,n=this._findNearestCell(o),r=this.getSizeAndPositionOfCell(n);o=r.offset+r.size;for(var l=n;o<i&&l<this._cellCount-1;)l++,o+=this.getSizeAndPositionOfCell(l).size;return{start:n,stop:l}}},{key:"resetCell",value:function(e){this._lastMeasuredIndex=Math.min(this._lastMeasuredIndex,e-1)}},{key:"_binarySearch",value:function(e,t,o){for(;t<=e;){var i=t+Math.floor((e-t)/2),n=this.getSizeAndPositionOfCell(i).offset;if(n===o)return i;n<o?t=i+1:n>o&&(e=i-1)}return t>0?t-1:0}},{key:"_exponentialSearch",value:function(e,t){for(var o=1;e<this._cellCount&&this.getSizeAndPositionOfCell(e).offset<t;)e+=o,o*=2;return this._binarySearch(Math.min(e,this._cellCount-1),Math.floor(e/2),t)}},{key:"_findNearestCell",value:function(e){if(isNaN(e))throw Error("Invalid offset ".concat(e," specified"));e=Math.max(0,e);var t=this.getSizeAndPositionOfLastMeasuredCell(),o=Math.max(0,this._lastMeasuredIndex);return t.offset>=e?this._binarySearch(o,0,e):this._exponentialSearch(o,e)}}])}(),q=["maxScrollSize"],K=function(){return(0,n.A)((function e(t){var o=t.maxScrollSize,n=void 0===o?"undefined"!=typeof window&&window.chrome?16777100:15e5:o,r=(0,U.A)(t,q);(0,i.A)(this,e),(0,a.A)(this,"_cellSizeAndPositionManager",void 0),(0,a.A)(this,"_maxScrollSize",void 0),this._cellSizeAndPositionManager=new V(r),this._maxScrollSize=n}),[{key:"areOffsetsAdjusted",value:function(){return this._cellSizeAndPositionManager.getTotalSize()>this._maxScrollSize}},{key:"configure",value:function(e){this._cellSizeAndPositionManager.configure(e)}},{key:"getCellCount",value:function(){return this._cellSizeAndPositionManager.getCellCount()}},{key:"getEstimatedCellSize",value:function(){return this._cellSizeAndPositionManager.getEstimatedCellSize()}},{key:"getLastMeasuredIndex",value:function(){return this._cellSizeAndPositionManager.getLastMeasuredIndex()}},{key:"getOffsetAdjustment",value:function(e){var t=e.containerSize,o=e.offset,i=this._cellSizeAndPositionManager.getTotalSize(),n=this.getTotalSize(),r=this._getOffsetPercentage({containerSize:t,offset:o,totalSize:n});return Math.round(r*(n-i))}},{key:"getSizeAndPositionOfCell",value:function(e){return this._cellSizeAndPositionManager.getSizeAndPositionOfCell(e)}},{key:"getSizeAndPositionOfLastMeasuredCell",value:function(){return this._cellSizeAndPositionManager.getSizeAndPositionOfLastMeasuredCell()}},{key:"getTotalSize",value:function(){return Math.min(this._maxScrollSize,this._cellSizeAndPositionManager.getTotalSize())}},{key:"getUpdatedOffsetForIndex",value:function(e){var t=e.align,o=void 0===t?"auto":t,i=e.containerSize,n=e.currentOffset,r=e.targetIndex;n=this._safeOffsetToOffset({containerSize:i,offset:n});var l=this._cellSizeAndPositionManager.getUpdatedOffsetForIndex({align:o,containerSize:i,currentOffset:n,targetIndex:r});return this._offsetToSafeOffset({containerSize:i,offset:l})}},{key:"getVisibleCellRange",value:function(e){var t=e.containerSize,o=e.offset;return o=this._safeOffsetToOffset({containerSize:t,offset:o}),this._cellSizeAndPositionManager.getVisibleCellRange({containerSize:t,offset:o})}},{key:"resetCell",value:function(e){this._cellSizeAndPositionManager.resetCell(e)}},{key:"_getOffsetPercentage",value:function(e){var t=e.containerSize,o=e.offset,i=e.totalSize;return i<=t?0:o/(i-t)}},{key:"_offsetToSafeOffset",value:function(e){var t=e.containerSize,o=e.offset,i=this._cellSizeAndPositionManager.getTotalSize(),n=this.getTotalSize();if(i===n)return o;var r=this._getOffsetPercentage({containerSize:t,offset:o,totalSize:i});return Math.round(r*(n-t))}},{key:"_safeOffsetToOffset",value:function(e){var t=e.containerSize,o=e.offset,i=this._cellSizeAndPositionManager.getTotalSize(),n=this.getTotalSize();if(i===n)return o;var r=this._getOffsetPercentage({containerSize:t,offset:o,totalSize:n});return Math.round(r*(i-t))}}])}();function X(e){var t=e.cellSize,o=e.cellSizeAndPositionManager,i=e.previousCellsCount,n=e.previousCellSize,r=e.previousScrollToAlignment,l=e.previousScrollToIndex,s=e.previousSize,a=e.scrollOffset,c=e.scrollToAlignment,d=e.scrollToIndex,h=e.size,u=e.sizeJustIncreasedFromZero,f=e.updateScrollIndexCallback,p=o.getCellCount(),_=d>=0&&d<p;_&&(h!==s||u||!n||"number"==typeof t&&t!==n||c!==r||d!==l)?f(d):!_&&p>0&&(h<s||p<i)&&a>o.getTotalSize()-h&&f(p-1)}var Y=(B="undefined"!=typeof window?window:"undefined"!=typeof self?self:{}).requestAnimationFrame||B.webkitRequestAnimationFrame||B.mozRequestAnimationFrame||B.oRequestAnimationFrame||B.msRequestAnimationFrame||function(e){return B.setTimeout(e,1e3/60)},J=B.cancelAnimationFrame||B.webkitCancelAnimationFrame||B.mozCancelAnimationFrame||B.oCancelAnimationFrame||B.msCancelAnimationFrame||function(e){B.clearTimeout(e)},Z=Y,Q=J,$=function(e){return Q(e.id)},ee=function(e,t){var o;Promise.resolve().then((function(){o=Date.now()}));var i=function(){Date.now()-o>=t?e.call():n.id=Z(i)},n={id:Z(i)};return n};function te(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,i)}return o}function oe(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?te(Object(o),!0).forEach((function(t){(0,a.A)(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):te(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}function ie(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(ie=function(){return!!e})()}var ne="requested",re=function(e){function t(e){var o,n,s,c;(0,i.A)(this,t),n=this,s=t,c=[e],s=(0,l.A)(s),o=(0,r.A)(n,ie()?Reflect.construct(s,c||[],(0,l.A)(n).constructor):s.apply(n,c)),(0,a.A)(o,"_onGridRenderedMemoizer",b()),(0,a.A)(o,"_onScrollMemoizer",b(!1)),(0,a.A)(o,"_deferredInvalidateColumnIndex",null),(0,a.A)(o,"_deferredInvalidateRowIndex",null),(0,a.A)(o,"_recomputeScrollLeftFlag",!1),(0,a.A)(o,"_recomputeScrollTopFlag",!1),(0,a.A)(o,"_horizontalScrollBarSize",0),(0,a.A)(o,"_verticalScrollBarSize",0),(0,a.A)(o,"_scrollbarPresenceChanged",!1),(0,a.A)(o,"_scrollingContainer",void 0),(0,a.A)(o,"_childrenToDisplay",void 0),(0,a.A)(o,"_columnStartIndex",void 0),(0,a.A)(o,"_columnStopIndex",void 0),(0,a.A)(o,"_rowStartIndex",void 0),(0,a.A)(o,"_rowStopIndex",void 0),(0,a.A)(o,"_renderedColumnStartIndex",0),(0,a.A)(o,"_renderedColumnStopIndex",0),(0,a.A)(o,"_renderedRowStartIndex",0),(0,a.A)(o,"_renderedRowStopIndex",0),(0,a.A)(o,"_initialScrollTop",void 0),(0,a.A)(o,"_initialScrollLeft",void 0),(0,a.A)(o,"_disablePointerEventsTimeoutId",void 0),(0,a.A)(o,"_styleCache",{}),(0,a.A)(o,"_cellCache",{}),(0,a.A)(o,"_debounceScrollEndedCallback",(function(){o._disablePointerEventsTimeoutId=null,o.setState({isScrolling:!1,needToResetStyleCache:!1})})),(0,a.A)(o,"_invokeOnGridRenderedHelper",(function(){var e=o.props.onSectionRendered;o._onGridRenderedMemoizer({callback:e,indices:{columnOverscanStartIndex:o._columnStartIndex,columnOverscanStopIndex:o._columnStopIndex,columnStartIndex:o._renderedColumnStartIndex,columnStopIndex:o._renderedColumnStopIndex,rowOverscanStartIndex:o._rowStartIndex,rowOverscanStopIndex:o._rowStopIndex,rowStartIndex:o._renderedRowStartIndex,rowStopIndex:o._renderedRowStopIndex}})})),(0,a.A)(o,"_setScrollingContainerRef",(function(e){o._scrollingContainer=e,"function"==typeof o.props.elementRef?o.props.elementRef(e):"object"===(0,j.A)(o.props.elementRef)&&(o.props.elementRef.current=e)})),(0,a.A)(o,"_onScroll",(function(e){e.target===o._scrollingContainer&&o.handleScrollEvent(e.target)}));var d=new K({cellCount:e.columnCount,cellSizeGetter:function(o){return t._wrapSizeGetter(e.columnWidth)(o)},estimatedCellSize:t._getEstimatedColumnSize(e)}),h=new K({cellCount:e.rowCount,cellSizeGetter:function(o){return t._wrapSizeGetter(e.rowHeight)(o)},estimatedCellSize:t._getEstimatedRowSize(e)});return o.state={instanceProps:{columnSizeAndPositionManager:d,rowSizeAndPositionManager:h,prevColumnWidth:e.columnWidth,prevRowHeight:e.rowHeight,prevColumnCount:e.columnCount,prevRowCount:e.rowCount,prevIsScrolling:!0===e.isScrolling,prevScrollToColumn:e.scrollToColumn,prevScrollToRow:e.scrollToRow,scrollbarSize:0,scrollbarSizeMeasured:!1},isScrolling:!1,scrollDirectionHorizontal:1,scrollDirectionVertical:1,scrollLeft:0,scrollTop:0,scrollPositionChangeReason:null,needToResetStyleCache:!1},e.scrollToRow>0&&(o._initialScrollTop=o._getCalculatedScrollTop(e,o.state)),e.scrollToColumn>0&&(o._initialScrollLeft=o._getCalculatedScrollLeft(e,o.state)),o}return(0,s.A)(t,e),(0,n.A)(t,[{key:"getOffsetForCell",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.alignment,o=void 0===t?this.props.scrollToAlignment:t,i=e.columnIndex,n=void 0===i?this.props.scrollToColumn:i,r=e.rowIndex,l=void 0===r?this.props.scrollToRow:r,s=oe(oe({},this.props),{},{scrollToAlignment:o,scrollToColumn:n,scrollToRow:l});return{scrollLeft:this._getCalculatedScrollLeft(s),scrollTop:this._getCalculatedScrollTop(s)}}},{key:"getTotalRowsHeight",value:function(){return this.state.instanceProps.rowSizeAndPositionManager.getTotalSize()}},{key:"getTotalColumnsWidth",value:function(){return this.state.instanceProps.columnSizeAndPositionManager.getTotalSize()}},{key:"handleScrollEvent",value:function(e){var t=e.scrollLeft,o=void 0===t?0:t,i=e.scrollTop,n=void 0===i?0:i;if(!(n<0)){this._debounceScrollEnded();var r=this.props,l=r.autoHeight,s=r.autoWidth,a=r.height,c=r.width,d=this.state.instanceProps,h=d.scrollbarSize,u=d.rowSizeAndPositionManager.getTotalSize(),f=d.columnSizeAndPositionManager.getTotalSize(),p=Math.min(Math.max(0,f-c+h),o),_=Math.min(Math.max(0,u-a+h),n);if(this.state.scrollLeft!==p||this.state.scrollTop!==_){var g={isScrolling:!0,scrollDirectionHorizontal:p!==this.state.scrollLeft?p>this.state.scrollLeft?1:-1:this.state.scrollDirectionHorizontal,scrollDirectionVertical:_!==this.state.scrollTop?_>this.state.scrollTop?1:-1:this.state.scrollDirectionVertical,scrollPositionChangeReason:"observed"};l||(g.scrollTop=_),s||(g.scrollLeft=p),g.needToResetStyleCache=!1,this.setState(g)}this._invokeOnScrollMemoizer({scrollLeft:p,scrollTop:_,totalColumnsWidth:f,totalRowsHeight:u})}}},{key:"invalidateCellSizeAfterRender",value:function(e){var t=e.columnIndex,o=e.rowIndex;this._deferredInvalidateColumnIndex="number"==typeof this._deferredInvalidateColumnIndex?Math.min(this._deferredInvalidateColumnIndex,t):t,this._deferredInvalidateRowIndex="number"==typeof this._deferredInvalidateRowIndex?Math.min(this._deferredInvalidateRowIndex,o):o}},{key:"measureAllCells",value:function(){var e=this.props,t=e.columnCount,o=e.rowCount,i=this.state.instanceProps;i.columnSizeAndPositionManager.getSizeAndPositionOfCell(t-1),i.rowSizeAndPositionManager.getSizeAndPositionOfCell(o-1)}},{key:"recomputeGridSize",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.columnIndex,o=void 0===t?0:t,i=e.rowIndex,n=void 0===i?0:i,r=this.props,l=r.scrollToColumn,s=r.scrollToRow,a=this.state.instanceProps;a.columnSizeAndPositionManager.resetCell(o),a.rowSizeAndPositionManager.resetCell(n),this._recomputeScrollLeftFlag=l>=0&&(1===this.state.scrollDirectionHorizontal?o<=l:o>=l),this._recomputeScrollTopFlag=s>=0&&(1===this.state.scrollDirectionVertical?n<=s:n>=s),this._styleCache={},this._cellCache={},this.forceUpdate()}},{key:"scrollToCell",value:function(e){var t=e.columnIndex,o=e.rowIndex,i=this.props.columnCount,n=this.props;i>1&&void 0!==t&&this._updateScrollLeftForScrollToColumn(oe(oe({},n),{},{scrollToColumn:t})),void 0!==o&&this._updateScrollTopForScrollToRow(oe(oe({},n),{},{scrollToRow:o}))}},{key:"componentDidMount",value:function(){var e=this.props,o=e.getScrollbarSize,i=e.height,n=e.scrollLeft,r=e.scrollToColumn,l=e.scrollTop,s=e.scrollToRow,a=e.width,c=this.state.instanceProps;if(this._initialScrollTop=0,this._initialScrollLeft=0,this._handleInvalidatedGridSize(),c.scrollbarSizeMeasured||this.setState((function(e){var t=oe(oe({},e),{},{needToResetStyleCache:!1});return t.instanceProps.scrollbarSize=o(),t.instanceProps.scrollbarSizeMeasured=!0,t})),"number"==typeof n&&n>=0||"number"==typeof l&&l>=0){var d=t._getScrollToPositionStateUpdate({prevState:this.state,scrollLeft:n,scrollTop:l});d&&(d.needToResetStyleCache=!1,this.setState(d))}this._scrollingContainer&&(this._scrollingContainer.scrollLeft!==this.state.scrollLeft&&(this._scrollingContainer.scrollLeft=this.state.scrollLeft),this._scrollingContainer.scrollTop!==this.state.scrollTop&&(this._scrollingContainer.scrollTop=this.state.scrollTop));var h=i>0&&a>0;r>=0&&h&&this._updateScrollLeftForScrollToColumn(),s>=0&&h&&this._updateScrollTopForScrollToRow(),this._invokeOnGridRenderedHelper(),this._invokeOnScrollMemoizer({scrollLeft:n||0,scrollTop:l||0,totalColumnsWidth:c.columnSizeAndPositionManager.getTotalSize(),totalRowsHeight:c.rowSizeAndPositionManager.getTotalSize()}),this._maybeCallOnScrollbarPresenceChange()}},{key:"componentDidUpdate",value:function(e,t){var o=this,i=this.props,n=i.autoHeight,r=i.autoWidth,l=i.columnCount,s=i.height,a=i.rowCount,c=i.scrollToAlignment,d=i.scrollToColumn,h=i.scrollToRow,u=i.width,f=this.state,p=f.scrollLeft,_=f.scrollPositionChangeReason,g=f.scrollTop,v=f.instanceProps;this._handleInvalidatedGridSize();var m=l>0&&0===e.columnCount||a>0&&0===e.rowCount;_===ne&&(!r&&p>=0&&(p!==this._scrollingContainer.scrollLeft||m)&&(this._scrollingContainer.scrollLeft=p),!n&&g>=0&&(g!==this._scrollingContainer.scrollTop||m)&&(this._scrollingContainer.scrollTop=g));var S=(0===e.width||0===e.height)&&s>0&&u>0;if(this._recomputeScrollLeftFlag?(this._recomputeScrollLeftFlag=!1,this._updateScrollLeftForScrollToColumn(this.props)):X({cellSizeAndPositionManager:v.columnSizeAndPositionManager,previousCellsCount:e.columnCount,previousCellSize:e.columnWidth,previousScrollToAlignment:e.scrollToAlignment,previousScrollToIndex:e.scrollToColumn,previousSize:e.width,scrollOffset:p,scrollToAlignment:c,scrollToIndex:d,size:u,sizeJustIncreasedFromZero:S,updateScrollIndexCallback:function(){return o._updateScrollLeftForScrollToColumn(o.props)}}),this._recomputeScrollTopFlag?(this._recomputeScrollTopFlag=!1,this._updateScrollTopForScrollToRow(this.props)):X({cellSizeAndPositionManager:v.rowSizeAndPositionManager,previousCellsCount:e.rowCount,previousCellSize:e.rowHeight,previousScrollToAlignment:e.scrollToAlignment,previousScrollToIndex:e.scrollToRow,previousSize:e.height,scrollOffset:g,scrollToAlignment:c,scrollToIndex:h,size:s,sizeJustIncreasedFromZero:S,updateScrollIndexCallback:function(){return o._updateScrollTopForScrollToRow(o.props)}}),this._invokeOnGridRenderedHelper(),p!==t.scrollLeft||g!==t.scrollTop){var C=v.rowSizeAndPositionManager.getTotalSize(),y=v.columnSizeAndPositionManager.getTotalSize();this._invokeOnScrollMemoizer({scrollLeft:p,scrollTop:g,totalColumnsWidth:y,totalRowsHeight:C})}this._maybeCallOnScrollbarPresenceChange()}},{key:"componentWillUnmount",value:function(){this._disablePointerEventsTimeoutId&&$(this._disablePointerEventsTimeoutId)}},{key:"render",value:function(){var e=this.props,t=e.autoContainerWidth,o=e.autoHeight,i=e.autoWidth,n=e.className,r=e.containerProps,l=e.containerRole,s=e.containerStyle,a=e.height,d=e.id,h=e.noContentRenderer,u=e.role,f=e.style,p=e.tabIndex,_=e.width,g=this.state,v=g.instanceProps,m=g.needToResetStyleCache,S=this._isScrolling(),C={boxSizing:"border-box",direction:"ltr",height:o?"auto":a,position:"relative",width:i?"auto":_,WebkitOverflowScrolling:"touch",willChange:"transform"};m&&(this._styleCache={}),this.state.isScrolling||this._resetStyleCache(),this._calculateChildrenToRender(this.props,this.state);var y=v.columnSizeAndPositionManager.getTotalSize(),w=v.rowSizeAndPositionManager.getTotalSize(),x=w>a?v.scrollbarSize:0,R=y>_?v.scrollbarSize:0;R===this._horizontalScrollBarSize&&x===this._verticalScrollBarSize||(this._horizontalScrollBarSize=R,this._verticalScrollBarSize=x,this._scrollbarPresenceChanged=!0),C.overflowX=y+x<=_?"hidden":"auto",C.overflowY=w+R<=a?"hidden":"auto";var b=this._childrenToDisplay,I=0===b.length&&a>0&&_>0;return c.createElement("div",(0,z.A)({ref:this._setScrollingContainerRef},r,{"aria-label":this.props["aria-label"],"aria-readonly":this.props["aria-readonly"],className:(0,T.default)("ReactVirtualized__Grid",n),id:d,onScroll:this._onScroll,role:u,style:oe(oe({},C),f),tabIndex:p}),b.length>0&&c.createElement("div",{className:"ReactVirtualized__Grid__innerScrollContainer",role:l,style:oe({width:t?"auto":y,height:w,maxWidth:y,maxHeight:w,overflow:"hidden",pointerEvents:S?"none":"",position:"relative"},s)},b),I&&h())}},{key:"_calculateChildrenToRender",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.state,o=e.cellRenderer,i=e.cellRangeRenderer,n=e.columnCount,r=e.deferredMeasurementCache,l=e.height,s=e.overscanColumnCount,a=e.overscanIndicesGetter,c=e.overscanRowCount,d=e.rowCount,h=e.width,u=e.isScrollingOptOut,f=t.scrollDirectionHorizontal,p=t.scrollDirectionVertical,_=t.instanceProps,g=this._initialScrollTop>0?this._initialScrollTop:t.scrollTop,v=this._initialScrollLeft>0?this._initialScrollLeft:t.scrollLeft,m=this._isScrolling(e,t);if(this._childrenToDisplay=[],l>0&&h>0){var S=_.columnSizeAndPositionManager.getVisibleCellRange({containerSize:h,offset:v}),C=_.rowSizeAndPositionManager.getVisibleCellRange({containerSize:l,offset:g}),y=_.columnSizeAndPositionManager.getOffsetAdjustment({containerSize:h,offset:v}),w=_.rowSizeAndPositionManager.getOffsetAdjustment({containerSize:l,offset:g});this._renderedColumnStartIndex=S.start,this._renderedColumnStopIndex=S.stop,this._renderedRowStartIndex=C.start,this._renderedRowStopIndex=C.stop;var x=a({direction:"horizontal",cellCount:n,overscanCellsCount:s,scrollDirection:f,startIndex:"number"==typeof S.start?S.start:0,stopIndex:"number"==typeof S.stop?S.stop:-1}),R=a({direction:"vertical",cellCount:d,overscanCellsCount:c,scrollDirection:p,startIndex:"number"==typeof C.start?C.start:0,stopIndex:"number"==typeof C.stop?C.stop:-1}),z=x.overscanStartIndex,T=x.overscanStopIndex,b=R.overscanStartIndex,I=R.overscanStopIndex;if(r){if(!r.hasFixedHeight())for(var A=b;A<=I;A++)if(!r.has(A,0)){z=0,T=n-1;break}if(!r.hasFixedWidth())for(var k=z;k<=T;k++)if(!r.has(0,k)){b=0,I=d-1;break}}this._childrenToDisplay=i({cellCache:this._cellCache,cellRenderer:o,columnSizeAndPositionManager:_.columnSizeAndPositionManager,columnStartIndex:z,columnStopIndex:T,deferredMeasurementCache:r,horizontalOffsetAdjustment:y,isScrolling:m,isScrollingOptOut:u,parent:this,rowSizeAndPositionManager:_.rowSizeAndPositionManager,rowStartIndex:b,rowStopIndex:I,scrollLeft:v,scrollTop:g,styleCache:this._styleCache,verticalOffsetAdjustment:w,visibleColumnIndices:S,visibleRowIndices:C}),this._columnStartIndex=z,this._columnStopIndex=T,this._rowStartIndex=b,this._rowStopIndex=I}}},{key:"_debounceScrollEnded",value:function(){var e=this.props.scrollingResetTimeInterval;this._disablePointerEventsTimeoutId&&$(this._disablePointerEventsTimeoutId),this._disablePointerEventsTimeoutId=ee(this._debounceScrollEndedCallback,e)}},{key:"_handleInvalidatedGridSize",value:function(){if("number"==typeof this._deferredInvalidateColumnIndex&&"number"==typeof this._deferredInvalidateRowIndex){var e=this._deferredInvalidateColumnIndex,t=this._deferredInvalidateRowIndex;this._deferredInvalidateColumnIndex=null,this._deferredInvalidateRowIndex=null,this.recomputeGridSize({columnIndex:e,rowIndex:t})}}},{key:"_invokeOnScrollMemoizer",value:function(e){var t=this,o=e.scrollLeft,i=e.scrollTop,n=e.totalColumnsWidth,r=e.totalRowsHeight;this._onScrollMemoizer({callback:function(e){var o=e.scrollLeft,i=e.scrollTop,l=t.props,s=l.height;(0,l.onScroll)({clientHeight:s,clientWidth:l.width,scrollHeight:r,scrollLeft:o,scrollTop:i,scrollWidth:n})},indices:{scrollLeft:o,scrollTop:i}})}},{key:"_isScrolling",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.state;return Object.hasOwnProperty.call(e,"isScrolling")?Boolean(e.isScrolling):Boolean(t.isScrolling)}},{key:"_maybeCallOnScrollbarPresenceChange",value:function(){if(this._scrollbarPresenceChanged){var e=this.props.onScrollbarPresenceChange;this._scrollbarPresenceChanged=!1,e({horizontal:this._horizontalScrollBarSize>0,size:this.state.instanceProps.scrollbarSize,vertical:this._verticalScrollBarSize>0})}}},{key:"scrollToPosition",value:function(e){var o=e.scrollLeft,i=e.scrollTop,n=t._getScrollToPositionStateUpdate({prevState:this.state,scrollLeft:o,scrollTop:i});n&&(n.needToResetStyleCache=!1,this.setState(n))}},{key:"_getCalculatedScrollLeft",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.state;return t._getCalculatedScrollLeft(e,o)}},{key:"_updateScrollLeftForScrollToColumn",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.state,i=t._getScrollLeftForScrollToColumnStateUpdate(e,o);i&&(i.needToResetStyleCache=!1,this.setState(i))}},{key:"_getCalculatedScrollTop",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.state;return t._getCalculatedScrollTop(e,o)}},{key:"_resetStyleCache",value:function(){var e=this._styleCache,t=this._cellCache,o=this.props.isScrollingOptOut;this._cellCache={},this._styleCache={};for(var i=this._rowStartIndex;i<=this._rowStopIndex;i++)for(var n=this._columnStartIndex;n<=this._columnStopIndex;n++){var r="".concat(i,"-").concat(n);this._styleCache[r]=e[r],o&&(this._cellCache[r]=t[r])}}},{key:"_updateScrollTopForScrollToRow",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.state,i=t._getScrollTopForScrollToRowStateUpdate(e,o);i&&(i.needToResetStyleCache=!1,this.setState(i))}}],[{key:"getDerivedStateFromProps",value:function(e,o){var i={};0===e.columnCount&&0!==o.scrollLeft||0===e.rowCount&&0!==o.scrollTop?(i.scrollLeft=0,i.scrollTop=0):(e.scrollLeft!==o.scrollLeft&&e.scrollToColumn<0||e.scrollTop!==o.scrollTop&&e.scrollToRow<0)&&Object.assign(i,t._getScrollToPositionStateUpdate({prevState:o,scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}));var n,r,l=o.instanceProps;return i.needToResetStyleCache=!1,e.columnWidth===l.prevColumnWidth&&e.rowHeight===l.prevRowHeight||(i.needToResetStyleCache=!0),l.columnSizeAndPositionManager.configure({cellCount:e.columnCount,estimatedCellSize:t._getEstimatedColumnSize(e),cellSizeGetter:t._wrapSizeGetter(e.columnWidth)}),l.rowSizeAndPositionManager.configure({cellCount:e.rowCount,estimatedCellSize:t._getEstimatedRowSize(e),cellSizeGetter:t._wrapSizeGetter(e.rowHeight)}),0!==l.prevColumnCount&&0!==l.prevRowCount||(l.prevColumnCount=0,l.prevRowCount=0),e.autoHeight&&!1===e.isScrolling&&!0===l.prevIsScrolling&&Object.assign(i,{isScrolling:!1}),N({cellCount:l.prevColumnCount,cellSize:"number"==typeof l.prevColumnWidth?l.prevColumnWidth:null,computeMetadataCallback:function(){return l.columnSizeAndPositionManager.resetCell(0)},computeMetadataCallbackProps:e,nextCellsCount:e.columnCount,nextCellSize:"number"==typeof e.columnWidth?e.columnWidth:null,nextScrollToIndex:e.scrollToColumn,scrollToIndex:l.prevScrollToColumn,updateScrollOffsetForScrollToIndex:function(){n=t._getScrollLeftForScrollToColumnStateUpdate(e,o)}}),N({cellCount:l.prevRowCount,cellSize:"number"==typeof l.prevRowHeight?l.prevRowHeight:null,computeMetadataCallback:function(){return l.rowSizeAndPositionManager.resetCell(0)},computeMetadataCallbackProps:e,nextCellsCount:e.rowCount,nextCellSize:"number"==typeof e.rowHeight?e.rowHeight:null,nextScrollToIndex:e.scrollToRow,scrollToIndex:l.prevScrollToRow,updateScrollOffsetForScrollToIndex:function(){r=t._getScrollTopForScrollToRowStateUpdate(e,o)}}),l.prevColumnCount=e.columnCount,l.prevColumnWidth=e.columnWidth,l.prevIsScrolling=!0===e.isScrolling,l.prevRowCount=e.rowCount,l.prevRowHeight=e.rowHeight,l.prevScrollToColumn=e.scrollToColumn,l.prevScrollToRow=e.scrollToRow,l.scrollbarSize=e.getScrollbarSize(),void 0===l.scrollbarSize?(l.scrollbarSizeMeasured=!1,l.scrollbarSize=0):l.scrollbarSizeMeasured=!0,i.instanceProps=l,oe(oe(oe({},i),n),r)}},{key:"_getEstimatedColumnSize",value:function(e){return"number"==typeof e.columnWidth?e.columnWidth:e.estimatedColumnSize}},{key:"_getEstimatedRowSize",value:function(e){return"number"==typeof e.rowHeight?e.rowHeight:e.estimatedRowSize}},{key:"_getScrollToPositionStateUpdate",value:function(e){var t=e.prevState,o=e.scrollLeft,i=e.scrollTop,n={scrollPositionChangeReason:ne};return"number"==typeof o&&o>=0&&(n.scrollDirectionHorizontal=o>t.scrollLeft?1:-1,n.scrollLeft=o),"number"==typeof i&&i>=0&&(n.scrollDirectionVertical=i>t.scrollTop?1:-1,n.scrollTop=i),"number"==typeof o&&o>=0&&o!==t.scrollLeft||"number"==typeof i&&i>=0&&i!==t.scrollTop?n:{}}},{key:"_wrapSizeGetter",value:function(e){return"function"==typeof e?e:function(){return e}}},{key:"_getCalculatedScrollLeft",value:function(e,t){var o=e.columnCount,i=e.height,n=e.scrollToAlignment,r=e.scrollToColumn,l=e.width,s=t.scrollLeft,a=t.instanceProps;if(o>0){var c=o-1,d=r<0?c:Math.min(c,r),h=a.rowSizeAndPositionManager.getTotalSize(),u=a.scrollbarSizeMeasured&&h>i?a.scrollbarSize:0;return a.columnSizeAndPositionManager.getUpdatedOffsetForIndex({align:n,containerSize:l-u,currentOffset:s,targetIndex:d})}return 0}},{key:"_getScrollLeftForScrollToColumnStateUpdate",value:function(e,o){var i=o.scrollLeft,n=t._getCalculatedScrollLeft(e,o);return"number"==typeof n&&n>=0&&i!==n?t._getScrollToPositionStateUpdate({prevState:o,scrollLeft:n,scrollTop:-1}):{}}},{key:"_getCalculatedScrollTop",value:function(e,t){var o=e.height,i=e.rowCount,n=e.scrollToAlignment,r=e.scrollToRow,l=e.width,s=t.scrollTop,a=t.instanceProps;if(i>0){var c=i-1,d=r<0?c:Math.min(c,r),h=a.columnSizeAndPositionManager.getTotalSize(),u=a.scrollbarSizeMeasured&&h>l?a.scrollbarSize:0;return a.rowSizeAndPositionManager.getUpdatedOffsetForIndex({align:n,containerSize:o-u,currentOffset:s,targetIndex:d})}return 0}},{key:"_getScrollTopForScrollToRowStateUpdate",value:function(e,o){var i=o.scrollTop,n=t._getCalculatedScrollTop(e,o);return"number"==typeof n&&n>=0&&i!==n?t._getScrollToPositionStateUpdate({prevState:o,scrollLeft:-1,scrollTop:n}):{}}}])}(c.PureComponent);(0,a.A)(re,"defaultProps",{"aria-label":"grid","aria-readonly":!0,autoContainerWidth:!1,autoHeight:!1,autoWidth:!1,cellRangeRenderer:function(e){for(var t=e.cellCache,o=e.cellRenderer,i=e.columnSizeAndPositionManager,n=e.columnStartIndex,r=e.columnStopIndex,l=e.deferredMeasurementCache,s=e.horizontalOffsetAdjustment,a=e.isScrolling,d=e.isScrollingOptOut,h=e.parent,u=e.rowSizeAndPositionManager,f=e.rowStartIndex,p=e.rowStopIndex,_=e.styleCache,g=e.verticalOffsetAdjustment,v=e.visibleColumnIndices,m=e.visibleRowIndices,S=[],C=i.areOffsetsAdjusted()||u.areOffsetsAdjusted(),y=!a&&!C,w=f;w<=p;w++)for(var x=u.getSizeAndPositionOfCell(w),R=n;R<=r;R++){var z=i.getSizeAndPositionOfCell(R),T=R>=v.start&&R<=v.stop&&w>=m.start&&w<=m.stop,b="".concat(w,"-").concat(R),I=void 0;y&&_[b]?I=_[b]:l&&!l.has(w,R)?I={height:"auto",left:0,position:"absolute",top:0,width:"auto"}:(I={height:x.size,left:z.offset+s,position:"absolute",top:x.offset+g,width:z.size},_[b]=I);var A={columnIndex:R,isScrolling:a,isVisible:T,key:b,parent:h,rowIndex:w,style:I},k=void 0;!d&&!a||s||g?k=o(A):(t[b]||(t[b]=o(A)),k=t[b]),null!=k&&!1!==k&&(k.props.role||(k=c.cloneElement(k,{role:"gridcell"})),S.push(k))}return S},containerRole:"row",containerStyle:{},estimatedColumnSize:100,estimatedRowSize:30,getScrollbarSize:I.default,noContentRenderer:function(){return null},onScroll:function(){},onScrollbarPresenceChange:function(){},onSectionRendered:function(){},overscanColumnCount:0,overscanIndicesGetter:function(e){var t=e.cellCount,o=e.overscanCellsCount,i=e.scrollDirection,n=e.startIndex,r=e.stopIndex;return 1===i?{overscanStartIndex:Math.max(0,n),overscanStopIndex:Math.min(t-1,r+o)}:{overscanStartIndex:Math.max(0,n-o),overscanStopIndex:Math.min(t-1,r)}},overscanRowCount:10,role:"grid",scrollingResetTimeInterval:150,scrollToAlignment:"auto",scrollToColumn:-1,scrollToRow:-1,style:{},tabIndex:0,isScrollingOptOut:!1}),(0,d.polyfill)(re);const le=re;function se(e){var t=e.cellCount,o=e.overscanCellsCount,i=e.scrollDirection,n=e.startIndex,r=e.stopIndex;return o=Math.max(1,o),1===i?{overscanStartIndex:Math.max(0,n-1),overscanStopIndex:Math.min(t-1,r+o)}:{overscanStartIndex:Math.max(0,n-o),overscanStopIndex:Math.min(t-1,r+1)}}var ae=o(45458);function ce(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(ce=function(){return!!e})()}var de=function(e){function t(e,o){var n,s,a,c;return(0,i.A)(this,t),s=this,a=t,c=[e,o],a=(0,l.A)(a),(n=(0,r.A)(s,ce()?Reflect.construct(a,c||[],(0,l.A)(s).constructor):a.apply(s,c)))._loadMoreRowsMemoizer=b(),n._onRowsRendered=n._onRowsRendered.bind(n),n._registerChild=n._registerChild.bind(n),n}return(0,s.A)(t,e),(0,n.A)(t,[{key:"resetLoadMoreRowsCache",value:function(e){this._loadMoreRowsMemoizer=b(),e&&this._doStuff(this._lastRenderedStartIndex,this._lastRenderedStopIndex)}},{key:"render",value:function(){return(0,this.props.children)({onRowsRendered:this._onRowsRendered,registerChild:this._registerChild})}},{key:"_loadUnloadedRanges",value:function(e){var t=this,o=this.props.loadMoreRows;e.forEach((function(e){var i=o(e);i&&i.then((function(){var o;(o={lastRenderedStartIndex:t._lastRenderedStartIndex,lastRenderedStopIndex:t._lastRenderedStopIndex,startIndex:e.startIndex,stopIndex:e.stopIndex}).startIndex>o.lastRenderedStopIndex||o.stopIndex<o.lastRenderedStartIndex||t._registeredChild&&function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,o="function"==typeof e.recomputeGridSize?e.recomputeGridSize:e.recomputeRowHeights;o?o.call(e,t):e.forceUpdate()}(t._registeredChild,t._lastRenderedStartIndex)}))}))}},{key:"_onRowsRendered",value:function(e){var t=e.startIndex,o=e.stopIndex;this._lastRenderedStartIndex=t,this._lastRenderedStopIndex=o,this._doStuff(t,o)}},{key:"_doStuff",value:function(e,t){var o,i=this,n=this.props,r=n.isRowLoaded,l=n.minimumBatchSize,s=n.rowCount,a=n.threshold,c=function(e){for(var t=e.isRowLoaded,o=e.minimumBatchSize,i=e.rowCount,n=e.stopIndex,r=[],l=null,s=null,a=e.startIndex;a<=n;a++)t({index:a})?null!==s&&(r.push({startIndex:l,stopIndex:s}),l=s=null):(s=a,null===l&&(l=a));if(null!==s){for(var c=Math.min(Math.max(s,l+o-1),i-1),d=s+1;d<=c&&!t({index:d});d++)s=d;r.push({startIndex:l,stopIndex:s})}if(r.length)for(var h=r[0];h.stopIndex-h.startIndex+1<o&&h.startIndex>0;){var u=h.startIndex-1;if(t({index:u}))break;h.startIndex=u}return r}({isRowLoaded:r,minimumBatchSize:l,rowCount:s,startIndex:Math.max(0,e-a),stopIndex:Math.min(s-1,t+a)}),d=(o=[]).concat.apply(o,(0,ae.A)(c.map((function(e){return[e.startIndex,e.stopIndex]}))));this._loadMoreRowsMemoizer({callback:function(){i._loadUnloadedRanges(c)},indices:{squashedUnloadedRanges:d}})}},{key:"_registerChild",value:function(e){this._registeredChild=e}}])}(c.PureComponent);function he(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(he=function(){return!!e})()}(0,a.A)(de,"defaultProps",{minimumBatchSize:10,rowCount:0,threshold:15}),de.propTypes={};var ue=function(e){function t(){var e,o,n,s;(0,i.A)(this,t);for(var c=arguments.length,d=new Array(c),h=0;h<c;h++)d[h]=arguments[h];return o=this,n=t,s=[].concat(d),n=(0,l.A)(n),e=(0,r.A)(o,he()?Reflect.construct(n,s||[],(0,l.A)(o).constructor):n.apply(o,s)),(0,a.A)(e,"Grid",void 0),(0,a.A)(e,"_cellRenderer",(function(t){var o=t.parent,i=t.rowIndex,n=t.style,r=t.isScrolling,l=t.isVisible,s=t.key,a=e.props.rowRenderer,c=Object.getOwnPropertyDescriptor(n,"width");return c&&c.writable&&(n.width="100%"),a({index:i,style:n,isScrolling:r,isVisible:l,key:s,parent:o})})),(0,a.A)(e,"_setRef",(function(t){e.Grid=t})),(0,a.A)(e,"_onScroll",(function(t){var o=t.clientHeight,i=t.scrollHeight,n=t.scrollTop;(0,e.props.onScroll)({clientHeight:o,scrollHeight:i,scrollTop:n})})),(0,a.A)(e,"_onSectionRendered",(function(t){var o=t.rowOverscanStartIndex,i=t.rowOverscanStopIndex,n=t.rowStartIndex,r=t.rowStopIndex;(0,e.props.onRowsRendered)({overscanStartIndex:o,overscanStopIndex:i,startIndex:n,stopIndex:r})})),e}return(0,s.A)(t,e),(0,n.A)(t,[{key:"forceUpdateGrid",value:function(){this.Grid&&this.Grid.forceUpdate()}},{key:"getOffsetForRow",value:function(e){var t=e.alignment,o=e.index;return this.Grid?this.Grid.getOffsetForCell({alignment:t,rowIndex:o,columnIndex:0}).scrollTop:0}},{key:"invalidateCellSizeAfterRender",value:function(e){var t=e.columnIndex,o=e.rowIndex;this.Grid&&this.Grid.invalidateCellSizeAfterRender({rowIndex:o,columnIndex:t})}},{key:"measureAllRows",value:function(){this.Grid&&this.Grid.measureAllCells()}},{key:"recomputeGridSize",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.columnIndex,o=void 0===t?0:t,i=e.rowIndex,n=void 0===i?0:i;this.Grid&&this.Grid.recomputeGridSize({rowIndex:n,columnIndex:o})}},{key:"recomputeRowHeights",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;this.Grid&&this.Grid.recomputeGridSize({rowIndex:e,columnIndex:0})}},{key:"scrollToPosition",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;this.Grid&&this.Grid.scrollToPosition({scrollTop:e})}},{key:"scrollToRow",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;this.Grid&&this.Grid.scrollToCell({columnIndex:0,rowIndex:e})}},{key:"render",value:function(){var e=this.props,t=e.className,o=e.noRowsRenderer,i=e.scrollToIndex,n=e.width,r=(0,T.default)("ReactVirtualized__List",t);return c.createElement(le,(0,z.A)({},this.props,{autoContainerWidth:!0,cellRenderer:this._cellRenderer,className:r,columnWidth:n,columnCount:1,noContentRenderer:o,onScroll:this._onScroll,onSectionRendered:this._onSectionRendered,ref:this._setRef,scrollToRow:i}))}}])}(c.PureComponent);(0,a.A)(ue,"defaultProps",{autoHeight:!1,estimatedRowSize:30,onScroll:function(){},noRowsRenderer:function(){return null},onRowsRendered:function(){},overscanIndicesGetter:se,overscanRowCount:10,scrollToAlignment:"auto",scrollToIndex:-1,style:{}});var fe=o(80296);const pe=function(e,t,o,i,n){return"function"==typeof o?function(e,t,o,i,n){for(var r=o+1;t<=o;){var l=t+o>>>1;n(e[l],i)>=0?(r=l,o=l-1):t=l+1}return r}(e,void 0===i?0:0|i,void 0===n?e.length-1:0|n,t,o):function(e,t,o,i){for(var n=o+1;t<=o;){var r=t+o>>>1;e[r]>=i?(n=r,o=r-1):t=r+1}return n}(e,void 0===o?0:0|o,void 0===i?e.length-1:0|i,t)};function _e(e,t,o,i,n){this.mid=e,this.left=t,this.right=o,this.leftPoints=i,this.rightPoints=n,this.count=(t?t.count:0)+(o?o.count:0)+i.length}var ge=_e.prototype;function ve(e,t){e.mid=t.mid,e.left=t.left,e.right=t.right,e.leftPoints=t.leftPoints,e.rightPoints=t.rightPoints,e.count=t.count}function me(e,t){var o=be(t);e.mid=o.mid,e.left=o.left,e.right=o.right,e.leftPoints=o.leftPoints,e.rightPoints=o.rightPoints,e.count=o.count}function Se(e,t){var o=e.intervals([]);o.push(t),me(e,o)}function Ce(e,t){var o=e.intervals([]),i=o.indexOf(t);return i<0?0:(o.splice(i,1),me(e,o),1)}function ye(e,t,o){for(var i=0;i<e.length&&e[i][0]<=t;++i){var n=o(e[i]);if(n)return n}}function we(e,t,o){for(var i=e.length-1;i>=0&&e[i][1]>=t;--i){var n=o(e[i]);if(n)return n}}function xe(e,t){for(var o=0;o<e.length;++o){var i=t(e[o]);if(i)return i}}function Re(e,t){return e-t}function ze(e,t){return e[0]-t[0]||e[1]-t[1]}function Te(e,t){return e[1]-t[1]||e[0]-t[0]}function be(e){if(0===e.length)return null;for(var t=[],o=0;o<e.length;++o)t.push(e[o][0],e[o][1]);t.sort(Re);var i=t[t.length>>1],n=[],r=[],l=[];for(o=0;o<e.length;++o){var s=e[o];s[1]<i?n.push(s):i<s[0]?r.push(s):l.push(s)}var a=l,c=l.slice();return a.sort(ze),c.sort(Te),new _e(i,be(n),be(r),a,c)}function Ie(e){this.root=e}ge.intervals=function(e){return e.push.apply(e,this.leftPoints),this.left&&this.left.intervals(e),this.right&&this.right.intervals(e),e},ge.insert=function(e){var t=this.count-this.leftPoints.length;if(this.count+=1,e[1]<this.mid)this.left?4*(this.left.count+1)>3*(t+1)?Se(this,e):this.left.insert(e):this.left=be([e]);else if(e[0]>this.mid)this.right?4*(this.right.count+1)>3*(t+1)?Se(this,e):this.right.insert(e):this.right=be([e]);else{var o=pe(this.leftPoints,e,ze),i=pe(this.rightPoints,e,Te);this.leftPoints.splice(o,0,e),this.rightPoints.splice(i,0,e)}},ge.remove=function(e){var t=this.count-this.leftPoints;if(e[1]<this.mid)return this.left?4*(this.right?this.right.count:0)>3*(t-1)?Ce(this,e):2===(r=this.left.remove(e))?(this.left=null,this.count-=1,1):(1===r&&(this.count-=1),r):0;if(e[0]>this.mid)return this.right?4*(this.left?this.left.count:0)>3*(t-1)?Ce(this,e):2===(r=this.right.remove(e))?(this.right=null,this.count-=1,1):(1===r&&(this.count-=1),r):0;if(1===this.count)return this.leftPoints[0]===e?2:0;if(1===this.leftPoints.length&&this.leftPoints[0]===e){if(this.left&&this.right){for(var o=this,i=this.left;i.right;)o=i,i=i.right;if(o===this)i.right=this.right;else{var n=this.left,r=this.right;o.count-=i.count,o.right=i.left,i.left=n,i.right=r}ve(this,i),this.count=(this.left?this.left.count:0)+(this.right?this.right.count:0)+this.leftPoints.length}else this.left?ve(this,this.left):ve(this,this.right);return 1}for(n=pe(this.leftPoints,e,ze);n<this.leftPoints.length&&this.leftPoints[n][0]===e[0];++n)if(this.leftPoints[n]===e)for(this.count-=1,this.leftPoints.splice(n,1),r=pe(this.rightPoints,e,Te);r<this.rightPoints.length&&this.rightPoints[r][1]===e[1];++r)if(this.rightPoints[r]===e)return this.rightPoints.splice(r,1),1;return 0},ge.queryPoint=function(e,t){return e<this.mid?this.left&&(o=this.left.queryPoint(e,t))?o:ye(this.leftPoints,e,t):e>this.mid?this.right&&(o=this.right.queryPoint(e,t))?o:we(this.rightPoints,e,t):xe(this.leftPoints,t);var o},ge.queryInterval=function(e,t,o){var i;return e<this.mid&&this.left&&(i=this.left.queryInterval(e,t,o))||t>this.mid&&this.right&&(i=this.right.queryInterval(e,t,o))?i:t<this.mid?ye(this.leftPoints,t,o):e>this.mid?we(this.rightPoints,e,o):xe(this.leftPoints,o)};var Ae=Ie.prototype;Ae.insert=function(e){this.root?this.root.insert(e):this.root=new _e(e[0],null,null,[e],[e])},Ae.remove=function(e){if(this.root){var t=this.root.remove(e);return 2===t&&(this.root=null),0!==t}return!1},Ae.queryPoint=function(e,t){if(this.root)return this.root.queryPoint(e,t)},Ae.queryInterval=function(e,t,o){if(e<=t&&this.root)return this.root.queryInterval(e,t,o)},Object.defineProperty(Ae,"count",{get:function(){return this.root?this.root.count:0}}),Object.defineProperty(Ae,"intervals",{get:function(){return this.root?this.root.intervals([]):[]}});var ke=function(){return(0,n.A)((function e(){(0,i.A)(this,e),(0,a.A)(this,"_columnSizeMap",{}),(0,a.A)(this,"_intervalTree",new Ie(null)),(0,a.A)(this,"_leftMap",{})}),[{key:"estimateTotalHeight",value:function(e,t,o){var i=e-this.count;return this.tallestColumnSize+Math.ceil(i/t)*o}},{key:"range",value:function(e,t,o){var i=this;this._intervalTree.queryInterval(e,e+t,(function(e){var t=(0,fe.A)(e,3),n=t[0],r=(t[1],t[2]);return o(r,i._leftMap[r],n)}))}},{key:"setPosition",value:function(e,t,o,i){this._intervalTree.insert([o,o+i,e]),this._leftMap[e]=t;var n=this._columnSizeMap,r=n[t];n[t]=void 0===r?o+i:Math.max(r,o+i)}},{key:"count",get:function(){return this._intervalTree.count}},{key:"shortestColumnSize",get:function(){var e=this._columnSizeMap,t=0;for(var o in e){var i=e[o];t=0===t?i:Math.min(t,i)}return t}},{key:"tallestColumnSize",get:function(){var e=this._columnSizeMap,t=0;for(var o in e){var i=e[o];t=Math.max(t,i)}return t}}])}();function Me(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,i)}return o}function Oe(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?Me(Object(o),!0).forEach((function(t){(0,a.A)(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):Me(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}function Pe(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(Pe=function(){return!!e})()}var Le=function(e){function t(){var e,o,n,s;(0,i.A)(this,t);for(var c=arguments.length,d=new Array(c),h=0;h<c;h++)d[h]=arguments[h];return o=this,n=t,s=[].concat(d),n=(0,l.A)(n),e=(0,r.A)(o,Pe()?Reflect.construct(n,s||[],(0,l.A)(o).constructor):n.apply(o,s)),(0,a.A)(e,"state",{isScrolling:!1,scrollTop:0}),(0,a.A)(e,"_debounceResetIsScrollingId",void 0),(0,a.A)(e,"_invalidateOnUpdateStartIndex",null),(0,a.A)(e,"_invalidateOnUpdateStopIndex",null),(0,a.A)(e,"_positionCache",new ke),(0,a.A)(e,"_startIndex",null),(0,a.A)(e,"_startIndexMemoized",null),(0,a.A)(e,"_stopIndex",null),(0,a.A)(e,"_stopIndexMemoized",null),(0,a.A)(e,"_debounceResetIsScrollingCallback",(function(){e.setState({isScrolling:!1})})),(0,a.A)(e,"_setScrollingContainerRef",(function(t){e._scrollingContainer=t})),(0,a.A)(e,"_onScroll",(function(t){var o=e.props.height,i=t.currentTarget.scrollTop,n=Math.min(Math.max(0,e._getEstimatedTotalHeight()-o),i);i===n&&(e._debounceResetIsScrolling(),e.state.scrollTop!==n&&e.setState({isScrolling:!0,scrollTop:n}))})),e}return(0,s.A)(t,e),(0,n.A)(t,[{key:"clearCellPositions",value:function(){this._positionCache=new ke,this.forceUpdate()}},{key:"invalidateCellSizeAfterRender",value:function(e){var t=e.rowIndex;null===this._invalidateOnUpdateStartIndex?(this._invalidateOnUpdateStartIndex=t,this._invalidateOnUpdateStopIndex=t):(this._invalidateOnUpdateStartIndex=Math.min(this._invalidateOnUpdateStartIndex,t),this._invalidateOnUpdateStopIndex=Math.max(this._invalidateOnUpdateStopIndex,t))}},{key:"recomputeCellPositions",value:function(){var e=this._positionCache.count-1;this._positionCache=new ke,this._populatePositionCache(0,e),this.forceUpdate()}},{key:"componentDidMount",value:function(){this._checkInvalidateOnUpdate(),this._invokeOnScrollCallback(),this._invokeOnCellsRenderedCallback()}},{key:"componentDidUpdate",value:function(e,t){this._checkInvalidateOnUpdate(),this._invokeOnScrollCallback(),this._invokeOnCellsRenderedCallback(),this.props.scrollTop!==e.scrollTop&&this._debounceResetIsScrolling()}},{key:"componentWillUnmount",value:function(){this._debounceResetIsScrollingId&&$(this._debounceResetIsScrollingId)}},{key:"render",value:function(){var e,t=this,o=this.props,i=o.autoHeight,n=o.cellCount,r=o.cellMeasurerCache,l=o.cellRenderer,s=o.className,d=o.height,h=o.id,u=o.keyMapper,f=o.overscanByPixels,p=o.role,_=o.style,g=o.tabIndex,v=o.width,m=o.rowDirection,S=this.state,C=S.isScrolling,y=S.scrollTop,w=[],x=this._getEstimatedTotalHeight(),R=this._positionCache.shortestColumnSize,z=this._positionCache.count,b=0;if(this._positionCache.range(Math.max(0,y-f),d+2*f,(function(o,i,n){void 0===e?(b=o,e=o):(b=Math.min(b,o),e=Math.max(e,o)),w.push(l({index:o,isScrolling:C,key:u(o),parent:t,style:(0,a.A)((0,a.A)((0,a.A)((0,a.A)({height:r.getHeight(o)},"ltr"===m?"left":"right",i),"position","absolute"),"top",n),"width",r.getWidth(o))}))})),R<y+d+f&&z<n)for(var I=Math.min(n-z,Math.ceil((y+d+f-R)/r.defaultHeight*v/r.defaultWidth)),A=z;A<z+I;A++)e=A,w.push(l({index:A,isScrolling:C,key:u(A),parent:this,style:{width:r.getWidth(A)}}));return this._startIndex=b,this._stopIndex=e,c.createElement("div",{ref:this._setScrollingContainerRef,"aria-label":this.props["aria-label"],className:(0,T.default)("ReactVirtualized__Masonry",s),id:h,onScroll:this._onScroll,role:p,style:Oe({boxSizing:"border-box",direction:"ltr",height:i?"auto":d,overflowX:"hidden",overflowY:x<d?"hidden":"auto",position:"relative",width:v,WebkitOverflowScrolling:"touch",willChange:"transform"},_),tabIndex:g},c.createElement("div",{className:"ReactVirtualized__Masonry__innerScrollContainer",style:{width:"100%",height:x,maxWidth:"100%",maxHeight:x,overflow:"hidden",pointerEvents:C?"none":"",position:"relative"}},w))}},{key:"_checkInvalidateOnUpdate",value:function(){if("number"==typeof this._invalidateOnUpdateStartIndex){var e=this._invalidateOnUpdateStartIndex,t=this._invalidateOnUpdateStopIndex;this._invalidateOnUpdateStartIndex=null,this._invalidateOnUpdateStopIndex=null,this._populatePositionCache(e,t),this.forceUpdate()}}},{key:"_debounceResetIsScrolling",value:function(){var e=this.props.scrollingResetTimeInterval;this._debounceResetIsScrollingId&&$(this._debounceResetIsScrollingId),this._debounceResetIsScrollingId=ee(this._debounceResetIsScrollingCallback,e)}},{key:"_getEstimatedTotalHeight",value:function(){var e=this.props,t=e.cellCount,o=e.cellMeasurerCache,i=e.width,n=Math.max(1,Math.floor(i/o.defaultWidth));return this._positionCache.estimateTotalHeight(t,n,o.defaultHeight)}},{key:"_invokeOnScrollCallback",value:function(){var e=this.props,t=e.height,o=e.onScroll,i=this.state.scrollTop;this._onScrollMemoized!==i&&(o({clientHeight:t,scrollHeight:this._getEstimatedTotalHeight(),scrollTop:i}),this._onScrollMemoized=i)}},{key:"_invokeOnCellsRenderedCallback",value:function(){this._startIndexMemoized===this._startIndex&&this._stopIndexMemoized===this._stopIndex||((0,this.props.onCellsRendered)({startIndex:this._startIndex,stopIndex:this._stopIndex}),this._startIndexMemoized=this._startIndex,this._stopIndexMemoized=this._stopIndex)}},{key:"_populatePositionCache",value:function(e,t){for(var o=this.props,i=o.cellMeasurerCache,n=o.cellPositioner,r=e;r<=t;r++){var l=n(r),s=l.left,a=l.top;this._positionCache.setPosition(r,s,a,i.getHeight(r))}}}],[{key:"getDerivedStateFromProps",value:function(e,t){return void 0!==e.scrollTop&&t.scrollTop!==e.scrollTop?{isScrolling:!0,scrollTop:e.scrollTop}:null}}])}(c.PureComponent);function Ge(){}(0,a.A)(Le,"defaultProps",{autoHeight:!1,keyMapper:function(e){return e},onCellsRendered:Ge,onScroll:Ge,overscanByPixels:20,role:"grid",scrollingResetTimeInterval:150,style:{},tabIndex:0,rowDirection:"ltr"}),(0,d.polyfill)(Le);var He=function(){return(0,n.A)((function e(){var t=this,o=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};(0,i.A)(this,e),(0,a.A)(this,"_cellMeasurerCache",void 0),(0,a.A)(this,"_columnIndexOffset",void 0),(0,a.A)(this,"_rowIndexOffset",void 0),(0,a.A)(this,"columnWidth",(function(e){var o=e.index;t._cellMeasurerCache.columnWidth({index:o+t._columnIndexOffset})})),(0,a.A)(this,"rowHeight",(function(e){var o=e.index;t._cellMeasurerCache.rowHeight({index:o+t._rowIndexOffset})}));var n=o.cellMeasurerCache,r=o.columnIndexOffset,l=void 0===r?0:r,s=o.rowIndexOffset,c=void 0===s?0:s;this._cellMeasurerCache=n,this._columnIndexOffset=l,this._rowIndexOffset=c}),[{key:"clear",value:function(e,t){this._cellMeasurerCache.clear(e+this._rowIndexOffset,t+this._columnIndexOffset)}},{key:"clearAll",value:function(){this._cellMeasurerCache.clearAll()}},{key:"defaultHeight",get:function(){return this._cellMeasurerCache.defaultHeight}},{key:"defaultWidth",get:function(){return this._cellMeasurerCache.defaultWidth}},{key:"hasFixedHeight",value:function(){return this._cellMeasurerCache.hasFixedHeight()}},{key:"hasFixedWidth",value:function(){return this._cellMeasurerCache.hasFixedWidth()}},{key:"getHeight",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return this._cellMeasurerCache.getHeight(e+this._rowIndexOffset,t+this._columnIndexOffset)}},{key:"getWidth",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return this._cellMeasurerCache.getWidth(e+this._rowIndexOffset,t+this._columnIndexOffset)}},{key:"has",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return this._cellMeasurerCache.has(e+this._rowIndexOffset,t+this._columnIndexOffset)}},{key:"set",value:function(e,t,o,i){this._cellMeasurerCache.set(e+this._rowIndexOffset,t+this._columnIndexOffset,o,i)}}])}(),We=["rowIndex"],Ee=["columnIndex","rowIndex"],De=["columnIndex"],Fe=["onScroll","onSectionRendered","onScrollbarPresenceChange","scrollLeft","scrollToColumn","scrollTop","scrollToRow"];function je(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,i)}return o}function Ne(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?je(Object(o),!0).forEach((function(t){(0,a.A)(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):je(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}function Be(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(Be=function(){return!!e})()}var Ue=function(e){function t(e,o){var n,s,d,h;(0,i.A)(this,t),s=this,d=t,h=[e,o],d=(0,l.A)(d),n=(0,r.A)(s,Be()?Reflect.construct(d,h||[],(0,l.A)(s).constructor):d.apply(s,h)),(0,a.A)(n,"state",{scrollLeft:0,scrollTop:0,scrollbarSize:0,showHorizontalScrollbar:!1,showVerticalScrollbar:!1}),(0,a.A)(n,"_deferredInvalidateColumnIndex",null),(0,a.A)(n,"_deferredInvalidateRowIndex",null),(0,a.A)(n,"_bottomLeftGridRef",(function(e){n._bottomLeftGrid=e})),(0,a.A)(n,"_bottomRightGridRef",(function(e){n._bottomRightGrid=e})),(0,a.A)(n,"_cellRendererBottomLeftGrid",(function(e){var t=e.rowIndex,o=(0,U.A)(e,We),i=n.props,r=i.cellRenderer,l=i.fixedRowCount;return t===i.rowCount-l?c.createElement("div",{key:o.key,style:Ne(Ne({},o.style),{},{height:20})}):r(Ne(Ne({},o),{},{parent:n,rowIndex:t+l}))})),(0,a.A)(n,"_cellRendererBottomRightGrid",(function(e){var t=e.columnIndex,o=e.rowIndex,i=(0,U.A)(e,Ee),r=n.props,l=r.cellRenderer,s=r.fixedColumnCount,a=r.fixedRowCount;return l(Ne(Ne({},i),{},{columnIndex:t+s,parent:n,rowIndex:o+a}))})),(0,a.A)(n,"_cellRendererTopRightGrid",(function(e){var t=e.columnIndex,o=(0,U.A)(e,De),i=n.props,r=i.cellRenderer,l=i.columnCount,s=i.fixedColumnCount;return t===l-s?c.createElement("div",{key:o.key,style:Ne(Ne({},o.style),{},{width:20})}):r(Ne(Ne({},o),{},{columnIndex:t+s,parent:n}))})),(0,a.A)(n,"_columnWidthRightGrid",(function(e){var t=e.index,o=n.props,i=o.columnCount,r=o.fixedColumnCount,l=o.columnWidth,s=n.state,a=s.scrollbarSize;return s.showHorizontalScrollbar&&t===i-r?a:"function"==typeof l?l({index:t+r}):l})),(0,a.A)(n,"_onScroll",(function(e){var t=e.scrollLeft,o=e.scrollTop;n.setState({scrollLeft:t,scrollTop:o});var i=n.props.onScroll;i&&i(e)})),(0,a.A)(n,"_onScrollbarPresenceChange",(function(e){var t=e.horizontal,o=e.size,i=e.vertical,r=n.state,l=r.showHorizontalScrollbar,s=r.showVerticalScrollbar;if(t!==l||i!==s){n.setState({scrollbarSize:o,showHorizontalScrollbar:t,showVerticalScrollbar:i});var a=n.props.onScrollbarPresenceChange;"function"==typeof a&&a({horizontal:t,size:o,vertical:i})}})),(0,a.A)(n,"_onScrollLeft",(function(e){var t=e.scrollLeft;n._onScroll({scrollLeft:t,scrollTop:n.state.scrollTop})})),(0,a.A)(n,"_onScrollTop",(function(e){var t=e.scrollTop;n._onScroll({scrollTop:t,scrollLeft:n.state.scrollLeft})})),(0,a.A)(n,"_rowHeightBottomGrid",(function(e){var t=e.index,o=n.props,i=o.fixedRowCount,r=o.rowCount,l=o.rowHeight,s=n.state,a=s.scrollbarSize;return s.showVerticalScrollbar&&t===r-i?a:"function"==typeof l?l({index:t+i}):l})),(0,a.A)(n,"_topLeftGridRef",(function(e){n._topLeftGrid=e})),(0,a.A)(n,"_topRightGridRef",(function(e){n._topRightGrid=e}));var u=e.deferredMeasurementCache,f=e.fixedColumnCount,p=e.fixedRowCount;return n._maybeCalculateCachedStyles(!0),u&&(n._deferredMeasurementCacheBottomLeftGrid=p>0?new He({cellMeasurerCache:u,columnIndexOffset:0,rowIndexOffset:p}):u,n._deferredMeasurementCacheBottomRightGrid=f>0||p>0?new He({cellMeasurerCache:u,columnIndexOffset:f,rowIndexOffset:p}):u,n._deferredMeasurementCacheTopRightGrid=f>0?new He({cellMeasurerCache:u,columnIndexOffset:f,rowIndexOffset:0}):u),n}return(0,s.A)(t,e),(0,n.A)(t,[{key:"forceUpdateGrids",value:function(){this._bottomLeftGrid&&this._bottomLeftGrid.forceUpdate(),this._bottomRightGrid&&this._bottomRightGrid.forceUpdate(),this._topLeftGrid&&this._topLeftGrid.forceUpdate(),this._topRightGrid&&this._topRightGrid.forceUpdate()}},{key:"invalidateCellSizeAfterRender",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.columnIndex,o=void 0===t?0:t,i=e.rowIndex,n=void 0===i?0:i;this._deferredInvalidateColumnIndex="number"==typeof this._deferredInvalidateColumnIndex?Math.min(this._deferredInvalidateColumnIndex,o):o,this._deferredInvalidateRowIndex="number"==typeof this._deferredInvalidateRowIndex?Math.min(this._deferredInvalidateRowIndex,n):n}},{key:"measureAllCells",value:function(){this._bottomLeftGrid&&this._bottomLeftGrid.measureAllCells(),this._bottomRightGrid&&this._bottomRightGrid.measureAllCells(),this._topLeftGrid&&this._topLeftGrid.measureAllCells(),this._topRightGrid&&this._topRightGrid.measureAllCells()}},{key:"recomputeGridSize",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.columnIndex,o=void 0===t?0:t,i=e.rowIndex,n=void 0===i?0:i,r=this.props,l=r.fixedColumnCount,s=r.fixedRowCount,a=Math.max(0,o-l),c=Math.max(0,n-s);this._bottomLeftGrid&&this._bottomLeftGrid.recomputeGridSize({columnIndex:o,rowIndex:c}),this._bottomRightGrid&&this._bottomRightGrid.recomputeGridSize({columnIndex:a,rowIndex:c}),this._topLeftGrid&&this._topLeftGrid.recomputeGridSize({columnIndex:o,rowIndex:n}),this._topRightGrid&&this._topRightGrid.recomputeGridSize({columnIndex:a,rowIndex:n}),this._leftGridWidth=null,this._topGridHeight=null,this._maybeCalculateCachedStyles(!0)}},{key:"componentDidMount",value:function(){var e=this.props,t=e.scrollLeft,o=e.scrollTop;if(t>0||o>0){var i={};t>0&&(i.scrollLeft=t),o>0&&(i.scrollTop=o),this.setState(i)}this._handleInvalidatedGridSize()}},{key:"componentDidUpdate",value:function(){this._handleInvalidatedGridSize()}},{key:"render",value:function(){var e=this.props,t=e.onScroll,o=e.onSectionRendered,i=(e.onScrollbarPresenceChange,e.scrollLeft,e.scrollToColumn),n=(e.scrollTop,e.scrollToRow),r=(0,U.A)(e,Fe);if(this._prepareForRender(),0===this.props.width||0===this.props.height)return null;var l=this.state,s=l.scrollLeft,a=l.scrollTop;return c.createElement("div",{style:this._containerOuterStyle},c.createElement("div",{style:this._containerTopStyle},this._renderTopLeftGrid(r),this._renderTopRightGrid(Ne(Ne({},r),{},{onScroll:t,scrollLeft:s}))),c.createElement("div",{style:this._containerBottomStyle},this._renderBottomLeftGrid(Ne(Ne({},r),{},{onScroll:t,scrollTop:a})),this._renderBottomRightGrid(Ne(Ne({},r),{},{onScroll:t,onSectionRendered:o,scrollLeft:s,scrollToColumn:i,scrollToRow:n,scrollTop:a}))))}},{key:"_getBottomGridHeight",value:function(e){return e.height-this._getTopGridHeight(e)}},{key:"_getLeftGridWidth",value:function(e){var t=e.fixedColumnCount,o=e.columnWidth;if(null==this._leftGridWidth)if("function"==typeof o){for(var i=0,n=0;n<t;n++)i+=o({index:n});this._leftGridWidth=i}else this._leftGridWidth=o*t;return this._leftGridWidth}},{key:"_getRightGridWidth",value:function(e){return e.width-this._getLeftGridWidth(e)}},{key:"_getTopGridHeight",value:function(e){var t=e.fixedRowCount,o=e.rowHeight;if(null==this._topGridHeight)if("function"==typeof o){for(var i=0,n=0;n<t;n++)i+=o({index:n});this._topGridHeight=i}else this._topGridHeight=o*t;return this._topGridHeight}},{key:"_handleInvalidatedGridSize",value:function(){if("number"==typeof this._deferredInvalidateColumnIndex){var e=this._deferredInvalidateColumnIndex,t=this._deferredInvalidateRowIndex;this._deferredInvalidateColumnIndex=null,this._deferredInvalidateRowIndex=null,this.recomputeGridSize({columnIndex:e,rowIndex:t}),this.forceUpdate()}}},{key:"_maybeCalculateCachedStyles",value:function(e){var t=this.props,o=t.columnWidth,i=t.enableFixedColumnScroll,n=t.enableFixedRowScroll,r=t.height,l=t.fixedColumnCount,s=t.fixedRowCount,a=t.rowHeight,c=t.style,d=t.styleBottomLeftGrid,h=t.styleBottomRightGrid,u=t.styleTopLeftGrid,f=t.styleTopRightGrid,p=t.width,_=e||r!==this._lastRenderedHeight||p!==this._lastRenderedWidth,g=e||o!==this._lastRenderedColumnWidth||l!==this._lastRenderedFixedColumnCount,v=e||s!==this._lastRenderedFixedRowCount||a!==this._lastRenderedRowHeight;(e||_||c!==this._lastRenderedStyle)&&(this._containerOuterStyle=Ne({height:r,overflow:"visible",width:p},c)),(e||_||v)&&(this._containerTopStyle={height:this._getTopGridHeight(this.props),position:"relative",width:p},this._containerBottomStyle={height:r-this._getTopGridHeight(this.props),overflow:"visible",position:"relative",width:p}),(e||d!==this._lastRenderedStyleBottomLeftGrid)&&(this._bottomLeftGridStyle=Ne({left:0,overflowX:"hidden",overflowY:i?"auto":"hidden",position:"absolute"},d)),(e||g||h!==this._lastRenderedStyleBottomRightGrid)&&(this._bottomRightGridStyle=Ne({left:this._getLeftGridWidth(this.props),position:"absolute"},h)),(e||u!==this._lastRenderedStyleTopLeftGrid)&&(this._topLeftGridStyle=Ne({left:0,overflowX:"hidden",overflowY:"hidden",position:"absolute",top:0},u)),(e||g||f!==this._lastRenderedStyleTopRightGrid)&&(this._topRightGridStyle=Ne({left:this._getLeftGridWidth(this.props),overflowX:n?"auto":"hidden",overflowY:"hidden",position:"absolute",top:0},f)),this._lastRenderedColumnWidth=o,this._lastRenderedFixedColumnCount=l,this._lastRenderedFixedRowCount=s,this._lastRenderedHeight=r,this._lastRenderedRowHeight=a,this._lastRenderedStyle=c,this._lastRenderedStyleBottomLeftGrid=d,this._lastRenderedStyleBottomRightGrid=h,this._lastRenderedStyleTopLeftGrid=u,this._lastRenderedStyleTopRightGrid=f,this._lastRenderedWidth=p}},{key:"_prepareForRender",value:function(){this._lastRenderedColumnWidth===this.props.columnWidth&&this._lastRenderedFixedColumnCount===this.props.fixedColumnCount||(this._leftGridWidth=null),this._lastRenderedFixedRowCount===this.props.fixedRowCount&&this._lastRenderedRowHeight===this.props.rowHeight||(this._topGridHeight=null),this._maybeCalculateCachedStyles(),this._lastRenderedColumnWidth=this.props.columnWidth,this._lastRenderedFixedColumnCount=this.props.fixedColumnCount,this._lastRenderedFixedRowCount=this.props.fixedRowCount,this._lastRenderedRowHeight=this.props.rowHeight}},{key:"_renderBottomLeftGrid",value:function(e){var t=e.enableFixedColumnScroll,o=e.fixedColumnCount,i=e.fixedRowCount,n=e.rowCount,r=e.hideBottomLeftGridScrollbar,l=this.state.showVerticalScrollbar;if(!o)return null;var s=l?1:0,a=this._getBottomGridHeight(e),d=this._getLeftGridWidth(e),h=this.state.showVerticalScrollbar?this.state.scrollbarSize:0,u=r?d+h:d,f=c.createElement(le,(0,z.A)({},e,{cellRenderer:this._cellRendererBottomLeftGrid,className:this.props.classNameBottomLeftGrid,columnCount:o,deferredMeasurementCache:this._deferredMeasurementCacheBottomLeftGrid,height:a,onScroll:t?this._onScrollTop:void 0,ref:this._bottomLeftGridRef,rowCount:Math.max(0,n-i)+s,rowHeight:this._rowHeightBottomGrid,style:this._bottomLeftGridStyle,tabIndex:null,width:u}));return r?c.createElement("div",{className:"BottomLeftGrid_ScrollWrapper",style:Ne(Ne({},this._bottomLeftGridStyle),{},{height:a,width:d,overflowY:"hidden"})},f):f}},{key:"_renderBottomRightGrid",value:function(e){var t=e.columnCount,o=e.fixedColumnCount,i=e.fixedRowCount,n=e.rowCount,r=e.scrollToColumn,l=e.scrollToRow;return c.createElement(le,(0,z.A)({},e,{cellRenderer:this._cellRendererBottomRightGrid,className:this.props.classNameBottomRightGrid,columnCount:Math.max(0,t-o),columnWidth:this._columnWidthRightGrid,deferredMeasurementCache:this._deferredMeasurementCacheBottomRightGrid,height:this._getBottomGridHeight(e),onScroll:this._onScroll,onScrollbarPresenceChange:this._onScrollbarPresenceChange,ref:this._bottomRightGridRef,rowCount:Math.max(0,n-i),rowHeight:this._rowHeightBottomGrid,scrollToColumn:r-o,scrollToRow:l-i,style:this._bottomRightGridStyle,width:this._getRightGridWidth(e)}))}},{key:"_renderTopLeftGrid",value:function(e){var t=e.fixedColumnCount,o=e.fixedRowCount;return t&&o?c.createElement(le,(0,z.A)({},e,{className:this.props.classNameTopLeftGrid,columnCount:t,height:this._getTopGridHeight(e),ref:this._topLeftGridRef,rowCount:o,style:this._topLeftGridStyle,tabIndex:null,width:this._getLeftGridWidth(e)})):null}},{key:"_renderTopRightGrid",value:function(e){var t=e.columnCount,o=e.enableFixedRowScroll,i=e.fixedColumnCount,n=e.fixedRowCount,r=e.scrollLeft,l=e.hideTopRightGridScrollbar,s=this.state,a=s.showHorizontalScrollbar,d=s.scrollbarSize;if(!n)return null;var h=a?1:0,u=this._getTopGridHeight(e),f=this._getRightGridWidth(e),p=a?d:0,_=u,g=this._topRightGridStyle;l&&(_=u+p,g=Ne(Ne({},this._topRightGridStyle),{},{left:0}));var v=c.createElement(le,(0,z.A)({},e,{cellRenderer:this._cellRendererTopRightGrid,className:this.props.classNameTopRightGrid,columnCount:Math.max(0,t-i)+h,columnWidth:this._columnWidthRightGrid,deferredMeasurementCache:this._deferredMeasurementCacheTopRightGrid,height:_,onScroll:o?this._onScrollLeft:void 0,ref:this._topRightGridRef,rowCount:n,scrollLeft:r,style:g,tabIndex:null,width:f}));return l?c.createElement("div",{className:"TopRightGrid_ScrollWrapper",style:Ne(Ne({},this._topRightGridStyle),{},{height:u,width:f,overflowX:"hidden"})},v):v}}],[{key:"getDerivedStateFromProps",value:function(e,t){return e.scrollLeft!==t.scrollLeft||e.scrollTop!==t.scrollTop?{scrollLeft:null!=e.scrollLeft&&e.scrollLeft>=0?e.scrollLeft:t.scrollLeft,scrollTop:null!=e.scrollTop&&e.scrollTop>=0?e.scrollTop:t.scrollTop}:null}}])}(c.PureComponent);function Ve(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(Ve=function(){return!!e})()}(0,a.A)(Ue,"defaultProps",{classNameBottomLeftGrid:"",classNameBottomRightGrid:"",classNameTopLeftGrid:"",classNameTopRightGrid:"",enableFixedColumnScroll:!1,enableFixedRowScroll:!1,fixedColumnCount:0,fixedRowCount:0,scrollToColumn:-1,scrollToRow:-1,style:{},styleBottomLeftGrid:{},styleBottomRightGrid:{},styleTopLeftGrid:{},styleTopRightGrid:{},hideTopRightGridScrollbar:!1,hideBottomLeftGridScrollbar:!1}),Ue.propTypes={},(0,d.polyfill)(Ue),(function(e){function t(e,o){var n,s,a,c;return(0,i.A)(this,t),s=this,a=t,c=[e,o],a=(0,l.A)(a),(n=(0,r.A)(s,Ve()?Reflect.construct(a,c||[],(0,l.A)(s).constructor):a.apply(s,c))).state={clientHeight:0,clientWidth:0,scrollHeight:0,scrollLeft:0,scrollTop:0,scrollWidth:0},n._onScroll=n._onScroll.bind(n),n}return(0,s.A)(t,e),(0,n.A)(t,[{key:"render",value:function(){var e=this.props.children,t=this.state,o=t.clientHeight,i=t.clientWidth,n=t.scrollHeight,r=t.scrollLeft,l=t.scrollTop,s=t.scrollWidth;return e({clientHeight:o,clientWidth:i,onScroll:this._onScroll,scrollHeight:n,scrollLeft:r,scrollTop:l,scrollWidth:s})}},{key:"_onScroll",value:function(e){var t=e.clientHeight,o=e.clientWidth,i=e.scrollHeight,n=e.scrollLeft,r=e.scrollTop,l=e.scrollWidth;this.setState({clientHeight:t,clientWidth:o,scrollHeight:i,scrollLeft:n,scrollTop:r,scrollWidth:l})}}])}(c.PureComponent)).propTypes={};const qe="ASC",Ke="DESC";function Xe(e){var t=e.sortDirection,o=(0,T.default)("ReactVirtualized__Table__sortableHeaderIcon",{"ReactVirtualized__Table__sortableHeaderIcon--ASC":t===qe,"ReactVirtualized__Table__sortableHeaderIcon--DESC":t===Ke});return c.createElement("svg",{className:o,width:18,height:18,viewBox:"0 0 24 24"},t===qe?c.createElement("path",{d:"M7 14l5-5 5 5z"}):c.createElement("path",{d:"M7 10l5 5 5-5z"}),c.createElement("path",{d:"M0 0h24v24H0z",fill:"none"}))}function Ye(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(Ye=function(){return!!e})()}Xe.propTypes={};var Je=function(e){function t(){return(0,i.A)(this,t),e=this,o=t,n=arguments,o=(0,l.A)(o),(0,r.A)(e,Ye()?Reflect.construct(o,n||[],(0,l.A)(e).constructor):o.apply(e,n));var e,o,n}return(0,s.A)(t,e),(0,n.A)(t)}(c.Component);function Ze(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,i)}return o}function Qe(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?Ze(Object(o),!0).forEach((function(t){(0,a.A)(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):Ze(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}function $e(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return($e=function(){return!!e})()}(0,a.A)(Je,"defaultProps",{cellDataGetter:function(e){var t=e.dataKey,o=e.rowData;return"function"==typeof o.get?o.get(t):o[t]},cellRenderer:function(e){var t=e.cellData;return null==t?"":String(t)},defaultSortDirection:qe,flexGrow:0,flexShrink:1,headerRenderer:function(e){var t=e.dataKey,o=e.label,i=e.sortBy,n=e.sortDirection,r=i===t,l=[c.createElement("span",{className:"ReactVirtualized__Table__headerTruncatedText",key:"label",title:"string"==typeof o?o:null},o)];return r&&l.push(c.createElement(Xe,{key:"SortIndicator",sortDirection:n})),l},style:{}}),Je.propTypes={};var et=function(e){function t(e){var o,n,s,a;return(0,i.A)(this,t),n=this,s=t,a=[e],s=(0,l.A)(s),(o=(0,r.A)(n,$e()?Reflect.construct(s,a||[],(0,l.A)(n).constructor):s.apply(n,a))).state={scrollbarWidth:0},o._createColumn=o._createColumn.bind(o),o._createRow=o._createRow.bind(o),o._onScroll=o._onScroll.bind(o),o._onSectionRendered=o._onSectionRendered.bind(o),o._setRef=o._setRef.bind(o),o._setGridElementRef=o._setGridElementRef.bind(o),o}return(0,s.A)(t,e),(0,n.A)(t,[{key:"forceUpdateGrid",value:function(){this.Grid&&this.Grid.forceUpdate()}},{key:"getOffsetForRow",value:function(e){var t=e.alignment,o=e.index;return this.Grid?this.Grid.getOffsetForCell({alignment:t,rowIndex:o}).scrollTop:0}},{key:"invalidateCellSizeAfterRender",value:function(e){var t=e.columnIndex,o=e.rowIndex;this.Grid&&this.Grid.invalidateCellSizeAfterRender({rowIndex:o,columnIndex:t})}},{key:"measureAllRows",value:function(){this.Grid&&this.Grid.measureAllCells()}},{key:"recomputeGridSize",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.columnIndex,o=void 0===t?0:t,i=e.rowIndex,n=void 0===i?0:i;this.Grid&&this.Grid.recomputeGridSize({rowIndex:n,columnIndex:o})}},{key:"recomputeRowHeights",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;this.Grid&&this.Grid.recomputeGridSize({rowIndex:e})}},{key:"scrollToPosition",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;this.Grid&&this.Grid.scrollToPosition({scrollTop:e})}},{key:"scrollToRow",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;this.Grid&&this.Grid.scrollToCell({columnIndex:0,rowIndex:e})}},{key:"getScrollbarWidth",value:function(){if(this.GridElement){var e=this.GridElement,t=e.clientWidth||0;return(e.offsetWidth||0)-t}return 0}},{key:"componentDidMount",value:function(){this._setScrollbarWidth()}},{key:"componentDidUpdate",value:function(){this._setScrollbarWidth()}},{key:"render",value:function(){var e=this,t=this.props,o=t.children,i=t.className,n=t.disableHeader,r=t.gridClassName,l=t.gridStyle,s=t.headerHeight,a=t.headerRowRenderer,d=t.height,h=t.id,u=t.noRowsRenderer,f=t.rowClassName,p=t.rowStyle,_=t.scrollToIndex,g=t.style,v=t.width,m=this.state.scrollbarWidth,S=n?d:d-s,C="function"==typeof f?f({index:-1}):f,y="function"==typeof p?p({index:-1}):p;return this._cachedColumnStyles=[],c.Children.toArray(o).forEach((function(t,o){var i=e._getFlexStyleForColumn(t,t.props.style||Je.defaultProps.style);e._cachedColumnStyles[o]=Qe({overflow:"hidden"},i)})),c.createElement("div",{"aria-label":this.props["aria-label"],"aria-labelledby":this.props["aria-labelledby"],"aria-colcount":c.Children.toArray(o).length,"aria-rowcount":this.props.rowCount,className:(0,T.default)("ReactVirtualized__Table",i),id:h,role:"grid",style:g},!n&&a({className:(0,T.default)("ReactVirtualized__Table__headerRow",C),columns:this._getHeaderColumns(),style:Qe({height:s,overflow:"hidden",paddingRight:m,width:v},y)}),c.createElement(le,(0,z.A)({},this.props,{elementRef:this._setGridElementRef,"aria-readonly":null,autoContainerWidth:!0,className:(0,T.default)("ReactVirtualized__Table__Grid",r),cellRenderer:this._createRow,columnWidth:v,columnCount:1,height:S,id:void 0,noContentRenderer:u,onScroll:this._onScroll,onSectionRendered:this._onSectionRendered,ref:this._setRef,role:"rowgroup",scrollbarWidth:m,scrollToRow:_,style:Qe(Qe({},l),{},{overflowX:"hidden"})})))}},{key:"_createColumn",value:function(e){var t=e.column,o=e.columnIndex,i=e.isScrolling,n=e.parent,r=e.rowData,l=e.rowIndex,s=this.props.onColumnClick,a=t.props,d=a.cellDataGetter,h=a.cellRenderer,u=a.className,f=a.columnData,p=a.dataKey,_=a.id,g=h({cellData:d({columnData:f,dataKey:p,rowData:r}),columnData:f,columnIndex:o,dataKey:p,isScrolling:i,parent:n,rowData:r,rowIndex:l}),v=this._cachedColumnStyles[o],m="string"==typeof g?g:null;return c.createElement("div",{"aria-colindex":o+1,"aria-describedby":_,className:(0,T.default)("ReactVirtualized__Table__rowColumn",u),key:"Row"+l+"-Col"+o,onClick:function(e){s&&s({columnData:f,dataKey:p,event:e})},role:"gridcell",style:v,title:m},g)}},{key:"_createHeader",value:function(e){var t,o,i,n,r,l=e.column,s=e.index,a=this.props,d=a.headerClassName,h=a.headerStyle,u=a.onHeaderClick,f=a.sort,p=a.sortBy,_=a.sortDirection,g=l.props,v=g.columnData,m=g.dataKey,S=g.defaultSortDirection,C=g.disableSort,y=g.headerRenderer,w=g.id,x=g.label,R=!C&&f,z=(0,T.default)("ReactVirtualized__Table__headerColumn",d,l.props.headerClassName,{ReactVirtualized__Table__sortableHeaderColumn:R}),b=this._getFlexStyleForColumn(l,Qe(Qe({},h),l.props.headerStyle)),I=y({columnData:v,dataKey:m,disableSort:C,label:x,sortBy:p,sortDirection:_});if(R||u){var A=p!==m?S:_===Ke?qe:Ke,k=function(e){R&&f({defaultSortDirection:S,event:e,sortBy:m,sortDirection:A}),u&&u({columnData:v,dataKey:m,event:e})};r=l.props["aria-label"]||x||m,n="none",i=0,t=k,o=function(e){"Enter"!==e.key&&" "!==e.key||k(e)}}return p===m&&(n=_===qe?"ascending":"descending"),c.createElement("div",{"aria-label":r,"aria-sort":n,className:z,id:w,key:"Header-Col"+s,onClick:t,onKeyDown:o,role:"columnheader",style:b,tabIndex:i},I)}},{key:"_createRow",value:function(e){var t=this,o=e.rowIndex,i=e.isScrolling,n=e.key,r=e.parent,l=e.style,s=this.props,a=s.children,d=s.onRowClick,h=s.onRowDoubleClick,u=s.onRowRightClick,f=s.onRowMouseOver,p=s.onRowMouseOut,_=s.rowClassName,g=s.rowGetter,v=s.rowRenderer,m=s.rowStyle,S=this.state.scrollbarWidth,C="function"==typeof _?_({index:o}):_,y="function"==typeof m?m({index:o}):m,w=g({index:o}),x=c.Children.toArray(a).map((function(e,n){return t._createColumn({column:e,columnIndex:n,isScrolling:i,parent:r,rowData:w,rowIndex:o,scrollbarWidth:S})})),R=(0,T.default)("ReactVirtualized__Table__row",C),z=Qe(Qe({},l),{},{height:this._getRowHeight(o),overflow:"hidden",paddingRight:S},y);return v({className:R,columns:x,index:o,isScrolling:i,key:n,onRowClick:d,onRowDoubleClick:h,onRowRightClick:u,onRowMouseOver:f,onRowMouseOut:p,rowData:w,style:z})}},{key:"_getFlexStyleForColumn",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o="".concat(e.props.flexGrow," ").concat(e.props.flexShrink," ").concat(e.props.width,"px"),i=Qe(Qe({},t),{},{flex:o,msFlex:o,WebkitFlex:o});return e.props.maxWidth&&(i.maxWidth=e.props.maxWidth),e.props.minWidth&&(i.minWidth=e.props.minWidth),i}},{key:"_getHeaderColumns",value:function(){var e=this,t=this.props,o=t.children;return(t.disableHeader?[]:c.Children.toArray(o)).map((function(t,o){return e._createHeader({column:t,index:o})}))}},{key:"_getRowHeight",value:function(e){var t=this.props.rowHeight;return"function"==typeof t?t({index:e}):t}},{key:"_onScroll",value:function(e){var t=e.clientHeight,o=e.scrollHeight,i=e.scrollTop;(0,this.props.onScroll)({clientHeight:t,scrollHeight:o,scrollTop:i})}},{key:"_onSectionRendered",value:function(e){var t=e.rowOverscanStartIndex,o=e.rowOverscanStopIndex,i=e.rowStartIndex,n=e.rowStopIndex;(0,this.props.onRowsRendered)({overscanStartIndex:t,overscanStopIndex:o,startIndex:i,stopIndex:n})}},{key:"_setRef",value:function(e){this.Grid=e}},{key:"_setGridElementRef",value:function(e){this.GridElement=e}},{key:"_setScrollbarWidth",value:function(){var e=this.getScrollbarWidth();this.setState({scrollbarWidth:e})}}])}(c.PureComponent);(0,a.A)(et,"defaultProps",{disableHeader:!1,estimatedRowSize:30,headerHeight:0,headerStyle:{},noRowsRenderer:function(){return null},onRowsRendered:function(){return null},onScroll:function(){return null},overscanIndicesGetter:se,overscanRowCount:10,rowRenderer:function(e){var t=e.className,o=e.columns,i=e.index,n=e.key,r=e.onRowClick,l=e.onRowDoubleClick,s=e.onRowMouseOut,a=e.onRowMouseOver,d=e.onRowRightClick,h=e.rowData,u=e.style,f={"aria-rowindex":i+1};return(r||l||s||a||d)&&(f["aria-label"]="row",f.tabIndex=0,r&&(f.onClick=function(e){return r({event:e,index:i,rowData:h})}),l&&(f.onDoubleClick=function(e){return l({event:e,index:i,rowData:h})}),s&&(f.onMouseOut=function(e){return s({event:e,index:i,rowData:h})}),a&&(f.onMouseOver=function(e){return a({event:e,index:i,rowData:h})}),d&&(f.onContextMenu=function(e){return d({event:e,index:i,rowData:h})})),c.createElement("div",(0,z.A)({},f,{className:t,key:n,role:"row",style:u}),o)},headerRowRenderer:function(e){var t=e.className,o=e.columns,i=e.style;return c.createElement("div",{className:t,role:"row",style:i},o)},rowStyle:{},scrollToAlignment:"auto",scrollToIndex:-1,style:{}}),et.propTypes={};var tt=[],ot=null,it=null;function nt(){it&&(it=null,document.body&&null!=ot&&(document.body.style.pointerEvents=ot),ot=null)}function rt(){nt(),tt.forEach((function(e){return e.__resetIsScrolling()}))}function lt(e){e.currentTarget===window&&null==ot&&document.body&&(ot=document.body.style.pointerEvents,document.body.style.pointerEvents="none"),function(){it&&$(it);var e=0;tt.forEach((function(t){e=Math.max(e,t.props.scrollingResetTimeInterval)})),it=ee(rt,e)}(),tt.forEach((function(t){t.props.scrollElement===e.currentTarget&&t.__handleWindowScrollEvent()}))}function st(e,t){tt.some((function(e){return e.props.scrollElement===t}))||t.addEventListener("scroll",lt),tt.push(e)}function at(e,t){(tt=tt.filter((function(t){return t!==e}))).length||(t.removeEventListener("scroll",lt),it&&($(it),nt()))}var ct=function(e){return e===window},dt=function(e){return e.getBoundingClientRect()};function ht(e,t){if(e){if(ct(e)){var o=window,i=o.innerHeight,n=o.innerWidth;return{height:"number"==typeof i?i:0,width:"number"==typeof n?n:0}}return dt(e)}return{height:t.serverHeight,width:t.serverWidth}}function ut(e){return ct(e)&&document.documentElement?{top:"scrollY"in window?window.scrollY:document.documentElement.scrollTop,left:"scrollX"in window?window.scrollX:document.documentElement.scrollLeft}:{top:e.scrollTop,left:e.scrollLeft}}var ft=o(96763);function pt(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,i)}return o}function _t(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?pt(Object(o),!0).forEach((function(t){(0,a.A)(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):pt(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}function gt(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(gt=function(){return!!e})()}var vt=function(){return"undefined"!=typeof window?window:void 0},mt=function(e){function t(){var e,o,n,s;(0,i.A)(this,t);for(var d=arguments.length,h=new Array(d),u=0;u<d;u++)h[u]=arguments[u];return o=this,n=t,s=[].concat(h),n=(0,l.A)(n),e=(0,r.A)(o,gt()?Reflect.construct(n,s||[],(0,l.A)(o).constructor):n.apply(o,s)),(0,a.A)(e,"_window",vt()),(0,a.A)(e,"_isMounted",!1),(0,a.A)(e,"_positionFromTop",0),(0,a.A)(e,"_positionFromLeft",0),(0,a.A)(e,"_detectElementResize",void 0),(0,a.A)(e,"_child",void 0),(0,a.A)(e,"_windowScrollerRef",c.createRef()),(0,a.A)(e,"state",_t(_t({},ht(e.props.scrollElement,e.props)),{},{isScrolling:!1,scrollLeft:0,scrollTop:0})),(0,a.A)(e,"_registerChild",(function(t){!t||t instanceof Element||ft.warn("WindowScroller registerChild expects to be passed Element or null"),e._child=t,e.updatePosition()})),(0,a.A)(e,"_onChildScroll",(function(t){var o=t.scrollTop;if(e.state.scrollTop!==o){var i=e.props.scrollElement;i&&("function"==typeof i.scrollTo?i.scrollTo(0,o+e._positionFromTop):i.scrollTop=o+e._positionFromTop)}})),(0,a.A)(e,"_registerResizeListener",(function(t){t===window?window.addEventListener("resize",e._onResize,!1):e._detectElementResize.addResizeListener(t,e._onResize)})),(0,a.A)(e,"_unregisterResizeListener",(function(t){t===window?window.removeEventListener("resize",e._onResize,!1):t&&e._detectElementResize.removeResizeListener(t,e._onResize)})),(0,a.A)(e,"_onResize",(function(){e.updatePosition()})),(0,a.A)(e,"__handleWindowScrollEvent",(function(){if(e._isMounted){var t=e.props.onScroll,o=e.props.scrollElement;if(o){var i=ut(o),n=Math.max(0,i.left-e._positionFromLeft),r=Math.max(0,i.top-e._positionFromTop);e.setState({isScrolling:!0,scrollLeft:n,scrollTop:r}),t({scrollLeft:n,scrollTop:r})}}})),(0,a.A)(e,"__resetIsScrolling",(function(){e.setState({isScrolling:!1})})),e}return(0,s.A)(t,e),(0,n.A)(t,[{key:"updatePosition",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props.scrollElement,t=this.props.onResize,o=this.state,i=o.height,n=o.width,r=this._child||this._windowScrollerRef.current;if(r instanceof Element&&e){var l=function(e,t){if(ct(t)&&document.documentElement){var o=document.documentElement,i=dt(e),n=dt(o);return{top:i.top-n.top,left:i.left-n.left}}var r=ut(t),l=dt(e),s=dt(t);return{top:l.top+r.top-s.top,left:l.left+r.left-s.left}}(r,e);this._positionFromTop=l.top,this._positionFromLeft=l.left}var s=ht(e,this.props);i===s.height&&n===s.width||(this.setState({height:s.height,width:s.width}),t({height:s.height,width:s.width})),!0===this.props.updateScrollTopOnUpdatePosition&&(this.__handleWindowScrollEvent(),this.__resetIsScrolling())}},{key:"componentDidMount",value:function(){var e=this.props.scrollElement;this._detectElementResize=_(),this.updatePosition(e),e&&(st(this,e),this._registerResizeListener(e)),this._isMounted=!0}},{key:"componentDidUpdate",value:function(e,t){var o=this.props.scrollElement,i=e.scrollElement;i!==o&&null!=i&&null!=o&&(this.updatePosition(o),at(this,i),st(this,o),this._unregisterResizeListener(i),this._registerResizeListener(o))}},{key:"componentWillUnmount",value:function(){var e=this.props.scrollElement;e&&(at(this,e),this._unregisterResizeListener(e)),this._isMounted=!1}},{key:"render",value:function(){var e=this.props.children,t=this.state,o=t.isScrolling,i=t.scrollTop,n=t.scrollLeft,r=t.height,l=t.width;return c.createElement("div",{ref:this._windowScrollerRef},e({onChildScroll:this._onChildScroll,registerChild:this._registerChild,height:r,isScrolling:o,scrollLeft:n,scrollTop:i,width:l}))}}])}(c.PureComponent);(0,a.A)(mt,"defaultProps",{onResize:function(){},onScroll:function(){},scrollingResetTimeInterval:150,scrollElement:vt(),serverHeight:0,serverWidth:0})}}]);
//# sourceMappingURL=6697.chunk.js.map