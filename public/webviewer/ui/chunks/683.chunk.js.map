{"version": 3, "file": "chunks/683.chunk.js", "mappings": ";6uBAkCA,MChCA,EDKA,SAAgDA,GAC9C,IAAQC,EAAyBD,EAAzBC,UAAWC,EAAcF,EAAdE,UAEbC,GAAWC,EAAAA,EAAAA,MACXC,GAAsBC,EAAAA,EAAAA,KAAY,SAACC,GAAK,OAAKC,EAAAA,EAAUC,gCAAgCF,EAAM,IAQnG,OACEG,EAAAA,cAAA,OACET,UAAWA,EACXU,QATyC,YACK,IAA5CN,EAAoBO,QAAQV,IAC9BC,EAASU,EAAAA,EAAQC,0BAA0B,CAACZ,IAEhD,GAOIQ,EAAAA,cAACK,EAAAA,EAAmB,CAClBC,YAAaC,EAAAA,EAAaC,iCAC1BC,QAASF,EAAAA,EAAaG,0BACtBC,IAAI,iBACJC,MAAM,sCAId,4hCElBA,IAAMC,EAAY,CAChBC,MAAOC,IAAAA,OAAiBC,YAGpBC,EAAkB,mBAElBC,EAAoB,SAAHC,GAAkB,IAAZL,EAAKK,EAALL,MACnBM,GAAMC,EAAAA,EAAAA,KAAND,EACDE,EAAkGC,GAA7E3B,EAAAA,EAAAA,KAAY,SAACC,GAAK,MAAK,CAACC,EAAAA,EAAUwB,kBAAkBzB,EAAOoB,GAAiB,IAAC,GAAjF,GACjBO,EAAmID,GAA3G3B,EAAAA,EAAAA,KAAY,SAACC,GAAK,MAAK,CAACC,EAAAA,EAAUwB,kBAAkBzB,EAAOU,EAAAA,EAAaC,kCAAkC,IAAC,GAA/G,GAIzBiB,EAAAF,GAHgE3B,EAAAA,EAAAA,KAAY,SAACC,GAAK,MAAK,CACvFC,EAAAA,EAAU4B,qCAAqC7B,GAC/CC,EAAAA,EAAUC,gCAAgCF,GAC3C,IAAC,GAHK8B,EAAsCF,EAAA,GAAEG,EAAeH,EAAA,GAIxDhC,GAAWC,EAAAA,EAAAA,MAUDmC,EAAAN,GAJZ3B,EAAAA,EAAAA,KAAY,SAACC,GAAK,MAAK,CACzBC,EAAAA,EAAUgC,eAAejC,GACzBC,EAAAA,EAAUiC,6BAA6BlC,GACvCC,EAAAA,EAAUkC,gBAAgBnC,GAC3B,GAAEoC,EAAAA,IAAa,GAPdC,EAAWL,EAAA,GACXM,EAA6BN,EAAA,GAC7BO,EAAYP,EAAA,GAOVQ,EAAcT,EAAgBU,OAAS,EAAIV,EAAgBW,KAAI,SAACC,GAAC,OAAKA,EAAI,CAAC,IAAI,CAAC1B,EAAQ,GAEtF2B,EAA8BJ,EAAYK,SAASR,GACnDS,EAAiBP,EAAaO,eAE/BF,IACHJ,EAAc,CAACH,IAGjB,IAAMU,EAAWC,EAAAA,EAAKC,cAChBC,EAAeH,aAAQ,EAARA,EAAUI,KACzBC,EAAQF,IAAiBG,EAAAA,EAAYC,IACrCC,EAAWL,IAAiBG,EAAAA,EAAYG,QAAUN,IAAiBG,EAAAA,EAAYI,cAE/EC,EAAc,CAClB,qBAAwBvD,EAAAA,cAACwD,EAAAA,EAAM,CAC7BjE,UAAU,gBACVoB,IAAI,6DACJV,QAAS,WAAF,OAAQwD,EAAAA,EAAAA,IAAgBpB,EAAY,EAC3CzB,MAAM,4CACNN,YAAY,yBAEd,4BAA+BN,EAAAA,cAACwD,EAAAA,EAAM,CACpC7C,IAAI,oEACJV,QAAS,WAAF,OAAQyD,EAAAA,EAAAA,IAAuBrB,EAAY,EAClDzB,MAAM,mDACNN,YAAY,gCAEd,YAAeN,EAAAA,cAACwD,EAAAA,EAAM,CACpBjE,UAAU,gBACVoB,IAAI,mBACJV,QAAS,WAAF,OAAQ0D,EAAAA,EAAAA,IAAYtB,EAAa5C,EAAUkC,EAAuC,EACzFf,MAAM,+BACNN,YAAY,cACZsD,oBAAmB,GAAAC,OAAKzC,EAAE,iBAAgB,KAAAyC,OAAIzC,EAAE,gBAAe,KAAAyC,OAAIzC,EAAE,qBAGrE0C,GAAe,EACbC,EAAkB,GAClBC,EAAU7B,EAA8BI,KAAI,SAAC0B,GACjD,IAAQ3D,EAAgB2D,EAAhB3D,YACF4D,EAAM5D,EACR6D,EAAYZ,EAAYjD,GAC5B,GAAIyD,EAAgB7D,QAAQI,IAAgB,EAC1C,OAAO,KAWT,GATAyD,EAAgBK,KAAK9D,IAShB6D,EAAW,CACdL,GAAe,EACf,IAAQnD,EAAwBsD,EAAxBtD,IAAKV,EAAmBgE,EAAnBhE,QAASW,EAAUqD,EAAVrD,MACtBuD,EAAYnE,EAAAA,cAACwD,EAAAA,EAAM,CACjBjE,UAAS,GAAAsE,OAAKvD,EAAW,WACzBK,IAAKA,EACLV,QAAS,WAAF,OAAQA,EAAQiC,EAAY,EACnCtB,MAAOA,EACPN,YAAaA,GAEjB,CAEA,OAAO6D,EACHnE,EAAAA,aAAmBmE,EAAW,CAC9BD,IAAAA,IAEA,IACN,IAEA,OAAI5C,EACK,KACH2B,GAASG,GAAYR,SAAAA,EAAUyB,4BAEjCrE,EAAAA,cAAA,OAAKT,UAAU,4BAA4B,eAAc0B,EACvDqD,MAAO,CAAEC,QAAS,SAElBvE,EAAAA,cAACwD,EAAAA,EAAM,CACL7C,IAAI,oEACJV,QAAS,WAAF,OAAQyD,EAAAA,EAAAA,IAAuBrB,EAAY,EAClDzB,MAAM,mDACNN,YAAY,gCAEdN,EAAAA,cAACwD,EAAAA,EAAM,CACL7C,IAAI,6DACJV,QAAS,WAAF,OAAQwD,EAAAA,EAAAA,IAAgBpB,EAAY,EAC3CzB,MAAM,4CACNN,YAAY,0BAMlBN,EAAAA,cAAA,OAAKT,UAAWiF,IAAW,CACzB,6BAA6B,EAC7B,iBAAkBV,EAClB,aAAcnB,IAEhB,eAAc1B,GAEX+C,EAEExC,EAAwB,KAAOxB,EAAAA,cAACyE,EAA6B,CAC5DlF,UAAW,eACXC,UAAWsB,IAMrB,EAGAI,EAAkBL,UAAYA,EAE9B,MC7JA,ED6JA,gpBE9JA6D,EAAA,kBAAAC,CAAA,MAAAvD,EAAAuD,EAAA,GAAAC,EAAAC,OAAAC,UAAAC,EAAAH,EAAAI,eAAAC,EAAAJ,OAAAK,gBAAA,SAAA9D,EAAAuD,EAAAC,GAAAxD,EAAAuD,GAAAC,EAAAO,KAAA,EAAA3C,EAAA,mBAAA4C,OAAAA,OAAA,GAAAC,EAAA7C,EAAA8C,UAAA,aAAAC,EAAA/C,EAAAgD,eAAA,kBAAAC,EAAAjD,EAAAkD,aAAA,yBAAAC,EAAAvE,EAAAuD,EAAAC,GAAA,OAAAC,OAAAK,eAAA9D,EAAAuD,EAAA,CAAAQ,MAAAP,EAAAgB,YAAA,EAAAC,cAAA,EAAAC,UAAA,IAAA1E,EAAAuD,EAAA,KAAAgB,EAAA,aAAAvE,GAAAuE,EAAA,SAAAvE,EAAAuD,EAAAC,GAAA,OAAAxD,EAAAuD,GAAAC,CAAA,WAAAmB,EAAA3E,EAAAuD,EAAAC,EAAAG,GAAA,IAAAvC,EAAAmC,GAAAA,EAAAG,qBAAAkB,EAAArB,EAAAqB,EAAAX,EAAAR,OAAAoB,OAAAzD,EAAAsC,WAAAS,EAAA,IAAAW,EAAAnB,GAAA,WAAAE,EAAAI,EAAA,WAAAF,MAAAgB,EAAA/E,EAAAwD,EAAAW,KAAAF,CAAA,UAAAe,EAAAhF,EAAAuD,EAAAC,GAAA,WAAA5B,KAAA,SAAAqD,IAAAjF,EAAAkF,KAAA3B,EAAAC,GAAA,OAAAxD,GAAA,OAAA4B,KAAA,QAAAqD,IAAAjF,EAAA,EAAAuD,EAAAoB,KAAAA,EAAA,IAAAQ,EAAA,iBAAAC,EAAA,iBAAAC,EAAA,YAAAC,EAAA,YAAAC,EAAA,YAAAX,IAAA,UAAAY,IAAA,UAAAC,IAAA,KAAAC,EAAA,GAAAnB,EAAAmB,EAAAzB,GAAA,8BAAA0B,EAAAlC,OAAAmC,eAAAC,EAAAF,GAAAA,EAAAA,EAAAG,EAAA,MAAAD,GAAAA,IAAArC,GAAAG,EAAAuB,KAAAW,EAAA5B,KAAAyB,EAAAG,GAAA,IAAAE,EAAAN,EAAA/B,UAAAkB,EAAAlB,UAAAD,OAAAoB,OAAAa,GAAA,SAAAM,EAAAhG,GAAA,0BAAAiG,SAAA,SAAA1C,GAAAgB,EAAAvE,EAAAuD,GAAA,SAAAvD,GAAA,YAAAkG,QAAA3C,EAAAvD,EAAA,gBAAAmG,EAAAnG,EAAAuD,GAAA,SAAA6C,EAAA5C,EAAAK,EAAAzC,EAAA6C,GAAA,IAAAE,EAAAa,EAAAhF,EAAAwD,GAAAxD,EAAA6D,GAAA,aAAAM,EAAAvC,KAAA,KAAAyC,EAAAF,EAAAc,IAAAE,EAAAd,EAAAN,MAAA,OAAAoB,GAAA,UAAAkB,EAAAlB,IAAAxB,EAAAuB,KAAAC,EAAA,WAAA5B,EAAA+C,QAAAnB,EAAAoB,SAAAC,MAAA,SAAAxG,GAAAoG,EAAA,OAAApG,EAAAoB,EAAA6C,EAAA,aAAAjE,GAAAoG,EAAA,QAAApG,EAAAoB,EAAA6C,EAAA,IAAAV,EAAA+C,QAAAnB,GAAAqB,MAAA,SAAAxG,GAAAqE,EAAAN,MAAA/D,EAAAoB,EAAAiD,EAAA,aAAArE,GAAA,OAAAoG,EAAA,QAAApG,EAAAoB,EAAA6C,EAAA,IAAAA,EAAAE,EAAAc,IAAA,KAAAzB,EAAAK,EAAA,gBAAAE,MAAA,SAAA/D,EAAA2D,GAAA,SAAA8C,IAAA,WAAAlD,GAAA,SAAAA,EAAAC,GAAA4C,EAAApG,EAAA2D,EAAAJ,EAAAC,EAAA,WAAAA,EAAAA,EAAAA,EAAAgD,KAAAC,EAAAA,GAAAA,GAAA,aAAA1B,EAAAxB,EAAAC,EAAAG,GAAA,IAAAE,EAAAsB,EAAA,gBAAA/D,EAAA6C,GAAA,GAAAJ,IAAAwB,EAAA,MAAAqB,MAAA,mCAAA7C,IAAAyB,EAAA,cAAAlE,EAAA,MAAA6C,EAAA,OAAAF,MAAA/D,EAAA2G,MAAA,OAAAhD,EAAAiD,OAAAxF,EAAAuC,EAAAsB,IAAAhB,IAAA,KAAAE,EAAAR,EAAAkD,SAAA,GAAA1C,EAAA,KAAAE,EAAAyC,EAAA3C,EAAAR,GAAA,GAAAU,EAAA,IAAAA,IAAAkB,EAAA,gBAAAlB,CAAA,cAAAV,EAAAiD,OAAAjD,EAAAoD,KAAApD,EAAAqD,MAAArD,EAAAsB,SAAA,aAAAtB,EAAAiD,OAAA,IAAA/C,IAAAsB,EAAA,MAAAtB,EAAAyB,EAAA3B,EAAAsB,IAAAtB,EAAAsD,kBAAAtD,EAAAsB,IAAA,gBAAAtB,EAAAiD,QAAAjD,EAAAuD,OAAA,SAAAvD,EAAAsB,KAAApB,EAAAwB,EAAA,IAAAK,EAAAV,EAAAzB,EAAAC,EAAAG,GAAA,cAAA+B,EAAA9D,KAAA,IAAAiC,EAAAF,EAAAgD,KAAArB,EAAAF,EAAAM,EAAAT,MAAAM,EAAA,gBAAAxB,MAAA2B,EAAAT,IAAA0B,KAAAhD,EAAAgD,KAAA,WAAAjB,EAAA9D,OAAAiC,EAAAyB,EAAA3B,EAAAiD,OAAA,QAAAjD,EAAAsB,IAAAS,EAAAT,IAAA,YAAA6B,EAAAvD,EAAAC,GAAA,IAAAG,EAAAH,EAAAoD,OAAA/C,EAAAN,EAAAW,SAAAP,GAAA,GAAAE,IAAA7D,EAAA,OAAAwD,EAAAqD,SAAA,eAAAlD,GAAAJ,EAAAW,SAAA,SAAAV,EAAAoD,OAAA,SAAApD,EAAAyB,IAAAjF,EAAA8G,EAAAvD,EAAAC,GAAA,UAAAA,EAAAoD,SAAA,WAAAjD,IAAAH,EAAAoD,OAAA,QAAApD,EAAAyB,IAAA,IAAAkC,UAAA,oCAAAxD,EAAA,aAAA4B,EAAA,IAAAnE,EAAA4D,EAAAnB,EAAAN,EAAAW,SAAAV,EAAAyB,KAAA,aAAA7D,EAAAQ,KAAA,OAAA4B,EAAAoD,OAAA,QAAApD,EAAAyB,IAAA7D,EAAA6D,IAAAzB,EAAAqD,SAAA,KAAAtB,EAAA,IAAAtB,EAAA7C,EAAA6D,IAAA,OAAAhB,EAAAA,EAAA0C,MAAAnD,EAAAD,EAAA6D,YAAAnD,EAAAF,MAAAP,EAAA6D,KAAA9D,EAAA+D,QAAA,WAAA9D,EAAAoD,SAAApD,EAAAoD,OAAA,OAAApD,EAAAyB,IAAAjF,GAAAwD,EAAAqD,SAAA,KAAAtB,GAAAtB,GAAAT,EAAAoD,OAAA,QAAApD,EAAAyB,IAAA,IAAAkC,UAAA,oCAAA3D,EAAAqD,SAAA,KAAAtB,EAAA,UAAAgC,EAAAvH,GAAA,IAAAuD,EAAA,CAAAiE,OAAAxH,EAAA,SAAAA,IAAAuD,EAAAkE,SAAAzH,EAAA,SAAAA,IAAAuD,EAAAmE,WAAA1H,EAAA,GAAAuD,EAAAoE,SAAA3H,EAAA,SAAA4H,WAAA5E,KAAAO,EAAA,UAAAsE,EAAA7H,GAAA,IAAAuD,EAAAvD,EAAA8H,YAAA,GAAAvE,EAAA3B,KAAA,gBAAA2B,EAAA0B,IAAAjF,EAAA8H,WAAAvE,CAAA,UAAAuB,EAAA9E,GAAA,KAAA4H,WAAA,EAAAJ,OAAA,SAAAxH,EAAAiG,QAAAsB,EAAA,WAAAQ,OAAA,YAAAjC,EAAAvC,GAAA,GAAAA,GAAA,KAAAA,EAAA,KAAAC,EAAAD,EAAAU,GAAA,GAAAT,EAAA,OAAAA,EAAA0B,KAAA3B,GAAA,sBAAAA,EAAA8D,KAAA,OAAA9D,EAAA,IAAAyE,MAAAzE,EAAArC,QAAA,KAAA2C,GAAA,EAAAzC,EAAA,SAAAiG,IAAA,OAAAxD,EAAAN,EAAArC,QAAA,GAAAyC,EAAAuB,KAAA3B,EAAAM,GAAA,OAAAwD,EAAAtD,MAAAR,EAAAM,GAAAwD,EAAAV,MAAA,EAAAU,EAAA,OAAAA,EAAAtD,MAAA/D,EAAAqH,EAAAV,MAAA,EAAAU,CAAA,SAAAjG,EAAAiG,KAAAjG,CAAA,YAAA+F,UAAAd,EAAA9C,GAAA,2BAAAiC,EAAA9B,UAAA+B,EAAA5B,EAAAkC,EAAA,eAAAhC,MAAA0B,EAAAhB,cAAA,IAAAZ,EAAA4B,EAAA,eAAA1B,MAAAyB,EAAAf,cAAA,IAAAe,EAAAyC,YAAA1D,EAAAkB,EAAApB,EAAA,qBAAAd,EAAA2E,oBAAA,SAAAlI,GAAA,IAAAuD,EAAA,mBAAAvD,GAAAA,EAAAmI,YAAA,QAAA5E,IAAAA,IAAAiC,GAAA,uBAAAjC,EAAA0E,aAAA1E,EAAA6E,MAAA,EAAA7E,EAAA8E,KAAA,SAAArI,GAAA,OAAAyD,OAAA6E,eAAA7E,OAAA6E,eAAAtI,EAAAyF,IAAAzF,EAAAuI,UAAA9C,EAAAlB,EAAAvE,EAAAqE,EAAA,sBAAArE,EAAA0D,UAAAD,OAAAoB,OAAAkB,GAAA/F,CAAA,EAAAuD,EAAAiF,MAAA,SAAAxI,GAAA,OAAAuG,QAAAvG,EAAA,EAAAgG,EAAAG,EAAAzC,WAAAa,EAAA4B,EAAAzC,UAAAS,GAAA,0BAAAZ,EAAA4C,cAAAA,EAAA5C,EAAAkF,MAAA,SAAAzI,EAAAwD,EAAAG,EAAAE,EAAAzC,QAAA,IAAAA,IAAAA,EAAAsH,SAAA,IAAAzE,EAAA,IAAAkC,EAAAxB,EAAA3E,EAAAwD,EAAAG,EAAAE,GAAAzC,GAAA,OAAAmC,EAAA2E,oBAAA1E,GAAAS,EAAAA,EAAAoD,OAAAb,MAAA,SAAAxG,GAAA,OAAAA,EAAA2G,KAAA3G,EAAA+D,MAAAE,EAAAoD,MAAA,KAAArB,EAAAD,GAAAxB,EAAAwB,EAAA1B,EAAA,aAAAE,EAAAwB,EAAA9B,GAAA,0BAAAM,EAAAwB,EAAA,qDAAAxC,EAAAoF,KAAA,SAAA3I,GAAA,IAAAuD,EAAAE,OAAAzD,GAAAwD,EAAA,WAAAG,KAAAJ,EAAAC,EAAAR,KAAAW,GAAA,OAAAH,EAAAoF,UAAA,SAAAvB,IAAA,KAAA7D,EAAAtC,QAAA,KAAAlB,EAAAwD,EAAAqF,MAAA,GAAA7I,KAAAuD,EAAA,OAAA8D,EAAAtD,MAAA/D,EAAAqH,EAAAV,MAAA,EAAAU,CAAA,QAAAA,EAAAV,MAAA,EAAAU,CAAA,GAAA9D,EAAAuC,OAAAA,EAAAhB,EAAApB,UAAA,CAAAyE,YAAArD,EAAAiD,MAAA,SAAAxE,GAAA,QAAAuF,KAAA,OAAAzB,KAAA,OAAAN,KAAA,KAAAC,MAAAhH,EAAA,KAAA2G,MAAA,OAAAE,SAAA,UAAAD,OAAA,YAAA3B,IAAAjF,EAAA,KAAA4H,WAAA3B,QAAA4B,IAAAtE,EAAA,QAAAC,KAAA,WAAAA,EAAAuF,OAAA,IAAApF,EAAAuB,KAAA,KAAA1B,KAAAwE,OAAAxE,EAAAwF,MAAA,WAAAxF,GAAAxD,EAAA,EAAAiJ,KAAA,gBAAAtC,MAAA,MAAA3G,EAAA,KAAA4H,WAAA,GAAAE,WAAA,aAAA9H,EAAA4B,KAAA,MAAA5B,EAAAiF,IAAA,YAAAiE,IAAA,EAAAjC,kBAAA,SAAA1D,GAAA,QAAAoD,KAAA,MAAApD,EAAA,IAAAC,EAAA,cAAA2F,EAAAxF,EAAAE,GAAA,OAAAI,EAAArC,KAAA,QAAAqC,EAAAgB,IAAA1B,EAAAC,EAAA6D,KAAA1D,EAAAE,IAAAL,EAAAoD,OAAA,OAAApD,EAAAyB,IAAAjF,KAAA6D,CAAA,SAAAA,EAAA,KAAA+D,WAAA1G,OAAA,EAAA2C,GAAA,IAAAA,EAAA,KAAAzC,EAAA,KAAAwG,WAAA/D,GAAAI,EAAA7C,EAAA0G,WAAA,YAAA1G,EAAAoG,OAAA,OAAA2B,EAAA,UAAA/H,EAAAoG,QAAA,KAAAsB,KAAA,KAAA3E,EAAAR,EAAAuB,KAAA9D,EAAA,YAAAiD,EAAAV,EAAAuB,KAAA9D,EAAA,iBAAA+C,GAAAE,EAAA,SAAAyE,KAAA1H,EAAAqG,SAAA,OAAA0B,EAAA/H,EAAAqG,UAAA,WAAAqB,KAAA1H,EAAAsG,WAAA,OAAAyB,EAAA/H,EAAAsG,WAAA,SAAAvD,GAAA,QAAA2E,KAAA1H,EAAAqG,SAAA,OAAA0B,EAAA/H,EAAAqG,UAAA,YAAApD,EAAA,MAAAqC,MAAA,kDAAAoC,KAAA1H,EAAAsG,WAAA,OAAAyB,EAAA/H,EAAAsG,WAAA,KAAAR,OAAA,SAAAlH,EAAAuD,GAAA,QAAAC,EAAA,KAAAoE,WAAA1G,OAAA,EAAAsC,GAAA,IAAAA,EAAA,KAAAK,EAAA,KAAA+D,WAAApE,GAAA,GAAAK,EAAA2D,QAAA,KAAAsB,MAAAnF,EAAAuB,KAAArB,EAAA,oBAAAiF,KAAAjF,EAAA6D,WAAA,KAAAtG,EAAAyC,EAAA,OAAAzC,IAAA,UAAApB,GAAA,aAAAA,IAAAoB,EAAAoG,QAAAjE,GAAAA,GAAAnC,EAAAsG,aAAAtG,EAAA,UAAA6C,EAAA7C,EAAAA,EAAA0G,WAAA,UAAA7D,EAAArC,KAAA5B,EAAAiE,EAAAgB,IAAA1B,EAAAnC,GAAA,KAAAwF,OAAA,YAAAS,KAAAjG,EAAAsG,WAAAnC,GAAA,KAAA6D,SAAAnF,EAAA,EAAAmF,SAAA,SAAApJ,EAAAuD,GAAA,aAAAvD,EAAA4B,KAAA,MAAA5B,EAAAiF,IAAA,gBAAAjF,EAAA4B,MAAA,aAAA5B,EAAA4B,KAAA,KAAAyF,KAAArH,EAAAiF,IAAA,WAAAjF,EAAA4B,MAAA,KAAAsH,KAAA,KAAAjE,IAAAjF,EAAAiF,IAAA,KAAA2B,OAAA,cAAAS,KAAA,kBAAArH,EAAA4B,MAAA2B,IAAA,KAAA8D,KAAA9D,GAAAgC,CAAA,EAAA8D,OAAA,SAAArJ,GAAA,QAAAuD,EAAA,KAAAqE,WAAA1G,OAAA,EAAAqC,GAAA,IAAAA,EAAA,KAAAC,EAAA,KAAAoE,WAAArE,GAAA,GAAAC,EAAAkE,aAAA1H,EAAA,YAAAoJ,SAAA5F,EAAAsE,WAAAtE,EAAAmE,UAAAE,EAAArE,GAAA+B,CAAA,kBAAAvF,GAAA,QAAAuD,EAAA,KAAAqE,WAAA1G,OAAA,EAAAqC,GAAA,IAAAA,EAAA,KAAAC,EAAA,KAAAoE,WAAArE,GAAA,GAAAC,EAAAgE,SAAAxH,EAAA,KAAA2D,EAAAH,EAAAsE,WAAA,aAAAnE,EAAA/B,KAAA,KAAAiC,EAAAF,EAAAsB,IAAA4C,EAAArE,EAAA,QAAAK,CAAA,QAAA6C,MAAA,0BAAA4C,cAAA,SAAA/F,EAAAC,EAAAG,GAAA,YAAAkD,SAAA,CAAA3C,SAAA4B,EAAAvC,GAAA6D,WAAA5D,EAAA8D,QAAA3D,GAAA,cAAAiD,SAAA,KAAA3B,IAAAjF,GAAAuF,CAAA,GAAAhC,CAAA,UAAAgG,EAAA5F,EAAA3D,EAAAuD,EAAAC,EAAAK,EAAAI,EAAAE,GAAA,QAAA/C,EAAAuC,EAAAM,GAAAE,GAAAE,EAAAjD,EAAA2C,KAAA,OAAAJ,GAAA,YAAAJ,EAAAI,EAAA,CAAAvC,EAAAuF,KAAA3G,EAAAqE,GAAAqE,QAAApC,QAAAjC,GAAAmC,KAAAhD,EAAAK,EAAA,UAAA1D,EAAAqD,EAAAD,GAAA,gBAAAC,GAAA,GAAAgG,MAAAC,QAAAjG,GAAA,OAAAA,CAAA,CAAAkG,CAAAlG,IAAA,SAAAA,EAAA4B,GAAA,IAAApF,EAAA,MAAAwD,EAAA,yBAAAQ,QAAAR,EAAAQ,OAAAE,WAAAV,EAAA,uBAAAxD,EAAA,KAAAuD,EAAAI,EAAAvC,EAAAiD,EAAAJ,EAAA,GAAAoB,GAAA,EAAAxB,GAAA,SAAAzC,GAAApB,EAAAA,EAAAkF,KAAA1B,IAAA6D,KAAA,IAAAjC,EAAA,IAAA3B,OAAAzD,KAAAA,EAAA,OAAAqF,GAAA,cAAAA,GAAA9B,EAAAnC,EAAA8D,KAAAlF,IAAA2G,QAAA1C,EAAAjB,KAAAO,EAAAQ,OAAAE,EAAA/C,SAAAkE,GAAAC,GAAA,UAAA7B,GAAAK,GAAA,EAAAF,EAAAH,CAAA,iBAAA6B,GAAA,MAAArF,EAAA,SAAAqE,EAAArE,EAAA,SAAAyD,OAAAY,KAAAA,GAAA,kBAAAR,EAAA,MAAAF,CAAA,SAAAM,CAAA,EAAA0F,CAAAnG,EAAAD,IAAAqG,EAAApG,EAAAD,IAAA,qBAAA4D,UAAA,6IAAA0C,EAAA,UAAAD,EAAApG,EAAAS,GAAA,GAAAT,EAAA,qBAAAA,EAAA,OAAAsG,EAAAtG,EAAAS,GAAA,IAAAjE,EAAA,GAAA+J,SAAA7E,KAAA1B,GAAAwF,MAAA,uBAAAhJ,GAAAwD,EAAA2E,cAAAnI,EAAAwD,EAAA2E,YAAAC,MAAA,QAAApI,GAAA,QAAAA,EAAAwJ,MAAAQ,KAAAxG,GAAA,cAAAxD,GAAA,2CAAAiK,KAAAjK,GAAA8J,EAAAtG,EAAAS,QAAA,YAAA6F,EAAAtG,EAAAS,IAAA,MAAAA,GAAAA,EAAAT,EAAAtC,UAAA+C,EAAAT,EAAAtC,QAAA,QAAAqC,EAAA,EAAAI,EAAA6F,MAAAvF,GAAAV,EAAAU,EAAAV,IAAAI,EAAAJ,GAAAC,EAAAD,GAAA,OAAAI,CAAA,CAwQA,QA3PkB,SAAH5D,GA2BT,IA1BJL,EAAKK,EAALL,MACAwK,EAAUnK,EAAVmK,WACAC,EAAiBpK,EAAjBoK,kBACAC,EAA2BrK,EAA3BqK,4BACAC,EAAetK,EAAfsK,gBACAC,EAAMvK,EAANuK,OAAMC,EAAAxK,EACNyK,SAAAA,OAAQ,IAAAD,EAAG,WAAQ,EAACA,EACpBE,EAAW1K,EAAX0K,YACAC,EAAU3K,EAAV2K,WACAC,EAAW5K,EAAX4K,YACAC,EAAkB7K,EAAlB6K,mBACAC,EAAa9K,EAAb8K,cACA/J,EAAWf,EAAXe,YAAWgK,EAAA/K,EACXgL,WAAAA,OAAU,IAAAD,EAAG,GAAEA,EACfvM,EAAmBwB,EAAnBxB,oBACAyM,EAA6BjL,EAA7BiL,8BACAC,EAAsBlL,EAAtBkL,uBACA5M,EAAQ0B,EAAR1B,SACAU,EAAOgB,EAAPhB,QACAmM,EAAQnL,EAARmL,SACAC,EAAOpL,EAAPoL,QACAC,EAAQrL,EAARqL,SACAC,EAAyBtL,EAAzBsL,0BACAC,EAAsBvL,EAAtBuL,uBACAC,EAAuBxL,EAAvBwL,wBACAC,EAAazL,EAAbyL,cAEMC,EAAYZ,EAAgBa,OAAOb,GAAiB,IAE2Bc,EAAAxL,GAAjDyL,EAAAA,EAAAA,UAAS,CAAEC,MAAOJ,EAAWK,OAAQL,IAAY,GAA9EM,EAAUJ,EAAA,GAAEK,EAAaL,EAAA,GAEWM,EAAA9L,GAAfyL,EAAAA,EAAAA,WAAS,GAAM,GAApCM,EAAMD,EAAA,GAAEE,EAASF,EAAA,GAEpBG,EAAc,KAEZC,EAAqB,WACzBD,EAAcE,YAAW,WACvB,IAnDN3I,EAiEsB4I,EAdVC,GAAqBC,EAAAA,EAAAA,MAAcC,cAAc,oBAADjK,OAAqB+I,EAAa,eAAA/I,OAAc/C,IAEhGiN,EAAUjN,EAAQ,EAClBkN,EAAiBnL,EAAAA,EAAKoL,YAAYF,GAElCG,EAAMrL,EAAAA,EAAKC,YAAY6J,GAI7B,GAAIuB,GAAOA,EAAIC,YAAYJ,GAAU,CACnC,IAAMK,EAAKF,EAAIG,WAAW,CACxBC,WAAYP,EACZd,MAAOJ,EACPK,OAAQL,EACR0B,cAjEVxJ,EAiEsBL,IAAA+E,MAAE,SAAA+E,EAAOC,GAAK,IAAAb,EAAAc,EAAAC,EAAAC,EAAAC,EAAA,OAAAnK,IAAAqB,MAAA,SAAA+I,GAAA,cAAAA,EAAA5E,KAAA4E,EAAArG,MAAA,QAClBmF,GAAqBC,EAAAA,EAAAA,MAAcC,cAAc,oBAADjK,OAAqB+I,EAAa,eAAA/I,OAAc/C,QAE9F4N,EAAed,EAAmBE,cAAc,iBAEpDF,EAAmBmB,YAAYL,GAGjCD,EAAMlP,UAAY,aAEZoP,EAAQK,KAAKC,IAAIpC,EAAY4B,EAAMxB,MAAOJ,EAAY4B,EAAMvB,QAClEuB,EAAMnK,MAAM2I,MAAQ,GAAHpJ,OAAM4K,EAAMxB,MAAQ0B,EAAK,MAC1CF,EAAMnK,MAAM4I,OAAS,GAAHrJ,OAAM4K,EAAMvB,OAASyB,EAAK,MAC5CvB,EAAc,CAAEH,MAAOH,OAAO2B,EAAMxB,OAAQC,OAAQJ,OAAO2B,EAAMvB,UAE7D8B,KAAKE,IAAIlB,KACLY,EAAe,UAAH/K,OAA8B,GAAjBmK,EAAmB,6BAC5Ca,EAAqB,WAC3BJ,EAAMnK,MAAiB,UAAIsK,EAC3BH,EAAMnK,MAAM,oBAAsBuK,EAClCJ,EAAMnK,MAAM,gBAAkBsK,EAC9BH,EAAMnK,MAAM,uBAAyBuK,EACrCJ,EAAMnK,MAAM,kBAAoBsK,EAChCH,EAAMnK,MAAM,yBAA2BuK,EACvCJ,EAAMnK,MAAM,4BAA8BuK,EAC1CJ,EAAMnK,MAAM,qBAAuBsK,EACnCH,EAAMnK,MAAM,gBAAkBsK,EAC9BH,EAAMnK,MAAM,uBAAyBuK,GAGvCjB,EAAmBuB,YAAYV,IAG7BlD,GACFA,EAAkBzK,GAGpB2K,EAAgB3K,GAChByM,GAAU,GAAM,wBAAAuB,EAAAzE,OAAA,GAAAmE,EAAA,IAtCNb,EAjEtB,eAAAvM,EAAA,KAAAuD,EAAAyK,UAAA,WAAAtF,SAAA,SAAAlF,EAAAK,GAAA,IAAAI,EAAAN,EAAAsK,MAAAjO,EAAAuD,GAAA,SAAA2K,EAAAvK,GAAA4F,EAAAtF,EAAAT,EAAAK,EAAAqK,EAAAC,EAAA,OAAAxK,EAAA,UAAAwK,EAAAxK,GAAA4F,EAAAtF,EAAAT,EAAAK,EAAAqK,EAAAC,EAAA,QAAAxK,EAAA,CAAAuK,OAAA,OAwGW,SAvCWE,GAAA,OAAA7B,EAAA0B,MAAA,KAAAD,UAAA,GAwCZK,8BAA8B,IAEhC/D,EAAO5K,EAAO8M,EAAoBQ,EACpC,CACF,GAlGyB,GAmG3B,GAEAsB,EAAAA,EAAAA,YAAU,WACR,IAAMC,EAAiB,SAACC,GACtB,IAAQC,EAA0CD,EAA1CC,eAAgBC,EAA0BF,EAA1BE,MAAOC,EAAmBH,EAAnBG,MAAOC,EAAYJ,EAAZI,QAEhC9N,EAAcpB,EAAQ,EAEtBmP,EAAcF,EAAMrN,SAASR,GAC7BgO,EAAgBL,EAAeM,MAAK,SAACC,GAAW,OAAKlO,IAAgBkO,CAAW,IAChFC,EAAcxL,OAAOkF,KAAK+F,GAAOK,MAAK,SAACG,GAAS,OAAKpO,IAAgBqO,SAASD,EAAU,IACxFE,EAAgBR,EAAQtN,SAASR,GACjCuO,EAAe5N,EAAAA,EAAK6N,gBAEtBV,EAAQ1N,OAAS,GAAKxB,EAAQ,EAAI2P,IAIlCR,GAAeC,GAAiBG,GAAeG,IACjD/C,GAEJ,EAEMkD,EAAoB,WACxBpD,GAAU,GACVE,GACF,EAOA,OALA5K,EAAAA,EAAK+N,iBAAiB,eAAgBjB,GACtC9M,EAAAA,EAAK+N,iBAAiB,kBAAmBD,GACrCpE,GACFkB,IAEK,WACL5K,EAAAA,EAAKgO,oBAAoB,eAAgBlB,GACzC9M,EAAAA,EAAKgO,oBAAoB,kBAAmBF,GAC5CG,aAAatD,GACb5B,EAAS9K,EACX,CACF,GAAG,KAEHiQ,EAAAA,EAAAA,IAAa,WACPxE,GACFkB,IACAlC,EAAkBzK,IAElB0L,EAAS1L,EAEb,GAAG,CAACyL,EAASI,IAEb,IA+DMqE,EAAW9O,IAAgBpB,EAAQ,EACnCmQ,EAAY9E,EAAWrL,GACzBoQ,EAAsB,UACpBC,EAAWtO,EAAAA,EAAKoL,YAAYnN,EAAQ,GAO1C,SANMqQ,GAAyB,IAAbA,IAAmBhE,EAAWF,MAAQE,EAAWD,SAE1C,IAAbiE,GAA+B,IAAbA,IAAmBhE,EAAWF,MAAQE,EAAWD,UAD7EgE,EAAsB,WAMtBlR,EAAAA,cAAA,OACET,UAAWiF,IAAW,CACpB4M,WAAW,EACXC,OAAQL,EACRM,SAAUhG,GAAcmB,IAE1BX,WAAY,SAACnH,GAAC,OAAKmH,EAAWnH,EAAG7D,EAAM,EACvCsN,GAAG,uBAEHpO,EAAAA,cAAA,OACET,UAAU,YACV+E,MAAO,CACL2I,MAAOJ,EACPK,OAAQL,GAEVhB,YAAa,SAAClH,GAAC,OAAKkH,EAAYlH,EAAG7D,EAAM,EACzCyQ,UAAWxF,EACX9L,QA3Fc,SAAC0E,GACnB,IAAM6M,EAAkB7M,EAAE8M,OAAOzO,MAA0B,aAAlB2B,EAAE8M,OAAOzO,KAClD,GAAIoJ,IAAkCC,EAAwB,CAC5D,IAAMqF,EAA2B/M,EAAEgN,SAAWhN,EAAEiN,QAC1CC,EAAkBlN,EAAEmN,SACtBC,EAAoBC,EAAOrS,GAE/B,GAAIkS,EAAiB,CACnBpS,EAASU,EAAQ8R,4BAA2B,IAE5C,IAAIC,EAAgB1G,EACE,OAAlB0G,IACFA,EAAgBhQ,EAAc,EAC9BzC,EAASU,EAAQgS,gCAAgCD,KAGnD,IAAME,EAAqBpD,KAAKC,IAAIiD,EAAepR,GAC7CuR,EAAqBrD,KAAKsD,IAAIJ,EAAepR,GACnDiR,EAAoBC,EAAO,IAAIO,IAAGP,EAAKpH,MAAMQ,KAC3C,CAAE9I,OAAQ+P,EAAqBD,EAAqB,IACpD,SAACI,EAAGhQ,GAAC,OAAKA,EAAI4P,CAAkB,MAEpC,MAAWV,GAA4BjF,GACrChN,EAASU,EAAQ8R,4BAA2B,KAExCP,GAA4BF,GAAmB9E,IAA2B+F,EAAAA,EAAmC,aAE5E,IAA/B9S,EAAoB2C,QAAiBmK,EAE9B9M,EAAoB+C,SAAS5B,GACtCiR,EAAuBpS,EAAoB+S,QAAO,SAAClT,GAAS,OAAKsB,IAAUtB,CAAS,IAEpFuS,EAAqB3N,KAAKtD,GAJ1BiR,EAAqB3N,KAAKlC,EAAc,IAO5CzC,EAASU,EAAQgS,gCAAgCrR,KAEjDiR,EAAuB,CAACjR,GAG1B,IAAM6R,EAAwBZ,EAAqBA,EAAqBzP,OAAS,IACzCmK,IAA8BoF,GAGpEpS,EAASU,EAAQgS,gCAAgCQ,IAGnDlT,EAASU,EAAQC,0BAA0B2R,GAC7C,MAAWzF,KACT7M,EAASU,EAAQyS,aAAa,cAKhClF,YAAW,WAGJ8D,GAAmB9E,IAA2B+F,EAAAA,EAAmC,WACpF5P,EAAAA,EAAKgQ,eAAe/R,EAAQ,EAEhC,GAAG,EACL,GAgCMd,EAAAA,cAAA,OAAKoO,GAAE,YAAAvK,OAAc/C,GAASvB,UAAU,cACvCkN,GAA6Ba,GAC5BtN,EAAAA,cAAC8S,EAAAA,EAAM,CAACvT,UAAS,YAAAsE,OAAcqN,GAAuB6B,QAASpT,EAAoB+C,SAAS5B,MAGhGd,EAAAA,cAAA,OAAKT,UAAU,cAAc0R,IAC3BxE,GAA6BuE,GAAYhF,GAAsBhM,EAAAA,cAACkB,EAAiB,CAACJ,MAAOA,IAGjG,gUC7MA,MCxDA,EDOuB,SAACxB,GACtB,QA0BC0T,MAdGpT,EAAAA,EAAAA,KACF,SAACC,GAAK,MAAK,CACTC,EAAAA,EAAUgC,eAAejC,GACzBC,EAAAA,EAAUmT,cAAcpT,GACxBC,EAAAA,EAAUC,gCAAgCF,GAC1CC,EAAAA,EAAUsM,8BAA8BvM,GACxCC,EAAAA,EAAUoT,aAAarT,GACvBC,EAAAA,EAAUqT,mBAAmBtT,GAC7BC,EAAAA,EAAUsT,+BAA+BvT,GACzCC,EAAAA,EAAU2M,0BAA0B5M,GACpCC,EAAAA,EAAUuT,0BAA0BxT,GACpCC,EAAAA,EAAUwT,2BAA2BzT,GACtC,GACDoC,EAAAA,MACD,u4BAzBCC,EAAW8Q,EAAA,GACX7G,EAAU6G,EAAA,GACVrT,EAAmBqT,EAAA,GACnB5G,EAA6B4G,EAAA,GAC7BE,EAAYF,EAAA,GACZG,EAAkBH,EAAA,GAClBxH,EAA2BwH,EAAA,GAC3BvG,EAAyBuG,EAAA,GACzBtG,EAAsBsG,EAAA,GACtBrG,EAAuBqG,EAAA,GACvBO,EAAcP,EAAA,IAiBVvT,GAAWC,EAAAA,EAAAA,MAEjB,OAAOM,EAAAA,cAACoR,EAASoC,EAAA,GAAKlU,EAAK,CACzB4C,YAAAA,EACAiK,WAAAA,EACAxM,oBAAAA,EACAyM,8BAAAA,EACAC,uBAAwB6G,GAAgBC,EACxC1T,SAAAA,EACAU,QAAAA,EAAAA,EACAmM,SAAAA,EAAAA,GACAd,4BAAAA,EACAiB,0BAAAA,EACAC,uBAAAA,EACA6G,eAAAA,EACA5G,wBAAAA,IAGJ,qDEpCA,QAfA,WACE,OACE3M,EAAAA,cAAA,OAAKT,UAAW,8BACdS,EAAAA,cAACK,EAAAA,EAAmB,CAClBO,MAAM,cACNrB,UAAW,gBACXkB,QAASF,EAAAA,EAAakT,0CACtBnT,YAAaC,EAAAA,EAAamT,kDAC1B/S,IAAI,mBAENX,EAAAA,cAAA,OAAKT,UAAW,cAGtB,ECEA,EAfA,WACE,OACES,EAAAA,cAAA,OAAKT,UAAW,iBACdS,EAAAA,cAACK,EAAAA,EAAmB,CAClBO,MAAM,uBACNrB,UAAW,gBACXkB,QAAQ,+BACRH,YAAY,sCACZK,IAAI,+CAENX,EAAAA,cAAA,OAAKT,UAAW,cAGtB,ECIA,EAnBA,SAAkC4B,GAA8B,IAA3BkB,EAAWlB,EAAXkB,YAAasR,EAAUxS,EAAVwS,WAChD,OACE3T,EAAAA,cAAAA,EAAAA,SAAA,KACG2T,EAAWpR,KAAI,SAACqR,EAAW9S,GAC1B,OACEd,EAAAA,cAACwD,EAAAA,EAAM,CACLU,IAAKpD,EACLvB,UAAW,eACXe,YAAasT,EAAUtT,YACvBK,IAAKiT,EAAUjT,IACfV,QAAS,WAAF,OAAQ2T,EAAU3T,QAAQoC,EAAY,EAC7CzB,MAAOgT,EAAUhT,OAEvB,IAIN,ECKA,EArBA,SAAgCO,GAAkD,IAA/C0S,EAAiB1S,EAAjB0S,kBAAmBC,EAAwB3S,EAAxB2S,yBACpD,OACE9T,EAAAA,cAAAA,EAAAA,SAAA,KACEA,EAAAA,cAACwD,EAAAA,EAAM,CACLjE,UAAW,eACXe,YAAY,0CACZK,IAAI,oEACJV,QAAS6T,EACTlT,MAAM,kCAERZ,EAAAA,cAACwD,EAAAA,EAAM,CACLjE,UAAW,eACXe,YAAY,mCACZK,IAAI,6DACJV,QAAS4T,EACTjT,MAAM,2BAId,0NChBA,SAASmT,EAA6B5S,GAAgE,IAA7D6S,EAAQ7S,EAAR6S,SAAU3R,EAAWlB,EAAXkB,YAAa4R,EAAkC9S,EAAlC8S,mCACxDC,EAAgBlU,EAAAA,SAAemU,QAAQH,GAC7C,OAAKC,EAGEA,EAAmC1R,KAAI,SAAC0B,EAAMnD,GACnD,IAAQR,EAAsB2D,EAAtB3D,YAAa0C,EAASiB,EAATjB,KACjBmB,EAAY+P,EAAcE,MAAK,SAACC,GAAK,OAAKA,EAAM/U,MAAMgB,cAAgBA,CAAW,IAC/E4D,EAAM5D,GAAe,GAAJuD,OAAOb,EAAI,KAAAa,OAAI/C,GAWtC,OATKqD,IACU,YAATnB,IACFmB,EAAYnE,EAAAA,cAAA,OAAKT,UAAU,aAGhB,wBAATyD,IACFmB,EAAYnE,EAAAA,cAACsU,EAAyBd,EAAA,CAACtP,IAAK5D,EAAa+B,YAAaA,GAAiB4B,MAGpFE,EACHnE,EAAAA,aAAmBmE,EAAW,CAC9BD,IAAAA,IAEA,IACN,IArBSgQ,CAsBX,CAkBA,QAfA,SAAgC5U,GAC9B,IAAQ+C,EAAsG/C,EAAtG+C,YAAakS,EAAyFjV,EAAzFiV,wCAAyCV,EAAgDvU,EAAhDuU,kBAAmBC,EAA6BxU,EAA7BwU,yBACjF,OACE9T,EAAAA,cAAA,OAAKT,UAAW,mCACdS,EAAAA,cAAC+T,EAA6B,CAAC1R,YAAaA,EAAa4R,mCAAoCM,GAC3FvU,EAAAA,cAACwU,EAAuB,CAACX,kBAAmBA,EAC1CC,yBAA0BA,EAC1BxT,YAAY,4BACdN,EAAAA,cAACyU,EAA4B,CAACnU,YAAY,iCAC1CN,EAAAA,cAAC0U,EAA0B,CAACpU,YAAY,gCAIhD,EC5BA,EAfA,WACE,OACEN,EAAAA,cAAA,OAAKT,UAAW,8BACdS,EAAAA,cAACK,EAAAA,EAAmB,CAClBO,MAAM,cACNrB,UAAW,gBACXkB,QAASF,EAAAA,EAAaoU,oCACtBrU,YAAaC,EAAAA,EAAaqU,4CAC1BjU,IAAI,mBAENX,EAAAA,cAAA,OAAKT,UAAW,cAGtB,ECOA,GArBA,SAA8B4B,GAA8B,IAA3B0T,EAAS1T,EAAT0T,UAAWC,EAAY3T,EAAZ2T,aAC1C,OACE9U,EAAAA,cAAAA,EAAAA,SAAA,KACEA,EAAAA,cAACwD,EAAAA,EAAM,CACLjE,UAAW,eACXe,YAAY,YACZK,IAAI,oBACJV,QAAS4U,EACTjU,MAAM,qBAERZ,EAAAA,cAACwD,EAAAA,EAAM,CACLjE,UAAW,eACXe,YAAY,eACZK,IAAI,sBACJV,QAAS6U,EACTlU,MAAM,wBAId,6NCfA,SAASmU,GAAwB5T,GAAgE,IAA7D6S,EAAQ7S,EAAR6S,SAAU3R,EAAWlB,EAAXkB,YAAa4R,EAAkC9S,EAAlC8S,mCACnDC,EAAgBlU,EAAAA,SAAemU,QAAQH,GAC7C,OAAKC,EAGEA,EAAmC1R,KAAI,SAAC0B,EAAMnD,GACnD,IAAQR,EAAsB2D,EAAtB3D,YAAa0C,EAASiB,EAATjB,KACjBmB,EAAY+P,EAAcE,MAAK,SAACC,GAAK,OAAKA,EAAM/U,MAAMgB,cAAgBA,CAAW,IAC/E4D,EAAM5D,GAAe,GAAJuD,OAAOb,EAAI,KAAAa,OAAI/C,GAWtC,OATKqD,IACU,YAATnB,IACFmB,EAAYnE,EAAAA,cAAA,OAAKT,UAAU,aAGhB,wBAATyD,IACFmB,EAAYnE,EAAAA,cAACsU,EAAyBd,GAAA,CAACtP,IAAK5D,EAAa+B,YAAaA,GAAiB4B,MAGpFE,EACHnE,EAAAA,aAAmBmE,EAAW,CAC9BD,IAAAA,IAEA,IACN,IArBSgQ,CAsBX,CAwBA,SArBA,SAA2B5U,GACzB,IACE+C,EAME/C,EANF+C,YACAwR,EAKEvU,EALFuU,kBACAC,EAIExU,EAJFwU,yBACAG,EAGE3U,EAHF2U,mCACAY,EAEEvV,EAFFuV,UACAC,EACExV,EADFwV,aAEF,OACE9U,EAAAA,cAAA,OAAKT,UAAW,6BACdS,EAAAA,cAAC+U,GAAwB,CAAC1S,YAAaA,EAAa4R,mCAAoCA,GACtFjU,EAAAA,cAACwU,EAAuB,CAACX,kBAAmBA,EAAmBC,yBAA0BA,EAA0BxT,YAAY,4BAC/HN,EAAAA,cAACgV,GAAqB,CAACH,UAAWA,EAAWC,aAAcA,EAAcxU,YAAY,0BACrFN,EAAAA,cAACiV,EAAqB,CAAC3U,YAAY,2BAI3C,ECpCA,GAdA,SAAgCa,GAAe,IAAZ+T,EAAQ/T,EAAR+T,SACjC,OACElV,EAAAA,cAAAA,EAAAA,SAAA,KACEA,EAAAA,cAACwD,EAAAA,EAAM,CACLjE,UAAW,eACXe,YAAY,0BACZK,IAAI,6BACJV,QAASiV,EACTtU,MAAM,kBAId,ECuBA,GAnCA,SAAoCO,GAAyD,IAAtD+T,EAAQ/T,EAAR+T,SAAUC,EAAShU,EAATgU,UAAWC,EAAcjU,EAAdiU,eAAgBC,EAAalU,EAAbkU,cAC1E,OACErV,EAAAA,cAAAA,EAAAA,SAAA,KACEA,EAAAA,cAACwD,EAAAA,EAAM,CACLjE,UAAW,eACXe,YAAY,0BACZK,IAAI,6BACJV,QAASiV,EACTtU,MAAM,kBAERZ,EAAAA,cAACwD,EAAAA,EAAM,CACLjE,UAAW,eACXe,YAAY,2BACZK,IAAI,wBACJV,QAASkV,EACTvU,MAAM,mBAERZ,EAAAA,cAACwD,EAAAA,EAAM,CACLjE,UAAW,eACXe,YAAY,2BACZK,IAAI,iCACJV,QAASmV,EACTxU,MAAM,mBAERZ,EAAAA,cAACwD,EAAAA,EAAM,CACLjE,UAAW,eACXe,YAAY,0BACZK,IAAI,mBACJV,QAASoV,EACTzU,MAAM,kBAId,6NC5BA,SAASmU,GAAwB5T,GAAgE,IAA7D6S,EAAQ7S,EAAR6S,SAAU3R,EAAWlB,EAAXkB,YAAa4R,EAAkC9S,EAAlC8S,mCACnDC,EAAgBlU,EAAAA,SAAemU,QAAQH,GAC7C,OAAKC,EAGEA,EAAmC1R,KAAI,SAAC0B,EAAMnD,GACnD,IAAQR,EAAsB2D,EAAtB3D,YAAa0C,EAASiB,EAATjB,KACjBmB,EAAY+P,EAAcE,MAAK,SAACC,GAAK,OAAKA,EAAM/U,MAAMgB,cAAgBA,CAAW,IAC/E4D,EAAM5D,GAAe,GAAJuD,OAAOb,EAAI,KAAAa,OAAI/C,GAWtC,OATKqD,IACU,YAATnB,IACFmB,EAAYnE,EAAAA,cAAA,OAAKT,UAAU,aAGhB,wBAATyD,IACFmB,EAAYnE,EAAAA,cAACsU,EAAyBd,GAAA,CAACtP,IAAK5D,EAAa+B,YAAaA,GAAiB4B,MAGpFE,EACHnE,EAAAA,aAAmBmE,EAAW,CAC9BD,IAAAA,IAEA,IACN,IArBSgQ,CAsBX,CAkBA,SAfA,SAAgC5U,GAC9B,IAAQ+C,EAA8K/C,EAA9K+C,YAAawR,EAAiKvU,EAAjKuU,kBAAmBC,EAA8IxU,EAA9IwU,yBAA0BoB,EAAoH5V,EAApH4V,SAAUC,EAA0G7V,EAA1G6V,UAAWC,EAA+F9V,EAA/F8V,eAAgBC,EAA+E/V,EAA/E+V,cAAeR,EAAgEvV,EAAhEuV,UAAWC,EAAqDxV,EAArDwV,aAAcb,EAAuC3U,EAAvC2U,mCAC/I,OACEjU,EAAAA,cAAA,OAAKT,UAAW,6BACdS,EAAAA,cAAC+U,GAAwB,CAAC1S,YAAaA,EAAa4R,mCAAoCA,GACtFjU,EAAAA,cAACwU,EAAuB,CAACX,kBAAmBA,EAAmBC,yBAA0BA,EAA0BxT,YAAY,4BAC/HN,EAAAA,cAACsV,GAAuB,CAACJ,SAAUA,EAAU5U,YAAY,4BACzDN,EAAAA,cAACuV,GAA2B,CAACL,SAAUA,EAAUC,UAAWA,EAAWC,eAAgBA,EAAgBC,cAAeA,EAAe/U,YAAY,gCACjJN,EAAAA,cAACgV,GAAqB,CAACH,UAAWA,EAAWC,aAAcA,EAAcxU,YAAY,2BAI7F,oVCgFA,MC/HA,GD0BA,SAAmCa,GAAoB,IAoDjDqU,MApDgCC,EAAatU,EAAbsU,cAC9BhW,GAAWC,EAAAA,EAAAA,MACX4M,GAAWoJ,EAAAA,EAAAA,MAiBf1C,MAREpT,EAAAA,EAAAA,KAAY,SAACC,GAAK,MAAK,CACzBC,EAAAA,EAAUC,gCAAgCF,GACzC4V,GAAmC,cAAlBA,EAAqE3V,EAAAA,EAAU6V,cAAc9V,EAAO4V,GAApE3V,EAAAA,EAAU8V,kBAAkB/V,GAC9EC,EAAAA,EAAU4B,qCAAqC7B,GAC/CC,EAAAA,EAAU+V,sCAAsChW,GAChDC,EAAAA,EAAUgW,2CAA2CjW,GACrDC,EAAAA,EAAUiW,2CAA2ClW,GACrDC,EAAAA,EAAUkW,oBAAoBnW,GAC/B,MAAC,w4BAfAF,EAAmBqT,EAAA,GACnBiD,EAAUjD,EAAA,GACVkD,EAAkBlD,EAAA,GAClBiB,EAAkCjB,EAAA,GAClCmD,EAAkCnD,EAAA,GAClCoD,EAAkCpD,EAAA,GAClCqD,EAAiBrD,EAAA,GAWb3Q,EAAc1C,EAAoB4C,KAAI,SAACzB,GAAK,OAAKA,EAAQ,CAAC,IAU1D+S,EAAoB,WAAH,QAAUyC,EAAAA,EAAAA,IAAuBjU,EAAa5C,KAAagE,EAAAA,EAAAA,IAAgBpB,EAAY,EACxGyR,EAA2B,WAAH,QAAUwC,EAAAA,EAAAA,IAAuBjU,EAAa5C,KAAaiE,EAAAA,EAAAA,IAAuBrB,EAAY,EAKtHO,EAAWC,EAAAA,EAAKC,cAChBC,EAAeH,aAAQ,EAARA,EAAUI,KACzBC,EAAQF,IAAiBG,EAAAA,EAAYC,IACrCC,EAAWL,IAAiBG,EAAAA,EAAYG,QAAUN,IAAiBG,EAAAA,EAAYI,cAErF,GAAIL,GAASG,GAAYR,SAAAA,EAAUyB,4BACjC,OACErE,EAAAA,cAAA,OAAKT,UAAW,mCACdS,EAAAA,cAACwU,EAAuB,CAACX,kBAAmBA,EAAmBC,yBAA0BA,KAQ/F,IAAKuC,GAAqB/J,EACxB,IAEEkJ,GADgB3H,EAAAA,EAAAA,MAAcC,cAAc,QAAQyI,wBACxBtJ,MA1DZ,EA2DlB,CAAE,MAAOtI,GACP6Q,GAAqBS,GAAcO,GAAAA,IA5DnB,EA6DlB,MAEAhB,GAAqBS,GAAcO,GAAAA,IA/DjB,GAiEpB,IACMC,EAAejB,EAbG,IAelBkB,EAAa,CACjBvB,UAvCgB,WAAH,QAAUmB,EAAAA,EAAAA,IAAuBjU,EAAa5C,KAAakX,EAAAA,EAAAA,IAAQlX,EAAS,EAwCzF2V,eAvCqB,WAAH,QAAUkB,EAAAA,EAAAA,IAAuBjU,EAAa5C,KAAamX,EAAAA,EAAAA,IAAavU,EAAa5C,EAAS,EAwChH4V,cAvCoB,WAAH,QAAUiB,EAAAA,EAAAA,IAAuBjU,EAAa5C,KAAakE,EAAAA,EAAAA,IAAYtB,EAAa5C,EAAUyW,EAAmB,EAwClIpC,yBAAAA,EACAD,kBAAAA,EACAqB,SAvCe,WAAH,QAAUoB,EAAAA,EAAAA,IAAuBjU,EAAa5C,KAT1DA,EAASU,EAAAA,EAAQyS,aAAarS,EAAAA,EAAaG,iCAC3CjB,EAASU,EAAAA,EAAQ0W,YAAY,oBAQ+D,EAwC5FhC,UAvCgB,WAAH,QAAUyB,EAAAA,EAAAA,IAAuBjU,EAAa5C,KAAaqX,EAAAA,EAAAA,IAAezU,EAAY,EAwCnGyS,aAvCmB,WAAH,QAAUwB,EAAAA,EAAAA,IAAuBjU,EAAa5C,KAAasX,EAAAA,EAAAA,IAAkB1U,EAAY,EAwCzGA,YAAAA,GAGF,OAfqBmT,EAbG,IA6BfxV,EAAAA,cAACgX,EAAsBxD,GAAA,GACxBkD,EAAU,CACdnC,wCAAyC4B,KAIzCM,EACKzW,EAAAA,cAACiX,GAAsBzD,GAAA,GACxBkD,EAAU,CACdzC,mCAAoCmC,KAMtCpW,EAAAA,cAACkX,GAAiB1D,GAAA,GACZkD,EAAU,CACdzC,mCAAoCA,IAG1C,sgCElHA,SAASkD,GAAcC,EAAmBjL,GAKxC,IAJA,IAAIkL,EAAe,GACbC,EAAcF,EAAkBG,MAAK,SAAClS,EAAGmS,GAAC,OAAKnS,EAAImS,CAAC,IACtDC,EAAY,KAEPjV,EAAI,EAAG8U,EAAYhV,OAASE,EAAGA,IAClC8U,EAAY9U,EAAI,KAAO8U,EAAY9U,GAAK,EAC1CiV,EAA0B,OAAdA,EAAqBA,EAAYH,EAAY9U,GAClC,OAAdiV,GACTJ,EAAe,GAAHxT,OAAMwT,GAAYxT,OAAGsI,EAAWsL,GAAU,KAAA5T,OAAIsI,EAAWmL,EAAY9U,IAAG,MACpFiV,EAAY,MAEZJ,EAAe,GAAHxT,OAAMwT,GAAYxT,OAAGsI,EAAWmL,EAAY9U,IAAG,MAI/D,OAAO6U,EAAajN,MAAM,GAAI,EAChC,UAEA,IAAMsN,GAAmB,SAAHvW,GAA8C,IAAxC6K,EAAkB7K,EAAlB6K,mBAAoByJ,EAAatU,EAAbsU,cACvCrU,EAAqBG,IAAhBF,EAAAA,EAAAA,KAAgB,GAApB,GACF5B,GAAWC,EAAAA,EAAAA,MAcfsT,EAAAzR,IANE3B,EAAAA,EAAAA,KAAY,SAACC,GAAK,MAAK,CACzBC,EAAAA,EAAUC,gCAAgCF,GAC1CC,EAAAA,EAAUwB,kBAAkBzB,EAAO,mBACnCC,EAAAA,EAAUmT,cAAcpT,GACxBC,EAAAA,EAAU2M,0BAA0B5M,GACpCC,EAAAA,EAAUkC,gBAAgBnC,GAC3B,IAAC,GAXAF,EAAmBqT,EAAA,GACnB2E,EAAU3E,EAAA,GACV7G,EAAU6G,EAAA,GACVvG,EAAyBuG,EAAA,GACzB5Q,EAAY4Q,EAAA,GASR4E,EAAqBT,GAAcxX,EAAqBwM,GAEEY,EAAAxL,IAA5ByL,EAAAA,EAAAA,UAAS4K,GAAmB,GAAzDC,EAAU9K,EAAA,GAAE+K,EAAa/K,EAAA,GACgDM,EAAA9L,IAA5ByL,EAAAA,EAAAA,UAAS4K,GAAmB,GAAzEG,EAAkB1K,EAAA,GAAE2K,EAAqB3K,EAAA,GAC1C1K,EAAiBP,EAAaO,gBAEpC+M,EAAAA,EAAAA,YAAU,WACRoI,EAAcX,GAAcxX,EAAqBwM,GACnD,GAAG,CAAC2L,EAAenY,EAAqBqM,EAAoBG,IAE5D,IAkCM8L,EAAgC,WACpCxY,EAASU,EAAAA,EAAQ8R,4BAA2B,GAC9C,EAEA,OAAO0F,EAAa,KAClB3X,EAAAA,cAAA,OAAKT,UAAW,4BAA6B,eAAc,mBACxDyM,EACChM,EAAAA,cAAA,OAAKT,UAAW,oBACdS,EAAAA,cAAA,OAAKT,UAAW,YACfkN,GAA6BzM,EAAAA,cAACkX,GAAiB,CAACzB,cAAeA,IAC/D9S,GACC3C,EAAAA,cAAA,SAAOT,UAAW,wBAAyB2Y,QAAQ,oBACjDlY,EAAAA,cAAA,YACGoB,EAAE,0CAA0C,MAE/CpB,EAAAA,cAAA,QAAMT,UAAU,2BACb6B,EAAE,mDAITpB,EAAAA,cAAA,OAAKT,UAAW,yBACdS,EAAAA,cAAA,SACEwJ,KAAK,mBACL2O,OAzDG,SAACxT,GACd,IAAMyT,EAAsBzT,EAAE8M,OAAOtM,MAAMwR,QAAQ,KAAM,IACnD0B,EAASD,GAA2BE,EAAAA,EAAAA,GAAuBF,EAAqBjM,GAAjD,GAC/BoM,EAAcF,EAAM9V,KAAI,SAACiW,GAAI,OAAKA,EAAO,CAAC,IAEhD,GAAIH,EAAM/V,SAAW8V,EAAqB,CACxC3Y,EAASU,EAAAA,EAAQC,0BAA0BmY,IAE3C,IAAME,EAAgBtB,GAAcxX,EAAqBwM,GAEzD2L,EAAcW,GACdT,EAAsBS,EACxB,MACEX,EAAcC,GAGZpY,EAAoB2C,OAAS,IAAMmK,GAGrCiB,YAAW,WACTuK,GACF,GAAG,IAEP,EAmCYS,SAjCa,SAAC/T,GACxBmT,EAAcnT,EAAE8M,OAAOtM,MACzB,EAgCYA,MAAO0S,EACPc,YAAahW,EAAiB,GCxH5C,aDyHc,aAAYvB,EAAE,0CACd7B,UAAU,aACVyD,KAAK,SAEPhD,EAAAA,cAAA,OAAKT,UAAW,0BACZkN,EAQAzM,EAAAA,cAACwD,EAAAA,EAAM,CACL7C,IAAK,aACLC,MAAO,uCACPX,QAhDuB,WACrCR,EAASU,EAAAA,EAAQC,0BAA0B,CAACyC,EAAAA,EAAKf,iBAAmB,KACpErC,EAASU,EAAAA,EAAQ8R,4BAA2B,GAC9C,EA8CgB3R,YAAa,0BAXfN,EAAAA,cAACwD,EAAAA,EAAM,CACL7C,IAAK,yBACLC,MAAO,wCACPX,QAASgY,EACT3X,YAAa,wBAarB,KAGV,EAEAoX,GAAiB7W,UAAY,CAC3B8W,WAAY5W,IAAAA,KACZoL,WAAYpL,IAAAA,QAAkBA,IAAAA,QAC9B6X,sBAAuB7X,IAAAA,KACvBiL,mBAAoBjL,IAAAA,MAGtB,ME1JA,GF0JA,mEG/Ia8X,GAAgB,SAACC,EAAYC,GAAW,IAAEC,IAAe5J,UAAA9M,OAAA,QAAA2W,IAAA7J,UAAA,KAAAA,UAAA,GAAO,OAAK,SAAC3P,GAGjF,OAFAA,EAASU,EAAAA,EAAQ0W,YAAYtW,EAAAA,EAAa2Y,gBAEnC,IAAIpP,SAAQ,SAACpC,EAASyR,GAC3BtW,EAAAA,EAAKgW,cAAcC,EAAYC,GAAanR,MAAK,SAACwR,GAChD3Z,EAASU,EAAAA,EAAQyS,aAAarS,EAAAA,EAAa2Y,gBAC3CrW,EAAAA,EAAKgQ,eAAekG,GAEhBC,IACFK,EAAAA,GAAAA,GAAUC,GAAAA,EAAOC,gBAAiBH,GAGpC1R,EAAQ0R,EACV,IAAE,OAAO,SAACI,GACRL,EAAOK,GACP/Z,EAASU,EAAAA,EAAQyS,aAAarS,EAAAA,EAAa2Y,eAC7C,GACF,GACF,CAAC,ytECLD,IAAMO,GAAgC,6BAEhCC,GAAiB,MACjBC,GAAiB,OACjBC,GAAkB,KAooBxB,MChqBA,GDiCwB,SAAHzY,GAA6C,IAAvCyL,EAAazL,EAAbyL,cAAeiN,EAAiB1Y,EAAjB0Y,kBAsCvC7G,EAAAzR,IApBG3B,EAAAA,EAAAA,KACF,SAACC,GAAK,MAAK,CACTC,EAAAA,EAAUga,cAAcja,EAAO,aAC/BC,EAAAA,EAAUwB,kBAAkBzB,EAAO,mBACnCC,EAAAA,EAAU4Q,cAAc7Q,GACxBC,EAAAA,EAAUgC,eAAejC,GACzBC,EAAAA,EAAUC,gCAAgCF,GAC1CC,EAAAA,EAAUia,6BAA6Bla,GACvCC,EAAAA,EAAUka,gCAAgCna,GAC1CC,EAAAA,EAAUma,2BAA2Bpa,GACrCC,EAAAA,EAAUwB,kBAAkBzB,EAAO,oBACnCC,EAAAA,EAAUwB,kBAAkBzB,EAAO,wBACnCC,EAAAA,EAAUoT,aAAarT,GACvBC,EAAAA,EAAUqT,mBAAmBtT,GAC7BC,EAAAA,EAAU4Q,cAAc7Q,EAAO,GAC/BC,EAAAA,EAAUwT,2BAA2BzT,GACrCC,EAAAA,EAAUoa,kDAAkDra,GAC5DC,EAAAA,EAAUkC,gBAAgBnC,GAC3B,GACDoC,EAAAA,IACD,IApCCkY,EAAenH,EAAA,GACf2E,EAAU3E,EAAA,GACVoH,EAAUpH,EAAA,GACV9Q,EAAW8Q,EAAA,GACXrT,EAAmBqT,EAAA,GACnBqH,EAAyBrH,EAAA,GACzBsH,EAA4BtH,EAAA,GAC5BuH,EAAuBvH,EAAA,GACvBwH,EAA0BxH,EAAA,GAC1ByH,EAAyBzH,EAAA,GACzBE,EAAYF,EAAA,IACZG,EAAkBH,EAAA,IAClB0H,EAAqC1H,EAAA,IACrCrG,EAAuBqG,EAAA,IACvB2H,EAAmB3H,EAAA,IACnB5Q,EAAY4Q,EAAA,IAuBP5R,EAAqBG,IAAhBF,EAAAA,EAAAA,KAAgB,GAApB,GAEFuZ,GAAUC,EAAAA,EAAAA,UACVC,GAAgBD,EAAAA,EAAAA,QAAO,IACvBE,GAASF,EAAAA,EAAAA,QAAO,IAChBG,GAAsBH,EAAAA,EAAAA,QAAO,MAEwB9N,EAAAxL,IAAfyL,EAAAA,EAAAA,WAAS,GAAM,GAApDiO,EAAclO,EAAA,GAAEmO,EAAiBnO,EAAA,GACIM,EAAA9L,IAAdyL,EAAAA,EAAAA,WAAS,GAAK,GAArCT,EAAOc,EAAA,GAAE8N,EAAU9N,EAAA,GACa+N,EAAA7Z,IAAXyL,EAAAA,EAAAA,UAAS,GAAE,GAAhCE,EAAMkO,EAAA,GAAEC,EAASD,EAAA,GACaE,EAAA/Z,IAAXyL,EAAAA,EAAAA,UAAS,GAAE,GAA9BC,EAAKqO,EAAA,GAAEC,EAAQD,EAAA,GACkDE,EAAAja,IAAdyL,EAAAA,EAAAA,UAAS,MAAK,GAAjEyO,EAAqBD,EAAA,GAAEE,GAAwBF,EAAA,GACuBG,GAAApa,IAAfyL,EAAAA,EAAAA,WAAS,GAAM,GAAtE4O,GAAwBD,GAAA,GAAEE,GAAyBF,GAAA,GACDG,GAAAva,IAAXyL,EAAAA,EAAAA,UAAS,GAAE,GAAlD+O,GAAeD,GAAA,GAAEE,GAAkBF,GAAA,GACSG,GAAA1a,IAAfyL,EAAAA,EAAAA,WAAS,GAAM,GAA5CkP,GAAUD,GAAA,GAAEE,GAAaF,GAAA,GAEuBG,GAAA7a,IAAbyL,EAAAA,EAAAA,UAAS,KAAI,GAAhDf,GAAamQ,GAAA,GAAEC,GAAgBD,GAAA,GACuBE,GAAA/a,IAAXyL,EAAAA,EAAAA,UAAS,GAAE,GAAtDuP,GAAiBD,GAAA,GAAEE,GAAoBF,GAAA,GACGG,GAAAlb,IAAXyL,EAAAA,EAAAA,UAAS,GAAE,GAA1C0P,GAAWD,GAAA,GAAEE,GAAcF,GAAA,GAC5BG,GAAwC,IAA5BjQ,EAAgC+N,EAAwCN,EACpFzX,GAAiBP,aAAY,EAAZA,EAAcO,eAE/BlD,IAAWC,EAAAA,EAAAA,MAMbmd,GAAqB,CAAC,EAuBpBtR,GAAoB,SAAC/L,GACzB,IAAMsd,EAAiB/B,EAAOgC,SAAWhC,EAAOgC,QAAQvd,IAAcub,EAAOgC,QAAQvd,GAAWiB,QAChG,GAAKqc,EAAL,CAIA,IAAMxO,EAAa9O,EAAY,EACzBwd,EAAYna,EAAAA,EAAKoa,aAAa3O,GAGpC4O,EA/BuB,SAACF,EAAWG,GACnC,IAAIlQ,EACAC,EACAyB,EAYJ,OAVIqO,EAAYG,GACdxO,EAAQqO,EAAY/Q,GACpBgB,EAAQhB,GACRiB,EAAS8B,KAAKoO,MAAMD,EAAaxO,KAEjCA,EAAQwO,EAAalR,GACrBgB,EAAQ+B,KAAKoO,MAAMJ,EAAYrO,GAC/BzB,EAASjB,IAGJ,CACLgB,MAAAA,EACAC,OAAAA,EAEJ,CAY4BmQ,CAAiBL,EAFxBna,EAAAA,EAAKya,cAAchP,IAE9BrB,EAAKiQ,EAALjQ,MAAOC,EAAMgQ,EAANhQ,OAETqQ,EAAcT,EAAehP,cAAc,sBAAwBlL,SAAS4a,cAAc,UAChGD,EAAYhe,UAAY,mBACxBge,EAAYE,KAAO,MACnBF,EAAYG,UAAY,GAAH7Z,OAAMzC,EAAE,eAAc,KAAAyC,OAAIyK,GAC/CiP,EAAYjZ,MAAMqZ,SAAW,GAAH9Z,OAAMoI,GAAa,MAC7CsR,EAAYjZ,MAAMsZ,UAAY,GAAH/Z,OAAMoI,GAAa,MAC9C,IAAM4R,EAAMN,EAAYO,WAAW,MAE/BC,EAAO,EACP5M,EAAWtO,EAAAA,EAAKmb,oBAAoB1P,GACpC6C,EAAW,IACbA,GAAY,GAEd,IAAM8M,EAAaC,OAAOC,KAAKC,sBAE3BjN,EAAW,GAAM,GACnBoM,EAAYtQ,MAAQA,EACpBsQ,EAAYrQ,OAASA,EACrB6Q,EAAOR,EAAYtQ,MAAQ+P,EAC3Be,GAAQE,IAERV,EAAYtQ,MAAQC,EACpBqQ,EAAYrQ,OAASD,EAErB8Q,EAAOR,EAAYrQ,OAAS8P,EAC5Be,GAAQE,GAGVnB,EAAe3N,YAAYoO,GAC3B1a,EAAAA,EAAKwb,6BAA6BR,EAAKE,EAAM5M,GAE7C,IAAImN,EAAU,CACZhQ,WAAAA,EACAiQ,eAAgBhB,GAGZ9O,EAAQqO,EAAehP,cAAc,eAEvCW,IACF6P,EAAOE,GAAAA,GAAA,GACFF,GAAO,IACVG,qBAAsBtN,EACtBuN,mBAAoBjQ,IAMnBoO,GAAmBvO,KACtBuO,GAAmBvO,GAAcqQ,IAAS9b,EAAAA,EAAK+b,gBAAiB,OAGlEC,EADsBhC,GAAmBvO,IAC3BgQ,GA5Dd,CA6DF,EAuHA,IArHA5O,EAAAA,EAAAA,YAAU,WACR,IAAMoP,EAAmB,WACvB3D,GAAW,EACb,EAEM4D,EAAsB,SAACC,GACtBA,GACH7D,GAAW,EAEf,EAEM8D,EAAmB,WAAM,IAAAC,EACS,kBAAhB,QAAlBA,EAAArc,EAAAA,EAAKC,qBAAa,IAAAoc,OAAA,EAAlBA,EAAoBC,WACtBjE,GAAkB,GAElBA,GAAkB,GAEpB2B,GAAqB,CAAC,EACtBpd,GAASU,EAAAA,EAAQC,0BAA0B,IAC7C,EAEMgf,EAAiB,WACjBpE,EAAoB+B,UACtBla,EAAAA,EAAKgQ,eAAemI,EAAoB+B,SACxC/B,EAAoB+B,QAAU,KAElC,EAcA,OAZAla,EAAAA,EAAK+N,iBAAiB,iBAAkBkO,GACxCjc,EAAAA,EAAK+N,iBAAiB,oBAAqBmO,GAC3Clc,EAAAA,EAAK+N,iBAAiB,iBAAkBqO,GACxCpc,EAAAA,EAAK+N,iBAAiB,eAAgBwO,GAKlCvc,EAAAA,EAAKC,eACPmc,IAGK,WACLpc,EAAAA,EAAKgO,oBAAoB,iBAAkBiO,GAC3Cjc,EAAAA,EAAKgO,oBAAoB,oBAAqBkO,GAC9Clc,EAAAA,EAAKgO,oBAAoB,iBAAkBoO,GAC3Cpc,EAAAA,EAAKgO,oBAAoB,eAAgBuO,EAC3C,CACF,GAAG,KAEH1P,EAAAA,EAAAA,YAAU,WACR,IAAMC,EAAiB,SAACC,GACtB,GAAKA,EAAL,CAGA,IAAIyP,EAAsBzU,MAAMQ,KAAKzL,GAEjCiQ,EAAQI,UACVqP,EAAsBA,EAAoB3M,QAAO,SAAClT,GAAS,OAAiD,IAA5CoQ,EAAQI,QAAQ9P,QAAQV,EAAY,EAAS,KAG3GoQ,EAAQE,QACVuP,EAAsBA,EAAoB9c,KAAI,SAAC/C,GAAS,OAAMoQ,EAAQE,MAAMtQ,EAAY,GAAKoQ,EAAQE,MAAMtQ,EAAY,GAAK,EAAIA,CAAS,KAI3I,IAAM8f,EAAoB1P,EAAQG,OAAUH,EAAQG,MAAM,GAAK,GAAMsP,EAAoB,GACtD,IAA/BA,EAAoB/c,QAAgBgd,IACtCD,EAAsBzP,EAAQG,MAAMxN,KAAI,SAAC+L,GAAU,OAAKA,EAAa,CAAC,KAGxE7O,GAASU,EAAAA,EAAQC,0BAA0Bif,GAjB3C,CAkBF,EAIA,OAFAxc,EAAAA,EAAK+N,iBAAiB,eAAgBjB,GAE/B,kBAAM9M,EAAAA,EAAKgO,oBAAoB,eAAgBlB,EAAe,CACvE,GAAG,CAAChQ,KAEJ+P,EAAAA,EAAAA,YAAU,WAAM,IAAA6P,EACC,QAAfA,EAAA3E,EAAQmC,eAAO,IAAAwC,GAAfA,EAAiBC,YAAYxQ,KAAKyQ,OAAOvd,EAAc,GAAK6Z,KAC5D,IAAM2D,EAAsB,SAACC,GAC3B,IAAMC,EAAU,GAEhBD,EAAOtY,SAAQ,SAACwY,GACd,IAAMrgB,EAAYqgB,EAAMC,WAAa,GAChCD,EAAME,UAAYH,EAAQ1f,QAAQV,IAAc,IAGrDogB,EAAQxb,KAAK5E,GAEb+L,GAAkB/L,GACpB,GACF,EAEMwgB,EAAsB,SAAC1R,GAAe,IAAA2R,EACpCzgB,EAAY8O,EAAa,EAChB,QAAf2R,EAAArF,EAAQmC,eAAO,IAAAkD,GAAfA,EAAiBT,YAAYxQ,KAAKyQ,MAAMjgB,EAAYuc,IACtD,EAMA,OAJAlZ,EAAAA,EAAK+N,iBAAiB,oBAAqBoP,GAC3Cnd,EAAAA,EAAK+N,iBAAiB,oBAAqB8O,GAC3C7c,EAAAA,EAAK+N,iBAAiB,mBAAoB8O,GAEnC,WACL7c,EAAAA,EAAKgO,oBAAoB,oBAAqBmP,GAC9Cnd,EAAAA,EAAKgO,oBAAoB,oBAAqB6O,GAC9C7c,EAAAA,EAAKgO,oBAAoB,mBAAoB6O,EAC/C,CACF,GAAG,CAACzT,GAAe8P,MAEnBrM,EAAAA,EAAAA,YAAU,YACJwD,GAAgBC,KAClB1T,GAASU,EAAAA,EAAQC,0BAA0B,KAC3CX,GAASU,EAAAA,EAAQ8R,4BAA2B,IAEhD,GAAG,CAACiB,EAAcC,IAGdwE,GAAcsD,IAAoBd,IAAoBvN,IAAkBjK,GAC1E,OAAO,KAET,IAAMud,GAAY,WAChB/D,IAAc,GACdT,GAAyB,KAC3B,EAEMyE,GAAoB,SAACrf,EAAOsf,EAAQC,GACxC,IAC2EC,EADrEC,GAAM,IAAIC,MAAOC,UACvB,OAAI3f,EAAQ8b,GAAY,GAAK9b,EAAQ,GAAKyf,EAAMhE,IAAqB8D,GACpD,QAAfC,EAAA1F,EAAQmC,eAAO,IAAAuD,GAAfA,EAAiBd,YAAYxQ,KAAKyQ,OAAO3e,EAAQsf,GAAUrE,KAC3DS,GAAqB+D,GACdzf,EAAQsf,GAEVtf,CACT,EAEMgL,GAAa,SAACnH,EAAG7D,GAIrB,GAFA6D,EAAE+b,iBACF/b,EAAEgc,kBACGrG,GAAiCD,EAAtC,CAIA,IAAMuG,EAAYjc,EAAE8M,OAAO8E,wBAGzBsF,GAFEE,GAAkB,IAEQpX,EAAEkc,MAAQD,EAAUE,EAAIF,EAAU3T,MAAQ,KAE1CtI,EAAEoc,MAAQH,EAAUja,EAAIia,EAAU1T,OAAS,IAGzEwO,GAAyB5a,GACzB,IACAkgB,GAD6CnT,EAAAA,EAAAA,MAAcC,cAAc,qCACdyI,wBAAnD5P,EAACqa,EAADra,EAAGsa,EAAMD,EAANC,OAEPtc,EAAEoc,MAAQpa,EAAIua,IAChBvE,GAAewD,GAAkBrf,GAAQ,EAAG,MACnC6D,EAAEoc,MAAQE,EAASC,KAC5BvE,GAAewD,GAAkBrf,EAAO,EAAG,KAjB7C,CAmBF,EAEMqgB,GAAa,WACjBxE,GAAewD,GAAkBzD,GAAa,EAAG,KACnD,EACM0E,GAAW,WACfzE,GAAewD,GAAkBzD,IAAc,EAAG,KACpD,EAEM7Q,GAAc,SAAClH,EAAG7D,GACtB6b,GAAe7b,GACfqb,IAAc,GACd,IDtWgC9Z,ECsW1Bgf,EAAuB1hB,EAAoBwQ,MAAK,SAAC3N,GAAC,OAAKA,IAAM1B,CAAK,IAClEwgB,EAAcD,EAAuB1hB,EAAoB4C,KAAI,SAACzB,GAAK,OAAKA,EAAQ,CAAC,IAAI,CAACA,EAAQ,IACpGuY,EAAAA,GAAAA,GAAUC,GAAAA,EAAOiI,mBAEjB5c,EAAE6c,aAAaC,QAAQ,OAAQ,IAE3BH,EAAYhf,OAAS,GAEvBqC,EAAE6c,aAAaE,aAAa,IAAIC,MAAS,EAAG,GAG1CtH,GAA6BE,IAC/B5V,EAAE6c,aAAaI,WAAa,OAC5Bjd,EAAE6c,aAAaK,cAAgB,MAC/Bld,EAAE6c,aAAaC,QAAQhI,GAA+ByE,OAAO4D,aAAa1T,IDpX5C/L,ECqXVif,EDnXxBpD,OAAO6D,sBAAuBC,EAAAA,GAAAA,GAA4B3f,GAC1D6b,OAAO+D,eAAiB5f,GCqXjBgf,GACH5hB,GAASU,EAAAA,EAAQC,0BAA0B,CAACU,KAG9C+B,EAAAA,EAAKgQ,eAAe/R,EAAQ,EAC9B,EAEMohB,GAAS,SAACvd,GACdA,EAAE+b,iBACF,IAEIyB,EAFIC,EAAUzd,EAAE6c,aAAZY,MACFC,EAAWzG,GAA2BH,EAAwB,EAAIA,EAAwB,EAE3F6G,EAAAA,KAEHH,EAA+Bxd,EAAE6c,aAAae,QAAQ9I,KAExD,ID9W2C+I,EAAUzJ,EC8W/C0J,EACHN,GAAgCjE,OAAO4D,aAAa1T,KAAO+T,GAAiCC,EAAM9f,OAC/FogB,EAAmBxgB,EAAc,EAEvC,GAAImY,GAA6BoI,EAC3BN,GAAgCjE,OAAO4D,aAAa1T,KAAO+T,EAC7D1iB,IDpXuC+iB,ECoXCL,EDpXSpJ,ECoXqBsJ,EDpXL,SAAC5iB,GACxE,OAAO,IAAIqK,SAAQ,SAACpC,EAASyR,GAC3B,IAAMwJ,EAAuBzE,OAAO0E,OAAOhgB,SAASkL,cAAc,IAADjK,OAAK2e,IACjEG,IACHE,GAAQC,KAAK,8CACb3J,KAGF,IAAM4I,EAAuBY,EAAqBI,cAAchB,qBAC3DA,IACHc,GAAQC,KAAK,4DACb3J,KAGF1Z,EAASU,EAAAA,EAAQ0W,YAAYtW,EAAAA,EAAa2Y,gBAC1C6I,EAAqBna,MAAK,SAACob,GACzBvjB,EAASoZ,GAAcmK,EAAYjK,GAAa,IAAQnR,MAAK,SAAAzG,GAAyB,IAAtB8hB,EAAQ9hB,EAAR8hB,SAAU5K,EAAKlX,EAALkX,OACxEgB,EAAAA,GAAAA,GAAUC,GAAAA,EAAOC,gBAAiB,CAAE0J,SAAAA,EAAU5K,MAAOsK,EAAqBI,cAAcd,iBACxFxiB,EAASU,EAAAA,EAAQyS,aAAarS,EAAAA,EAAa2Y,gBAC3CxR,EAAQ,CAAEub,SAAAA,EAAU5K,MAAAA,GACtB,GACF,IAAE,OAAO,SAACmB,GACR/Z,EAASU,EAAAA,EAAQyS,aAAarS,EAAAA,EAAa2Y,gBAC3CC,EAAOK,EACT,GACF,GACF,IC2ViB4I,EAAM9f,QACfsI,MAAMQ,KAAKgX,GAAO/a,SAAQ,SAAC6b,GACzBzjB,GAASoZ,GAAcqK,EAAMb,GAC/B,SAEG,GAAI/H,IAAiCmI,GACZ,OAA1BhH,EAAgC,CAClC,IAAM0H,EAAmBvH,GAA2BH,EAAwB,EAAIA,EAAwB,EAElG2H,EADuBzjB,EAAoBwQ,MAAK,SAAC3N,GAAC,OAAKA,IAAMkgB,CAAgB,IAClC/iB,EAAoB4C,KAAI,SAACC,GAAC,OAAKA,EAAI,CAAC,IAAI,CAACN,GAC1F8Y,EAAoB+B,QAAUoG,EAAmBC,EAAkB1Q,QAAO,SAAC5L,GAAC,OAAKA,EAAIqc,CAAgB,IAAE7gB,OACvGO,EAAAA,EAAKwgB,UAAUD,EAAmBD,GAElC,IADA,IAAMG,EAAsB,GACnBC,EAAS,EAAGA,EAASH,EAAkB9gB,OAAQihB,IACtDD,EAAoBlf,KAAK4W,EAAoB+B,QAAUwG,IAEzDlK,EAAAA,GAAAA,GAAUC,GAAAA,EAAOkK,kBAAmB,CAAEC,sBAAuBL,EAAmBM,sBAAuBJ,EAAqBK,mBAAoBL,EAAoBhhB,QACtK,CAEFoZ,GAAyB,MACzBS,IAAc,EAChB,EAEMzQ,GAAS,SAAClM,EAAWiB,EAAS2N,GAC7BwV,GAAcpkB,IAAeqkB,GAAerkB,KAC/Cub,EAAOgC,QAAQvd,GAAa,CAC1BiB,QAAAA,EACA6M,QAAQ,GAGVwN,EAAciC,QAAQ3Y,KAAK,CACzB5E,UAAAA,EACA4O,GAAAA,IAGN,EAEM0V,GAA0B,SAACtkB,GAC/B,IAAMsB,EAAQijB,GAAqBvkB,IACpB,IAAXsB,GACFga,EAAciC,QAAQiH,OAAOljB,EAAO,EAExC,EAEM8iB,GAAgB,SAACpkB,GAAS,IAAAykB,EAAA,OAA8B,QAA9BA,EAAKlJ,EAAOgC,QAAQvd,UAAU,IAAAykB,OAAA,EAAzBA,EAA2B3W,MAAM,EAEhEuW,GAAiB,SAACrkB,GAAS,OAA0C,IAArCukB,GAAqBvkB,EAAiB,EAEtEgN,GAAW,SAAChN,GAChB,IAAMsB,EAAQijB,GAAqBvkB,IACpB,IAAXsB,IACF+B,EAAAA,EAAKqhB,oBAAoBpJ,EAAciC,QAAQjc,GAAOsN,IACtD0M,EAAciC,QAAQiH,OAAOljB,EAAO,GAExC,EAiBMijB,GAAuB,SAACvkB,GAAS,OAAKsb,EAAciC,QAAQoH,WAAU,SAACC,GAAW,OAAKA,EAAY5kB,YAAcA,CAAS,GAAC,EAE3HoM,GAAW,SAACpM,GAAc,IAAA6kB,EAAAC,EAC9B9X,GAAShN,GACT,IAAM+kB,EAAoC,QAA5BF,EAAGtJ,EAAOgC,QAAQvd,UAAU,IAAA6kB,GAAS,QAATC,EAAzBD,EAA2B5jB,eAAO,IAAA6jB,OAAT,EAAzBA,EAAoCE,iBAAiB,UAClED,SAAAA,EAAUjiB,QACZiiB,EAASld,SAAQ,SAAC9B,GAChBA,EAAE2H,OAAS,EACX3H,EAAE0H,MAAQ,CACZ,IAGE4P,GAAmBrd,IACrBqd,GAAmBrd,GAAWilB,SAEhC1J,EAAOgC,QAAQvd,GAAa,IAC9B,EAEMklB,GAAmB,SAAHC,GAA8B,IAAxB7jB,EAAK6jB,EAAL7jB,MAAOoD,EAAGygB,EAAHzgB,IAAKI,EAAKqgB,EAALrgB,MAChC/E,EAAYiF,IAAW,CAC3BogB,oBAAqB7I,GAAkB,EACvC8I,KAAK,IAEDC,IAA0B5R,GAAgBC,GAEhD,OACEnT,EAAAA,cAAA,OAAKyd,KAAK,MAAM,aAAW,MAAMle,UAAWA,EAAW2E,IAAKA,EAAKI,MAAOA,GACrE,IAAIsG,MAAMmR,IAAiBgJ,OAAOxiB,KAAI,SAACiQ,EAAGwS,GACzC,IAAMC,EAAankB,EAAQib,GAAkBiJ,EACvCE,EAAmBJ,IAA0BzK,GAA6BC,GAC1E6K,EAAkBD,GAAoBzJ,IAA0BwJ,EAEtE,OAAOA,EAAarI,GAClB5c,EAAAA,cAACA,EAAAA,SAAc,CAACkE,IAAK+gB,IACjBlJ,GAAkB,GAAoB,IAAfkJ,IAAqBE,GAAmBvJ,IAA4B5b,EAAAA,cAAA,OAAKkE,IAAG,gBAAAL,OAAkBohB,GAAc1lB,UAAU,yBAC/IS,EAAAA,cAAA,OAAKkE,IAAK+gB,EAAYxH,KAAK,OAAOyC,UAAWA,GAAW3gB,UAAU,qBAAqB6lB,cAAe,SAACzgB,GAAC,OAAKgW,IAlD5Fnb,EAkDmIylB,GAlD1II,EAkDuI1gB,GAjDrJ+b,iBACN7d,EAAAA,EAAKgQ,eAAerT,EAAY,GAC3BG,EAAoB+C,SAASlD,IAChCC,GAASU,EAAAA,EAAQC,0BAA0B,CAACZ,UAG1C0T,GAAgBC,IAIpB1T,GAASU,EAAAA,EAAQmlB,8CAA8C,CAAEC,KAAMF,EAAMxE,MAAO2E,MAAO,OAAQC,IAAKJ,EAAMtE,SAC9GthB,GAASU,EAAAA,EAAQulB,aAAa,CAACnlB,EAAAA,EAAaG,gCAZzB,IAAC2kB,EAAO7lB,CAkD8I,GAC7JQ,EAAAA,cAACoR,EAAS,CACRrF,YAAamZ,EACb5Z,WAAY3L,EAAoB+C,SAASuiB,GACzCnkB,MAAOmkB,EACP1Y,QAASA,EACTb,OAAQA,GACRc,SAAUA,GACVZ,SAAUA,GACVC,YAAaA,GACbC,WAAYA,GACZL,gBAAiBqY,GACjBvY,kBAAmBA,GACnBS,mBAAoB8Y,EACpB7Y,cAAeA,GACfW,cAAeA,KAGlBuY,IAAoBvJ,IAA4B5b,EAAAA,cAAA,OAAKkE,IAAG,gBAAAL,OAAkBohB,GAAc1lB,UAAU,0BAEnG,IACN,IAGN,EAQMomB,GAAwB,WAC5B3J,GAAmBhN,KAAKC,IAlgBR,GAkgByBD,KAAKsD,IAAI,EAAGtD,KAAKyQ,MAAMxS,EAAQhB,MAC1E,EAEM2Z,GAAkBpL,EAA6B1N,OAAOb,IAAiB,GAAKa,OAAOb,IAAiB,GACpGD,KAAuBkH,GAAgBC,GACvC0S,GAA+B,CACnC,OAAU,GAAFhiB,OAtgBY,GAsgBQ,OAExBiiB,GAAYC,GAAAA,GAEZC,GAAiB,SAACC,EAAU9gB,GAChC,IAAI+gB,EAAYpZ,OAAO3H,GAASwU,GAC5BuM,EAAY,MACdA,EAAY,KAEd7J,GAAiB6J,GACjBP,IACF,EAEA,OACE3lB,EAAAA,cAACA,EAAAA,SAAc,MACXya,GAA6Bza,EAAAA,cAAA,OAAK,eAAa,uBAAuBT,UAAU,8BAChFS,EAAAA,cAACwD,EAAAA,EAAM,CACL7C,IAAI,sBACJC,MAAM,iBACNulB,qBAAmB,EACnBlmB,QAAS,WACHgM,GAAgB2N,GAAkBA,KACpCyC,GAAiBpQ,GAAgB2N,IACjC+L,KAEJ,EACArlB,YAAY,uBAEbqC,IACC3C,EAAAA,cAAComB,GAAAA,EAAM,CACL9lB,YAAa,uBACb2lB,SAAU,OACVI,gBAAiB,OACjBpX,IAAKnC,OAAO4M,IACZpH,IAAKxF,OAAO6M,IACZxU,MAAO8G,GACPqa,gBAAiB,WAAF,OAAQra,EAAa,EACpCsa,mBAAoB,EACpBC,sBAAuB,EACvBC,kBAAmB,SAACC,EAAY3I,GAI9B,OAHIA,EAAO,IACTA,GAAQ,KAEHA,EAAO2I,EAAaZ,EAC7B,EAEAa,qCAAsC,SAACC,GAAc,OAAKA,CAAc,EACxEZ,eAAgBA,GAChBa,cAAeb,GACfc,KAAMha,OAAO8M,IACbmN,uBAAuB,EACvBC,uBAAuB,KAGzBrkB,IACA3C,EAAAA,cAAA,SACEyd,KAAK,SACLza,KAAK,QACL,aAAW,wBACXiM,IAAKyK,GACLpH,IAAKqH,GACLxU,MAAO8G,GACP,gBAAeyN,GACf,gBAAeC,GACf,gBAAe1N,GACfyM,SAAU,SAAC/T,GACT0X,GAAiBvP,OAAOnI,EAAE8M,OAAOtM,QACjCwgB,IACF,EACAmB,KAAMlN,GACNra,UAAU,mBACV6O,GAAG,kBAGPpO,EAAAA,cAACwD,EAAAA,EAAM,CACL7C,IAAI,qBACJC,MAAM,gBACNulB,qBAAmB,EACnBlmB,QAAS,WACHgM,GAAgBa,OAAO8M,IAAmB,OAC5CyC,GAAiBpQ,GAAgBa,OAAO8M,KACxC+L,KAEJ,EACArlB,YAAY,uBAGhBN,EAAAA,cAACinB,EAAAA,EAAO,CAACC,QAAM,EAACC,SApGE,SAAHC,GAAmB,IAAbF,EAAME,EAANF,OACvB7L,EAAU6L,EAAOha,QACjBqO,EAAS2L,EAAOja,OAChB+O,GAAmBhN,KAAKC,IA9fR,GA8fyBD,KAAKsD,IAAI,EAAGtD,KAAKyQ,MAAMyH,EAAOja,MAAQhB,MACjF,EAgG6C/H,IAAK+H,KAC3C,SAAAob,GAAA,IAAGC,EAAUD,EAAVC,WAAU,OACZtnB,EAAAA,cAAA,OAAKT,UAAS,yBAAAsE,OAA2B+I,GAAiBwB,GAAG,mCAAmC,eAAa,kBAAkB8T,OAAQA,GAAQqF,IAAKD,GAClJtnB,EAAAA,cAAA,OAAKT,UAAU,oCACZ2c,GACClc,EAAAA,cAAA,OAAKT,UAAU,0BAA0BuM,WAAYsV,GAAU9c,MAAOuhB,KAAuC,GAE/G7lB,EAAAA,cAACwnB,EAAAA,GAAI,CACHD,IAAK3M,EACL1N,OAAQA,EACRD,MAAOA,EACPwa,UAAW7B,GAGX8B,SAAU1Y,KAAK2Y,KAAK/K,GAAYb,IAChC6L,YAAalD,GACbmD,iBAAkB,EAClBtoB,UAAW,iBACX+E,MAAO,CAAEwjB,QAAS,QAElBC,cAAe/Y,KAAKyQ,OAAOvd,EAAc,GAAK6Z,IAC9C0B,KAAK,OACL,aAAYrc,EAAE,+BAEf8a,GACClc,EAAAA,cAAA,OAAKT,UAAU,0BAA0BuM,WAAYqV,GAAY7c,MAAKka,GAAAA,GAAA,GAAOqH,IAA4B,IAAE,OAAU,WAAmB,IAGxI,IAGV7lB,EAAAA,cAAC0X,GAAgB,CAAC1L,mBAAoBA,GAAoByJ,cAAeoE,GAAqBjN,IAGpG,oBE9pBAob,EADkC,EAAQ,MAChCC,EAA4B,IAE9B7jB,KAAK,CAAC8jB,EAAO9Z,GAAI,2jEAA4jE,KAErlE4Z,EAAQG,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,QAEvBD,EAAOF,QAAUA,mBCVjB,IAAII,EAAM,EAAQ,OACFC,EAAU,EAAQ,OAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACH,EAAO9Z,GAAIia,EAAS,MAwDjCD,EAAIC,EArDH,CAEd/J,OAAiB,SAAUkK,GAgBX,IAAKtK,OAAOuK,8BAEV,YADA7lB,SAAS8lB,KAAKvZ,YAAYqZ,GAI5B,IAAIG,EAEJA,EAAgB/lB,SAASgmB,qBAAqB,oBAEzCD,EAAcrmB,SACjBqmB,EAzBF,SAASE,EAAwBC,EAASC,EAAOnmB,UAC/C,MAAMomB,EAAW,GAYjB,OATAD,EAAKvE,iBAAiBsE,GAASzhB,SAAQ4hB,GAAMD,EAAS5kB,KAAK6kB,KAG3DF,EAAKvE,iBAAiB,KAAKnd,SAAQ4hB,IAC7BA,EAAGC,YACLF,EAAS5kB,QAAQykB,EAAwBC,EAASG,EAAGC,YACvD,IAGKF,CACT,CAWkBH,CAAwB,qBAG1C,MAAMM,EAAkB,GACxB,IAAK,IAAI3mB,EAAI,EAAGA,EAAImmB,EAAcrmB,OAAQE,IAAK,CAC7C,MAAM4mB,EAAeT,EAAcnmB,GACnC,GAAU,IAANA,EACF4mB,EAAaF,WAAW/Z,YAAYqZ,GACpCA,EAASa,OAAS,WACZF,EAAgB7mB,OAAS,GAC3B6mB,EAAgB9hB,SAASiiB,IAEvBA,EAAUC,UAAYf,EAASe,SAAS,GAG9C,MACK,CACL,MAAMD,EAAYd,EAASgB,WAAU,GACrCJ,EAAaF,WAAW/Z,YAAYma,GACpCH,EAAgB/kB,KAAKklB,EACvB,CACF,CACF,EACdhL,WAAoB,IAMpB4J,EAAOF,QAAUK,EAAQF,QAAU,CAAC,mBClEpC,IAAIC,EAAM,EAAQ,OACFC,EAAU,EAAQ,OAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACH,EAAO9Z,GAAIia,EAAS,MAwDjCD,EAAIC,EArDH,CAEd/J,OAAiB,SAAUkK,GAgBX,IAAKtK,OAAOuK,8BAEV,YADA7lB,SAAS8lB,KAAKvZ,YAAYqZ,GAI5B,IAAIG,EAEJA,EAAgB/lB,SAASgmB,qBAAqB,oBAEzCD,EAAcrmB,SACjBqmB,EAzBF,SAASE,EAAwBC,EAASC,EAAOnmB,UAC/C,MAAMomB,EAAW,GAYjB,OATAD,EAAKvE,iBAAiBsE,GAASzhB,SAAQ4hB,GAAMD,EAAS5kB,KAAK6kB,KAG3DF,EAAKvE,iBAAiB,KAAKnd,SAAQ4hB,IAC7BA,EAAGC,YACLF,EAAS5kB,QAAQykB,EAAwBC,EAASG,EAAGC,YACvD,IAGKF,CACT,CAWkBH,CAAwB,qBAG1C,MAAMM,EAAkB,GACxB,IAAK,IAAI3mB,EAAI,EAAGA,EAAImmB,EAAcrmB,OAAQE,IAAK,CAC7C,MAAM4mB,EAAeT,EAAcnmB,GACnC,GAAU,IAANA,EACF4mB,EAAaF,WAAW/Z,YAAYqZ,GACpCA,EAASa,OAAS,WACZF,EAAgB7mB,OAAS,GAC3B6mB,EAAgB9hB,SAASiiB,IAEvBA,EAAUC,UAAYf,EAASe,SAAS,GAG9C,MACK,CACL,MAAMD,EAAYd,EAASgB,WAAU,GACrCJ,EAAaF,WAAW/Z,YAAYma,GACpCH,EAAgB/kB,KAAKklB,EACvB,CACF,CACF,EACdhL,WAAoB,IAMpB4J,EAAOF,QAAUK,EAAQF,QAAU,CAAC,oBChEpCH,EADkC,EAAQ,MAChCC,EAA4B,IAE9B7jB,KAAK,CAAC8jB,EAAO9Z,GAAI,uwEAA0wE,KAEnyE4Z,EAAQG,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,QAEvBD,EAAOF,QAAUA,mBCVjB,IAAII,EAAM,EAAQ,OACFC,EAAU,EAAQ,OAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACH,EAAO9Z,GAAIia,EAAS,MAwDjCD,EAAIC,EArDH,CAEd/J,OAAiB,SAAUkK,GAgBX,IAAKtK,OAAOuK,8BAEV,YADA7lB,SAAS8lB,KAAKvZ,YAAYqZ,GAI5B,IAAIG,EAEJA,EAAgB/lB,SAASgmB,qBAAqB,oBAEzCD,EAAcrmB,SACjBqmB,EAzBF,SAASE,EAAwBC,EAASC,EAAOnmB,UAC/C,MAAMomB,EAAW,GAYjB,OATAD,EAAKvE,iBAAiBsE,GAASzhB,SAAQ4hB,GAAMD,EAAS5kB,KAAK6kB,KAG3DF,EAAKvE,iBAAiB,KAAKnd,SAAQ4hB,IAC7BA,EAAGC,YACLF,EAAS5kB,QAAQykB,EAAwBC,EAASG,EAAGC,YACvD,IAGKF,CACT,CAWkBH,CAAwB,qBAG1C,MAAMM,EAAkB,GACxB,IAAK,IAAI3mB,EAAI,EAAGA,EAAImmB,EAAcrmB,OAAQE,IAAK,CAC7C,MAAM4mB,EAAeT,EAAcnmB,GACnC,GAAU,IAANA,EACF4mB,EAAaF,WAAW/Z,YAAYqZ,GACpCA,EAASa,OAAS,WACZF,EAAgB7mB,OAAS,GAC3B6mB,EAAgB9hB,SAASiiB,IAEvBA,EAAUC,UAAYf,EAASe,SAAS,GAG9C,MACK,CACL,MAAMD,EAAYd,EAASgB,WAAU,GACrCJ,EAAaF,WAAW/Z,YAAYma,GACpCH,EAAgB/kB,KAAKklB,EACvB,CACF,CACF,EACdhL,WAAoB,IAMpB4J,EAAOF,QAAUK,EAAQF,QAAU,CAAC,oBChEpCH,EADkC,EAAQ,MAChCC,EAA4B,IAE9B7jB,KAAK,CAAC8jB,EAAO9Z,GAAI,yzKAA0zK,KAEn1K4Z,EAAQG,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,QAEvBD,EAAOF,QAAUA,mBCVjB,IAAII,EAAM,EAAQ,OACFC,EAAU,EAAQ,OAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACH,EAAO9Z,GAAIia,EAAS,MAwDjCD,EAAIC,EArDH,CAEd/J,OAAiB,SAAUkK,GAgBX,IAAKtK,OAAOuK,8BAEV,YADA7lB,SAAS8lB,KAAKvZ,YAAYqZ,GAI5B,IAAIG,EAEJA,EAAgB/lB,SAASgmB,qBAAqB,oBAEzCD,EAAcrmB,SACjBqmB,EAzBF,SAASE,EAAwBC,EAASC,EAAOnmB,UAC/C,MAAMomB,EAAW,GAYjB,OATAD,EAAKvE,iBAAiBsE,GAASzhB,SAAQ4hB,GAAMD,EAAS5kB,KAAK6kB,KAG3DF,EAAKvE,iBAAiB,KAAKnd,SAAQ4hB,IAC7BA,EAAGC,YACLF,EAAS5kB,QAAQykB,EAAwBC,EAASG,EAAGC,YACvD,IAGKF,CACT,CAWkBH,CAAwB,qBAG1C,MAAMM,EAAkB,GACxB,IAAK,IAAI3mB,EAAI,EAAGA,EAAImmB,EAAcrmB,OAAQE,IAAK,CAC7C,MAAM4mB,EAAeT,EAAcnmB,GACnC,GAAU,IAANA,EACF4mB,EAAaF,WAAW/Z,YAAYqZ,GACpCA,EAASa,OAAS,WACZF,EAAgB7mB,OAAS,GAC3B6mB,EAAgB9hB,SAASiiB,IAEvBA,EAAUC,UAAYf,EAASe,SAAS,GAG9C,MACK,CACL,MAAMD,EAAYd,EAASgB,WAAU,GACrCJ,EAAaF,WAAW/Z,YAAYma,GACpCH,EAAgB/kB,KAAKklB,EACvB,CACF,CACF,EACdhL,WAAoB,IAMpB4J,EAAOF,QAAUK,EAAQF,QAAU,CAAC,oBChEpCH,EADkC,EAAQ,MAChCC,EAA4B,IAE9B7jB,KAAK,CAAC8jB,EAAO9Z,GAAI,yrGAA0rG,KAEntG4Z,EAAQG,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,QAEvBD,EAAOF,QAAUA,mBCVjB,IAAII,EAAM,EAAQ,OACFC,EAAU,EAAQ,OAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACH,EAAO9Z,GAAIia,EAAS,MAwDjCD,EAAIC,EArDH,CAEd/J,OAAiB,SAAUkK,GAgBX,IAAKtK,OAAOuK,8BAEV,YADA7lB,SAAS8lB,KAAKvZ,YAAYqZ,GAI5B,IAAIG,EAEJA,EAAgB/lB,SAASgmB,qBAAqB,oBAEzCD,EAAcrmB,SACjBqmB,EAzBF,SAASE,EAAwBC,EAASC,EAAOnmB,UAC/C,MAAMomB,EAAW,GAYjB,OATAD,EAAKvE,iBAAiBsE,GAASzhB,SAAQ4hB,GAAMD,EAAS5kB,KAAK6kB,KAG3DF,EAAKvE,iBAAiB,KAAKnd,SAAQ4hB,IAC7BA,EAAGC,YACLF,EAAS5kB,QAAQykB,EAAwBC,EAASG,EAAGC,YACvD,IAGKF,CACT,CAWkBH,CAAwB,qBAG1C,MAAMM,EAAkB,GACxB,IAAK,IAAI3mB,EAAI,EAAGA,EAAImmB,EAAcrmB,OAAQE,IAAK,CAC7C,MAAM4mB,EAAeT,EAAcnmB,GACnC,GAAU,IAANA,EACF4mB,EAAaF,WAAW/Z,YAAYqZ,GACpCA,EAASa,OAAS,WACZF,EAAgB7mB,OAAS,GAC3B6mB,EAAgB9hB,SAASiiB,IAEvBA,EAAUC,UAAYf,EAASe,SAAS,GAG9C,MACK,CACL,MAAMD,EAAYd,EAASgB,WAAU,GACrCJ,EAAaF,WAAW/Z,YAAYma,GACpCH,EAAgB/kB,KAAKklB,EACvB,CACF,CACF,EACdhL,WAAoB,IAMpB4J,EAAOF,QAAUK,EAAQF,QAAU,CAAC,oBChEpCH,EADkC,EAAQ,MAChCC,EAA4B,IAE9B7jB,KAAK,CAAC8jB,EAAO9Z,GAAI,i7DAAk7D,KAE38D4Z,EAAQG,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,QAEvBD,EAAOF,QAAUA", "sources": ["webpack://webviewer-ui/./src/components/PageManipulationOverlayButton/PageManipulationOverlayButtonContainer.js", "webpack://webviewer-ui/./src/components/PageManipulationOverlayButton/index.js", "webpack://webviewer-ui/./src/components/ThumbnailControls/ThumbnailControls.js", "webpack://webviewer-ui/./src/components/ThumbnailControls/index.js", "webpack://webviewer-ui/./src/components/Thumbnail/Thumbnail.js", "webpack://webviewer-ui/./src/components/Thumbnail/ThumbnailRedux.js", "webpack://webviewer-ui/./src/components/Thumbnail/index.js", "webpack://webviewer-ui/./src/components/LeftPanelPageTabs/LeftPanelPageTabsMoreSmall/LeftPanelPageTabsMoreSmall.js", "webpack://webviewer-ui/./src/components/LeftPanelPageTabs/LeftPanelPageTabsInsertSmall/LeftPanelPageTabsInsertSmall.js", "webpack://webviewer-ui/./src/components/LeftPanelPageTabs/CustomLeftPanelOperations/CustomLeftPanelOperations.js", "webpack://webviewer-ui/./src/components/LeftPanelPageTabs/LeftPanelPageTabsRotate/LeftPanelPageTabsRotate.js", "webpack://webviewer-ui/./src/components/LeftPanelPageTabs/LeftPanelPageTabsSmall/LeftPanelPageTabsSmall.js", "webpack://webviewer-ui/./src/components/LeftPanelPageTabs/LeftPanelPageTabsMore/LeftPanelPageTabsMore.js", "webpack://webviewer-ui/./src/components/LeftPanelPageTabs/LeftPanelPageTabsMove/LeftPanelPageTabsMove.js", "webpack://webviewer-ui/./src/components/LeftPanelPageTabs/LeftPanelPageTabs/LeftPanelPageTabs.js", "webpack://webviewer-ui/./src/components/LeftPanelPageTabs/LeftPanelPageTabsInsert/LeftPanelPageTabsInsert.js", "webpack://webviewer-ui/./src/components/LeftPanelPageTabs/LeftPanelPageTabsOperations/LeftPanelPageTabsOperations.js", "webpack://webviewer-ui/./src/components/LeftPanelPageTabs/LeftPanelPageTabsLarge/LeftPanelPageTabsLarge.js", "webpack://webviewer-ui/./src/components/LeftPanelPageTabs/LeftPanelPageTabsContainer.js", "webpack://webviewer-ui/./src/components/LeftPanelPageTabs/index.js", "webpack://webviewer-ui/./src/components/DocumentControls/DocumentControls.js", "webpack://webviewer-ui/./src/constants/pageNumberPlaceholder.js", "webpack://webviewer-ui/./src/components/DocumentControls/index.js", "webpack://webviewer-ui/./src/helpers/pageManipulation.js", "webpack://webviewer-ui/./src/components/ThumbnailsPanel/ThumbnailsPanel.js", "webpack://webviewer-ui/./src/components/ThumbnailsPanel/index.js", "webpack://webviewer-ui/./src/components/LeftPanelPageTabs/LeftPanelPageTabs/LeftPanelPageTabsContainer.scss", "webpack://webviewer-ui/./src/components/ThumbnailsPanel/ThumbnailsPanel.scss?a7cf", "webpack://webviewer-ui/./src/components/ThumbnailControls/ThumbnailControls.scss?5972", "webpack://webviewer-ui/./src/components/ThumbnailControls/ThumbnailControls.scss", "webpack://webviewer-ui/./src/components/DocumentControls/DocumentControls.scss?bf92", "webpack://webviewer-ui/./src/components/ThumbnailsPanel/ThumbnailsPanel.scss", "webpack://webviewer-ui/./src/components/LeftPanelPageTabs/LeftPanelPageTabs/LeftPanelPageTabsContainer.scss?e3c4", "webpack://webviewer-ui/./src/components/DocumentControls/DocumentControls.scss", "webpack://webviewer-ui/./src/components/Thumbnail/Thumbnail.scss?0bd8", "webpack://webviewer-ui/./src/components/Thumbnail/Thumbnail.scss"], "sourcesContent": ["import React from 'react';\nimport ToggleElementButton from '../ToggleElementButton';\nimport { useDispatch, useSelector } from 'react-redux';\nimport selectors from 'selectors';\nimport actions from 'actions';\nimport DataElements from 'constants/dataElement';\n\nfunction PageManipulationOverlayButtonContainer(props) {\n  const { className, pageIndex } = props;\n\n  const dispatch = useDispatch();\n  const selectedPageIndexes = useSelector((state) => selectors.getSelectedThumbnailPageIndexes(state));\n\n  const onClickPageManipulationOverlayButton = () => {\n    if (selectedPageIndexes.indexOf(pageIndex) === -1) {\n      dispatch(actions.setSelectedPageThumbnails([pageIndex]));\n    }\n  };\n\n  return (\n    <div\n      className={className}\n      onClick={onClickPageManipulationOverlayButton}\n    >\n      <ToggleElementButton\n        dataElement={DataElements.PAGE_MANIPULATION_OVERLAY_BUTTON}\n        element={DataElements.PAGE_MANIPULATION_OVERLAY}\n        img=\"icon-tool-more\"\n        title=\"option.thumbnailPanel.moreOptions\"\n      />\n    </div>\n  );\n}\n\nexport default PageManipulationOverlayButtonContainer;", "import PageManipulationOverlayButtonContainer from './PageManipulationOverlayButtonContainer';\n\nexport default PageManipulationOverlayButtonContainer;", "import React from 'react';\nimport { useDispatch, shallowEqual, useSelector } from 'react-redux';\nimport PropTypes from 'prop-types';\nimport classNames from 'classnames';\nimport { deletePages, rotateClockwise, rotateCounterClockwise } from 'helpers/pageManipulationFunctions';\nimport Button from 'components/Button';\nimport selectors from 'selectors';\nimport './ThumbnailControls.scss';\nimport PageManipulationOverlayButton from 'components/PageManipulationOverlayButton';\nimport { workerTypes } from 'constants/types';\nimport core from 'src/core';\nimport DataElements from 'constants/dataElement';\nimport { useTranslation } from 'react-i18next';\n\nconst propTypes = {\n  index: PropTypes.number.isRequired,\n};\n\nconst dataElementName = 'thumbnailControl';\n\nconst ThumbnailControls = ({ index }) => {\n  const { t } = useTranslation();\n  const [isElementDisabled] = useSelector((state) => [selectors.isElementDisabled(state, dataElementName)]);\n  const [isMoreOptionDisabled] = useSelector((state) => [selectors.isElementDisabled(state, DataElements.PAGE_MANIPULATION_OVERLAY_BUTTON)]);\n  const [isPageDeletionConfirmationModalEnabled, selectedIndexes] = useSelector((state) => [\n    selectors.pageDeletionConfirmationModalEnabled(state),\n    selectors.getSelectedThumbnailPageIndexes(state),\n  ]);\n  const dispatch = useDispatch();\n\n  const [\n    currentPage,\n    pageThumbnailControlMenuItems,\n    featureFlags,\n  ] = useSelector((state) => [\n    selectors.getCurrentPage(state),\n    selectors.getThumbnailControlMenuItems(state),\n    selectors.getFeatureFlags(state),\n  ], shallowEqual);\n\n  let pageNumbers = selectedIndexes.length > 0 ? selectedIndexes.map((i) => i + 1) : [index + 1];\n\n  const isCurrentPageInTheSelection = pageNumbers.includes(currentPage);\n  const customizableUI = featureFlags.customizableUI;\n\n  if (!isCurrentPageInTheSelection) {\n    pageNumbers = [currentPage];\n  }\n\n  const document = core.getDocument();\n  const documentType = document?.type;\n  const isXod = documentType === workerTypes.XOD;\n  const isOffice = documentType === workerTypes.OFFICE || documentType === workerTypes.LEGACY_OFFICE;\n\n  const BUTTONS_MAP = {\n    'thumbRotateClockwise': <Button\n      className=\"rotate-button\"\n      img=\"icon-header-page-manipulation-page-rotation-clockwise-line\"\n      onClick={() => rotateClockwise(pageNumbers)}\n      title=\"option.thumbnailPanel.rotatePageClockwise\"\n      dataElement=\"thumbRotateClockwise\"\n    />,\n    'thumbRotateCounterClockwise': <Button\n      img=\"icon-header-page-manipulation-page-rotation-counterclockwise-line\"\n      onClick={() => rotateCounterClockwise(pageNumbers)}\n      title=\"option.thumbnailPanel.rotatePageCounterClockwise\"\n      dataElement=\"thumbRotateCounterClockwise\"\n    />,\n    'thumbDelete': <Button\n      className=\"delete-button\"\n      img=\"icon-delete-line\"\n      onClick={() => deletePages(pageNumbers, dispatch, isPageDeletionConfirmationModalEnabled)}\n      title=\"option.thumbnailPanel.delete\"\n      dataElement=\"thumbDelete\"\n      onClickAnnouncement={`${t('action.delete')} ${t('action.modal')} ${t('action.isOpen')}`}\n    />,\n  };\n  let isCustomized = false;\n  const occurredButtons = [];\n  const buttons = pageThumbnailControlMenuItems.map((item) => {\n    const { dataElement } = item;\n    const key = dataElement;\n    let component = BUTTONS_MAP[dataElement];\n    if (occurredButtons.indexOf(dataElement) > -1) {\n      return null;\n    }\n    occurredButtons.push(dataElement);\n\n    /* Example button object:\n    {\n      title: 'Alert me',\n      img: '/path-to-image',\n      onClick: (selectedPageNumbers) => alert(``),\n      dataElement: 'alertMeDataElement',\n    } */\n    if (!component) {\n      isCustomized = true;\n      const { img, onClick, title } = item;\n      component = <Button\n        className={`${dataElement}-button`}\n        img={img}\n        onClick={() => onClick(currentPage)}\n        title={title}\n        dataElement={dataElement}\n      />;\n    }\n\n    return component\n      ? React.cloneElement(component, {\n        key,\n      })\n      : null;\n  });\n\n  if (isElementDisabled) {\n    return null;\n  } if (isXod || isOffice || document?.isWebViewerServerDocument()) {\n    return (\n      <div className=\"thumbnailControls-overlay\" data-element={dataElementName}\n        style={{ display: 'flex' }}\n      >\n        <Button\n          img=\"icon-header-page-manipulation-page-rotation-counterclockwise-line\"\n          onClick={() => rotateCounterClockwise(pageNumbers)}\n          title=\"option.thumbnailPanel.rotatePageCounterClockwise\"\n          dataElement=\"thumbRotateCounterClockwise\"\n        />\n        <Button\n          img=\"icon-header-page-manipulation-page-rotation-clockwise-line\"\n          onClick={() => rotateClockwise(pageNumbers)}\n          title=\"option.thumbnailPanel.rotatePageClockwise\"\n          dataElement=\"thumbRotateClockwise\"\n        />\n      </div>\n    );\n  }\n  return (\n    <div className={classNames({\n      'thumbnailControls-overlay': true,\n      'custom-buttons': isCustomized,\n      'modular-ui': customizableUI,\n    })}\n    data-element={dataElementName}\n    >\n      {buttons}\n      {\n        (isMoreOptionDisabled) ? null : <PageManipulationOverlayButton\n          className={'more-options'}\n          pageIndex={index}\n        />\n      }\n\n    </div>\n  );\n};\n\n\nThumbnailControls.propTypes = propTypes;\n\nexport default ThumbnailControls;\n", "import ThumbnailControls from './ThumbnailControls';\n\nexport default ThumbnailControls;\n", "import React, { useEffect, useState } from 'react';\nimport classNames from 'classnames';\nimport useDidUpdate from 'hooks/useDidUpdate';\nimport core from 'core';\nimport ThumbnailControls from 'components/ThumbnailControls';\nimport thumbnailSelectionModes from 'constants/thumbnailSelectionModes';\n\nimport './Thumbnail.scss';\nimport { Choice } from '@pdftron/webviewer-react-toolkit';\nimport getRootNode from 'helpers/getRootNode';\n\n// adds a delay in ms so thumbs that are only on the screen briefly are not loaded.\nconst THUMBNAIL_LOAD_DELAY = 50;\n\nconst Thumbnail = ({\n  index,\n  isSelected,\n  updateAnnotations,\n  shiftKeyThumbnailPivotIndex,\n  onFinishLoading,\n  onLoad,\n  onRemove = () => { },\n  onDragStart,\n  onDragOver,\n  isDraggable,\n  shouldShowControls,\n  thumbnailSize,\n  currentPage,\n  pageLabels = [],\n  selectedPageIndexes,\n  isThumbnailMultiselectEnabled,\n  isReaderModeOrReadOnly,\n  dispatch,\n  actions,\n  isMobile,\n  canLoad,\n  onCancel,\n  isThumbnailSelectingPages,\n  thumbnailSelectionMode,\n  activeDocumentViewerKey,\n  panelSelector\n}) => {\n  const thumbSize = thumbnailSize ? Number(thumbnailSize) : 150;\n\n  const [dimensions, setDimensions] = useState({ width: thumbSize, height: thumbSize });\n  // To ensure checkmark loads after thumbnail\n  const [loaded, setLoaded] = useState(false);\n\n  let loadTimeout = null;\n\n  const loadThumbnailAsync = () => {\n    loadTimeout = setTimeout(() => {\n      const thumbnailContainer = getRootNode().querySelector(`.ThumbnailsPanel.${panelSelector} #pageThumb${index}`);\n\n      const pageNum = index + 1;\n      const viewerRotation = core.getRotation(pageNum);\n\n      const doc = core.getDocument(activeDocumentViewerKey);\n      // Possible race condition can happen where we try to render a thumbnail for a page that has\n      // been deleted. Prevent that by checking if pageInfo exists\n\n      if (doc && doc.getPageInfo(pageNum)) {\n        const id = doc.loadCanvas({\n          pageNumber: pageNum,\n          width: thumbSize,\n          height: thumbSize,\n          drawComplete: async (thumb) => {\n            const thumbnailContainer = getRootNode().querySelector(`.ThumbnailsPanel.${panelSelector} #pageThumb${index}`);\n            if (thumbnailContainer) {\n              const childElement = thumbnailContainer.querySelector('.page-image');\n              if (childElement) {\n                thumbnailContainer.removeChild(childElement);\n              }\n\n              thumb.className = 'page-image';\n\n              const ratio = Math.min(thumbSize / thumb.width, thumbSize / thumb.height);\n              thumb.style.width = `${thumb.width * ratio}px`;\n              thumb.style.height = `${thumb.height * ratio}px`;\n              setDimensions({ width: Number(thumb.width), height: Number(thumb.height) });\n\n              if (Math.abs(viewerRotation)) {\n                const cssTransform = `rotate(${viewerRotation * 90}deg) translate(-50%,-50%)`;\n                const cssTransformOrigin = 'top left';\n                thumb.style['transform'] = cssTransform;\n                thumb.style['transform-origin'] = cssTransformOrigin;\n                thumb.style['ms-transform'] = cssTransform;\n                thumb.style['ms-transform-origin'] = cssTransformOrigin;\n                thumb.style['-moz-transform'] = cssTransform;\n                thumb.style['-moz-transform-origin'] = cssTransformOrigin;\n                thumb.style['-webkit-transform-origin'] = cssTransformOrigin;\n                thumb.style['-webkit-transform'] = cssTransform;\n                thumb.style['-o-transform'] = cssTransform;\n                thumb.style['-o-transform-origin'] = cssTransformOrigin;\n              }\n\n              thumbnailContainer.appendChild(thumb);\n            }\n\n            if (updateAnnotations) {\n              updateAnnotations(index);\n            }\n\n            onFinishLoading(index);\n            setLoaded(true);\n          },\n          allowUseOfOptimizedThumbnail: true,\n        });\n        onLoad(index, thumbnailContainer, id);\n      }\n    }, THUMBNAIL_LOAD_DELAY);\n  };\n\n  useEffect(() => {\n    const onPagesUpdated = (changes) => {\n      const { contentChanged, moved, added, removed } = changes;\n\n      const currentPage = index + 1;\n\n      const isPageAdded = added.includes(currentPage);\n      const didPageChange = contentChanged.some((changedPage) => currentPage === changedPage);\n      const didPageMove = Object.keys(moved).some((movedPage) => currentPage === parseInt(movedPage));\n      const isPageRemoved = removed.includes(currentPage);\n      const newPageCount = core.getTotalPages();\n\n      if (removed.length > 0 && index + 1 > newPageCount) {\n        return;\n      }\n\n      if (isPageAdded || didPageChange || didPageMove || isPageRemoved) {\n        loadThumbnailAsync();\n      }\n    };\n\n    const onRotationUpdated = () => {\n      setLoaded(false);\n      loadThumbnailAsync();\n    };\n\n    core.addEventListener('pagesUpdated', onPagesUpdated);\n    core.addEventListener('rotationUpdated', onRotationUpdated);\n    if (canLoad) {\n      loadThumbnailAsync();\n    }\n    return () => {\n      core.removeEventListener('pagesUpdated', onPagesUpdated);\n      core.removeEventListener('rotationUpdated', onRotationUpdated);\n      clearTimeout(loadTimeout);\n      onRemove(index);\n    };\n  }, []);\n\n  useDidUpdate(() => {\n    if (canLoad) {\n      loadThumbnailAsync();\n      updateAnnotations(index);\n    } else {\n      onCancel(index);\n    }\n  }, [canLoad, activeDocumentViewerKey]);\n\n  const handleClick = (e) => {\n    const checkboxToggled = e.target.type && e.target.type === 'checkbox';\n    if (isThumbnailMultiselectEnabled && !isReaderModeOrReadOnly) {\n      const multiSelectionKeyPressed = e.ctrlKey || e.metaKey;\n      const shiftKeyPressed = e.shiftKey;\n      let updatedSelectedPages = [...selectedPageIndexes];\n\n      if (shiftKeyPressed) {\n        dispatch(actions.setThumbnailSelectingPages(true));\n        // Include current page as part of selection if we just started shift-selecting\n        let shiftKeyPivot = shiftKeyThumbnailPivotIndex;\n        if (shiftKeyPivot === null) {\n          shiftKeyPivot = currentPage - 1;\n          dispatch(actions.setShiftKeyThumbnailsPivotIndex(shiftKeyPivot));\n        }\n        // get the range of the selected index and update selected page\n        const currSelectMinIndex = Math.min(shiftKeyPivot, index);\n        const currSelectMaxIndex = Math.max(shiftKeyPivot, index);\n        updatedSelectedPages = [...new Set([...Array.from(\n          { length: currSelectMaxIndex - currSelectMinIndex + 1 },\n          (_, i) => i + currSelectMinIndex,\n        )])];\n      } else if (multiSelectionKeyPressed || isThumbnailSelectingPages) {\n        dispatch(actions.setThumbnailSelectingPages(true));\n        // Only select a page if multiSelectionKeyPressed or if checkbox is checked unless in 'thumbnail' mode\n        if (multiSelectionKeyPressed || checkboxToggled || thumbnailSelectionMode === thumbnailSelectionModes['THUMBNAIL']) {\n          // Include current page as part of selection if we just started multi-selecting\n          if (selectedPageIndexes.length === 0 && !isThumbnailSelectingPages) {\n            updatedSelectedPages.push(currentPage - 1);\n          } else if (selectedPageIndexes.includes(index)) {\n            updatedSelectedPages = selectedPageIndexes.filter((pageIndex) => index !== pageIndex);\n          } else {\n            updatedSelectedPages.push(index);\n          }\n        }\n        dispatch(actions.setShiftKeyThumbnailsPivotIndex(index));\n      } else {\n        updatedSelectedPages = [index];\n      }\n      // set shiftKeyPivot when press ctr key or single key\n      const lastSelectedPageIndex = updatedSelectedPages[updatedSelectedPages.length - 1];\n      const shouldUpdateShiftKeyPivotIndex = !isThumbnailSelectingPages && !shiftKeyPressed;\n\n      if (shouldUpdateShiftKeyPivotIndex) {\n        dispatch(actions.setShiftKeyThumbnailsPivotIndex(lastSelectedPageIndex));\n      }\n\n      dispatch(actions.setSelectedPageThumbnails(updatedSelectedPages));\n    } else if (isMobile()) {\n      dispatch(actions.closeElement('leftPanel'));\n    }\n\n    // due to the race condition, we need a settimeout here\n    // otherwise, sometimes the current page will not be visible in left panel\n    setTimeout(() => {\n      // If user clicks on checkbox, it should not automatically jump to that page,\n      // only if the user clicks on thumbnail then go to page and view it, unless in 'thumbnail' mode\n      if (!checkboxToggled || thumbnailSelectionMode === thumbnailSelectionModes['THUMBNAIL']) {\n        core.setCurrentPage(index + 1);\n      }\n    }, 0);\n  };\n\n  const isActive = currentPage === index + 1;\n  const pageLabel = pageLabels[index];\n  let checkboxRotateClass = 'default';\n  const rotation = core.getRotation(index + 1);\n  if ((!rotation || rotation === 2) && dimensions.width > dimensions.height) {\n    checkboxRotateClass = 'rotated';\n  } else if ((rotation === 1 || rotation === 3) && dimensions.width < dimensions.height) {\n    checkboxRotateClass = 'rotated';\n  }\n\n  return (\n    <div\n      className={classNames({\n        Thumbnail: true,\n        active: isActive,\n        selected: isSelected && isThumbnailSelectingPages,\n      })}\n      onDragOver={(e) => onDragOver(e, index)}\n      id=\"Thumbnail-container\"\n    >\n      <div\n        className=\"container\"\n        style={{\n          width: thumbSize,\n          height: thumbSize,\n        }}\n        onDragStart={(e) => onDragStart(e, index)}\n        draggable={isDraggable}\n        onClick={handleClick}\n      >\n        <div id={`pageThumb${index}`} className=\"thumbnail\" />\n        {isThumbnailSelectingPages && loaded && (\n          <Choice className={`checkbox ${checkboxRotateClass}`} checked={selectedPageIndexes.includes(index)} />\n        )}\n      </div>\n      <div className=\"page-label\">{pageLabel}</div>\n      {!isThumbnailSelectingPages && isActive && shouldShowControls && <ThumbnailControls index={index} />}\n    </div>\n  );\n};\n\nexport default Thumbnail;\n", "import React from 'react';\nimport { useSelector, shallowEqual, useDispatch } from 'react-redux';\n\nimport selectors from 'selectors';\nimport actions from 'actions';\nimport { isMobile } from 'helpers/device';\n\nimport Thumbnail from './Thumbnail';\n\nconst ThumbnailRedux = (props) => {\n  const [\n    currentPage,\n    pageLabels,\n    selectedPageIndexes,\n    isThumbnailMultiselectEnabled,\n    isReaderMode,\n    isDocumentReadOnly,\n    shiftKeyThumbnailPivotIndex,\n    isThumbnailSelectingPages,\n    thumbnailSelectionMode,\n    activeDocumentViewerKey,\n    selectionModes\n  ] = useSelector(\n    (state) => [\n      selectors.getCurrentPage(state),\n      selectors.getPageLabels(state),\n      selectors.getSelectedThumbnailPageIndexes(state),\n      selectors.isThumbnailMultiselectEnabled(state),\n      selectors.isReaderMode(state),\n      selectors.isDocumentReadOnly(state),\n      selectors.getShiftKeyThumbnailPivotIndex(state),\n      selectors.isThumbnailSelectingPages(state),\n      selectors.getThumbnailSelectionMode(state),\n      selectors.getActiveDocumentViewerKey(state),\n    ],\n    shallowEqual,\n  );\n\n  const dispatch = useDispatch();\n\n  return <Thumbnail {...props} {...{\n    currentPage,\n    pageLabels,\n    selectedPageIndexes,\n    isThumbnailMultiselectEnabled,\n    isReaderModeOrReadOnly: isReaderMode || isDocumentReadOnly,\n    dispatch,\n    actions,\n    isMobile,\n    shiftKeyThumbnailPivotIndex,\n    isThumbnailSelectingPages,\n    thumbnailSelectionMode,\n    selectionModes,\n    activeDocumentViewerKey,\n  }}\n  />;\n};\n\nexport default ThumbnailRedux;\n", "import ThumbnailRedux from './ThumbnailRedux';\n\nexport default ThumbnailRedux;", "import React from 'react';\nimport ToggleElementButton from 'components/ToggleElementButton';\nimport '../LeftPanelPageTabs/LeftPanelPageTabsContainer.scss';\nimport DataElements from 'constants/dataElement';\n\nfunction LeftPanelPageTabsMoreSmall() {\n  return (\n    <div className={'dropdown-menu button-hover'}>\n      <ToggleElementButton\n        title=\"action.more\"\n        className={'dropdown-menu'}\n        element={DataElements.THUMBNAILS_CONTROL_MANIPULATE_POPUP_SMALL}\n        dataElement={DataElements.THUMBNAILS_CONTROL_MANIPULATE_POPUP_SMALL_TRIGGER}\n        img=\"icon-tool-more\"\n      />\n      <div className={'indicator'} />\n    </div>\n  );\n}\n\nexport default LeftPanelPageTabsMoreSmall;\n", "import React from 'react';\n\nimport ToggleElementButton from 'components/ToggleElementButton';\nimport '../LeftPanelPageTabs/LeftPanelPageTabsContainer.scss';\n\nfunction LeftPanelPageTabsInsertSmall() {\n  return (\n    <div className={'dropdown-menu'}>\n      <ToggleElementButton\n        title=\"action.PageInsertion\"\n        className={'dropdown-menu'}\n        element=\"thumbnailsControlInsertPopup\"\n        dataElement=\"thumbnailsControlInsertPopupTrigger\"\n        img=\"icon-header-page-manipulation-insert-above\"\n      />\n      <div className={'indicator'} />\n    </div>\n  );\n}\n\nexport default LeftPanelPageTabsInsertSmall;\n", "import React from 'react';\nimport Button from 'components/Button';\n\nfunction CustomLeftPanelOperations({ pageNumbers, operations }) {\n  return (\n    <>\n      {operations.map((operation, index) => {\n        return (\n          <Button\n            key={index}\n            className={'button-hover'}\n            dataElement={operation.dataElement}\n            img={operation.img}\n            onClick={() => operation.onClick(pageNumbers)}\n            title={operation.title}\n          />);\n      })\n      }\n    </>\n  );\n}\n\nexport default CustomLeftPanelOperations;\n", "import React from 'react';\nimport Button from 'components/Button';\nimport '../LeftPanelPageTabs/LeftPanelPageTabsContainer.scss';\n\nfunction LeftPanelPageTabsRotate({ onRotateClockwise, onRotateCounterClockwise }) {\n  return (\n    <>\n      <Button\n        className={'button-hover'}\n        dataElement=\"thumbnailsControlRotateCounterClockwise\"\n        img=\"icon-header-page-manipulation-page-rotation-counterclockwise-line\"\n        onClick={onRotateCounterClockwise}\n        title=\"action.rotateCounterClockwise\"\n      />\n      <Button\n        className={'button-hover'}\n        dataElement=\"thumbnailsControlRotateClockwise\"\n        img=\"icon-header-page-manipulation-page-rotation-clockwise-line\"\n        onClick={onRotateClockwise}\n        title=\"action.rotateClockwise\"\n      />\n    </>\n  );\n}\n\nexport default LeftPanelPageTabsRotate;\n", "import React from 'react';\nimport '../LeftPanelPageTabs/LeftPanelPageTabsContainer.scss';\nimport LeftPanelPageTabsMoreSmall from '../LeftPanelPageTabsMoreSmall/LeftPanelPageTabsMoreSmall';\nimport LeftPanelPageTabsInsertSmall from '../LeftPanelPageTabsInsertSmall/LeftPanelPageTabsInsertSmall';\nimport CustomLeftPanelOperations from '../CustomLeftPanelOperations/CustomLeftPanelOperations';\nimport LeftPanelPageTabsRotate from '../LeftPanelPageTabsRotate/LeftPanelPageTabsRotate';\n\nfunction InitialLeftPanelPageTabsSmall({ children, pageNumbers, multiPageManipulationControlsItems }) {\n  const childrenArray = React.Children.toArray(children);\n  if (!multiPageManipulationControlsItems) {\n    return childrenArray;\n  }\n  return multiPageManipulationControlsItems.map((item, index) => {\n    const { dataElement, type } = item;\n    let component = childrenArray.find((child) => child.props.dataElement === dataElement);\n    const key = dataElement || `${type}-${index}`;\n\n    if (!component) {\n      if (type === 'divider') {\n        component = <div className=\"divider\" />;\n      }\n\n      if (type === 'customPageOperation') {\n        component = <CustomLeftPanelOperations key={dataElement} pageNumbers={pageNumbers} {...item} />;\n      }\n    }\n    return component\n      ? React.cloneElement(component, {\n        key,\n      })\n      : null;\n  });\n}\n\n\nfunction LeftPanelPageTabsSmall(props) {\n  const { pageNumbers, multiPageManipulationControlsItemsSmall, onRotateClockwise, onRotateCounterClockwise } = props;\n  return (\n    <div className={'PageControlContainer root small'}>\n      <InitialLeftPanelPageTabsSmall pageNumbers={pageNumbers} multiPageManipulationControlsItems={multiPageManipulationControlsItemsSmall}>\n        <LeftPanelPageTabsRotate onRotateClockwise={onRotateClockwise}\n          onRotateCounterClockwise={onRotateCounterClockwise}\n          dataElement=\"leftPanelPageTabsRotate\"/>\n        <LeftPanelPageTabsInsertSmall dataElement=\"leftPanelPageTabsInsertSmall\" />\n        <LeftPanelPageTabsMoreSmall dataElement=\"leftPanelPageTabsMoreSmall\" />\n      </InitialLeftPanelPageTabsSmall>\n    </div>\n  );\n}\n\nexport default LeftPanelPageTabsSmall;\n", "import React from 'react';\nimport ToggleElementButton from 'components/ToggleElementButton';\nimport '../LeftPanelPageTabs/LeftPanelPageTabsContainer.scss';\nimport DataElements from 'constants/dataElement';\n\nfunction LeftPanelPageTabsMore() {\n  return (\n    <div className={'dropdown-menu button-hover'}>\n      <ToggleElementButton\n        title=\"action.more\"\n        className={'dropdown-menu'}\n        element={DataElements.THUMBNAILS_CONTROL_MANIPULATE_POPUP}\n        dataElement={DataElements.THUMBNAILS_CONTROL_MANIPULATE_POPUP_TRIGGER}\n        img=\"icon-tool-more\"\n      />\n      <div className={'indicator'} />\n    </div>\n  );\n}\n\nexport default LeftPanelPageTabsMore;\n", "import React from 'react';\nimport Button from 'components/Button';\nimport '../LeftPanelPageTabs/LeftPanelPageTabsContainer.scss';\n\nfunction LeftPanelPageTabsMove({ moveToTop, moveToBottom }) {\n  return (\n    <>\n      <Button\n        className={'button-hover'}\n        dataElement=\"moveToTop\"\n        img=\"icon-page-move-up\"\n        onClick={moveToTop}\n        title=\"action.moveToTop\"\n      />\n      <Button\n        className={'button-hover'}\n        dataElement=\"moveToBottom\"\n        img=\"icon-page-move-down\"\n        onClick={moveToBottom}\n        title=\"action.moveToBottom\"\n      />\n    </>\n  );\n}\n\nexport default LeftPanelPageTabsMove;\n", "import React from 'react';\n\nimport LeftPanelPageTabsMore from '../LeftPanelPageTabsMore/LeftPanelPageTabsMore';\nimport LeftPanelPageTabsRotate from '../LeftPanelPageTabsRotate/LeftPanelPageTabsRotate';\nimport CustomLeftPanelOperations from '../CustomLeftPanelOperations/CustomLeftPanelOperations';\nimport LeftPanelPageTabsMove from 'components/LeftPanelPageTabs/LeftPanelPageTabsMove/LeftPanelPageTabsMove';\n\n\nfunction InitialLeftPanelPageTabs({ children, pageNumbers, multiPageManipulationControlsItems }) {\n  const childrenArray = React.Children.toArray(children);\n  if (!multiPageManipulationControlsItems) {\n    return childrenArray;\n  }\n  return multiPageManipulationControlsItems.map((item, index) => {\n    const { dataElement, type } = item;\n    let component = childrenArray.find((child) => child.props.dataElement === dataElement);\n    const key = dataElement || `${type}-${index}`;\n\n    if (!component) {\n      if (type === 'divider') {\n        component = <div className=\"divider\" />;\n      }\n\n      if (type === 'customPageOperation') {\n        component = <CustomLeftPanelOperations key={dataElement} pageNumbers={pageNumbers} {...item} />;\n      }\n    }\n    return component\n      ? React.cloneElement(component, {\n        key,\n      })\n      : null;\n  });\n}\n\n\nfunction LeftPanelPageTabs(props) {\n  const {\n    pageNumbers,\n    onRotateClockwise,\n    onRotateCounterClockwise,\n    multiPageManipulationControlsItems,\n    moveToTop,\n    moveToBottom\n  } = props;\n  return (\n    <div className={'PageControlContainer root'}>\n      <InitialLeftPanelPageTabs pageNumbers={pageNumbers} multiPageManipulationControlsItems={multiPageManipulationControlsItems} >\n        <LeftPanelPageTabsRotate onRotateClockwise={onRotateClockwise} onRotateCounterClockwise={onRotateCounterClockwise} dataElement=\"leftPanelPageTabsRotate\" />\n        <LeftPanelPageTabsMove moveToTop={moveToTop} moveToBottom={moveToBottom} dataElement=\"leftPanelPageTabsMove\"/>\n        <LeftPanelPageTabsMore dataElement=\"leftPanelPageTabsMore\" />\n      </InitialLeftPanelPageTabs>\n    </div>\n  );\n}\n\n\nexport default LeftPanelPageTabs;\n", "import React from 'react';\nimport Button from 'components/Button';\nimport '../LeftPanelPageTabs/LeftPanelPageTabsContainer.scss';\n\nfunction LeftPanelPageTabsInsert({ onInsert }) {\n  return (\n    <>\n      <Button\n        className={'button-hover'}\n        dataElement=\"thumbnailsControlInsert\"\n        img=\"icon-page-insertion-insert\"\n        onClick={onInsert}\n        title=\"action.insert\"\n      />\n    </>\n  );\n}\n\nexport default LeftPanelPageTabsInsert;\n", "import React from 'react';\nimport Button from 'components/Button';\nimport '../LeftPanelPageTabs/LeftPanelPageTabsContainer.scss';\n\nfunction LeftPanelPageTabsOperations({ onInsert, onReplace, onExtractPages, onDeletePages }) {\n  return (\n    <>\n      <Button\n        className={'button-hover'}\n        dataElement=\"thumbnailsControlInsert\"\n        img=\"icon-page-insertion-insert\"\n        onClick={onInsert}\n        title=\"action.insert\"\n      />\n      <Button\n        className={'button-hover'}\n        dataElement=\"thumbnailsControlReplace\"\n        img=\"icon-page-replacement\"\n        onClick={onReplace}\n        title=\"action.replace\"\n      />\n      <Button\n        className={'button-hover'}\n        dataElement=\"thumbnailsControlExtract\"\n        img=\"icon-page-manipulation-extract\"\n        onClick={onExtractPages}\n        title=\"action.extract\"\n      />\n      <Button\n        className={'button-hover'}\n        dataElement=\"thumbnailsControlDelete\"\n        img=\"icon-delete-line\"\n        onClick={onDeletePages}\n        title=\"action.delete\"\n      />\n    </>\n  );\n}\n\nexport default LeftPanelPageTabsOperations;\n", "import React from 'react';\n\nimport LeftPanelPageTabsInsert from '../LeftPanelPageTabsInsert/LeftPanelPageTabsInsert';\nimport LeftPanelPageTabsRotate from '../LeftPanelPageTabsRotate/LeftPanelPageTabsRotate';\nimport LeftPanelPageTabsOperations from '../LeftPanelPageTabsOperations/LeftPanelPageTabsOperations';\nimport CustomLeftPanelOperations from '../CustomLeftPanelOperations/CustomLeftPanelOperations';\nimport LeftPanelPageTabsMove from '../LeftPanelPageTabsMove/LeftPanelPageTabsMove';\n\n\nfunction InitialLeftPanelPageTabs({ children, pageNumbers, multiPageManipulationControlsItems }) {\n  const childrenArray = React.Children.toArray(children);\n  if (!multiPageManipulationControlsItems) {\n    return childrenArray;\n  }\n  return multiPageManipulationControlsItems.map((item, index) => {\n    const { dataElement, type } = item;\n    let component = childrenArray.find((child) => child.props.dataElement === dataElement);\n    const key = dataElement || `${type}-${index}`;\n\n    if (!component) {\n      if (type === 'divider') {\n        component = <div className=\"divider\" />;\n      }\n\n      if (type === 'customPageOperation') {\n        component = <CustomLeftPanelOperations key={dataElement} pageNumbers={pageNumbers} {...item} />;\n      }\n    }\n    return component\n      ? React.cloneElement(component, {\n        key,\n      })\n      : null;\n  });\n}\n\n\nfunction LeftPanelPageTabsLarge(props) {\n  const { pageNumbers, onRotateClockwise, onRotateCounterClockwise, onInsert, onReplace, onExtractPages, onDeletePages, moveToTop, moveToBottom, multiPageManipulationControlsItems } = props;\n  return (\n    <div className={'PageControlContainer root'}>\n      <InitialLeftPanelPageTabs pageNumbers={pageNumbers} multiPageManipulationControlsItems={multiPageManipulationControlsItems} >\n        <LeftPanelPageTabsRotate onRotateClockwise={onRotateClockwise} onRotateCounterClockwise={onRotateCounterClockwise} dataElement=\"leftPanelPageTabsRotate\" />\n        <LeftPanelPageTabsInsert onInsert={onInsert} dataElement=\"leftPanelPageTabsInsert\" />\n        <LeftPanelPageTabsOperations onInsert={onInsert} onReplace={onReplace} onExtractPages={onExtractPages} onDeletePages={onDeletePages} dataElement=\"leftPanelPageTabsOperations\" />\n        <LeftPanelPageTabsMove moveToTop={moveToTop} moveToBottom={moveToBottom} dataElement=\"leftPanelPageTabsMove\" />\n      </InitialLeftPanelPageTabs>\n    </div>\n  );\n}\n\n\nexport default LeftPanelPageTabsLarge;\n", "import React from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport selectors from 'selectors';\nimport actions from 'actions';\nimport {\n  deletePages,\n  extractPages,\n  noPagesSelectedWarning,\n  replace,\n  rotateClockwise,\n  rotateCounterClockwise,\n  movePagesToBottom,\n  movePagesToTop\n} from 'helpers/pageManipulationFunctions';\nimport LeftPanelPageTabsSmall from 'src/components/LeftPanelPageTabs/LeftPanelPageTabsSmall/LeftPanelPageTabsSmall';\nimport LeftPanelPageTabs from 'components/LeftPanelPageTabs/LeftPanelPageTabs/LeftPanelPageTabs';\nimport { workerTypes } from 'constants/types';\nimport core from 'src/core';\nimport LeftPanelPageTabsRotate from 'components/LeftPanelPageTabs/LeftPanelPageTabsRotate/LeftPanelPageTabsRotate';\nimport LeftPanelPageTabsLarge from './LeftPanelPageTabsLarge/LeftPanelPageTabsLarge';\nimport DataElements from 'constants/dataElement';\nimport { panelMinWidth } from 'constants/panel';\nimport { isMobile as isInMobile } from 'helpers/device';\nimport getRootNode from 'helpers/getRootNode';\n\n// Values come from the CSS\nconst WIDTH_MARGINS = 16 + 8 + 16 + 16 + 16 + 16;\n\nfunction LeftPanelPageTabsContainer({ parentElement }) {\n  const dispatch = useDispatch();\n  const isMobile = isInMobile();\n  const [\n    selectedPageIndexes,\n    panelWidth,\n    deleteModalEnabled,\n    multiPageManipulationControlsItems,\n    multiPageManipulationControlsSmall,\n    multiPageManipulationControlsLarge,\n    isDesktopOnlyMode,\n  ] = useSelector((state) => [\n    selectors.getSelectedThumbnailPageIndexes(state),\n    !parentElement || parentElement === 'leftPanel' ? selectors.getLeftPanelWidth(state) : selectors.getPanelWidth(state, parentElement),\n    selectors.pageDeletionConfirmationModalEnabled(state),\n    selectors.getMultiPageManipulationControlsItems(state),\n    selectors.getMultiPageManipulationControlsItemsSmall(state),\n    selectors.getMultiPageManipulationControlsItemsLarge(state),\n    selectors.isInDesktopOnlyMode(state),\n  ]);\n\n  const pageNumbers = selectedPageIndexes.map((index) => index + 1);\n\n  const openInsertPageModal = () => {\n    dispatch(actions.closeElement(DataElements.PAGE_MANIPULATION_OVERLAY));\n    dispatch(actions.openElement('insertPageModal'));\n  };\n\n  const onReplace = () => !noPagesSelectedWarning(pageNumbers, dispatch) && replace(dispatch);\n  const onExtractPages = () => !noPagesSelectedWarning(pageNumbers, dispatch) && extractPages(pageNumbers, dispatch);\n  const onDeletePages = () => !noPagesSelectedWarning(pageNumbers, dispatch) && deletePages(pageNumbers, dispatch, deleteModalEnabled);\n  const onRotateClockwise = () => !noPagesSelectedWarning(pageNumbers, dispatch) && rotateClockwise(pageNumbers);\n  const onRotateCounterClockwise = () => !noPagesSelectedWarning(pageNumbers, dispatch) && rotateCounterClockwise(pageNumbers);\n  const onInsert = () => !noPagesSelectedWarning(pageNumbers, dispatch) && openInsertPageModal();\n  const moveToTop = () => !noPagesSelectedWarning(pageNumbers, dispatch) && movePagesToTop(pageNumbers);\n  const moveToBottom = () => !noPagesSelectedWarning(pageNumbers, dispatch) && movePagesToBottom(pageNumbers);\n\n  const document = core.getDocument();\n  const documentType = document?.type;\n  const isXod = documentType === workerTypes.XOD;\n  const isOffice = documentType === workerTypes.OFFICE || documentType === workerTypes.LEGACY_OFFICE;\n\n  if (isXod || isOffice || document?.isWebViewerServerDocument()) {\n    return (\n      <div className={'PageControlContainer root small'}>\n        <LeftPanelPageTabsRotate onRotateClockwise={onRotateClockwise} onRotateCounterClockwise={onRotateCounterClockwise} />\n      </div>\n    );\n  }\n\n  const smallBreakPoint = 190;\n  const largeBreakPoint = 290;\n  let widthMinusMargins;\n  if (!isDesktopOnlyMode && isMobile) {\n    try {\n      const appRect = getRootNode().querySelector('.App').getBoundingClientRect();\n      widthMinusMargins = appRect.width - WIDTH_MARGINS;\n    } catch (e) {\n      widthMinusMargins = (panelWidth || panelMinWidth) - WIDTH_MARGINS;\n    }\n  } else {\n    widthMinusMargins = (panelWidth || panelMinWidth) - WIDTH_MARGINS;\n  }\n  const isPanelSmall = widthMinusMargins < smallBreakPoint;\n  const isPanelLarge = widthMinusMargins > largeBreakPoint;\n\n  const childProps = {\n    onReplace,\n    onExtractPages,\n    onDeletePages,\n    onRotateCounterClockwise,\n    onRotateClockwise,\n    onInsert,\n    moveToTop,\n    moveToBottom,\n    pageNumbers,\n  };\n\n  if (isPanelSmall) {\n    return <LeftPanelPageTabsSmall\n      {...childProps}\n      multiPageManipulationControlsItemsSmall={multiPageManipulationControlsSmall}\n    />;\n  }\n\n  if (isPanelLarge) {\n    return <LeftPanelPageTabsLarge\n      {...childProps}\n      multiPageManipulationControlsItems={multiPageManipulationControlsLarge}\n    />;\n  }\n\n\n  return (\n    <LeftPanelPageTabs\n      {...childProps}\n      multiPageManipulationControlsItems={multiPageManipulationControlsItems}\n    />\n  );\n}\n\nexport default LeftPanelPageTabsContainer;\n", "import LeftPanelPageTabsContainer from './LeftPanelPageTabsContainer';\n\nexport default LeftPanelPageTabsContainer;", "import React, { useEffect, useState } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport PropTypes from 'prop-types';\nimport Button from 'components/Button';\nimport getPageArrayFromString from 'helpers/getPageArrayFromString';\nimport selectors from 'selectors';\nimport actions from 'actions';\nimport pageNumberPlaceholder from 'constants/pageNumberPlaceholder';\nimport core from 'src/core';\nimport { useTranslation } from 'react-i18next';\nimport LeftPanelPageTabs from 'components/LeftPanelPageTabs';\nimport './DocumentControls.scss';\n\nfunction getPageString(selectedPageArray, pageLabels) {\n  let pagesToPrint = '';\n  const sortedPages = selectedPageArray.sort((a, b) => a - b);\n  let prevIndex = null;\n\n  for (let i = 0; sortedPages.length > i; i++) {\n    if (sortedPages[i + 1] === sortedPages[i] + 1) {\n      prevIndex = prevIndex !== null ? prevIndex : sortedPages[i];\n    } else if (prevIndex !== null) {\n      pagesToPrint = `${pagesToPrint}${pageLabels[prevIndex]}-${pageLabels[sortedPages[i]]}, `;\n      prevIndex = null;\n    } else {\n      pagesToPrint = `${pagesToPrint}${pageLabels[sortedPages[i]]}, `;\n    }\n  }\n\n  return pagesToPrint.slice(0, -2);\n}\n\nconst DocumentControls = ({ shouldShowControls, parentElement }) => {\n  const [t] = useTranslation();\n  const dispatch = useDispatch();\n\n  const [\n    selectedPageIndexes,\n    isDisabled,\n    pageLabels,\n    isThumbnailSelectingPages,\n    featureFlags,\n  ] = useSelector((state) => [\n    selectors.getSelectedThumbnailPageIndexes(state),\n    selectors.isElementDisabled(state, 'documentControl'),\n    selectors.getPageLabels(state),\n    selectors.isThumbnailSelectingPages(state),\n    selectors.getFeatureFlags(state),\n  ]);\n\n  const initialPagesString = getPageString(selectedPageIndexes, pageLabels);\n\n  const [pageString, setPageString] = useState(initialPagesString);\n  const [previousPageString, setPreviousPageString] = useState(initialPagesString);\n  const customizableUI = featureFlags.customizableUI;\n\n  useEffect(() => {\n    setPageString(getPageString(selectedPageIndexes, pageLabels));\n  }, [setPageString, selectedPageIndexes, shouldShowControls, pageLabels]);\n\n  const onBlur = (e) => {\n    const selectedPagesString = e.target.value.replace(/ /g, '');\n    const pages = !selectedPagesString ? [] : getPageArrayFromString(selectedPagesString, pageLabels);\n    const pageIndexes = pages.map((page) => page - 1);\n\n    if (pages.length || !selectedPagesString) {\n      dispatch(actions.setSelectedPageThumbnails(pageIndexes));\n\n      const updatedString = getPageString(selectedPageIndexes, pageLabels);\n\n      setPageString(updatedString);\n      setPreviousPageString(updatedString);\n    } else {\n      setPageString(previousPageString);\n    }\n\n    if (selectedPageIndexes.length > 0 && !isThumbnailSelectingPages) {\n      // set a short timeout due to race condition caused by onBlur and\n      // changing the documentControlsButton based on isThumbnailSelectingPages\n      setTimeout(() => {\n        enableThumbnailSelectingPages();\n      }, 100);\n    }\n  };\n\n  const pageStringUpdate = (e) => {\n    setPageString(e.target.value);\n  };\n\n  const disableThumbnailSelectingPages = () => {\n    dispatch(actions.setSelectedPageThumbnails([core.getCurrentPage() - 1]));\n    dispatch(actions.setThumbnailSelectingPages(false));\n  };\n\n  const enableThumbnailSelectingPages = () => {\n    dispatch(actions.setThumbnailSelectingPages(true));\n  };\n\n  return isDisabled ? null : (\n    <div className={'documentControlsContainer'} data-element={'documentControl'}>\n      {shouldShowControls ? (\n        <div className={'documentControls'}>\n          <div className={'divider'}></div>\n          {isThumbnailSelectingPages && <LeftPanelPageTabs parentElement={parentElement} />}\n          {customizableUI &&\n            <label className={'documentControlsLabel'} htmlFor=\"pageNumbersInput\">\n              <span>\n                {t('option.thumbnailPanel.multiSelectPages')} -\n              </span>\n              <span className='multiSelectExampleLabel'>\n                {t('option.thumbnailPanel.multiSelectPagesExample')}\n              </span>\n            </label>\n          }\n          <div className={'documentControlsInput'}>\n            <input\n              name=\"pageNumbersInput\"\n              onBlur={onBlur}\n              onChange={pageStringUpdate}\n              value={pageString}\n              placeholder={customizableUI ? '' : pageNumberPlaceholder}\n              aria-label={t('option.thumbnailPanel.enterPageNumbers')}\n              className=\"pagesInput\"\n              type=\"text\"\n            />\n            <div className={'documentControlsButton'}>\n              {!isThumbnailSelectingPages ? (\n                <Button\n                  img={'icon-tool-select-pages'}\n                  title={'option.documentControls.selectTooltip'}\n                  onClick={enableThumbnailSelectingPages}\n                  dataElement={'thumbMultiSelect'}\n                />\n              ) : (\n                <Button\n                  img={'icon-close'}\n                  title={'option.documentControls.closeTooltip'}\n                  onClick={disableThumbnailSelectingPages}\n                  dataElement={'thumbCloseMultiSelect'}\n                />\n              )}\n            </div>\n          </div>\n        </div>\n      ) : null}\n    </div>\n  );\n};\n\nDocumentControls.propTypes = {\n  isDisabled: PropTypes.bool,\n  pageLabels: PropTypes.arrayOf(PropTypes.string),\n  toggleDocumentControl: PropTypes.func,\n  shouldShowControls: PropTypes.bool,\n};\n\nexport default DocumentControls;\n", "export default '1, 3, 5-10';", "import DocumentControls from './DocumentControls';\n\nexport default DocumentControls;", "import core from 'core';\nimport actions from 'actions';\nimport extractPagesWithAnnotations from './extractPagesWithAnnotations';\nimport fireEvent from './fireEvent';\nimport Events from 'constants/events';\nimport DataElements from 'src/constants/dataElement';\n\nexport const extractPagesToMerge = (pageNumbers) => {\n  // extract pages and put the data on the iFrame window element for another instance of WebViewer to access\n  window.extractedDataPromise = extractPagesWithAnnotations(pageNumbers);\n  window.pagesExtracted = pageNumbers;\n};\n\nexport const mergeDocument = (srcToMerge, mergeToPage, shouldFireEvent = true) => (dispatch) => {\n  dispatch(actions.openElement(DataElements.LOADING_MODAL));\n\n  return new Promise((resolve, reject) => {\n    core.mergeDocument(srcToMerge, mergeToPage).then((mergeResults) => {\n      dispatch(actions.closeElement(DataElements.LOADING_MODAL));\n      core.setCurrentPage(mergeToPage);\n\n      if (shouldFireEvent) {\n        fireEvent(Events.DOCUMENT_MERGED, mergeResults);\n      }\n\n      resolve(mergeResults);\n    }).catch((err) => {\n      reject(err);\n      dispatch(actions.closeElement(DataElements.LOADING_MODAL));\n    });\n  });\n};\n\nexport const mergeExternalWebViewerDocument = (viewerID, mergeToPage) => (dispatch) => {\n  return new Promise((resolve, reject) => {\n    const otherWebViewerIframe = window.parent.document.querySelector(`#${viewerID}`);\n    if (!otherWebViewerIframe) {\n      console.warn('Could not find other instance of WebViewer');\n      reject();\n    }\n\n    const extractedDataPromise = otherWebViewerIframe.contentWindow.extractedDataPromise;\n    if (!extractedDataPromise) {\n      console.warn('Could not retrieve data from other instance of WebViewer');\n      reject();\n    }\n\n    dispatch(actions.openElement(DataElements.LOADING_MODAL));\n    extractedDataPromise.then((docToMerge) => {\n      dispatch(mergeDocument(docToMerge, mergeToPage, false)).then(({ filename, pages }) => {\n        fireEvent(Events.DOCUMENT_MERGED, { filename, pages: otherWebViewerIframe.contentWindow.pagesExtracted });\n        dispatch(actions.closeElement(DataElements.LOADING_MODAL));\n        resolve({ filename, pages });\n      });\n    }).catch((err) => {\n      dispatch(actions.closeElement(DataElements.LOADING_MODAL));\n      reject(err);\n    });\n  });\n};", "import debounce from 'lodash/debounce';\nimport React, { useEffect, useRef, useState } from 'react';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport { List } from 'react-virtualized';\nimport Measure from 'react-measure';\nimport classNames from 'classnames';\nimport { isIE11 } from 'helpers/device';\n\nimport Thumbnail from 'components/Thumbnail';\nimport DocumentControls from 'components/DocumentControls';\nimport Button from 'components/Button';\nimport Slider from 'components/Slider';\n\nimport core from 'core';\nimport { extractPagesToMerge, mergeDocument, mergeExternalWebViewerDocument } from 'helpers/pageManipulation';\nimport selectors from 'selectors';\nimport actions from 'actions';\nimport Events from 'constants/events';\nimport DataElements from 'constants/dataElement';\nimport { circleRadius } from 'constants/slider';\nimport fireEvent from 'helpers/fireEvent';\n\nimport './ThumbnailsPanel.scss';\nimport getRootNode from 'helpers/getRootNode';\nimport { useTranslation } from 'react-i18next';\n\nconst dataTransferWebViewerFrameKey = 'dataTransferWebViewerFrame';\n\nconst ZOOM_RANGE_MIN = '100';\nconst ZOOM_RANGE_MAX = '1000';\nconst ZOOM_RANGE_STEP = '50';\nconst MAX_COLUMNS = 16;\n\nconst hoverAreaHeight = 25;\n\nconst ThumbnailsPanel = ({ panelSelector, parentDataElement }) => {\n  const [\n    isLeftPanelOpen,\n    isDisabled,\n    totalPages,\n    currentPage,\n    selectedPageIndexes,\n    isThumbnailMergingEnabled,\n    isThumbnailReorderingEnabled,\n    isMultipleViewerMerging,\n    isThumbnailControlDisabled,\n    isThumbnailSliderDisabled,\n    isReaderMode,\n    isDocumentReadOnly,\n    totalPagesFromSecondaryDocumentViewer,\n    activeDocumentViewerKey,\n    isRightClickEnabled,\n    featureFlags,\n  ] = useSelector(\n    (state) => [\n      selectors.isElementOpen(state, 'leftPanel'),\n      selectors.isElementDisabled(state, 'thumbnailsPanel'),\n      selectors.getTotalPages(state),\n      selectors.getCurrentPage(state),\n      selectors.getSelectedThumbnailPageIndexes(state),\n      selectors.getIsThumbnailMergingEnabled(state),\n      selectors.getIsThumbnailReorderingEnabled(state),\n      selectors.getIsMultipleViewerMerging(state),\n      selectors.isElementDisabled(state, 'thumbnailControl'),\n      selectors.isElementDisabled(state, 'thumbnailsSizeSlider'),\n      selectors.isReaderMode(state),\n      selectors.isDocumentReadOnly(state),\n      selectors.getTotalPages(state, 2),\n      selectors.getActiveDocumentViewerKey(state),\n      selectors.openingPageManipulationOverlayByRightClickEnabled(state),\n      selectors.getFeatureFlags(state),\n    ],\n    shallowEqual,\n  );\n\n  const [t] = useTranslation();\n\n  const listRef = useRef();\n  const pendingThumbs = useRef([]);\n  const thumbs = useRef([]);\n  const afterMovePageNumber = useRef(null);\n\n  const [isOfficeEditor, setIsOfficeEditor] = useState(false);\n  const [canLoad, setCanLoad] = useState(true);\n  const [height, setHeight] = useState(0);\n  const [width, setWidth] = useState(0);\n  const [draggingOverPageIndex, setDraggingOverPageIndex] = useState(null);\n  const [isDraggingToPreviousPage, setDraggingToPreviousPage] = useState(false);\n  const [numberOfColumns, setNumberOfColumns] = useState(1);\n  const [isDragging, setIsDragging] = useState(false);\n\n  const [thumbnailSize, setThumbnailSize] = useState(150);\n  const [lastTimeTriggered, setLastTimeTriggered] = useState(0);\n  const [globalIndex, setGlobalIndex] = useState(0);\n  const pageCount = activeDocumentViewerKey === 2 ? totalPagesFromSecondaryDocumentViewer : totalPages;\n  const customizableUI = featureFlags?.customizableUI;\n\n  const dispatch = useDispatch();\n\n  // If memory becomes an issue, change this to use pageNumbers.\n  // Instead of a debounced drawAnnotations function, perhaps use\n  // a function that first checks for the pageNumber in this map\n  // before calling drawAnnotations on a page.\n  let activeThumbRenders = {};\n\n  const getThumbnailSize = (pageWidth, pageHeight) => {\n    let width;\n    let height;\n    let ratio;\n\n    if (pageWidth > pageHeight) {\n      ratio = pageWidth / thumbnailSize;\n      width = thumbnailSize;\n      height = Math.round(pageHeight / ratio);\n    } else {\n      ratio = pageHeight / thumbnailSize;\n      width = Math.round(pageWidth / ratio); // Chrome has trouble displaying borders of non integer width canvases.\n      height = thumbnailSize;\n    }\n\n    return {\n      width,\n      height,\n    };\n  };\n\n  const updateAnnotations = (pageIndex) => {\n    const thumbContainer = thumbs.current && thumbs.current[pageIndex] && thumbs.current[pageIndex].element;\n    if (!thumbContainer) {\n      return;\n    }\n\n    const pageNumber = pageIndex + 1;\n    const pageWidth = core.getPageWidth(pageNumber);\n    const pageHeight = core.getPageHeight(pageNumber);\n\n    const { width, height } = getThumbnailSize(pageWidth, pageHeight);\n\n    const annotCanvas = thumbContainer.querySelector('.annotation-image') || document.createElement('canvas');\n    annotCanvas.className = 'annotation-image';\n    annotCanvas.role = 'img';\n    annotCanvas.ariaLabel = `${t('action.page')} ${pageNumber}`;\n    annotCanvas.style.maxWidth = `${thumbnailSize}px`;\n    annotCanvas.style.maxHeight = `${thumbnailSize}px`;\n    const ctx = annotCanvas.getContext('2d');\n\n    let zoom = 1;\n    let rotation = core.getCompleteRotation(pageNumber);\n    if (rotation < 0) {\n      rotation += 4;\n    }\n    const multiplier = window.Core.getCanvasMultiplier();\n\n    if (rotation % 2 === 0) {\n      annotCanvas.width = width;\n      annotCanvas.height = height;\n      zoom = annotCanvas.width / pageWidth;\n      zoom /= multiplier;\n    } else {\n      annotCanvas.width = height;\n      annotCanvas.height = width;\n\n      zoom = annotCanvas.height / pageWidth;\n      zoom /= multiplier;\n    }\n\n    thumbContainer.appendChild(annotCanvas);\n    core.setAnnotationCanvasTransform(ctx, zoom, rotation);\n\n    let options = {\n      pageNumber,\n      overrideCanvas: annotCanvas,\n    };\n\n    const thumb = thumbContainer.querySelector('.page-image');\n\n    if (thumb) {\n      options = {\n        ...options,\n        overridePageRotation: rotation,\n        overridePageCanvas: thumb,\n      };\n    } else {\n      return;\n    }\n\n    if (!activeThumbRenders[pageNumber]) {\n      activeThumbRenders[pageNumber] = debounce(core.drawAnnotations, 112);\n    }\n    const debouncedDraw = activeThumbRenders[pageNumber];\n    debouncedDraw(options);\n  };\n\n  useEffect(() => {\n    const onBeginRendering = () => {\n      setCanLoad(false);\n    };\n\n    const onFinishedRendering = (needsMoreRendering) => {\n      if (!needsMoreRendering) {\n        setCanLoad(true);\n      }\n    };\n\n    const onDocumentLoaded = () => {\n      if (core.getDocument()?.getType() === 'officeEditor') {\n        setIsOfficeEditor(true);\n      } else {\n        setIsOfficeEditor(false);\n      }\n      activeThumbRenders = {};\n      dispatch(actions.setSelectedPageThumbnails([]));\n    };\n\n    const onPageComplete = () => {\n      if (afterMovePageNumber.current) {\n        core.setCurrentPage(afterMovePageNumber.current);\n        afterMovePageNumber.current = null;\n      }\n    };\n\n    core.addEventListener('beginRendering', onBeginRendering);\n    core.addEventListener('finishedRendering', onFinishedRendering);\n    core.addEventListener('documentLoaded', onDocumentLoaded);\n    core.addEventListener('pageComplete', onPageComplete);\n\n\n    // The document might have already been loaded before this component is mounted.\n    // If document is already loaded, call 'onDocumentLoaded()' manually to update the state properly.\n    if (core.getDocument()) {\n      onDocumentLoaded();\n    }\n\n    return () => {\n      core.removeEventListener('beginRendering', onBeginRendering);\n      core.removeEventListener('finishedRendering', onFinishedRendering);\n      core.removeEventListener('documentLoaded', onDocumentLoaded);\n      core.removeEventListener('pageComplete', onPageComplete);\n    };\n  }, []);\n\n  useEffect(() => {\n    const onPagesUpdated = (changes) => {\n      if (!changes) {\n        return;\n      }\n      let updatedPagesIndexes = Array.from(selectedPageIndexes);\n\n      if (changes.removed) {\n        updatedPagesIndexes = updatedPagesIndexes.filter((pageIndex) => changes.removed.indexOf(pageIndex + 1) === -1);\n      }\n\n      if (changes.moved) {\n        updatedPagesIndexes = updatedPagesIndexes.map((pageIndex) => (changes.moved[pageIndex + 1] ? changes.moved[pageIndex + 1] - 1 : pageIndex),\n        );\n      }\n\n      const isPageAddedBefore = changes.added && (changes.added[0] - 1) <= updatedPagesIndexes[0];\n      if (updatedPagesIndexes.length === 1 && isPageAddedBefore) {\n        updatedPagesIndexes = changes.added.map((pageNumber) => pageNumber - 1);\n      }\n\n      dispatch(actions.setSelectedPageThumbnails(updatedPagesIndexes));\n    };\n\n    core.addEventListener('pagesUpdated', onPagesUpdated);\n\n    return () => core.removeEventListener('pagesUpdated', onPagesUpdated);\n  }, [selectedPageIndexes]);\n\n  useEffect(() => {\n    listRef.current?.scrollToRow(Math.floor((currentPage - 1) / numberOfColumns));\n    const onAnnotationChanged = (annots) => {\n      const indices = [];\n\n      annots.forEach((annot) => {\n        const pageIndex = annot.PageNumber - 1;\n        if (!annot.Listable || indices.indexOf(pageIndex) > -1) {\n          return;\n        }\n        indices.push(pageIndex);\n\n        updateAnnotations(pageIndex);\n      });\n    };\n\n    const onPageNumberUpdated = (pageNumber) => {\n      const pageIndex = pageNumber - 1;\n      listRef.current?.scrollToRow(Math.floor(pageIndex / numberOfColumns));\n    };\n\n    core.addEventListener('pageNumberUpdated', onPageNumberUpdated);\n    core.addEventListener('annotationChanged', onAnnotationChanged);\n    core.addEventListener('annotationHidden', onAnnotationChanged);\n\n    return () => {\n      core.removeEventListener('pageNumberUpdated', onPageNumberUpdated);\n      core.removeEventListener('annotationChanged', onAnnotationChanged);\n      core.removeEventListener('annotationHidden', onAnnotationChanged);\n    };\n  }, [thumbnailSize, numberOfColumns]);\n\n  useEffect(() => {\n    if (isReaderMode || isDocumentReadOnly) {\n      dispatch(actions.setSelectedPageThumbnails([]));\n      dispatch(actions.setThumbnailSelectingPages(false));\n    }\n  }, [isReaderMode, isDocumentReadOnly]);\n\n  // if disabled, or is office editor or left panel is not open when we are not in customize mode, return\n  if (isDisabled || isOfficeEditor || (!isLeftPanelOpen && !panelSelector && !customizableUI)) {\n    return null;\n  }\n  const onDragEnd = () => {\n    setIsDragging(false);\n    setDraggingOverPageIndex(null);\n  };\n\n  const scrollToRowHelper = (index, change, time) => {\n    const now = new Date().getTime();\n    if (index < pageCount - 1 && index > 0 && now - lastTimeTriggered >= time) {\n      listRef.current?.scrollToRow(Math.floor((index + change) / numberOfColumns));\n      setLastTimeTriggered(now);\n      return index + change;\n    }\n    return index;\n  };\n\n  const onDragOver = (e, index) => {\n    // 'preventDefault' to prevent opening pdf dropped in and 'stopPropagation' to keep parent from opening pdf\n    e.preventDefault();\n    e.stopPropagation();\n    if (!isThumbnailReorderingEnabled && !isThumbnailMergingEnabled) {\n      return;\n    }\n\n    const thumbnail = e.target.getBoundingClientRect();\n    if (numberOfColumns > 1) {\n      // row with more than 1 thumbnail so user are dragging to the left and right\n      setDraggingToPreviousPage(!(e.pageX > thumbnail.x + thumbnail.width / 2));\n    } else {\n      setDraggingToPreviousPage(!(e.pageY > thumbnail.y + thumbnail.height / 2));\n    }\n\n    setDraggingOverPageIndex(index);\n    const virtualizedThumbnailContainerElement = getRootNode().querySelector('#virtualized-thumbnails-container');\n    const { y, bottom } = virtualizedThumbnailContainerElement.getBoundingClientRect();\n\n    if (e.pageY < y + hoverAreaHeight * 4) {\n      setGlobalIndex(scrollToRowHelper(index, -1, 400));\n    } else if (e.pageY > bottom - hoverAreaHeight * 4) {\n      setGlobalIndex(scrollToRowHelper(index, 1, 400));\n    }\n  };\n\n  const scrollDown = () => {\n    setGlobalIndex(scrollToRowHelper(globalIndex, 1, 200));\n  };\n  const scrollUp = () => {\n    setGlobalIndex(scrollToRowHelper(globalIndex, -1, 200));\n  };\n\n  const onDragStart = (e, index) => {\n    setGlobalIndex(index);\n    setIsDragging(true);\n    const draggingSelectedPage = selectedPageIndexes.some((i) => i === index);\n    const pagesToMove = draggingSelectedPage ? selectedPageIndexes.map((index) => index + 1) : [index + 1];\n    fireEvent(Events.THUMBNAIL_DRAGGED);\n    // need to set 'text' to empty for drag to work in FireFox and mobile\n    e.dataTransfer.setData('text', '');\n\n    if (pagesToMove.length > 1) {\n      // can't set to null so set to new instance of an image\n      e.dataTransfer.setDragImage(new Image(), 0, 0);\n    }\n\n    if (isThumbnailMergingEnabled && isMultipleViewerMerging) {\n      e.dataTransfer.dropEffect = 'move';\n      e.dataTransfer.effectAllowed = 'all';\n      e.dataTransfer.setData(dataTransferWebViewerFrameKey, window.frameElement.id);\n      extractPagesToMerge(pagesToMove);\n    }\n\n    if (!draggingSelectedPage) {\n      dispatch(actions.setSelectedPageThumbnails([index]));\n    }\n\n    core.setCurrentPage(index + 1);\n  };\n\n  const onDrop = (e) => {\n    e.preventDefault();\n    const { files } = e.dataTransfer;\n    const insertTo = isDraggingToPreviousPage ? draggingOverPageIndex + 1 : draggingOverPageIndex + 2;\n    let externalPageWebViewerFrameId;\n    if (!isIE11) {\n      // at this time of writing, IE11 does not really have support for getData\n      externalPageWebViewerFrameId = e.dataTransfer.getData(dataTransferWebViewerFrameKey);\n    }\n    const mergingDocument =\n      (externalPageWebViewerFrameId && window.frameElement.id !== externalPageWebViewerFrameId) || files.length;\n    const currentPageIndex = currentPage - 1;\n\n    if (isThumbnailMergingEnabled && mergingDocument) {\n      if (externalPageWebViewerFrameId && window.frameElement.id !== externalPageWebViewerFrameId) {\n        dispatch(mergeExternalWebViewerDocument(externalPageWebViewerFrameId, insertTo));\n      } else if (files.length) {\n        Array.from(files).forEach((file) => {\n          dispatch(mergeDocument(file, insertTo));\n        });\n      }\n    } else if (isThumbnailReorderingEnabled && !mergingDocument) {\n      if (draggingOverPageIndex !== null) {\n        const targetPageNumber = isDraggingToPreviousPage ? draggingOverPageIndex + 1 : draggingOverPageIndex + 2;\n        const draggingSelectedPage = selectedPageIndexes.some((i) => i === currentPageIndex);\n        const pageNumbersToMove = draggingSelectedPage ? selectedPageIndexes.map((i) => i + 1) : [currentPage];\n        afterMovePageNumber.current = targetPageNumber - pageNumbersToMove.filter((p) => p < targetPageNumber).length;\n        core.movePages(pageNumbersToMove, targetPageNumber);\n        const updatedPagesNumbers = [];\n        for (let offset = 0; offset < pageNumbersToMove.length; offset++) {\n          updatedPagesNumbers.push(afterMovePageNumber.current + offset);\n        }\n        fireEvent(Events.THUMBNAIL_DROPPED, { pageNumbersBeforeMove: pageNumbersToMove, pagesNumbersAfterMove: updatedPagesNumbers, numberOfPagesMoved: updatedPagesNumbers.length });\n      }\n    }\n    setDraggingOverPageIndex(null);\n    setIsDragging(false);\n  };\n\n  const onLoad = (pageIndex, element, id) => {\n    if (!thumbIsLoaded(pageIndex) && !thumbIsPending(pageIndex)) {\n      thumbs.current[pageIndex] = {\n        element,\n        loaded: false,\n      };\n\n      pendingThumbs.current.push({\n        pageIndex,\n        id,\n      });\n    }\n  };\n\n  const removeFromPendingThumbs = (pageIndex) => {\n    const index = getPendingThumbIndex(pageIndex);\n    if (index !== -1) {\n      pendingThumbs.current.splice(index, 1);\n    }\n  };\n\n  const thumbIsLoaded = (pageIndex) => thumbs.current[pageIndex]?.loaded;\n\n  const thumbIsPending = (pageIndex) => getPendingThumbIndex(pageIndex) !== -1;\n\n  const onCancel = (pageIndex) => {\n    const index = getPendingThumbIndex(pageIndex);\n    if (index !== -1) {\n      core.cancelLoadThumbnail(pendingThumbs.current[index].id);\n      pendingThumbs.current.splice(index, 1);\n    }\n  };\n\n  const onRightClick = (event, pageIndex) => {\n    event.preventDefault();\n    core.setCurrentPage(pageIndex + 1);\n    if (!selectedPageIndexes.includes(pageIndex)) {\n      dispatch(actions.setSelectedPageThumbnails([pageIndex]));\n    }\n\n    if (isReaderMode || isDocumentReadOnly) {\n      return;\n    }\n\n    dispatch(actions.setPageManipulationOverlayAlternativePosition({ left: event.pageX, right: 'auto', top: event.pageY }));\n    dispatch(actions.openElements([DataElements.PAGE_MANIPULATION_OVERLAY]));\n  };\n\n  const getPendingThumbIndex = (pageIndex) => pendingThumbs.current.findIndex((thumbStatus) => thumbStatus.pageIndex === pageIndex);\n\n  const onRemove = (pageIndex) => {\n    onCancel(pageIndex);\n    const canvases = thumbs.current[pageIndex]?.element?.querySelectorAll('canvas');\n    if (canvases?.length) {\n      canvases.forEach((c) => {\n        c.height = 0;\n        c.width = 0;\n      });\n    }\n\n    if (activeThumbRenders[pageIndex]) {\n      activeThumbRenders[pageIndex].cancel();\n    }\n    thumbs.current[pageIndex] = null;\n  };\n\n  const renderThumbnails = ({ index, key, style }) => {\n    const className = classNames({\n      columnsOfThumbnails: numberOfColumns > 1,\n      row: true,\n    });\n    const allowPageOperationsUI = !(isReaderMode || isDocumentReadOnly);\n\n    return (\n      <div role=\"row\" aria-label=\"row\" className={className} key={key} style={style}>\n        {new Array(numberOfColumns).fill().map((_, columnIndex) => {\n          const thumbIndex = index * numberOfColumns + columnIndex;\n          const allowDragAndDrop = allowPageOperationsUI && (isThumbnailMergingEnabled || isThumbnailReorderingEnabled);\n          const showPlaceHolder = allowDragAndDrop && draggingOverPageIndex === thumbIndex;\n\n          return thumbIndex < pageCount ? (\n            <React.Fragment key={thumbIndex}>\n              {(numberOfColumns > 1 || thumbIndex === 0) && showPlaceHolder && isDraggingToPreviousPage && <div key={`placeholder1-${thumbIndex}`} className=\"thumbnailPlaceholder\" />}\n              <div key={thumbIndex} role=\"cell\" onDragEnd={onDragEnd} className=\"cellThumbContainer\" onContextMenu={(e) => isRightClickEnabled && onRightClick(e, thumbIndex)}>\n                <Thumbnail\n                  isDraggable={allowDragAndDrop}\n                  isSelected={selectedPageIndexes.includes(thumbIndex)}\n                  index={thumbIndex}\n                  canLoad={canLoad}\n                  onLoad={onLoad}\n                  onCancel={onCancel}\n                  onRemove={onRemove}\n                  onDragStart={onDragStart}\n                  onDragOver={onDragOver}\n                  onFinishLoading={removeFromPendingThumbs}\n                  updateAnnotations={updateAnnotations}\n                  shouldShowControls={allowPageOperationsUI}\n                  thumbnailSize={thumbnailSize}\n                  panelSelector={panelSelector}\n                />\n              </div>\n              {showPlaceHolder && !isDraggingToPreviousPage && <div key={`placeholder2-${thumbIndex}`} className=\"thumbnailPlaceholder\" />}\n            </React.Fragment>\n          ) : null;\n        })}\n      </div>\n    );\n  };\n\n  const onPanelResize = ({ bounds }) => {\n    setHeight(bounds.height);\n    setWidth(bounds.width);\n    setNumberOfColumns(Math.min(MAX_COLUMNS, Math.max(1, Math.floor(bounds.width / thumbnailSize))));\n  };\n\n  const updateNumberOfColumns = () => {\n    setNumberOfColumns(Math.min(MAX_COLUMNS, Math.max(1, Math.floor(width / thumbnailSize))));\n  };\n\n  const thumbnailHeight = isThumbnailControlDisabled ? Number(thumbnailSize) + 50 : Number(thumbnailSize) + 80;\n  const shouldShowControls = !(isReaderMode || isDocumentReadOnly);\n  const thumbnailAutoScrollAreaStyle = {\n    'height': `${hoverAreaHeight}px`,\n  };\n  const lineStart = circleRadius;\n\n  const onSliderChange = (property, value) => {\n    let zoomValue = Number(value) * ZOOM_RANGE_MAX;\n    if (zoomValue < 100) {\n      zoomValue = 100;\n    }\n    setThumbnailSize(zoomValue);\n    updateNumberOfColumns();\n  };\n\n  return (\n    <React.Fragment>\n      {!isThumbnailSliderDisabled && <div data-element=\"thumbnailsSizeSlider\" className=\"thumbnail-slider-container\">\n        <Button\n          img=\"icon-zoom-thumb-out\"\n          title=\"action.zoomOut\"\n          hideTooltipShortcut\n          onClick={() => {\n            if (thumbnailSize - ZOOM_RANGE_STEP > ZOOM_RANGE_STEP) {\n              setThumbnailSize(thumbnailSize - ZOOM_RANGE_STEP);\n              updateNumberOfColumns();\n            }\n          }}\n          dataElement=\"zoomThumbOutButton\"\n        />\n        {customizableUI &&\n          <Slider\n            dataElement={'thumbnailsSizeSlider'}\n            property={'zoom'}\n            displayProperty={'zoom'}\n            min={Number(ZOOM_RANGE_MIN)}\n            max={Number(ZOOM_RANGE_MAX)}\n            value={thumbnailSize}\n            getDisplayValue={() => thumbnailSize}\n            customCircleRadius={8}\n            customLineStrokeWidth={4}\n            getCirclePosition={(lineLength, zoom) => {\n              if (zoom > 1) {\n                zoom /= 1000;\n              }\n              return zoom * lineLength + lineStart;\n            }\n            }\n            convertRelativeCirclePositionToValue={(circlePosition) => circlePosition}\n            onSliderChange={onSliderChange}\n            onStyleChange={onSliderChange}\n            step={Number(ZOOM_RANGE_STEP)}\n            shouldHideSliderTitle={true}\n            shouldHideSliderValue={true}\n          />\n        }\n        {!customizableUI &&\n          <input\n            role='slider'\n            type=\"range\"\n            aria-label='thumbnail size slider'\n            min={ZOOM_RANGE_MIN}\n            max={ZOOM_RANGE_MAX}\n            value={thumbnailSize}\n            aria-valuemin={ZOOM_RANGE_MIN}\n            aria-valuemax={ZOOM_RANGE_MAX}\n            aria-valuenow={thumbnailSize}\n            onChange={(e) => {\n              setThumbnailSize(Number(e.target.value));\n              updateNumberOfColumns();\n            }}\n            step={ZOOM_RANGE_STEP}\n            className=\"thumbnail-slider\"\n            id=\"thumbnailSize\"\n          />\n        }\n        <Button\n          img=\"icon-zoom-thumb-in\"\n          title=\"action.zoomIn\"\n          hideTooltipShortcut\n          onClick={() => {\n            if (thumbnailSize + Number(ZOOM_RANGE_STEP) < 1001) {\n              setThumbnailSize(thumbnailSize + Number(ZOOM_RANGE_STEP));\n              updateNumberOfColumns();\n            }\n          }}\n          dataElement=\"zoomThumbInButton\"\n        />\n      </div>}\n      <Measure bounds onResize={onPanelResize} key={thumbnailSize}>\n        {({ measureRef }) => (\n          <div className={`Panel ThumbnailsPanel ${panelSelector}`} id=\"virtualized-thumbnails-container\" data-element=\"thumbnailsPanel\" onDrop={onDrop} ref={measureRef}>\n            <div className=\"virtualized-thumbnails-container\">\n              {isDragging ?\n                <div className=\"thumbnailAutoScrollArea\" onDragOver={scrollUp} style={thumbnailAutoScrollAreaStyle}></div> : ''\n              }\n              <List\n                ref={listRef}\n                height={height}\n                width={width}\n                rowHeight={thumbnailHeight}\n                // Round it to a whole number because React-Virtualized list library doesn't round it for us and throws errors when rendering non whole number rows\n                // use ceiling rather than floor so that an extra row can be created in case the items can't be evenly distributed between rows\n                rowCount={Math.ceil(pageCount / numberOfColumns)}\n                rowRenderer={renderThumbnails}\n                overscanRowCount={3}\n                className={'thumbnailsList'}\n                style={{ outline: 'none' }}\n                // Ensure we show the current page in the thumbnails when we open the panel\n                scrollToIndex={Math.floor((currentPage - 1) / numberOfColumns)}\n                role='grid'\n                aria-label={t('component.thumbnailsPanel')}\n              />\n              {isDragging ?\n                <div className=\"thumbnailAutoScrollArea\" onDragOver={scrollDown} style={{ ...thumbnailAutoScrollAreaStyle, 'bottom': '70px' }}></div> : ''\n              }\n            </div>\n          </div>\n        )}\n      </Measure>\n      <DocumentControls shouldShowControls={shouldShowControls} parentElement={parentDataElement || panelSelector} />\n    </React.Fragment>\n  );\n};\n\nexport default ThumbnailsPanel;\n", "import ThumbnailsPanel from './ThumbnailsPanel';\n\nexport default ThumbnailsPanel;\n", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../../node_modules/css-loader/dist/runtime/api.js\");\nexports = ___CSS_LOADER_API_IMPORT___(false);\n// Module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .documentControlsContainer{margin-left:16px;margin-right:16px;margin-bottom:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .documentControlsContainer{margin-left:16px;margin-right:16px;margin-bottom:16px}}.documentControlsContainer .PageControlContainer{display:flex;background-color:var(--gray-2);justify-content:center;align-content:center;border-radius:4px}.documentControlsContainer .PageControlContainer .dropdown-menu{position:relative}.documentControlsContainer .PageControlContainer .dropdown-menu .indicator{position:absolute;bottom:1px;right:1px;width:0;height:0;border-left:4px solid transparent;border-right:4px solid transparent;border-top:4px solid #c4c4c4;transform:rotateY(0deg) rotate(315deg)}.documentControlsContainer .PageControlContainer button .Icon{height:21px;width:21px;color:var(--icon-color)}.documentControlsContainer .PageControlContainer .button-hover:hover{background:var(--view-header-button-hover);border-radius:4px}.documentControlsContainer .PageControlContainer .divider{height:20px;width:1px;background:var(--divider);margin:6px}\", \"\"]);\n// Exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};\nmodule.exports = exports;\n", "var api = require(\"!../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../node_modules/css-loader/dist/cjs.js!../../../node_modules/postcss-loader/src/index.js??ruleSet[1].rules[1].use[2]!../../../node_modules/sass-loader/dist/cjs.js!./ThumbnailsPanel.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "var api = require(\"!../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../node_modules/css-loader/dist/cjs.js!../../../node_modules/postcss-loader/src/index.js??ruleSet[1].rules[1].use[2]!../../../node_modules/sass-loader/dist/cjs.js!./ThumbnailControls.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../node_modules/css-loader/dist/runtime/api.js\");\nexports = ___CSS_LOADER_API_IMPORT___(false);\n// Module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.thumbnailControls-overlay{display:grid;text-align:center;z-index:2;margin-top:5px;grid-template-areas:\\\"rotate delete . more\\\";grid-template-columns:repeat(3,1fr)}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.thumbnailControls-overlay{display:flex}}.thumbnailControls-overlay .Button{height:32px;padding:0;width:32px}.thumbnailControls-overlay .Button .Icon{height:24px;width:24px;color:var(--icon-color)}.thumbnailControls-overlay .Button:hover{background:var(--view-header-button-hover);border-radius:4px}.thumbnailControls-overlay .Button.active{background:var(--view-header-button-active)}.thumbnailControls-overlay .Button.active .Icon{color:var(--selected-icon-color)}.thumbnailControls-overlay.modular-ui .Button:hover{border:1px solid var(--focus-border);background:var(--tools-button-hover)}.thumbnailControls-overlay .rotate-button{grid-area:rotate}.thumbnailControls-overlay .delete-button{grid-area:delete}.thumbnailControls-overlay .more-options{grid-area:more}.thumbnailControls-overlay.custom-buttons .Button{grid-area:auto}.thumbnailControls{display:flex;flex-direction:row;text-align:center;z-index:2;margin-top:5px}.thumbnailControls .Button{height:32px;padding:0;width:32px;margin:0 4px}.thumbnailControls .Button .Icon{height:24px;width:24px;color:var(--icon-color)}.thumbnailControls .Button:hover{background:var(--view-header-button-hover);border-radius:4px}\", \"\"]);\n// Exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};\nmodule.exports = exports;\n", "var api = require(\"!../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../node_modules/css-loader/dist/cjs.js!../../../node_modules/postcss-loader/src/index.js??ruleSet[1].rules[1].use[2]!../../../node_modules/sass-loader/dist/cjs.js!./DocumentControls.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../node_modules/css-loader/dist/runtime/api.js\");\nexports = ___CSS_LOADER_API_IMPORT___(false);\n// Module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.thumbnail-slider-container{display:flex;align-items:center;width:230px;margin:0 auto}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .thumbnail-slider-container{width:inherit;margin:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .thumbnail-slider-container{width:inherit;margin:16px}}.thumbnail-slider-container .thumbnail-slider{width:100%;height:20px;padding:0;color:transparent;background-color:transparent;border:0 transparent}.thumbnail-slider-container input[type=range]{-webkit-appearance:none;margin:10px 0;width:100%}.thumbnail-slider-container input[type=range]:focus{outline:none;box-shadow:0 0 5px var(--focus-border)}.thumbnail-slider-container input[type=range]::-webkit-slider-runnable-track{width:100%;height:3px;cursor:pointer;animate:.2s;box-shadow:0 0 0 var(--gray-12);background:var(--slider-filled);border-radius:5px;border:0 solid var(--gray-12)}.thumbnail-slider-container input[type=range]::-webkit-slider-thumb{box-shadow:0 0 1px var(--gray-12);border:0 solid var(--gray-12);height:15px;width:15px;border-radius:50px;background:var(--slider-filled);cursor:pointer;-webkit-appearance:none;margin-top:-5px}.thumbnail-slider-container input[type=range]:focus::-webkit-slider-runnable-track{background:var(--slider-filled)}.thumbnail-slider-container input[type=range]::-moz-range-track{width:100%;height:3px;cursor:pointer;animate:.2s;box-shadow:0 0 0 var(--gray-12);background:var(--slider-filled);border-radius:5px;border:0 solid var(--gray-12)}.thumbnail-slider-container input[type=range]::-moz-range-thumb{box-shadow:0 0 1px var(--gray-12);border:0 solid var(--gray-12);height:15px;width:15px;border-radius:50px;background:var(--slider-filled);cursor:pointer}.thumbnail-slider-container input[type=range]::-ms-track{padding:5px 0 0;width:100%;height:3px;background:transparent;border-color:transparent;color:transparent;cursor:pointer;border-width:6px 0;animate:.2s}.thumbnail-slider-container input[type=range]::-ms-fill-lower,.thumbnail-slider-container input[type=range]::-ms-fill-upper{background:var(--slider-filled);border:0 solid var(--gray-12);border-radius:10px;box-shadow:0 0 0 var(--gray-12)}.thumbnail-slider-container input[type=range]::-ms-thumb{box-shadow:0 0 1px var(--gray-12);border:0 solid var(--gray-12);height:15px;width:15px;border-radius:50%;background:var(--slider-filled);cursor:pointer}.thumbnail-slider-container input[type=range]:focus::-ms-fill-lower,.thumbnail-slider-container input[type=range]:focus::-ms-fill-upper{background:var(--slider-filled)}.thumbnail-slider-container Button{width:15px;height:15px;margin:2.5px;padding-top:6px}.thumbnail-slider-container Button:hover{background:var(--view-header-button-hover);border-radius:4px}.thumbnail-slider-container .slider{width:100%}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .thumbnail-slider-container .slider{margin-top:0}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .thumbnail-slider-container .slider{margin-top:0}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .thumbnail-slider-container .slider .slider-element-container{width:auto;margin-left:auto}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .thumbnail-slider-container .slider .slider-element-container{width:auto;margin-left:auto}}.ThumbnailsPanel{overflow:hidden!important;display:flex;height:100%}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ThumbnailsPanel{width:inherit;margin:0 16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .ThumbnailsPanel{width:inherit;margin:0 16px}}.ThumbnailsPanel #virtualized-thumbnails-container{flex:1}.ThumbnailsPanel .row{display:flex;justify-content:space-around;align-items:center;flex-direction:column}.ThumbnailsPanel .thumbnailPlaceholder{width:150px;margin:2px;border:1px solid var(--focus-border)}.ThumbnailsPanel .columnsOfThumbnails.row{display:flex;justify-content:left;align-items:center;flex-direction:row}.ThumbnailsPanel .columnsOfThumbnails .cellThumbContainer{display:flex;flex-direction:row}.ThumbnailsPanel .columnsOfThumbnails .Thumbnail{display:inline-flex}.ThumbnailsPanel .columnsOfThumbnails .thumbnailPlaceholder{width:116px;min-width:116px;height:150px;margin-bottom:30px}.thumbnailAutoScrollArea{position:absolute;width:calc(100% - 55px);z-index:10;background:hsla(0,0%,100%,0)}\", \"\"]);\n// Exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};\nmodule.exports = exports;\n", "var api = require(\"!../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../node_modules/css-loader/dist/cjs.js!../../../../node_modules/postcss-loader/src/index.js??ruleSet[1].rules[1].use[2]!../../../../node_modules/sass-loader/dist/cjs.js!./LeftPanelPageTabsContainer.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../node_modules/css-loader/dist/runtime/api.js\");\nexports = ___CSS_LOADER_API_IMPORT___(false);\n// Module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.documentControls{display:flex;flex-direction:column}.documentControls .divider{height:1px;background:var(--divider);margin:16px 0 8px}.documentControls .documentControlsInput{display:flex;flex-direction:row;padding-bottom:16px;padding-top:8px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .documentControls .documentControlsInput{padding-bottom:0}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .documentControls .documentControlsInput{padding-bottom:0}}.documentControls .documentControlsInput.customizableUI{padding:8px 0}.documentControls .documentControlsInput .pagesInput{width:100%;height:30px;border:1px solid var(--border);border-radius:4px;color:var(--text-color);padding:4px 8px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .documentControls .documentControlsInput .pagesInput{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .documentControls .documentControlsInput .pagesInput{font-size:13px}}.documentControls .documentControlsInput .pagesInput:focus{outline:none;border:1px solid var(--focus-border)}.documentControls .documentControlsInput .pagesInput::-moz-placeholder{color:var(--placeholder-text)}.documentControls .documentControlsInput .pagesInput::placeholder{color:var(--placeholder-text)}.documentControls .documentControlsInput .documentControlsButton{display:flex;flex-direction:row;padding-left:2px}.documentControls .documentControlsInput .documentControlsButton .Button{height:30px;padding:0;width:30px;margin:0 4px}.documentControls .documentControlsInput .documentControlsButton .Button .Icon{height:24px;width:24px;color:var(--icon-color)}.documentControls .documentControlsInput .documentControlsButton .Button:hover{background:var(--view-header-button-hover);border-radius:4px}.documentControls .documentControlsLabel{margin-top:16px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .documentControls .documentControlsLabel{margin-top:8px;font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .documentControls .documentControlsLabel{margin-top:8px;font-size:13px}}.documentControls .documentControlsLabel .multiSelectExampleLabel{color:var(--faded-text);margin-left:2px}\", \"\"]);\n// Exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};\nmodule.exports = exports;\n", "var api = require(\"!../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../node_modules/css-loader/dist/cjs.js!../../../node_modules/postcss-loader/src/index.js??ruleSet[1].rules[1].use[2]!../../../node_modules/sass-loader/dist/cjs.js!./Thumbnail.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../node_modules/css-loader/dist/runtime/api.js\");\nexports = ___CSS_LOADER_API_IMPORT___(false);\n// Module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.Thumbnail{display:flex;flex-direction:column;justify-content:center;align-items:center;text-align:center;cursor:pointer}.Thumbnail.active .container .page-image{border:2px solid var(--focus-border);box-shadow:none;box-sizing:content-box}.Thumbnail .container{position:relative;display:flex;justify-content:center;align-items:center;cursor:pointer}.Thumbnail .container .page-image{box-shadow:0 0 3px 0 var(--box-shadow)}.Thumbnail .container .annotation-image,.Thumbnail .container .page-image{position:absolute;left:50%;top:50%;transform:translate(-50%,-50%)}.Thumbnail .container .checkbox{position:absolute;border-radius:4px;z-index:4}.Thumbnail .container .default{top:3%;right:15%}.Thumbnail .container .rotated{top:15%;right:3%}.Thumbnail .page-label{margin-top:11px}.Thumbnail.selected .container .thumbnail:before{color:#fff;background:var(--focus-border);width:16px;height:16px;position:absolute;z-index:10}.Thumbnail.selected .container canvas{background:hsla(0,0%,100%,.6)}.Thumbnail.active .page-label{color:var(--focus-border)!important}\", \"\"]);\n// Exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};\nmodule.exports = exports;\n"], "names": ["props", "className", "pageIndex", "dispatch", "useDispatch", "selectedPageIndexes", "useSelector", "state", "selectors", "getSelectedThumbnailPageIndexes", "React", "onClick", "indexOf", "actions", "setSelectedPageThumbnails", "ToggleElementButton", "dataElement", "DataElements", "PAGE_MANIPULATION_OVERLAY_BUTTON", "element", "PAGE_MANIPULATION_OVERLAY", "img", "title", "propTypes", "index", "PropTypes", "isRequired", "dataElementName", "ThumbnailControls", "_ref", "t", "useTranslation", "isElementDisabled", "_slicedToArray", "isMoreOptionDisabled", "_useSelector6", "pageDeletionConfirmationModalEnabled", "isPageDeletionConfirmationModalEnabled", "selectedIndexes", "_useSelector8", "getCurrentPage", "getThumbnailControlMenuItems", "getFeatureFlags", "shallowEqual", "currentPage", "pageThumbnailControlMenuItems", "featureFlags", "pageNumbers", "length", "map", "i", "isCurrentPageInTheSelection", "includes", "customizableUI", "document", "core", "getDocument", "documentType", "type", "isXod", "workerTypes", "XOD", "isOffice", "OFFICE", "LEGACY_OFFICE", "BUTTONS_MAP", "<PERSON><PERSON>", "rotateClockwise", "rotateCounterClockwise", "deletePages", "onClickAnnouncement", "concat", "isCustomized", "occurredButtons", "buttons", "item", "key", "component", "push", "isWebViewerServerDocument", "style", "display", "classNames", "PageManipulationOverlayButton", "_regeneratorRuntime", "e", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "_typeof", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "resetTryEntry", "completion", "reset", "isNaN", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "Array", "isArray", "_arrayWithHoles", "_iterableToArrayLimit", "_unsupportedIterableToArray", "_nonIterableRest", "_arrayLikeToArray", "toString", "from", "test", "isSelected", "updateAnnotations", "shiftKeyThumbnailPivotIndex", "onFinishLoading", "onLoad", "_ref$onRemove", "onRemove", "onDragStart", "onDragOver", "isDraggable", "shouldShowControls", "thumbnailSize", "_ref$pageLabels", "pageLabe<PERSON>", "isThumbnailMultiselectEnabled", "isReaderModeOrReadOnly", "isMobile", "canLoad", "onCancel", "isThumbnailSelectingPages", "thumbnailSelectionMode", "activeDocumentViewerKey", "panelSelector", "thumbSize", "Number", "_useState2", "useState", "width", "height", "dimensions", "setDimensions", "_useState4", "loaded", "setLoaded", "loadTimeout", "loadThumbnailAsync", "setTimeout", "_drawComplete", "thumbnail<PERSON><PERSON><PERSON>", "getRootNode", "querySelector", "pageNum", "viewerRotation", "getRotation", "doc", "getPageInfo", "id", "loadCanvas", "pageNumber", "drawComplete", "_callee", "thumb", "childElement", "ratio", "cssTransform", "cssTransformOrigin", "_context", "<PERSON><PERSON><PERSON><PERSON>", "Math", "min", "abs", "append<PERSON><PERSON><PERSON>", "arguments", "apply", "_next", "_throw", "_x", "allowUseOfOptimizedThumbnail", "useEffect", "onPagesUpdated", "changes", "contentChanged", "moved", "added", "removed", "isPageAdded", "didPageChange", "some", "changedPage", "didPageMove", "movedPage", "parseInt", "isPageRemoved", "newPageCount", "getTotalPages", "onRotationUpdated", "addEventListener", "removeEventListener", "clearTimeout", "useDidUpdate", "isActive", "pageLabel", "checkboxRotateClass", "rotation", "<PERSON><PERSON><PERSON><PERSON>", "active", "selected", "draggable", "checkboxToggled", "target", "multiSelectionKeyPressed", "ctrl<PERSON>ey", "metaKey", "shiftKeyPressed", "shift<PERSON>ey", "updatedSelectedPages", "_toConsumableArray", "setThumbnailSelectingPages", "shiftKeyPivot", "setShiftKeyThumbnailsPivotIndex", "currSelectMinIndex", "currSelectMaxIndex", "max", "Set", "_", "thumbnailSelectionModes", "filter", "lastSelectedPageIndex", "closeElement", "setCurrentPage", "Choice", "checked", "_useSelector2", "getPageLabels", "isReaderMode", "isDocumentReadOnly", "getShiftKeyThumbnailPivotIndex", "getThumbnailSelectionMode", "getActiveDocumentViewerKey", "selectionModes", "_extends", "THUMBNAILS_CONTROL_MANIPULATE_POPUP_SMALL", "THUMBNAILS_CONTROL_MANIPULATE_POPUP_SMALL_TRIGGER", "operations", "operation", "onRotateClockwise", "onRotateCounterClockwise", "InitialLeftPanelPageTabsSmall", "children", "multiPageManipulationControlsItems", "childrenA<PERSON>y", "toArray", "find", "child", "CustomLeftPanelOperations", "multiPageManipulationControlsItemsSmall", "LeftPanelPageTabsRotate", "LeftPanelPageTabsInsertSmall", "LeftPanelPageTabsMoreSmall", "THUMBNAILS_CONTROL_MANIPULATE_POPUP", "THUMBNAILS_CONTROL_MANIPULATE_POPUP_TRIGGER", "moveToTop", "moveToBottom", "InitialLeftPanelPageTabs", "LeftPanelPageTabsMove", "LeftPanelPageTabsMore", "onInsert", "onReplace", "onExtractPages", "onDeletePages", "LeftPanelPageTabsInsert", "LeftPanelPageTabsOperations", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parentElement", "isInMobile", "getPanel<PERSON>idth", "getLeftPanelWidth", "getMultiPageManipulationControlsItems", "getMultiPageManipulationControlsItemsSmall", "getMultiPageManipulationControlsItemsLarge", "isInDesktopOnlyMode", "panelWidth", "deleteModalEnabled", "multiPageManipulationControlsSmall", "multiPageManipulationControlsLarge", "isDesktopOnlyMode", "noPagesSelectedWarning", "getBoundingClientRect", "panel<PERSON><PERSON><PERSON><PERSON><PERSON>", "isPanelLarge", "childProps", "replace", "extractPages", "openElement", "movePagesToTop", "movePagesToBottom", "LeftPanelPageTabsSmall", "LeftPanelPageTabsLarge", "LeftPanelPageTabs", "getPageString", "selected<PERSON><PERSON><PERSON><PERSON><PERSON>", "pagesToPrint", "sortedPages", "sort", "b", "prevIndex", "DocumentControls", "isDisabled", "initialPagesString", "pageString", "setPageString", "previousPageString", "setPreviousPageString", "enableThumbnailSelectingPages", "htmlFor", "onBlur", "selectedPagesString", "pages", "getPageArrayFromString", "pageIndexes", "page", "updatedString", "onChange", "placeholder", "toggleDocumentControl", "mergeDocument", "srcToMerge", "mergeToPage", "shouldFireEvent", "undefined", "LOADING_MODAL", "reject", "mergeResults", "fireEvent", "Events", "DOCUMENT_MERGED", "err", "dataTransferWebViewerFrameKey", "ZOOM_RANGE_MIN", "ZOOM_RANGE_MAX", "ZOOM_RANGE_STEP", "parentDataElement", "isElementOpen", "getIsThumbnailMergingEnabled", "getIsThumbnailReorderingEnabled", "getIsMultipleViewerMerging", "openingPageManipulationOverlayByRightClickEnabled", "isLeftPanelOpen", "totalPages", "isThumbnailMergingEnabled", "isThumbnailReorderingEnabled", "isMultipleViewerMerging", "isThumbnailControlDisabled", "isThumbnailSliderDisabled", "totalPagesFromSecondaryDocumentViewer", "isRightClickEnabled", "listRef", "useRef", "pendingThumbs", "thumbs", "afterMovePageNumber", "isOfficeEditor", "setIsOfficeEditor", "setCanLoad", "_useState6", "setHeight", "_useState8", "<PERSON><PERSON><PERSON><PERSON>", "_useState10", "draggingOverPageIndex", "setDraggingOverPageIndex", "_useState12", "isDraggingToPreviousPage", "setDraggingToPreviousPage", "_useState14", "numberOfColumns", "setNumberOfColumns", "_useState16", "isDragging", "setIsDragging", "_useState18", "setThumbnailSize", "_useState20", "lastTimeTriggered", "setLastTimeTriggered", "_useState22", "globalIndex", "setGlobalIndex", "pageCount", "activeThumbRenders", "thumbContainer", "current", "pageWidth", "getPageWidth", "_getThumbnailSize", "pageHeight", "round", "getThumbnailSize", "getPageHeight", "annotCanvas", "createElement", "role", "aria<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "maxHeight", "ctx", "getContext", "zoom", "getCompleteRotation", "multiplier", "window", "Core", "getCanvasMultiplier", "setAnnotationCanvasTransform", "options", "override<PERSON><PERSON><PERSON>", "_objectSpread", "overridePageRotation", "overridePageCanvas", "debounce", "drawAnnotations", "debouncedDraw", "onBeginRendering", "onFinishedRendering", "needsMoreRendering", "onDocumentLoaded", "_core$getDocument", "getType", "onPageComplete", "updatedPagesIndexes", "isPageAddedBefore", "_listRef$current", "scrollToRow", "floor", "onAnnotationChanged", "annots", "indices", "annot", "PageNumber", "Listable", "onPageNumberUpdated", "_listRef$current2", "onDragEnd", "scrollToRowHelper", "change", "time", "_listRef$current3", "now", "Date", "getTime", "preventDefault", "stopPropagation", "thumbnail", "pageX", "x", "pageY", "_virtualizedThumbnail", "bottom", "hoverAreaHeight", "scrollDown", "scrollUp", "draggingSelectedPage", "pagesToMove", "THUMBNAIL_DRAGGED", "dataTransfer", "setData", "setDragImage", "Image", "dropEffect", "effectAllowed", "frameElement", "extractedDataPromise", "extractPagesWithAnnotations", "pagesExtracted", "onDrop", "externalPageWebViewerFrameId", "files", "insertTo", "isIE11", "getData", "viewerID", "mergingDocument", "currentPageIndex", "otherWebViewerIframe", "parent", "console", "warn", "contentWindow", "docToMerge", "filename", "file", "targetPageNumber", "pageNumbersToMove", "movePages", "updatedPagesNumbers", "offset", "THUMBNAIL_DROPPED", "pageNumbersBeforeMove", "pagesNumbersAfterMove", "numberOfPagesMoved", "thumbIsLoaded", "thumbIsPending", "removeFromPendingThumbs", "getPendingThumbIndex", "splice", "_thumbs$current$pageI", "cancelLoadThumbnail", "findIndex", "thumbStatus", "_thumbs$current$pageI2", "_thumbs$current$pageI3", "canvases", "querySelectorAll", "cancel", "renderThumbnails", "_ref2", "columnsOfThumbnails", "row", "allowPageOperationsUI", "fill", "columnIndex", "thumbIndex", "allowDragAndDrop", "showPlaceHolder", "onContextMenu", "event", "setPageManipulationOverlayAlternativePosition", "left", "right", "top", "openElements", "updateNumberOfColumns", "thumbnailHeight", "thumbnailAutoScrollAreaStyle", "lineStart", "circleRadius", "onSliderChange", "property", "zoomValue", "hideTooltipShortcut", "Slide<PERSON>", "displayProperty", "getDisplayValue", "customCircleRadius", "customLineStrokeWidth", "getCirclePosition", "lineLength", "convertRelativeCirclePositionToValue", "circlePosition", "onStyleChange", "step", "shouldHideSliderTitle", "shouldHideSliderValue", "Measure", "bounds", "onResize", "_ref3", "_ref4", "measureRef", "ref", "List", "rowHeight", "rowCount", "ceil", "<PERSON><PERSON><PERSON><PERSON>", "overscanRowCount", "outline", "scrollToIndex", "exports", "___CSS_LOADER_API_IMPORT___", "module", "locals", "api", "content", "__esModule", "default", "styleTag", "isApryseWebViewerWebComponent", "head", "webComponents", "getElementsByTagName", "findNestedWebComponents", "tagName", "root", "elements", "el", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode"], "sourceRoot": ""}