"use strict";(self.webpackChunkwebviewer_ui=self.webpackChunkwebviewer_ui||[]).push([[9391],{9391:(n,e,t)=>{var i=t(46518),r=t(96395),o=t(80550),u=t(79039),f=t(97751),l=t(94901),a=t(2293),c=t(93438),s=t(36840),h=o&&o.prototype;if(i({target:"Promise",proto:!0,real:!0,forced:!!o&&u((function(){h.finally.call({then:function(){}},(function(){}))}))},{finally:function(n){var e=a(this,f("Promise")),t=l(n);return this.then(t?function(t){return c(e,n()).then((function(){return t}))}:n,t?function(t){return c(e,n()).then((function(){throw t}))}:n)}}),!r&&l(o)){var p=f("Promise").prototype.finally;h.finally!==p&&s(h,"finally",p,{unsafe:!0})}}}]);
//# sourceMappingURL=9391.chunk.js.map