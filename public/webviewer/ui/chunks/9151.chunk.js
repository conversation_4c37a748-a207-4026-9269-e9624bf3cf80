(self.webpackChunkwebviewer_ui=self.webpackChunkwebviewer_ui||[]).push([[9151],{12452:(e,t,n)=>{var o=n(85072),r=n(42553);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.id,r,""]]),o(r,{insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach((e=>o.push(e))),n.querySelectorAll("*").forEach((n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))})),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const r=t[o];if(0===o)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach((t=>{t.innerHTML=e.innerHTML}))};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1}),e.exports=r.locals||{}},17649:(e,t,n)=>{var o=n(85072),r=n(90038);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.id,r,""]]),o(r,{insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach((e=>o.push(e))),n.querySelectorAll("*").forEach((n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))})),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const r=t[o];if(0===o)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach((t=>{t.innerHTML=e.innerHTML}))};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1}),e.exports=r.locals||{}},26524:(e,t,n)=>{var o=n(85072),r=n(66697);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.id,r,""]]),o(r,{insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach((e=>o.push(e))),n.querySelectorAll("*").forEach((n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))})),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const r=t[o];if(0===o)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach((t=>{t.innerHTML=e.innerHTML}))};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1}),e.exports=r.locals||{}},26838:(e,t,n)=>{var o=n(85072),r=n(40547);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.id,r,""]]),o(r,{insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach((e=>o.push(e))),n.querySelectorAll("*").forEach((n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))})),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const r=t[o];if(0===o)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach((t=>{t.innerHTML=e.innerHTML}))};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1}),e.exports=r.locals||{}},40547:(e,t,n)=>{(t=n(76314)(!1)).push([e.id,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.notes-panel-container{z-index:65;display:flex;flex-direction:row;position:relative;overflow:hidden}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .notes-panel-container{z-index:95}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .notes-panel-container{z-index:95}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .notes-panel-container{border-left:1px solid var(--side-panel-border)}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .notes-panel-container{border-left:1px solid var(--side-panel-border)}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .notes-panel-container{top:0;right:0;height:100%;width:100%}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .notes-panel-container{top:0;right:0;height:100%;width:100%}}.notes-panel-container .NotesPanel{width:100%;padding-left:16px;padding-bottom:0;display:flex;flex-direction:column;position:relative;padding-top:16px}.notes-panel-container .NotesPanel .close-container{display:flex;align-items:center;justify-content:flex-end}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .notes-panel-container .NotesPanel .close-container{height:28px;padding-right:16px;margin-bottom:8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .notes-panel-container .NotesPanel .close-container{height:28px;padding-right:16px;margin-bottom:8px}}.notes-panel-container .NotesPanel .close-container .close-icon-container{display:flex;align-items:center;cursor:pointer}.notes-panel-container .NotesPanel .close-container .close-icon-container .close-icon{width:24px;height:24px}.notes-panel-container .NotesPanel .multi-select-place-holder{height:72px}.notes-panel-container .NotesPanel .preview-all-changes{height:57px;display:flex;flex-direction:column;justify-content:center;position:relative;margin-right:16px}.notes-panel-container .NotesPanel .preview-all-changes .divider{height:1px;width:100%;background:var(--divider);position:absolute;top:0}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.notes-panel-container .NotesPanel .reply-area-container .reply-button{width:28px}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .notes-panel-container .NotesPanel{width:100%;min-width:100%;padding-top:0}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .notes-panel-container .NotesPanel .normal-notes-container{padding-bottom:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .notes-panel-container .NotesPanel{width:100%;min-width:100%;padding-top:0}.App.is-web-component:not(.is-in-desktop-only-mode) .notes-panel-container .NotesPanel .normal-notes-container{padding-bottom:16px}}.notes-panel-container .NotesPanel .no-annotations{display:flex;flex-direction:column;align-items:center}.notes-panel-container .NotesPanel .no-annotations .msg{text-align:center}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .notes-panel-container .NotesPanel .no-annotations .msg{line-height:15px;width:146px}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .notes-panel-container .NotesPanel .no-annotations .msg{line-height:15px;width:146px}}.notes-panel-container .NotesPanel .no-annotations .empty-icon,.notes-panel-container .NotesPanel .no-annotations .empty-icon svg{width:65px;height:83px}.notes-panel-container .NotesPanel .no-annotations .empty-icon *{fill:var(--gray-6);color:var(--gray-6)}.notes-panel-container .NotesPanel .normal-notes-container,.notes-panel-container .NotesPanel .virtualized-notes-container{margin-top:10px;flex:1;padding-right:18px}.notes-panel-container .NotesPanel .virtualized-notes-container{overflow:hidden}.notes-panel-container .NotesPanel .normal-notes-container{overflow:auto;overflow:overlay}.notes-panel-container .NotesPanel .note-wrapper:first-child .ListSeparator{margin-top:0;word-break:break-word}.notes-panel-container .NotesPanel .no-results{display:flex;flex-direction:column;align-items:center;padding-right:18px}.notes-panel-container .NotesPanel .no-results .msg{text-align:center}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .notes-panel-container .NotesPanel .no-results .msg{line-height:15px;width:92px}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .notes-panel-container .NotesPanel .no-results .msg{line-height:15px;width:92px}}.notes-panel-container .NotesPanel .no-results .empty-icon,.notes-panel-container .NotesPanel .no-results .empty-icon svg{width:65px;height:83px}.notes-panel-container .NotesPanel .no-results .empty-icon *{fill:var(--border);color:var(--border)}",""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"},e.exports=t},40722:(e,t,n)=>{var o=n(85072),r=n(48537);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.id,r,""]]),o(r,{insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach((e=>o.push(e))),n.querySelectorAll("*").forEach((n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))})),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const r=t[o];if(0===o)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach((t=>{t.innerHTML=e.innerHTML}))};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1}),e.exports=r.locals||{}},42553:(e,t,n)=>{(t=n(76314)(!1)).push([e.id,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.modular-ui-header .sort-row .sort-container .Dropdown__wrapper .Dropdown__items .Dropdown__item:hover{cursor:pointer;background:var(--primary-button-hover);color:var(--gray-0)}.modular-ui-header .sort-row .sort-container .Dropdown__wrapper .Dropdown__items .Dropdown__item.active{cursor:pointer;background:var(--blue-5);color:var(--gray-0)}.comments-counter{height:19px;margin-top:24px;margin-bottom:12px;font-size:16px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .comments-counter{margin-top:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .comments-counter{margin-top:16px}}.comments-counter span{font-weight:700}[data-element=notesPanelHeader]{padding-right:18px}[data-element=notesPanelHeader] .buttons-container{display:flex;grid-column-gap:8px;-moz-column-gap:8px;column-gap:8px;justify-content:space-between}[data-element=notesPanelHeader] .buttons-container .Button{height:28px;width:28px;cursor:pointer}[data-element=notesPanelHeader] .buttons-container .Button:hover:enabled{background:var(--view-header-button-hover)}[data-element=notesPanelHeader] .buttons-container .Button.active{background:var(--view-header-button-active)}[data-element=notesPanelHeader] .buttons-container .Button.active .Icon{color:var(--view-header-icon-active-fill)}[data-element=notesPanelHeader] .buttons-container .Button:disabled{cursor:default}[data-element=notesPanelHeader] .sort-row{display:flex;justify-content:space-between;margin-bottom:16px}[data-element=notesPanelHeader] .sort-row .sort-container{display:flex;flex-direction:row;align-items:center;justify-content:flex-end;align-self:flex-end}[data-element=notesPanelHeader] .sort-row .sort-container .label{margin-right:8px}[data-element=notesPanelHeader] .sort-row .sort-container .picked-option{text-align:left}[data-element=notesPanelHeader] .input-container{display:flex;position:relative;flex:1}[data-element=notesPanelHeader] .input-container input{width:100%;border:1px solid var(--border);border-radius:4px;color:var(--text-color);padding:4px 8px 6px}[data-element=notesPanelHeader] .input-container.modular-ui-input{box-sizing:border-box;border:1px solid var(--border);border-radius:4px;height:28px;align-items:center;color:var(--text-color);padding:6px 2px 6px 6px}[data-element=notesPanelHeader] .input-container.modular-ui-input[focus-within]{outline:none;border:1px solid var(--focus-border)}[data-element=notesPanelHeader] .input-container.modular-ui-input:focus-within{outline:none;border:1px solid var(--focus-border)}[data-element=notesPanelHeader] .input-container.modular-ui-input input{padding-left:8px;padding-right:26px;height:20px;border:none;background:transparent}[data-element=notesPanelHeader] .input-button{cursor:pointer;background:var(--primary-button);border-radius:4px;height:100%;width:40px;display:flex;align-items:center;justify-content:center;position:absolute;bottom:0;right:0}[data-element=notesPanelHeader] .input-button .Icon{width:20px;height:20px}[data-element=notesPanelHeader] .input-button svg{color:var(--primary-button-text)}[data-element=notesPanelHeader] .input-button:hover{background:var(--primary-button-hover)}[data-element=notesPanelHeader] .divider{height:1px;width:100%;background:var(--divider);margin:16px 0}.modular-ui-header .sort-row .sort-container .Dropdown__wrapper .Dropdown__items .Dropdown__item{font-size:13px}.modular-ui-header .sort-row .sort-container .Dropdown__wrapper .Dropdown__items .Dropdown__item .Icon .arrow{margin-top:0}.modular-ui-header .input-container.modular-ui-input .Icon{width:16px;height:16px}",""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"},e.exports=t},48537:(e,t,n)=>{(t=n(76314)(!1)).push([e.id,".notes-panel-container .buttons-container{display:flex;grid-column-gap:8px;-moz-column-gap:8px;column-gap:8px;justify-content:space-between}.notes-panel-container .buttons-container .Button{height:28px;width:28px}.notes-panel-container .buttons-container .Button:hover:enabled{background:var(--view-header-button-hover)}.notes-panel-container .buttons-container .Button.active{background:var(--view-header-button-active)}.notes-panel-container .buttons-container .Button.active .Icon{color:var(--view-header-icon-active-fill)}.notes-panel-container .multi-select-footer{background:var(--panel-background);display:flex;border-top:1px solid var(--gray-5);margin-top:auto;margin-bottom:16px;height:64px;padding:24px 16px;align-items:center;justify-content:center;position:absolute;bottom:0;width:100%;z-index:100}.notes-panel-container .multi-select-footer .close-container{margin-top:auto;right:26px;position:absolute}",""]),e.exports=t},59151:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>Ye}),n(52675),n(89463),n(2259),n(45700),n(2008),n(50113),n(51629),n(23418),n(64346),n(23792),n(34782),n(89572),n(23288),n(94170),n(62010),n(2892),n(69085),n(67945),n(84185),n(83851),n(81278),n(79432),n(26099),n(27495),n(38781),n(47764),n(23500),n(62953);var o=n(96540),r=n(28854),i=(n(28706),n(48980),n(74423),n(25276),n(15086),n(54554),n(16034),n(21699),n(76031),n(46942)),a=n.n(i),l=n(61113),c=n(18492),s=n(5556),u=n.n(s),d=n(40367),p=n(46697);function m(e){return m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},m(e)}function f(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function h(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?f(Object(n),!0).forEach((function(t){y(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):f(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function y(e,t,n){return(t=function(e){var t=function(e){if("object"!=m(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=m(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==m(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function g(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,s=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){s=!0,r=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw r}}return l}}(e,t)||function(e,t){if(e){if("string"==typeof e)return b(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?b(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function b(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}var v={notes:u().array.isRequired,children:u().func.isRequired,onScroll:u().func.isRequired,initialScrollTop:u().number.isRequired,selectedIndex:u().number,sortStrategy:u().string},w=new p.jS({defaultHeight:50,fixedWidth:!0}),A=o.forwardRef((function(e,t){var n=e.notes,r=e.children,i=e.onScroll,a=e.initialScrollTop,l=e.selectedIndex,c=e.sortStrategy,s=(0,o.useRef)(),u=g((0,o.useState)(0),2),m=u[0],f=u[1],y=g((0,o.useState)({width:0,height:0}),2),b=y[0],v=y[1],A=window.innerHeight;(0,o.useImperativeHandle)(t,(function(){return{scrollToPosition:function(e){s.current.scrollToPosition(e)},scrollToRow:function(e){s.current.scrollToRow(e)}}})),(0,o.useEffect)((function(){s.current.scrollToPosition(a)}),[a]),(0,o.useEffect)((function(){var e,t;w.clearAll(),null==s||null===(e=s.current)||void 0===e||e.measureAllRows(),-1!==l&&(null===(t=s.current)||void 0===t||t.scrollToRow(l))}),[l]),(0,o.useEffect)((function(){var e,t;w.clearAll(),null==s||null===(e=s.current)||void 0===e||e.measureAllRows(),null==s||null===(t=s.current)||void 0===t||t.forceUpdateGrid()}),[n.length,c]),(0,o.useEffect)((function(){var e=function(){var e=window.innerHeight-A;e&&(window.innerHeight<A&&f(e),A=window.innerHeight)};return window.addEventListener("resize",e),function(){window.removeEventListener("resize",e)}}));var x=function(e){var t=e.scrollTop;i(t)},E=function(e){var t=e.index,i=e.key,a=e.parent,l=e.style,c=n[t];return o.createElement(p.dl,{key:"".concat(i).concat(c.Id),cache:w,columnIndex:0,parent:a,rowIndex:t},(function(e){var i=e.measure;return o.createElement("div",{style:h(h({},l),{},{paddingRight:"12px"})},r(n,t,(function(){!function(e){var t;w.clear(e),null===(t=s.current)||void 0===t||t.recomputeRowHeights(e)}(t),i()})))}))};return o.createElement(d.A,{bounds:!0,offset:!0,onResize:function(e){var t=e.bounds;v(h(h({},t),{},{height:t.height+2*m})),f(0)}},(function(e){var t=e.measureRef;return o.createElement("div",{ref:t,className:"virtualized-notes-container"},o.createElement(p.B8,{deferredMeasurementCache:w,style:{outline:"none"},height:b.height-m,width:b.width,overscanRowCount:10,ref:s,rowCount:n.length,rowHeight:w.rowHeight,rowRenderer:E,onScroll:x,"aria-label":"presentation",role:"presentation"}))}))}));A.displayName="VirtualizedList",A.propTypes=v;const x=A;n(62062);var E={notes:u().array.isRequired,children:u().func.isRequired,onScroll:u().func.isRequired,initialScrollTop:u().number.isRequired},S=o.forwardRef((function(e,t){var n=e.notes,r=e.children,i=e.onScroll,a=e.initialScrollTop,l=(0,o.useRef)();return(0,o.useImperativeHandle)(t,(function(){return{scrollToPosition:function(e){l.current.scrollTop=e},scrollToRow:function(e){var t=l.current,n=t.children[e];if(n){var o=t.getBoundingClientRect(),r=n.getBoundingClientRect();r.top>=o.top&&r.top<=o.top+t.clientHeight||(t.scrollTop=r.top+t.scrollTop-o.top)}}}})),(0,o.useEffect)((function(){l.current.scrollTop=a}),[a]),o.createElement("div",{ref:l,className:"normal-notes-container",onScroll:function(e){i(e.target.scrollTop)},role:"list"},n.map((function(e,t){return o.createElement(o.Fragment,{key:"".concat(t,"_").concat(e.Id)},r(n,t))})))}));S.displayName="NormalList",S.propTypes=E;const O=S;var N=n(84850),P=n(11623),T=n(67319),M=n(36980),I=(n(31415),n(51e3)),R=(n(42762),n(10544)),C=n(41366),j=n(347),k=n(75710);function _(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,s=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){s=!0,r=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw r}}return l}}(e,t)||function(e,t){if(e){if("string"==typeof e)return L(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?L(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function L(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}n(26524);const H=function(e){var t=e.annotations,n=e.onSubmit,i=e.onClose,s=_((0,l.d4)((function(e){var t;return[k.A.getIsMentionEnabled(e),k.A.getActiveDocumentViewerKey(e),null===(t=k.A.getFeatureFlags(e))||void 0===t?void 0:t.customizableUI]}),l.bN),3),u=s[0],d=s[1],p=s[2],m=_((0,o.useState)(""),2),f=m[0],h=m[1],y=_((0,c.B)(),1)[0],g=(0,o.useRef)();(0,o.useEffect)((function(){g.current.focus()}),[]);var b=a()({"reply-area":!0});return o.createElement("div",{className:a()({"reply-area-multi-container":!0,"modular-ui":p})},o.createElement("div",{className:"reply-area-multi-header"},o.createElement("div",{className:"title"},y("action.multiReplyAnnotations",{count:t.length})),o.createElement(I.A,{className:"close-icon",onClick:i,img:"ic_close_black_24px"})),o.createElement("form",{onSubmit:function(e){e.preventDefault();var o=g.current.getEditor(),i=C.A.getFormattedTextFromDeltas(o.getContents());i.trim()&&(t.forEach((function(e){if(u){var t=C.A.createMentionReply(e,i);r.A.addAnnotations([t],d),(0,j.A)(o,t)}else{var n=r.A.createAnnotationReply(e,i,d);(0,j.A)(o,n)}})),n())}},o.createElement("div",{className:b,onMouseDown:function(e){return e.stopPropagation()}},o.createElement(R.A,{ref:function(e){g.current=e},value:f,onChange:function(e){return function(e){h(e)}(e)},isReply:!0})),o.createElement("div",{className:"reply-button-container"},o.createElement(I.A,{img:"icon-post-reply",className:"reply-button",isSubmitType:!0}))))};var D=n(18439),q=n(40961),F=n(59844);const U=function(e){var t=e.id,n=e.position,r=e.children,i=function(e){var t=(0,o.useRef)(null);return(0,o.useEffect)((function(){var n,o,r=document.querySelector("#".concat(e)),i=r||function(e){var t=document.createElement("div");return t.setAttribute("id",e),t}(e);return r||(n=i,(o=window.isApryseWebViewerWebComponent?(0,F.Ay)():document.body).insertBefore(n,o.lastElementChild.nextElementSibling)),i.appendChild(t.current),function(){t.current.remove(),i.childElementCount||i.remove()}}),[e]),t.current||(t.current=document.createElement("div")),t.current}(t);return i.style.position="absolute",i.style.top="auto"===n.top?n.top:"".concat(n.top,"px"),i.style.left="auto"===n.left?n.left:"".concat(n.left,"px"),i.style.right="auto"===n.right?n.right:"".concat(n.right,"px"),i.style.pointerEvents="none",i.style.zIndex=999,(0,q.createPortal)(r,i)};var B=n(4683),z=n(42688),W=n(88595),V=n(14269),G=n(26247),K=n(17246);function $(e){return $="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},$(e)}function Q(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function Y(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Q(Object(n),!0).forEach((function(t){X(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Q(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function X(e,t,n){return(t=function(e){var t=function(e){if("object"!=$(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=$(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==$(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function J(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,s=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){s=!0,r=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw r}}return l}}(e,t)||function(e,t){if(e){if("string"==typeof e)return Z(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Z(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Z(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}n(17649);const ee=function(e){var t=e.annotations,n=e.triggerElementName,r=e.onClose,i=void 0===r?function(){}:r,a=(0,l.wA)(),c=(0,o.useRef)(),s=J((0,o.useState)([]),2),u=s[0],d=s[1],p=J((0,o.useState)({left:"auto",right:"auto",top:"auto"}),2),m=p[0],f=p[1],h="MultiStyle";(0,z.A)(c,(function(e){var t=document.querySelector("[data-element=".concat(n,"]")).contains(e.target),o=(0,V.pb)(),r=(0,V.nK)(),a=(0,V.nz)();t||o||r||a||i()}));var y={};t.forEach((function(e){y=Y(Y({},y),(0,W.A)(e))}));var g=t.find((function(e){return e instanceof window.Core.Annotations.FreeTextAnnotation&&(e.getIntent()===window.Core.Annotations.FreeTextAnnotation.Intent.FreeText||e.getIntent()===window.Core.Annotations.FreeTextAnnotation.Intent.FreeTextCallout)})),b={};if(g){var v,w,A,x,E,S,O,N,P,T,M,I=g.getRichTextStyle();b={Font:g.Font,FontSize:g.FontSize,TextAlign:g.TextAlign,TextVerticalAlign:g.TextVerticalAlign,bold:null!==(v="bold"===(null==I||null===(w=I[0])||void 0===w?void 0:w["font-weight"]))&&void 0!==v&&v,italic:null!==(A="italic"===(null==I||null===(x=I[0])||void 0===x?void 0:x["font-style"]))&&void 0!==A&&A,underline:(null==I||null===(E=I[0])||void 0===E||null===(S=E["text-decoration"])||void 0===S?void 0:S.includes("underline"))||(null==I||null===(O=I[0])||void 0===O||null===(N=O["text-decoration"])||void 0===N?void 0:N.includes("word")),strikeout:null!==(P=null==I||null===(T=I[0])||void 0===T||null===(M=T["text-decoration"])||void 0===M?void 0:M.includes("line-through"))&&void 0!==P&&P}}return(0,o.useEffect)((function(){var e=[];if(y.TextColor&&e.push(K.Rc.TEXT_COLOR),y.StrokeColor&&e.push(K.Rc.STROKE_COLOR),y.FillColor&&e.push(K.Rc.FILL_COLOR),(0,K.sT)(h,e,e[0])){var t=(0,K.re)("currentStyleTab","iconColor");a(G.A.setColorMap(t));var n=(0,K.ap)(h).styleTabs;d(n)}}),[t]),0===u.length?null:o.createElement(U,{id:"multi-style-popup-portal",position:m},o.createElement("div",{className:"multi-style-container",ref:c},o.createElement(B.A,{annotations:t,style:y,isOpen:!0,onResize:function(){var e=(0,D.A)(n,c);f(e)},isFreeText:!!g,colorMapKey:h,properties:b,isRedaction:!1,isMeasure:!1,showLineStyleOptions:!1,hideSnapModeCheckbox:!1})))};var te=n(76161),ne=n(18023),oe=n(75076),re=n(97160);function ie(e){return ie="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ie(e)}function ae(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function le(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ae(Object(n),!0).forEach((function(t){ce(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ae(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ce(e,t,n){return(t=function(e){var t=function(e){if("object"!=ie(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=ie(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==ie(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function se(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,s=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){s=!0,r=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw r}}return l}}(e,t)||function(e,t){if(e){if("string"==typeof e)return ue(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?ue(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ue(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}n(40722);var de={showMultiReply:u().bool.isRequired,setShowMultiReply:u().func.isRequired,setShowMultiState:u().func.isRequired,showMultiStyle:u().bool.isRequired,setShowMultiStyle:u().func.isRequired,setMultiSelectMode:u().func.isRequired,isMultiSelectedMap:u().object.isRequired,setIsMultiSelectedMap:u().func.isRequired,multiSelectedAnnotations:u().array.isRequired},pe=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,n=new Set;return e.forEach((function(e){if(e.isGrouped()){var o=r.A.getAnnotationById(e.InReplyTo,t);o&&n.add(o)}else n.add(e)})),Array.from(n)},me=function(e){var t=e.showMultiReply,n=e.setShowMultiReply,i=e.setShowMultiState,a=e.showMultiStyle,s=e.setShowMultiStyle,u=e.setMultiSelectMode,d=e.isMultiSelectedMap,p=e.setIsMultiSelectedMap,m=e.multiSelectedAnnotations,f=se((0,o.useState)([]),2),h=f[0],y=f[1],g=(0,l.wA)(),b=se((0,c.B)(),1)[0],v=(0,l.d4)(k.A.getActiveDocumentViewerKey),w=(0,l.d4)(k.A.isDocumentReadOnly);(0,o.useEffect)((function(){var e=function(e,t){if("delete"===t){var n=le({},d);e.forEach((function(e){delete n[e.Id]})),p(n)}else if("modify"===t){var o=le({},d);e.forEach((function(e){var t=r.A.getGroupAnnotations(e,v);t.some((function(e){return d[e.Id]}))&&t.forEach((function(e){o[e.Id]=e}))}));var i=Object.keys(d),a=Object.keys(d);i.filter((function(e){return!a.includes(e)})).length>0&&p(o)}};return r.A.addEventListener("annotationChanged",e,void 0,v),function(){r.A.removeEventListener("annotationChanged",e,v)}}),[d,v]),(0,o.useEffect)((function(){return function(){n(!1),p({})}}),[]),(0,o.useEffect)((function(){var e=m.filter((function(e){return r.A.canModify(e,v)}));y(e)}),[m]);var A=r.A.getNumberOfGroups(h,v)>1,x=!A&&(h.length>2||h.length>0&&r.A.getGroupAnnotations(h[0],v).length>1),E=(0,o.useCallback)((function(e){pe(m,v).forEach((function(t){var n=(0,oe.H)(t,e,v);t.addReply(n);var o=r.A.getAnnotationManager(v);o.addAnnotation(n),o.trigger("addReply",[n,t,o.getRootAnnotation(t)])})),i(!1)}),[m,v]);return t?o.createElement(T.A.Provider,{value:{resize:function(){}}},o.createElement(H,{annotations:pe(m,v),onSubmit:function(){return n(!1)},onClose:function(){return n(!1)}})):o.createElement("div",{className:"multi-select-footer"},o.createElement("div",{className:"buttons-container"},o.createElement(I.A,{dataElement:re.A.NOTE_MULTI_REPLY_BUTTON,disabled:w||0===m.length,img:"icon-header-chat-line",onClick:function(){n(!0)},title:"action.comment"}),o.createElement(ne.A,{dataElement:re.A.NOTE_MULTI_STATE_BUTTON,title:b("option.notesOrder.status"),img:"icon-annotation-status-none",toggleElement:re.A.NOTE_STATE_FLYOUT,disabled:w||0===h.length}),o.createElement(te.A,{isMultiSelectMode:!0,handleStateChange:E}),o.createElement(I.A,{dataElement:re.A.NOTE_MULTI_STYLE_BUTTON,img:"icon-menu-style-line",disabled:w||0===h.length,onClick:function(){s(!a)},title:"action.style"}),a&&o.createElement(ee,{annotations:h,triggerElementName:"multiStyleButton",onClose:function(){s(!1)}}),!x&&o.createElement(I.A,{dataElement:re.A.NOTE_MULTI_GROUP_BUTTON,disabled:w||!A,img:"group-annotations-icon",onClick:function(){r.A.groupAnnotations(m[0],m,v)},title:"action.group"}),x&&o.createElement(I.A,{dataElement:re.A.NOTE_MULTI_UNGROUP_BUTTON,img:"ungroup-annotations-icon",onClick:function(){r.A.ungroupAnnotations(m,v)},title:"action.ungroup"}),o.createElement(I.A,{dataElement:re.A.NOTE_MULTI_DELETE_BUTTON,disabled:w||0===h.length,img:"icon-delete-line",onClick:function(){var e={title:b("warning.multiDeleteAnnotation.title"),message:b("warning.multiDeleteAnnotation.message"),confirmBtnText:b("action.delete"),onConfirm:function(){r.A.deleteAnnotations(h,void 0,v)}};g(G.A.showWarningMessage(e))},title:"action.delete"})),o.createElement("div",{className:"close-container"},o.createElement(I.A,{className:"close-icon-container",onClick:function(){u(!1)},img:"ic_close_black_24px"})))};me.propTypes=de;const fe=me;var he=n(17647),ye=n(38221),ge=n.n(ye),be=n(20714),ve=n(21012),we=n(33647),Ae=n(56478),xe=n(33311),Ee=n(73154);function Se(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,s=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){s=!0,r=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw r}}return l}}(e,t)||function(e,t){if(e){if("string"==typeof e)return Oe(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Oe(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Oe(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}n(12452);var Ne={notes:u().array.isRequired,disableFilterAnnotation:u().bool,setSearchInputHandler:u().func.isRequired,isMultiSelectMode:u().bool,toggleMultiSelectMode:u().func,isMultiSelectEnabled:u().bool},Pe="sortContainer";function Te(e){var t=e.notes,n=e.disableFilterAnnotation,i=e.setSearchInputHandler,s=e.isMultiSelectMode,u=e.toggleMultiSelectMode,d=e.isMultiSelectEnabled,p=Se((0,l.d4)((function(e){var t;return[k.A.getSortStrategy(e),k.A.isElementDisabled(e,Pe),k.A.getNotesPanelCustomHeaderOptions(e),k.A.getAnnotationFilters(e),k.A.getIsOfficeEditorMode(e),k.A.getOfficeEditorEditMode(e),null===(t=k.A.getFeatureFlags(e))||void 0===t?void 0:t.customizableUI]}),l.bN),7),m=p[0],f=p[1],h=p[2],y=p[3],g=p[4],b=p[5],v=p[6],w=Se((0,c.B)(),1)[0],A=(0,l.wA)(),x=Se((0,o.useState)(!1),2),E=x[0],S=x[1],O=Se((0,o.useState)(!1),2),N=O[0],T=O[1],M=Se((0,o.useState)(""),2),R=M[0],C=M[1];(0,o.useEffect)((function(){var e=y.authorFilter,t=y.colorFilter,n=y.statusFilter,o=y.typeFilter;((null==e?void 0:e.length)>0||(null==t?void 0:t.length)>0||(null==n?void 0:n.length)>0||(null==o?void 0:o.length)>0)&&S(!0);var r=function(e){var t=e.detail,n=t.types,o=t.authors,r=t.colors,i=t.statuses;n.length>0||o.length>0||r.length>0||i.length>0?S(!0):S(!1)};return window.addEventListener(we.A.ANNOTATION_FILTER_CHANGED,r),function(){window.removeEventListener(we.A.ANNOTATION_FILTER_CHANGED,r)}}),[]),(0,o.useEffect)((function(){g&&b===xe.qr.PREVIEW?(T(!0),i(""),C("")):T(!1)}),[g,b]);var j=ge()((function(e){r.A.deselectAllAnnotations(),i(e)}),500),_=o.createElement("div",{className:"sort-container","data-element":Pe},o.createElement("div",{className:"label"},"".concat(w("message.sort"),":")),o.createElement(be.A,{dataElement:"notesOrderDropdown",disabled:0===t.length||N,ariaLabel:"".concat(w("message.sortBy")," ").concat(m),items:Object.keys((0,Ae.Qk)()),translationPrefix:"option.notesOrder",currentSelectionKey:m,onClickItem:function(e){A(G.A.setNotesPanelSortStrategy(e))}})),L=(0,Ee.A)((function(){return A(G.A.openElement("filterModal"))})),H=w(g?"message.searchSuggestionsPlaceholder":"message.searchCommentsPlaceholder"),D=o.createElement(ve.A,{className:a()({header:!0,"modular-ui-header":v}),dataElement:"notesPanelHeader"},o.createElement(ve.A,{className:a()({"input-container":!0,"modular-ui-input":v}),dataElement:re.A.NotesPanel.DefaultHeader.INPUT_CONTAINER},v&&o.createElement(P.A,{glyph:"icon-header-search"}),o.createElement("input",{disabled:N,type:"text",placeholder:v?"":H,"aria-label":H,onChange:function(e){C(e.target.value),j(e.target.value)},id:"NotesPanel__input",value:R})),o.createElement(ve.A,{className:"comments-counter",dataElement:re.A.NotesPanel.DefaultHeader.COMMENTS_COUNTER},o.createElement("span",{className:"main-comment"},w(g?"officeEditor.reviewing":"component.notesPanel"))," ","(".concat(t.length,")")),o.createElement(ve.A,{className:"sort-row",dataElement:re.A.NotesPanel.DefaultHeader.SORT_ROW},f?o.createElement("div",{className:"sort-container"}):_,o.createElement("div",{className:"buttons-container"},d&&!g&&o.createElement(I.A,{dataElement:re.A.NOTE_MULTI_SELECT_MODE_BUTTON,className:a()({active:s}),disabled:0===t.length,img:"icon-annotation-select-multiple",onClick:function(){r.A.deselectAllAnnotations(),u()},title:w("component.multiSelectButton")}),o.createElement(I.A,{dataElement:re.A.NotesPanel.DefaultHeader.FILTER_ANNOTATION_BUTTON,className:a()({active:E}),disabled:n,img:"icon-comments-filter",onClick:L,title:w("component.filter")}))));return o.createElement(o.Fragment,null,h&&o.createElement(he.A,{render:h.render,renderArguments:[t]}),(!h||!h.overwriteDefaultHeader)&&D)}Te.propTypes=Ne;const Me=Te;var Ie=n(70319),Re=n(815),Ce=n(90540),je=n(41998);function ke(e){return ke="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ke(e)}function _e(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function Le(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?_e(Object(n),!0).forEach((function(t){He(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):_e(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function He(e,t,n){return(t=function(e){var t=function(e){if("object"!=ke(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=ke(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==ke(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function De(e){return function(e){if(Array.isArray(e))return Ue(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||Fe(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function qe(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,s=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){s=!0,r=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw r}}return l}}(e,t)||Fe(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Fe(e,t){if(e){if("string"==typeof e)return Ue(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Ue(e,t):void 0}}function Ue(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}n(26838);const Be=function(e){var t=e.currentLeftPanelWidth,n=e.notes,i=e.selectedNoteIds,s=e.setSelectedNoteIds,u=e.searchInput,d=e.setSearchInput,p=e.isMultiSelectMode,m=e.setMultiSelectMode,f=e.isMultiSelectedMap,h=e.setIsMultiSelectedMap,y=e.scrollToSelectedAnnot,g=e.setScrollToSelectedAnnot,b=e.isCustomPanel,v=e.isCustomPanelOpen,w=e.isLeftSide,A=e.parentDataElement,E=(0,l.d4)(k.A.getSortStrategy),S=(0,l.d4)((function(e){return k.A.isElementOpen(e,re.A.NOTES_PANEL)})),I=(0,l.d4)((function(e){return k.A.isElementDisabled(e,re.A.NOTES_PANEL)})),R=(0,l.d4)(k.A.getPageLabels,l.bN),C=(0,l.d4)(k.A.getCustomNoteFilter,l.bN),j=(0,l.d4)((function(e){return A?k.A.getPanelWidth(e,A):k.A.getNotesPanelWidth(e)}),l.bN),_=(0,l.d4)(k.A.getNotesInLeftPanel),L=(0,l.d4)(k.A.isDocumentReadOnly),H=(0,l.d4)(k.A.isAnnotationNumberingEnabled),D=(0,l.d4)(k.A.getEnableNotesPanelVirtualizedList),q=(0,l.d4)(k.A.isInDesktopOnlyMode),F=(0,l.d4)(k.A.getNotesPanelCustomEmptyPanel,l.bN),U=(0,l.d4)(k.A.getIsNotesPanelMultiSelectEnabled),B=(0,l.d4)(k.A.getActiveDocumentViewerKey),z=(0,l.d4)(k.A.getIsOfficeEditorMode),W=(0,l.wA)(),V=qe((0,c.B)(),1)[0],K=t||j,$=(0,Re.IS)(),Q=qe((0,o.useState)([]),2),Y=Q[0],X=Q[1],J=qe((0,o.useState)(!1),2),Z=J[0],ee=J[1],te=qe((0,o.useState)(!1),2),ne=te[0],oe=te[1],ie=qe((0,o.useState)(!1),2),ae=ie[0],le=ie[1],ce=qe((0,o.useState)(void 0),2),se=ce[0],ue=ce[1],de=(0,o.useRef)(),pe=(0,o.useRef)(0),me=D?Ce.lT?25:100:1/0;(0,o.useEffect)((function(){var e=function(e){W(G.A.setAnnotationNumbering(e))},t=function(e,t){"selected"===t&&ue(e[0].Id)};return r.A.addEventListener("annotationNumberingUpdated",e),r.A.addEventListener("annotationSelected",t),function(){r.A.removeEventListener("annotationNumberingUpdated",e),r.A.removeEventListener("annotationSelected",t)}}),[]);var ye=-1,ge=function(e){e&&(pe.current=e),W(G.A.closeElement("annotationNoteConnectorLine"))},be=function(e){var t=e.getContents(),n=r.A.getDisplayAuthor(e.Author),o=e.getCustomData("trn-annot-preview");return(null==t?void 0:t.toLowerCase().includes(u.toLowerCase()))||(null==n?void 0:n.toLowerCase().includes(u.toLowerCase()))||(null==o?void 0:o.toLowerCase().includes(u.toLowerCase()))},ve=(0,Ae.Qk)()[E].getSortedNotes(n).filter((function(e){var t=!0;if(C&&(t=t&&C(e)),u){var n=e.getReplies(),o=[e].concat(De(n));t=t&&o.some(be)}return t}));(0,o.useEffect)((function(){Object.keys(i).length&&-1!==ye&&setTimeout((function(){var e;null===(e=de.current)||void 0===e||e.scrollToRow(ye)}),0)}),[i]);var we=function(e){return!Object.keys(i).length&&u&&ve.filter((function(e){return e.getReplies().some(be)})).some((function(t){return t.Id===e.Id}))},Ee=qe((0,o.useState)({}),2),Se=Ee[0],Oe=Ee[1],Ne=(0,o.useCallback)((function(e,t){Oe((function(n){return Le(Le({},n),{},He({},t,e))}))}),[Oe]),Pe=qe((0,o.useState)({}),2),Te=Pe[0],ke=Pe[1],_e=(0,o.useCallback)((function(e,t){ke((function(n){return Le(Le({},n),{},He({},t,e))}))}),[ke]),Fe=qe((0,o.useState)({}),2),Ue=Fe[0],Be=Fe[1],ze=function(e,t){Be((function(n){return Le(Le({},n),{},He({},e,[].concat(De(n[e]||[]),De(t))))}))},We=function(e){Be((function(t){return Le(Le({},t),{},He({},e,[]))}))},Ve=function(e,t){var n=Ue[e];if((null==n?void 0:n.length)>0){var o=n.indexOf(t);o>-1&&(n.splice(o,1),Be((function(t){return Le(Le({},t),{},He({},e,De(n)))})))}};(0,o.useEffect)((function(){X(Object.values(f))}),[f]);var Ge=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(){},a=null,l=(0,Ae.Qk)()[E],c=l.shouldRenderSeparator,d=l.getSeparatorContent,m=0===t?null:e[t-1],b=e[t];c&&d&&(!m||c(m,b))&&(a=o.createElement(M.A,{renderContent:function(){return d(m,b,{pageLabels:R})}}));var A={searchInput:u,resize:n,isSelected:i[b.Id],isContentEditable:r.A.canModifyContents(b,B)&&!b.getContents(),pendingEditTextMap:Se,setPendingEditText:Ne,pendingReplyMap:Te,setPendingReply:_e,isDocumentReadOnly:L,onTopNoteContentClicked:function(){!p&&i[b.Id]&&(s((function(e){var t=Le({},e);return delete t[b.Id],t})),r.A.deselectAnnotation(b,B))},isExpandedFromSearch:we(b),scrollToSelectedAnnot:y,sortStrategy:E,showAnnotationNumbering:H,setCurAnnotId:ue,pendingAttachmentMap:Ue,clearAttachments:We,deleteAttachment:Ve,addAttachments:ze,documentViewerKey:B};return t===ye&&setTimeout((function(){g(!1),W(G.A.openElement("annotationNoteConnectorLine"))}),0),o.createElement("div",{role:"listitem",className:"note-wrapper"},a,o.createElement(T.A.Provider,{value:A},o.createElement(N.A,{isCustomPanelOpen:v,shouldHideConnectorLine:w,annotation:b,isMultiSelected:!!f[b.Id],isMultiSelectMode:p,isMultiSelectEnabled:U,isInNotesPanel:!0,handleMultiSelect:function(e){if(e){var t=Le({},f),n=r.A.getGroupAnnotations(b,B);n.forEach((function(e){t[e.Id]=e})),h(t),r.A.selectAnnotations(n)}else{var o=Le({},f),i=r.A.getGroupAnnotations(b,B);i.forEach((function(e){delete o[e.Id]})),h(o),r.A.deselectAnnotations([b].concat(De(i)))}}})))},Ke=o.createElement("div",{className:"no-results"},o.createElement("div",null,o.createElement(P.A,{className:"empty-icon",glyph:"illustration - empty state - outlines"})),o.createElement("p",{"aria-live":"assertive",className:"msg no-margin"},V("message.noResults"))),$e=null!=F&&F.icon?F.icon:z?"ic-edit-page":"illustration - empty state - outlines",Qe=null!=F&&F.message?F.message:V(z?"message.noRevisions":"message.noAnnotations"),Ye=F&&F.readOnlyMessage?F.readOnlyMessage:V("message.noAnnotationsReadOnly"),Xe=F&&!F.hideIcon||!F,Je=F&&F.render,Ze=o.createElement("div",{className:"no-annotations"},Je?o.createElement(he.A,{render:F.render}):o.createElement(o.Fragment,null,Xe&&o.createElement("div",null,o.createElement(P.A,{className:"empty-icon",glyph:$e})),o.createElement("div",{className:"msg"},L?Ye:Qe))),et=o.createElement("div",{className:"multi-select-place-holder"}),tt=o.createElement("div",{className:"multi-reply-place-holder"}),nt=Object.keys(i);1===nt.length?ye=ve.findIndex((function(e){return e.Id===nt[0]})):nt.length&&ve.filter((function(e){return i[e.Id]})).length&&(ye=ve.findIndex((function(e){return e.Id===se})));var ot={};return b||!q&&$||(ot={width:"".concat(K,"px"),minWidth:"".concat(K,"px")}),!I&&(S||_||b)?o.createElement("div",{className:"notes-panel-container"},o.createElement("div",{className:a()({Panel:!0,NotesPanel:!0}),style:ot,"data-element":"notesPanel",onMouseUp:function(){return r.A.deselectAllAnnotations}},!q&&$&&!_&&o.createElement("div",{className:"close-container"},o.createElement("div",{className:"close-icon-container",onClick:function(){W(G.A.closeElements([re.A.NOTES_PANEL]))}},o.createElement(P.A,{glyph:"ic_close_black_24px",className:"close-icon"}))),o.createElement(o.Fragment,null,o.createElement(Me,{notes:ve,disableFilterAnnotation:0===n.length,setSearchInputHandler:d,isMultiSelectMode:p,toggleMultiSelectMode:function(){m(!p)},isMultiSelectEnabled:U}),0===ve.length?0===n.length?Ze:Ke:ve.length<=me?o.createElement(O,{ref:de,notes:ve,onScroll:ge,initialScrollTop:pe.current},Ge):o.createElement(x,{ref:de,notes:ve,sortStrategy:E,onScroll:ge,initialScrollTop:pe.current,selectedIndex:ye},Ge),p?Z?tt:et:null,z&&!p&&ve.length>0&&o.createElement("div",{className:"preview-all-changes"},o.createElement("div",{className:"divider"}),o.createElement(Ie.A,{isSwitch:!0,label:V("officeEditor.previewAllChanges"),onChange:function(e){return r.A.getOfficeEditor().setEditMode(e.target.checked?xe.qr.PREVIEW:xe.qr.REVIEWING)}})))),p&&o.createElement(fe,{showMultiReply:Z,setShowMultiReply:ee,showMultiState:ne,setShowMultiState:oe,showMultiStyle:ae,setShowMultiStyle:le,setMultiSelectMode:m,isMultiSelectedMap:f,setIsMultiSelectedMap:h,multiSelectedAnnotations:Y}),o.createElement(je.A,{annotationId:se,addAttachments:ze})):null};function ze(e){return ze="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ze(e)}function We(){return We=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},We.apply(null,arguments)}function Ve(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function Ge(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ve(Object(n),!0).forEach((function(t){Ke(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ve(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Ke(e,t,n){return(t=function(e){var t=function(e){if("object"!=ze(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=ze(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==ze(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function $e(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,s=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){s=!0,r=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw r}}return l}}(e,t)||function(e,t){if(e){if("string"==typeof e)return Qe(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Qe(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Qe(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}const Ye=function(e){var t=e.isCustomPanelOpen,n=e.parentDataElement,i=void 0===n?void 0:n,a=e.dataElement,c=$e((0,l.d4)((function(e){return[k.A.isElementOpen(e,i||a||re.A.NOTES_PANEL),k.A.getNotesInLeftPanel(e),k.A.getIsNotesPanelMultiSelectEnabled(e),k.A.isMultiViewerMode(e),k.A.getActiveDocumentViewerKey(e),k.A.getIsOfficeEditorMode(e)]}),l.bN),6),s=c[0],u=c[1],d=c[2],p=c[3],m=c[4],f=c[5],h=$e((0,o.useState)(""),2),y=h[0],g=h[1],b=$e((0,o.useState)(!1),2),v=b[0],w=b[1],A=$e((0,o.useState)(!1),2),x=A[0],E=A[1],S=$e((0,o.useState)({1:[],2:[]}),2),O=S[0],N=S[1],P=(0,o.useCallback)((function(e){O[arguments.length>1&&void 0!==arguments[1]?arguments[1]:m]=e,N(Ge({},O))}),[m,O[1],O[2],N]),T=O[m]||O[1],M=$e((0,o.useState)({1:{},2:{}}),2),I=M[0],R=M[1],C=(0,o.useCallback)((function(e){I[arguments.length>1&&void 0!==arguments[1]?arguments[1]:m]=e,R(Ge({},I))}),[m,I[1],I[2],R]),j=I[m]||I[1],_=$e((0,o.useState)({1:{},2:{}}),2),L=_[0],H=_[1],D=(0,o.useCallback)((function(e){L[arguments.length>1&&void 0!==arguments[1]?arguments[1]:m]=e,H(Ge({},L))}),[m,L[1],L[2],H]),q=L[m]||L[1];function F(e){var t=e.find((function(e){return e.isGrouped()})),n=[];return t&&e.forEach((function(e){t.InReplyTo!==e.InReplyTo&&t.InReplyTo!==e.Id||n.push(e)})),n}(0,o.useEffect)((function(){var e=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:m;return function(){P([],e),C({}),g("")}},t=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:m;return function(){var t=r.A.getSelectedAnnotations(e),n=F(t);v&&n.length===t.length&&w(!1),P(r.A.getAnnotationsList(e).filter((function(e){return e.Listable&&!e.isReply()&&!e.Hidden&&!e.isGrouped()&&e.ToolName!==window.Core.Tools.ToolNames.CROP&&!e.isContentEditPlaceholder()&&(!f||(0,K.c7)(e)===K.oC.TRACKED_CHANGE)})),e)}},n=e(1);r.A.addEventListener("documentUnloaded",n);var o,i,a=t(1);return r.A.addEventListener("annotationChanged",a),r.A.addEventListener("annotationHidden",a),r.A.addEventListener("updateAnnotationPermission",a),a(),p&&(o=e(2),r.A.addEventListener("documentUnloaded",o,void 0,2),i=t(2),r.A.addEventListener("annotationChanged",i,void 0,2),r.A.addEventListener("annotationHidden",i,void 0,2),r.A.addEventListener("updateAnnotationPermission",i,void 0,2),i()),function(){p&&(r.A.removeEventListener("documentUnloaded",o,2),r.A.removeEventListener("annotationChanged",i,2),r.A.removeEventListener("annotationHidden",i,2),r.A.removeEventListener("updateAnnotationPermission",i,2)),r.A.removeEventListener("documentUnloaded",n),r.A.removeEventListener("annotationChanged",a),r.A.removeEventListener("annotationHidden",a),r.A.removeEventListener("updateAnnotationPermission",a)}}),[p,f]),(0,o.useEffect)((function(){var e,n=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:m;return function(n,o){var i=r.A.getSelectedAnnotations(e),a={};i.forEach((function(e){a[e.Id]=!0})),(t||s||u)&&(C(a,e),E(!0));var l=F(i),c=i.length>1&&l.length!==i.length||v;d&&"selected"===o&&c?(w(!0),i.forEach((function(e){q[e.Id]=e})),D(Ge({},q),e)):"deselected"===o&&(n.forEach((function(e){delete q[e.Id]})),D(Ge({},q),e))}},o=n(1);return o(),r.A.addEventListener("annotationSelected",o),p&&((e=n(2))(),r.A.addEventListener("annotationSelected",e,void 0,2)),function(){r.A.removeEventListener("annotationSelected",o),p&&r.A.removeEventListener("annotationSelected",e,2)}}),[t,s,u,v,q,d,p]);var U={notes:T,selectedNoteIds:j,setSelectedNoteIds:C,searchInput:y,setSearchInput:g,isMultiSelectMode:v,setMultiSelectMode:w,isMultiSelectedMap:q,setIsMultiSelectedMap:D,scrollToSelectedAnnot:x,setScrollToSelectedAnnot:E};return o.createElement(Be,We({},e,U))}},66697:(e,t,n)=>{(t=n(76314)(!1)).push([e.id,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.multi-reply-place-holder{height:100px}.reply-area-multi-container{border-top:1px solid var(--divider);display:flex;flex-direction:column;width:100%;position:absolute;bottom:0;z-index:100;background:var(--panel-background)}.reply-area-multi-container form{width:100%;display:flex}.reply-area-multi-container .reply-area-multi-header{display:flex;flex-direction:row;margin-left:16px;margin-top:28px;position:relative;justify-content:space-between}.reply-area-multi-container .reply-area-multi-header .title{font-size:13px;font-weight:700;display:flex;align-items:center}.reply-area-multi-container .reply-area-multi-header .close-icon{display:flex;align-items:center;padding-right:12px;cursor:pointer;position:relative;top:1px}.reply-area-multi-container .reply-area-multi-header .close-icon .close-icon{width:22px;height:22px}.reply-area-multi-container .reply-button-container{display:flex;justify-content:flex-end;flex-direction:column}.reply-area-multi-container .reply-button{width:28px;height:28px;padding:0;border:none;background-color:transparent;right:10px;bottom:12px}:host(:not([data-tabbing=true])) .reply-area-multi-container .reply-button,html:not([data-tabbing=true]) .reply-area-multi-container .reply-button{outline:none}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.reply-area-multi-container .reply-button{width:80px}}.reply-area-multi-container .reply-button:hover{background:var(--blue-1)}.reply-area-multi-container .reply-button.disabled{cursor:not-allowed}.reply-area-multi-container .reply-area{position:relative;flex:1;margin:13px 17px 12px 16px;border-radius:4px;align-items:center;background:var(--component-background)}.reply-area-multi-container .reply-area.unread{background:rgba(0,165,228,.08)}.reply-area-multi-container .reply-area .comment-textarea .ql-container .ql-editor.ql-blank{padding:4px}.reply-area-multi-container .reply-area .comment-textarea .ql-container .ql-editor.ql-blank:before{left:4px}.reply-area-multi-container .add-attachment{display:none}.reply-area-multi-container.modular-ui{padding:16px 16px 24px}.reply-area-multi-container.modular-ui form{margin-bottom:0}.reply-area-multi-container.modular-ui .reply-area{margin:13px 16px 24px 0}.reply-area-multi-container.modular-ui .reply-area-multi-header{margin-left:0;margin-top:0}.reply-area-multi-container.modular-ui .reply-area-multi-header .close-icon{width:28px;height:28px}.reply-area-multi-container.modular-ui .close-icon{padding-right:0;margin-right:16px}.reply-area-multi-container.modular-ui .reply-button-container{margin-right:16px;margin-bottom:12px}.reply-area-multi-container.modular-ui .reply-button{left:0;right:0}",""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"},e.exports=t},90038:(e,t,n)=>{(t=n(76314)(!1)).push([e.id,".multi-style-container{border-radius:4px;box-shadow:0 0 3px 0 var(--document-box-shadow);background:var(--component-background);position:relative;right:20px;bottom:52px;pointer-events:auto}.multi-style-container *{box-sizing:border-box}",""]),e.exports=t}}]);
//# sourceMappingURL=9151.chunk.js.map