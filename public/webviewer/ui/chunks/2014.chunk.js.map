{"version": 3, "file": "chunks/2014.chunk.js", "mappings": ";+FAEAA,EADkC,EAAQ,MAChCC,EAA4B,IAE9BC,KAAK,CAACC,EAAOC,GAAI,ghQAAihQ,KAE1iQD,EAAOH,QAAUA,qlBCLjBK,EAAA,kBAAAC,CAAA,MAAAC,EAAAD,EAAA,GAAAE,EAAAC,OAAAC,UAAAC,EAAAH,EAAAI,eAAAC,EAAAJ,OAAAK,gBAAA,SAAAP,EAAAD,EAAAE,GAAAD,EAAAD,GAAAE,EAAAO,KAAA,EAAAC,EAAA,mBAAAC,OAAAA,OAAA,GAAAC,EAAAF,EAAAG,UAAA,aAAAC,EAAAJ,EAAAK,eAAA,kBAAAC,EAAAN,EAAAO,aAAA,yBAAAC,EAAAjB,EAAAD,EAAAE,GAAA,OAAAC,OAAAK,eAAAP,EAAAD,EAAA,CAAAS,MAAAP,EAAAiB,YAAA,EAAAC,cAAA,EAAAC,UAAA,IAAApB,EAAAD,EAAA,KAAAkB,EAAA,aAAAjB,GAAAiB,EAAA,SAAAjB,EAAAD,EAAAE,GAAA,OAAAD,EAAAD,GAAAE,CAAA,WAAAoB,EAAArB,EAAAD,EAAAE,EAAAG,GAAA,IAAAK,EAAAV,GAAAA,EAAAI,qBAAAmB,EAAAvB,EAAAuB,EAAAX,EAAAT,OAAAqB,OAAAd,EAAAN,WAAAU,EAAA,IAAAW,EAAApB,GAAA,WAAAE,EAAAK,EAAA,WAAAH,MAAAiB,EAAAzB,EAAAC,EAAAY,KAAAF,CAAA,UAAAe,EAAA1B,EAAAD,EAAAE,GAAA,WAAA0B,KAAA,SAAAC,IAAA5B,EAAA6B,KAAA9B,EAAAE,GAAA,OAAAD,GAAA,OAAA2B,KAAA,QAAAC,IAAA5B,EAAA,EAAAD,EAAAsB,KAAAA,EAAA,IAAAS,EAAA,iBAAAC,EAAA,iBAAAC,EAAA,YAAAC,EAAA,YAAAC,EAAA,YAAAZ,IAAA,UAAAa,IAAA,UAAAC,IAAA,KAAAC,EAAA,GAAApB,EAAAoB,EAAA1B,GAAA,8BAAA2B,EAAApC,OAAAqC,eAAAC,EAAAF,GAAAA,EAAAA,EAAAG,EAAA,MAAAD,GAAAA,IAAAvC,GAAAG,EAAAyB,KAAAW,EAAA7B,KAAA0B,EAAAG,GAAA,IAAAE,EAAAN,EAAAjC,UAAAmB,EAAAnB,UAAAD,OAAAqB,OAAAc,GAAA,SAAAM,EAAA3C,GAAA,0BAAA4C,SAAA,SAAA7C,GAAAkB,EAAAjB,EAAAD,GAAA,SAAAC,GAAA,YAAA6C,QAAA9C,EAAAC,EAAA,gBAAA8C,EAAA9C,EAAAD,GAAA,SAAAgD,EAAA9C,EAAAK,EAAAG,EAAAE,GAAA,IAAAE,EAAAa,EAAA1B,EAAAC,GAAAD,EAAAM,GAAA,aAAAO,EAAAc,KAAA,KAAAZ,EAAAF,EAAAe,IAAAE,EAAAf,EAAAP,MAAA,OAAAsB,GAAA,UAAAkB,EAAAlB,IAAA1B,EAAAyB,KAAAC,EAAA,WAAA/B,EAAAkD,QAAAnB,EAAAoB,SAAAC,MAAA,SAAAnD,GAAA+C,EAAA,OAAA/C,EAAAS,EAAAE,EAAA,aAAAX,GAAA+C,EAAA,QAAA/C,EAAAS,EAAAE,EAAA,IAAAZ,EAAAkD,QAAAnB,GAAAqB,MAAA,SAAAnD,GAAAe,EAAAP,MAAAR,EAAAS,EAAAM,EAAA,aAAAf,GAAA,OAAA+C,EAAA,QAAA/C,EAAAS,EAAAE,EAAA,IAAAA,EAAAE,EAAAe,IAAA,KAAA3B,EAAAK,EAAA,gBAAAE,MAAA,SAAAR,EAAAI,GAAA,SAAAgD,IAAA,WAAArD,GAAA,SAAAA,EAAAE,GAAA8C,EAAA/C,EAAAI,EAAAL,EAAAE,EAAA,WAAAA,EAAAA,EAAAA,EAAAkD,KAAAC,EAAAA,GAAAA,GAAA,aAAA3B,EAAA1B,EAAAE,EAAAG,GAAA,IAAAE,EAAAwB,EAAA,gBAAArB,EAAAE,GAAA,GAAAL,IAAA0B,EAAA,MAAAqB,MAAA,mCAAA/C,IAAA2B,EAAA,cAAAxB,EAAA,MAAAE,EAAA,OAAAH,MAAAR,EAAAsD,MAAA,OAAAlD,EAAAmD,OAAA9C,EAAAL,EAAAwB,IAAAjB,IAAA,KAAAE,EAAAT,EAAAoD,SAAA,GAAA3C,EAAA,KAAAE,EAAA0C,EAAA5C,EAAAT,GAAA,GAAAW,EAAA,IAAAA,IAAAmB,EAAA,gBAAAnB,CAAA,cAAAX,EAAAmD,OAAAnD,EAAAsD,KAAAtD,EAAAuD,MAAAvD,EAAAwB,SAAA,aAAAxB,EAAAmD,OAAA,IAAAjD,IAAAwB,EAAA,MAAAxB,EAAA2B,EAAA7B,EAAAwB,IAAAxB,EAAAwD,kBAAAxD,EAAAwB,IAAA,gBAAAxB,EAAAmD,QAAAnD,EAAAyD,OAAA,SAAAzD,EAAAwB,KAAAtB,EAAA0B,EAAA,IAAAK,EAAAX,EAAA3B,EAAAE,EAAAG,GAAA,cAAAiC,EAAAV,KAAA,IAAArB,EAAAF,EAAAkD,KAAArB,EAAAF,EAAAM,EAAAT,MAAAM,EAAA,gBAAA1B,MAAA6B,EAAAT,IAAA0B,KAAAlD,EAAAkD,KAAA,WAAAjB,EAAAV,OAAArB,EAAA2B,EAAA7B,EAAAmD,OAAA,QAAAnD,EAAAwB,IAAAS,EAAAT,IAAA,YAAA6B,EAAA1D,EAAAE,GAAA,IAAAG,EAAAH,EAAAsD,OAAAjD,EAAAP,EAAAa,SAAAR,GAAA,GAAAE,IAAAN,EAAA,OAAAC,EAAAuD,SAAA,eAAApD,GAAAL,EAAAa,SAAA,SAAAX,EAAAsD,OAAA,SAAAtD,EAAA2B,IAAA5B,EAAAyD,EAAA1D,EAAAE,GAAA,UAAAA,EAAAsD,SAAA,WAAAnD,IAAAH,EAAAsD,OAAA,QAAAtD,EAAA2B,IAAA,IAAAkC,UAAA,oCAAA1D,EAAA,aAAA8B,EAAA,IAAAzB,EAAAiB,EAAApB,EAAAP,EAAAa,SAAAX,EAAA2B,KAAA,aAAAnB,EAAAkB,KAAA,OAAA1B,EAAAsD,OAAA,QAAAtD,EAAA2B,IAAAnB,EAAAmB,IAAA3B,EAAAuD,SAAA,KAAAtB,EAAA,IAAAvB,EAAAF,EAAAmB,IAAA,OAAAjB,EAAAA,EAAA2C,MAAArD,EAAAF,EAAAgE,YAAApD,EAAAH,MAAAP,EAAA+D,KAAAjE,EAAAkE,QAAA,WAAAhE,EAAAsD,SAAAtD,EAAAsD,OAAA,OAAAtD,EAAA2B,IAAA5B,GAAAC,EAAAuD,SAAA,KAAAtB,GAAAvB,GAAAV,EAAAsD,OAAA,QAAAtD,EAAA2B,IAAA,IAAAkC,UAAA,oCAAA7D,EAAAuD,SAAA,KAAAtB,EAAA,UAAAgC,EAAAlE,GAAA,IAAAD,EAAA,CAAAoE,OAAAnE,EAAA,SAAAA,IAAAD,EAAAqE,SAAApE,EAAA,SAAAA,IAAAD,EAAAsE,WAAArE,EAAA,GAAAD,EAAAuE,SAAAtE,EAAA,SAAAuE,WAAA5E,KAAAI,EAAA,UAAAyE,EAAAxE,GAAA,IAAAD,EAAAC,EAAAyE,YAAA,GAAA1E,EAAA4B,KAAA,gBAAA5B,EAAA6B,IAAA5B,EAAAyE,WAAA1E,CAAA,UAAAyB,EAAAxB,GAAA,KAAAuE,WAAA,EAAAJ,OAAA,SAAAnE,EAAA4C,QAAAsB,EAAA,WAAAQ,OAAA,YAAAjC,EAAA1C,GAAA,GAAAA,GAAA,KAAAA,EAAA,KAAAE,EAAAF,EAAAY,GAAA,GAAAV,EAAA,OAAAA,EAAA4B,KAAA9B,GAAA,sBAAAA,EAAAiE,KAAA,OAAAjE,EAAA,IAAA4E,MAAA5E,EAAA6E,QAAA,KAAAtE,GAAA,EAAAG,EAAA,SAAAuD,IAAA,OAAA1D,EAAAP,EAAA6E,QAAA,GAAAxE,EAAAyB,KAAA9B,EAAAO,GAAA,OAAA0D,EAAAxD,MAAAT,EAAAO,GAAA0D,EAAAV,MAAA,EAAAU,EAAA,OAAAA,EAAAxD,MAAAR,EAAAgE,EAAAV,MAAA,EAAAU,CAAA,SAAAvD,EAAAuD,KAAAvD,CAAA,YAAAqD,UAAAd,EAAAjD,GAAA,2BAAAoC,EAAAhC,UAAAiC,EAAA9B,EAAAoC,EAAA,eAAAlC,MAAA4B,EAAAjB,cAAA,IAAAb,EAAA8B,EAAA,eAAA5B,MAAA2B,EAAAhB,cAAA,IAAAgB,EAAA0C,YAAA5D,EAAAmB,EAAArB,EAAA,qBAAAhB,EAAA+E,oBAAA,SAAA9E,GAAA,IAAAD,EAAA,mBAAAC,GAAAA,EAAA+E,YAAA,QAAAhF,IAAAA,IAAAoC,GAAA,uBAAApC,EAAA8E,aAAA9E,EAAAiF,MAAA,EAAAjF,EAAAkF,KAAA,SAAAjF,GAAA,OAAAE,OAAAgF,eAAAhF,OAAAgF,eAAAlF,EAAAoC,IAAApC,EAAAmF,UAAA/C,EAAAnB,EAAAjB,EAAAe,EAAA,sBAAAf,EAAAG,UAAAD,OAAAqB,OAAAmB,GAAA1C,CAAA,EAAAD,EAAAqF,MAAA,SAAApF,GAAA,OAAAkD,QAAAlD,EAAA,EAAA2C,EAAAG,EAAA3C,WAAAc,EAAA6B,EAAA3C,UAAAU,GAAA,0BAAAd,EAAA+C,cAAAA,EAAA/C,EAAAsF,MAAA,SAAArF,EAAAC,EAAAG,EAAAE,EAAAG,QAAA,IAAAA,IAAAA,EAAA6E,SAAA,IAAA3E,EAAA,IAAAmC,EAAAzB,EAAArB,EAAAC,EAAAG,EAAAE,GAAAG,GAAA,OAAAV,EAAA+E,oBAAA7E,GAAAU,EAAAA,EAAAqD,OAAAb,MAAA,SAAAnD,GAAA,OAAAA,EAAAsD,KAAAtD,EAAAQ,MAAAG,EAAAqD,MAAA,KAAArB,EAAAD,GAAAzB,EAAAyB,EAAA3B,EAAA,aAAAE,EAAAyB,EAAA/B,GAAA,0BAAAM,EAAAyB,EAAA,qDAAA3C,EAAAwF,KAAA,SAAAvF,GAAA,IAAAD,EAAAG,OAAAF,GAAAC,EAAA,WAAAG,KAAAL,EAAAE,EAAAN,KAAAS,GAAA,OAAAH,EAAAuF,UAAA,SAAAxB,IAAA,KAAA/D,EAAA2E,QAAA,KAAA5E,EAAAC,EAAAwF,MAAA,GAAAzF,KAAAD,EAAA,OAAAiE,EAAAxD,MAAAR,EAAAgE,EAAAV,MAAA,EAAAU,CAAA,QAAAA,EAAAV,MAAA,EAAAU,CAAA,GAAAjE,EAAA0C,OAAAA,EAAAjB,EAAArB,UAAA,CAAA4E,YAAAvD,EAAAkD,MAAA,SAAA3E,GAAA,QAAA2F,KAAA,OAAA1B,KAAA,OAAAN,KAAA,KAAAC,MAAA3D,EAAA,KAAAsD,MAAA,OAAAE,SAAA,UAAAD,OAAA,YAAA3B,IAAA5B,EAAA,KAAAuE,WAAA3B,QAAA4B,IAAAzE,EAAA,QAAAE,KAAA,WAAAA,EAAA0F,OAAA,IAAAvF,EAAAyB,KAAA,KAAA5B,KAAA0E,OAAA1E,EAAA2F,MAAA,WAAA3F,GAAAD,EAAA,EAAA6F,KAAA,gBAAAvC,MAAA,MAAAtD,EAAA,KAAAuE,WAAA,GAAAE,WAAA,aAAAzE,EAAA2B,KAAA,MAAA3B,EAAA4B,IAAA,YAAAkE,IAAA,EAAAlC,kBAAA,SAAA7D,GAAA,QAAAuD,KAAA,MAAAvD,EAAA,IAAAE,EAAA,cAAA8F,EAAA3F,EAAAE,GAAA,OAAAK,EAAAgB,KAAA,QAAAhB,EAAAiB,IAAA7B,EAAAE,EAAA+D,KAAA5D,EAAAE,IAAAL,EAAAsD,OAAA,OAAAtD,EAAA2B,IAAA5B,KAAAM,CAAA,SAAAA,EAAA,KAAAiE,WAAAK,OAAA,EAAAtE,GAAA,IAAAA,EAAA,KAAAG,EAAA,KAAA8D,WAAAjE,GAAAK,EAAAF,EAAAgE,WAAA,YAAAhE,EAAA0D,OAAA,OAAA4B,EAAA,UAAAtF,EAAA0D,QAAA,KAAAuB,KAAA,KAAA7E,EAAAT,EAAAyB,KAAApB,EAAA,YAAAM,EAAAX,EAAAyB,KAAApB,EAAA,iBAAAI,GAAAE,EAAA,SAAA2E,KAAAjF,EAAA2D,SAAA,OAAA2B,EAAAtF,EAAA2D,UAAA,WAAAsB,KAAAjF,EAAA4D,WAAA,OAAA0B,EAAAtF,EAAA4D,WAAA,SAAAxD,GAAA,QAAA6E,KAAAjF,EAAA2D,SAAA,OAAA2B,EAAAtF,EAAA2D,UAAA,YAAArD,EAAA,MAAAsC,MAAA,kDAAAqC,KAAAjF,EAAA4D,WAAA,OAAA0B,EAAAtF,EAAA4D,WAAA,KAAAR,OAAA,SAAA7D,EAAAD,GAAA,QAAAE,EAAA,KAAAsE,WAAAK,OAAA,EAAA3E,GAAA,IAAAA,EAAA,KAAAK,EAAA,KAAAiE,WAAAtE,GAAA,GAAAK,EAAA6D,QAAA,KAAAuB,MAAAtF,EAAAyB,KAAAvB,EAAA,oBAAAoF,KAAApF,EAAA+D,WAAA,KAAA5D,EAAAH,EAAA,OAAAG,IAAA,UAAAT,GAAA,aAAAA,IAAAS,EAAA0D,QAAApE,GAAAA,GAAAU,EAAA4D,aAAA5D,EAAA,UAAAE,EAAAF,EAAAA,EAAAgE,WAAA,UAAA9D,EAAAgB,KAAA3B,EAAAW,EAAAiB,IAAA7B,EAAAU,GAAA,KAAA8C,OAAA,YAAAS,KAAAvD,EAAA4D,WAAAnC,GAAA,KAAA8D,SAAArF,EAAA,EAAAqF,SAAA,SAAAhG,EAAAD,GAAA,aAAAC,EAAA2B,KAAA,MAAA3B,EAAA4B,IAAA,gBAAA5B,EAAA2B,MAAA,aAAA3B,EAAA2B,KAAA,KAAAqC,KAAAhE,EAAA4B,IAAA,WAAA5B,EAAA2B,MAAA,KAAAmE,KAAA,KAAAlE,IAAA5B,EAAA4B,IAAA,KAAA2B,OAAA,cAAAS,KAAA,kBAAAhE,EAAA2B,MAAA5B,IAAA,KAAAiE,KAAAjE,GAAAmC,CAAA,EAAA+D,OAAA,SAAAjG,GAAA,QAAAD,EAAA,KAAAwE,WAAAK,OAAA,EAAA7E,GAAA,IAAAA,EAAA,KAAAE,EAAA,KAAAsE,WAAAxE,GAAA,GAAAE,EAAAoE,aAAArE,EAAA,YAAAgG,SAAA/F,EAAAwE,WAAAxE,EAAAqE,UAAAE,EAAAvE,GAAAiC,CAAA,kBAAAlC,GAAA,QAAAD,EAAA,KAAAwE,WAAAK,OAAA,EAAA7E,GAAA,IAAAA,EAAA,KAAAE,EAAA,KAAAsE,WAAAxE,GAAA,GAAAE,EAAAkE,SAAAnE,EAAA,KAAAI,EAAAH,EAAAwE,WAAA,aAAArE,EAAAuB,KAAA,KAAArB,EAAAF,EAAAwB,IAAA4C,EAAAvE,EAAA,QAAAK,CAAA,QAAA+C,MAAA,0BAAA6C,cAAA,SAAAnG,EAAAE,EAAAG,GAAA,YAAAoD,SAAA,CAAA5C,SAAA6B,EAAA1C,GAAAgE,WAAA9D,EAAAgE,QAAA7D,GAAA,cAAAmD,SAAA,KAAA3B,IAAA5B,GAAAkC,CAAA,GAAAnC,CAAA,UAAAoG,EAAA/F,EAAAJ,EAAAD,EAAAE,EAAAK,EAAAK,EAAAE,GAAA,QAAAJ,EAAAL,EAAAO,GAAAE,GAAAE,EAAAN,EAAAD,KAAA,OAAAJ,GAAA,YAAAL,EAAAK,EAAA,CAAAK,EAAA6C,KAAAtD,EAAAe,GAAAuE,QAAArC,QAAAlC,GAAAoC,KAAAlD,EAAAK,EAAA,UAAA8F,EAAArG,EAAAE,GAAA,IAAAD,EAAAE,OAAAqF,KAAAxF,GAAA,GAAAG,OAAAmG,sBAAA,KAAA/F,EAAAJ,OAAAmG,sBAAAtG,GAAAE,IAAAK,EAAAA,EAAAgG,QAAA,SAAArG,GAAA,OAAAC,OAAAqG,yBAAAxG,EAAAE,GAAAiB,UAAA,KAAAlB,EAAAL,KAAA6G,MAAAxG,EAAAM,EAAA,QAAAN,CAAA,UAAAyG,EAAA1G,GAAA,QAAAE,EAAA,EAAAA,EAAAyG,UAAA9B,OAAA3E,IAAA,KAAAD,EAAA,MAAA0G,UAAAzG,GAAAyG,UAAAzG,GAAA,GAAAA,EAAA,EAAAmG,EAAAlG,OAAAF,IAAA,GAAA4C,SAAA,SAAA3C,GAAA0G,EAAA5G,EAAAE,EAAAD,EAAAC,GAAA,IAAAC,OAAA0G,0BAAA1G,OAAA2G,iBAAA9G,EAAAG,OAAA0G,0BAAA5G,IAAAoG,EAAAlG,OAAAF,IAAA4C,SAAA,SAAA3C,GAAAC,OAAAK,eAAAR,EAAAE,EAAAC,OAAAqG,yBAAAvG,EAAAC,GAAA,WAAAF,CAAA,UAAA4G,EAAA5G,EAAAE,EAAAD,GAAA,OAAAC,EAAA,SAAAD,GAAA,IAAAS,EAAA,SAAAT,GAAA,aAAAgD,EAAAhD,KAAAA,EAAA,OAAAA,EAAA,IAAAD,EAAAC,EAAAU,OAAAoG,aAAA,YAAA/G,EAAA,KAAAU,EAAAV,EAAA8B,KAAA7B,EAAAC,UAAA,aAAA+C,EAAAvC,GAAA,OAAAA,EAAA,UAAAqD,UAAA,uDAAAiD,OAAA/G,EAAA,CAAAgH,CAAAhH,GAAA,gBAAAgD,EAAAvC,GAAAA,EAAAA,EAAA,GAAAwG,CAAAhH,MAAAF,EAAAG,OAAAK,eAAAR,EAAAE,EAAA,CAAAO,MAAAR,EAAAkB,YAAA,EAAAC,cAAA,EAAAC,UAAA,IAAArB,EAAAE,GAAAD,EAAAD,CAAA,CAIA,IAAMmH,EAAmB,SAACC,EAAQC,EAAMC,GAAK,MAAM,CACjDD,KAAAA,EACAC,MAAAA,EACAC,MAAOD,EACPF,OAAAA,EACAI,YAAa,GAAFC,OAAKL,EAAO,GAAGM,cAAiBN,EAAOvB,MAAM,GAAE,UAC3D,EAEY8B,EAAY,CACvBC,SAAU,WACVC,OAAQ,SACRC,QAAS,iBACTC,SAAU,WACVC,OAAQ,UAGGC,EAAY,CACvBd,EAAiBQ,EAAUC,SAAU,sBAAuB,sBAC5DT,EAAiBQ,EAAUE,OAAQ,oBAAqB,iBACxDV,EAAiBQ,EAAUG,QAAS,iBAAkB,yBACtDX,EAAiBQ,EAAUI,SAAU,gBAAiB,mBACtDZ,EAAiBQ,EAAUK,OAAQ,mBAAoB,kBAGnDE,EAA+B,SAAHC,GAM5B,IALJvG,EAAIuG,EAAJvG,KACAwG,EAAaD,EAAbC,cACAC,EAAaF,EAAbE,cACAC,EAAcH,EAAdG,eACAC,EAAsBJ,EAAtBI,uBAEMC,GAAWC,EAAAA,EAAAA,MAmCjB,OAjCAC,EAAAA,EAAAA,kBAAgB,WACd,IAAMC,EAAwB,CAC5BnB,YAAac,EACbM,UAAW,+BACXC,MAAOZ,EAAUa,KAAI,SAACC,GACpB,IAAQ3B,EAAW2B,EAAX3B,OACJ4B,GAAS,EASb,OARI5B,IAAWO,EAAUK,OACvBgB,EAAST,EACAnB,IAAWO,EAAUI,UAAYX,IAAWO,EAAUC,SAC/DoB,EAAkB,cAATpH,EACAwF,IAAWO,EAAUG,UAC9BkB,EAAkB,YAATpH,GAGX8E,EAAAA,EAAA,GACKqC,GAAI,IACPC,OAAAA,EACAxB,YAAa,GAAFC,OAAK7F,GAAI6F,OAAGsB,EAAKvB,aAC5ByB,QAAS,WAAF,OAAQb,EAAcW,EAAK3B,OAAO,GAE7C,KAEwB,SAAA8B,IA5D9B,IAAA7I,EAkEK,OAlELA,EA4D8BN,IAAAmF,MAA1B,SAAAiE,IAAA,OAAApJ,IAAAuB,MAAA,SAAA8H,GAAA,cAAAA,EAAAzD,KAAAyD,EAAAnF,MAAA,OAIIuE,EAHGH,EAGMgB,EAAAA,EAAQC,aAAaX,EAAsBnB,YAAamB,GAFxDU,EAAAA,EAAQE,UAAUZ,IAG5B,wBAAAS,EAAAtD,OAAA,GAAAqD,EAAA,IALuBD,EA5D9B,eAAAjJ,EAAA,KAAAD,EAAA2G,UAAA,WAAApB,SAAA,SAAArF,EAAAK,GAAA,IAAAK,EAAAP,EAAAoG,MAAAxG,EAAAD,GAAA,SAAAwJ,EAAAnJ,GAAA+F,EAAAxF,EAAAV,EAAAK,EAAAiJ,EAAAC,EAAA,OAAApJ,EAAA,UAAAoJ,EAAApJ,GAAA+F,EAAAxF,EAAAV,EAAAK,EAAAiJ,EAAAC,EAAA,QAAApJ,EAAA,CAAAmJ,OAAA,OAkEKN,EAAAzC,MAAA,KAAAE,UAAA,EAPC,WACwBuC,EAAAzC,MAAC,KAADE,UAAA,CAO1B+C,EACF,GAAG,IAEI,IACT,EAGAxB,EAA6ByB,UAAY,CACvC/H,KAAMgI,IAAAA,MAAgB,CAAC,WAAY,UAAW,cAAcC,WAC5DzB,cAAewB,IAAAA,KACfvB,cAAeuB,IAAAA,OACftB,eAAgBsB,IAAAA,OAChBrB,uBAAwBqB,IAAAA,MAG1B,2BCnFA,IAAIE,EAAM,EAAQ,OACFC,EAAU,EAAQ,KAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAAClK,EAAOC,GAAIiK,EAAS,MAwDjCD,EAAIC,EArDH,CAEdG,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAc5F,SACjB4F,EAzBF,SAASE,EAAwBC,EAASC,EAAOP,UAC/C,MAAMQ,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAAS/H,SAAQmI,GAAMF,EAASlL,KAAKoL,KAG3DH,EAAKE,iBAAiB,KAAKlI,SAAQmI,IAC7BA,EAAGC,YACLH,EAASlL,QAAQ+K,EAAwBC,EAASI,EAAGC,YACvD,IAGKH,CACT,CAWkBH,CAAwB,qBAG1C,MAAMO,EAAkB,GACxB,IAAK,IAAIxK,EAAI,EAAGA,EAAI+J,EAAc5F,OAAQnE,IAAK,CAC7C,MAAMyK,EAAeV,EAAc/J,GACnC,GAAU,IAANA,EACFyK,EAAaF,WAAWT,YAAYL,GACpCA,EAASiB,OAAS,WACZF,EAAgBrG,OAAS,GAC3BqG,EAAgBrI,SAASwI,IAEvBA,EAAUC,UAAYnB,EAASmB,SAAS,GAG9C,MACK,CACL,MAAMD,EAAYlB,EAASoB,WAAU,GACrCJ,EAAaF,WAAWT,YAAYa,GACpCH,EAAgBtL,KAAKyL,EACvB,CACF,CACF,EACdnB,WAAoB,IAMpBrK,EAAOH,QAAUqK,EAAQyB,QAAU,CAAC", "sources": ["webpack://webviewer-ui/./src/constants/bookmarksOutlinesShared.scss", "webpack://webviewer-ui/./src/components/MoreOptionsContextMenuFlyout/MoreOptionsContextMenuFlyout.js", "webpack://webviewer-ui/./src/constants/bookmarksOutlinesShared.scss?6867"], "sourcesContent": ["// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/api.js\");\nexports = ___CSS_LOADER_API_IMPORT___(false);\n// Module\nexports.push([module.id, \".bookmark-outline-panel{display:flex;padding-left:var(--padding);padding-right:var(--padding-small)}.bookmark-outline-control-button{width:auto}.bookmark-outline-control-button span{color:inherit}.bookmark-outline-control-button,.bookmark-outline-control-button.disabled,.bookmark-outline-control-button[disabled]{color:var(--secondary-button-text)}.bookmark-outline-control-button.disabled,.bookmark-outline-control-button[disabled]{opacity:.5}.bookmark-outline-control-button.disabled span,.bookmark-outline-control-button[disabled] span{color:inherit}.bookmark-outline-control-button:not(.disabled):active,.bookmark-outline-control-button:not(.disabled):hover,.bookmark-outline-control-button:not([disabled]):active,.bookmark-outline-control-button:not([disabled]):hover{color:var(--secondary-button-hover)}.bookmark-outline-panel-header{display:flex;flex-flow:row nowrap;justify-content:space-between;align-items:center;padding:var(--padding-tiny);border-bottom:1px solid var(--divider)}.bookmark-outline-panel-header .header-title{font-size:16px}.bookmark-outline-row{flex-grow:1;overflow-y:auto}.msg-no-bookmark-outline{color:var(--placeholder-text);text-align:center}.bookmark-outline-single-container{display:flex;flex-flow:row nowrap;align-items:flex-start;border-radius:4px;margin-left:2px;margin-right:2px}.bookmark-outline-single-container.default{padding:var(--padding-tiny);border:1px solid transparent}.bookmark-outline-single-container.default.hover,.bookmark-outline-single-container.default:hover,.bookmark-outline-single-container.default[focus-within]{cursor:pointer}.bookmark-outline-single-container.default.hover,.bookmark-outline-single-container.default:focus-within,.bookmark-outline-single-container.default:hover{cursor:pointer}.bookmark-outline-single-container.default.hover .bookmark-outline-more-button,.bookmark-outline-single-container.default:hover .bookmark-outline-more-button,.bookmark-outline-single-container.default[focus-within] .bookmark-outline-more-button{display:flex;background-color:transparent}.bookmark-outline-single-container.default.hover .bookmark-outline-more-button,.bookmark-outline-single-container.default:focus-within .bookmark-outline-more-button,.bookmark-outline-single-container.default:hover .bookmark-outline-more-button{display:flex;background-color:transparent}.bookmark-outline-single-container.default:hover{outline:1px solid var(--hover-border)}.bookmark-outline-single-container.default.hover,.bookmark-outline-single-container.default.selected{background-color:var(--popup-button-active)}.bookmark-outline-single-container.default[focus-within]{border-color:transparent;outline:1px solid var(--hover-border)}.bookmark-outline-single-container.default:focus-within{border-color:transparent;outline:1px solid var(--hover-border)}.bookmark-outline-single-container.default.selected{background-color:var(--outline-selected);outline:1px solid var(--bookmark-outline-hover-border)}.bookmark-outline-single-container.default .bookmark-outline-label-row{overflow:hidden}.bookmark-outline-single-container.default.focus-visible,.bookmark-outline-single-container.default:focus-visible{outline:var(--focus-visible-outline)!important}.bookmark-outline-single-container.editing{background-color:var(--faded-component-background);padding:var(--padding-medium) 20px}.bookmark-outline-single-container.editing.focus-visible,.bookmark-outline-single-container.editing:focus-visible{outline:var(--focus-visible-outline)!important}.bookmark-outline-single-container.preview{display:inline-flex;margin-top:0;padding:var(--padding-small);background-color:var(--component-background);box-shadow:0 0 3px var(--note-box-shadow)}.bookmark-outline-single-container .bookmark-outline-checkbox{flex-grow:0;flex-shrink:0;margin-top:2px;margin-bottom:2px;margin-right:var(--padding-small)}.bookmark-outline-single-container .bookmark-outline-label-row{flex-grow:1;flex-shrink:1;display:flex;flex-flow:row wrap;align-items:flex-start;position:relative;overflow:hidden}.bookmark-outline-single-container .bookmark-outline-label{font-weight:600;flex-grow:1;flex-shrink:1;margin-bottom:var(--padding-small)}.bookmark-outline-single-container .bookmark-outline-input,.bookmark-outline-single-container .bookmark-outline-text{flex-grow:1;flex-shrink:1;flex-basis:calc(100% - 22px);margin-top:2px;margin-bottom:2px}.bookmark-outline-single-container .bookmark-text-input{margin-left:var(--padding-large)}.bookmark-outline-single-container .bookmark-outline-input{color:var(--text-color);width:calc(100% - var(--padding-large));padding:var(--padding-small);border:1px solid var(--border)}.bookmark-outline-single-container .bookmark-outline-input:focus{border-color:var(--outline-color)}.bookmark-outline-single-container .bookmark-outline-input::-moz-placeholder{color:var(--placeholder-text)}.bookmark-outline-single-container .bookmark-outline-input::placeholder{color:var(--placeholder-text)}.bookmark-outline-single-container .bookmark-outline-more-button{display:none;flex-grow:0;flex-shrink:0;width:16px;height:16px;margin:2px 2px 2px var(--padding-tiny)}.bookmark-outline-single-container .bookmark-outline-more-button .Icon{width:14px;height:14px}.bookmark-outline-single-container .bookmark-outline-more-button.icon-only:hover:not(:disabled):not(.disabled){box-shadow:none;outline:solid 1px var(--hover-border)}.bookmark-outline-single-container .bookmark-outline-more-button[focus-within].icon-only{border:none;box-shadow:none}.bookmark-outline-single-container .bookmark-outline-more-button:focus-within.icon-only{border:none;box-shadow:none}.bookmark-outline-single-container .bookmark-outline-more-button[focus-within] .Icon{color:var(--focus-border)}.bookmark-outline-single-container .bookmark-outline-more-button:focus-within .Icon{color:var(--focus-border)}.bookmark-outline-single-container .bookmark-outline-editing-controls{padding:2px;flex-basis:100%;display:flex;flex-flow:row wrap;justify-content:flex-end;align-items:center;margin-top:var(--padding-medium)}.bookmark-outline-single-container .bookmark-outline-cancel-button,.bookmark-outline-single-container .bookmark-outline-save-button{width:auto;padding:6px var(--padding)}.bookmark-outline-single-container .bookmark-outline-cancel-button{color:var(--secondary-button-text)}.bookmark-outline-single-container .bookmark-outline-cancel-button:hover{color:var(--secondary-button-hover)}.bookmark-outline-single-container .bookmark-outline-save-button{color:var(--primary-button-text);background-color:var(--primary-button);margin-left:var(--padding-tiny);border-radius:4px}.bookmark-outline-single-container .bookmark-outline-save-button:hover{background-color:var(--primary-button-hover)}.bookmark-outline-single-container .bookmark-outline-save-button.disabled,.bookmark-outline-single-container .bookmark-outline-save-button:disabled{background-color:var(--primary-button)!important;opacity:.5}.bookmark-outline-single-container .bookmark-outline-save-button.disabled span,.bookmark-outline-single-container .bookmark-outline-save-button:disabled span{color:var(--primary-button-text)}.bookmark-outline-footer{border-top:1.5px solid var(--gray-4);padding-top:var(--padding-medium);padding-bottom:var(--padding-medium);display:flex;justify-content:center;align-items:center}.bookmark-outline-footer .add-new-button .Icon{width:14px;height:14px;margin-right:var(--padding-tiny);color:inherit;fill:currentColor}.bookmark-outline-footer .add-new-button.disabled .Icon.disabled,.bookmark-outline-footer .add-new-button.disabled .Icon.disabled path,.bookmark-outline-footer .add-new-button[disabled] .Icon.disabled,.bookmark-outline-footer .add-new-button[disabled] .Icon.disabled path{color:inherit;fill:currentColor}.bookmark-outline-footer .multi-selection-button{width:auto;padding:7px}.bookmark-outline-footer .multi-selection-button .Icon{width:18px;height:18px}.bookmark-outline-footer .multi-selection-button:not(:first-child){margin-left:var(--padding-tiny)}.bookmark-outline-footer .multi-selection-button:hover{background-color:transparent}.bookmark-outline-footer .multi-selection-button.disabled:hover,.bookmark-outline-footer .multi-selection-button:disabled:hover{box-shadow:none}\", \"\"]);\n// Exports\nmodule.exports = exports;\n", "import { useLayoutEffect } from 'react';\nimport { useDispatch } from 'react-redux';\nimport actions from 'actions';\nimport PropTypes from 'prop-types';\n\nconst createFlyoutItem = (option, icon, label) => ({\n  icon,\n  label,\n  title: label,\n  option,\n  dataElement: `${option[0].toUpperCase() +  option.slice(1)}Button`,\n});\n\nexport const menuTypes = {\n  OPENFILE: 'openFile',\n  RENAME: 'rename',\n  SETDEST: 'setDestination',\n  DOWNLOAD: 'download',\n  DELETE: 'delete'\n};\n\nexport const menuItems = [\n  createFlyoutItem(menuTypes.OPENFILE, 'icon-portfolio-file', 'portfolio.openFile'),\n  createFlyoutItem(menuTypes.RENAME, 'ic_edit_page_24px', 'action.rename'),\n  createFlyoutItem(menuTypes.SETDEST, 'icon-thumbtack', 'action.setDestination'),\n  createFlyoutItem(menuTypes.DOWNLOAD, 'icon-download', 'action.download'),\n  createFlyoutItem(menuTypes.DELETE, 'icon-delete-line', 'action.delete'),\n];\n\nconst MoreOptionsContextMenuFlyout = ({\n  type,\n  handleOnClick,\n  currentFlyout,\n  flyoutSelector,\n  shouldHideDeleteButton,\n}) => {\n  const dispatch = useDispatch();\n\n  useLayoutEffect(() => {\n    const bookmarkOutlineFlyout = {\n      dataElement: flyoutSelector,\n      className: 'MoreOptionsContextMenuFlyout',\n      items: menuItems.map((item) => {\n        const { option } = item;\n        let hidden = false;\n        if (option === menuTypes.DELETE) {\n          hidden = shouldHideDeleteButton;\n        } else if (option === menuTypes.DOWNLOAD || option === menuTypes.OPENFILE) {\n          hidden = type !== 'portfolio';\n        } else if (option === menuTypes.SETDEST) {\n          hidden = type !== 'outline';\n        }\n\n        return {\n          ...item,\n          hidden,\n          dataElement: `${type}${item.dataElement}`,\n          onClick: () => handleOnClick(item.option),\n        };\n      }),\n    };\n    async function runDispatch() {\n      if (!currentFlyout) {\n        dispatch(actions.addFlyout(bookmarkOutlineFlyout));\n      } else {\n        dispatch(actions.updateFlyout(bookmarkOutlineFlyout.dataElement, bookmarkOutlineFlyout));\n      }\n    }\n    runDispatch();\n  }, []);\n\n  return null;\n};\n\n\nMoreOptionsContextMenuFlyout.propTypes = {\n  type: PropTypes.oneOf(['bookmark', 'outline', 'portfolio']).isRequired,\n  handleOnClick: PropTypes.func,\n  currentFlyout: PropTypes.object,\n  flyoutSelector: PropTypes.string,\n  shouldHideDeleteButton: PropTypes.bool,\n};\n\nexport default MoreOptionsContextMenuFlyout;", "var api = require(\"!../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../node_modules/css-loader/dist/cjs.js!../../node_modules/postcss-loader/src/index.js??ruleSet[1].rules[1].use[2]!../../node_modules/sass-loader/dist/cjs.js!./bookmarksOutlinesShared.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};"], "names": ["exports", "___CSS_LOADER_API_IMPORT___", "push", "module", "id", "_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "_typeof", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "ownKeys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "apply", "_objectSpread", "arguments", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "toPrimitive", "String", "_toPrimitive", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "createFlyoutItem", "option", "icon", "label", "title", "dataElement", "concat", "toUpperCase", "menuTypes", "OPENFILE", "RENAME", "SETDEST", "DOWNLOAD", "DELETE", "menuItems", "MoreOptionsContextMenuFlyout", "_ref", "handleOnClick", "currentFlyout", "flyoutSelector", "shouldHideDeleteButton", "dispatch", "useDispatch", "useLayoutEffect", "bookmarkOutlineFlyout", "className", "items", "map", "item", "hidden", "onClick", "_runDispatch", "_callee", "_context", "actions", "updateFlyout", "addFlyout", "_next", "_throw", "runDispatch", "propTypes", "PropTypes", "isRequired", "api", "content", "__esModule", "default", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "el", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "locals"], "sourceRoot": ""}