{"version": 3, "file": "chunks/5497.chunk.js", "mappings": "6GACkC,EAAQ,MAI1CA,CAA4B,SAAS,SAAUC,GAC7C,OAAO,SAA2BC,EAAMC,EAAYC,GAClD,OAAOH,EAAKI,KAAMH,EAAMC,EAAYC,EACtC,CACF,IAAG,E,kBCR+B,EAAQ,MAI1CJ,CAA4B,QAAQ,SAAUC,GAC5C,OAAO,SAAmBC,EAAMC,EAAYC,GAC1C,OAAOH,EAAKI,KAAMH,EAAMC,EAAYC,EACtC,CACF,G", "sources": ["webpack://webviewer-ui/./node_modules/core-js/modules/es.typed-array.uint8-clamped-array.js", "webpack://webviewer-ui/./node_modules/core-js/modules/es.typed-array.int8-array.js"], "sourcesContent": ["'use strict';\nvar createTypedArrayConstructor = require('../internals/typed-array-constructor');\n\n// `Uint8ClampedArray` constructor\n// https://tc39.es/ecma262/#sec-typedarray-objects\ncreateTypedArrayConstructor('Uint8', function (init) {\n  return function Uint8ClampedArray(data, byteOffset, length) {\n    return init(this, data, byteOffset, length);\n  };\n}, true);\n", "'use strict';\nvar createTypedArrayConstructor = require('../internals/typed-array-constructor');\n\n// `Int8Array` constructor\n// https://tc39.es/ecma262/#sec-typedarray-objects\ncreateTypedArrayConstructor('Int8', function (init) {\n  return function Int8Array(data, byteOffset, length) {\n    return init(this, data, byteOffset, length);\n  };\n});\n"], "names": ["createTypedArrayConstructor", "init", "data", "byteOffset", "length", "this"], "sourceRoot": ""}