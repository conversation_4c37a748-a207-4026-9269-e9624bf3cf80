{"version": 3, "file": "chunks/9911.chunk.js", "mappings": "sGAAoEA,EAAOC,QAAgL,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,EAAE,CAAOG,CAAEH,GAAGI,EAAE,CAACC,KAAK,QAAQC,SAAS,yDAAyDC,MAAM,KAAKC,OAAO,sFAAsFD,MAAM,KAAKE,UAAU,EAAEC,cAAc,uBAAuBH,MAAM,KAAKI,YAAY,sFAAsFJ,MAAM,KAAKK,YAAY,uBAAuBL,MAAM,KAAKM,QAAQ,SAASb,GAAG,OAAOA,CAAC,EAAEc,QAAQ,CAACC,GAAG,QAAQC,IAAI,WAAWC,EAAE,aAAaC,GAAG,0BAA0BC,IAAI,iCAAiCC,KAAK,wCAAwCC,aAAa,CAACC,OAAO,WAAWC,KAAK,WAAWC,EAAE,eAAeC,EAAE,YAAYC,GAAG,WAAWC,EAAE,YAAYC,GAAG,WAAWxB,EAAE,UAAUyB,GAAG,SAASC,EAAE,UAAUC,GAAG,SAASC,EAAE,UAAUC,GAAG,WAAW,OAAOhC,EAAEC,QAAQgC,OAAO9B,EAAE,MAAK,GAAIA,CAAE,CAAxlCD,CAAE,EAAQ,O", "sources": ["webpack://webviewer-ui/./node_modules/dayjs/locale/ug-cn.js"], "sourcesContent": ["!function(_,e){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=e(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],e):(_=\"undefined\"!=typeof globalThis?globalThis:_||self).dayjs_locale_ug_cn=e(_.dayjs)}(this,(function(_){\"use strict\";function e(_){return _&&\"object\"==typeof _&&\"default\"in _?_:{default:_}}var t=e(_),d={name:\"ug-cn\",weekdays:\"يەكشەنبە_دۈشەنبە_سەيشەنبە_چارشەنبە_پەيشەنبە_جۈمە_شەنبە\".split(\"_\"),months:\"يانۋار_فېۋرال_مارت_ئاپرېل_ماي_ئىيۇن_ئىيۇل_ئاۋغۇست_سېنتەبىر_ئۆكتەبىر_نويابىر_دېكابىر\".split(\"_\"),weekStart:1,weekdaysShort:\"يە_دۈ_سە_چا_پە_جۈ_شە\".split(\"_\"),monthsShort:\"يانۋار_فېۋرال_مارت_ئاپرېل_ماي_ئىيۇن_ئىيۇل_ئاۋغۇست_سېنتەبىر_ئۆكتەبىر_نويابىر_دېكابىر\".split(\"_\"),weekdaysMin:\"يە_دۈ_سە_چا_پە_جۈ_شە\".split(\"_\"),ordinal:function(_){return _},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"YYYY-MM-DD\",LL:\"YYYY-يىلىM-ئاينىڭD-كۈنى\",LLL:\"YYYY-يىلىM-ئاينىڭD-كۈنى، HH:mm\",LLLL:\"dddd، YYYY-يىلىM-ئاينىڭD-كۈنى، HH:mm\"},relativeTime:{future:\"%s كېيىن\",past:\"%s بۇرۇن\",s:\"نەچچە سېكونت\",m:\"بىر مىنۇت\",mm:\"%d مىنۇت\",h:\"بىر سائەت\",hh:\"%d سائەت\",d:\"بىر كۈن\",dd:\"%d كۈن\",M:\"بىر ئاي\",MM:\"%d ئاي\",y:\"بىر يىل\",yy:\"%d يىل\"}};return t.default.locale(d,null,!0),d}));"], "names": ["module", "exports", "_", "t", "default", "e", "d", "name", "weekdays", "split", "months", "weekStart", "weekdaysShort", "monthsShort", "weekdaysMin", "ordinal", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "relativeTime", "future", "past", "s", "m", "mm", "h", "hh", "dd", "M", "MM", "y", "yy", "locale"], "sourceRoot": ""}