/*! For license information please see 2014.chunk.js.LICENSE.txt */
(self.webpackChunkwebviewer_ui=self.webpackChunkwebviewer_ui||[]).push([[2014],{856:(o,t,e)=>{(t=e(76314)(!1)).push([o.id,".bookmark-outline-panel{display:flex;padding-left:var(--padding);padding-right:var(--padding-small)}.bookmark-outline-control-button{width:auto}.bookmark-outline-control-button span{color:inherit}.bookmark-outline-control-button,.bookmark-outline-control-button.disabled,.bookmark-outline-control-button[disabled]{color:var(--secondary-button-text)}.bookmark-outline-control-button.disabled,.bookmark-outline-control-button[disabled]{opacity:.5}.bookmark-outline-control-button.disabled span,.bookmark-outline-control-button[disabled] span{color:inherit}.bookmark-outline-control-button:not(.disabled):active,.bookmark-outline-control-button:not(.disabled):hover,.bookmark-outline-control-button:not([disabled]):active,.bookmark-outline-control-button:not([disabled]):hover{color:var(--secondary-button-hover)}.bookmark-outline-panel-header{display:flex;flex-flow:row nowrap;justify-content:space-between;align-items:center;padding:var(--padding-tiny);border-bottom:1px solid var(--divider)}.bookmark-outline-panel-header .header-title{font-size:16px}.bookmark-outline-row{flex-grow:1;overflow-y:auto}.msg-no-bookmark-outline{color:var(--placeholder-text);text-align:center}.bookmark-outline-single-container{display:flex;flex-flow:row nowrap;align-items:flex-start;border-radius:4px;margin-left:2px;margin-right:2px}.bookmark-outline-single-container.default{padding:var(--padding-tiny);border:1px solid transparent}.bookmark-outline-single-container.default.hover,.bookmark-outline-single-container.default:hover,.bookmark-outline-single-container.default[focus-within]{cursor:pointer}.bookmark-outline-single-container.default.hover,.bookmark-outline-single-container.default:focus-within,.bookmark-outline-single-container.default:hover{cursor:pointer}.bookmark-outline-single-container.default.hover .bookmark-outline-more-button,.bookmark-outline-single-container.default:hover .bookmark-outline-more-button,.bookmark-outline-single-container.default[focus-within] .bookmark-outline-more-button{display:flex;background-color:transparent}.bookmark-outline-single-container.default.hover .bookmark-outline-more-button,.bookmark-outline-single-container.default:focus-within .bookmark-outline-more-button,.bookmark-outline-single-container.default:hover .bookmark-outline-more-button{display:flex;background-color:transparent}.bookmark-outline-single-container.default:hover{outline:1px solid var(--hover-border)}.bookmark-outline-single-container.default.hover,.bookmark-outline-single-container.default.selected{background-color:var(--popup-button-active)}.bookmark-outline-single-container.default[focus-within]{border-color:transparent;outline:1px solid var(--hover-border)}.bookmark-outline-single-container.default:focus-within{border-color:transparent;outline:1px solid var(--hover-border)}.bookmark-outline-single-container.default.selected{background-color:var(--outline-selected);outline:1px solid var(--bookmark-outline-hover-border)}.bookmark-outline-single-container.default .bookmark-outline-label-row{overflow:hidden}.bookmark-outline-single-container.default.focus-visible,.bookmark-outline-single-container.default:focus-visible{outline:var(--focus-visible-outline)!important}.bookmark-outline-single-container.editing{background-color:var(--faded-component-background);padding:var(--padding-medium) 20px}.bookmark-outline-single-container.editing.focus-visible,.bookmark-outline-single-container.editing:focus-visible{outline:var(--focus-visible-outline)!important}.bookmark-outline-single-container.preview{display:inline-flex;margin-top:0;padding:var(--padding-small);background-color:var(--component-background);box-shadow:0 0 3px var(--note-box-shadow)}.bookmark-outline-single-container .bookmark-outline-checkbox{flex-grow:0;flex-shrink:0;margin-top:2px;margin-bottom:2px;margin-right:var(--padding-small)}.bookmark-outline-single-container .bookmark-outline-label-row{flex-grow:1;flex-shrink:1;display:flex;flex-flow:row wrap;align-items:flex-start;position:relative;overflow:hidden}.bookmark-outline-single-container .bookmark-outline-label{font-weight:600;flex-grow:1;flex-shrink:1;margin-bottom:var(--padding-small)}.bookmark-outline-single-container .bookmark-outline-input,.bookmark-outline-single-container .bookmark-outline-text{flex-grow:1;flex-shrink:1;flex-basis:calc(100% - 22px);margin-top:2px;margin-bottom:2px}.bookmark-outline-single-container .bookmark-text-input{margin-left:var(--padding-large)}.bookmark-outline-single-container .bookmark-outline-input{color:var(--text-color);width:calc(100% - var(--padding-large));padding:var(--padding-small);border:1px solid var(--border)}.bookmark-outline-single-container .bookmark-outline-input:focus{border-color:var(--outline-color)}.bookmark-outline-single-container .bookmark-outline-input::-moz-placeholder{color:var(--placeholder-text)}.bookmark-outline-single-container .bookmark-outline-input::placeholder{color:var(--placeholder-text)}.bookmark-outline-single-container .bookmark-outline-more-button{display:none;flex-grow:0;flex-shrink:0;width:16px;height:16px;margin:2px 2px 2px var(--padding-tiny)}.bookmark-outline-single-container .bookmark-outline-more-button .Icon{width:14px;height:14px}.bookmark-outline-single-container .bookmark-outline-more-button.icon-only:hover:not(:disabled):not(.disabled){box-shadow:none;outline:solid 1px var(--hover-border)}.bookmark-outline-single-container .bookmark-outline-more-button[focus-within].icon-only{border:none;box-shadow:none}.bookmark-outline-single-container .bookmark-outline-more-button:focus-within.icon-only{border:none;box-shadow:none}.bookmark-outline-single-container .bookmark-outline-more-button[focus-within] .Icon{color:var(--focus-border)}.bookmark-outline-single-container .bookmark-outline-more-button:focus-within .Icon{color:var(--focus-border)}.bookmark-outline-single-container .bookmark-outline-editing-controls{padding:2px;flex-basis:100%;display:flex;flex-flow:row wrap;justify-content:flex-end;align-items:center;margin-top:var(--padding-medium)}.bookmark-outline-single-container .bookmark-outline-cancel-button,.bookmark-outline-single-container .bookmark-outline-save-button{width:auto;padding:6px var(--padding)}.bookmark-outline-single-container .bookmark-outline-cancel-button{color:var(--secondary-button-text)}.bookmark-outline-single-container .bookmark-outline-cancel-button:hover{color:var(--secondary-button-hover)}.bookmark-outline-single-container .bookmark-outline-save-button{color:var(--primary-button-text);background-color:var(--primary-button);margin-left:var(--padding-tiny);border-radius:4px}.bookmark-outline-single-container .bookmark-outline-save-button:hover{background-color:var(--primary-button-hover)}.bookmark-outline-single-container .bookmark-outline-save-button.disabled,.bookmark-outline-single-container .bookmark-outline-save-button:disabled{background-color:var(--primary-button)!important;opacity:.5}.bookmark-outline-single-container .bookmark-outline-save-button.disabled span,.bookmark-outline-single-container .bookmark-outline-save-button:disabled span{color:var(--primary-button-text)}.bookmark-outline-footer{border-top:1.5px solid var(--gray-4);padding-top:var(--padding-medium);padding-bottom:var(--padding-medium);display:flex;justify-content:center;align-items:center}.bookmark-outline-footer .add-new-button .Icon{width:14px;height:14px;margin-right:var(--padding-tiny);color:inherit;fill:currentColor}.bookmark-outline-footer .add-new-button.disabled .Icon.disabled,.bookmark-outline-footer .add-new-button.disabled .Icon.disabled path,.bookmark-outline-footer .add-new-button[disabled] .Icon.disabled,.bookmark-outline-footer .add-new-button[disabled] .Icon.disabled path{color:inherit;fill:currentColor}.bookmark-outline-footer .multi-selection-button{width:auto;padding:7px}.bookmark-outline-footer .multi-selection-button .Icon{width:18px;height:18px}.bookmark-outline-footer .multi-selection-button:not(:first-child){margin-left:var(--padding-tiny)}.bookmark-outline-footer .multi-selection-button:hover{background-color:transparent}.bookmark-outline-footer .multi-selection-button.disabled:hover,.bookmark-outline-footer .multi-selection-button:disabled:hover{box-shadow:none}",""]),o.exports=t},57770:(o,t,e)=>{"use strict";e.d(t,{Ay:()=>g,NN:()=>k}),e(52675),e(89463),e(2259),e(45700),e(28706),e(2008),e(51629),e(23792),e(62062),e(94490),e(34782),e(89572),e(62010),e(2892),e(59904),e(67945),e(84185),e(83851),e(81278),e(40875),e(79432),e(10287),e(26099),e(3362),e(47764),e(23500),e(62953);var n=e(96540),r=e(61113),i=e(26247),a=e(5556),l=e.n(a);function u(o){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(o){return typeof o}:function(o){return o&&"function"==typeof Symbol&&o.constructor===Symbol&&o!==Symbol.prototype?"symbol":typeof o},u(o)}function c(){c=function(){return t};var o,t={},e=Object.prototype,n=e.hasOwnProperty,r=Object.defineProperty||function(o,t,e){o[t]=e.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function d(o,t,e){return Object.defineProperty(o,t,{value:e,enumerable:!0,configurable:!0,writable:!0}),o[t]}try{d({},"")}catch(o){d=function(o,t,e){return o[t]=e}}function b(o,t,e,n){var i=t&&t.prototype instanceof v?t:v,a=Object.create(i.prototype),l=new T(n||[]);return r(a,"_invoke",{value:P(o,e,l)}),a}function f(o,t,e){try{return{type:"normal",arg:o.call(t,e)}}catch(o){return{type:"throw",arg:o}}}t.wrap=b;var p="suspendedStart",k="suspendedYield",m="executing",h="completed",g={};function v(){}function y(){}function w(){}var x={};d(x,a,(function(){return this}));var E=Object.getPrototypeOf,O=E&&E(E(F([])));O&&O!==e&&n.call(O,a)&&(x=O);var L=w.prototype=v.prototype=Object.create(x);function j(o){["next","throw","return"].forEach((function(t){d(o,t,(function(o){return this._invoke(t,o)}))}))}function S(o,t){function e(r,i,a,l){var c=f(o[r],o,i);if("throw"!==c.type){var s=c.arg,d=s.value;return d&&"object"==u(d)&&n.call(d,"__await")?t.resolve(d.__await).then((function(o){e("next",o,a,l)}),(function(o){e("throw",o,a,l)})):t.resolve(d).then((function(o){s.value=o,a(s)}),(function(o){return e("throw",o,a,l)}))}l(c.arg)}var i;r(this,"_invoke",{value:function(o,n){function r(){return new t((function(t,r){e(o,n,t,r)}))}return i=i?i.then(r,r):r()}})}function P(t,e,n){var r=p;return function(i,a){if(r===m)throw Error("Generator is already running");if(r===h){if("throw"===i)throw a;return{value:o,done:!0}}for(n.method=i,n.arg=a;;){var l=n.delegate;if(l){var u=_(l,n);if(u){if(u===g)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(r===p)throw r=h,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r=m;var c=f(t,e,n);if("normal"===c.type){if(r=n.done?h:k,c.arg===g)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(r=h,n.method="throw",n.arg=c.arg)}}}function _(t,e){var n=e.method,r=t.iterator[n];if(r===o)return e.delegate=null,"throw"===n&&t.iterator.return&&(e.method="return",e.arg=o,_(t,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g;var i=f(r,t.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,g;var a=i.arg;return a?a.done?(e[t.resultName]=a.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=o),e.delegate=null,g):a:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,g)}function N(o){var t={tryLoc:o[0]};1 in o&&(t.catchLoc=o[1]),2 in o&&(t.finallyLoc=o[2],t.afterLoc=o[3]),this.tryEntries.push(t)}function D(o){var t=o.completion||{};t.type="normal",delete t.arg,o.completion=t}function T(o){this.tryEntries=[{tryLoc:"root"}],o.forEach(N,this),this.reset(!0)}function F(t){if(t||""===t){var e=t[a];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,i=function e(){for(;++r<t.length;)if(n.call(t,r))return e.value=t[r],e.done=!1,e;return e.value=o,e.done=!0,e};return i.next=i}}throw new TypeError(u(t)+" is not iterable")}return y.prototype=w,r(L,"constructor",{value:w,configurable:!0}),r(w,"constructor",{value:y,configurable:!0}),y.displayName=d(w,s,"GeneratorFunction"),t.isGeneratorFunction=function(o){var t="function"==typeof o&&o.constructor;return!!t&&(t===y||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(o){return Object.setPrototypeOf?Object.setPrototypeOf(o,w):(o.__proto__=w,d(o,s,"GeneratorFunction")),o.prototype=Object.create(L),o},t.awrap=function(o){return{__await:o}},j(S.prototype),d(S.prototype,l,(function(){return this})),t.AsyncIterator=S,t.async=function(o,e,n,r,i){void 0===i&&(i=Promise);var a=new S(b(o,e,n,r),i);return t.isGeneratorFunction(e)?a:a.next().then((function(o){return o.done?o.value:a.next()}))},j(L),d(L,s,"Generator"),d(L,a,(function(){return this})),d(L,"toString",(function(){return"[object Generator]"})),t.keys=function(o){var t=Object(o),e=[];for(var n in t)e.push(n);return e.reverse(),function o(){for(;e.length;){var n=e.pop();if(n in t)return o.value=n,o.done=!1,o}return o.done=!0,o}},t.values=F,T.prototype={constructor:T,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=o,this.done=!1,this.delegate=null,this.method="next",this.arg=o,this.tryEntries.forEach(D),!t)for(var e in this)"t"===e.charAt(0)&&n.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=o)},stop:function(){this.done=!0;var o=this.tryEntries[0].completion;if("throw"===o.type)throw o.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function r(n,r){return l.type="throw",l.arg=t,e.next=n,r&&(e.method="next",e.arg=o),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],l=a.completion;if("root"===a.tryLoc)return r("end");if(a.tryLoc<=this.prev){var u=n.call(a,"catchLoc"),c=n.call(a,"finallyLoc");if(u&&c){if(this.prev<a.catchLoc)return r(a.catchLoc,!0);if(this.prev<a.finallyLoc)return r(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return r(a.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return r(a.finallyLoc)}}}},abrupt:function(o,t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc<=this.prev&&n.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===o||"continue"===o)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=o,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,g):this.complete(a)},complete:function(o,t){if("throw"===o.type)throw o.arg;return"break"===o.type||"continue"===o.type?this.next=o.arg:"return"===o.type?(this.rval=this.arg=o.arg,this.method="return",this.next="end"):"normal"===o.type&&t&&(this.next=t),g},finish:function(o){for(var t=this.tryEntries.length-1;t>=0;--t){var e=this.tryEntries[t];if(e.finallyLoc===o)return this.complete(e.completion,e.afterLoc),D(e),g}},catch:function(o){for(var t=this.tryEntries.length-1;t>=0;--t){var e=this.tryEntries[t];if(e.tryLoc===o){var n=e.completion;if("throw"===n.type){var r=n.arg;D(e)}return r}}throw Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={iterator:F(t),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=o),g}},t}function s(o,t,e,n,r,i,a){try{var l=o[i](a),u=l.value}catch(o){return void e(o)}l.done?t(u):Promise.resolve(u).then(n,r)}function d(o,t){var e=Object.keys(o);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(o);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(o,t).enumerable}))),e.push.apply(e,n)}return e}function b(o){for(var t=1;t<arguments.length;t++){var e=null!=arguments[t]?arguments[t]:{};t%2?d(Object(e),!0).forEach((function(t){f(o,t,e[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(o,Object.getOwnPropertyDescriptors(e)):d(Object(e)).forEach((function(t){Object.defineProperty(o,t,Object.getOwnPropertyDescriptor(e,t))}))}return o}function f(o,t,e){return(t=function(o){var t=function(o){if("object"!=u(o)||!o)return o;var t=o[Symbol.toPrimitive];if(void 0!==t){var e=t.call(o,"string");if("object"!=u(e))return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}(o);return"symbol"==u(t)?t:t+""}(t))in o?Object.defineProperty(o,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):o[t]=e,o}var p=function(o,t,e){return{icon:t,label:e,title:e,option:o,dataElement:"".concat(o[0].toUpperCase()+o.slice(1),"Button")}},k={OPENFILE:"openFile",RENAME:"rename",SETDEST:"setDestination",DOWNLOAD:"download",DELETE:"delete"},m=[p(k.OPENFILE,"icon-portfolio-file","portfolio.openFile"),p(k.RENAME,"ic_edit_page_24px","action.rename"),p(k.SETDEST,"icon-thumbtack","action.setDestination"),p(k.DOWNLOAD,"icon-download","action.download"),p(k.DELETE,"icon-delete-line","action.delete")],h=function(o){var t=o.type,e=o.handleOnClick,a=o.currentFlyout,l=o.flyoutSelector,u=o.shouldHideDeleteButton,d=(0,r.wA)();return(0,n.useLayoutEffect)((function(){var o={dataElement:l,className:"MoreOptionsContextMenuFlyout",items:m.map((function(o){var n=o.option,r=!1;return n===k.DELETE?r=u:n===k.DOWNLOAD||n===k.OPENFILE?r="portfolio"!==t:n===k.SETDEST&&(r="outline"!==t),b(b({},o),{},{hidden:r,dataElement:"".concat(t).concat(o.dataElement),onClick:function(){return e(o.option)}})}))};function n(){var t;return t=c().mark((function t(){return c().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:d(a?i.A.updateFlyout(o.dataElement,o):i.A.addFlyout(o));case 1:case"end":return t.stop()}}),t)})),n=function(){var o=this,e=arguments;return new Promise((function(n,r){var i=t.apply(o,e);function a(o){s(i,n,r,a,l,"next",o)}function l(o){s(i,n,r,a,l,"throw",o)}a(void 0)}))},n.apply(this,arguments)}!function(){n.apply(this,arguments)}()}),[]),null};h.propTypes={type:l().oneOf(["bookmark","outline","portfolio"]).isRequired,handleOnClick:l().func,currentFlyout:l().object,flyoutSelector:l().string,shouldHideDeleteButton:l().bool};const g=h},97223:(o,t,e)=>{var n=e(85072),r=e(856);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[o.id,r,""]]),n(r,{insert:function(o){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(o);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function o(t,e=document){const n=[];return e.querySelectorAll(t).forEach((o=>n.push(o))),e.querySelectorAll("*").forEach((e=>{e.shadowRoot&&n.push(...o(t,e.shadowRoot))})),n}("apryse-webviewer"));const e=[];for(let n=0;n<t.length;n++){const r=t[n];if(0===n)r.shadowRoot.appendChild(o),o.onload=function(){e.length>0&&e.forEach((t=>{t.innerHTML=o.innerHTML}))};else{const t=o.cloneNode(!0);r.shadowRoot.appendChild(t),e.push(t)}}},singleton:!1}),o.exports=r.locals||{}}}]);
//# sourceMappingURL=2014.chunk.js.map