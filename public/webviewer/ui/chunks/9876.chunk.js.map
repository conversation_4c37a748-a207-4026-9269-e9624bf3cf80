{"version": 3, "file": "chunks/9876.chunk.js", "mappings": ";iGAEAA,EADkC,EAAQ,MAChCC,EAA4B,IAE9BC,KAAK,CAACC,EAAOC,GAAI,syGAAuyG,KAEh0GJ,EAAQK,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,QAEvBF,EAAOH,QAAUA,mBCVjB,IAAIM,EAAM,EAAQ,OACFC,EAAU,EAAQ,OAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACJ,EAAOC,GAAIG,EAAS,MAwDjCD,EAAIC,EArDH,CAEdG,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,SAAQC,GAAMH,EAASrB,KAAKwB,KAG3DJ,EAAKE,iBAAiB,KAAKC,SAAQC,IAC7BA,EAAGC,YACLJ,EAASrB,QAAQkB,EAAwBC,EAASK,EAAGC,YACvD,IAGKJ,CACT,CAWkBH,CAAwB,qBAG1C,MAAMQ,EAAkB,GACxB,IAAK,IAAIC,EAAI,EAAGA,EAAIZ,EAAcE,OAAQU,IAAK,CAC7C,MAAMC,EAAeb,EAAcY,GACnC,GAAU,IAANA,EACFC,EAAaH,WAAWX,YAAYL,GACpCA,EAASoB,OAAS,WACZH,EAAgBT,OAAS,GAC3BS,EAAgBH,SAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,SAAS,GAG9C,MACK,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaH,WAAWX,YAAYgB,GACpCJ,EAAgB1B,KAAK8B,EACvB,CACF,CACF,EACdtB,WAAoB,IAMpBP,EAAOH,QAAUO,EAAQF,QAAU,CAAC,8xBCjEpC8B,EAAA,kBAAAC,CAAA,MAAAC,EAAAD,EAAA,GAAAE,EAAAC,OAAAC,UAAAC,EAAAH,EAAAI,eAAAC,EAAAJ,OAAAK,gBAAA,SAAAP,EAAAD,EAAAE,GAAAD,EAAAD,GAAAE,EAAAO,KAAA,EAAAhB,EAAA,mBAAAiB,OAAAA,OAAA,GAAAC,EAAAlB,EAAAmB,UAAA,aAAAC,EAAApB,EAAAqB,eAAA,kBAAAC,EAAAtB,EAAAuB,aAAA,yBAAAC,EAAAhB,EAAAD,EAAAE,GAAA,OAAAC,OAAAK,eAAAP,EAAAD,EAAA,CAAAS,MAAAP,EAAAgB,YAAA,EAAAC,cAAA,EAAAC,UAAA,IAAAnB,EAAAD,EAAA,KAAAiB,EAAA,aAAAhB,GAAAgB,EAAA,SAAAhB,EAAAD,EAAAE,GAAA,OAAAD,EAAAD,GAAAE,CAAA,WAAAmB,EAAApB,EAAAD,EAAAE,EAAAG,GAAA,IAAAZ,EAAAO,GAAAA,EAAAI,qBAAAkB,EAAAtB,EAAAsB,EAAAX,EAAAR,OAAAoB,OAAA9B,EAAAW,WAAAS,EAAA,IAAAW,EAAAnB,GAAA,WAAAE,EAAAI,EAAA,WAAAF,MAAAgB,EAAAxB,EAAAC,EAAAW,KAAAF,CAAA,UAAAe,EAAAzB,EAAAD,EAAAE,GAAA,WAAAyB,KAAA,SAAAC,IAAA3B,EAAA4B,KAAA7B,EAAAE,GAAA,OAAAD,GAAA,OAAA0B,KAAA,QAAAC,IAAA3B,EAAA,EAAAD,EAAAqB,KAAAA,EAAA,IAAAS,EAAA,iBAAAC,EAAA,iBAAAC,EAAA,YAAAC,EAAA,YAAAC,EAAA,YAAAZ,IAAA,UAAAa,IAAA,UAAAC,IAAA,KAAAC,EAAA,GAAApB,EAAAoB,EAAA1B,GAAA,8BAAA2B,EAAAnC,OAAAoC,eAAAC,EAAAF,GAAAA,EAAAA,EAAAG,EAAA,MAAAD,GAAAA,IAAAtC,GAAAG,EAAAwB,KAAAW,EAAA7B,KAAA0B,EAAAG,GAAA,IAAAE,EAAAN,EAAAhC,UAAAkB,EAAAlB,UAAAD,OAAAoB,OAAAc,GAAA,SAAAM,EAAA1C,GAAA,0BAAAZ,SAAA,SAAAW,GAAAiB,EAAAhB,EAAAD,GAAA,SAAAC,GAAA,YAAA2C,QAAA5C,EAAAC,EAAA,gBAAA4C,EAAA5C,EAAAD,GAAA,SAAA8C,EAAA5C,EAAAK,EAAAd,EAAAkB,GAAA,IAAAE,EAAAa,EAAAzB,EAAAC,GAAAD,EAAAM,GAAA,aAAAM,EAAAc,KAAA,KAAAZ,EAAAF,EAAAe,IAAAE,EAAAf,EAAAN,MAAA,OAAAqB,GAAA,UAAAiB,EAAAjB,IAAAzB,EAAAwB,KAAAC,EAAA,WAAA9B,EAAAgD,QAAAlB,EAAAmB,SAAAC,MAAA,SAAAjD,GAAA6C,EAAA,OAAA7C,EAAAR,EAAAkB,EAAA,aAAAV,GAAA6C,EAAA,QAAA7C,EAAAR,EAAAkB,EAAA,IAAAX,EAAAgD,QAAAlB,GAAAoB,MAAA,SAAAjD,GAAAc,EAAAN,MAAAR,EAAAR,EAAAsB,EAAA,aAAAd,GAAA,OAAA6C,EAAA,QAAA7C,EAAAR,EAAAkB,EAAA,IAAAA,EAAAE,EAAAe,IAAA,KAAA1B,EAAAK,EAAA,gBAAAE,MAAA,SAAAR,EAAAI,GAAA,SAAA8C,IAAA,WAAAnD,GAAA,SAAAA,EAAAE,GAAA4C,EAAA7C,EAAAI,EAAAL,EAAAE,EAAA,WAAAA,EAAAA,EAAAA,EAAAgD,KAAAC,EAAAA,GAAAA,GAAA,aAAA1B,EAAAzB,EAAAE,EAAAG,GAAA,IAAAE,EAAAuB,EAAA,gBAAArC,EAAAkB,GAAA,GAAAJ,IAAAyB,EAAA,MAAAoB,MAAA,mCAAA7C,IAAA0B,EAAA,cAAAxC,EAAA,MAAAkB,EAAA,OAAAF,MAAAR,EAAAoD,MAAA,OAAAhD,EAAAiD,OAAA7D,EAAAY,EAAAuB,IAAAjB,IAAA,KAAAE,EAAAR,EAAAkD,SAAA,GAAA1C,EAAA,KAAAE,EAAAyC,EAAA3C,EAAAR,GAAA,GAAAU,EAAA,IAAAA,IAAAmB,EAAA,gBAAAnB,CAAA,cAAAV,EAAAiD,OAAAjD,EAAAoD,KAAApD,EAAAqD,MAAArD,EAAAuB,SAAA,aAAAvB,EAAAiD,OAAA,IAAA/C,IAAAuB,EAAA,MAAAvB,EAAA0B,EAAA5B,EAAAuB,IAAAvB,EAAAsD,kBAAAtD,EAAAuB,IAAA,gBAAAvB,EAAAiD,QAAAjD,EAAAuD,OAAA,SAAAvD,EAAAuB,KAAArB,EAAAyB,EAAA,IAAAK,EAAAX,EAAA1B,EAAAE,EAAAG,GAAA,cAAAgC,EAAAV,KAAA,IAAApB,EAAAF,EAAAgD,KAAApB,EAAAF,EAAAM,EAAAT,MAAAM,EAAA,gBAAAzB,MAAA4B,EAAAT,IAAAyB,KAAAhD,EAAAgD,KAAA,WAAAhB,EAAAV,OAAApB,EAAA0B,EAAA5B,EAAAiD,OAAA,QAAAjD,EAAAuB,IAAAS,EAAAT,IAAA,YAAA4B,EAAAxD,EAAAE,GAAA,IAAAG,EAAAH,EAAAoD,OAAA/C,EAAAP,EAAAY,SAAAP,GAAA,GAAAE,IAAAN,EAAA,OAAAC,EAAAqD,SAAA,eAAAlD,GAAAL,EAAAY,SAAA,SAAAV,EAAAoD,OAAA,SAAApD,EAAA0B,IAAA3B,EAAAuD,EAAAxD,EAAAE,GAAA,UAAAA,EAAAoD,SAAA,WAAAjD,IAAAH,EAAAoD,OAAA,QAAApD,EAAA0B,IAAA,IAAAiC,UAAA,oCAAAxD,EAAA,aAAA6B,EAAA,IAAAzC,EAAAiC,EAAAnB,EAAAP,EAAAY,SAAAV,EAAA0B,KAAA,aAAAnC,EAAAkC,KAAA,OAAAzB,EAAAoD,OAAA,QAAApD,EAAA0B,IAAAnC,EAAAmC,IAAA1B,EAAAqD,SAAA,KAAArB,EAAA,IAAAvB,EAAAlB,EAAAmC,IAAA,OAAAjB,EAAAA,EAAA0C,MAAAnD,EAAAF,EAAA8D,YAAAnD,EAAAF,MAAAP,EAAA6D,KAAA/D,EAAAgE,QAAA,WAAA9D,EAAAoD,SAAApD,EAAAoD,OAAA,OAAApD,EAAA0B,IAAA3B,GAAAC,EAAAqD,SAAA,KAAArB,GAAAvB,GAAAT,EAAAoD,OAAA,QAAApD,EAAA0B,IAAA,IAAAiC,UAAA,oCAAA3D,EAAAqD,SAAA,KAAArB,EAAA,UAAA+B,EAAAhE,GAAA,IAAAD,EAAA,CAAAkE,OAAAjE,EAAA,SAAAA,IAAAD,EAAAmE,SAAAlE,EAAA,SAAAA,IAAAD,EAAAoE,WAAAnE,EAAA,GAAAD,EAAAqE,SAAApE,EAAA,SAAAqE,WAAAxG,KAAAkC,EAAA,UAAAuE,EAAAtE,GAAA,IAAAD,EAAAC,EAAAuE,YAAA,GAAAxE,EAAA2B,KAAA,gBAAA3B,EAAA4B,IAAA3B,EAAAuE,WAAAxE,CAAA,UAAAwB,EAAAvB,GAAA,KAAAqE,WAAA,EAAAJ,OAAA,SAAAjE,EAAAZ,QAAA4E,EAAA,WAAAQ,OAAA,YAAAhC,EAAAzC,GAAA,GAAAA,GAAA,KAAAA,EAAA,KAAAE,EAAAF,EAAAW,GAAA,GAAAT,EAAA,OAAAA,EAAA2B,KAAA7B,GAAA,sBAAAA,EAAA+D,KAAA,OAAA/D,EAAA,IAAA0E,MAAA1E,EAAAjB,QAAA,KAAAwB,GAAA,EAAAd,EAAA,SAAAsE,IAAA,OAAAxD,EAAAP,EAAAjB,QAAA,GAAAsB,EAAAwB,KAAA7B,EAAAO,GAAA,OAAAwD,EAAAtD,MAAAT,EAAAO,GAAAwD,EAAAV,MAAA,EAAAU,EAAA,OAAAA,EAAAtD,MAAAR,EAAA8D,EAAAV,MAAA,EAAAU,CAAA,SAAAtE,EAAAsE,KAAAtE,CAAA,YAAAoE,UAAAd,EAAA/C,GAAA,2BAAAmC,EAAA/B,UAAAgC,EAAA7B,EAAAmC,EAAA,eAAAjC,MAAA2B,EAAAjB,cAAA,IAAAZ,EAAA6B,EAAA,eAAA3B,MAAA0B,EAAAhB,cAAA,IAAAgB,EAAAwC,YAAA1D,EAAAmB,EAAArB,EAAA,qBAAAf,EAAA4E,oBAAA,SAAA3E,GAAA,IAAAD,EAAA,mBAAAC,GAAAA,EAAA4E,YAAA,QAAA7E,IAAAA,IAAAmC,GAAA,uBAAAnC,EAAA2E,aAAA3E,EAAA8E,MAAA,EAAA9E,EAAA+E,KAAA,SAAA9E,GAAA,OAAAE,OAAA6E,eAAA7E,OAAA6E,eAAA/E,EAAAmC,IAAAnC,EAAAgF,UAAA7C,EAAAnB,EAAAhB,EAAAc,EAAA,sBAAAd,EAAAG,UAAAD,OAAAoB,OAAAmB,GAAAzC,CAAA,EAAAD,EAAAkF,MAAA,SAAAjF,GAAA,OAAAgD,QAAAhD,EAAA,EAAA0C,EAAAE,EAAAzC,WAAAa,EAAA4B,EAAAzC,UAAAS,GAAA,0BAAAb,EAAA6C,cAAAA,EAAA7C,EAAAmF,MAAA,SAAAlF,EAAAC,EAAAG,EAAAE,EAAAd,QAAA,IAAAA,IAAAA,EAAA2F,SAAA,IAAAzE,EAAA,IAAAkC,EAAAxB,EAAApB,EAAAC,EAAAG,EAAAE,GAAAd,GAAA,OAAAO,EAAA4E,oBAAA1E,GAAAS,EAAAA,EAAAoD,OAAAb,MAAA,SAAAjD,GAAA,OAAAA,EAAAoD,KAAApD,EAAAQ,MAAAE,EAAAoD,MAAA,KAAApB,EAAAD,GAAAzB,EAAAyB,EAAA3B,EAAA,aAAAE,EAAAyB,EAAA/B,GAAA,0BAAAM,EAAAyB,EAAA,qDAAA1C,EAAAqF,KAAA,SAAApF,GAAA,IAAAD,EAAAG,OAAAF,GAAAC,EAAA,WAAAG,KAAAL,EAAAE,EAAApC,KAAAuC,GAAA,OAAAH,EAAAoF,UAAA,SAAAvB,IAAA,KAAA7D,EAAAnB,QAAA,KAAAkB,EAAAC,EAAAqF,MAAA,GAAAtF,KAAAD,EAAA,OAAA+D,EAAAtD,MAAAR,EAAA8D,EAAAV,MAAA,EAAAU,CAAA,QAAAA,EAAAV,MAAA,EAAAU,CAAA,GAAA/D,EAAAyC,OAAAA,EAAAjB,EAAApB,UAAA,CAAAyE,YAAArD,EAAAiD,MAAA,SAAAzE,GAAA,QAAAwF,KAAA,OAAAzB,KAAA,OAAAN,KAAA,KAAAC,MAAAzD,EAAA,KAAAoD,MAAA,OAAAE,SAAA,UAAAD,OAAA,YAAA1B,IAAA3B,EAAA,KAAAqE,WAAAjF,QAAAkF,IAAAvE,EAAA,QAAAE,KAAA,WAAAA,EAAAuF,OAAA,IAAApF,EAAAwB,KAAA,KAAA3B,KAAAwE,OAAAxE,EAAAwF,MAAA,WAAAxF,GAAAD,EAAA,EAAA0F,KAAA,gBAAAtC,MAAA,MAAApD,EAAA,KAAAqE,WAAA,GAAAE,WAAA,aAAAvE,EAAA0B,KAAA,MAAA1B,EAAA2B,IAAA,YAAAgE,IAAA,EAAAjC,kBAAA,SAAA3D,GAAA,QAAAqD,KAAA,MAAArD,EAAA,IAAAE,EAAA,cAAA2F,EAAAxF,EAAAE,GAAA,OAAAI,EAAAgB,KAAA,QAAAhB,EAAAiB,IAAA5B,EAAAE,EAAA6D,KAAA1D,EAAAE,IAAAL,EAAAoD,OAAA,OAAApD,EAAA0B,IAAA3B,KAAAM,CAAA,SAAAA,EAAA,KAAA+D,WAAAvF,OAAA,EAAAwB,GAAA,IAAAA,EAAA,KAAAd,EAAA,KAAA6E,WAAA/D,GAAAI,EAAAlB,EAAA+E,WAAA,YAAA/E,EAAAyE,OAAA,OAAA2B,EAAA,UAAApG,EAAAyE,QAAA,KAAAsB,KAAA,KAAA3E,EAAAR,EAAAwB,KAAApC,EAAA,YAAAsB,EAAAV,EAAAwB,KAAApC,EAAA,iBAAAoB,GAAAE,EAAA,SAAAyE,KAAA/F,EAAA0E,SAAA,OAAA0B,EAAApG,EAAA0E,UAAA,WAAAqB,KAAA/F,EAAA2E,WAAA,OAAAyB,EAAApG,EAAA2E,WAAA,SAAAvD,GAAA,QAAA2E,KAAA/F,EAAA0E,SAAA,OAAA0B,EAAApG,EAAA0E,UAAA,YAAApD,EAAA,MAAAqC,MAAA,kDAAAoC,KAAA/F,EAAA2E,WAAA,OAAAyB,EAAApG,EAAA2E,WAAA,KAAAR,OAAA,SAAA3D,EAAAD,GAAA,QAAAE,EAAA,KAAAoE,WAAAvF,OAAA,EAAAmB,GAAA,IAAAA,EAAA,KAAAK,EAAA,KAAA+D,WAAApE,GAAA,GAAAK,EAAA2D,QAAA,KAAAsB,MAAAnF,EAAAwB,KAAAtB,EAAA,oBAAAiF,KAAAjF,EAAA6D,WAAA,KAAA3E,EAAAc,EAAA,OAAAd,IAAA,UAAAQ,GAAA,aAAAA,IAAAR,EAAAyE,QAAAlE,GAAAA,GAAAP,EAAA2E,aAAA3E,EAAA,UAAAkB,EAAAlB,EAAAA,EAAA+E,WAAA,UAAA7D,EAAAgB,KAAA1B,EAAAU,EAAAiB,IAAA5B,EAAAP,GAAA,KAAA6D,OAAA,YAAAS,KAAAtE,EAAA2E,WAAAlC,GAAA,KAAA4D,SAAAnF,EAAA,EAAAmF,SAAA,SAAA7F,EAAAD,GAAA,aAAAC,EAAA0B,KAAA,MAAA1B,EAAA2B,IAAA,gBAAA3B,EAAA0B,MAAA,aAAA1B,EAAA0B,KAAA,KAAAoC,KAAA9D,EAAA2B,IAAA,WAAA3B,EAAA0B,MAAA,KAAAiE,KAAA,KAAAhE,IAAA3B,EAAA2B,IAAA,KAAA0B,OAAA,cAAAS,KAAA,kBAAA9D,EAAA0B,MAAA3B,IAAA,KAAA+D,KAAA/D,GAAAkC,CAAA,EAAA6D,OAAA,SAAA9F,GAAA,QAAAD,EAAA,KAAAsE,WAAAvF,OAAA,EAAAiB,GAAA,IAAAA,EAAA,KAAAE,EAAA,KAAAoE,WAAAtE,GAAA,GAAAE,EAAAkE,aAAAnE,EAAA,YAAA6F,SAAA5F,EAAAsE,WAAAtE,EAAAmE,UAAAE,EAAArE,GAAAgC,CAAA,kBAAAjC,GAAA,QAAAD,EAAA,KAAAsE,WAAAvF,OAAA,EAAAiB,GAAA,IAAAA,EAAA,KAAAE,EAAA,KAAAoE,WAAAtE,GAAA,GAAAE,EAAAgE,SAAAjE,EAAA,KAAAI,EAAAH,EAAAsE,WAAA,aAAAnE,EAAAsB,KAAA,KAAApB,EAAAF,EAAAuB,IAAA2C,EAAArE,EAAA,QAAAK,CAAA,QAAA6C,MAAA,0BAAA4C,cAAA,SAAAhG,EAAAE,EAAAG,GAAA,YAAAkD,SAAA,CAAA3C,SAAA6B,EAAAzC,GAAA8D,WAAA5D,EAAA8D,QAAA3D,GAAA,cAAAiD,SAAA,KAAA1B,IAAA3B,GAAAiC,CAAA,GAAAlC,CAAA,UAAAiG,EAAA5F,EAAAJ,EAAAD,EAAAE,EAAAK,EAAAI,EAAAE,GAAA,QAAApB,EAAAY,EAAAM,GAAAE,GAAAE,EAAAtB,EAAAgB,KAAA,OAAAJ,GAAA,YAAAL,EAAAK,EAAA,CAAAZ,EAAA4D,KAAApD,EAAAc,GAAAqE,QAAApC,QAAAjC,GAAAmC,KAAAhD,EAAAK,EAAA,UASA,IAAM2F,EAAmB,CAAC,MAAO,OAAQ,MAAO,OAAOC,KACrD,SAACC,GAAM,UAAAC,OAASD,EAAM,IACtBE,KAAK,MAEDC,EAAW,SAACC,GAAI,OAAK,IAAIpB,SAAQ,SAACpC,EAASyD,GAC/C,IAAMC,EAAS,IAAIC,WACnBD,EAAOE,cAAcJ,GACrBE,EAAO/G,OAAS,kBAAMqD,EAAQ0D,EAAOG,OAAO,EAC5CH,EAAOI,QAAUL,CACnB,GAAE,EAuCF,MCxDA,EDmB0B,WACxB,IAAMM,GAAWC,EAAAA,EAAAA,MAEXC,EAAY,eAvBpB5G,EAuBoB6G,GAvBpB7G,EAuBoBN,IAAAgF,MAAG,SAAAoC,EAAOnH,GAAC,IAAAwG,EAAAY,EAAAC,EAAA,OAAAtH,IAAAsB,MAAA,SAAAiG,GAAA,cAAAA,EAAA9B,KAAA8B,EAAAvD,MAAA,OACG,KAAxByC,EAAOxG,EAAEuH,OAAOC,MAAM,IAClB,CAAFF,EAAAvD,KAAA,SAEsD,OAFtDuD,EAAA9B,KAAA,EAEJuB,EAASU,EAAAA,EAAQC,YAAYC,EAAAA,EAAaC,gBAAgBN,EAAAvD,KAAA,EACrCwC,EAASC,GAAK,OAAvB,OAANY,EAAME,EAAA7D,KAAA6D,EAAAvD,KAAG,EACT8D,EAAAA,EAAKC,kBAAkBC,oBAAoBX,GAAO,OACxDL,EAASU,EAAAA,EAAQO,aAAaL,EAAAA,EAAaC,gBAAgBN,EAAAvD,KAAA,iBAAAuD,EAAA9B,KAAA,GAAA8B,EAAAW,GAAAX,EAAA,SAE3DP,EAASU,EAAAA,EAAQO,aAAaL,EAAAA,EAAaC,gBAC3Cb,EAASU,EAAAA,EAAQS,mBAAmB,CAClCC,MAAO,QACPC,QAAOd,EAAAW,MACL,SAEAZ,GAASgB,EAAAA,EAAAA,MAAcC,cAAc,iCAEzCjB,EAAO5G,MAAQ,IAChB,yBAAA6G,EAAA3B,OAAA,GAAAwB,EAAA,kBAzCP,eAAAlH,EAAA,KAAAD,EAAAuI,UAAA,WAAAnD,SAAA,SAAAlF,EAAAK,GAAA,IAAAI,EAAAN,EAAAmI,MAAAvI,EAAAD,GAAA,SAAAyI,EAAApI,GAAA4F,EAAAtF,EAAAT,EAAAK,EAAAkI,EAAAC,EAAA,OAAArI,EAAA,UAAAqI,EAAArI,GAAA4F,EAAAtF,EAAAT,EAAAK,EAAAkI,EAAAC,EAAA,QAAArI,EAAA,CAAAoI,OAAA,QA2CG,gBApBiBE,GAAA,OAAAzB,EAAAsB,MAAA,KAAAD,UAAA,KAsBlB,OACEK,EAAAA,cAAA,OAAKC,UAAU,qBACbD,EAAAA,cAAA,SACE5K,GAAG,4BACH2D,KAAK,OACLmH,OAAQ5C,EACR6C,SAAU9B,IAIlB,0lCEyBA,MC/EA,EDc2B,SAAHC,GAIlB,IAHJ8B,EAAK9B,EAAL8B,MACAC,EAAa/B,EAAb+B,cAAaC,EAAAhC,EACbiC,gBAAAA,OAAe,IAAAD,EAAG,MAAKA,EAEwEE,EAAAC,GAA/DC,EAAAA,EAAAA,WAAS,iBAAO,CAAEC,KAAM,QAASC,MAAO,OAAQC,IAAK,OAAQ,IAAE,GAAxFC,EAAQN,EAAA,GAAEO,EAAWP,EAAA,GACtBQ,GAAaC,EAAAA,EAAAA,QAAO,MAExBC,EAGAT,GAFEU,EAAAA,EAAAA,KAAY,SAACC,GAAK,MAAK,CACzBC,EAAAA,EAAUC,cAAcF,EAAO,sBAChC,IAAC,GAHM,GAKFjD,GAAWC,EAAAA,EAAAA,MAwBjB,OAfAmD,EAAAA,EAAAA,GAAkBP,GAPK,SAAC5J,GACtB,IAAMoK,EAAa1L,SAAS4J,cAAc,qCAChB8B,aAAU,EAAVA,EAAYC,SAASrK,EAAEuH,UAE/CR,EAASU,EAAAA,EAAQ6C,cAAc,CAAC,uBAEpC,KAGAC,EAAAA,EAAAA,kBAAgB,WACd,GAAIT,EAAQ,CACV,IAAMU,EAAW,WACf,IAAMC,GAAkBC,EAAAA,EAAAA,GAA0B,kBAAmBd,GACrED,EAAYc,EACd,EAIA,OAHAD,IAEAhM,OAAOmM,iBAAiB,SAAUH,GAC3B,kBAAMhM,OAAOoM,oBAAoB,SAAUJ,EAAS,CAC7D,CACF,GAAG,CAACV,KAEGe,EAAAA,EAAAA,cACLjC,EAAAA,cAACkC,EAAAA,EAAkB,CACjB,eAAa,qBACbjC,UAAWkC,IAAW,CACpBC,oBAAoB,EACpBC,OAAO,EACPC,KAAMpB,EACNqB,QAASrB,IAEXsB,MAAO1B,EACP2B,IAAKzB,GAELhB,EAAAA,cAAC0C,EAAAA,EAAY,CACXtC,MAAOA,EACPuC,SAAS,YACTtC,cAAeA,EACfuC,sBAAoB,IAEtB5C,EAAAA,cAAC6C,EAAAA,EAAkB,CACjBzC,MAAOA,EACPC,cAAeA,EACfyC,YAAU,MAGdrD,EAAAA,EAAAA,MAAcsD,eAAexC,GAEjC,y5DE9EApJ,EAAA,kBAAAC,CAAA,MAAAC,EAAAD,EAAA,GAAAE,EAAAC,OAAAC,UAAAC,EAAAH,EAAAI,eAAAC,EAAAJ,OAAAK,gBAAA,SAAAP,EAAAD,EAAAE,GAAAD,EAAAD,GAAAE,EAAAO,KAAA,EAAAhB,EAAA,mBAAAiB,OAAAA,OAAA,GAAAC,EAAAlB,EAAAmB,UAAA,aAAAC,EAAApB,EAAAqB,eAAA,kBAAAC,EAAAtB,EAAAuB,aAAA,yBAAAC,EAAAhB,EAAAD,EAAAE,GAAA,OAAAC,OAAAK,eAAAP,EAAAD,EAAA,CAAAS,MAAAP,EAAAgB,YAAA,EAAAC,cAAA,EAAAC,UAAA,IAAAnB,EAAAD,EAAA,KAAAiB,EAAA,aAAAhB,GAAAgB,EAAA,SAAAhB,EAAAD,EAAAE,GAAA,OAAAD,EAAAD,GAAAE,CAAA,WAAAmB,EAAApB,EAAAD,EAAAE,EAAAG,GAAA,IAAAZ,EAAAO,GAAAA,EAAAI,qBAAAkB,EAAAtB,EAAAsB,EAAAX,EAAAR,OAAAoB,OAAA9B,EAAAW,WAAAS,EAAA,IAAAW,EAAAnB,GAAA,WAAAE,EAAAI,EAAA,WAAAF,MAAAgB,EAAAxB,EAAAC,EAAAW,KAAAF,CAAA,UAAAe,EAAAzB,EAAAD,EAAAE,GAAA,WAAAyB,KAAA,SAAAC,IAAA3B,EAAA4B,KAAA7B,EAAAE,GAAA,OAAAD,GAAA,OAAA0B,KAAA,QAAAC,IAAA3B,EAAA,EAAAD,EAAAqB,KAAAA,EAAA,IAAAS,EAAA,iBAAAC,EAAA,iBAAAC,EAAA,YAAAC,EAAA,YAAAC,EAAA,YAAAZ,IAAA,UAAAa,IAAA,UAAAC,IAAA,KAAAC,EAAA,GAAApB,EAAAoB,EAAA1B,GAAA,8BAAA2B,EAAAnC,OAAAoC,eAAAC,EAAAF,GAAAA,EAAAA,EAAAG,EAAA,MAAAD,GAAAA,IAAAtC,GAAAG,EAAAwB,KAAAW,EAAA7B,KAAA0B,EAAAG,GAAA,IAAAE,EAAAN,EAAAhC,UAAAkB,EAAAlB,UAAAD,OAAAoB,OAAAc,GAAA,SAAAM,EAAA1C,GAAA,0BAAAZ,SAAA,SAAAW,GAAAiB,EAAAhB,EAAAD,GAAA,SAAAC,GAAA,YAAA2C,QAAA5C,EAAAC,EAAA,gBAAA4C,EAAA5C,EAAAD,GAAA,SAAA8C,EAAA5C,EAAAK,EAAAd,EAAAkB,GAAA,IAAAE,EAAAa,EAAAzB,EAAAC,GAAAD,EAAAM,GAAA,aAAAM,EAAAc,KAAA,KAAAZ,EAAAF,EAAAe,IAAAE,EAAAf,EAAAN,MAAA,OAAAqB,GAAA,UAAAiB,EAAAjB,IAAAzB,EAAAwB,KAAAC,EAAA,WAAA9B,EAAAgD,QAAAlB,EAAAmB,SAAAC,MAAA,SAAAjD,GAAA6C,EAAA,OAAA7C,EAAAR,EAAAkB,EAAA,aAAAV,GAAA6C,EAAA,QAAA7C,EAAAR,EAAAkB,EAAA,IAAAX,EAAAgD,QAAAlB,GAAAoB,MAAA,SAAAjD,GAAAc,EAAAN,MAAAR,EAAAR,EAAAsB,EAAA,aAAAd,GAAA,OAAA6C,EAAA,QAAA7C,EAAAR,EAAAkB,EAAA,IAAAA,EAAAE,EAAAe,IAAA,KAAA1B,EAAAK,EAAA,gBAAAE,MAAA,SAAAR,EAAAI,GAAA,SAAA8C,IAAA,WAAAnD,GAAA,SAAAA,EAAAE,GAAA4C,EAAA7C,EAAAI,EAAAL,EAAAE,EAAA,WAAAA,EAAAA,EAAAA,EAAAgD,KAAAC,EAAAA,GAAAA,GAAA,aAAA1B,EAAAzB,EAAAE,EAAAG,GAAA,IAAAE,EAAAuB,EAAA,gBAAArC,EAAAkB,GAAA,GAAAJ,IAAAyB,EAAA,MAAAoB,MAAA,mCAAA7C,IAAA0B,EAAA,cAAAxC,EAAA,MAAAkB,EAAA,OAAAF,MAAAR,EAAAoD,MAAA,OAAAhD,EAAAiD,OAAA7D,EAAAY,EAAAuB,IAAAjB,IAAA,KAAAE,EAAAR,EAAAkD,SAAA,GAAA1C,EAAA,KAAAE,EAAAyC,EAAA3C,EAAAR,GAAA,GAAAU,EAAA,IAAAA,IAAAmB,EAAA,gBAAAnB,CAAA,cAAAV,EAAAiD,OAAAjD,EAAAoD,KAAApD,EAAAqD,MAAArD,EAAAuB,SAAA,aAAAvB,EAAAiD,OAAA,IAAA/C,IAAAuB,EAAA,MAAAvB,EAAA0B,EAAA5B,EAAAuB,IAAAvB,EAAAsD,kBAAAtD,EAAAuB,IAAA,gBAAAvB,EAAAiD,QAAAjD,EAAAuD,OAAA,SAAAvD,EAAAuB,KAAArB,EAAAyB,EAAA,IAAAK,EAAAX,EAAA1B,EAAAE,EAAAG,GAAA,cAAAgC,EAAAV,KAAA,IAAApB,EAAAF,EAAAgD,KAAApB,EAAAF,EAAAM,EAAAT,MAAAM,EAAA,gBAAAzB,MAAA4B,EAAAT,IAAAyB,KAAAhD,EAAAgD,KAAA,WAAAhB,EAAAV,OAAApB,EAAA0B,EAAA5B,EAAAiD,OAAA,QAAAjD,EAAAuB,IAAAS,EAAAT,IAAA,YAAA4B,EAAAxD,EAAAE,GAAA,IAAAG,EAAAH,EAAAoD,OAAA/C,EAAAP,EAAAY,SAAAP,GAAA,GAAAE,IAAAN,EAAA,OAAAC,EAAAqD,SAAA,eAAAlD,GAAAL,EAAAY,SAAA,SAAAV,EAAAoD,OAAA,SAAApD,EAAA0B,IAAA3B,EAAAuD,EAAAxD,EAAAE,GAAA,UAAAA,EAAAoD,SAAA,WAAAjD,IAAAH,EAAAoD,OAAA,QAAApD,EAAA0B,IAAA,IAAAiC,UAAA,oCAAAxD,EAAA,aAAA6B,EAAA,IAAAzC,EAAAiC,EAAAnB,EAAAP,EAAAY,SAAAV,EAAA0B,KAAA,aAAAnC,EAAAkC,KAAA,OAAAzB,EAAAoD,OAAA,QAAApD,EAAA0B,IAAAnC,EAAAmC,IAAA1B,EAAAqD,SAAA,KAAArB,EAAA,IAAAvB,EAAAlB,EAAAmC,IAAA,OAAAjB,EAAAA,EAAA0C,MAAAnD,EAAAF,EAAA8D,YAAAnD,EAAAF,MAAAP,EAAA6D,KAAA/D,EAAAgE,QAAA,WAAA9D,EAAAoD,SAAApD,EAAAoD,OAAA,OAAApD,EAAA0B,IAAA3B,GAAAC,EAAAqD,SAAA,KAAArB,GAAAvB,GAAAT,EAAAoD,OAAA,QAAApD,EAAA0B,IAAA,IAAAiC,UAAA,oCAAA3D,EAAAqD,SAAA,KAAArB,EAAA,UAAA+B,EAAAhE,GAAA,IAAAD,EAAA,CAAAkE,OAAAjE,EAAA,SAAAA,IAAAD,EAAAmE,SAAAlE,EAAA,SAAAA,IAAAD,EAAAoE,WAAAnE,EAAA,GAAAD,EAAAqE,SAAApE,EAAA,SAAAqE,WAAAxG,KAAAkC,EAAA,UAAAuE,EAAAtE,GAAA,IAAAD,EAAAC,EAAAuE,YAAA,GAAAxE,EAAA2B,KAAA,gBAAA3B,EAAA4B,IAAA3B,EAAAuE,WAAAxE,CAAA,UAAAwB,EAAAvB,GAAA,KAAAqE,WAAA,EAAAJ,OAAA,SAAAjE,EAAAZ,QAAA4E,EAAA,WAAAQ,OAAA,YAAAhC,EAAAzC,GAAA,GAAAA,GAAA,KAAAA,EAAA,KAAAE,EAAAF,EAAAW,GAAA,GAAAT,EAAA,OAAAA,EAAA2B,KAAA7B,GAAA,sBAAAA,EAAA+D,KAAA,OAAA/D,EAAA,IAAA0E,MAAA1E,EAAAjB,QAAA,KAAAwB,GAAA,EAAAd,EAAA,SAAAsE,IAAA,OAAAxD,EAAAP,EAAAjB,QAAA,GAAAsB,EAAAwB,KAAA7B,EAAAO,GAAA,OAAAwD,EAAAtD,MAAAT,EAAAO,GAAAwD,EAAAV,MAAA,EAAAU,EAAA,OAAAA,EAAAtD,MAAAR,EAAA8D,EAAAV,MAAA,EAAAU,CAAA,SAAAtE,EAAAsE,KAAAtE,CAAA,YAAAoE,UAAAd,EAAA/C,GAAA,2BAAAmC,EAAA/B,UAAAgC,EAAA7B,EAAAmC,EAAA,eAAAjC,MAAA2B,EAAAjB,cAAA,IAAAZ,EAAA6B,EAAA,eAAA3B,MAAA0B,EAAAhB,cAAA,IAAAgB,EAAAwC,YAAA1D,EAAAmB,EAAArB,EAAA,qBAAAf,EAAA4E,oBAAA,SAAA3E,GAAA,IAAAD,EAAA,mBAAAC,GAAAA,EAAA4E,YAAA,QAAA7E,IAAAA,IAAAmC,GAAA,uBAAAnC,EAAA2E,aAAA3E,EAAA8E,MAAA,EAAA9E,EAAA+E,KAAA,SAAA9E,GAAA,OAAAE,OAAA6E,eAAA7E,OAAA6E,eAAA/E,EAAAmC,IAAAnC,EAAAgF,UAAA7C,EAAAnB,EAAAhB,EAAAc,EAAA,sBAAAd,EAAAG,UAAAD,OAAAoB,OAAAmB,GAAAzC,CAAA,EAAAD,EAAAkF,MAAA,SAAAjF,GAAA,OAAAgD,QAAAhD,EAAA,EAAA0C,EAAAE,EAAAzC,WAAAa,EAAA4B,EAAAzC,UAAAS,GAAA,0BAAAb,EAAA6C,cAAAA,EAAA7C,EAAAmF,MAAA,SAAAlF,EAAAC,EAAAG,EAAAE,EAAAd,QAAA,IAAAA,IAAAA,EAAA2F,SAAA,IAAAzE,EAAA,IAAAkC,EAAAxB,EAAApB,EAAAC,EAAAG,EAAAE,GAAAd,GAAA,OAAAO,EAAA4E,oBAAA1E,GAAAS,EAAAA,EAAAoD,OAAAb,MAAA,SAAAjD,GAAA,OAAAA,EAAAoD,KAAApD,EAAAQ,MAAAE,EAAAoD,MAAA,KAAApB,EAAAD,GAAAzB,EAAAyB,EAAA3B,EAAA,aAAAE,EAAAyB,EAAA/B,GAAA,0BAAAM,EAAAyB,EAAA,qDAAA1C,EAAAqF,KAAA,SAAApF,GAAA,IAAAD,EAAAG,OAAAF,GAAAC,EAAA,WAAAG,KAAAL,EAAAE,EAAApC,KAAAuC,GAAA,OAAAH,EAAAoF,UAAA,SAAAvB,IAAA,KAAA7D,EAAAnB,QAAA,KAAAkB,EAAAC,EAAAqF,MAAA,GAAAtF,KAAAD,EAAA,OAAA+D,EAAAtD,MAAAR,EAAA8D,EAAAV,MAAA,EAAAU,CAAA,QAAAA,EAAAV,MAAA,EAAAU,CAAA,GAAA/D,EAAAyC,OAAAA,EAAAjB,EAAApB,UAAA,CAAAyE,YAAArD,EAAAiD,MAAA,SAAAzE,GAAA,QAAAwF,KAAA,OAAAzB,KAAA,OAAAN,KAAA,KAAAC,MAAAzD,EAAA,KAAAoD,MAAA,OAAAE,SAAA,UAAAD,OAAA,YAAA1B,IAAA3B,EAAA,KAAAqE,WAAAjF,QAAAkF,IAAAvE,EAAA,QAAAE,KAAA,WAAAA,EAAAuF,OAAA,IAAApF,EAAAwB,KAAA,KAAA3B,KAAAwE,OAAAxE,EAAAwF,MAAA,WAAAxF,GAAAD,EAAA,EAAA0F,KAAA,gBAAAtC,MAAA,MAAApD,EAAA,KAAAqE,WAAA,GAAAE,WAAA,aAAAvE,EAAA0B,KAAA,MAAA1B,EAAA2B,IAAA,YAAAgE,IAAA,EAAAjC,kBAAA,SAAA3D,GAAA,QAAAqD,KAAA,MAAArD,EAAA,IAAAE,EAAA,cAAA2F,EAAAxF,EAAAE,GAAA,OAAAI,EAAAgB,KAAA,QAAAhB,EAAAiB,IAAA5B,EAAAE,EAAA6D,KAAA1D,EAAAE,IAAAL,EAAAoD,OAAA,OAAApD,EAAA0B,IAAA3B,KAAAM,CAAA,SAAAA,EAAA,KAAA+D,WAAAvF,OAAA,EAAAwB,GAAA,IAAAA,EAAA,KAAAd,EAAA,KAAA6E,WAAA/D,GAAAI,EAAAlB,EAAA+E,WAAA,YAAA/E,EAAAyE,OAAA,OAAA2B,EAAA,UAAApG,EAAAyE,QAAA,KAAAsB,KAAA,KAAA3E,EAAAR,EAAAwB,KAAApC,EAAA,YAAAsB,EAAAV,EAAAwB,KAAApC,EAAA,iBAAAoB,GAAAE,EAAA,SAAAyE,KAAA/F,EAAA0E,SAAA,OAAA0B,EAAApG,EAAA0E,UAAA,WAAAqB,KAAA/F,EAAA2E,WAAA,OAAAyB,EAAApG,EAAA2E,WAAA,SAAAvD,GAAA,QAAA2E,KAAA/F,EAAA0E,SAAA,OAAA0B,EAAApG,EAAA0E,UAAA,YAAApD,EAAA,MAAAqC,MAAA,kDAAAoC,KAAA/F,EAAA2E,WAAA,OAAAyB,EAAApG,EAAA2E,WAAA,KAAAR,OAAA,SAAA3D,EAAAD,GAAA,QAAAE,EAAA,KAAAoE,WAAAvF,OAAA,EAAAmB,GAAA,IAAAA,EAAA,KAAAK,EAAA,KAAA+D,WAAApE,GAAA,GAAAK,EAAA2D,QAAA,KAAAsB,MAAAnF,EAAAwB,KAAAtB,EAAA,oBAAAiF,KAAAjF,EAAA6D,WAAA,KAAA3E,EAAAc,EAAA,OAAAd,IAAA,UAAAQ,GAAA,aAAAA,IAAAR,EAAAyE,QAAAlE,GAAAA,GAAAP,EAAA2E,aAAA3E,EAAA,UAAAkB,EAAAlB,EAAAA,EAAA+E,WAAA,UAAA7D,EAAAgB,KAAA1B,EAAAU,EAAAiB,IAAA5B,EAAAP,GAAA,KAAA6D,OAAA,YAAAS,KAAAtE,EAAA2E,WAAAlC,GAAA,KAAA4D,SAAAnF,EAAA,EAAAmF,SAAA,SAAA7F,EAAAD,GAAA,aAAAC,EAAA0B,KAAA,MAAA1B,EAAA2B,IAAA,gBAAA3B,EAAA0B,MAAA,aAAA1B,EAAA0B,KAAA,KAAAoC,KAAA9D,EAAA2B,IAAA,WAAA3B,EAAA0B,MAAA,KAAAiE,KAAA,KAAAhE,IAAA3B,EAAA2B,IAAA,KAAA0B,OAAA,cAAAS,KAAA,kBAAA9D,EAAA0B,MAAA3B,IAAA,KAAA+D,KAAA/D,GAAAkC,CAAA,EAAA6D,OAAA,SAAA9F,GAAA,QAAAD,EAAA,KAAAsE,WAAAvF,OAAA,EAAAiB,GAAA,IAAAA,EAAA,KAAAE,EAAA,KAAAoE,WAAAtE,GAAA,GAAAE,EAAAkE,aAAAnE,EAAA,YAAA6F,SAAA5F,EAAAsE,WAAAtE,EAAAmE,UAAAE,EAAArE,GAAAgC,CAAA,kBAAAjC,GAAA,QAAAD,EAAA,KAAAsE,WAAAvF,OAAA,EAAAiB,GAAA,IAAAA,EAAA,KAAAE,EAAA,KAAAoE,WAAAtE,GAAA,GAAAE,EAAAgE,SAAAjE,EAAA,KAAAI,EAAAH,EAAAsE,WAAA,aAAAnE,EAAAsB,KAAA,KAAApB,EAAAF,EAAAuB,IAAA2C,EAAArE,EAAA,QAAAK,CAAA,QAAA6C,MAAA,0BAAA4C,cAAA,SAAAhG,EAAAE,EAAAG,GAAA,YAAAkD,SAAA,CAAA3C,SAAA6B,EAAAzC,GAAA8D,WAAA5D,EAAA8D,QAAA3D,GAAA,cAAAiD,SAAA,KAAA1B,IAAA3B,GAAAiC,CAAA,GAAAlC,CAAA,UAAAiG,EAAA5F,EAAAJ,EAAAD,EAAAE,EAAAK,EAAAI,EAAAE,GAAA,QAAApB,EAAAY,EAAAM,GAAAE,GAAAE,EAAAtB,EAAAgB,KAAA,OAAAJ,GAAA,YAAAL,EAAAK,EAAA,CAAAZ,EAAA4D,KAAApD,EAAAc,GAAAqE,QAAApC,QAAAjC,GAAAmC,KAAAhD,EAAAK,EAAA,UAAAqL,EAAAvL,GAAA,sBAAAJ,EAAA,KAAAD,EAAAuI,UAAA,WAAAnD,SAAA,SAAAlF,EAAAK,GAAA,IAAAI,EAAAN,EAAAmI,MAAAvI,EAAAD,GAAA,SAAAyI,EAAApI,GAAA4F,EAAAtF,EAAAT,EAAAK,EAAAkI,EAAAC,EAAA,OAAArI,EAAA,UAAAqI,EAAArI,GAAA4F,EAAAtF,EAAAT,EAAAK,EAAAkI,EAAAC,EAAA,QAAArI,EAAA,CAAAoI,OAAA,gBAAAoD,EAAA7L,EAAAE,EAAAD,GAAA,OAAAC,EAAA,SAAAD,GAAA,IAAAR,EAAA,SAAAQ,GAAA,aAAA8C,EAAA9C,KAAAA,EAAA,OAAAA,EAAA,IAAAD,EAAAC,EAAAS,OAAAoL,aAAA,YAAA9L,EAAA,KAAAP,EAAAO,EAAA6B,KAAA5B,EAAAC,UAAA,aAAA6C,EAAAtD,GAAA,OAAAA,EAAA,UAAAoE,UAAA,uDAAAkI,OAAA9L,EAAA,CAAA+L,CAAA/L,GAAA,gBAAA8C,EAAAtD,GAAAA,EAAAA,EAAA,GAAAwM,CAAA/L,MAAAF,EAAAG,OAAAK,eAAAR,EAAAE,EAAA,CAAAO,MAAAR,EAAAiB,YAAA,EAAAC,cAAA,EAAAC,UAAA,IAAApB,EAAAE,GAAAD,EAAAD,CAAA,CAkCA,IAGMkM,EAA+B1N,OAAO2N,KAAKC,SAASC,6BAepDC,EAAa,SAAHpF,GAAyB,IAAnBqF,EAAYrF,EAAZqF,aACpB,OAAOpM,OAAOsC,OAAOyJ,GAA8B/F,KAAI,SAACiF,GAAK,OAC3DxC,EAAAA,cAAC4D,EAAAA,EAAY,CACXC,IAAKrB,EACLsB,SAAUH,EAAanB,GACvBuB,QAAS,WACP9E,EAAAA,EAAKC,kBAAkB8E,8BAA6Bf,EAAC,CAAC,EAAET,GAAQ,GAClE,EACAyB,YAAW,iBAAAxG,OAAmB+E,GAC9BjD,MAAK,gBAAA9B,OAAkB+E,GACvB0B,IAAG,aAAAzG,OAAe+E,IAClB,GAEN,EAEM2B,EAAuB,SAAHC,GAA0B,IAApBC,EAAaD,EAAbC,cAC9B,OACErE,EAAAA,cAAAA,EAAAA,SAAA,KACEA,EAAAA,cAAC4D,EAAAA,EAAY,CACXE,SAAUO,IAAkBC,EAAAA,GAAsBC,KAClDN,YAAY,2BACZ1E,MAAM,yBACN2E,IAAI,uBACJH,QAAS,WACP9E,EAAAA,EAAKC,kBAAkBsF,qBAAqB,CAC1CH,cAAe,QAEnB,IAEFrE,EAAAA,cAAC4D,EAAAA,EAAY,CACXE,SAAUO,IAAkBC,EAAAA,GAAsBG,OAClDR,YAAY,6BACZ1E,MAAM,2BACN2E,IAAI,yBACJH,QAAS,WACP9E,EAAAA,EAAKC,kBAAkBsF,qBAAqB,CAC1CH,cAAe,UAEnB,IAEFrE,EAAAA,cAAC4D,EAAAA,EAAY,CACXE,SAAUO,IAAkBC,EAAAA,GAAsBI,MAClDT,YAAY,4BACZ1E,MAAM,0BACN2E,IAAI,wBACJH,QAAS,WACP9E,EAAAA,EAAKC,kBAAkBsF,qBAAqB,CAC1CH,cAAe,SAEnB,IAEFrE,EAAAA,cAAC4D,EAAAA,EAAY,CACXE,SAAUO,IAAkBC,EAAAA,GAAsBK,KAClDV,YAAY,wBACZ1E,MAAM,uBACN2E,IAAI,uBACJH,QAAS,WACP9E,EAAAA,EAAKC,kBAAkBsF,qBAAqB,CAC1CH,cAAe,QAEnB,IAIR,EAEMO,EAAc,SAAHC,GAAqB,IAAfC,EAAQD,EAARC,SACfC,EAAoBC,EAAAA,GAAsBzH,KAAI,SAAC7H,GAAO,MAAM,CAChEuK,UAAW,+BACX4D,IAAKnO,EAAO,KACZuP,IAAKvP,EAAQwO,IACd,IAEKgB,EAAoBC,EAAAA,GAAsB5H,KAAI,SAAC7H,GAAO,MAAM,CAChEuK,UAAW,+BACX4D,IAAKnO,EAAO,KACZuP,IAAKvP,EAAQwO,IACd,IAED,OACElE,EAAAA,cAAAA,EAAAA,SAAA,KACEA,EAAAA,cAAC4D,EAAAA,EAAY,CACXE,SAAUgB,IAAaM,EAAAA,GAAaC,UACpCpB,YAAY,4BACZ1E,MAAM,0BACN2E,IAAI,iCACJjE,UAAU,oBACV8D,QAAS,WACP9E,EAAAA,EAAKC,kBAAkBoG,oBAAoBF,EAAAA,GAAaC,UAC1D,IAEFrF,EAAAA,cAACuF,EAAAA,EAAQ,CACPtB,YAAY,qCACZuB,OAAQT,EACRU,QAAS,EACTC,YAAa,SAACC,GACZ1G,EAAAA,EAAKC,kBAAkB0G,cAAcD,EACvC,EACA1F,UAAU,wBAEZD,EAAAA,cAAC4D,EAAAA,EAAY,CACXE,SAAUgB,IAAaM,EAAAA,GAAaS,QACpC5B,YAAY,4BACZ1E,MAAM,0BACN2E,IAAI,iCACJjE,UAAU,oBACV8D,QAAS,WACP9E,EAAAA,EAAKC,kBAAkBoG,oBAAoBF,EAAAA,GAAaS,QAC1D,IAEF7F,EAAAA,cAACuF,EAAAA,EAAQ,CACPtB,YAAY,qCACZuB,OAAQN,EACRO,QAAS,EACTC,YAAa,SAACC,GACZ1G,EAAAA,EAAKC,kBAAkB0G,cAAcD,EACvC,EACA1F,UAAU,wBAEZD,EAAAA,cAAC4D,EAAAA,EAAY,CACXK,YAAY,gCACZ1E,MAAM,8BACN2E,IAAI,qBACJH,QAAOf,EAAA7L,IAAAgF,MAAE,SAAAoC,IAAA,OAAApH,IAAAsB,MAAA,SAAAiG,GAAA,cAAAA,EAAA9B,KAAA8B,EAAAvD,MAAA,cAAAuD,EAAAvD,KAAA,EACD8D,EAAAA,EAAKC,kBAAkB4G,iBAAgB,wBAAApH,EAAA3B,OAAA,GAAAwB,EAAA,OAGjDyB,EAAAA,cAAC4D,EAAAA,EAAY,CACXK,YAAY,gCACZ1E,MAAM,8BACN2E,IAAI,qBACJH,QAAOf,EAAA7L,IAAAgF,MAAE,SAAA4J,IAAA,OAAA5O,IAAAsB,MAAA,SAAAuN,GAAA,cAAAA,EAAApJ,KAAAoJ,EAAA7K,MAAA,cAAA6K,EAAA7K,KAAA,EACD8D,EAAAA,EAAKC,kBAAkB+G,iBAAgB,wBAAAD,EAAAjJ,OAAA,GAAAgJ,EAAA,OAKvD,EAqZA,QAnZgC,WAAM,IAAAG,EAC9B/H,GAAWC,EAAAA,EAAAA,MAkBhB+H,EAAA1F,GAVGU,EAAAA,EAAAA,KACF,SAACC,GAAK,MAAK,CACTC,EAAAA,EAAUC,cAAcF,EAAOgF,EAAAA,EAAYC,4BAC3ChF,EAAAA,EAAUiF,gCAAgClF,GAC1CC,EAAAA,EAAUkF,mCAAmCnF,GAC7CC,EAAAA,EAAUmF,sBAAsBpF,GAChCC,EAAAA,EAAUoF,eAAerF,GACzBC,EAAAA,EAAUqF,iBAAiBtF,GAC5B,GACDuF,EAAAA,IACD,GAhBCzF,EAAMiF,EAAA,GACNS,EAAgBT,EAAA,GAChBU,EAAmBV,EAAA,GACnBW,EAAkBX,EAAA,GAClBY,EAAWZ,EAAA,GACXa,EAAab,EAAA,GAawC3F,EAAAC,GAAXC,EAAAA,EAAAA,UAAS,GAAE,GAAhDuG,EAAczG,EAAA,GAAE0G,EAAiB1G,EAAA,GACuB2G,EAAA1G,GAAXC,EAAAA,EAAAA,UAAS,GAAE,GAAxD0G,EAAkBD,EAAA,GAAEE,EAAqBF,EAAA,GACaG,EAAA7G,GAAXC,EAAAA,EAAAA,UAAS,GAAE,GAAtD6G,EAAiBD,EAAA,GAAEE,EAAoBF,EAAA,GACWG,EAAAhH,GAAfC,EAAAA,EAAAA,WAAS,GAAM,GAAlDgH,EAAaD,EAAA,GAAEE,EAAgBF,EAAA,IAEtCG,EAAAA,EAAAA,YAAU,WAAM,IAAAC,EACRC,EAAyB,eAAAC,EAAA/E,EAAA7L,IAAAgF,MAAG,SAAA6L,EAAOpB,GAAgB,OAAAzP,IAAAsB,MAAA,SAAAwP,GAAA,cAAAA,EAAArL,KAAAqL,EAAA9M,MAAA,OACvDgD,EAASU,EAAAA,EAAQqJ,gCAAgCtB,IAAmB,wBAAAqB,EAAAlL,OAAA,GAAAiL,EAAA,KACrE,gBAF8BjI,GAAA,OAAAgI,EAAAnI,MAAA,KAAAD,UAAA,KAGzBwI,EAA+B,SAACtB,GACpC1I,EAASU,EAAAA,EAAQuJ,mCAAmCvB,GACtD,EAKA,OAHA5H,EAAAA,EAAKoJ,cAActG,iBAAiB,0BAA2B+F,GAC7C,QAAlBD,EAAA5I,EAAAA,EAAKoJ,qBAAa,IAAAR,GAAlBA,EAAoB9F,iBAAiB,6BAA8BoG,GAE5D,WACLlJ,EAAAA,EAAKoJ,cAAcrG,oBAAoB,6BAA8BmG,GACrElJ,EAAAA,EAAKoJ,cAAcrG,oBAAoB,0BAA2B8F,EACpE,CACF,GAAG,KAEHF,EAAAA,EAAAA,YAAU,WACJhB,EAAiB0B,WAAaxB,EAAmByB,SAAS3B,EAAiB0B,WAC7EnK,EAASU,EAAAA,EAAQ2J,iCAAiC5B,EAAiB0B,UAEvE,GAAG,CAACxB,EAAoBF,KAExBgB,EAAAA,EAAAA,YAAU,WACR,GAAuB,IAAnBX,GAA+C,IAAvBG,EAA5B,CAIA,IAAMqB,EAAuBxB,EAAiB,GAI5CO,EAFAiB,GAAwBrB,EAEH,EAErBqB,EAAuBrB,GACvBqB,GAAyBrB,EA1NN,IAED,GA0NG,EAErBqB,EAAwBrB,EA9NL,IAED,IA6NlBqB,GAAyBrB,EA/NN,IACS,IACV,IA8NlBqB,GAAwB,IAEH,EAEA,EAnBvB,CAqBF,GAAG,CAACxB,EAAgBG,IAEpB,IAnOwChH,EAiQlCsI,EADiBzJ,EAAAA,EAAKC,kBAAkByJ,iBACV9B,EAAsBD,EACpDgC,EAASF,EAAWG,KACpBC,EAAWJ,EAAWK,OACtBC,EAA4C,WAA9BN,EAAWO,eACzBX,EAAWI,EAAWJ,UAAY,GAClCY,GAAYR,EAAWQ,UACvBC,QAAsCC,IAAdF,GAA0B,GAAKA,GAAUG,WACjEhF,GAAgBqE,EAAWY,oBAAoBjF,cAC/CkF,GAtCuB,SAACC,EAAsBD,EAAYE,GAE9D,IAAMC,EAAcH,EAAaA,EAAaE,EAAWD,EAazD,OAV2BjS,OAAOsC,OAAO8P,EAAAA,IAAsBC,QAAO,SAAC7R,EAAG8R,GACxE,IAAMC,EAAQC,KAAKC,IAAIjS,EAAI2R,GACrBO,EAAQF,KAAKC,IAAIH,EAAIH,GAE3B,OAAII,IAAUG,EACLlS,EAAI8R,EAAI9R,EAAI8R,EAEdI,EAAQH,EAAQD,EAAI9R,CAC7B,KAGE,KAAK,EAQL,QACE,MAAO,SAPT,KAAK,KACH,MAAO,OACT,KAAK,IACH,MAAO,MACT,KAAK,EACH,MAAO,SAIb,CAWmBmS,CACjBxB,EAAWY,oBAAoBE,qBAC/Bd,EAAWY,oBAAoBC,WAC/B3C,EAAiB0C,oBAAoBa,eAAiBC,EAAAA,IAElDtF,GAAW4D,EAAWY,oBAAoBxE,SAE1CuF,GAActD,IAAgBuD,EAAAA,EAAMC,MACpCC,IAjRkCpK,EAiRasI,EAAWtI,OA5QzD,IAAIxK,OAAO2N,KAAKkH,YAAYC,MACjCtK,EAAM9I,EACN8I,EAAMtG,EACNsG,EAAMyJ,EACN,GAPO,IAAIjU,OAAO2N,KAAKkH,YAAYC,MAAM,EAAG,EAAG,EAAG,GAgR9CC,GAAqBN,GAAyC,wBAA3BG,GAAYnB,WAAkE,kBAA3BmB,GAAYnB,WAClGuB,GAAYJ,UAAwB,QAAbtE,EAAXsE,GAAaK,mBAAW,IAAA3E,OAAb,EAAXA,EAAAjN,KAAAuR,IAEZM,GAA6B,SAAClE,GAClC,IAAAmE,EAGInE,GAAoB,CAAC,EAFvBsC,EAAS6B,EAAT7B,UACO8B,EAAYD,EAAnB3K,MAGI6K,EAAqB,cAC3B,IAAK/B,IAAc8B,EACjB,OAAOC,EAGT,IAAMxB,EAAW,GAAHhM,OAAMyL,EAAS,MACzB9I,EAAQ8K,EAAAA,GAAqB,MASjC,OARI9K,IACFA,GAAQ+K,EAAAA,EAAAA,GACNH,EAAa1T,EACb0T,EAAalR,EACbkR,EAAanB,GACb/M,MAAM,GAAI,IAGPvF,OAAOkF,KAAK2O,EAAAA,IAA4BC,MAC7C,SAAC7I,GAAK,OAAK4I,EAAAA,GAA2B5I,GAAOiH,WAAaA,GAAY2B,EAAAA,GAA2B5I,GAAOpC,QAAUA,CAAK,KACpH6K,CACP,EAEA,OAAO/J,EACLlB,EAAAA,cAACkC,EAAAA,EAAkB,CACjB+B,YAAamC,EAAAA,EAAYC,2BACzBpG,UAAU,wBAEVD,EAAAA,cAACsL,EAAAA,EAAO,CACNC,QAAM,EACN3J,SAAU,SAAF4J,GAAkB,IAAbD,EAAMC,EAAND,OACXrE,EAAkBqE,EAAOE,MAC3B,IAEC,SAAAC,GAAA,IAAGC,EAAUD,EAAVC,WAAU,OACZ3L,EAAAA,cAAA,OACEC,UAAU,qCACVwC,IAAKkJ,GAEL3L,EAAAA,cAACsL,EAAAA,EAAO,CACNC,QAAM,EACN3J,SAAU,SAAFgK,GAAkB,IAAbL,EAAMK,EAANL,OACa,IAAvBnE,GAA6BC,EAAsBkE,EAAOE,MAC7D,IAEC,SAAAI,GAAA,IAAGF,EAAUE,EAAVF,WAAU,OACZ3L,EAAAA,cAAA,OACEC,UAAU,cACVwC,IAAKkJ,GAEL3L,EAAAA,cAACuF,EAAAA,EAAQ,CACPuG,MAAOvU,OAAOkF,KAAK2O,EAAAA,IAGnBW,SAAU,WAAF,OAAQpE,GAAiB,EAAM,EACvCjC,YAAW,eAAAsG,EAAAhJ,EAAA7L,IAAAgF,MAAE,SAAA8P,EAAOC,GAAI,IAAAC,EAAAhC,EAAAiC,EAAAC,EAAAC,EAAA,OAAAnV,IAAAsB,MAAA,SAAA8T,GAAA,cAAAA,EAAA3P,KAAA2P,EAAApR,MAAA,OAiBrB,OAhBKgR,EAAcf,EAAAA,GAA2Bc,GACzC/B,EAAgBqC,SAASL,EAAY1C,SAAU,IAC/C2C,EAAY,IAAIxW,OAAO2N,KAAKkH,YAAYC,MAAMyB,EAAY/L,OAC1DiM,EAAkB,CACtB/U,EAAG8U,EAAUK,EACb3S,EAAGsS,EAAUM,EACb7C,EAAGuC,EAAUO,EACb5U,EAAG,KAGCuU,EAAe,CACnBzD,MAAM,EACNE,QAAQ,EACR6D,WAAW,EACX1D,UAAWiB,EACX/J,MAAOiM,GACRE,EAAApR,KAAA,EAEK8D,EAAAA,EAAKC,kBAAkB2N,4BAA4BP,GAAa,cAAAC,EAAApR,KAAA,EAChE8D,EAAAA,EAAKC,kBAAkB4N,mBAAmBR,GAAa,wBAAAC,EAAAxP,OAAA,GAAAkP,EAAA,KAC9D,gBAAAc,GAAA,OAAAf,EAAApM,MAAA,KAAAD,UAAA,EArBU,GAsBXqN,mBAAoB,SAACd,GAAI,OAAAe,EAAAA,EAAA,GAAW7B,EAAAA,GAA2Bc,IAAK,IAAEgB,QAAS,YAAa9M,MAAO,iBAAe,EAClH+M,0BAA0B,EAC1BC,oBAAqBtC,GAA2BpC,GAChD+C,MAAO,IACPxH,YAAY,8BAEdjE,EAAAA,cAACuF,EAAAA,EAAQ,CACPuG,MAAOhF,EACPiF,SAAU,WAAF,OAAQpE,GAAiB,EAAM,EACvCjC,YAAa,SAAC4C,GACY,iBAAbA,GACTrJ,EAAAA,EAAKC,kBAAkB8E,8BAA8B,CAAEsE,SAAAA,GAE3D,EACA0E,mBAAoB,SAACd,GAAI,OAAAe,EAAA,GAAWjG,EAAckF,GAAK,EACvDmB,UAAW,IACXC,oBAAqB,SAACC,GAAI,OAAKzG,EAAmByB,SAASgF,EAAK,EAChE9B,MAAO,IACPxH,YAAY,qBACZmJ,oBAAqB9E,EACrBkF,UAAQ,IAEVxN,EAAAA,cAACuF,EAAAA,EAAQ,CACPuG,MAAO2B,EAAAA,GACP1B,SAAU,WAAF,OAAQpE,GAAiB,EAAM,EACvCjC,YAAa,SAACwD,GACZ,IAAIiB,EAAgBqC,SAAStD,EAAW,IAEpCpN,MAAMqO,KACRA,EAAgBC,EAAAA,IAGdD,EAAgBuD,EAAAA,GAAUC,IAC5BxD,EAAgBuD,EAAAA,GAAUC,IACjBxD,EAAgBuD,EAAAA,GAAUE,MACnCzD,EAAgBuD,EAAAA,GAAUE,KAE5B3O,EAAAA,EAAKC,kBAAkB8E,8BAA8B,CAAEkF,UAAWiB,GACpE,EACAiD,oBAAqBjE,GACrBsC,MAAO,GACPxH,YAAY,0BACZuJ,UAAQ,EACRK,iBAAiB,IAEjBtG,GAAqB,GACrBvH,EAAAA,cAAAA,EAAAA,SAAA,KACEA,EAAAA,cAAA,OAAKC,UAAU,YACfD,EAAAA,cAAC0D,EAAU,CACTC,aAAc,CACZkF,KAAMD,EACNG,OAAQD,EACR8D,UAAW5D,MAKnBhJ,EAAAA,cAAA,OAAKC,UAAU,YACfD,EAAAA,cAAC8N,EAAAA,EAAmB,CAClB/J,QAAS,WAAF,OAAQ4D,GAAiB,EAAM,EACtC1D,YAAY,kBACZ1E,MAAM,yBACNqL,UAAWA,GACX1G,IAAI,4BACJ6J,QAAQ,qBACR3N,MAAOoK,GAAYnB,WACnB2E,cAAa,GAAAvQ,OAAKkN,GAAqB,cAAgB,GAAE,sBAE3D3K,EAAAA,cAACoC,EAAkB,CACjB/B,cAAe,SAAC4N,EAAGC,GACjB,IAAM9N,EAAQ,CACZ9I,EAAG4W,EAASzB,EACZ3S,EAAGoU,EAASxB,EACZ7C,EAAGqE,EAASvB,EACZ5U,EAAG,KAELkH,EAAAA,EAAKC,kBAAkB8E,8BAA8B,CAAE5D,MAAAA,IACvDjC,EAASU,EAAAA,EAAQ6C,cAAc,CAAC,uBAClC,EACAtB,MAAOoK,KAEPjD,GAAqB,GACrBvH,EAAAA,cAAAA,EAAAA,SAAA,KACEA,EAAAA,cAAA,OAAKC,UAAU,YACfD,EAAAA,cAACmE,EAAoB,CAACE,cAAeA,MAGzCrE,EAAAA,cAAA,OAAKC,UAAU,YACfD,EAAAA,cAACuF,EAAAA,EAAQ,CACPuG,MAAOvU,OAAOkF,KAAKkN,EAAAA,IACnBjE,YAAa,SAACyI,GACZ,IAAMzE,EAAcC,EAAAA,GAAqBwE,GACzClP,EAAAA,EAAKC,kBAAkBsF,qBAAqB,CAC1C,qBAAwBkF,IAE1BzK,EAAAA,EAAKC,kBAAkB4N,mBAAmB,CACxCvD,WAAAA,IAEJ,EACA6D,oBAAqB7D,GACrBkC,MAAO,GACPxH,YAAY,6BACZmK,cAAe,SAAClN,GAAM,OACpBlB,EAAAA,cAAC4D,EAAAA,EAAY,CACXrE,MAAM,2BACN2E,IAAI,kCACJJ,SAAU5C,EACV6C,QAAS,WAAF,OAAQ4D,GAAiB,EAAM,GACtC,IAGN3H,EAAAA,cAAA,OAAKC,UAAU,YACfD,EAAAA,cAACuF,EAAAA,EAAQ,CACPtB,YAAamC,EAAAA,EAAYiI,wCACzBpO,UAAU,wBACVmO,cAAe,SAAClN,GAAM,OACpBlB,EAAAA,cAAAA,EAAAA,SAAA,KACEA,EAAAA,cAAC4D,EAAAA,EAAY,CACXrE,MAAM,qBACN2E,IAAI,WACJJ,SAAU5C,IAEZlB,EAAAA,cAACsO,EAAAA,EAAI,CAACrO,UAAU,QAAQsO,MAAK,gBAAA9Q,OAAkByD,EAAS,KAAO,UAC9D,GAGLlB,EAAAA,cAACwO,EAAAA,EAA4B,OAE/BxO,EAAAA,cAAAA,EAAAA,SAAA,KACEA,EAAAA,cAAC4D,EAAAA,EAAY,CACX3D,UAAU,oBACVgE,YAAamC,EAAAA,EAAYqI,wCACzBlP,MAAM,2BACN2E,IAAI,uBACJH,QAAS,WCjhB/B,IAAqB2K,EACsC,QAAzDA,GAAAjP,EAAAA,EAAAA,MAAcC,cAAc,qCAA6B,IAAAgP,GAAzDA,EAA2DC,ODkhBvC,IAEF3O,EAAAA,cAAC4O,EAAkC,OAEb,IAAtBrH,GACAvH,EAAAA,cAAAA,EAAAA,SAAA,KACEA,EAAAA,cAAA,OAAKC,UAAU,YACfD,EAAAA,cAAC4E,EAAW,CAACE,SAAUA,MAGzByC,EAAoB,GACpBvH,EAAAA,cAAAA,EAAAA,SAAA,KACEA,EAAAA,cAAA,OAAKC,UAAU,YACfD,EAAAA,cAAA,OAAKC,UAAU,yBACbD,EAAAA,cAAC4D,EAAAA,EAAY,CACX3D,UAAU,oBACV6D,SAAU4D,EACVzD,YAAY,2BACZ1E,MAAM,cACN2E,IAAI,2BACJH,QAAS,WAAF,OAAQ4D,GAAkBD,EAAc,IAEhDA,GACC1H,EAAAA,cAAA,OAAKC,UAAU,iDACbD,EAAAA,cAAA,OAAKC,UAAU,eACXsH,EAAoB,GACpBvH,EAAAA,cAAAA,EAAAA,SAAA,KACEA,EAAAA,cAAC0D,EAAU,CACTC,aAAc,CACZkF,KAAMD,EACNG,OAAQD,EACR8D,UAAW5D,KAGfhJ,EAAAA,cAAA,OAAKC,UAAU,aAGjBsH,EAAoB,GACpBvH,EAAAA,cAAAA,EAAAA,SAAA,KACEA,EAAAA,cAACmE,EAAoB,CAACE,cAAeA,KACrCrE,EAAAA,cAAA,OAAKC,UAAU,aAGjBsH,EAAoB,GACpBvH,EAAAA,cAAC4E,EAAW,CAACE,SAAUA,SAQjC,IAGN,KAIV,IACN,mBEjlBA,IAAIxP,EAAM,EAAQ,OACFC,EAAU,EAAQ,OAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACJ,EAAOC,GAAIG,EAAS,MAwDjCD,EAAIC,EArDH,CAEdG,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,SAAQC,GAAMH,EAASrB,KAAKwB,KAG3DJ,EAAKE,iBAAiB,KAAKC,SAAQC,IAC7BA,EAAGC,YACLJ,EAASrB,QAAQkB,EAAwBC,EAASK,EAAGC,YACvD,IAGKJ,CACT,CAWkBH,CAAwB,qBAG1C,MAAMQ,EAAkB,GACxB,IAAK,IAAIC,EAAI,EAAGA,EAAIZ,EAAcE,OAAQU,IAAK,CAC7C,MAAMC,EAAeb,EAAcY,GACnC,GAAU,IAANA,EACFC,EAAaH,WAAWX,YAAYL,GACpCA,EAASoB,OAAS,WACZH,EAAgBT,OAAS,GAC3BS,EAAgBH,SAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,SAAS,GAG9C,MACK,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaH,WAAWX,YAAYgB,GACpCJ,EAAgB1B,KAAK8B,EACvB,CACF,CACF,EACdtB,WAAoB,IAMpBP,EAAOH,QAAUO,EAAQF,QAAU,CAAC,oBChEpCL,EADkC,EAAQ,MAChCC,EAA4B,IAE9BC,KAAK,CAACC,EAAOC,GAAI,iiGAAkiG,KAE3jGJ,EAAQK,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,QAEvBF,EAAOH,QAAUA", "sources": ["webpack://webviewer-ui/./src/components/Header/OfficeHeader.scss", "webpack://webviewer-ui/./src/components/ColorPickerOverlay/ColorPickerOverlay.scss?ee31", "webpack://webviewer-ui/./src/components/OfficeEditorImageFilePickerHandler/OfficeEditorImageFilePickerHandler.js", "webpack://webviewer-ui/./src/components/OfficeEditorImageFilePickerHandler/index.js", "webpack://webviewer-ui/./src/components/ColorPickerOverlay/ColorPickerOverlay.js", "webpack://webviewer-ui/./src/components/ColorPickerOverlay/index.js", "webpack://webviewer-ui/./src/components/Header/OfficeEditorToolsHeader.js", "webpack://webviewer-ui/./src/helpers/openOfficeEditorFilePicker.js", "webpack://webviewer-ui/./src/components/Header/OfficeHeader.scss?4e7d", "webpack://webviewer-ui/./src/components/ColorPickerOverlay/ColorPickerOverlay.scss"], "sourcesContent": ["// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../node_modules/css-loader/dist/runtime/api.js\");\nexports = ___CSS_LOADER_API_IMPORT___(false);\n// Module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.MainHeader.OfficeEditorTools{padding:0 8px;align-items:center;flex-direction:row}.MainHeader.OfficeEditorTools .HeaderItems{width:auto}.MainHeader.OfficeEditorTools .HeaderItems .Dropdown{margin-left:8px}.MainHeader.OfficeEditorTools .HeaderItems .Dropdown .picked-option{text-align:left}.MainHeader.OfficeEditorTools .HeaderItems .action-button-wrapper{display:flex;align-items:center;position:relative}.MainHeader.OfficeEditorTools .HeaderItems .action-button-wrapper .more-tools{position:absolute;top:46px;right:6px;width:auto;padding:0}.MainHeader.OfficeEditorTools .Button .Icon svg{vertical-align:middle}.MainHeader.OfficeEditorTools .icon-text-color{display:flex;align-items:center;justify-content:center}.MainHeader.OfficeEditorTools .list-style-button{margin-right:0!important}.MainHeader.OfficeEditorTools .list-style-dropdown{background:transparent;border:none;width:12px!important;height:32px!important;margin-left:0!important;z-index:0}.MainHeader.OfficeEditorTools .list-style-dropdown.Dropdown__wrapper:hover{border-radius:4px;background:var(--tools-button-hover)}.MainHeader.OfficeEditorTools .list-style-dropdown .Dropdown{padding:0}.MainHeader.OfficeEditorTools .list-style-dropdown .Dropdown svg rect{stroke:none}.MainHeader.OfficeEditorTools .list-style-dropdown .picked-option-text{display:none}.MainHeader.OfficeEditorTools .list-style-dropdown .arrow{padding-left:0}.MainHeader.OfficeEditorTools .list-style-dropdown .Dropdown__items{overflow:hidden}.MainHeader.OfficeEditorTools .list-style-dropdown .Dropdown__items .Dropdown__item{height:74px!important;padding:15px 0;margin:3px;cursor:pointer}.MainHeader.OfficeEditorTools .list-style-dropdown .Dropdown__items .Dropdown__item .officeEditor-list-style-icon{width:60px!important;height:74px!important}.MainHeader.OfficeEditorTools .insert-table-dropdown{margin:0 6px}.MainHeader.OfficeEditorTools .insert-table-dropdown.open{background-color:var(--tools-button-hover);border-radius:4px}.MainHeader.OfficeEditorTools .insert-table-dropdown .display-button{display:flex;align-items:center;cursor:pointer}.MainHeader.OfficeEditorTools .insert-table-dropdown .display-button:hover{background-color:var(--tools-button-hover);border-radius:4px}.MainHeader.OfficeEditorTools .insert-table-dropdown .display-button .Button{background-color:transparent!important}.MainHeader.OfficeEditorTools .insert-table-dropdown .display-button .Icon.arrow{width:12px;height:12px;margin:0 2px}\", \"\"]);\n// Exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};\nmodule.exports = exports;\n", "var api = require(\"!../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../node_modules/css-loader/dist/cjs.js!../../../node_modules/postcss-loader/src/index.js??ruleSet[1].rules[1].use[2]!../../../node_modules/sass-loader/dist/cjs.js!./ColorPickerOverlay.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "import React from 'react';\nimport actions from 'actions';\nimport { useDispatch } from 'react-redux';\nimport getRootNode from 'helpers/getRootNode';\nimport core from 'core';\nimport DataElements from 'constants/dataElement';\n\nimport '../FilePickerHandler/FilePickerHandler.scss';\n\n// TODO: Can we accept any other image formats?\nconst ACCEPTED_FORMATS = ['jpg', 'jpeg', 'png', 'bmp'].map(\n  (format) => `.${format}`,\n).join(', ');\n\nconst toBase64 = (file) => new Promise((resolve, reject) => {\n  const reader = new FileReader();\n  reader.readAsDataURL(file);\n  reader.onload = () => resolve(reader.result);\n  reader.onerror = reject;\n});\n\nconst FilePickerHandler = () => {\n  const dispatch = useDispatch();\n\n  const openDocument = async (e) => {\n    const file = e.target.files[0];\n    if (file) {\n      try {\n        dispatch(actions.openElement(DataElements.LOADING_MODAL));\n        const base64 = await toBase64(file);\n        await core.getOfficeEditor().insertImageAtCursor(base64);\n        dispatch(actions.closeElement(DataElements.LOADING_MODAL));\n      } catch (error) {\n        dispatch(actions.closeElement(DataElements.LOADING_MODAL));\n        dispatch(actions.showWarningMessage({\n          title: 'Error',\n          message: error,\n        }));\n      }\n      const picker = getRootNode().querySelector('#office-editor-file-picker');\n      if (picker) {\n        picker.value = '';\n      }\n    }\n  };\n\n  return (\n    <div className=\"FilePickerHandler\">\n      <input\n        id=\"office-editor-file-picker\"\n        type=\"file\"\n        accept={ACCEPTED_FORMATS}\n        onChange={openDocument}\n      />\n    </div>\n  );\n};\n\nexport default FilePickerHandler;\n", "import FilePickerHandler from './OfficeEditorImageFilePickerHandler';\n\nexport default FilePickerHandler;\n", "import React, { useState, useLayoutEffect, useRef } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { createPortal } from 'react-dom';\nimport selectors from 'selectors';\nimport DataElementWrapper from 'components/DataElementWrapper';\nimport ColorPalette from 'components/ColorPalette';\nimport ColorPalettePicker from 'components/ColorPalettePicker';\nimport actions from 'actions';\n\nimport useOnClickOutside from 'hooks/useOnClickOutside';\nimport getOverlayPositionBasedOn from 'helpers/getOverlayPositionBasedOn';\nimport classNames from 'classnames';\nimport getRootNode from 'helpers/getRootNode';\n\nimport './ColorPickerOverlay.scss';\n\nconst ColorPickerOverlay = ({\n  color,\n  onStyleChange,\n  portalElementId = 'app',\n}) => {\n  const [position, setPosition] = useState(() => ({ left: '555px', right: 'auto', top: 'auto' }));\n  const overlayRef = useRef(null);\n  const [\n    isOpen,\n  ] = useSelector((state) => [\n    selectors.isElementOpen(state, 'colorPickerOverlay'),\n  ]);\n\n  const dispatch = useDispatch();\n\n  const onClickOutside = (e) => {\n    const menuButton = document.querySelector('[data-element=\"textColorButton\"]');\n    const clickedMenuButton = menuButton?.contains(e.target);\n    if (!clickedMenuButton) {\n      dispatch(actions.closeElements(['colorPickerOverlay']));\n    }\n  };\n  useOnClickOutside(overlayRef, onClickOutside);\n\n  useLayoutEffect(() => {\n    if (isOpen) {\n      const onResize = () => {\n        const overlayPosition = getOverlayPositionBasedOn('textColorButton', overlayRef);\n        setPosition(overlayPosition);\n      };\n      onResize();\n\n      window.addEventListener('resize', onResize);\n      return () => window.removeEventListener('resize', onResize);\n    }\n  }, [isOpen]);\n\n  return createPortal(\n    <DataElementWrapper\n      data-element='colorPickerOverlay'\n      className={classNames({\n        ColorPickerOverlay: true,\n        Popup: true,\n        open: isOpen,\n        closed: !isOpen\n      })}\n      style={position}\n      ref={overlayRef}\n    >\n      <ColorPalette\n        color={color}\n        property='TextColor'\n        onStyleChange={onStyleChange}\n        useMobileMinMaxWidth\n      />\n      <ColorPalettePicker\n        color={color}\n        onStyleChange={onStyleChange}\n        enableEdit\n      />\n    </DataElementWrapper>,\n    getRootNode().getElementById(portalElementId),\n  );\n};\n\nexport default ColorPickerOverlay;", "import ColorPickerOverlay from './ColorPickerOverlay';\n\nexport default ColorPickerOverlay;", "import React, { useEffect, useState } from 'react';\nimport { useSelector, useDispatch, shallowEqual } from 'react-redux';\nimport actions from 'actions';\nimport selectors from 'selectors';\nimport Measure from 'react-measure';\nimport core from 'core';\nimport Dropdown from 'components/Dropdown';\nimport ActionButton from 'components/ActionButton';\nimport ToggleElementButton from 'components/ToggleElementButton';\nimport DataElementWrapper from 'components/DataElementWrapper';\nimport OfficeEditorImageFilePickerHandler from 'components/OfficeEditorImageFilePickerHandler';\nimport ColorPickerOverlay from 'components/ColorPickerOverlay';\nimport Icon from 'components/Icon';\nimport OfficeEditorCreateTablePopup from 'components/OfficeEditorCreateTablePopup';\nimport DataElement from 'constants/dataElement';\nimport Theme from 'constants/theme';\nimport {\n  LINE_SPACING_OPTIONS,\n  JUSTIFICATION_OPTIONS,\n  LIST_OPTIONS,\n  DEFAULT_POINT_SIZE,\n  OFFICE_BULLET_OPTIONS,\n  OFFICE_NUMBER_OPTIONS,\n  FONT_SIZE,\n  AVAILABLE_STYLE_PRESET_MAP,\n  AVAILABLE_POINT_SIZES,\n} from 'constants/officeEditor';\nimport { rgbaToHex } from 'helpers/color';\nimport openOfficeEditorFilePicker from 'helpers/openOfficeEditorFilePicker';\n\nimport './Header.scss';\nimport './OfficeHeader.scss';\nimport '../HeaderItems/HeaderItems.scss';\nimport { COMMON_COLORS } from 'constants/commonColors';\n\nconst listOptionsWidth = 121;\nconst justificationOptionsWidth = 209;\nconst moreButtonWidth = 77;\nconst officeEditorToggleableStyles = window.Core.Document.OfficeEditorToggleableStyles;\n\nconst convertCoreColorToWebViewerColor = (color) => {\n  if (!color) {\n    return new window.Core.Annotations.Color(0, 0, 0, 1);\n  }\n\n  return new window.Core.Annotations.Color(\n    color.r,\n    color.g,\n    color.b,\n    1,\n  );\n};\n\nconst TextStyles = ({ activeStates }) => {\n  return Object.values(officeEditorToggleableStyles).map((style) => (\n    <ActionButton\n      key={style}\n      isActive={activeStates[style]}\n      onClick={() => {\n        core.getOfficeEditor().updateSelectionAndCursorStyle({ [style]: true });\n      }}\n      dataElement={`office-editor-${style}`}\n      title={`officeEditor.${style}`}\n      img={`icon-text-${style}`}\n    />\n  ));\n};\n\nconst JustificationOptions = ({ justification }) => {\n  return (\n    <>\n      <ActionButton\n        isActive={justification === JUSTIFICATION_OPTIONS.Left}\n        dataElement='office-editor-left-align'\n        title='officeEditor.leftAlign'\n        img='icon-menu-left-align'\n        onClick={() => {\n          core.getOfficeEditor().updateParagraphStyle({\n            justification: 'left'\n          });\n        }}\n      />\n      <ActionButton\n        isActive={justification === JUSTIFICATION_OPTIONS.Center}\n        dataElement='office-editor-center-align'\n        title='officeEditor.centerAlign'\n        img='icon-menu-centre-align'\n        onClick={() => {\n          core.getOfficeEditor().updateParagraphStyle({\n            justification: 'center'\n          });\n        }}\n      />\n      <ActionButton\n        isActive={justification === JUSTIFICATION_OPTIONS.Right}\n        dataElement='office-editor-right-align'\n        title='officeEditor.rightAlign'\n        img='icon-menu-right-align'\n        onClick={() => {\n          core.getOfficeEditor().updateParagraphStyle({\n            justification: 'right'\n          });\n        }}\n      />\n      <ActionButton\n        isActive={justification === JUSTIFICATION_OPTIONS.Both}\n        dataElement='office-editor-justify'\n        title='officeEditor.justify'\n        img='icon-menu-both-align'\n        onClick={() => {\n          core.getOfficeEditor().updateParagraphStyle({\n            justification: 'both'\n          });\n        }}\n      />\n    </>\n  );\n};\n\nconst ListOptions = ({ listType }) => {\n  const bulletListObjects = OFFICE_BULLET_OPTIONS.map((options) => ({\n    className: 'officeEditor-list-style-icon',\n    key: options.enum,\n    src: options.img\n  }));\n\n  const numberListOptions = OFFICE_NUMBER_OPTIONS.map((options) => ({\n    className: 'officeEditor-list-style-icon',\n    key: options.enum,\n    src: options.img\n  }));\n\n  return (\n    <>\n      <ActionButton\n        isActive={listType === LIST_OPTIONS.Unordered}\n        dataElement='office-editor-bullet-list'\n        title='officeEditor.bulletList'\n        img='icon-office-editor-bullet-list'\n        className='list-style-button'\n        onClick={() => {\n          core.getOfficeEditor().toggleListSelection(LIST_OPTIONS.Unordered);\n        }}\n      />\n      <Dropdown\n        dataElement='office-editor-bullet-list-dropdown'\n        images={bulletListObjects}\n        columns={3}\n        onClickItem={(val) => {\n          core.getOfficeEditor().setListPreset(val);\n        }}\n        className='list-style-dropdown'\n      />\n      <ActionButton\n        isActive={listType === LIST_OPTIONS.Ordered}\n        dataElement='office-editor-number-list'\n        title='officeEditor.numberList'\n        img='icon-office-editor-number-list'\n        className='list-style-button'\n        onClick={() => {\n          core.getOfficeEditor().toggleListSelection(LIST_OPTIONS.Ordered);\n        }}\n      />\n      <Dropdown\n        dataElement='office-editor-number-list-dropdown'\n        images={numberListOptions}\n        columns={3}\n        onClickItem={(val) => {\n          core.getOfficeEditor().setListPreset(val);\n        }}\n        className='list-style-dropdown'\n      />\n      <ActionButton\n        dataElement='office-editor-decrease-indent'\n        title='officeEditor.decreaseIndent'\n        img='ic-indent-decrease'\n        onClick={async () => {\n          await core.getOfficeEditor().decreaseIndent();\n        }}\n      />\n      <ActionButton\n        dataElement='office-editor-increase-indent'\n        title='officeEditor.increaseIndent'\n        img='ic-indent-increase'\n        onClick={async () => {\n          await core.getOfficeEditor().increaseIndent();\n        }}\n      />\n    </>\n  );\n};\n\nconst OfficeEditorToolsHeader = () => {\n  const dispatch = useDispatch();\n  const [\n    isOpen,\n    cursorProperties,\n    selectionProperties,\n    availableFontFaces,\n    activeTheme,\n    cssFontValues,\n  ] = useSelector(\n    (state) => [\n      selectors.isElementOpen(state, DataElement.OFFICE_EDITOR_TOOLS_HEADER),\n      selectors.getOfficeEditorCursorProperties(state),\n      selectors.getOfficeEditorSelectionProperties(state),\n      selectors.getAvailableFontFaces(state),\n      selectors.getActiveTheme(state),\n      selectors.getCSSFontValues(state),\n    ],\n    shallowEqual\n  );\n\n  const [containerWidth, setContainerWidth] = useState(0);\n  const [initialHeaderWidth, setInitialHeaderWidth] = useState(0);\n  const [visibleGroupCount, setVisibleGroupCount] = useState(6);\n  const [showMoreTools, setShowMoreTools] = useState(false);\n\n  useEffect(() => {\n    const onCursorPropertiesUpdated = async (cursorProperties) => {\n      dispatch(actions.setOfficeEditorCursorProperties(cursorProperties));\n    };\n    const onSelectionPropertiesUpdated = (selectionProperties) => {\n      dispatch(actions.setOfficeEditorSelectionProperties(selectionProperties));\n    };\n\n    core.getDocument().addEventListener('cursorPropertiesUpdated', onCursorPropertiesUpdated);\n    core.getDocument()?.addEventListener('selectionPropertiesUpdated', onSelectionPropertiesUpdated);\n\n    return () => {\n      core.getDocument().removeEventListener('selectionPropertiesUpdated', onSelectionPropertiesUpdated);\n      core.getDocument().removeEventListener('cursorPropertiesUpdated', onCursorPropertiesUpdated);\n    };\n  }, []);\n\n  useEffect(() => {\n    if (cursorProperties.fontFace && !availableFontFaces.includes(cursorProperties.fontFace)) {\n      dispatch(actions.addOfficeEditorAvailableFontFace(cursorProperties.fontFace));\n    }\n  }, [availableFontFaces, cursorProperties]);\n\n  useEffect(() => {\n    if (containerWidth === 0 || initialHeaderWidth === 0) {\n      return;\n    }\n\n    const actualContainerWidth = containerWidth - 16;\n    if (\n      actualContainerWidth >= initialHeaderWidth\n    ) {\n      setVisibleGroupCount(6);\n    } else if (\n      actualContainerWidth < initialHeaderWidth &&\n      actualContainerWidth >= (initialHeaderWidth - listOptionsWidth + moreButtonWidth)\n    ) {\n      setVisibleGroupCount(5);\n    } else if (\n      actualContainerWidth < (initialHeaderWidth - listOptionsWidth + moreButtonWidth) &&\n      actualContainerWidth >= (initialHeaderWidth - listOptionsWidth - justificationOptionsWidth + moreButtonWidth) &&\n      actualContainerWidth >= 858 // HeaderItems's width when on small devices (screen width < 900px)\n    ) {\n      setVisibleGroupCount(4);\n    } else {\n      setVisibleGroupCount(3);\n    }\n  }, [containerWidth, initialHeaderWidth]);\n\n  const calculateLineSpacing = (lineHeightMultiplier, lineHeight, fontSize) => {\n    // if lineHeight is provided, it takes precedence, because the rule sets the line height in points (either exact or at least)\n    const lineSpacing = lineHeight ? lineHeight / fontSize : lineHeightMultiplier;\n\n    // Sometimes we get floating points so we locate the closest line spacing option\n    const roundedLineSpacing = Object.values(LINE_SPACING_OPTIONS).reduce((a, b) => {\n      const aDiff = Math.abs(a - lineSpacing);\n      const bDiff = Math.abs(b - lineSpacing);\n\n      if (aDiff === bDiff) {\n        return a < b ? a : b;\n      }\n      return bDiff < aDiff ? b : a;\n    });\n\n    switch (roundedLineSpacing) {\n      case 1:\n        return 'Single';\n      case 1.15:\n        return '1.15';\n      case 1.5:\n        return '1.5';\n      case 2:\n        return 'Double';\n      default:\n        return 'Single';\n    }\n  };\n\n  const isTextSelected = core.getOfficeEditor().isTextSelected();\n  const properties = isTextSelected ? selectionProperties : cursorProperties;\n  const isBold = properties.bold;\n  const isItalic = properties.italic;\n  const isUnderline = properties.underlineStyle === 'single';\n  const fontFace = properties.fontFace || '';\n  const pointSize = properties.pointSize;\n  const pointSizeSelectionKey = pointSize === undefined ? '' : pointSize.toString();\n  const justification = properties.paragraphProperties.justification;\n  const lineHeight = calculateLineSpacing(\n    properties.paragraphProperties.lineHeightMultiplier,\n    properties.paragraphProperties.lineHeight,\n    cursorProperties.paragraphProperties.fontPointSize || DEFAULT_POINT_SIZE,\n  );\n  const listType = properties.paragraphProperties.listType;\n\n  const isLightMode = activeTheme === Theme.LIGHT;\n  const wvFontColor = convertCoreColorToWebViewerColor(properties.color);\n  const useColorIconBorder = isLightMode ? wvFontColor.toString() === 'rgba(255,255,255,1)' : wvFontColor.toString() === 'rgba(0,0,0,1)';\n  const ariaLabel = wvFontColor?.toHexString?.();\n\n  const convertCursorToStylePreset = (cursorProperties) => {\n    const {\n      pointSize,\n      color: currentColor\n    } = cursorProperties || {};\n\n    const defaultStylePreset = 'Normal Text';\n    if (!pointSize || !currentColor) {\n      return defaultStylePreset;\n    }\n\n    const fontSize = `${pointSize}pt`;\n    let color = COMMON_COLORS['black'];\n    if (color) {\n      color = rgbaToHex(\n        currentColor.r,\n        currentColor.g,\n        currentColor.b\n      ).slice(0, -2);\n    }\n\n    return Object.keys(AVAILABLE_STYLE_PRESET_MAP).find(\n      (style) => AVAILABLE_STYLE_PRESET_MAP[style].fontSize === fontSize && AVAILABLE_STYLE_PRESET_MAP[style].color === color\n    ) || defaultStylePreset;\n  };\n\n  return isOpen ? (\n    <DataElementWrapper\n      dataElement={DataElement.OFFICE_EDITOR_TOOLS_HEADER}\n      className='HeaderToolsContainer'\n    >\n      <Measure\n        bounds\n        onResize={({ bounds }) => {\n          setContainerWidth(bounds.width);\n        }}\n      >\n        {({ measureRef }) => (\n          <div\n            className='MainHeader Tools OfficeEditorTools'\n            ref={measureRef}\n          >\n            <Measure\n              bounds\n              onResize={({ bounds }) => {\n                (initialHeaderWidth === 0) && setInitialHeaderWidth(bounds.width);\n              }}\n            >\n              {({ measureRef }) => (\n                <div\n                  className=\"HeaderItems\"\n                  ref={measureRef}\n                >\n                  <Dropdown\n                    items={Object.keys(AVAILABLE_STYLE_PRESET_MAP)}\n                    // TODO: This shouldn't be closing more tools popup\n                    // It shouldn't know about the existence of it.\n                    onOpened={() => setShowMoreTools(false)}\n                    onClickItem={async (item) => {\n                      const stylePreset = AVAILABLE_STYLE_PRESET_MAP[item];\n                      const fontPointSize = parseInt(stylePreset.fontSize, 10);\n                      const fontColor = new window.Core.Annotations.Color(stylePreset.color);\n                      const parsedFontColor = {\n                        r: fontColor.R,\n                        g: fontColor.G,\n                        b: fontColor.B,\n                        a: 255,\n                      };\n\n                      const newTextStyle = {\n                        bold: false,\n                        italic: false,\n                        underline: false,\n                        pointSize: fontPointSize,\n                        color: parsedFontColor\n                      };\n\n                      await core.getOfficeEditor().updateParagraphStylePresets(newTextStyle);\n                      await core.getOfficeEditor().setMainCursorStyle(newTextStyle);\n                    }}\n                    getCustomItemStyle={(item) => ({ ...AVAILABLE_STYLE_PRESET_MAP[item], padding: '20px 10px', color: 'var(--gray-8)' })}\n                    applyCustomStyleToButton={false}\n                    currentSelectionKey={convertCursorToStylePreset(properties)}\n                    width={160}\n                    dataElement=\"office-editor-text-format\"\n                  />\n                  <Dropdown\n                    items={availableFontFaces}\n                    onOpened={() => setShowMoreTools(false)}\n                    onClickItem={(fontFace) => {\n                      if (typeof fontFace === 'string') {\n                        core.getOfficeEditor().updateSelectionAndCursorStyle({ fontFace });\n                      }\n                    }}\n                    getCustomItemStyle={(item) => ({ ...cssFontValues[item] })}\n                    maxHeight={500}\n                    customDataValidator={(font) => availableFontFaces.includes(font)}\n                    width={160}\n                    dataElement=\"office-editor-font\"\n                    currentSelectionKey={fontFace}\n                    hasInput\n                  />\n                  <Dropdown\n                    items={AVAILABLE_POINT_SIZES}\n                    onOpened={() => setShowMoreTools(false)}\n                    onClickItem={(pointSize) => {\n                      let fontPointSize = parseInt(pointSize, 10);\n\n                      if (isNaN(fontPointSize)) {\n                        fontPointSize = DEFAULT_POINT_SIZE;\n                      }\n\n                      if (fontPointSize > FONT_SIZE.MAX) {\n                        fontPointSize = FONT_SIZE.MAX;\n                      } else if (fontPointSize < FONT_SIZE.MIN) {\n                        fontPointSize = FONT_SIZE.MIN;\n                      }\n                      core.getOfficeEditor().updateSelectionAndCursorStyle({ pointSize: fontPointSize });\n                    }}\n                    currentSelectionKey={pointSizeSelectionKey}\n                    width={80}\n                    dataElement=\"office-editor-font-size\"\n                    hasInput\n                    isSearchEnabled={false}\n                  />\n                  {(visibleGroupCount >= 4) && (\n                    <>\n                      <div className=\"divider\" />\n                      <TextStyles\n                        activeStates={{\n                          bold: isBold,\n                          italic: isItalic,\n                          underline: isUnderline\n                        }}\n                      />\n                    </>\n                  )}\n                  <div className=\"divider\" />\n                  <ToggleElementButton\n                    onClick={() => setShowMoreTools(false)}\n                    dataElement='textColorButton'\n                    title='officeEditor.textColor'\n                    ariaLabel={ariaLabel}\n                    img='icon-office-editor-circle'\n                    element='colorPickerOverlay'\n                    color={wvFontColor.toString()}\n                    iconClassName={`${useColorIconBorder ? 'icon-border' : ''} icon-text-color`}\n                  />\n                  <ColorPickerOverlay\n                    onStyleChange={(_, newColor) => {\n                      const color = {\n                        r: newColor.R,\n                        g: newColor.G,\n                        b: newColor.B,\n                        a: 255,\n                      };\n                      core.getOfficeEditor().updateSelectionAndCursorStyle({ color });\n                      dispatch(actions.closeElements(['colorPickerOverlay']));\n                    }}\n                    color={wvFontColor}\n                  />\n                  {(visibleGroupCount >= 5) && (\n                    <>\n                      <div className=\"divider\" />\n                      <JustificationOptions justification={justification} />\n                    </>\n                  )}\n                  <div className=\"divider\" />\n                  <Dropdown\n                    items={Object.keys(LINE_SPACING_OPTIONS)}\n                    onClickItem={(lineSpacingOption) => {\n                      const lineSpacing = LINE_SPACING_OPTIONS[lineSpacingOption];\n                      core.getOfficeEditor().updateParagraphStyle({\n                        'lineHeightMultiplier': lineSpacing\n                      });\n                      core.getOfficeEditor().setMainCursorStyle({\n                        lineHeight,\n                      });\n                    }}\n                    currentSelectionKey={lineHeight}\n                    width={80}\n                    dataElement=\"office-editor-line-spacing\"\n                    displayButton={(isOpen) => (\n                      <ActionButton\n                        title='officeEditor.lineSpacing'\n                        img='icon-office-editor-line-spacing'\n                        isActive={isOpen}\n                        onClick={() => setShowMoreTools(false)}\n                      />\n                    )}\n                  />\n                  <div className=\"divider\" />\n                  <Dropdown\n                    dataElement={DataElement.OFFICE_EDITOR_TOOLS_HEADER_INSERT_TABLE}\n                    className=\"insert-table-dropdown\"\n                    displayButton={(isOpen) => (\n                      <>\n                        <ActionButton\n                          title='officeEditor.table'\n                          img='ic-table'\n                          isActive={isOpen}\n                        />\n                        <Icon className=\"arrow\" glyph={`icon-chevron-${isOpen ? 'up' : 'down'}`} />\n                      </>\n                    )}\n                  >\n                    <OfficeEditorCreateTablePopup />\n                  </Dropdown>\n                  <>\n                    <ActionButton\n                      className=\"tool-group-button\"\n                      dataElement={DataElement.OFFICE_EDITOR_TOOLS_HEADER_INSERT_IMAGE}\n                      title='officeEditor.insertImage'\n                      img='icon-tool-image-line'\n                      onClick={() => {\n                        openOfficeEditorFilePicker();\n                      }}\n                    />\n                    <OfficeEditorImageFilePickerHandler />\n                  </>\n                  {(visibleGroupCount === 6) && (\n                    <>\n                      <div className=\"divider\" />\n                      <ListOptions listType={listType} />\n                    </>\n                  )}\n                  {(visibleGroupCount < 6) && (\n                    <>\n                      <div className=\"divider\" />\n                      <div className=\"action-button-wrapper\">\n                        <ActionButton\n                          className=\"tool-group-button\"\n                          isActive={showMoreTools}\n                          dataElement='office-editor-more-tools'\n                          title='action.more'\n                          img='icon-tools-more-vertical'\n                          onClick={() => setShowMoreTools(!showMoreTools)}\n                        />\n                        {showMoreTools && (\n                          <div className=\"more-tools MainHeader Tools OfficeEditorTools\">\n                            <div className=\"HeaderItems\">\n                              {(visibleGroupCount < 4) && (\n                                <>\n                                  <TextStyles\n                                    activeStates={{\n                                      bold: isBold,\n                                      italic: isItalic,\n                                      underline: isUnderline\n                                    }}\n                                  />\n                                  <div className=\"divider\" />\n                                </>\n                              )}\n                              {(visibleGroupCount < 5) && (\n                                <>\n                                  <JustificationOptions justification={justification} />\n                                  <div className=\"divider\" />\n                                </>\n                              )}\n                              {(visibleGroupCount < 6) && (\n                                <ListOptions listType={listType} />\n                              )}\n                            </div>\n                          </div>\n                        )}\n                      </div>\n                    </>\n                  )}\n                </div>\n              )}\n            </Measure>\n          </div>\n        )}\n      </Measure>\n    </DataElementWrapper>\n  ) : null;\n};\n\nexport default OfficeEditorToolsHeader;\n", "import getRootNode from 'helpers/getRootNode';\n\nexport default () => {\n  getRootNode().querySelector('#office-editor-file-picker')?.click();\n};\n", "var api = require(\"!../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../node_modules/css-loader/dist/cjs.js!../../../node_modules/postcss-loader/src/index.js??ruleSet[1].rules[1].use[2]!../../../node_modules/sass-loader/dist/cjs.js!./OfficeHeader.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../node_modules/css-loader/dist/runtime/api.js\");\nexports = ___CSS_LOADER_API_IMPORT___(false);\n// Module\nexports.push([module.id, \".open.ColorPickerOverlay{visibility:visible}.closed.ColorPickerOverlay{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.ColorPickerOverlay{position:absolute;z-index:70;justify-content:center;align-items:center}.ColorPickerOverlay:empty{padding:0}.ColorPickerOverlay .buttons{display:flex}.ColorPickerOverlay .Button{margin:4px;width:32px;height:32px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ColorPickerOverlay .Button{width:42px;height:42px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .ColorPickerOverlay .Button{width:42px;height:42px}}.ColorPickerOverlay .Button:hover{background:var(--popup-button-hover)}.ColorPickerOverlay .Button:hover:disabled{background:none}.ColorPickerOverlay .Button .Icon{width:18px;height:18px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ColorPickerOverlay .Button .Icon{width:24px;height:24px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .ColorPickerOverlay .Button .Icon{width:24px;height:24px}}.is-vertical.ColorPickerOverlay .Button.main-menu-button{width:100%;border-radius:0;justify-content:flex-start;padding-left:var(--padding-small);padding-right:var(--padding-small);margin:0 0 var(--padding-tiny) 0}.is-vertical.ColorPickerOverlay .Button.main-menu-button:first-child{margin-top:var(--padding-tiny)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .is-vertical.ColorPickerOverlay .Button.main-menu-button{width:100%;height:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .is-vertical.ColorPickerOverlay .Button.main-menu-button{width:100%;height:32px}}.is-vertical.ColorPickerOverlay .Button.main-menu-button .Icon{margin-right:10px}.is-vertical.ColorPickerOverlay .Button.main-menu-button span{white-space:nowrap}@keyframes bottom-up{0%{transform:translateY(100%)}to{transform:translateY(0)}}@keyframes up-bottom{0%{transform:translateY(0)}to{transform:translateY(100%)}}.ColorPickerOverlay{border-radius:4px;box-shadow:0 0 3px 0 var(--document-box-shadow);background:var(--component-background);display:flex;flex-direction:column;padding:16px}\", \"\"]);\n// Exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};\nmodule.exports = exports;\n"], "names": ["exports", "___CSS_LOADER_API_IMPORT___", "push", "module", "id", "locals", "api", "content", "__esModule", "default", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "shadowRoot", "clonedStyleTags", "i", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "_invoke", "AsyncIterator", "invoke", "_typeof", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "resetTryEntry", "completion", "reset", "isNaN", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "ACCEPTED_FORMATS", "map", "format", "concat", "join", "toBase64", "file", "reject", "reader", "FileReader", "readAsDataURL", "result", "onerror", "dispatch", "useDispatch", "openDocument", "_ref", "_callee", "base64", "picker", "_context", "target", "files", "actions", "openElement", "DataElements", "LOADING_MODAL", "core", "getOfficeEditor", "insertImageAtCursor", "closeElement", "t0", "showWarningMessage", "title", "message", "getRootNode", "querySelector", "arguments", "apply", "_next", "_throw", "_x", "React", "className", "accept", "onChange", "color", "onStyleChange", "_ref$portalElementId", "portalElementId", "_useState2", "_slicedToArray", "useState", "left", "right", "top", "position", "setPosition", "overlayRef", "useRef", "isOpen", "useSelector", "state", "selectors", "isElementOpen", "useOnClickOutside", "menuButton", "contains", "closeElements", "useLayoutEffect", "onResize", "overlayPosition", "getOverlayPositionBasedOn", "addEventListener", "removeEventListener", "createPortal", "DataElementWrapper", "classNames", "ColorPickerOverlay", "Popup", "open", "closed", "style", "ref", "ColorPalette", "property", "useMobileMinMaxWidth", "ColorPalettePicker", "enableEdit", "getElementById", "_asyncToGenerator", "_defineProperty", "toPrimitive", "String", "_toPrimitive", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "officeEditorToggleableStyles", "Core", "Document", "OfficeEditorToggleableStyles", "TextStyles", "activeStates", "ActionButton", "key", "isActive", "onClick", "updateSelectionAndCursorStyle", "dataElement", "img", "JustificationOptions", "_ref2", "justification", "JUSTIFICATION_OPTIONS", "Left", "updateParagraphStyle", "Center", "Right", "Both", "ListOptions", "_ref3", "listType", "bulletListObjects", "OFFICE_BULLET_OPTIONS", "src", "numberListOptions", "OFFICE_NUMBER_OPTIONS", "LIST_OPTIONS", "Unordered", "toggleListSelection", "Dropdown", "images", "columns", "onClickItem", "val", "setListPreset", "Ordered", "decreaseIndent", "_callee2", "_context2", "increaseIndent", "_wvFontColor$toHexStr", "_useSelector2", "DataElement", "OFFICE_EDITOR_TOOLS_HEADER", "getOfficeEditorCursorProperties", "getOfficeEditorSelectionProperties", "getAvailableFontFaces", "getActiveTheme", "getCSSFontValues", "shallowEqual", "cursorProperties", "selectionProperties", "availableFontFaces", "activeTheme", "cssFontValues", "containerWidth", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_useState4", "initialHeader<PERSON>idth", "setInitialHeaderWidth", "_useState6", "visibleGroupCount", "setVisibleGroupCount", "_useState8", "showMoreTools", "setShowMoreTools", "useEffect", "_core$getDocument", "onCursorPropertiesUpdated", "_ref6", "_callee3", "_context3", "setOfficeEditorCursorProperties", "onSelectionPropertiesUpdated", "setOfficeEditorSelectionProperties", "getDocument", "fontFace", "includes", "addOfficeEditorAvailableFontFace", "actualContainer<PERSON><PERSON>th", "properties", "isTextSelected", "isBold", "bold", "isItalic", "italic", "isUnderline", "underlineStyle", "pointSize", "pointSizeSelectionKey", "undefined", "toString", "paragraphProperties", "lineHeight", "lineHeightMultiplier", "fontSize", "lineSpacing", "LINE_SPACING_OPTIONS", "reduce", "b", "aDiff", "Math", "abs", "bDiff", "calculateLineSpacing", "fontPointSize", "DEFAULT_POINT_SIZE", "isLightMode", "Theme", "LIGHT", "wvFontColor", "Annotations", "Color", "useColorIconBorder", "aria<PERSON><PERSON><PERSON>", "toHexString", "convertCursorToStylePreset", "_ref7", "currentColor", "defaultStylePreset", "COMMON_COLORS", "rgbaToHex", "AVAILABLE_STYLE_PRESET_MAP", "find", "Measure", "bounds", "_ref8", "width", "_ref9", "measureRef", "_ref10", "_ref11", "items", "onOpened", "_ref12", "_callee4", "item", "stylePreset", "fontColor", "parsedFontColor", "newTextStyle", "_context4", "parseInt", "R", "G", "B", "underline", "updateParagraphStylePresets", "setMainCursorStyle", "_x2", "getCustomItemStyle", "_objectSpread", "padding", "applyCustomStyleToButton", "currentSelectionKey", "maxHeight", "customDataValidator", "font", "hasInput", "AVAILABLE_POINT_SIZES", "FONT_SIZE", "MAX", "MIN", "isSearchEnabled", "ToggleElementButton", "element", "iconClassName", "_", "newColor", "lineSpacingOption", "displayButton", "OFFICE_EDITOR_TOOLS_HEADER_INSERT_TABLE", "Icon", "glyph", "OfficeEditorCreateTablePopup", "OFFICE_EDITOR_TOOLS_HEADER_INSERT_IMAGE", "_getRootNode$querySel", "click", "OfficeEditorImageFilePickerHandler"], "sourceRoot": ""}