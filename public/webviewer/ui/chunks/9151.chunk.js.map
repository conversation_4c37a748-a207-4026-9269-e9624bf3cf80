{"version": 3, "file": "chunks/9151.chunk.js", "mappings": "gGAAA,IAAIA,EAAM,EAAQ,OACFC,EAAU,EAAQ,OAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,GAAIJ,EAAS,MAwDjCD,EAAIC,EArDH,CAEdK,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,SAAQC,GAAMH,EAASI,KAAKD,KAG3DJ,EAAKE,iBAAiB,KAAKC,SAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,YACvD,IAGKL,CACT,CAWkBH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIC,EAAI,EAAGA,EAAIb,EAAcE,OAAQW,IAAK,CAC7C,MAAMC,EAAed,EAAca,GACnC,GAAU,IAANA,EACFC,EAAaH,WAAWZ,YAAYL,GACpCA,EAASqB,OAAS,WACZH,EAAgBV,OAAS,GAC3BU,EAAgBJ,SAASQ,IAEvBA,EAAUC,UAAYvB,EAASuB,SAAS,GAG9C,MACK,CACL,MAAMD,EAAYtB,EAASwB,WAAU,GACrCJ,EAAaH,WAAWZ,YAAYiB,GACpCJ,EAAgBF,KAAKM,EACvB,CACF,CACF,EACdvB,WAAoB,IAMpBF,EAAO4B,QAAU/B,EAAQgC,QAAU,CAAC,C,kBClEpC,IAAIjC,EAAM,EAAQ,OACFC,EAAU,EAAQ,OAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,GAAIJ,EAAS,MAwDjCD,EAAIC,EArDH,CAEdK,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,SAAQC,GAAMH,EAASI,KAAKD,KAG3DJ,EAAKE,iBAAiB,KAAKC,SAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,YACvD,IAGKL,CACT,CAWkBH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIC,EAAI,EAAGA,EAAIb,EAAcE,OAAQW,IAAK,CAC7C,MAAMC,EAAed,EAAca,GACnC,GAAU,IAANA,EACFC,EAAaH,WAAWZ,YAAYL,GACpCA,EAASqB,OAAS,WACZH,EAAgBV,OAAS,GAC3BU,EAAgBJ,SAASQ,IAEvBA,EAAUC,UAAYvB,EAASuB,SAAS,GAG9C,MACK,CACL,MAAMD,EAAYtB,EAASwB,WAAU,GACrCJ,EAAaH,WAAWZ,YAAYiB,GACpCJ,EAAgBF,KAAKM,EACvB,CACF,CACF,EACdvB,WAAoB,IAMpBF,EAAO4B,QAAU/B,EAAQgC,QAAU,CAAC,C,kBClEpC,IAAIjC,EAAM,EAAQ,OACFC,EAAU,EAAQ,OAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,GAAIJ,EAAS,MAwDjCD,EAAIC,EArDH,CAEdK,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,SAAQC,GAAMH,EAASI,KAAKD,KAG3DJ,EAAKE,iBAAiB,KAAKC,SAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,YACvD,IAGKL,CACT,CAWkBH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIC,EAAI,EAAGA,EAAIb,EAAcE,OAAQW,IAAK,CAC7C,MAAMC,EAAed,EAAca,GACnC,GAAU,IAANA,EACFC,EAAaH,WAAWZ,YAAYL,GACpCA,EAASqB,OAAS,WACZH,EAAgBV,OAAS,GAC3BU,EAAgBJ,SAASQ,IAEvBA,EAAUC,UAAYvB,EAASuB,SAAS,GAG9C,MACK,CACL,MAAMD,EAAYtB,EAASwB,WAAU,GACrCJ,EAAaH,WAAWZ,YAAYiB,GACpCJ,EAAgBF,KAAKM,EACvB,CACF,CACF,EACdvB,WAAoB,IAMpBF,EAAO4B,QAAU/B,EAAQgC,QAAU,CAAC,C,kBClEpC,IAAIjC,EAAM,EAAQ,OACFC,EAAU,EAAQ,OAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,GAAIJ,EAAS,MAwDjCD,EAAIC,EArDH,CAEdK,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,SAAQC,GAAMH,EAASI,KAAKD,KAG3DJ,EAAKE,iBAAiB,KAAKC,SAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,YACvD,IAGKL,CACT,CAWkBH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIC,EAAI,EAAGA,EAAIb,EAAcE,OAAQW,IAAK,CAC7C,MAAMC,EAAed,EAAca,GACnC,GAAU,IAANA,EACFC,EAAaH,WAAWZ,YAAYL,GACpCA,EAASqB,OAAS,WACZH,EAAgBV,OAAS,GAC3BU,EAAgBJ,SAASQ,IAEvBA,EAAUC,UAAYvB,EAASuB,SAAS,GAG9C,MACK,CACL,MAAMD,EAAYtB,EAASwB,WAAU,GACrCJ,EAAaH,WAAWZ,YAAYiB,GACpCJ,EAAgBF,KAAKM,EACvB,CACF,CACF,EACdvB,WAAoB,IAMpBF,EAAO4B,QAAU/B,EAAQgC,QAAU,CAAC,C,mBChEpCD,EADkC,EAAQ,MAChCE,EAA4B,IAE9BX,KAAK,CAACnB,EAAOC,GAAI,gsLAAisL,KAE1tL2B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,QAEvB7B,EAAO4B,QAAUA,C,kBCVjB,IAAIhC,EAAM,EAAQ,OACFC,EAAU,EAAQ,OAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,GAAIJ,EAAS,MAwDjCD,EAAIC,EArDH,CAEdK,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,SAAQC,GAAMH,EAASI,KAAKD,KAG3DJ,EAAKE,iBAAiB,KAAKC,SAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,YACvD,IAGKL,CACT,CAWkBH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIC,EAAI,EAAGA,EAAIb,EAAcE,OAAQW,IAAK,CAC7C,MAAMC,EAAed,EAAca,GACnC,GAAU,IAANA,EACFC,EAAaH,WAAWZ,YAAYL,GACpCA,EAASqB,OAAS,WACZH,EAAgBV,OAAS,GAC3BU,EAAgBJ,SAASQ,IAEvBA,EAAUC,UAAYvB,EAASuB,SAAS,GAG9C,MACK,CACL,MAAMD,EAAYtB,EAASwB,WAAU,GACrCJ,EAAaH,WAAWZ,YAAYiB,GACpCJ,EAAgBF,KAAKM,EACvB,CACF,CACF,EACdvB,WAAoB,IAMpBF,EAAO4B,QAAU/B,EAAQgC,QAAU,CAAC,C,mBChEpCD,EADkC,EAAQ,MAChCE,EAA4B,IAE9BX,KAAK,CAACnB,EAAOC,GAAI,m7IAAo7I,KAE78I2B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,QAEvB7B,EAAO4B,QAAUA,C,mBCRjBA,EADkC,EAAQ,MAChCE,EAA4B,IAE9BX,KAAK,CAACnB,EAAOC,GAAI,u5BAAw5B,KAEj7BD,EAAO4B,QAAUA,C,yqFCDjB,IAAMG,EAAY,CAChBC,MAAOC,IAAAA,MAAgBC,WACvBC,SAAUF,IAAAA,KAAeC,WACzBE,SAAUH,IAAAA,KAAeC,WACzBG,iBAAkBJ,IAAAA,OAAiBC,WACnCI,cAAeL,IAAAA,OACfM,aAAcN,IAAAA,QAGVO,EAAQ,IAAIC,EAAAA,GAAkB,CAAEC,cAAe,GAAIC,YAAY,IAE/DC,EAAkBC,EAAAA,YACtB,SAAAC,EAA+EC,GAAiB,IAA7Ff,EAAKc,EAALd,MAAOG,EAAQW,EAARX,SAAUC,EAAQU,EAARV,SAAUC,EAAgBS,EAAhBT,iBAAkBC,EAAaQ,EAAbR,cAAeC,EAAYO,EAAZP,aACvDS,GAAUC,EAAAA,EAAAA,UACuBC,EAAAC,GAAXC,EAAAA,EAAAA,UAAS,GAAE,GAAhCC,EAAMH,EAAA,GAAEI,EAASJ,EAAA,GAC2CK,EAAAJ,GAAjCC,EAAAA,EAAAA,UAAS,CAAEI,MAAO,EAAGC,OAAQ,IAAI,GAA5DC,EAASH,EAAA,GAAEI,EAAYJ,EAAA,GAC1BK,EAAmBxD,OAAOyD,aAE9BC,EAAAA,EAAAA,qBAAoBf,GAAc,iBAAO,CACvCgB,iBAAkB,SAACC,GACjBhB,EAAQiB,QAAQF,iBAAiBC,EACnC,EACAE,YAAa,SAACC,GACZnB,EAAQiB,QAAQC,YAAYC,EAC9B,EACD,KAEDC,EAAAA,EAAAA,YAAU,WACRpB,EAAQiB,QAAQF,iBAAiB1B,EACnC,GAAG,CAACA,KAEJ+B,EAAAA,EAAAA,YAAU,WAAM,IAAAC,EAIYC,EAH1B9B,EAAM+B,WACNvB,SAAgB,QAATqB,EAAPrB,EAASiB,eAAO,IAAAI,GAAhBA,EAAkBG,kBAEK,IAAnBlC,IACa,QAAfgC,EAAAtB,EAAQiB,eAAO,IAAAK,GAAfA,EAAiBJ,YAAY5B,GAEjC,GAAG,CAACA,KAEJ8B,EAAAA,EAAAA,YAAU,WAAM,IAAAK,EAAAC,EACdlC,EAAM+B,WACNvB,SAAgB,QAATyB,EAAPzB,EAASiB,eAAO,IAAAQ,GAAhBA,EAAkBD,iBAClBxB,SAAgB,QAAT0B,EAAP1B,EAASiB,eAAO,IAAAS,GAAhBA,EAAkBC,iBACpB,GAAG,CAAC3C,EAAMrB,OAAQ4B,KAElB6B,EAAAA,EAAAA,YAAU,WACR,IAAMQ,EAAsB,WAC1B,IAAMC,EAAOzE,OAAOyD,YAAcD,EAC9BiB,IAEEzE,OAAOyD,YAAcD,GACvBN,EAAUuB,GAEZjB,EAAmBxD,OAAOyD,YAE9B,EAGA,OAFAzD,OAAO0E,iBAAiB,SAAUF,GAE3B,WACLxE,OAAO2E,oBAAoB,SAAUH,EACvC,CACF,IAEA,IAKMI,EAAe,SAAHC,GAAsB,IAAhBjB,EAASiB,EAATjB,UACtB5B,EAAS4B,EACX,EAGMkB,EAAc,SAAHC,GAAsC,IAAhChB,EAAKgB,EAALhB,MAAOiB,EAAGD,EAAHC,IAAKC,EAAMF,EAANE,OAAQC,EAAKH,EAALG,MACnCC,EAAWvD,EAAMmC,GAGvB,OACEtB,EAAAA,cAAC2C,EAAAA,GAAY,CACXJ,IAAG,GAAAK,OAAKL,GAAGK,OAAGF,EAASG,IACvBlD,MAAOA,EACPmD,YAAa,EACbN,OAAQA,EACRO,SAAUzB,IAET,SAAA0B,GAAA,IAAGC,EAAOD,EAAPC,QAAO,OACTjD,EAAAA,cAAA,OAAKyC,MAAKS,EAAAA,EAAA,GAAOT,GAAK,IAAEU,aAAc,UACnC7D,EAASH,EAAOmC,GAAO,YAxBlB,SAACA,GAAU,IAAA8B,EACzBzD,EAAM0D,MAAM/B,GACG,QAAf8B,EAAAjD,EAAQiB,eAAO,IAAAgC,GAAfA,EAAiBE,oBAAoBhC,EACvC,CAsBYiC,CAAQjC,GACR2B,GACF,IACI,GAGd,EAEA,OACEjD,EAAAA,cAACwD,EAAAA,EAAO,CAACC,QAAM,EAACjD,QAAM,EAACkD,SAAU,SAAFC,GAAkB,IAAbF,EAAME,EAANF,OAClC3C,EAAYoC,EAAAA,EAAC,CAAC,EACTO,GAAM,IAET7C,OAAQ6C,EAAO7C,OAAkB,EAATJ,KAE1BC,EAAU,EACZ,IAEG,SAAAmD,GAAA,IAAGC,EAAUD,EAAVC,WAAU,OACZ7D,EAAAA,cAAA,OAAK8D,IAAKD,EAAYE,UAAU,+BAC9B/D,EAAAA,cAACgE,EAAAA,GAAI,CACHC,yBAA0BtE,EAC1B8C,MAAO,CAAEyB,QAAS,QAClBtD,OAAQC,EAAUD,OAASJ,EAC3BG,MAAOE,EAAUF,MACjBwD,iBAAkB,GAClBL,IAAK3D,EACLiE,SAAUjF,EAAMrB,OAChBuG,UAAW1E,EAAM0E,UACjBhC,YAAaA,EACb9C,SAAU4C,EACV,aAAW,eACXmC,KAAK,iBAEH,GAId,IAGFvE,EAAgBwE,YAAc,kBAC9BxE,EAAgBb,UAAYA,EAE5B,MCxIA,EDwIA,E,aEvIMA,EAAY,CAChBC,MAAOC,IAAAA,MAAgBC,WACvBC,SAAUF,IAAAA,KAAeC,WACzBE,SAAUH,IAAAA,KAAeC,WACzBG,iBAAkBJ,IAAAA,OAAiBC,YAG/BmF,EAAaxE,EAAAA,YACjB,SAAAC,EAAkDC,GAAiB,IAAhEf,EAAKc,EAALd,MAAOG,EAAQW,EAARX,SAAUC,EAAQU,EAARV,SAAUC,EAAgBS,EAAhBT,iBACtBW,GAAUC,EAAAA,EAAAA,UAiChB,OA/BAa,EAAAA,EAAAA,qBAAoBf,GAAc,iBAAO,CACvCgB,iBAAkB,SAACC,GACjBhB,EAAQiB,QAAQD,UAAYA,CAC9B,EACAE,YAAa,SAACC,GACZ,IAAMkB,EAASrC,EAAQiB,QACjBqD,EAAQjC,EAAOlD,SAASgC,GAC9B,GAAKmD,EAAL,CAIA,IAAMC,EAAalC,EAAOmC,wBACpBC,EAAYH,EAAME,wBAGtBC,EAAUC,KAAOH,EAAWG,KAC5BD,EAAUC,KAAOH,EAAWG,IAAMrC,EAAOsC,eAEzCtC,EAAOrB,UAAYyD,EAAUC,IAAMrC,EAAOrB,UAAYuD,EAAWG,IATnE,CAWF,EACD,KAEDtD,EAAAA,EAAAA,YAAU,WACRpB,EAAQiB,QAAQD,UAAY3B,CAC9B,GAAG,CAACA,IAOFQ,EAAAA,cAAA,OACE8D,IAAK3D,EACL4D,UAAU,yBACVxE,SARiB,SAACwF,GACpBxF,EAASwF,EAAEC,OAAO7D,UACpB,EAOImD,KAAK,QAEJnF,EAAM8F,KAAI,SAACvC,EAAUpB,GAAK,OACzBtB,EAAAA,cAACA,EAAAA,SAAc,CAACuC,IAAG,GAAAK,OAAKtB,EAAK,KAAAsB,OAAIF,EAASG,KACvCvD,EAASH,EAAOmC,GACF,IAIzB,IAGFkD,EAAWD,YAAc,aACzBC,EAAWtF,UAAYA,EAEvB,MC/DA,ED+DA,E,moCEkDA,QArGkB,SAAHe,GAA2C,IAArCiF,EAAWjF,EAAXiF,YAAaC,EAAQlF,EAARkF,SAAUC,EAAOnF,EAAPmF,QAYzCC,EAAA/E,GAPGgF,EAAAA,EAAAA,KACF,SAACC,GAAK,IAAAC,EAAA,MAAK,CACTC,EAAAA,EAAUC,oBAAoBH,GAC9BE,EAAAA,EAAUE,2BAA2BJ,GACL,QADWC,EAC3CC,EAAAA,EAAUG,gBAAgBL,UAAM,IAAAC,OAAA,EAAhCA,EAAkCK,eACnC,GACDC,EAAAA,IACD,GAVCC,EAAgBV,EAAA,GAChBW,EAAuBX,EAAA,GACvBQ,EAAcR,EAAA,GAUoChF,EAAAC,GAAZC,EAAAA,EAAAA,UAAS,IAAG,GAA7C0F,EAAY5F,EAAA,GAAE6F,EAAe7F,EAAA,GAC7B8F,EAAqB7F,GAAhB8F,EAAAA,EAAAA,KAAgB,GAApB,GACFC,GAAcjG,EAAAA,EAAAA,WAEpBmB,EAAAA,EAAAA,YAAU,WACR8E,EAAYjF,QAAQkF,OACtB,GAAG,IAEH,IA0BMC,EAAiBC,IAAW,CAChC,cAAc,IAOhB,OACExG,EAAAA,cAAA,OAAK+D,UAAWyC,IAAW,CACzB,8BAA8B,EAC9B,aAAcX,KAEd7F,EAAAA,cAAA,OACE+D,UAAU,2BAEV/D,EAAAA,cAAA,OAAK+D,UAAU,SACZoC,EAAE,+BAAgC,CAAEM,MAAOvB,EAAYpH,UAE1DkC,EAAAA,cAAC0G,EAAAA,EAAM,CACL3C,UAAU,aACV4C,QAASvB,EACTwB,IAAI,yBAGR5G,EAAAA,cAAA,QAAMmF,SAnDQ,SAACJ,GAEjBA,EAAE8B,iBAEF,IAAMC,EAAST,EAAYjF,QAAQ2F,YAC7BC,EAAYC,EAAAA,EAAgBC,2BAA2BJ,EAAOK,eAE/DH,EAAUI,SAGflC,EAAY9G,SAAQ,SAACiJ,GACnB,GAAItB,EAAkB,CACpB,IAAMuB,EAAkBL,EAAAA,EAAgBM,mBAAmBF,EAAYL,GACvEQ,EAAAA,EAAKC,eAAe,CAACH,GAAkBtB,IACvC0B,EAAAA,EAAAA,GAA2BZ,EAAQQ,EACrC,KAAO,CAGL,IAAMA,EAAkBE,EAAAA,EAAKG,sBAAsBN,EAAYL,EAAWhB,IAC1E0B,EAAAA,EAAAA,GAA2BZ,EAAQQ,EACrC,CACF,IAEAnC,IACF,GA4BMnF,EAAAA,cAAA,OACE+D,UAAWwC,EAGXqB,YAAa,SAAC7C,GAAC,OAAKA,EAAE8C,iBAAiB,GAEvC7H,EAAAA,cAAC8H,EAAAA,EAAY,CACXhE,IAAK,SAACzF,GACJgI,EAAYjF,QAAU/C,CACxB,EACA0J,MAAO9B,EACP+B,SAAU,SAACD,GAAK,OAjCO,SAACA,GAChC7B,EAAgB6B,EAClB,CA+B+BE,CAAyBF,EAAM,EACpDG,SAAO,KAGXlI,EAAAA,cAAA,OAAK+D,UAAU,0BACb/D,EAAAA,cAAC0G,EAAAA,EAAM,CACLE,IAAI,kBACJ7C,UAAW,eACXoE,cAAc,MAM1B,E,qCC7BA,MClFA,ECCe,SAAHlI,GAAmC,IAA7B7C,EAAE6C,EAAF7C,GAAIgL,EAAQnI,EAARmI,SAAU9I,EAAQW,EAARX,SACxB0F,EFmCR,SAAmB5H,GACjB,IAAMiL,GAAcjI,EAAAA,EAAAA,QAAO,MAyC3B,OAxCAmB,EAAAA,EAAAA,YAAU,WAGR,IAzBoB+G,EAChBC,EAwBEC,EAAiB/K,SAASgL,cAAc,IAAD7F,OAAKxF,IAE5CsL,EAAaF,GAtCvB,SAA2BpL,GACzB,IAAMuL,EAAgBlL,SAASmL,cAAc,OAE7C,OADAD,EAAcE,aAAa,KAAMzL,GAC1BuL,CACT,CAkCyCG,CAAkB1L,GAUvD,OAPKoL,IA9BeF,EA+BHI,GA9BbH,EAAQhL,OAAOC,+BAAiCuL,EAAAA,EAAAA,MAAgBtL,SAASuL,MAC1EC,aACHX,EACAC,EAAKW,iBAAiBC,qBA+BtBT,EAAW/K,YAAY0K,EAAYjH,SAE5B,WACLiH,EAAYjH,QAAQgI,SACfV,EAAWW,mBACdX,EAAWU,QAEf,CACF,GAAG,CAAChM,IAaGiL,EAAYjH,UACfiH,EAAYjH,QAAU3D,SAASmL,cAAc,QAExCP,EAAYjH,OAIvB,CE9EiBkI,CAAUlM,GAQzB,OAPA4H,EAAOvC,MAAM2F,SAAW,WACxBpD,EAAOvC,MAAMoC,IAAuB,SAAjBuD,EAASvD,IAAiBuD,EAASvD,IAAM,GAAHjC,OAAMwF,EAASvD,IAAG,MAC3EG,EAAOvC,MAAM8G,KAAyB,SAAlBnB,EAASmB,KAAkBnB,EAASmB,KAAO,GAAH3G,OAAMwF,EAASmB,KAAI,MAC/EvE,EAAOvC,MAAM+G,MAA2B,SAAnBpB,EAASoB,MAAmBpB,EAASoB,MAAQ,GAAH5G,OAAMwF,EAASoB,MAAK,MACnFxE,EAAOvC,MAAMgH,cAAgB,OAC7BzE,EAAOvC,MAAMiH,OAAS,KAEfC,EAAAA,EAAAA,cACLrK,EACA0F,EAEJ,E,gwEC4GA,SAxGwB,SAAH/E,GAIf,IAHJiF,EAAWjF,EAAXiF,YACA0E,EAAkB3J,EAAlB2J,mBAAkBC,EAAA5J,EAClBmF,QAAAA,OAAO,IAAAyE,EAAG,WAAQ,EAACA,EAEbC,GAAWC,EAAAA,EAAAA,MACXC,GAAW5J,EAAAA,EAAAA,UAC6BC,EAAAC,GAAZC,EAAAA,EAAAA,UAAS,IAAG,GAAvC0J,EAAS5J,EAAA,GAAE6J,EAAY7J,EAAA,GACwDK,EAAAJ,GAAtDC,EAAAA,EAAAA,UAAS,CAAEgJ,KAAM,OAAQC,MAAO,OAAQ3E,IAAK,SAAS,GAA/EuD,EAAQ1H,EAAA,GAAEyJ,EAAWzJ,EAAA,GACtB0J,EAAc,cAEpBC,EAAAA,EAAAA,GAAkBL,GAAU,SAACjF,GAC3B,IACMuF,EADiB7M,SAASgL,cAAc,iBAAD7F,OAAkBgH,EAAkB,MAC3CW,SAASxF,EAAEC,QAC3CwF,GAAeC,EAAAA,EAAAA,MACfC,GAAcC,EAAAA,EAAAA,MACdC,GAAaC,EAAAA,EAAAA,MAEdP,GAAmBE,GAAiBE,GAAgBE,GAEvDxF,GAEJ,IAEA,IAAI3C,EAAQ,CAAC,EACbyC,EAAY9G,SAAQ,SAACiJ,GACnB5E,EAAKS,EAAAA,EAAA,GAAQT,IAAUqI,EAAAA,EAAAA,GAAoBzD,GAC7C,IACA,IAAM0D,EAAqB7F,EAAY8F,MAAK,SAAC3D,GAC3C,OACEA,aAAsB9J,OAAO0N,KAAKC,YAAYC,qBAC7C9D,EAAW+D,cAAgB7N,OAAO0N,KAAKC,YAAYC,mBAAmBE,OAAOC,UAC9EjE,EAAW+D,cAAgB7N,OAAO0N,KAAKC,YAAYC,mBAAmBE,OAAOE,gBAEjF,IACIC,EAAa,CAAC,EAClB,GAAIT,EAAoB,KAAA3I,EAAAqJ,EAAAnJ,EAAAoJ,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAChBC,EAAiBnB,EAAmBoB,mBAC1CX,EAAa,CACXY,KAAMrB,EAAmBqB,KACzBC,SAAUtB,EAAmBsB,SAC7BC,UAAWvB,EAAmBuB,UAC9BC,kBAAmBxB,EAAmBwB,kBACtCC,KAAqD,QAAjDpK,EAA2C,UAAzC8J,SAAmB,QAALT,EAAdS,EAAiB,UAAE,IAAAT,OAAL,EAAdA,EAAsB,uBAAyB,IAAArJ,GAAAA,EACrDqK,OAAwD,QAAlDnK,EAA0C,YAAxC4J,SAAmB,QAALR,EAAdQ,EAAiB,UAAE,IAAAR,OAAL,EAAdA,EAAsB,sBAA0B,IAAApJ,GAAAA,EACxDoK,WAAWR,SAAmB,QAALP,EAAdO,EAAiB,UAAE,IAAAP,GAAqB,QAArBC,EAAnBD,EAAsB,0BAAkB,IAAAC,OAA1B,EAAdA,EAA0Ce,SAAS,gBAAgBT,SAAmB,QAALL,EAAdK,EAAiB,UAAE,IAAAL,GAAqB,QAArBC,EAAnBD,EAAsB,0BAAkB,IAAAC,OAA1B,EAAdA,EAA0Ca,SAAS,SACjIC,UAA6E,QAApEb,EAAEG,SAAmB,QAALF,EAAdE,EAAiB,UAAE,IAAAF,GAAqB,QAArBC,EAAnBD,EAAsB,0BAAkB,IAAAC,OAA1B,EAAdA,EAA0CU,SAAS,uBAAe,IAAAZ,GAAAA,EAEjF,CAyBA,OAtBAxK,EAAAA,EAAAA,YAAU,WACR,IAAMsL,EAAoB,GAO1B,GANApK,EAAiB,WAAKoK,EAAkBvO,KAAKwO,EAAAA,GAAyBC,YACtEtK,EAAmB,aAAKoK,EAAkBvO,KAAKwO,EAAAA,GAAyBE,cACxEvK,EAAiB,WAAKoK,EAAkBvO,KAAKwO,EAAAA,GAAyBG,aAGpEC,EAAAA,EAAAA,IAA+B9C,EAAayC,EAAmBA,EAAkB,IACpE,CACb,IAAMM,GAAcC,EAAAA,EAAAA,IAA0B,kBAAmB,aACjEtD,EAASuD,EAAAA,EAAQC,YAAYH,IAC7B,IAAmBI,GAAeC,EAAAA,EAAAA,IAAepD,GAAzCH,UACRC,EAAaqD,EACf,CACF,GAAG,CAACrI,IAQqB,IAArB+E,EAAUnM,OACL,KAIPkC,EAAAA,cAACyN,EAAW,CACVrQ,GAAG,2BACHgL,SAAUA,GAEVpI,EAAAA,cAAA,OACE+D,UAAU,wBACVD,IAAKkG,GAELhK,EAAAA,cAAC0N,EAAAA,EAAoB,CACnBxI,YAAaA,EACbzC,MAAOA,EACPkL,QAAM,EACNjK,SAvBe,WACrB,IAAM0E,GAAWwF,EAAAA,EAAAA,GAA0BhE,EAAoBI,GAC/DG,EAAY/B,EACd,EAqBQyF,aAAc9C,EACdX,YAAaA,EACboB,WAAYA,EACZsC,aAAa,EACbC,WAAW,EACXC,sBAAsB,EACtBC,sBAAsB,KAKhC,E,+vECtGA,IAAM/O,GAAY,CAChBgP,eAAgB9O,IAAAA,KAAeC,WAC/B8O,kBAAmB/O,IAAAA,KAAeC,WAClC+O,kBAAmBhP,IAAAA,KAAeC,WAClCgP,eAAgBjP,IAAAA,KAAeC,WAC/BiP,kBAAmBlP,IAAAA,KAAeC,WAClCkP,mBAAoBnP,IAAAA,KAAeC,WACnCmP,mBAAoBpP,IAAAA,OAAiBC,WACrCoP,sBAAuBrP,IAAAA,KAAeC,WACtCqP,yBAA0BtP,IAAAA,MAAgBC,YAGtCsP,GAAuB,SAACzJ,GAAuC,IAA1B0J,EAAiBC,UAAA/Q,OAAA,QAAAgR,IAAAD,UAAA,GAAAA,UAAA,GAAG,EACvDE,EAAW,IAAIC,IAWrB,OAVA9J,EAAY9G,SAAQ,SAACiJ,GACnB,GAAIA,EAAW4H,YAAa,CAC1B,IAAMC,EAAmB1H,EAAAA,EAAK2H,kBAAkB9H,EAAsB,UAAGuH,GACrEM,GACFH,EAASK,IAAIF,EAEjB,MACEH,EAASK,IAAI/H,EAEjB,IACOgI,MAAMC,KAAKP,EACpB,EAEMQ,GAAsB,SAAHtP,GAUnB,IATJiO,EAAcjO,EAAdiO,eACAC,EAAiBlO,EAAjBkO,kBACAC,EAAiBnO,EAAjBmO,kBACAC,EAAcpO,EAAdoO,eACAC,EAAiBrO,EAAjBqO,kBACAC,EAAkBtO,EAAlBsO,mBACAC,EAAkBvO,EAAlBuO,mBACAC,EAAqBxO,EAArBwO,sBACAC,EAAwBzO,EAAxByO,yBAKgBrO,EAAAC,IAAZC,EAAAA,EAAAA,UAAS,IAAG,GAFdiP,EAAgCnP,EAAA,GAChCoP,EAAmCpP,EAAA,GAE/ByJ,GAAWC,EAAAA,EAAAA,MACV5D,EAAqB7F,IAAhB8F,EAAAA,EAAAA,KAAgB,GAApB,GAEFJ,GAA0BV,EAAAA,EAAAA,IAAYG,EAAAA,EAAUE,4BAChD+J,GAAqBpK,EAAAA,EAAAA,IAAYG,EAAAA,EAAUiK,qBAEjDnO,EAAAA,EAAAA,YAAU,WACR,IAAMoO,EAAsB,SAACzK,EAAa0K,GACxC,GAAe,WAAXA,EAAqB,CACvB,IAAMC,EAAmB3M,GAAA,GAAQsL,GACjCtJ,EAAY9G,SAAQ,SAAC0R,UACZD,EAAoBC,EAAMjN,GACnC,IACA4L,EAAsBoB,EACxB,MAAO,GAAe,WAAXD,EAAqB,CAC9B,IAAMC,EAAmB3M,GAAA,GAAQsL,GAGjCtJ,EAAY9G,SAAQ,SAAC0R,GACnB,IAAMC,EAAgBvI,EAAAA,EAAKwI,oBAAoBF,EAAO9J,GAEpD+J,EAAcE,MAAK,SAACC,GAAY,OAAK1B,EAAmB0B,EAAarN,GAAG,KAGxEkN,EAAc3R,SAAQ,SAAC+R,GACrBN,EAAoBM,EAAWtN,IAAMsN,CACvC,GAEJ,IAEA,IAAMC,EAAeC,OAAOC,KAAK9B,GAC3B+B,EAAcF,OAAOC,KAAK9B,GACb4B,EAAaI,QAAO,SAACjO,GAAG,OAAMgO,EAAY5D,SAASpK,EAAI,IAC3DzE,OAAS,GAGtB2Q,EAAsBoB,EAE1B,CACF,EAGA,OADArI,EAAAA,EAAKvF,iBAAiB,oBAAqB0N,OAAqBb,EAAW9I,GACpE,WACLwB,EAAAA,EAAKtF,oBAAoB,oBAAqByN,EAAqB3J,EACrE,CACF,GAAG,CAACwI,EAAoBxI,KAExBzE,EAAAA,EAAAA,YAAU,WACR,OAAO,WACL4M,GAAkB,GAClBM,EAAsB,CAAC,EACzB,CACF,GAAG,KAEHlN,EAAAA,EAAAA,YAAU,WACR,IAAMkP,EAAoC/B,EAAyB8B,QAAO,SAACE,GACzE,OAAOlJ,EAAAA,EAAKmJ,UAAUD,EAAoB1K,EAC5C,IACAyJ,EAAoCgB,EACtC,GAAG,CAAC/B,IAEJ,IACMkC,EADiBpJ,EAAAA,EAAKqJ,kBAAkBrB,EAAkCxJ,GAC9C,EAC5B8K,GAAcF,IAAapB,EAAiC1R,OAAS,GACxE0R,EAAiC1R,OAAS,GAAK0J,EAAAA,EAAKwI,oBAAoBR,EAAiC,GAAIxJ,GAAyBlI,OAAS,GAE5IiT,GAAoBC,EAAAA,EAAAA,cAAY,SAACC,GACrCtC,GAAqBD,EAA0B1I,GAAyB5H,SAAQ,SAAC0R,GAC/E,IAAMoB,GAAkBC,EAAAA,GAAAA,GAAsBrB,EAAOmB,EAAUjL,GAC/D8J,EAAMsB,SAASF,GACf,IAAMG,EAAoB7J,EAAAA,EAAK8J,qBAAqBtL,GACpDqL,EAAkBE,cAAcL,GAChCG,EAAkBG,QAAQ,WAAY,CAACN,EAAiBpB,EAAOuB,EAAkBI,kBAAkB3B,IACrG,IACA1B,GAAkB,EACpB,GAAG,CAACM,EAA0B1I,IAE9B,OAAIkI,EAEAlO,EAAAA,cAAC0R,EAAAA,EAAYC,SAAQ,CAAC5J,MAAO,CAC3B6J,OAAQ,WAAO,IAEf5R,EAAAA,cAAC6R,EAAoB,CACnB3M,YAAayJ,GAAqBD,EAA0B1I,GAC5Db,SAAU,WAAF,OAAQgJ,GAAkB,EAAM,EACxC/I,QAAS,WAAF,OAAQ+I,GAAkB,EAAM,KAO7CnO,EAAAA,cAAA,OAAK+D,UAAU,uBACb/D,EAAAA,cAAA,OAAK+D,UAAU,qBACb/D,EAAAA,cAAC0G,EAAAA,EAAM,CACLoL,YAAaC,GAAAA,EAAaC,wBAC1BC,SAAUvC,GAA0D,IAApChB,EAAyB5Q,OACzD8I,IAAI,wBACJD,QAAS,WACPwH,GAAkB,EACpB,EACA+D,MAAM,mBAERlS,EAAAA,cAACmS,GAAAA,EAAmB,CAClBL,YAAaC,GAAAA,EAAaK,wBAC1BF,MAAO/L,EAAE,4BACTS,IAAK,8BACLyL,cAAeN,GAAAA,EAAaO,kBAC5BL,SAAUvC,GAAkE,IAA5CF,EAAiC1R,SAEnEkC,EAAAA,cAACuS,GAAAA,EAAe,CACdC,mBAAmB,EACnBzB,kBAAmBA,IAErB/Q,EAAAA,cAAC0G,EAAAA,EAAM,CACLoL,YAAaC,GAAAA,EAAaU,wBAC1B7L,IAAI,uBACJqL,SAAUvC,GAAkE,IAA5CF,EAAiC1R,OACjE6I,QAAS,WACP2H,GAAmBD,EACrB,EACA6D,MAAM,iBAEP7D,GACCrO,EAAAA,cAAC0S,GAAe,CACdxN,YAAasK,EACb5F,mBAAmB,mBACnBxE,QAAS,WACPkJ,GAAkB,EACpB,KAEFwC,GACA9Q,EAAAA,cAAC0G,EAAAA,EAAM,CACLoL,YAAaC,GAAAA,EAAaY,wBAC1BV,SAAUvC,IAAuBkB,EACjChK,IAAI,yBACJD,QAAS,WACPa,EAAAA,EAAKoL,iBAAiBlE,EAAyB,GAAIA,EAA0B1I,EAC/E,EACAkM,MAAM,iBAETpB,GACC9Q,EAAAA,cAAC0G,EAAAA,EAAM,CACLoL,YAAaC,GAAAA,EAAac,0BAC1BjM,IAAI,2BACJD,QAAS,WACPa,EAAAA,EAAKsL,mBAAmBpE,EAA0B1I,EACpD,EACAkM,MAAM,mBAEVlS,EAAAA,cAAC0G,EAAAA,EAAM,CACLoL,YAAaC,GAAAA,EAAagB,yBAC1Bd,SAAUvC,GAAkE,IAA5CF,EAAiC1R,OACjE8I,IAAI,mBACJD,QAAS,WACP,IAIMqM,EAAU,CACdd,MALY/L,EAAE,uCAMd8M,QALc9M,EAAE,yCAMhB+M,eALqB/M,EAAE,iBAMvBgN,UAAW,WACT3L,EAAAA,EAAK4L,kBAAkB5D,OAAkCV,EAAW9I,EACtE,GAEF8D,EAASuD,EAAAA,EAAQgG,mBAAmBL,GACtC,EACAd,MAAM,mBAGVlS,EAAAA,cAAA,OACE+D,UAAU,mBAEV/D,EAAAA,cAAC0G,EAAAA,EAAM,CACL3C,UAAU,uBACV4C,QAAS,WACP4H,GAAmB,EACrB,EACA3H,IAAI,yBAKd,EAEA2I,GAAoBrQ,UAAYA,GAEhC,Y,4nCCnOA,IAAMA,GAAY,CAChBC,MAAOC,IAAAA,MAAgBC,WACvBiU,wBAAyBlU,IAAAA,KACzBmU,sBAAuBnU,IAAAA,KAAeC,WACtCmT,kBAAmBpT,IAAAA,KACnBoU,sBAAuBpU,IAAAA,KACvBqU,qBAAsBrU,IAAAA,MAGlBsU,GAAyB,gBAC/B,SAASC,GAAgB1T,GAOtB,IANDd,EAAKc,EAALd,MACAmU,EAAuBrT,EAAvBqT,wBACAC,EAAqBtT,EAArBsT,sBACAf,EAAiBvS,EAAjBuS,kBACAgB,EAAqBvT,EAArBuT,sBACAC,EAAoBxT,EAApBwT,qBAqBCpO,EAAA/E,IAXGgF,EAAAA,EAAAA,KACF,SAACC,GAAK,IAAAC,EAAA,MAAK,CACTC,EAAAA,EAAUmO,gBAAgBrO,GAC1BE,EAAAA,EAAUoO,kBAAkBtO,EAAOmO,IACnCjO,EAAAA,EAAUqO,iCAAiCvO,GAC3CE,EAAAA,EAAUsO,qBAAqBxO,GAC/BE,EAAAA,EAAUuO,sBAAsBzO,GAChCE,EAAAA,EAAUwO,wBAAwB1O,GACF,QADQC,EACxCC,EAAAA,EAAUG,gBAAgBL,UAAM,IAAAC,OAAA,EAAhCA,EAAkCK,eACnC,GACDC,EAAAA,IACD,GAlBCpG,EAAY2F,EAAA,GACZ6O,EAAuB7O,EAAA,GACvB8O,EAAmB9O,EAAA,GACnB+O,EAAiB/O,EAAA,GACjBgP,EAAkBhP,EAAA,GAClBiP,EAAoBjP,EAAA,GACpBQ,EAAcR,EAAA,GAcTc,EAAqB7F,IAAhB8F,EAAAA,EAAAA,KAAgB,GAApB,GACF0D,GAAWC,EAAAA,EAAAA,MACwC1J,EAAAC,IAAfC,EAAAA,EAAAA,WAAS,GAAM,GAAlDgU,EAAalU,EAAA,GAAEmU,EAAgBnU,EAAA,GAC6CK,EAAAJ,IAAfC,EAAAA,EAAAA,WAAS,GAAM,GAA5EkU,EAA0B/T,EAAA,GAAEgU,EAA6BhU,EAAA,GACdiU,EAAArU,IAAZC,EAAAA,EAAAA,UAAS,IAAG,GAA3CqU,EAAWD,EAAA,GAAEE,EAAcF,EAAA,IAElCpT,EAAAA,EAAAA,YAAU,WAER,IAAQuT,EAAwDV,EAAxDU,aAAcC,EAA0CX,EAA1CW,YAAaC,EAA6BZ,EAA7BY,aAAcC,EAAeb,EAAfa,aAC7CH,aAAY,EAAZA,EAAchX,QAAS,IAAKiX,aAAW,EAAXA,EAAajX,QAAS,IAAKkX,aAAY,EAAZA,EAAclX,QAAS,IAAKmX,aAAU,EAAVA,EAAYnX,QAAS,IAC1G0W,GAAiB,GAGnB,IAAMU,EAAoB,SAACnQ,GACzB,IAAAoQ,EAA6CpQ,EAAEqQ,OAAvCC,EAAKF,EAALE,MAAOC,EAAOH,EAAPG,QAASC,EAAMJ,EAANI,OAAQC,EAAQL,EAARK,SAC5BH,EAAMvX,OAAS,GAAKwX,EAAQxX,OAAS,GAAKyX,EAAOzX,OAAS,GAAK0X,EAAS1X,OAAS,EACnF0W,GAAiB,GAEjBA,GAAiB,EAErB,EAGA,OADAjX,OAAO0E,iBAAiBwT,GAAAA,EAAOC,0BAA2BR,GACnD,WACL3X,OAAO2E,oBAAoBuT,GAAAA,EAAOC,0BAA2BR,EAC/D,CACF,GAAG,KAGH3T,EAAAA,EAAAA,YAAU,WACJ8S,GAAsBC,IAAyBqB,GAAAA,GAAwBC,SACzElB,GAA8B,GAC9BnB,EAAsB,IACtBsB,EAAe,KAEfH,GAA8B,EAElC,GAAG,CAACL,EAAoBC,IAExB,IAKMuB,EAAqBC,MAAS,SAAC/N,GAGnCP,EAAAA,EAAKuO,yBACLxC,EAAsBxL,EACxB,GAAG,KAEGiO,EACJhW,EAAAA,cAAA,OAAK+D,UAAU,iBAAiB,eAAc2P,IAC5C1T,EAAAA,cAAA,OAAK+D,UAAU,SAAO,GAAAnB,OAAKuD,EAAE,gBAAe,MAC5CnG,EAAAA,cAACiW,GAAAA,EAAQ,CACPnE,YAAY,qBACZG,SAA2B,IAAjB9S,EAAMrB,QAAgB2W,EAChCyB,UAAS,GAAAtT,OAAKuD,EAAE,kBAAiB,KAAAvD,OAAIlD,GACrCyW,MAAO9F,OAAOC,MAAK8F,EAAAA,GAAAA,OACnBC,kBAAkB,oBAClBC,oBAAqB5W,EACrB6W,YAAa,SAACC,GACZ1M,EAASuD,EAAAA,EAAQoJ,0BAA0BD,GAC7C,KAKAE,GAAmCC,EAAAA,GAAAA,IAAgB,kBAAM7M,EAASuD,EAAAA,EAAQuJ,YAAY,eAAe,IACrGC,EAAuC1Q,EAArBkO,EAAuB,uCAA4C,qCACrFyC,EACJ9W,EAAAA,cAAC+W,GAAAA,EAAkB,CACjBhT,UACEyC,IAAW,CACT,QAAU,EACV,oBAAqBX,IAEzBiM,YAAY,oBAEZ9R,EAAAA,cAAC+W,GAAAA,EAAkB,CACjBhT,UAAWyC,IAAW,CACpB,mBAAmB,EACnB,mBAAoBX,IAEtBiM,YAAaC,GAAAA,EAAaiF,WAAWC,cAAcC,iBAElDrR,GAAkB7F,EAAAA,cAACmX,EAAAA,EAAI,CAACC,MAAM,uBAC/BpX,EAAAA,cAAA,SACEiS,SAAUwC,EACV4C,KAAK,OACLC,YAAazR,EAAiB,GAAKgR,EACnC,aAAYA,EACZ7O,SArDkB,SAACjD,GACzB8P,EAAe9P,EAAEC,OAAO+C,OACxB8N,EAAmB9Q,EAAEC,OAAO+C,MAC9B,EAmDQ3K,GAAG,oBACH2K,MAAO6M,KAIX5U,EAAAA,cAAC+W,GAAAA,EAAkB,CACjBhT,UAAU,mBACV+N,YAAaC,GAAAA,EAAaiF,WAAWC,cAAcM,kBAEnDvX,EAAAA,cAAA,QAAM+D,UAAU,gBAAqCoC,EAArBkO,EAAuB,yBAA8B,yBAA+B,IAAC,IAAAzR,OAAKzD,EAAMrB,OAAM,MAGxIkC,EAAAA,cAAC+W,GAAAA,EAAkB,CACjBhT,UAAU,WACV+N,YAAaC,GAAAA,EAAaiF,WAAWC,cAAcO,UAEjDtD,EAA2BlU,EAAAA,cAAA,OAAK+D,UAAU,mBAA0BiS,EACtEhW,EAAAA,cAAA,OACE+D,UAAU,qBAET0P,IAAyBY,GACxBrU,EAAAA,cAAC0G,EAAAA,EAAM,CACLoL,YAAaC,GAAAA,EAAa0F,8BAC1B1T,UAAWyC,IAAW,CACpBkR,OAAQlF,IAEVP,SAA2B,IAAjB9S,EAAMrB,OAChB8I,IAAI,kCACJD,QAAS,WACPa,EAAAA,EAAKuO,yBACLvC,GACF,EACAtB,MAAO/L,EAAE,iCAGbnG,EAAAA,cAAC0G,EAAAA,EAAM,CACLoL,YAAaC,GAAAA,EAAaiF,WAAWC,cAAcU,yBACnD5T,UAAWyC,IAAW,CACpBkR,OAAQnD,IAEVtC,SAAUqB,EACV1M,IAAI,uBACJD,QAAS+P,EACTxE,MAAO/L,EAAE,yBAOnB,OACEnG,EAAAA,cAAAA,EAAAA,SAAA,KACGmU,GACCnU,EAAAA,cAAC4X,GAAAA,EAAa,CACZC,OAAQ1D,EAAoB0D,OAC5BC,gBAAiB,CAAC3Y,OAInBgV,IAAwBA,EAAoB4D,yBAC7CjB,EAIR,CAEAnD,GAAiBzU,UAAYA,GAE7B,MC9NA,GD8NA,G,gnFEyQA,SA7cmB,SAAHe,GAiBV,IAhBJ+X,EAAqB/X,EAArB+X,sBACA7Y,EAAKc,EAALd,MACA8Y,EAAehY,EAAfgY,gBACAC,EAAkBjY,EAAlBiY,mBACAtD,EAAW3U,EAAX2U,YACAC,EAAc5U,EAAd4U,eACArC,EAAiBvS,EAAjBuS,kBACAjE,EAAkBtO,EAAlBsO,mBACAC,EAAkBvO,EAAlBuO,mBACAC,EAAqBxO,EAArBwO,sBACA0J,EAAqBlY,EAArBkY,sBACAC,EAAwBnY,EAAxBmY,yBACAC,EAAapY,EAAboY,cACAC,EAAiBrY,EAAjBqY,kBACAC,EAAUtY,EAAVsY,WACAC,EAAiBvY,EAAjBuY,kBAGM9Y,GAAe4F,EAAAA,EAAAA,IAAYG,EAAAA,EAAUmO,iBACrCjG,GAASrI,EAAAA,EAAAA,KAAY,SAACC,GAAK,OAAKE,EAAAA,EAAUgT,cAAclT,EAAOwM,GAAAA,EAAa2G,YAAY,IACxFC,GAAarT,EAAAA,EAAAA,KAAY,SAACC,GAAK,OAAKE,EAAAA,EAAUoO,kBAAkBtO,EAAOwM,GAAAA,EAAa2G,YAAY,IAChGE,GAAatT,EAAAA,EAAAA,IAAYG,EAAAA,EAAUoT,cAAe/S,EAAAA,IAClDgT,GAAmBxT,EAAAA,EAAAA,IAAYG,EAAAA,EAAUsT,oBAAqBjT,EAAAA,IAC9DkT,GAAyB1T,EAAAA,EAAAA,KAAY,SAACC,GAAK,OAAKiT,EAAoB/S,EAAAA,EAAUwT,cAAc1T,EAAOiT,GAAqB/S,EAAAA,EAAUyT,mBAAmB3T,EAAM,GAAEO,EAAAA,IAC7JqT,GAAmB7T,EAAAA,EAAAA,IAAYG,EAAAA,EAAU2T,qBACzC1J,GAAqBpK,EAAAA,EAAAA,IAAYG,EAAAA,EAAUiK,oBAC3C2J,GAA0B/T,EAAAA,EAAAA,IAAYG,EAAAA,EAAU6T,8BAChDC,GAAkCjU,EAAAA,EAAAA,IAAYG,EAAAA,EAAU+T,oCACxDC,GAAsBnU,EAAAA,EAAAA,IAAYG,EAAAA,EAAUgU,qBAC5CC,GAAmBpU,EAAAA,EAAAA,IAAYG,EAAAA,EAAUkU,8BAA+B7T,EAAAA,IACxE8T,GAAiCtU,EAAAA,EAAAA,IAAYG,EAAAA,EAAUoU,mCACvD7T,GAA0BV,EAAAA,EAAAA,IAAYG,EAAAA,EAAUE,4BAChD0O,GAAqB/O,EAAAA,EAAAA,IAAYG,EAAAA,EAAUuO,uBAE3ClK,GAAWC,EAAAA,EAAAA,MACV5D,EAAqB7F,IAAhB8F,EAAAA,EAAAA,KAAgB,GAApB,GAEF0T,EAAe9B,GAAyBgB,EACxCe,GAAWC,EAAAA,GAAAA,MAE2D3Z,EAAAC,IAAZC,EAAAA,EAAAA,UAAS,IAAG,GAArEmO,EAAwBrO,EAAA,GAAE4Z,EAA2B5Z,EAAA,GACDK,EAAAJ,IAAfC,EAAAA,EAAAA,WAAS,GAAM,GAApD2N,EAAcxN,EAAA,GAAEyN,GAAiBzN,EAAA,GACmBiU,GAAArU,IAAfC,EAAAA,EAAAA,WAAS,GAAM,GAApD2Z,GAAcvF,GAAA,GAAEvG,GAAiBuG,GAAA,GACmBwF,GAAA7Z,IAAfC,EAAAA,EAAAA,WAAS,GAAM,GAApD8N,GAAc8L,GAAA,GAAE7L,GAAiB6L,GAAA,GACeC,GAAA9Z,IAAnBC,EAAAA,EAAAA,eAASuO,GAAU,GAAhDuL,GAAUD,GAAA,GAAEE,GAAaF,GAAA,GAE1Bja,IAAUC,EAAAA,EAAAA,UAIVma,IAAena,EAAAA,EAAAA,QAAO,GACtBoa,GAA2BjB,EAAmCkB,GAAAA,GAAO,GAAK,IAAOC,KAEvFnZ,EAAAA,EAAAA,YAAU,WACR,IAAMoZ,EAA+B,SAACC,GACpC9Q,EAASuD,EAAAA,EAAQwN,uBAAuBD,GAC1C,EACME,EAAuB,SAAC5V,EAAa0K,GAC1B,aAAXA,GACF0K,GAAcpV,EAAY,GAAGrC,GAEjC,EAKA,OAHA2E,EAAAA,EAAKvF,iBAAiB,6BAA8B0Y,GACpDnT,EAAAA,EAAKvF,iBAAiB,qBAAsB6Y,GAErC,WACLtT,EAAAA,EAAKtF,oBAAoB,6BAA8ByY,GACvDnT,EAAAA,EAAKtF,oBAAoB,qBAAsB4Y,EACjD,CACF,GAAG,IAEH,IAAIC,IAA2B,EAEzB5Y,GAAe,SAAChB,GAChBA,IACFoZ,GAAanZ,QAAUD,GAEzB2I,EAASuD,EAAAA,EAAQ2N,aAAa,+BAChC,EAEMC,GAAwB,SAACC,GAC7B,IAAMle,EAAUke,EAAK/T,cACfgU,EAAa3T,EAAAA,EAAK4T,iBAAiBF,EAAa,QAChDG,EAAoBH,EAAKI,cAAc,qBAG7C,OACEte,aAAO,EAAPA,EAASue,cAAc5O,SAASiI,EAAY2G,kBAC5CJ,aAAU,EAAVA,EAAYI,cAAc5O,SAASiI,EAAY2G,kBAC/CF,aAAiB,EAAjBA,EAAmBE,cAAc5O,SAASiI,EAAY2G,eAE1D,EAoBMC,IAAgBpF,EAAAA,GAAAA,MAAoB1W,GAAc+b,eAAetc,GAAOqR,QAlB3D,SAAC0K,GAClB,IAAIQ,GAAe,EAMnB,GAJI5C,IACF4C,EAAeA,GAAgB5C,EAAiBoC,IAG9CtG,EAAa,CACf,IAAM+G,EAAUT,EAAKU,aAGfC,EAAiB,CAACX,GAAItY,OAAAkZ,GAAKH,IAEjCD,EAAeA,GAAgBG,EAAe5L,KAAKgL,GACrD,CACA,OAAOS,CACT,KAIAna,EAAAA,EAAAA,YAAU,WACJ8O,OAAOC,KAAK2H,GAAiBna,SAAuC,IAA7Bid,IACzCgB,YAAW,WAAM,IAAAva,EAEA,QAAfA,EAAArB,GAAQiB,eAAO,IAAAI,GAAfA,EAAiBH,YAAY0Z,GAC/B,GAAG,EAEP,GAAG,CAAC9C,IAGJ,IAAM+D,GAA+B,SAACtZ,GACpC,OAAI2N,OAAOC,KAAK2H,GAAiBna,QAI/B8W,GACA4G,GACGhL,QAAO,SAAC0K,GACP,OAAOA,EAAKU,aAAa3L,KAAKgL,GAChC,IACChL,MAAK,SAAC0L,GAAO,OAAKA,EAAQ9Y,KAAOH,EAASG,EAAE,GAEnD,EAEgEoZ,GAAA3b,IAAZC,EAAAA,EAAAA,UAAS,CAAC,GAAE,GAAzD2b,GAAkBD,GAAA,GAAEE,GAAqBF,GAAA,GAC1CG,IAAqBpL,EAAAA,EAAAA,cACzB,SAACqL,EAAaC,GACZH,IAAsB,SAAClX,GAAG,OAAA/B,GAAAA,GAAA,GACrB+B,GAAG,GAAAsX,GAAA,GACLD,EAAeD,GAAW,GAE/B,GACA,CAACF,KAGuDK,GAAAlc,IAAZC,EAAAA,EAAAA,UAAS,CAAC,GAAE,GAAnDkc,GAAeD,GAAA,GAAEE,GAAkBF,GAAA,GACpCtW,IAAkB8K,EAAAA,EAAAA,cACtB,SAAC/K,EAAcqW,GACbI,IAAmB,SAACzX,GAAG,OAAA/B,GAAAA,GAAA,GAClB+B,GAAG,GAAAsX,GAAA,GACLD,EAAerW,GAAY,GAEhC,GACA,CAACyW,KAGiEC,GAAArc,IAAZC,EAAAA,EAAAA,UAAS,CAAC,GAAE,GAA7Dqc,GAAoBD,GAAA,GAAEE,GAAuBF,GAAA,GAC9CG,GAAiB,SAACR,EAAcS,GACpCF,IAAwB,SAAC5X,GAAG,OAAA/B,GAAAA,GAAA,GACvB+B,GAAG,GAAAsX,GAAA,GACLD,EAAY,GAAA1Z,OAAAkZ,GAAQ7W,EAAIqX,IAAiB,IAAER,GAAMiB,KAAW,GAEjE,EACMC,GAAmB,SAACV,GACxBO,IAAwB,SAAC5X,GAAG,OAAA/B,GAAAA,GAAA,GACvB+B,GAAG,GAAAsX,GAAA,GACLD,EAAe,IAAE,GAEtB,EACMW,GAAmB,SAACX,EAAcY,GACtC,IAAMC,EAAiBP,GAAqBN,GAC5C,IAAIa,aAAc,EAAdA,EAAgBrf,QAAS,EAAG,CAC9B,IAAMwD,EAAQ6b,EAAeC,QAAQF,GACjC5b,GAAS,IACX6b,EAAeE,OAAO/b,EAAO,GAC7Bub,IAAwB,SAAC5X,GAAG,OAAA/B,GAAAA,GAAA,GACvB+B,GAAG,GAAAsX,GAAA,GACLD,EAAYR,GAAOqB,IAAc,IAGxC,CACF,GAEA5b,EAAAA,EAAAA,YAAU,WACR0Y,EAA4B5J,OAAOiN,OAAO9O,GAC5C,GAAG,CAACA,IAEJ,IAQM+O,GAAc,SAClBpe,EACAmC,GAMG,IADHsQ,EAAM/C,UAAA/Q,OAAA,QAAAgR,IAAAD,UAAA,GAAAA,UAAA,GAAG,WAAQ,EAEb2O,EAAgB,KACpBC,GAAuDrH,EAAAA,GAAAA,MAAoB1W,GAAnEge,EAAqBD,EAArBC,sBAAuBC,EAAmBF,EAAnBE,oBACzBC,EAAqB,IAAVtc,EAAc,KAAOnC,EAAMmC,EAAQ,GAC9CoB,EAAWvD,EAAMmC,GAEnBoc,GAAyBC,KAAyBC,GAAYF,EAAsBE,EAAUlb,MAChG8a,EAAgBxd,EAAAA,cAAC6d,EAAAA,EAAa,CAACC,cAAe,WAAF,OAAQH,EAAoBC,EAAUlb,EAAU,CAAEkW,WAAAA,GAAa,KAI7G,IAYMmF,EAAe,CACnBnJ,YAAAA,EACAhD,OAAAA,EACAoM,WAAY/F,EAAgBvV,EAASG,IACrCob,kBAAmBzW,EAAAA,EAAK0W,kBAAkBxb,EAAUsD,KAA6BtD,EAASyE,cAC1F+U,mBAAAA,GACAE,mBAAAA,GACAK,gBAAAA,GACAvW,gBAAAA,GACAwJ,mBAAAA,EACAyO,wBAtBwB,YACnB3L,GAAqByF,EAAgBvV,EAASG,MACjDqV,GAAmB,SAACkG,GAClB,IAAMC,EAAKnb,GAAA,GAAQkb,GAEnB,cADOC,EAAM3b,EAASG,IACfwb,CACT,IACA7W,EAAAA,EAAK8W,mBAAmB5b,EAAUsD,GAEtC,EAcEuY,qBAAsBvC,GAA6BtZ,GACnDyV,sBAAAA,EACAzY,aAAAA,EACA2Z,wBAAAA,EACAiB,cAAAA,GACAsC,qBAAAA,GACAI,iBAAAA,GACAC,iBAAAA,GACAH,eAAAA,GACAlO,kBAAmB5I,GAWrB,OARI1E,IAAUyZ,IACZgB,YAAW,WACT3D,GAAyB,GAEzBtO,EAASuD,EAAAA,EAAQuJ,YAAY,+BAC/B,GAAG,GAOH5W,EAAAA,cAAA,OAAKsE,KAAK,WAAWP,UAAU,gBAC5ByZ,EACDxd,EAAAA,cAAC0R,EAAAA,EAAYC,SAAQ,CAAC5J,MAAOgW,GAC3B/d,EAAAA,cAACwe,EAAAA,EAAI,CACHlG,kBAAmBA,EACnBmG,wBAAyBlG,EACzBlR,WAAY3E,EACZgc,kBAAmBlQ,EAAmB9L,EAASG,IAC/C2P,kBAAmBA,EACnBiB,qBAAsBmG,EACtB+E,gBAAc,EACdC,kBAAmB,SAACC,GAClB,GAAIA,EAAS,CACX,IAAMhP,EAAmB3M,GAAA,GAAQsL,GAC3BsQ,EAActX,EAAAA,EAAKwI,oBAAoBtN,EAAUsD,GACvD8Y,EAAY1gB,SAAQ,SAAC+R,GACnBN,EAAoBM,EAAWtN,IAAMsN,CACvC,IACA1B,EAAsBoB,GACtBrI,EAAAA,EAAKuX,kBAAkBD,EACzB,KAAO,CACL,IAAMjP,EAAmB3M,GAAA,GAAQsL,GAC3BsQ,EAActX,EAAAA,EAAKwI,oBAAoBtN,EAAUsD,GACvD8Y,EAAY1gB,SAAQ,SAAC+R,UACZN,EAAoBM,EAAWtN,GACxC,IACA4L,EAAsBoB,GACtBrI,EAAAA,EAAKwX,oBAAoB,CAACtc,GAAQE,OAAAkZ,GAAKgD,IACzC,CACF,KAKV,EAEMG,GACJjf,EAAAA,cAAA,OAAK+D,UAAU,cACb/D,EAAAA,cAAA,WACEA,EAAAA,cAACmX,EAAAA,EAAI,CAACpT,UAAU,aAAaqT,MAAM,2CAErCpX,EAAAA,cAAA,KAAG,YAAU,YAAY+D,UAAU,iBAAiBoC,EAAE,uBAIpD+Y,GAAqBxF,SAAAA,EAAkByF,KAC3CzF,EAAiByF,KAChB9K,EAAqB,eAAiB,wCACnC+K,GAAuB1F,SAAAA,EAAkBzG,QAC7CyG,EAAiBzG,QACK9M,EAArBkO,EAAuB,sBAA2B,yBAC/CgL,GACJ3F,GAAoBA,EAAiB4F,gBACjC5F,EAAiB4F,gBACjBnZ,EAAE,iCACFoZ,GAAiC7F,IAAqBA,EAAiB8F,WAAc9F,EACrF+F,GAA+B/F,GAAoBA,EAAiB7B,OAEpE6H,GACJ1f,EAAAA,cAAA,OAAK+D,UAAU,kBACZ0b,GACCzf,EAAAA,cAAC4X,GAAAA,EAAa,CAACC,OAAQ6B,EAAiB7B,SAExC7X,EAAAA,cAAAA,EAAAA,SAAA,KACGuf,IACCvf,EAAAA,cAAA,WACEA,EAAAA,cAACmX,EAAAA,EAAI,CAACpT,UAAU,aAAaqT,MAAO8H,MAGxClf,EAAAA,cAAA,OAAK+D,UAAU,OAAO2L,EAAqB2P,GAA+BD,MAM5EO,GAAyB3f,EAAAA,cAAA,OAAK+D,UAAU,8BAExC6b,GAAwB5f,EAAAA,cAAA,OAAK+D,UAAU,6BAIvC8b,GAAMxP,OAAOC,KAAK2H,GACL,IAAf4H,GAAI/hB,OACNid,GAA0BS,GAAcsE,WAAU,SAAC5E,GAAI,OAAKA,EAAKrY,KAAOgd,GAAI,EAAE,IACrEA,GAAI/hB,QAGiB0d,GAAchL,QAAO,SAAC0K,GAAI,OAAKjD,EAAgBiD,EAAKrY,GAAG,IAE3D/E,SACxBid,GAA0BS,GAAcsE,WAAU,SAAC5E,GAAI,OAAKA,EAAKrY,KAAOwX,EAAU,KAItF,IAAI5X,GAAQ,CAAC,EAOb,OANK4V,IAAkBoB,GAAwBM,IAC7CtX,GAAQ,CAAE9B,MAAO,GAAFiC,OAAKkX,EAAY,MAAMiG,SAAU,GAAFnd,OAAKkX,EAAY,SAG1CnB,IAAehL,GAAUwL,GAAoBd,GAGlErY,EAAAA,cAAA,OAAK+D,UAAU,yBACb/D,EAAAA,cAAA,OACE+D,UAAWyC,IAAW,CACpBwZ,OAAO,EACPhJ,YAAY,IAEdvU,MAAOA,GACP,eAAa,aACbwd,UAAW,WAAF,OAAQzY,EAAAA,EAAKuO,sBAAsB,IAE1C0D,GAAuBM,IAAaZ,GACpCnZ,EAAAA,cAAA,OAAK+D,UAAU,mBACb/D,EAAAA,cAAA,OACE+D,UAAU,uBACV4C,QAAS,WACPmD,EAASuD,EAAAA,EAAQ6S,cAAc,CAACnO,GAAAA,EAAa2G,cAC/C,GAEA1Y,EAAAA,cAACmX,EAAAA,EAAI,CAACC,MAAM,sBAAsBrT,UAAU,iBAIlD/D,EAAAA,cAAAA,EAAAA,SAAA,KACEA,EAAAA,cAAC2T,GAAgB,CACfxU,MAAOqc,GACPlI,wBAA0C,IAAjBnU,EAAMrB,OAC/ByV,sBAAuBsB,EACvBrC,kBAAmBA,EACnBgB,sBA5MoB,WAE1BjF,GADEiE,EAKN,EAuMUiB,qBAAsBmG,IAEE,IAAzB4B,GAAc1d,OACI,IAAjBqB,EAAMrB,OACJ4hB,GAEAT,GAEAzD,GAAc1d,QAAU0c,GAC1Bxa,EAAAA,cAACwE,EAAU,CACTV,IAAK3D,GACLhB,MAAOqc,GACPjc,SAAU4C,GACV3C,iBAAkB+a,GAAanZ,SAE9Bmc,IAGHvd,EAAAA,cAACD,EAAe,CACd+D,IAAK3D,GACLhB,MAAOqc,GACP9b,aAAcA,EACdH,SAAU4C,GACV3C,iBAAkB+a,GAAanZ,QAC/B3B,cAAesb,IAEdwC,IAMJ/K,EAAqBtE,EAAiB0R,GAAwBD,GAA0B,KACxFtL,IAAuB7B,GAAsBgJ,GAAc1d,OAAS,GACnEkC,EAAAA,cAAA,OAAK+D,UAAU,uBACb/D,EAAAA,cAAA,OAAK+D,UAAU,YACf/D,EAAAA,cAACmgB,GAAAA,EAAM,CACLC,UAAQ,EACRC,MAAOla,EAAE,kCACT6B,SAAU,SAACjD,GAAC,OAAKyC,EAAAA,EAAK8Y,kBAAkBC,YAAYxb,EAAEC,OAAO6Z,QAAUlJ,GAAAA,GAAwBC,QAAUD,GAAAA,GAAwB6K,UAAU,OAMpJhO,GACCxS,EAAAA,cAACuP,GAAmB,CAClBrB,eAAgBA,EAChBC,kBAAmBA,GACnB+L,eAAgBA,GAChB9L,kBAAmBA,GACnBC,eAAgBA,GAChBC,kBAAmBA,GACnBC,mBAAoBA,EACpBC,mBAAoBA,EACpBC,sBAAuBA,EACvBC,yBAA0BA,IAG9B1O,EAAAA,cAACygB,GAAAA,EAAqB,CAACC,aAAcrG,GAAYyC,eAAgBA,MAzF7C,IA4F1B,E,65EC9RA,MCvMA,GDMA,SAA6B6D,GAC3B,IAAQrI,EAAkEqI,EAAlErI,kBAAiBsI,EAAiDD,EAA/CnI,kBAAAA,OAAiB,IAAAoI,OAAG9R,EAAS8R,EAAE9O,EAAgB6O,EAAhB7O,YAkBzDzM,EAAA/E,IAVGgF,EAAAA,EAAAA,KACF,SAACC,GAAK,MAAK,CACTE,EAAAA,EAAUgT,cAAclT,EAAOiT,GAAqB1G,GAAeC,GAAAA,EAAa2G,aAChFjT,EAAAA,EAAU2T,oBAAoB7T,GAC9BE,EAAAA,EAAUoU,kCAAkCtU,GAC5CE,EAAAA,EAAUob,kBAAkBtb,GAC5BE,EAAAA,EAAUE,2BAA2BJ,GACrCE,EAAAA,EAAUuO,sBAAsBzO,GACjC,GACDO,EAAAA,IACD,GAhBC6H,EAAMtI,EAAA,GACN8T,EAAgB9T,EAAA,GAChBuU,EAA8BvU,EAAA,GAC9Bwb,EAAiBxb,EAAA,GACjBW,EAAuBX,EAAA,GACvBgP,EAAkBhP,EAAA,GAY8BhF,EAAAC,IAAZC,EAAAA,EAAAA,UAAS,IAAG,GAA3CqU,EAAWvU,EAAA,GAAEwU,EAAcxU,EAAA,GAC6BK,EAAAJ,IAAfC,EAAAA,EAAAA,WAAS,GAAM,GAAxDiS,EAAiB9R,EAAA,GAAE6N,EAAkB7N,EAAA,GAC6BiU,EAAArU,IAAfC,EAAAA,EAAAA,WAAS,GAAM,GAAlE4X,EAAqBxD,EAAA,GAAEyD,EAAwBzD,EAAA,GAEEwF,EAAA7Z,IAA1BC,EAAAA,EAAAA,UAAS,CAAE,EAAG,GAAI,EAAG,KAAK,GAAjDugB,EAAO3G,EAAA,GAAE4G,EAAU5G,EAAA,GACpB6G,GAAWhQ,EAAAA,EAAAA,cAAY,SAAC7R,GAC5B2hB,EADoDjS,UAAA/Q,OAAA,QAAAgR,IAAAD,UAAA,GAAAA,UAAA,GAAG7I,GAC1B7G,EAC7B4hB,EAAU7d,GAAC,CAAC,EAAI4d,GAClB,GAAG,CAAC9a,EAAyB8a,EAAQ,GAAIA,EAAQ,GAAIC,IAC/C5hB,EAAQ2hB,EAAQ9a,IAA4B8a,EAAQ,GAIqB1G,EAAA9Z,IAA3BC,EAAAA,EAAAA,UAAS,CAAE,EAAG,CAAC,EAAG,EAAG,CAAC,IAAK,GAAxE0gB,EAAkB7G,EAAA,GAAE8G,EAAqB9G,EAAA,GAC1ClC,GAAqBlH,EAAAA,EAAAA,cAAY,SAACiH,GACtCgJ,EADwEpS,UAAA/Q,OAAA,QAAAgR,IAAAD,UAAA,GAAAA,UAAA,GAAG7I,GACnCiS,EACxCiJ,EAAqBhe,GAAC,CAAC,EAAI+d,GAC7B,GAAG,CAACjb,EAAyBib,EAAmB,GAAIA,EAAmB,GAAIC,IACrEjJ,EAAkBgJ,EAAmBjb,IAA4Bib,EAAmB,GAEChF,EAAA3b,IAA3BC,EAAAA,EAAAA,UAAS,CAAE,EAAG,CAAC,EAAG,EAAG,CAAC,IAAK,GAApF4gB,EAAwBlF,EAAA,GAAEmF,EAA2BnF,EAAA,GACtDxN,GAAwBuC,EAAAA,EAAAA,cAAY,SAAC0N,GACzCyC,EAD2EtS,UAAA/Q,OAAA,QAAAgR,IAAAD,UAAA,GAAAA,UAAA,GAAG7I,GAChC0Y,EAC9C0C,EAA2Ble,GAAC,CAAC,EAAIie,GACnC,GAAG,CAACnb,EAAyBmb,EAAyB,GAAIA,EAAyB,GAAIC,IACjF5S,EAAqB2S,EAAyBnb,IAA4Bmb,EAAyB,GAgHzG,SAASE,EAAiBC,GACxB,IAAMC,EAAYD,EAAoBtW,MAAK,SAAC8E,GAAK,OAAKA,EAAMb,WAAW,IACjEc,EAAgB,GAWtB,OARIwR,GACFD,EAAoBljB,SAAQ,SAAC0R,GACvByR,EAAqB,YAAMzR,EAAiB,WAC3CyR,EAAqB,YAAMzR,EAAU,IACxCC,EAAczR,KAAKwR,EAEvB,IAEKC,CACT,EA5HAxO,EAAAA,EAAAA,YAAU,WACR,IAAMigB,EAAqB,WAAH,IAAI5S,EAAiBC,UAAA/Q,OAAA,QAAAgR,IAAAD,UAAA,GAAAA,UAAA,GAAG7I,EAAuB,OAAK,WAC1Egb,EAAS,GAAIpS,GACbsJ,EAAmB,CAAC,GACpBrD,EAAe,GACjB,CAAC,EACK4M,EAAY,WAAH,IAAI7S,EAAiBC,UAAA/Q,OAAA,QAAAgR,IAAAD,UAAA,GAAAA,UAAA,GAAG7I,EAAuB,OAAK,WACjE,IAAMsb,EAAsB9Z,EAAAA,EAAKka,uBAAuB9S,GAClDmB,EAAgBsR,EAAiBC,GAEnC9O,GAAqBzC,EAAcjS,SAAWwjB,EAAoBxjB,QACpEyQ,GAAmB,GAGrByS,EACExZ,EAAAA,EACGma,mBAAmB/S,GACnB4B,QACC,SAACV,GAAK,OAAKA,EAAM8R,WACd9R,EAAM5H,YACN4H,EAAM+R,SACN/R,EAAMb,aACPa,EAAMgS,WAAavkB,OAAO0N,KAAK8W,MAAMC,UAAUC,OAC9CnS,EAAMoS,8BACL7N,IAAsB8N,EAAAA,EAAAA,IAAmBrS,KAAWsS,EAAAA,GAAkBC,eAAe,IAE7FzT,EAEJ,CAAC,EAEK0T,EAAuBd,EAAmB,GAChDha,EAAAA,EAAKvF,iBAAiB,mBAAoBqgB,GAC1C,IAMIC,EACAC,EAPEC,EAAYhB,EAAU,GAkB5B,OAjBAja,EAAAA,EAAKvF,iBAAiB,oBAAqBwgB,GAC3Cjb,EAAAA,EAAKvF,iBAAiB,mBAAoBwgB,GAC1Cjb,EAAAA,EAAKvF,iBAAiB,6BAA8BwgB,GACpDA,IAII5B,IACF0B,EAAuBf,EAAmB,GAC1Cha,EAAAA,EAAKvF,iBAAiB,mBAAoBsgB,OAAsBzT,EAAW,GAC3E0T,EAAYf,EAAU,GACtBja,EAAAA,EAAKvF,iBAAiB,oBAAqBugB,OAAW1T,EAAW,GACjEtH,EAAAA,EAAKvF,iBAAiB,mBAAoBugB,OAAW1T,EAAW,GAChEtH,EAAAA,EAAKvF,iBAAiB,6BAA8BugB,OAAW1T,EAAW,GAC1E0T,KAGK,WACD3B,IACFrZ,EAAAA,EAAKtF,oBAAoB,mBAAoBqgB,EAAsB,GACnE/a,EAAAA,EAAKtF,oBAAoB,oBAAqBsgB,EAAW,GACzDhb,EAAAA,EAAKtF,oBAAoB,mBAAoBsgB,EAAW,GACxDhb,EAAAA,EAAKtF,oBAAoB,6BAA8BsgB,EAAW,IAEpEhb,EAAAA,EAAKtF,oBAAoB,mBAAoBogB,GAC7C9a,EAAAA,EAAKtF,oBAAoB,oBAAqBugB,GAC9Cjb,EAAAA,EAAKtF,oBAAoB,mBAAoBugB,GAC7Cjb,EAAAA,EAAKtF,oBAAoB,6BAA8BugB,EACzD,CACF,GAAG,CAAC5B,EAAmBxM,KAEvB9S,EAAAA,EAAAA,YAAU,WACR,IA+BImhB,EA/BE5H,EAAuB,WAAH,IAAIlM,EAAiBC,UAAA/Q,OAAA,QAAAgR,IAAAD,UAAA,GAAAA,UAAA,GAAG7I,EAAuB,OAAK,SAACd,EAAa0K,GAC1F,IAAM0R,EAAsB9Z,EAAAA,EAAKka,uBAAuB9S,GAClDiR,EAAM,CAAC,EACbyB,EAAoBljB,SAAQ,SAAC0R,GAC3B+P,EAAI/P,EAAMjN,KAAM,CAClB,KACIyV,GAAqB3K,GAAUwL,KACjCjB,EAAmB2H,EAAKjR,GACxBwJ,GAAyB,IAE3B,IAAMrI,EAAgBsR,EAAiBC,GACjCqB,EAA4BrB,EAAoBxjB,OAAS,GAAKiS,EAAcjS,SAAWwjB,EAAoBxjB,QAAW0U,EAExHoH,GACY,aAAXhK,GACA+S,GACHpU,GAAmB,GACnB+S,EAAoBljB,SAAQ,SAACwkB,GAC3BpU,EAAmBoU,EAAc/f,IAAM+f,CACzC,IACAnU,EAAqBvL,GAAC,CAAC,EAAIsL,GAAsBI,IAC7B,eAAXgB,IACT1K,EAAY9G,SAAQ,SAACykB,UACZrU,EAAmBqU,EAAEhgB,GAC9B,IACA4L,EAAqBvL,GAAC,CAAC,EAAIsL,GAAsBI,GAErD,CAAC,EACKkU,EAAwBhI,EAAqB,GASnD,OARAgI,IACAtb,EAAAA,EAAKvF,iBAAiB,qBAAsB6gB,GAExCjC,KACF6B,EAAwB5H,EAAqB,MAE7CtT,EAAAA,EAAKvF,iBAAiB,qBAAsBygB,OAAuB5T,EAAW,IAEzE,WACLtH,EAAAA,EAAKtF,oBAAoB,qBAAsB4gB,GAC3CjC,GACFrZ,EAAAA,EAAKtF,oBAAoB,qBAAsBwgB,EAAuB,EAE1E,CACF,GAAG,CAACpK,EAAmB3K,EAAQwL,EAAkB3G,EAAmBhE,EAAoBoL,EAAgCiH,IAkBxH,IAAMkC,EAAY,CAChB5jB,MAAAA,EACA8Y,gBAAAA,EACAC,mBAAAA,EACAtD,YAAAA,EACAC,eAAAA,EACArC,kBAAAA,EACAjE,mBAAAA,EACAC,mBAAAA,EACAC,sBAAAA,EACA0J,sBAAAA,EACAC,yBAAAA,GAIF,OACEpY,EAAAA,cAACgX,GAAUgM,GAAA,GAAKrC,EAAWoC,GAE/B,C,mBErMAhkB,EADkC,EAAQ,MAChCE,EAA4B,IAE9BX,KAAK,CAACnB,EAAOC,GAAI,8+GAA++G,KAExgH2B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,QAEvB7B,EAAO4B,QAAUA,C,mBCRjBA,EADkC,EAAQ,MAChCE,EAA4B,IAE9BX,KAAK,CAACnB,EAAOC,GAAI,+OAAgP,KAEzQD,EAAO4B,QAAUA,C", "sources": ["webpack://webviewer-ui/./src/components/NotesPanelHeader/NotesPanelHeader.scss?2433", "webpack://webviewer-ui/./src/components/NotesPanel/MultiStylePopup.scss?bef0", "webpack://webviewer-ui/./src/components/Note/ReplyArea/ReplyAreaMultiSelect.scss?06f4", "webpack://webviewer-ui/./src/components/NotesPanel/NotesPanel.scss?d97d", "webpack://webviewer-ui/./src/components/NotesPanel/NotesPanel.scss", "webpack://webviewer-ui/./src/components/NotesPanel/MultiSelectControls.scss?ae2d", "webpack://webviewer-ui/./src/components/NotesPanelHeader/NotesPanelHeader.scss", "webpack://webviewer-ui/./src/components/NotesPanel/MultiSelectControls.scss", "webpack://webviewer-ui/./src/components/NotesPanel/VirtualizedList/VirtualizedList.js", "webpack://webviewer-ui/./src/components/NotesPanel/VirtualizedList/index.js", "webpack://webviewer-ui/./src/components/NotesPanel/NormalList/NormalList.js", "webpack://webviewer-ui/./src/components/NotesPanel/NormalList/index.js", "webpack://webviewer-ui/./src/components/Note/ReplyArea/ReplyAreaMultiSelect.js", "webpack://webviewer-ui/./src/hooks/usePortal.js", "webpack://webviewer-ui/./src/components/PopupPortal/index.js", "webpack://webviewer-ui/./src/components/PopupPortal/PopupPortal.js", "webpack://webviewer-ui/./src/components/NotesPanel/MultiStylePopup.js", "webpack://webviewer-ui/./src/components/NotesPanel/MultiSelectControls.js", "webpack://webviewer-ui/./src/components/NotesPanelHeader/NotesPanelHeader.js", "webpack://webviewer-ui/./src/components/NotesPanelHeader/index.js", "webpack://webviewer-ui/./src/components/NotesPanel/NotesPanel.js", "webpack://webviewer-ui/./src/components/NotesPanel/NotesPanelContainer.js", "webpack://webviewer-ui/./src/components/NotesPanel/index.js", "webpack://webviewer-ui/./src/components/Note/ReplyArea/ReplyAreaMultiSelect.scss", "webpack://webviewer-ui/./src/components/NotesPanel/MultiStylePopup.scss"], "sourcesContent": ["var api = require(\"!../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../node_modules/css-loader/dist/cjs.js!../../../node_modules/postcss-loader/src/index.js??ruleSet[1].rules[1].use[2]!../../../node_modules/sass-loader/dist/cjs.js!./NotesPanelHeader.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "var api = require(\"!../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../node_modules/css-loader/dist/cjs.js!../../../node_modules/postcss-loader/src/index.js??ruleSet[1].rules[1].use[2]!../../../node_modules/sass-loader/dist/cjs.js!./MultiStylePopup.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "var api = require(\"!../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../node_modules/css-loader/dist/cjs.js!../../../../node_modules/postcss-loader/src/index.js??ruleSet[1].rules[1].use[2]!../../../../node_modules/sass-loader/dist/cjs.js!./ReplyAreaMultiSelect.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "var api = require(\"!../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../node_modules/css-loader/dist/cjs.js!../../../node_modules/postcss-loader/src/index.js??ruleSet[1].rules[1].use[2]!../../../node_modules/sass-loader/dist/cjs.js!./NotesPanel.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../node_modules/css-loader/dist/runtime/api.js\");\nexports = ___CSS_LOADER_API_IMPORT___(false);\n// Module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.notes-panel-container{z-index:65;display:flex;flex-direction:row;position:relative;overflow:hidden}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .notes-panel-container{z-index:95}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .notes-panel-container{z-index:95}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .notes-panel-container{border-left:1px solid var(--side-panel-border)}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .notes-panel-container{border-left:1px solid var(--side-panel-border)}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .notes-panel-container{top:0;right:0;height:100%;width:100%}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .notes-panel-container{top:0;right:0;height:100%;width:100%}}.notes-panel-container .NotesPanel{width:100%;padding-left:16px;padding-bottom:0;display:flex;flex-direction:column;position:relative;padding-top:16px}.notes-panel-container .NotesPanel .close-container{display:flex;align-items:center;justify-content:flex-end}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .notes-panel-container .NotesPanel .close-container{height:28px;padding-right:16px;margin-bottom:8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .notes-panel-container .NotesPanel .close-container{height:28px;padding-right:16px;margin-bottom:8px}}.notes-panel-container .NotesPanel .close-container .close-icon-container{display:flex;align-items:center;cursor:pointer}.notes-panel-container .NotesPanel .close-container .close-icon-container .close-icon{width:24px;height:24px}.notes-panel-container .NotesPanel .multi-select-place-holder{height:72px}.notes-panel-container .NotesPanel .preview-all-changes{height:57px;display:flex;flex-direction:column;justify-content:center;position:relative;margin-right:16px}.notes-panel-container .NotesPanel .preview-all-changes .divider{height:1px;width:100%;background:var(--divider);position:absolute;top:0}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.notes-panel-container .NotesPanel .reply-area-container .reply-button{width:28px}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .notes-panel-container .NotesPanel{width:100%;min-width:100%;padding-top:0}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .notes-panel-container .NotesPanel .normal-notes-container{padding-bottom:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .notes-panel-container .NotesPanel{width:100%;min-width:100%;padding-top:0}.App.is-web-component:not(.is-in-desktop-only-mode) .notes-panel-container .NotesPanel .normal-notes-container{padding-bottom:16px}}.notes-panel-container .NotesPanel .no-annotations{display:flex;flex-direction:column;align-items:center}.notes-panel-container .NotesPanel .no-annotations .msg{text-align:center}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .notes-panel-container .NotesPanel .no-annotations .msg{line-height:15px;width:146px}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .notes-panel-container .NotesPanel .no-annotations .msg{line-height:15px;width:146px}}.notes-panel-container .NotesPanel .no-annotations .empty-icon,.notes-panel-container .NotesPanel .no-annotations .empty-icon svg{width:65px;height:83px}.notes-panel-container .NotesPanel .no-annotations .empty-icon *{fill:var(--gray-6);color:var(--gray-6)}.notes-panel-container .NotesPanel .normal-notes-container,.notes-panel-container .NotesPanel .virtualized-notes-container{margin-top:10px;flex:1;padding-right:18px}.notes-panel-container .NotesPanel .virtualized-notes-container{overflow:hidden}.notes-panel-container .NotesPanel .normal-notes-container{overflow:auto;overflow:overlay}.notes-panel-container .NotesPanel .note-wrapper:first-child .ListSeparator{margin-top:0;word-break:break-word}.notes-panel-container .NotesPanel .no-results{display:flex;flex-direction:column;align-items:center;padding-right:18px}.notes-panel-container .NotesPanel .no-results .msg{text-align:center}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .notes-panel-container .NotesPanel .no-results .msg{line-height:15px;width:92px}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .notes-panel-container .NotesPanel .no-results .msg{line-height:15px;width:92px}}.notes-panel-container .NotesPanel .no-results .empty-icon,.notes-panel-container .NotesPanel .no-results .empty-icon svg{width:65px;height:83px}.notes-panel-container .NotesPanel .no-results .empty-icon *{fill:var(--border);color:var(--border)}\", \"\"]);\n// Exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};\nmodule.exports = exports;\n", "var api = require(\"!../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../node_modules/css-loader/dist/cjs.js!../../../node_modules/postcss-loader/src/index.js??ruleSet[1].rules[1].use[2]!../../../node_modules/sass-loader/dist/cjs.js!./MultiSelectControls.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../node_modules/css-loader/dist/runtime/api.js\");\nexports = ___CSS_LOADER_API_IMPORT___(false);\n// Module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.modular-ui-header .sort-row .sort-container .Dropdown__wrapper .Dropdown__items .Dropdown__item:hover{cursor:pointer;background:var(--primary-button-hover);color:var(--gray-0)}.modular-ui-header .sort-row .sort-container .Dropdown__wrapper .Dropdown__items .Dropdown__item.active{cursor:pointer;background:var(--blue-5);color:var(--gray-0)}.comments-counter{height:19px;margin-top:24px;margin-bottom:12px;font-size:16px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .comments-counter{margin-top:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .comments-counter{margin-top:16px}}.comments-counter span{font-weight:700}[data-element=notesPanelHeader]{padding-right:18px}[data-element=notesPanelHeader] .buttons-container{display:flex;grid-column-gap:8px;-moz-column-gap:8px;column-gap:8px;justify-content:space-between}[data-element=notesPanelHeader] .buttons-container .Button{height:28px;width:28px;cursor:pointer}[data-element=notesPanelHeader] .buttons-container .Button:hover:enabled{background:var(--view-header-button-hover)}[data-element=notesPanelHeader] .buttons-container .Button.active{background:var(--view-header-button-active)}[data-element=notesPanelHeader] .buttons-container .Button.active .Icon{color:var(--view-header-icon-active-fill)}[data-element=notesPanelHeader] .buttons-container .Button:disabled{cursor:default}[data-element=notesPanelHeader] .sort-row{display:flex;justify-content:space-between;margin-bottom:16px}[data-element=notesPanelHeader] .sort-row .sort-container{display:flex;flex-direction:row;align-items:center;justify-content:flex-end;align-self:flex-end}[data-element=notesPanelHeader] .sort-row .sort-container .label{margin-right:8px}[data-element=notesPanelHeader] .sort-row .sort-container .picked-option{text-align:left}[data-element=notesPanelHeader] .input-container{display:flex;position:relative;flex:1}[data-element=notesPanelHeader] .input-container input{width:100%;border:1px solid var(--border);border-radius:4px;color:var(--text-color);padding:4px 8px 6px}[data-element=notesPanelHeader] .input-container.modular-ui-input{box-sizing:border-box;border:1px solid var(--border);border-radius:4px;height:28px;align-items:center;color:var(--text-color);padding:6px 2px 6px 6px}[data-element=notesPanelHeader] .input-container.modular-ui-input[focus-within]{outline:none;border:1px solid var(--focus-border)}[data-element=notesPanelHeader] .input-container.modular-ui-input:focus-within{outline:none;border:1px solid var(--focus-border)}[data-element=notesPanelHeader] .input-container.modular-ui-input input{padding-left:8px;padding-right:26px;height:20px;border:none;background:transparent}[data-element=notesPanelHeader] .input-button{cursor:pointer;background:var(--primary-button);border-radius:4px;height:100%;width:40px;display:flex;align-items:center;justify-content:center;position:absolute;bottom:0;right:0}[data-element=notesPanelHeader] .input-button .Icon{width:20px;height:20px}[data-element=notesPanelHeader] .input-button svg{color:var(--primary-button-text)}[data-element=notesPanelHeader] .input-button:hover{background:var(--primary-button-hover)}[data-element=notesPanelHeader] .divider{height:1px;width:100%;background:var(--divider);margin:16px 0}.modular-ui-header .sort-row .sort-container .Dropdown__wrapper .Dropdown__items .Dropdown__item{font-size:13px}.modular-ui-header .sort-row .sort-container .Dropdown__wrapper .Dropdown__items .Dropdown__item .Icon .arrow{margin-top:0}.modular-ui-header .input-container.modular-ui-input .Icon{width:16px;height:16px}\", \"\"]);\n// Exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};\nmodule.exports = exports;\n", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../node_modules/css-loader/dist/runtime/api.js\");\nexports = ___CSS_LOADER_API_IMPORT___(false);\n// Module\nexports.push([module.id, \".notes-panel-container .buttons-container{display:flex;grid-column-gap:8px;-moz-column-gap:8px;column-gap:8px;justify-content:space-between}.notes-panel-container .buttons-container .Button{height:28px;width:28px}.notes-panel-container .buttons-container .Button:hover:enabled{background:var(--view-header-button-hover)}.notes-panel-container .buttons-container .Button.active{background:var(--view-header-button-active)}.notes-panel-container .buttons-container .Button.active .Icon{color:var(--view-header-icon-active-fill)}.notes-panel-container .multi-select-footer{background:var(--panel-background);display:flex;border-top:1px solid var(--gray-5);margin-top:auto;margin-bottom:16px;height:64px;padding:24px 16px;align-items:center;justify-content:center;position:absolute;bottom:0;width:100%;z-index:100}.notes-panel-container .multi-select-footer .close-container{margin-top:auto;right:26px;position:absolute}\", \"\"]);\n// Exports\nmodule.exports = exports;\n", "import React, { useState, useEffect, useRef, useImperativeHandle } from 'react';\nimport PropTypes from 'prop-types';\nimport Measure from 'react-measure';\nimport { CellMeasurer, CellMeasurerCache, List } from 'react-virtualized';\n\nconst propTypes = {\n  notes: PropTypes.array.isRequired,\n  children: PropTypes.func.isRequired,\n  onScroll: PropTypes.func.isRequired,\n  initialScrollTop: PropTypes.number.isRequired,\n  selectedIndex: PropTypes.number,\n  sortStrategy: PropTypes.string,\n};\n\nconst cache = new CellMeasurerCache({ defaultHeight: 50, fixedWidth: true });\n\nconst VirtualizedList = React.forwardRef(\n  ({ notes, children, onScroll, initialScrollTop, selectedIndex, sortStrategy }, forwardedRef) => {\n    const listRef = useRef();\n    const [offset, setOffset] = useState(0);\n    const [dimension, setDimension] = useState({ width: 0, height: 0 });\n    let prevWindowHeight = window.innerHeight;\n\n    useImperativeHandle(forwardedRef, () => ({\n      scrollToPosition: (scrollTop) => {\n        listRef.current.scrollToPosition(scrollTop);\n      },\n      scrollToRow: (index) => {\n        listRef.current.scrollToRow(index);\n      },\n    }));\n\n    useEffect(() => {\n      listRef.current.scrollToPosition(initialScrollTop);\n    }, [initialScrollTop]);\n\n    useEffect(() => {\n      cache.clearAll();\n      listRef?.current?.measureAllRows();\n\n      if (selectedIndex !== -1) {\n        listRef.current?.scrollToRow(selectedIndex);\n      }\n    }, [selectedIndex]);\n\n    useEffect(() => {\n      cache.clearAll();\n      listRef?.current?.measureAllRows();\n      listRef?.current?.forceUpdateGrid();\n    }, [notes.length, sortStrategy]);\n\n    useEffect(() => {\n      const windowResizeHandler = () => {\n        const diff = window.innerHeight - prevWindowHeight;\n        if (diff) {\n          // List height never resizes down after exiting fullscreen\n          if (window.innerHeight < prevWindowHeight) {\n            setOffset(diff);\n          }\n          prevWindowHeight = window.innerHeight;\n        }\n      };\n      window.addEventListener('resize', windowResizeHandler);\n\n      return () => {\n        window.removeEventListener('resize', windowResizeHandler);\n      };\n    });\n\n    const _resize = (index) => {\n      cache.clear(index);\n      listRef.current?.recomputeRowHeights(index);\n    };\n\n    const handleScroll = ({ scrollTop }) => {\n      onScroll(scrollTop);\n    };\n\n    /* eslint-disable react/prop-types */\n    const rowRenderer = ({ index, key, parent, style }) => {\n      const currNote = notes[index];\n\n      // Padding added to the right since virtualized list lets it get cut off on the right\n      return (\n        <CellMeasurer\n          key={`${key}${currNote.Id}`}\n          cache={cache}\n          columnIndex={0}\n          parent={parent}\n          rowIndex={index}\n        >\n          {({ measure }) => (\n            <div style={{ ...style, paddingRight: '12px' }}>\n              {children(notes, index, () => {\n                _resize(index);\n                measure();\n              })}\n            </div>)}\n        </CellMeasurer>\n      );\n    };\n\n    return (\n      <Measure bounds offset onResize={({ bounds }) => {\n        setDimension({\n          ...bounds,\n          // Override height and compensate for extra size\n          height: bounds.height + offset * 2,\n        });\n        setOffset(0);\n      }}\n      >\n        {({ measureRef }) => (\n          <div ref={measureRef} className=\"virtualized-notes-container\">\n            <List\n              deferredMeasurementCache={cache}\n              style={{ outline: 'none' }}\n              height={dimension.height - offset}\n              width={dimension.width}\n              overscanRowCount={10}\n              ref={listRef}\n              rowCount={notes.length}\n              rowHeight={cache.rowHeight}\n              rowRenderer={rowRenderer}\n              onScroll={handleScroll}\n              aria-label='presentation'\n              role='presentation'\n            />\n          </div>\n        )}\n      </Measure>\n    );\n  },\n);\n\nVirtualizedList.displayName = 'VirtualizedList';\nVirtualizedList.propTypes = propTypes;\n\nexport default VirtualizedList;\n", "import VirtualizedList from './VirtualizedList';\n\nexport default VirtualizedList;", "import React, { useEffect, useRef, useImperativeHandle } from 'react';\nimport PropTypes from 'prop-types';\n\nconst propTypes = {\n  notes: PropTypes.array.isRequired,\n  children: PropTypes.func.isRequired,\n  onScroll: PropTypes.func.isRequired,\n  initialScrollTop: PropTypes.number.isRequired,\n};\n\nconst NormalList = React.forwardRef(\n  ({ notes, children, onScroll, initialScrollTop }, forwardedRef) => {\n    const listRef = useRef();\n\n    useImperativeHandle(forwardedRef, () => ({\n      scrollToPosition: (scrollTop) => {\n        listRef.current.scrollTop = scrollTop;\n      },\n      scrollToRow: (index) => {\n        const parent = listRef.current;\n        const child = parent.children[index];\n        if (!child) {\n          return;\n        }\n\n        const parentRect = parent.getBoundingClientRect();\n        const childRect = child.getBoundingClientRect();\n\n        const isViewable =\n          childRect.top >= parentRect.top &&\n          childRect.top <= parentRect.top + parent.clientHeight;\n        if (!isViewable) {\n          parent.scrollTop = childRect.top + parent.scrollTop - parentRect.top;\n        }\n      },\n    }));\n\n    useEffect(() => {\n      listRef.current.scrollTop = initialScrollTop;\n    }, [initialScrollTop]);\n\n    const handleScroll = (e) => {\n      onScroll(e.target.scrollTop);\n    };\n\n    return (\n      <div\n        ref={listRef}\n        className=\"normal-notes-container\"\n        onScroll={handleScroll}\n        role=\"list\"\n      >\n        {notes.map((currNote, index) => (\n          <React.Fragment key={`${index}_${currNote.Id}`}>\n            {children(notes, index)}\n          </React.Fragment>\n        ))}\n      </div>\n    );\n  },\n);\n\nNormalList.displayName = 'NormalList';\nNormalList.propTypes = propTypes;\n\nexport default NormalList;\n", "import NormalList from './NormalList';\n\nexport default NormalList;", "import React, { useState, useEffect, useRef } from 'react';\nimport { useSelector, shallowEqual } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\nimport NoteTextarea from 'components/NoteTextarea';\nimport classNames from 'classnames';\nimport core from 'core';\nimport mentionsManager from 'helpers/MentionsManager';\nimport setAnnotationRichTextStyle from 'helpers/setAnnotationRichTextStyle';\nimport selectors from 'selectors';\nimport Button from 'src/components/Button';\n\nimport './ReplyAreaMultiSelect.scss';\n\n// a component that contains the reply textarea, the reply button and the cancel button\nconst ReplyArea = ({ annotations, onSubmit, onClose }) => {\n  const [\n    isMentionEnabled,\n    activeDocumentViewerKey,\n    customizableUI,\n  ] = useSelector(\n    (state) => [\n      selectors.getIsMentionEnabled(state),\n      selectors.getActiveDocumentViewerKey(state),\n      selectors.getFeatureFlags(state)?.customizableUI,\n    ],\n    shallowEqual\n  );\n\n  const [pendingReply, setPendingReply] = useState('');\n  const [t] = useTranslation();\n  const textareaRef = useRef();\n\n  useEffect(() => {\n    textareaRef.current.focus();\n  }, []);\n\n  const postReply = (e) => {\n    // prevent the textarea from blurring out\n    e.preventDefault();\n\n    const editor = textareaRef.current.getEditor();\n    const replyText = mentionsManager.getFormattedTextFromDeltas(editor.getContents());\n\n    if (!replyText.trim()) {\n      return;\n    }\n    annotations.forEach((annotation) => {\n      if (isMentionEnabled) {\n        const replyAnnotation = mentionsManager.createMentionReply(annotation, replyText);\n        core.addAnnotations([replyAnnotation], activeDocumentViewerKey);\n        setAnnotationRichTextStyle(editor, replyAnnotation);\n      } else {\n        // TODO: This is bugged and does not work. createAnnotationReply\n        // does not return an annotation\n        const replyAnnotation = core.createAnnotationReply(annotation, replyText, activeDocumentViewerKey);\n        setAnnotationRichTextStyle(editor, replyAnnotation);\n      }\n    });\n\n    onSubmit();\n  };\n\n  const replyAreaClass = classNames({\n    'reply-area': true,\n  });\n\n  const handleNoteTextareaChange = (value) => {\n    setPendingReply(value);\n  };\n\n  return (\n    <div className={classNames({\n      'reply-area-multi-container': true,\n      'modular-ui': customizableUI,\n    })}>\n      <div\n        className=\"reply-area-multi-header\"\n      >\n        <div className=\"title\">\n          {t('action.multiReplyAnnotations', { count: annotations.length })}\n        </div>\n        <Button\n          className=\"close-icon\"\n          onClick={onClose}\n          img='ic_close_black_24px'\n        />\n      </div>\n      <form onSubmit={postReply} >\n        <div\n          className={replyAreaClass}\n          // stop bubbling up otherwise the note will be closed\n          // due to annotation deselection\n          onMouseDown={(e) => e.stopPropagation()}\n        >\n          <NoteTextarea\n            ref={(el) => {\n              textareaRef.current = el;\n            }}\n            value={pendingReply}\n            onChange={(value) => handleNoteTextareaChange(value)}\n            isReply\n          />\n        </div>\n        <div className='reply-button-container'>\n          <Button\n            img=\"icon-post-reply\"\n            className={'reply-button'}\n            isSubmitType={true}\n          />\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default ReplyArea;\n", "import { useRef, useEffect } from 'react';\nimport getRootNode from 'helpers/getRootNode';\n\n/**\n * Creates DOM element to be used as React root.\n * @ignore\n * @returns {HTMLElement}\n */\nfunction createRootElement(id) {\n  const rootContainer = document.createElement('div');\n  rootContainer.setAttribute('id', id);\n  return rootContainer;\n}\n\n/**\n * Appends element as last child of body.\n * @ignore\n * @param {HTMLElement} rootElem\n */\nfunction addRootElement(rootElem) {\n  const node = (window.isApryseWebViewerWebComponent) ? getRootNode() : document.body;\n  node.insertBefore(\n    rootElem,\n    node.lastElementChild.nextElementSibling,\n  );\n}\n\n/**\n * Hook to create a React Portal.\n * Automatically handles creating and tearing-down the root elements (no SRR\n * makes this trivial), so there is no need to ensure the parent target already\n * exists.\n * @ignore\n * @example\n * const target = usePortal(id, [id]);\n * return createPortal(children, target);\n * @param {String} id The id of the target container, e.g 'modal' or 'spotlight'\n * @returns {HTMLElement} The DOM node to use as the Portal target.\n */\nfunction usePortal(id) {\n  const rootElemRef = useRef(null);\n  useEffect(function setupElement() {\n    // Look for existing target dom element to append to\n\n    const existingParent = document.querySelector(`#${id}`);\n    // Parent is either a new root or the existing dom element\n    const parentElem = existingParent || createRootElement(id);\n\n    // If there is no existing DOM element, add a new one.\n    if (!existingParent) {\n      addRootElement(parentElem);\n    }\n\n    // Add the detached element to the parent\n    parentElem.appendChild(rootElemRef.current);\n\n    return function removeElement() {\n      rootElemRef.current.remove();\n      if (!parentElem.childElementCount) {\n        parentElem.remove();\n      }\n    };\n  }, [id]);\n\n  /**\n   * It's important we evaluate this lazily:\n   * - We need first render to contain the DOM element, so it shouldn't happen\n   *   in useEffect. We would normally put this in the constructor().\n   * - We can't do 'const rootElemRef = useRef(document.createElement('div))',\n   *   since this will run every single render (that's a lot).\n   * - We want the ref to consistently point to the same DOM element and only\n   *   ever run once.\n   * @link https://reactjs.org/docs/hooks-faq.html#how-to-create-expensive-objects-lazily\n   */\n  function getRootElem() {\n    if (!rootElemRef.current) {\n      rootElemRef.current = document.createElement('div');\n    }\n    return rootElemRef.current;\n  }\n\n  return getRootElem();\n}\n\nexport default usePortal;", "import PopupPortal from './PopupPortal';\n\nexport default PopupPortal;", "import { createPortal } from 'react-dom';\nimport usePortal from 'hooks/usePortal';\n\nconst Portal = ({ id, position, children }) => {\n  const target = usePortal(id);\n  target.style.position = 'absolute';\n  target.style.top = position.top === 'auto' ? position.top : `${position.top}px`;\n  target.style.left = position.left === 'auto' ? position.left : `${position.left}px`;\n  target.style.right = position.right === 'auto' ? position.right : `${position.right}px`;\n  target.style.pointerEvents = 'none';\n  target.style.zIndex = 999;\n\n  return createPortal(\n    children,\n    target,\n  );\n};\n\nexport default Portal;", "import React, { useState, useRef, useEffect } from 'react';\nimport { useDispatch } from 'react-redux';\nimport getOverlayPositionBasedOn from 'helpers/getOverlayPositionBasedOn';\nimport PopupPortal from 'components/PopupPortal';\nimport AnnotationStylePopup from 'components/AnnotationStylePopup';\n\nimport useOnClickOutside from 'hooks/useOnClickOutside';\nimport getAnnotationStyles from 'helpers/getAnnotationStyles';\nimport { getOpenedWarningModal, getOpenedColorPicker, getDatePicker } from 'helpers/getElements';\nimport actions from 'actions';\nimport {\n  AnnotationStylePopupTabs,\n  updateAnnotationStylePopupTabs,\n  copyMapWithDataProperties,\n  getDataWithKey,\n} from 'constants/map';\n\n\nimport './MultiStylePopup.scss';\n\nconst MultiStylePopup = ({\n  annotations,\n  triggerElementName,\n  onClose = () => { },\n}) => {\n  const dispatch = useDispatch();\n  const popupRef = useRef();\n  const [styleTabs, setStyleTabs] = useState([]);\n  const [position, setPosition] = useState({ left: 'auto', right: 'auto', top: 'auto' });\n  const colorMapKey = 'MultiStyle';\n\n  useOnClickOutside(popupRef, (e) => {\n    const triggerElement = document.querySelector(`[data-element=${triggerElementName}]`);\n    const clickedTrigger = triggerElement.contains(e.target);\n    const warningModal = getOpenedWarningModal();\n    const colorPicker = getOpenedColorPicker();\n    const datePicker = getDatePicker();\n\n    if (!clickedTrigger && !warningModal && !colorPicker && !datePicker) {\n      // we only want to close the popup if we clicked outside and not on the trigger\n      onClose();\n    }\n  });\n\n  let style = {};\n  annotations.forEach((annotation) => {\n    style = { ...style, ...getAnnotationStyles(annotation) };\n  });\n  const freeTextAnnotation = annotations.find((annotation) => {\n    return (\n      annotation instanceof window.Core.Annotations.FreeTextAnnotation &&\n      (annotation.getIntent() === window.Core.Annotations.FreeTextAnnotation.Intent.FreeText ||\n      annotation.getIntent() === window.Core.Annotations.FreeTextAnnotation.Intent.FreeTextCallout)\n    );\n  });\n  let properties = {};\n  if (freeTextAnnotation) {\n    const richTextStyles = freeTextAnnotation.getRichTextStyle();\n    properties = {\n      Font: freeTextAnnotation.Font,\n      FontSize: freeTextAnnotation.FontSize,\n      TextAlign: freeTextAnnotation.TextAlign,\n      TextVerticalAlign: freeTextAnnotation.TextVerticalAlign,\n      bold: richTextStyles?.[0]?.['font-weight'] === 'bold' ?? false,\n      italic: richTextStyles?.[0]?.['font-style'] === 'italic' ?? false,\n      underline: richTextStyles?.[0]?.['text-decoration']?.includes('underline') || richTextStyles?.[0]?.['text-decoration']?.includes('word'),\n      strikeout: richTextStyles?.[0]?.['text-decoration']?.includes('line-through') ?? false,\n    };\n  }\n\n  // Update available palettesfor MultiStyle annotation type in map\n  useEffect(() => {\n    const availablePalettes = [];\n    style['TextColor'] && availablePalettes.push(AnnotationStylePopupTabs.TEXT_COLOR);\n    style['StrokeColor'] && availablePalettes.push(AnnotationStylePopupTabs.STROKE_COLOR);\n    style['FillColor'] && availablePalettes.push(AnnotationStylePopupTabs.FILL_COLOR);\n\n    const didUpdate =\n      updateAnnotationStylePopupTabs(colorMapKey, availablePalettes, availablePalettes[0]);\n    if (didUpdate) {\n      const newColorMap = copyMapWithDataProperties('currentStyleTab', 'iconColor');\n      dispatch(actions.setColorMap(newColorMap));\n      const { styleTabs: _styleTabs } = getDataWithKey(colorMapKey);\n      setStyleTabs(_styleTabs);\n    }\n  }, [annotations]);\n\n  const updatePosition = () => {\n    const position = getOverlayPositionBasedOn(triggerElementName, popupRef);\n    setPosition(position);\n  };\n\n  // Have to wait until palettes are available before showing popup\n  if (styleTabs.length === 0) {\n    return null;\n  }\n\n  return (\n    <PopupPortal\n      id=\"multi-style-popup-portal\"\n      position={position}\n    >\n      <div\n        className='multi-style-container'\n        ref={popupRef}\n      >\n        <AnnotationStylePopup\n          annotations={annotations}\n          style={style}\n          isOpen\n          onResize={updatePosition}\n          isFreeText={!!freeTextAnnotation}\n          colorMapKey={colorMapKey}\n          properties={properties}\n          isRedaction={false}\n          isMeasure={false}\n          showLineStyleOptions={false}\n          hideSnapModeCheckbox={false}\n        />\n      </div>\n    </PopupPortal >\n  );\n};\n\nexport default MultiStylePopup;", "import React, { useCallback, useEffect, useState } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\n\nimport Button from 'components/Button';\nimport ReplyAreaMultiSelect from 'components/Note/ReplyArea/ReplyAreaMultiSelect';\nimport MultiStylePopup from 'components/NotesPanel/MultiStylePopup';\nimport NoteContext from 'components/Note/Context';\nimport NoteStateFlyout from 'components/ModularComponents/NoteStateFlyout';\nimport ToggleElementButton from 'components/ModularComponents/ToggleElementButton';\n\nimport { createStateAnnotation } from 'helpers/NoteStateUtils';\nimport DataElements from 'constants/dataElement';\nimport PropTypes from 'prop-types';\nimport actions from 'actions';\nimport core from 'core';\nimport selectors from 'selectors';\n\nimport './MultiSelectControls.scss';\n\nconst propTypes = {\n  showMultiReply: PropTypes.bool.isRequired,\n  setShowMultiReply: PropTypes.func.isRequired,\n  setShowMultiState: PropTypes.func.isRequired,\n  showMultiStyle: PropTypes.bool.isRequired,\n  setShowMultiStyle: PropTypes.func.isRequired,\n  setMultiSelectMode: PropTypes.func.isRequired,\n  isMultiSelectedMap: PropTypes.object.isRequired,\n  setIsMultiSelectedMap: PropTypes.func.isRequired,\n  multiSelectedAnnotations: PropTypes.array.isRequired,\n};\n\nconst getParentAnnotations = (annotations, documentViewerKey = 1) => {\n  const annotSet = new Set();\n  annotations.forEach((annotation) => {\n    if (annotation.isGrouped()) {\n      const parentAnnotation = core.getAnnotationById(annotation['InReplyTo'], documentViewerKey);\n      if (parentAnnotation) {\n        annotSet.add(parentAnnotation);\n      }\n    } else {\n      annotSet.add(annotation);\n    }\n  });\n  return Array.from(annotSet);\n};\n\nconst MultiSelectControls = ({\n  showMultiReply,\n  setShowMultiReply,\n  setShowMultiState,\n  showMultiStyle,\n  setShowMultiStyle,\n  setMultiSelectMode,\n  isMultiSelectedMap,\n  setIsMultiSelectedMap,\n  multiSelectedAnnotations,\n}) => {\n  const [\n    modifiableMultiSelectAnnotations,\n    setModifiableMultiSelectAnnotations,\n  ] = useState([]);\n  const dispatch = useDispatch();\n  const [t] = useTranslation();\n\n  const activeDocumentViewerKey = useSelector(selectors.getActiveDocumentViewerKey);\n  const isDocumentReadOnly = useSelector(selectors.isDocumentReadOnly);\n\n  useEffect(() => {\n    const onAnnotationChanged = (annotations, action) => {\n      if (action === 'delete') {\n        const _isMultiSelectedMap = { ...isMultiSelectedMap };\n        annotations.forEach((annot) => {\n          delete _isMultiSelectedMap[annot.Id];\n        });\n        setIsMultiSelectedMap(_isMultiSelectedMap);\n      } else if (action === 'modify') {\n        const _isMultiSelectedMap = { ...isMultiSelectedMap };\n        // update isMultiSelectedMap by looking at modified annots and\n        // their grouped annots\n        annotations.forEach((annot) => {\n          const groupedAnnots = core.getGroupAnnotations(annot, activeDocumentViewerKey);\n          const someAnnotInGroupIsSelected =\n            groupedAnnots.some((groupedAnnot) => isMultiSelectedMap[groupedAnnot.Id]);\n          if (someAnnotInGroupIsSelected) {\n            // select all annots in this group\n            groupedAnnots.forEach((groupAnnot) => {\n              _isMultiSelectedMap[groupAnnot.Id] = groupAnnot;\n            });\n          }\n        });\n\n        const originalKeys = Object.keys(isMultiSelectedMap);\n        const updatedKeys = Object.keys(isMultiSelectedMap);\n        const difference = originalKeys.filter((key) => !updatedKeys.includes(key));\n        if (difference.length > 0) {\n          // Only update if we have a difference to not cause re-renders and\n          // actions to not be dispatched if unneeded.\n          setIsMultiSelectedMap(_isMultiSelectedMap);\n        }\n      }\n    };\n\n    core.addEventListener('annotationChanged', onAnnotationChanged, undefined, activeDocumentViewerKey);\n    return () => {\n      core.removeEventListener('annotationChanged', onAnnotationChanged, activeDocumentViewerKey);\n    };\n  }, [isMultiSelectedMap, activeDocumentViewerKey]);\n\n  useEffect(() => {\n    return () => {\n      setShowMultiReply(false);\n      setIsMultiSelectedMap({});\n    };\n  }, []);\n\n  useEffect(() => {\n    const _modifiableMultiSelectAnnotations = multiSelectedAnnotations.filter((multiSelectedAnnot) => {\n      return core.canModify(multiSelectedAnnot, activeDocumentViewerKey);\n    });\n    setModifiableMultiSelectAnnotations(_modifiableMultiSelectAnnotations);\n  }, [multiSelectedAnnotations]);\n\n  const numberOfGroups = core.getNumberOfGroups(modifiableMultiSelectAnnotations, activeDocumentViewerKey);\n  const canGroup = numberOfGroups > 1;\n  const canUngroup = !canGroup && (modifiableMultiSelectAnnotations.length > 2 ||\n    (modifiableMultiSelectAnnotations.length > 0 && core.getGroupAnnotations(modifiableMultiSelectAnnotations[0], activeDocumentViewerKey).length > 1));\n\n  const handleStateChange = useCallback((newValue) => {\n    getParentAnnotations(multiSelectedAnnotations, activeDocumentViewerKey).forEach((annot) => {\n      const stateAnnotation = createStateAnnotation(annot, newValue, activeDocumentViewerKey);\n      annot.addReply(stateAnnotation);\n      const annotationManager = core.getAnnotationManager(activeDocumentViewerKey);\n      annotationManager.addAnnotation(stateAnnotation);\n      annotationManager.trigger('addReply', [stateAnnotation, annot, annotationManager.getRootAnnotation(annot)]);\n    });\n    setShowMultiState(false);\n  }, [multiSelectedAnnotations, activeDocumentViewerKey]);\n\n  if (showMultiReply) {\n    return (\n      <NoteContext.Provider value={{\n        resize: () => {},\n      }}>\n        <ReplyAreaMultiSelect\n          annotations={getParentAnnotations(multiSelectedAnnotations, activeDocumentViewerKey)}\n          onSubmit={() => setShowMultiReply(false)}\n          onClose={() => setShowMultiReply(false)}\n        />\n      </NoteContext.Provider>\n    );\n  }\n\n  return (\n    <div className=\"multi-select-footer\">\n      <div className=\"buttons-container\">\n        <Button\n          dataElement={DataElements.NOTE_MULTI_REPLY_BUTTON}\n          disabled={isDocumentReadOnly || multiSelectedAnnotations.length === 0}\n          img=\"icon-header-chat-line\"\n          onClick={() => {\n            setShowMultiReply(true);\n          }}\n          title=\"action.comment\"\n        />\n        <ToggleElementButton\n          dataElement={DataElements.NOTE_MULTI_STATE_BUTTON}\n          title={t('option.notesOrder.status')}\n          img={'icon-annotation-status-none'}\n          toggleElement={DataElements.NOTE_STATE_FLYOUT}\n          disabled={isDocumentReadOnly || modifiableMultiSelectAnnotations.length === 0}\n        />\n        <NoteStateFlyout\n          isMultiSelectMode={true}\n          handleStateChange={handleStateChange}\n        />\n        <Button\n          dataElement={DataElements.NOTE_MULTI_STYLE_BUTTON}\n          img=\"icon-menu-style-line\"\n          disabled={isDocumentReadOnly || modifiableMultiSelectAnnotations.length === 0}\n          onClick={() => {\n            setShowMultiStyle(!showMultiStyle);\n          }}\n          title=\"action.style\"\n        />\n        {showMultiStyle &&\n          <MultiStylePopup\n            annotations={modifiableMultiSelectAnnotations}\n            triggerElementName=\"multiStyleButton\"\n            onClose={() => {\n              setShowMultiStyle(false);\n            }}\n          />}\n        {!canUngroup &&\n          <Button\n            dataElement={DataElements.NOTE_MULTI_GROUP_BUTTON}\n            disabled={isDocumentReadOnly || !canGroup}\n            img=\"group-annotations-icon\"\n            onClick={() => {\n              core.groupAnnotations(multiSelectedAnnotations[0], multiSelectedAnnotations, activeDocumentViewerKey);\n            }}\n            title=\"action.group\"\n          />}\n        {canUngroup &&\n          <Button\n            dataElement={DataElements.NOTE_MULTI_UNGROUP_BUTTON}\n            img=\"ungroup-annotations-icon\"\n            onClick={() => {\n              core.ungroupAnnotations(multiSelectedAnnotations, activeDocumentViewerKey);\n            }}\n            title=\"action.ungroup\"\n          />}\n        <Button\n          dataElement={DataElements.NOTE_MULTI_DELETE_BUTTON}\n          disabled={isDocumentReadOnly || modifiableMultiSelectAnnotations.length === 0}\n          img=\"icon-delete-line\"\n          onClick={() => {\n            const title = t('warning.multiDeleteAnnotation.title');\n            const message = t('warning.multiDeleteAnnotation.message');\n            const confirmBtnText = t('action.delete');\n\n            const warning = {\n              title,\n              message,\n              confirmBtnText,\n              onConfirm: () => {\n                core.deleteAnnotations(modifiableMultiSelectAnnotations, undefined, activeDocumentViewerKey);\n              },\n            };\n            dispatch(actions.showWarningMessage(warning));\n          }}\n          title=\"action.delete\"\n        />\n      </div>\n      <div\n        className=\"close-container\"\n      >\n        <Button\n          className=\"close-icon-container\"\n          onClick={() => {\n            setMultiSelectMode(false);\n          }}\n          img='ic_close_black_24px'\n        />\n      </div>\n    </div>\n  );\n};\n\nMultiSelectControls.propTypes = propTypes;\n\nexport default MultiSelectControls;\n", "import React, { useState, useEffect } from 'react';\nimport { useSelector, useDispatch, shallowEqual } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\nimport PropTypes from 'prop-types';\nimport debounce from 'lodash/debounce';\nimport selectors from 'selectors';\nimport actions from 'actions';\nimport classNames from 'classnames';\nimport core from 'core';\n\nimport Dropdown from 'components/Dropdown';\nimport Button from 'components/Button';\nimport DataElementWrapper from 'components/DataElementWrapper';\nimport CustomElement from 'components/CustomElement';\n\nimport Events from 'constants/events';\nimport { getSortStrategies } from 'constants/sortStrategies';\nimport DataElements from 'constants/dataElement';\nimport { OFFICE_EDITOR_EDIT_MODE } from 'constants/officeEditor';\nimport useFocusHandler from 'hooks/useFocusHandler';\n\nimport './NotesPanelHeader.scss';\nimport Icon from '../Icon';\n\nconst propTypes = {\n  notes: PropTypes.array.isRequired,\n  disableFilterAnnotation: PropTypes.bool,\n  setSearchInputHandler: PropTypes.func.isRequired,\n  isMultiSelectMode: PropTypes.bool,\n  toggleMultiSelectMode: PropTypes.func,\n  isMultiSelectEnabled: PropTypes.bool,\n};\n\nconst SORT_CONTAINER_ELEMENT = 'sortContainer';\nfunction NotesPanelHeader({\n  notes,\n  disableFilterAnnotation,\n  setSearchInputHandler,\n  isMultiSelectMode,\n  toggleMultiSelectMode,\n  isMultiSelectEnabled,\n}) {\n  const [\n    sortStrategy,\n    isSortContainerDisabled,\n    customHeaderOptions,\n    annotationFilters,\n    isOfficeEditorMode,\n    officeEditorEditMode,\n    customizableUI,\n  ] = useSelector(\n    (state) => [\n      selectors.getSortStrategy(state),\n      selectors.isElementDisabled(state, SORT_CONTAINER_ELEMENT),\n      selectors.getNotesPanelCustomHeaderOptions(state),\n      selectors.getAnnotationFilters(state),\n      selectors.getIsOfficeEditorMode(state),\n      selectors.getOfficeEditorEditMode(state),\n      selectors.getFeatureFlags(state)?.customizableUI,\n    ],\n    shallowEqual\n  );\n\n  const [t] = useTranslation();\n  const dispatch = useDispatch();\n  const [filterEnabled, setFilterEnabled] = useState(false);\n  const [isPreviewingTrackedChanges, setIsPreviewingTrackedChanges] = useState(false);\n  const [searchInput, setSearchInput] = useState('');\n\n  useEffect(() => {\n    // check if Redux filter state is enabled on mount and set filterEnabled to true\n    const { authorFilter, colorFilter, statusFilter, typeFilter } = annotationFilters;\n    if (authorFilter?.length > 0 || colorFilter?.length > 0 || statusFilter?.length > 0 || typeFilter?.length > 0) {\n      setFilterEnabled(true);\n    }\n\n    const toggleFilterStyle = (e) => {\n      const { types, authors, colors, statuses } = e.detail;\n      if (types.length > 0 || authors.length > 0 || colors.length > 0 || statuses.length > 0) {\n        setFilterEnabled(true);\n      } else {\n        setFilterEnabled(false);\n      }\n    };\n\n    window.addEventListener(Events.ANNOTATION_FILTER_CHANGED, toggleFilterStyle);\n    return () => {\n      window.removeEventListener(Events.ANNOTATION_FILTER_CHANGED, toggleFilterStyle);\n    };\n  }, []);\n\n  // on oe preview mode, disable and clear the search input\n  useEffect(() => {\n    if (isOfficeEditorMode && officeEditorEditMode === OFFICE_EDITOR_EDIT_MODE.PREVIEW) {\n      setIsPreviewingTrackedChanges(true);\n      setSearchInputHandler('');\n      setSearchInput('');\n    } else {\n      setIsPreviewingTrackedChanges(false);\n    }\n  }, [isOfficeEditorMode, officeEditorEditMode]);\n\n  const handleInputChange = (e) => {\n    setSearchInput(e.target.value);\n    _handleInputChange(e.target.value);\n  };\n\n  const _handleInputChange = debounce((value) => {\n    // this function is used to solve the issue with using synthetic event asynchronously.\n    // https://reactjs.org/docs/events.html#event-pooling\n    core.deselectAllAnnotations();\n    setSearchInputHandler(value);\n  }, 500);\n\n  const sortContainer = (\n    <div className=\"sort-container\" data-element={SORT_CONTAINER_ELEMENT}>\n      <div className=\"label\">{`${t('message.sort')}:`}</div>\n      <Dropdown\n        dataElement=\"notesOrderDropdown\"\n        disabled={notes.length === 0 || isPreviewingTrackedChanges}\n        ariaLabel={`${t('message.sortBy')} ${sortStrategy}`}\n        items={Object.keys(getSortStrategies())}\n        translationPrefix=\"option.notesOrder\"\n        currentSelectionKey={sortStrategy}\n        onClickItem={(strategy) => {\n          dispatch(actions.setNotesPanelSortStrategy(strategy));\n        }}\n      />\n    </div>\n  );\n\n  const openFilterModalWithFocusTransfer = useFocusHandler(() => dispatch(actions.openElement('filterModal')));\n  const placeholderText = isOfficeEditorMode ? t('message.searchSuggestionsPlaceholder') : t('message.searchCommentsPlaceholder');\n  const originalHeaderElement = (\n    <DataElementWrapper\n      className={\n        classNames({\n          'header': true,\n          'modular-ui-header': customizableUI,\n        })}\n      dataElement=\"notesPanelHeader\"\n    >\n      <DataElementWrapper\n        className={classNames({\n          'input-container': true,\n          'modular-ui-input': customizableUI,\n        })}\n        dataElement={DataElements.NotesPanel.DefaultHeader.INPUT_CONTAINER}\n      >\n        {customizableUI && <Icon glyph=\"icon-header-search\" />}\n        <input\n          disabled={isPreviewingTrackedChanges}\n          type=\"text\"\n          placeholder={customizableUI ? '' : placeholderText}\n          aria-label={placeholderText}\n          onChange={handleInputChange}\n          id=\"NotesPanel__input\"\n          value={searchInput}\n        />\n      </DataElementWrapper>\n\n      <DataElementWrapper\n        className=\"comments-counter\"\n        dataElement={DataElements.NotesPanel.DefaultHeader.COMMENTS_COUNTER}\n      >\n        <span className='main-comment'>{isOfficeEditorMode ? t('officeEditor.reviewing') : t('component.notesPanel')}</span> {`(${notes.length})`}\n      </DataElementWrapper>\n\n      <DataElementWrapper\n        className=\"sort-row\"\n        dataElement={DataElements.NotesPanel.DefaultHeader.SORT_ROW}\n      >\n        {(isSortContainerDisabled) ? <div className=\"sort-container\"></div> : sortContainer}\n        <div\n          className=\"buttons-container\"\n        >\n          {isMultiSelectEnabled && !isOfficeEditorMode && (\n            <Button\n              dataElement={DataElements.NOTE_MULTI_SELECT_MODE_BUTTON}\n              className={classNames({\n                active: isMultiSelectMode,\n              })}\n              disabled={notes.length === 0}\n              img=\"icon-annotation-select-multiple\"\n              onClick={() => {\n                core.deselectAllAnnotations();\n                toggleMultiSelectMode();\n              }}\n              title={t('component.multiSelectButton')}\n            />\n          )}\n          <Button\n            dataElement={DataElements.NotesPanel.DefaultHeader.FILTER_ANNOTATION_BUTTON}\n            className={classNames({\n              active: filterEnabled\n            })}\n            disabled={disableFilterAnnotation}\n            img=\"icon-comments-filter\"\n            onClick={openFilterModalWithFocusTransfer}\n            title={t('component.filter')}\n          />\n        </div>\n      </DataElementWrapper>\n    </DataElementWrapper>\n  );\n\n  return (\n    <>\n      {customHeaderOptions &&\n        <CustomElement\n          render={customHeaderOptions.render}\n          renderArguments={[notes]}\n        />\n      }\n\n      {(!customHeaderOptions || !customHeaderOptions.overwriteDefaultHeader) &&\n        originalHeaderElement\n      }\n    </>\n  );\n}\n\nNotesPanelHeader.propTypes = propTypes;\n\nexport default NotesPanelHeader;\n", "import NotesPanelHeader from './NotesPanelHeader';\n\nexport default NotesPanelHeader;", "import React, { useState, useRef, useEffect, useCallback } from 'react';\nimport classNames from 'classnames';\nimport { useSelector, useDispatch, shallowEqual } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\n\nimport VirtualizedList from 'components/NotesPanel/VirtualizedList';\nimport NormalList from 'components/NotesPanel/NormalList';\nimport Note from 'components/Note';\nimport Icon from 'components/Icon';\nimport NoteContext from 'components/Note/Context';\nimport ListSeparator from 'components/ListSeparator';\nimport MultiSelectControls from 'components/NotesPanel/MultiSelectControls';\nimport CustomElement from 'components/CustomElement';\nimport NotesPanelHeader from 'components/NotesPanelHeader';\nimport Choice from 'components/Choice';\n\nimport core from 'core';\nimport DataElements from 'constants/dataElement';\nimport { getSortStrategies } from 'constants/sortStrategies';\nimport { OFFICE_EDITOR_EDIT_MODE } from 'constants/officeEditor';\nimport actions from 'actions';\nimport selectors from 'selectors';\nimport { isMobileSize } from 'helpers/getDeviceSize';\nimport { isIE } from 'helpers/device';\nimport ReplyAttachmentPicker from './ReplyAttachmentPicker';\n\nimport './NotesPanel.scss';\n\nconst NotesPanel = ({\n  currentLeftPanelWidth,\n  notes,\n  selectedNoteIds,\n  setSelectedNoteIds,\n  searchInput,\n  setSearchInput,\n  isMultiSelectMode,\n  setMultiSelectMode,\n  isMultiSelectedMap,\n  setIsMultiSelectedMap,\n  scrollToSelectedAnnot,\n  setScrollToSelectedAnnot,\n  isCustomPanel,\n  isCustomPanelOpen,\n  isLeftSide,\n  parentDataElement,\n}) => {\n\n  const sortStrategy = useSelector(selectors.getSortStrategy);\n  const isOpen = useSelector((state) => selectors.isElementOpen(state, DataElements.NOTES_PANEL));\n  const isDisabled = useSelector((state) => selectors.isElementDisabled(state, DataElements.NOTES_PANEL));\n  const pageLabels = useSelector(selectors.getPageLabels, shallowEqual);\n  const customNoteFilter = useSelector(selectors.getCustomNoteFilter, shallowEqual);\n  const currentNotesPanelWidth = useSelector((state) => parentDataElement ? selectors.getPanelWidth(state, parentDataElement) : selectors.getNotesPanelWidth(state), shallowEqual);\n  const notesInLeftPanel = useSelector(selectors.getNotesInLeftPanel);\n  const isDocumentReadOnly = useSelector(selectors.isDocumentReadOnly);\n  const showAnnotationNumbering = useSelector(selectors.isAnnotationNumberingEnabled);\n  const enableNotesPanelVirtualizedList = useSelector(selectors.getEnableNotesPanelVirtualizedList);\n  const isInDesktopOnlyMode = useSelector(selectors.isInDesktopOnlyMode);\n  const customEmptyPanel = useSelector(selectors.getNotesPanelCustomEmptyPanel, shallowEqual);\n  const isNotesPanelMultiSelectEnabled = useSelector(selectors.getIsNotesPanelMultiSelectEnabled);\n  const activeDocumentViewerKey = useSelector(selectors.getActiveDocumentViewerKey);\n  const isOfficeEditorMode = useSelector(selectors.getIsOfficeEditorMode);\n\n  const dispatch = useDispatch();\n  const [t] = useTranslation();\n\n  const currentWidth = currentLeftPanelWidth || currentNotesPanelWidth;\n  const isMobile = isMobileSize();\n\n  const [multiSelectedAnnotations, setMultiSelectedAnnotations] = useState([]);\n  const [showMultiReply, setShowMultiReply] = useState(false);\n  const [showMultiState, setShowMultiState] = useState(false);\n  const [showMultiStyle, setShowMultiStyle] = useState(false);\n  const [curAnnotId, setCurAnnotId] = useState(undefined);\n\n  const listRef = useRef();\n  // a ref that is used to keep track of the current scroll position\n  // when the number of notesToRender goes over/below the threshold, we will unmount the current list and mount the other one\n  // this will result in losing the scroll position and we will use this ref to recover\n  const scrollTopRef = useRef(0);\n  const VIRTUALIZATION_THRESHOLD = enableNotesPanelVirtualizedList ? (isIE ? 25 : 100) : Infinity;\n\n  useEffect(() => {\n    const onAnnotationNumberingUpdated = (isEnabled) => {\n      dispatch(actions.setAnnotationNumbering(isEnabled));\n    };\n    const onAnnotationSelected = (annotations, action) => {\n      if (action === 'selected') {\n        setCurAnnotId(annotations[0].Id);\n      }\n    };\n\n    core.addEventListener('annotationNumberingUpdated', onAnnotationNumberingUpdated);\n    core.addEventListener('annotationSelected', onAnnotationSelected);\n\n    return () => {\n      core.removeEventListener('annotationNumberingUpdated', onAnnotationNumberingUpdated);\n      core.removeEventListener('annotationSelected', onAnnotationSelected);\n    };\n  }, []);\n\n  let singleSelectedNoteIndex = -1;\n\n  const handleScroll = (scrollTop) => {\n    if (scrollTop) {\n      scrollTopRef.current = scrollTop;\n    }\n    dispatch(actions.closeElement('annotationNoteConnectorLine'));\n  };\n\n  const filterNotesWithSearch = (note) => {\n    const content = note.getContents();\n    const authorName = core.getDisplayAuthor(note['Author']);\n    const annotationPreview = note.getCustomData('trn-annot-preview');\n\n    // didn't use regex here because the search input may form an invalid regex, e.g. *\n    return (\n      content?.toLowerCase().includes(searchInput.toLowerCase()) ||\n      authorName?.toLowerCase().includes(searchInput.toLowerCase()) ||\n      annotationPreview?.toLowerCase().includes(searchInput.toLowerCase())\n    );\n  };\n\n  const filterNote = (note) => {\n    let shouldRender = true;\n\n    if (customNoteFilter) {\n      shouldRender = shouldRender && customNoteFilter(note);\n    }\n\n    if (searchInput) {\n      const replies = note.getReplies();\n      // reply is also a kind of annotation\n      // https://docs.apryse.com/api/web/Core.AnnotationManager.html#createAnnotationReply__anchor\n      const noteAndReplies = [note, ...replies];\n\n      shouldRender = shouldRender && noteAndReplies.some(filterNotesWithSearch);\n    }\n    return shouldRender;\n  };\n\n  const notesToRender = getSortStrategies()[sortStrategy].getSortedNotes(notes).filter(filterNote);\n\n  useEffect(() => {\n    if (Object.keys(selectedNoteIds).length && singleSelectedNoteIndex !== -1) {\n      setTimeout(() => {\n        // wait for the previous selected annotation to resize() after closing before scrolling to the newly selected one\n        listRef.current?.scrollToRow(singleSelectedNoteIndex);\n      }, 0);\n    }\n  }, [selectedNoteIds]);\n\n  // expand a reply note when search content is match\n  const onlyReplyContainsSearchInput = (currNote) => {\n    if (Object.keys(selectedNoteIds).length) {\n      return false;\n    }\n    return (\n      searchInput &&\n      notesToRender\n        .filter((note) => {\n          return note.getReplies().some(filterNotesWithSearch);\n        })\n        .some((replies) => replies.Id === currNote.Id)\n    );\n  };\n\n  const [pendingEditTextMap, setPendingEditTextMap] = useState({});\n  const setPendingEditText = useCallback(\n    (pendingText, annotationID) => {\n      setPendingEditTextMap((map) => ({\n        ...map,\n        [annotationID]: pendingText,\n      }));\n    },\n    [setPendingEditTextMap],\n  );\n\n  const [pendingReplyMap, setPendingReplyMap] = useState({});\n  const setPendingReply = useCallback(\n    (pendingReply, annotationID) => {\n      setPendingReplyMap((map) => ({\n        ...map,\n        [annotationID]: pendingReply,\n      }));\n    },\n    [setPendingReplyMap],\n  );\n\n  const [pendingAttachmentMap, setPendingAttachmentMap] = useState({});\n  const addAttachments = (annotationID, attachments) => {\n    setPendingAttachmentMap((map) => ({\n      ...map,\n      [annotationID]: [...(map[annotationID] || []), ...attachments],\n    }));\n  };\n  const clearAttachments = (annotationID) => {\n    setPendingAttachmentMap((map) => ({\n      ...map,\n      [annotationID]: [],\n    }));\n  };\n  const deleteAttachment = (annotationID, attachment) => {\n    const attachmentList = pendingAttachmentMap[annotationID];\n    if (attachmentList?.length > 0) {\n      const index = attachmentList.indexOf(attachment);\n      if (index > -1) {\n        attachmentList.splice(index, 1);\n        setPendingAttachmentMap((map) => ({\n          ...map,\n          [annotationID]: [...attachmentList],\n        }));\n      }\n    }\n  };\n\n  useEffect(() => {\n    setMultiSelectedAnnotations(Object.values(isMultiSelectedMap));\n  }, [isMultiSelectedMap]);\n\n  const toggleMultiSelectMode = () => {\n    if (isMultiSelectMode) {\n      setMultiSelectMode(false);\n    } else {\n      setMultiSelectMode(true);\n    }\n  };\n\n  const renderChild = (\n    notes,\n    index,\n    // when we are virtualizing the notes, all of them will be absolutely positioned\n    // this function needs to be called by a Note component whenever its height changes\n    // to clear the cache(used by react-virtualized) and recompute the height so that each note\n    // can have the correct position\n    resize = () => { },\n  ) => {\n    let listSeparator = null;\n    const { shouldRenderSeparator, getSeparatorContent } = getSortStrategies()[sortStrategy];\n    const prevNote = index === 0 ? null : notes[index - 1];\n    const currNote = notes[index];\n\n    if (shouldRenderSeparator && getSeparatorContent && (!prevNote || shouldRenderSeparator(prevNote, currNote))) {\n      listSeparator = <ListSeparator renderContent={() => getSeparatorContent(prevNote, currNote, { pageLabels })} />;\n    }\n\n    // Collapse an expanded note when the top non-reply NoteContent is clicked\n    const handleNoteClicked = () => {\n      if (!isMultiSelectMode && selectedNoteIds[currNote.Id]) {\n        setSelectedNoteIds((currIds) => {\n          const clone = { ...currIds };\n          delete clone[currNote.Id];\n          return clone;\n        });\n        core.deselectAnnotation(currNote, activeDocumentViewerKey);\n      }\n    };\n\n    // can potentially optimize this a bit since a new reference will cause consumers to rerender\n    const contextValue = {\n      searchInput,\n      resize,\n      isSelected: selectedNoteIds[currNote.Id],\n      isContentEditable: core.canModifyContents(currNote, activeDocumentViewerKey) && !currNote.getContents(),\n      pendingEditTextMap,\n      setPendingEditText,\n      pendingReplyMap,\n      setPendingReply,\n      isDocumentReadOnly,\n      onTopNoteContentClicked: handleNoteClicked,\n      isExpandedFromSearch: onlyReplyContainsSearchInput(currNote),\n      scrollToSelectedAnnot,\n      sortStrategy,\n      showAnnotationNumbering,\n      setCurAnnotId,\n      pendingAttachmentMap,\n      clearAttachments,\n      deleteAttachment,\n      addAttachments,\n      documentViewerKey: activeDocumentViewerKey,\n    };\n\n    if (index === singleSelectedNoteIndex) {\n      setTimeout(() => {\n        setScrollToSelectedAnnot(false);\n        // open the 'annotationNoteConnectorLine' since the note it's pointing to is being rendered\n        dispatch(actions.openElement('annotationNoteConnectorLine'));\n      }, 0);\n    }\n\n    return (\n      // unfortunately we need to use an actual div instead of React.Fragment here so that we can pass the correct index to scrollToRow\n      // if this is a fragment then the listSeparator is rendered as a separate child, which means\n      // singleSelectedNoteIndex might not be the index of the selected note among all the child elements of the notes panel\n      <div role=\"listitem\" className=\"note-wrapper\">\n        {listSeparator}\n        <NoteContext.Provider value={contextValue}>\n          <Note\n            isCustomPanelOpen={isCustomPanelOpen}\n            shouldHideConnectorLine={isLeftSide}\n            annotation={currNote}\n            isMultiSelected={!!isMultiSelectedMap[currNote.Id]}\n            isMultiSelectMode={isMultiSelectMode}\n            isMultiSelectEnabled={isNotesPanelMultiSelectEnabled}\n            isInNotesPanel\n            handleMultiSelect={(checked) => {\n              if (checked) {\n                const _isMultiSelectedMap = { ...isMultiSelectedMap };\n                const groupAnnots = core.getGroupAnnotations(currNote, activeDocumentViewerKey);\n                groupAnnots.forEach((groupAnnot) => {\n                  _isMultiSelectedMap[groupAnnot.Id] = groupAnnot;\n                });\n                setIsMultiSelectedMap(_isMultiSelectedMap);\n                core.selectAnnotations(groupAnnots);\n              } else {\n                const _isMultiSelectedMap = { ...isMultiSelectedMap };\n                const groupAnnots = core.getGroupAnnotations(currNote, activeDocumentViewerKey);\n                groupAnnots.forEach((groupAnnot) => {\n                  delete _isMultiSelectedMap[groupAnnot.Id];\n                });\n                setIsMultiSelectedMap(_isMultiSelectedMap);\n                core.deselectAnnotations([currNote, ...groupAnnots]);\n              }\n            }}\n          />\n        </NoteContext.Provider>\n      </div>\n    );\n  };\n\n  const NoResults = (\n    <div className=\"no-results\">\n      <div>\n        <Icon className=\"empty-icon\" glyph=\"illustration - empty state - outlines\" />\n      </div>\n      <p aria-live=\"assertive\" className=\"msg no-margin\">{t('message.noResults')}</p>\n    </div>\n  );\n\n  const NoAnnotationsGlyph = customEmptyPanel?.icon ?\n    customEmptyPanel.icon :\n    (isOfficeEditorMode ? 'ic-edit-page' : 'illustration - empty state - outlines');\n  const NoAnnotationsMessage = customEmptyPanel?.message ?\n    customEmptyPanel.message :\n    (isOfficeEditorMode ? t('message.noRevisions') : t('message.noAnnotations'));\n  const NoAnnotationsReadOnlyMessage =\n    customEmptyPanel && customEmptyPanel.readOnlyMessage\n      ? customEmptyPanel.readOnlyMessage\n      : t('message.noAnnotationsReadOnly');\n  const shouldRenderNoAnnotationsIcon = (customEmptyPanel && !customEmptyPanel.hideIcon) || !customEmptyPanel;\n  const shouldRenderCustomEmptyPanel = customEmptyPanel && customEmptyPanel.render;\n\n  const NoAnnotations = (\n    <div className=\"no-annotations\">\n      {shouldRenderCustomEmptyPanel ? (\n        <CustomElement render={customEmptyPanel.render} />\n      ) : (\n        <>\n          {shouldRenderNoAnnotationsIcon && (\n            <div>\n              <Icon className=\"empty-icon\" glyph={NoAnnotationsGlyph} />\n            </div>\n          )}\n          <div className=\"msg\">{isDocumentReadOnly ? NoAnnotationsReadOnlyMessage : NoAnnotationsMessage}</div>\n        </>\n      )}\n    </div>\n  );\n\n  const MultiSelectPlaceHolder = <div className=\"multi-select-place-holder\" />;\n\n  const MultiReplyPlaceHolder = <div className=\"multi-reply-place-holder\" />;\n\n  // keep track of the index of the single selected note in the sorted and filtered list\n  // in order to scroll it into view in this render effect\n  const ids = Object.keys(selectedNoteIds);\n  if (ids.length === 1) {\n    singleSelectedNoteIndex = notesToRender.findIndex((note) => note.Id === ids[0]);\n  } else if (ids.length) {\n    // when selecting annotations that are grouped together, scroll to parent annotation that is in \"notesToRender\"\n    // selectedNoteIds will have every ID in the group, while only the parent is in notesToRender\n    const existingSelectedNotes = notesToRender.filter((note) => selectedNoteIds[note.Id]);\n\n    if (existingSelectedNotes.length) {\n      singleSelectedNoteIndex = notesToRender.findIndex((note) => note.Id === curAnnotId);\n    }\n  }\n\n  let style = {};\n  if (!isCustomPanel && (isInDesktopOnlyMode || !isMobile)) {\n    style = { width: `${currentWidth}px`, minWidth: `${currentWidth}px` };\n  }\n\n  const showNotePanel = !isDisabled && (isOpen || notesInLeftPanel || isCustomPanel);\n\n  return !showNotePanel ? null : (\n    <div className=\"notes-panel-container\">\n      <div\n        className={classNames({\n          Panel: true,\n          NotesPanel: true,\n        })}\n        style={style}\n        data-element=\"notesPanel\"\n        onMouseUp={() => core.deselectAllAnnotations}\n      >\n        {!isInDesktopOnlyMode && isMobile && !notesInLeftPanel && (\n          <div className=\"close-container\">\n            <div\n              className=\"close-icon-container\"\n              onClick={() => {\n                dispatch(actions.closeElements([DataElements.NOTES_PANEL]));\n              }}\n            >\n              <Icon glyph=\"ic_close_black_24px\" className=\"close-icon\" />\n            </div>\n          </div>\n        )}\n        <>\n          <NotesPanelHeader\n            notes={notesToRender}\n            disableFilterAnnotation={notes.length === 0}\n            setSearchInputHandler={setSearchInput}\n            isMultiSelectMode={isMultiSelectMode}\n            toggleMultiSelectMode={toggleMultiSelectMode}\n            isMultiSelectEnabled={isNotesPanelMultiSelectEnabled}\n          />\n          {notesToRender.length === 0 ? (\n            notes.length === 0 ? (\n              NoAnnotations\n            ) : (\n              NoResults\n            )\n          ) : notesToRender.length <= VIRTUALIZATION_THRESHOLD ? (\n            <NormalList\n              ref={listRef}\n              notes={notesToRender}\n              onScroll={handleScroll}\n              initialScrollTop={scrollTopRef.current}\n            >\n              {renderChild}\n            </NormalList>\n          ) : (\n            <VirtualizedList\n              ref={listRef}\n              notes={notesToRender}\n              sortStrategy={sortStrategy}\n              onScroll={handleScroll}\n              initialScrollTop={scrollTopRef.current}\n              selectedIndex={singleSelectedNoteIndex}\n            >\n              {renderChild}\n            </VirtualizedList>\n          )}\n          {/* These two placeholders need to exist so that MultiSelectControls can\n          be overlayed with position absolute and extend into the right panel while\n          still being able to not have any notes cut off */}\n          {isMultiSelectMode ? (showMultiReply ? MultiReplyPlaceHolder : MultiSelectPlaceHolder) : null}\n          {isOfficeEditorMode && !isMultiSelectMode && (notesToRender.length > 0) && (\n            <div className=\"preview-all-changes\">\n              <div className=\"divider\" />\n              <Choice\n                isSwitch\n                label={t('officeEditor.previewAllChanges')}\n                onChange={(e) => core.getOfficeEditor().setEditMode(e.target.checked ? OFFICE_EDITOR_EDIT_MODE.PREVIEW : OFFICE_EDITOR_EDIT_MODE.REVIEWING)}\n              />\n            </div>\n          )}\n        </>\n      </div>\n      {isMultiSelectMode && (\n        <MultiSelectControls\n          showMultiReply={showMultiReply}\n          setShowMultiReply={setShowMultiReply}\n          showMultiState={showMultiState}\n          setShowMultiState={setShowMultiState}\n          showMultiStyle={showMultiStyle}\n          setShowMultiStyle={setShowMultiStyle}\n          setMultiSelectMode={setMultiSelectMode}\n          isMultiSelectedMap={isMultiSelectedMap}\n          setIsMultiSelectedMap={setIsMultiSelectedMap}\n          multiSelectedAnnotations={multiSelectedAnnotations}\n        />\n      )}\n      <ReplyAttachmentPicker annotationId={curAnnotId} addAttachments={addAttachments} />\n    </div>\n  );\n};\n\nexport default NotesPanel;\n", "import React, { useEffect, useState, useCallback } from 'react';\nimport core from 'core';\nimport NotesPanel from './NotesPanel';\nimport { useSelector, shallowEqual } from 'react-redux';\nimport selectors from 'selectors';\nimport DataElements from 'constants/dataElement';\nimport { mapAnnotationToKey, annotationMapKeys } from 'constants/map';\n\nfunction NotesPanelContainer(props) {\n  const { isCustomPanelOpen, parentDataElement = undefined, dataElement } = props;\n  const [\n    isOpen,\n    notesInLeftPanel,\n    isNotesPanelMultiSelectEnabled,\n    isMultiViewerMode,\n    activeDocumentViewerKey,\n    isOfficeEditorMode,\n  ] = useSelector(\n    (state) => [\n      selectors.isElementOpen(state, parentDataElement || dataElement || DataElements.NOTES_PANEL),\n      selectors.getNotesInLeftPanel(state),\n      selectors.getIsNotesPanelMultiSelectEnabled(state),\n      selectors.isMultiViewerMode(state),\n      selectors.getActiveDocumentViewerKey(state),\n      selectors.getIsOfficeEditorMode(state),\n    ],\n    shallowEqual,\n  );\n  const [searchInput, setSearchInput] = useState('');\n  const [isMultiSelectMode, setMultiSelectMode] = useState(false);\n  const [scrollToSelectedAnnot, setScrollToSelectedAnnot] = useState(false);\n\n  const [noteMap, setNoteMap] = useState({ 1: [], 2: [] });\n  const setNotes = useCallback((notes, documentViewerKey = activeDocumentViewerKey) => {\n    noteMap[documentViewerKey] = notes;\n    setNoteMap({ ...noteMap });\n  }, [activeDocumentViewerKey, noteMap[1], noteMap[2], setNoteMap]);\n  const notes = noteMap[activeDocumentViewerKey] || noteMap[1];\n\n  // the object will be in a shape of { [note.Id]: true }\n  // use a map here instead of an array to achieve an O(1) time complexity for checking if a note is selected\n  const [selectedNoteIdsMap, setSelectedNoteIdsMap] = useState({ 1: {}, 2: {}, });\n  const setSelectedNoteIds = useCallback((selectedNoteIds, documentViewerKey = activeDocumentViewerKey) => {\n    selectedNoteIdsMap[documentViewerKey] = selectedNoteIds;\n    setSelectedNoteIdsMap({ ...selectedNoteIdsMap });\n  }, [activeDocumentViewerKey, selectedNoteIdsMap[1], selectedNoteIdsMap[2], setSelectedNoteIdsMap]);\n  const selectedNoteIds = selectedNoteIdsMap[activeDocumentViewerKey] || selectedNoteIdsMap[1];\n\n  const [isMultiSelectedViewerMap, setIsMultiSelectedViewerMap] = useState({ 1: {}, 2: {}, });\n  const setIsMultiSelectedMap = useCallback((isMultiSelected, documentViewerKey = activeDocumentViewerKey) => {\n    isMultiSelectedViewerMap[documentViewerKey] = isMultiSelected;\n    setIsMultiSelectedViewerMap({ ...isMultiSelectedViewerMap });\n  }, [activeDocumentViewerKey, isMultiSelectedViewerMap[1], isMultiSelectedViewerMap[2], setIsMultiSelectedViewerMap]);\n  const isMultiSelectedMap = isMultiSelectedViewerMap[activeDocumentViewerKey] || isMultiSelectedViewerMap[1];\n\n  useEffect(() => {\n    const onDocumentUnloaded = (documentViewerKey = activeDocumentViewerKey) => () => {\n      setNotes([], documentViewerKey);\n      setSelectedNoteIds({});\n      setSearchInput('');\n    };\n    const _setNotes = (documentViewerKey = activeDocumentViewerKey) => () => {\n      const selectedAnnotations = core.getSelectedAnnotations(documentViewerKey);\n      const groupedAnnots = getGroupedAnnots(selectedAnnotations);\n\n      if (isMultiSelectMode && groupedAnnots.length === selectedAnnotations.length) {\n        setMultiSelectMode(false);\n      }\n\n      setNotes(\n        core\n          .getAnnotationsList(documentViewerKey)\n          .filter(\n            (annot) => annot.Listable &&\n              !annot.isReply() &&\n              !annot.Hidden &&\n              !annot.isGrouped() &&\n              annot.ToolName !== window.Core.Tools.ToolNames.CROP &&\n              !annot.isContentEditPlaceholder() &&\n              (!isOfficeEditorMode || mapAnnotationToKey(annot) === annotationMapKeys.TRACKED_CHANGE),\n          ),\n        documentViewerKey,\n      );\n    };\n\n    const setDocumentUnloaded1 = onDocumentUnloaded(1);\n    core.addEventListener('documentUnloaded', setDocumentUnloaded1);\n    const setNotes1 = _setNotes(1);\n    core.addEventListener('annotationChanged', setNotes1);\n    core.addEventListener('annotationHidden', setNotes1);\n    core.addEventListener('updateAnnotationPermission', setNotes1);\n    setNotes1();\n\n    let setDocumentUnloaded2;\n    let setNotes2;\n    if (isMultiViewerMode) {\n      setDocumentUnloaded2 = onDocumentUnloaded(2);\n      core.addEventListener('documentUnloaded', setDocumentUnloaded2, undefined, 2);\n      setNotes2 = _setNotes(2);\n      core.addEventListener('annotationChanged', setNotes2, undefined, 2);\n      core.addEventListener('annotationHidden', setNotes2, undefined, 2);\n      core.addEventListener('updateAnnotationPermission', setNotes2, undefined, 2);\n      setNotes2();\n    }\n\n    return () => {\n      if (isMultiViewerMode) {\n        core.removeEventListener('documentUnloaded', setDocumentUnloaded2, 2);\n        core.removeEventListener('annotationChanged', setNotes2, 2);\n        core.removeEventListener('annotationHidden', setNotes2, 2);\n        core.removeEventListener('updateAnnotationPermission', setNotes2, 2);\n      }\n      core.removeEventListener('documentUnloaded', setDocumentUnloaded1);\n      core.removeEventListener('annotationChanged', setNotes1);\n      core.removeEventListener('annotationHidden', setNotes1);\n      core.removeEventListener('updateAnnotationPermission', setNotes1);\n    };\n  }, [isMultiViewerMode, isOfficeEditorMode]);\n\n  useEffect(() => {\n    const onAnnotationSelected = (documentViewerKey = activeDocumentViewerKey) => (annotations, action) => {\n      const selectedAnnotations = core.getSelectedAnnotations(documentViewerKey);\n      const ids = {};\n      selectedAnnotations.forEach((annot) => {\n        ids[annot.Id] = true;\n      });\n      if (isCustomPanelOpen || isOpen || notesInLeftPanel) {\n        setSelectedNoteIds(ids, documentViewerKey);\n        setScrollToSelectedAnnot(true);\n      }\n      const groupedAnnots = getGroupedAnnots(selectedAnnotations);\n      const shouldDisplayMultiSelect = (selectedAnnotations.length > 1 && groupedAnnots.length !== selectedAnnotations.length) || isMultiSelectMode;\n\n      if (isNotesPanelMultiSelectEnabled\n        && action === 'selected'\n        && shouldDisplayMultiSelect) {\n        setMultiSelectMode(true);\n        selectedAnnotations.forEach((selectedAnnot) => {\n          isMultiSelectedMap[selectedAnnot.Id] = selectedAnnot;\n        });\n        setIsMultiSelectedMap({ ...isMultiSelectedMap }, documentViewerKey);\n      } else if (action === 'deselected') {\n        annotations.forEach((a) => {\n          delete isMultiSelectedMap[a.Id];\n        });\n        setIsMultiSelectedMap({ ...isMultiSelectedMap }, documentViewerKey);\n      }\n    };\n    const onAnnotationSelected1 = onAnnotationSelected(1);\n    onAnnotationSelected1();\n    core.addEventListener('annotationSelected', onAnnotationSelected1);\n    let onAnnotationSelected2;\n    if (isMultiViewerMode) {\n      onAnnotationSelected2 = onAnnotationSelected(2);\n      onAnnotationSelected2();\n      core.addEventListener('annotationSelected', onAnnotationSelected2, undefined, 2);\n    }\n    return () => {\n      core.removeEventListener('annotationSelected', onAnnotationSelected1);\n      if (isMultiViewerMode) {\n        core.removeEventListener('annotationSelected', onAnnotationSelected2, 2);\n      }\n    };\n  }, [isCustomPanelOpen, isOpen, notesInLeftPanel, isMultiSelectMode, isMultiSelectedMap, isNotesPanelMultiSelectEnabled, isMultiViewerMode]);\n\n  function getGroupedAnnots(selectedAnnotations) {\n    const mainAnnot = selectedAnnotations.find((annot) => annot.isGrouped());\n    const groupedAnnots = [];\n\n    // check if all selected annots are grouped annotations\n    if (mainAnnot) {\n      selectedAnnotations.forEach((annot) => {\n        if (mainAnnot['InReplyTo'] === annot['InReplyTo']\n          || mainAnnot['InReplyTo'] === annot['Id']) {\n          groupedAnnots.push(annot);\n        }\n      });\n    }\n    return groupedAnnots;\n  }\n\n  const passProps = {\n    notes,\n    selectedNoteIds,\n    setSelectedNoteIds,\n    searchInput,\n    setSearchInput,\n    isMultiSelectMode,\n    setMultiSelectMode,\n    isMultiSelectedMap,\n    setIsMultiSelectedMap,\n    scrollToSelectedAnnot,\n    setScrollToSelectedAnnot,\n  };\n\n  // We wrap the element in a div so the tooltip works properly\n  return (\n    <NotesPanel {...props} {...passProps} />\n  );\n}\n\nexport default NotesPanelContainer;\n", "import NotesPanelContainer from './NotesPanelContainer';\n\nexport default NotesPanelContainer;\n", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../../node_modules/css-loader/dist/runtime/api.js\");\nexports = ___CSS_LOADER_API_IMPORT___(false);\n// Module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.multi-reply-place-holder{height:100px}.reply-area-multi-container{border-top:1px solid var(--divider);display:flex;flex-direction:column;width:100%;position:absolute;bottom:0;z-index:100;background:var(--panel-background)}.reply-area-multi-container form{width:100%;display:flex}.reply-area-multi-container .reply-area-multi-header{display:flex;flex-direction:row;margin-left:16px;margin-top:28px;position:relative;justify-content:space-between}.reply-area-multi-container .reply-area-multi-header .title{font-size:13px;font-weight:700;display:flex;align-items:center}.reply-area-multi-container .reply-area-multi-header .close-icon{display:flex;align-items:center;padding-right:12px;cursor:pointer;position:relative;top:1px}.reply-area-multi-container .reply-area-multi-header .close-icon .close-icon{width:22px;height:22px}.reply-area-multi-container .reply-button-container{display:flex;justify-content:flex-end;flex-direction:column}.reply-area-multi-container .reply-button{width:28px;height:28px;padding:0;border:none;background-color:transparent;right:10px;bottom:12px}:host(:not([data-tabbing=true])) .reply-area-multi-container .reply-button,html:not([data-tabbing=true]) .reply-area-multi-container .reply-button{outline:none}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.reply-area-multi-container .reply-button{width:80px}}.reply-area-multi-container .reply-button:hover{background:var(--blue-1)}.reply-area-multi-container .reply-button.disabled{cursor:not-allowed}.reply-area-multi-container .reply-area{position:relative;flex:1;margin:13px 17px 12px 16px;border-radius:4px;align-items:center;background:var(--component-background)}.reply-area-multi-container .reply-area.unread{background:rgba(0,165,228,.08)}.reply-area-multi-container .reply-area .comment-textarea .ql-container .ql-editor.ql-blank{padding:4px}.reply-area-multi-container .reply-area .comment-textarea .ql-container .ql-editor.ql-blank:before{left:4px}.reply-area-multi-container .add-attachment{display:none}.reply-area-multi-container.modular-ui{padding:16px 16px 24px}.reply-area-multi-container.modular-ui form{margin-bottom:0}.reply-area-multi-container.modular-ui .reply-area{margin:13px 16px 24px 0}.reply-area-multi-container.modular-ui .reply-area-multi-header{margin-left:0;margin-top:0}.reply-area-multi-container.modular-ui .reply-area-multi-header .close-icon{width:28px;height:28px}.reply-area-multi-container.modular-ui .close-icon{padding-right:0;margin-right:16px}.reply-area-multi-container.modular-ui .reply-button-container{margin-right:16px;margin-bottom:12px}.reply-area-multi-container.modular-ui .reply-button{left:0;right:0}\", \"\"]);\n// Exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};\nmodule.exports = exports;\n", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../node_modules/css-loader/dist/runtime/api.js\");\nexports = ___CSS_LOADER_API_IMPORT___(false);\n// Module\nexports.push([module.id, \".multi-style-container{border-radius:4px;box-shadow:0 0 3px 0 var(--document-box-shadow);background:var(--component-background);position:relative;right:20px;bottom:52px;pointer-events:auto}.multi-style-container *{box-sizing:border-box}\", \"\"]);\n// Exports\nmodule.exports = exports;\n"], "names": ["api", "content", "__esModule", "default", "module", "id", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "i", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "___CSS_LOADER_API_IMPORT___", "propTypes", "notes", "PropTypes", "isRequired", "children", "onScroll", "initialScrollTop", "selectedIndex", "sortStrategy", "cache", "CellMeasurerCache", "defaultHeight", "fixedWidth", "VirtualizedList", "React", "_ref", "forwardedRef", "listRef", "useRef", "_useState2", "_slicedToArray", "useState", "offset", "setOffset", "_useState4", "width", "height", "dimension", "setDimension", "prevWindowHeight", "innerHeight", "useImperativeHandle", "scrollToPosition", "scrollTop", "current", "scrollToRow", "index", "useEffect", "_listRef$current", "_listRef$current2", "clearAll", "measureAllRows", "_listRef$current3", "_listRef$current4", "forceUpdateGrid", "windowResizeHandler", "diff", "addEventListener", "removeEventListener", "handleScroll", "_ref2", "<PERSON><PERSON><PERSON><PERSON>", "_ref3", "key", "parent", "style", "currNote", "CellMeasurer", "concat", "Id", "columnIndex", "rowIndex", "_ref4", "measure", "_objectSpread", "paddingRight", "_listRef$current5", "clear", "recomputeRowHeights", "_resize", "Measure", "bounds", "onResize", "_ref5", "_ref6", "measureRef", "ref", "className", "List", "deferredMeasurementCache", "outline", "overscanRowCount", "rowCount", "rowHeight", "role", "displayName", "NormalList", "child", "parentRect", "getBoundingClientRect", "childRect", "top", "clientHeight", "e", "target", "map", "annotations", "onSubmit", "onClose", "_useSelector2", "useSelector", "state", "_selectors$getFeature", "selectors", "getIsMentionEnabled", "getActiveDocumentViewerKey", "getFeatureFlags", "customizableUI", "shallowEqual", "isMentionEnabled", "activeDocumentViewerKey", "pendingReply", "setPendingReply", "t", "useTranslation", "textareaRef", "focus", "replyAreaClass", "classNames", "count", "<PERSON><PERSON>", "onClick", "img", "preventDefault", "editor", "getEditor", "replyText", "mentions<PERSON>anager", "getFormattedTextFromDeltas", "getContents", "trim", "annotation", "replyAnnotation", "createMentionReply", "core", "addAnnotations", "setAnnotationRichTextStyle", "createAnnotationReply", "onMouseDown", "stopPropagation", "NoteTextarea", "value", "onChange", "handleNoteTextareaChange", "isReply", "isSubmitType", "position", "rootElemRef", "rootElem", "node", "existingParent", "querySelector", "parentElem", "rootContainer", "createElement", "setAttribute", "createRootElement", "getRootNode", "body", "insertBefore", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nextElement<PERSON><PERSON>ling", "remove", "childElementCount", "usePortal", "left", "right", "pointerEvents", "zIndex", "createPortal", "triggerElementName", "_ref$onClose", "dispatch", "useDispatch", "popupRef", "styleTabs", "setStyleTabs", "setPosition", "colorMapKey", "useOnClickOutside", "clickedTrigger", "contains", "warningModal", "getOpenedWarningModal", "colorPicker", "getOpenedColorPicker", "datePicker", "getDatePicker", "getAnnotationStyles", "freeTextAnnotation", "find", "Core", "Annotations", "FreeTextAnnotation", "getIntent", "Intent", "FreeText", "FreeTextCallout", "properties", "_richTextStyles$", "_richTextStyles$2", "_richTextStyles$3", "_richTextStyles$3$tex", "_richTextStyles$4", "_richTextStyles$4$tex", "_richTextStyles$0$tex", "_richTextStyles$5", "_richTextStyles$5$tex", "richTextStyles", "getRichTextStyle", "Font", "FontSize", "TextAlign", "TextVerticalAlign", "bold", "italic", "underline", "includes", "strikeout", "availablePalettes", "AnnotationStylePopupTabs", "TEXT_COLOR", "STROKE_COLOR", "FILL_COLOR", "updateAnnotationStylePopupTabs", "newColorMap", "copyMapWithDataProperties", "actions", "setColorMap", "_styleTabs", "getDataWithKey", "PopupPortal", "AnnotationStylePopup", "isOpen", "getOverlayPositionBasedOn", "isFreeText", "isRedaction", "isMeasure", "showLineStyleOptions", "hideSnapModeCheckbox", "showMultiReply", "setShowMultiReply", "setShowMultiState", "showMultiStyle", "setShowMultiStyle", "setMultiSelectMode", "isMultiSelectedMap", "setIsMultiSelectedMap", "multiSelectedAnnotations", "getParentAnnotations", "documentViewerKey", "arguments", "undefined", "annotSet", "Set", "isGrouped", "parentAnnotation", "getAnnotationById", "add", "Array", "from", "MultiSelectControls", "modifiableMultiSelectAnnotations", "setModifiableMultiSelectAnnotations", "isDocumentReadOnly", "onAnnotationChanged", "action", "_isMultiSelectedMap", "annot", "groupedAnnots", "getGroupAnnotations", "some", "<PERSON><PERSON><PERSON><PERSON>", "groupAnnot", "originalKeys", "Object", "keys", "updatedKeys", "filter", "_modifiableMultiSelectAnnotations", "multiSelectedAnnot", "canModify", "canGroup", "getNumberOfGroups", "canUngroup", "handleStateChange", "useCallback", "newValue", "stateAnnotation", "createStateAnnotation", "addReply", "annotationManager", "getAnnotationManager", "addAnnotation", "trigger", "getRootAnnotation", "NoteContext", "Provider", "resize", "ReplyAreaMultiSelect", "dataElement", "DataElements", "NOTE_MULTI_REPLY_BUTTON", "disabled", "title", "ToggleElementButton", "NOTE_MULTI_STATE_BUTTON", "toggleElement", "NOTE_STATE_FLYOUT", "NoteStateFlyout", "isMultiSelectMode", "NOTE_MULTI_STYLE_BUTTON", "MultiStylePopup", "NOTE_MULTI_GROUP_BUTTON", "groupAnnotations", "NOTE_MULTI_UNGROUP_BUTTON", "ungroupAnnotations", "NOTE_MULTI_DELETE_BUTTON", "warning", "message", "confirmBtnText", "onConfirm", "deleteAnnotations", "showWarningMessage", "disableFilterAnnotation", "setSearchInputHandler", "toggleMultiSelectMode", "isMultiSelectEnabled", "SORT_CONTAINER_ELEMENT", "NotesPanelHeader", "getSortStrategy", "isElementDisabled", "getNotesPanelCustomHeaderOptions", "getAnnotationFilters", "getIsOfficeEditorMode", "getOfficeEditorEditMode", "isSortContainerDisabled", "customHeaderOptions", "annotationFilters", "isOfficeEditorMode", "officeEditorEditMode", "filterEnabled", "setFilter<PERSON><PERSON>bled", "isPreviewingTrackedChanges", "setIsPreviewingTrackedChanges", "_useState6", "searchInput", "setSearchInput", "<PERSON><PERSON><PERSON><PERSON>", "colorFilter", "statusFilter", "typeFilter", "toggleFilterStyle", "_e$detail", "detail", "types", "authors", "colors", "statuses", "Events", "ANNOTATION_FILTER_CHANGED", "OFFICE_EDITOR_EDIT_MODE", "PREVIEW", "_handleInputChange", "debounce", "deselectAllAnnotations", "sortContainer", "Dropdown", "aria<PERSON><PERSON><PERSON>", "items", "getSortStrategies", "translationPrefix", "currentSelectionKey", "onClickItem", "strategy", "setNotesPanelSortStrategy", "openFilterModalWithFocusTransfer", "useFocusHandler", "openElement", "placeholderText", "originalHeaderElement", "DataElementWrapper", "NotesPanel", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "INPUT_CONTAINER", "Icon", "glyph", "type", "placeholder", "COMMENTS_COUNTER", "SORT_ROW", "NOTE_MULTI_SELECT_MODE_BUTTON", "active", "FILTER_ANNOTATION_BUTTON", "CustomElement", "render", "renderArguments", "overwriteDefaultHeader", "currentLeftPanelWidth", "selectedNoteIds", "setSelectedNoteIds", "scrollToSelectedAnnot", "setScrollToSelectedAnnot", "isCustomPanel", "isCustomPanelOpen", "isLeftSide", "parentDataElement", "isElementOpen", "NOTES_PANEL", "isDisabled", "pageLabe<PERSON>", "getPageLabels", "customNoteFilter", "getCustomNoteFilter", "currentNotesPanelWidth", "getPanel<PERSON>idth", "getNotesPanelWidth", "notesInLeftPanel", "getNotesInLeftPanel", "showAnnotationNumbering", "isAnnotationNumberingEnabled", "enableNotesPanelVirtualizedList", "getEnableNotesPanelVirtualizedList", "isInDesktopOnlyMode", "customEmptyPanel", "getNotesPanelCustomEmptyPanel", "isNotesPanelMultiSelectEnabled", "getIsNotesPanelMultiSelectEnabled", "currentWidth", "isMobile", "isMobileSize", "setMultiSelectedAnnotations", "showMultiState", "_useState8", "_useState10", "curAnnotId", "setCurAnnotId", "scrollTopRef", "VIRTUALIZATION_THRESHOLD", "isIE", "Infinity", "onAnnotationNumberingUpdated", "isEnabled", "setAnnotationNumbering", "onAnnotationSelected", "singleSelectedNoteIndex", "closeElement", "filterNotesWithSearch", "note", "<PERSON><PERSON><PERSON>", "getDisplayAuthor", "annotationPreview", "getCustomData", "toLowerCase", "notesToRender", "getSortedNotes", "shouldRender", "replies", "getReplies", "noteAndReplies", "_toConsumableArray", "setTimeout", "onlyReplyContainsSearchInput", "_useState12", "pendingEditTextMap", "setPendingEditTextMap", "setPendingEditText", "pendingText", "annotationID", "_defineProperty", "_useState14", "pendingReplyMap", "setPendingReplyMap", "_useState16", "pendingAttachmentMap", "setPendingAttachmentMap", "addAttachments", "attachments", "clearAttachments", "deleteAttachment", "attachment", "attachmentList", "indexOf", "splice", "values", "<PERSON><PERSON><PERSON><PERSON>", "listSeparator", "_getSortStrategies$so", "shouldRenderSeparator", "getSeparator<PERSON><PERSON>nt", "prevNote", "ListSeparator", "renderContent", "contextValue", "isSelected", "isContentEditable", "canModifyContents", "onTopNoteContentClicked", "currIds", "clone", "deselectAnnotation", "isExpandedFromSearch", "Note", "shouldHideConnectorLine", "isMultiSelected", "isInNotesPanel", "handleMultiSelect", "checked", "groupAnnots", "selectAnnotations", "deselectAnnotations", "NoResults", "NoAnnotationsGlyph", "icon", "NoAnnotationsMessage", "NoAnnotationsReadOnlyMessage", "readOnlyMessage", "shouldRenderNoAnnotationsIcon", "hideIcon", "shouldRenderCustomEmptyPanel", "NoAnnotations", "MultiSelectPlaceHolder", "MultiReplyPlaceHolder", "ids", "findIndex", "min<PERSON><PERSON><PERSON>", "Panel", "onMouseUp", "closeElements", "Choice", "isSwitch", "label", "getOfficeEditor", "setEditMode", "REVIEWING", "ReplyAttachmentPicker", "annotationId", "props", "_props$parentDataElem", "isMultiViewerMode", "noteMap", "setNoteMap", "setNotes", "selectedNoteIdsMap", "setSelectedNoteIdsMap", "isMultiSelectedViewerMap", "setIsMultiSelectedViewerMap", "getGroupedAnnots", "selectedAnnotations", "mainAnn<PERSON>", "onDocumentUnloaded", "_setNotes", "getSelectedAnnotations", "getAnnotationsList", "Listable", "Hidden", "ToolName", "Tools", "ToolNames", "CROP", "isContentEditPlaceholder", "mapAnnotationToKey", "annotationMapKeys", "TRACKED_CHANGE", "setDocumentUnloaded1", "setDocumentUnloaded2", "setNotes2", "setNotes1", "onAnnotationSelected2", "shouldDisplayMultiSelect", "<PERSON><PERSON><PERSON><PERSON>", "a", "onAnnotationSelected1", "passProps", "_extends"], "sourceRoot": ""}