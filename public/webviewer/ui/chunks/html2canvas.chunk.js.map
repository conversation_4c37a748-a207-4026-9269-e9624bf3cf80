{"version": 3, "file": "chunks/html2canvas.chunk.js", "mappings": "2GAEAA,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQE,eAAiBF,EAAQG,mBAAqBH,EAAQI,gBAAkBJ,EAAQK,yBAAsBC,EAE9G,IAAIC,EAAc,EAAQ,OAEtBF,EAAsBL,EAAQK,oBAAsB,CACpDG,OAAQ,EACRC,QAAS,GAGTL,EAAkBJ,EAAQI,gBAAkB,CAC5CM,MAAO,EACPC,KAAM,EACNC,OAAQ,EACRC,OAAQ,EACRC,QAAS,EACTC,YAAa,EACbC,qBAAsB,EACtBC,YAAa,EACbC,YAAa,EACbC,YAAa,EACbC,YAAa,EACbC,YAAa,GACbC,aAAc,GACdC,SAAU,GACVC,QAAS,GACTC,UAAW,GACXC,mBAAoB,GACpBC,kBAAmB,GACnBC,gBAAiB,GACjBC,WAAY,GACZC,iBAAkB,GAClBC,SAAU,GACVC,SAAU,GACVC,SAAU,GACVC,OAAQ,GACRC,SAAU,GACVC,eAAgB,GAChBC,gBAAiB,GACjBC,kBAAmB,GACnBC,QAAS,GACTC,SAAU,GACVC,eAAgB,GAChBC,MAAO,GACPC,qBAAsB,GACtBC,oBAAqB,GACrBC,sBAAuB,GACvBC,IAAK,GACLC,eAAgB,GAChBC,UAAW,GACXC,UAAW,GACXC,QAAS,GACTC,MAAO,GACPC,QAAS,GACTC,oBAAqB,GACrBC,sBAAuB,GACvBC,MAAO,GACPC,OAAQ,GACRC,KAAM,GACNC,QAAS,GACTC,oBAAqB,GACrBC,sBAAuB,GACvBC,eAAgB,GAChBC,gBAAiB,GACjBC,kBAAmB,IAGnB5D,EAAqBH,EAAQG,mBAAqB,SAA4B6D,GAC9E,OAAQA,GACJ,IAAK,OACD,OAAO5D,EAAgBO,KAC3B,IAAK,SACD,OAAOP,EAAgBQ,OAC3B,IAAK,SACD,OAAOR,EAAgBS,OAC3B,IAAK,UACD,OAAOT,EAAgBU,QAC3B,IAAK,cACD,OAAOV,EAAgBW,YAC3B,IAAK,uBACD,OAAOX,EAAgBY,qBAC3B,IAAK,cACD,OAAOZ,EAAgBa,YAC3B,IAAK,cACD,OAAOb,EAAgBc,YAC3B,IAAK,cACD,OAAOd,EAAgBe,YAC3B,IAAK,cACD,OAAOf,EAAgBgB,YAC3B,IAAK,cACD,OAAOhB,EAAgBiB,YAC3B,IAAK,eACD,OAAOjB,EAAgBkB,aAC3B,IAAK,WACD,OAAOlB,EAAgBmB,SAC3B,IAAK,UACD,OAAOnB,EAAgBoB,QAC3B,IAAK,YACD,OAAOpB,EAAgBqB,UAC3B,IAAK,qBACD,OAAOrB,EAAgBsB,mBAC3B,IAAK,oBACD,OAAOtB,EAAgBuB,kBAC3B,IAAK,kBACD,OAAOvB,EAAgBwB,gBAC3B,IAAK,aACD,OAAOxB,EAAgByB,WAC3B,IAAK,mBACD,OAAOzB,EAAgB0B,iBAC3B,IAAK,WACD,OAAO1B,EAAgB2B,SAC3B,IAAK,WACD,OAAO3B,EAAgB4B,SAC3B,IAAK,WACD,OAAO5B,EAAgB6B,SAC3B,IAAK,SACD,OAAO7B,EAAgB8B,OAC3B,IAAK,WACD,OAAO9B,EAAgB+B,SAC3B,IAAK,iBACD,OAAO/B,EAAgBgC,eAC3B,IAAK,kBACD,OAAOhC,EAAgBiC,gBAC3B,IAAK,oBACD,OAAOjC,EAAgBkC,kBAC3B,IAAK,UACD,OAAOlC,EAAgBmC,QAC3B,IAAK,WACD,OAAOnC,EAAgBoC,SAC3B,IAAK,iBACD,OAAOpC,EAAgBqC,eAC3B,IAAK,QACD,OAAOrC,EAAgBsC,MAC3B,IAAK,uBACD,OAAOtC,EAAgBuC,qBAC3B,IAAK,sBACD,OAAOvC,EAAgBwC,oBAC3B,IAAK,wBACD,OAAOxC,EAAgByC,sBAC3B,IAAK,MACD,OAAOzC,EAAgB0C,IAC3B,IAAK,iBACD,OAAO1C,EAAgB2C,eAC3B,IAAK,YACD,OAAO3C,EAAgB4C,UAC3B,IAAK,YACD,OAAO5C,EAAgB6C,UAC3B,IAAK,UACD,OAAO7C,EAAgB8C,QAC3B,IAAK,QACD,OAAO9C,EAAgB+C,MAC3B,IAAK,UACD,OAAO/C,EAAgBgD,QAC3B,IAAK,sBACD,OAAOhD,EAAgBiD,oBAC3B,IAAK,wBACD,OAAOjD,EAAgBkD,sBAC3B,IAAK,QACD,OAAOlD,EAAgBmD,MAC3B,IAAK,SACD,OAAOnD,EAAgBoD,OAC3B,IAAK,OACD,OAAOpD,EAAgBqD,KAC3B,IAAK,UACD,OAAOrD,EAAgBsD,QAC3B,IAAK,sBACD,OAAOtD,EAAgBuD,oBAC3B,IAAK,wBACD,OAAOvD,EAAgBwD,sBAC3B,IAAK,iBACD,OAAOxD,EAAgByD,eAC3B,IAAK,kBACD,OAAOzD,EAAgB0D,gBAC3B,IAAK,oBACD,OAAO1D,EAAgB2D,kBAE3B,QACI,OAAO3D,EAAgBM,KAEnC,EAWIuD,GATiBjE,EAAQE,eAAiB,SAAwBgE,GAClE,IAAIC,GAAiB,EAAI5D,EAAY6D,sBAAsBF,EAAMG,iBAAiB,qBAClF,MAAO,CACHC,cAAenE,EAAmB+D,EAAMG,iBAAiB,oBACzDF,eAAgBA,EAAeI,OAASJ,EAAe,GAAK,KAC5DK,kBAAmBP,EAAuBC,EAAMG,iBAAiB,wBAEzE,EAE6B,SAAgCI,GACzD,MACS,WADDA,EAEOpE,EAAoBG,OAGpBH,EAAoBI,OAEvC,E,iBCxMAX,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQ0E,WAAa1E,EAAQ2E,cAAgB3E,EAAQ4E,kBAAetE,EAEpE,IAAIuE,EAAgB,EAAQ,OAE5B/E,OAAOC,eAAeC,EAAS,eAA/B,CACI8E,YAAY,EACZC,IAAK,WACD,OAAOF,EAAcD,YACzB,IAEJ9E,OAAOC,eAAeC,EAAS,gBAA/B,CACI8E,YAAY,EACZC,IAAK,WACD,OAAOF,EAAcF,aACzB,IAGJ,IAMgCK,EAF5BC,IAE4BD,EANX,EAAQ,SAMwBA,EAAIE,WAFrC,EAAQ,OAIXlF,EAAQ0E,WAAa,SAAoBS,EAAKC,GAS3D,IARA,IAAIC,GAAU,EAAIR,EAAcS,aAAaH,EAAK,CAC9CI,UAAWH,EAAOlB,MAAMqB,UACxBC,UAAWJ,EAAOlB,MAAMuB,eAAiBR,EAAcS,cAAcC,WAAa,aAAeP,EAAOlB,MAAMsB,YAG9GI,EAAQ,GACRC,OAAK,IAEAA,EAAKR,EAAQS,QAAQC,MAC1BH,EAAMI,KAAKH,EAAG5F,MAAMgG,SAGxB,OAAOL,CACX,C,iBC1CA9F,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQkG,KAAOlG,EAAQmG,qBAAuBnG,EAAQoG,oBAAsBpG,EAAQqG,4BAA8BrG,EAAQsG,kCAAoCtG,EAAQuG,sBAAwBvG,EAAQwG,8BAAgCxG,EAAQyG,8BAAgCzG,EAAQ0G,0BAA4B1G,EAAQ2G,2BAA6B3G,EAAQ4G,iBAAmB5G,EAAQ6G,yBAA2B7G,EAAQ8G,2BAA6B9G,EAAQ+G,iBAAmB/G,EAAQgH,mBAAqBhH,EAAQiH,eAAiBjH,EAAQkH,oBAAiB5G,EAEpiB,IAAI6G,EAAe,WAAc,SAASC,EAAiBC,EAAQC,GAAS,IAAK,IAAIC,EAAI,EAAGA,EAAID,EAAM/C,OAAQgD,IAAK,CAAE,IAAIC,EAAaF,EAAMC,GAAIC,EAAW1C,WAAa0C,EAAW1C,aAAc,EAAO0C,EAAWC,cAAe,EAAU,UAAWD,IAAYA,EAAWE,UAAW,GAAM5H,OAAOC,eAAesH,EAAQG,EAAWG,IAAKH,EAAa,CAAE,CAAE,OAAO,SAAUI,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYT,EAAiBQ,EAAYG,UAAWF,GAAiBC,GAAaV,EAAiBQ,EAAaE,GAAqBF,CAAa,CAAG,CAA7hB,GAEfI,EAAQ,EAAQ,OAKhBd,EAAiBlH,EAAQkH,eAAiB,EAG1CD,EAAiBjH,EAAQiH,eAAiB,GAQ1CD,EAAqBhH,EAAQgH,mBAAqB,EAMlDD,EAAmB/G,EAAQ+G,iBAAmBE,EAAiBC,EAQ/DJ,EAA6B9G,EAAQ8G,2BAA6B,OAAWI,EAG7EL,EAA2B7G,EAAQ6G,yBAA2B,GAAKK,EAEnEN,EAAmB5G,EAAQ4G,iBAAmBC,EAA2B,EAEzEF,EAA6B3G,EAAQ2G,2BAA6B,MAASO,EAE3ER,EAA4B1G,EAAQ0G,0BAA4BI,EAA6BH,EAK7FF,EAAgCzG,EAAQyG,8BAAgCC,EACxEF,EAAgCxG,EAAQwG,8BAAgC,GAaxED,EAAwBvG,EAAQuG,sBAAwBE,EAAgCD,EAMxFF,EAAoCtG,EAAQsG,kCAAoC,OAAWW,EAG3FZ,EAA8BrG,EAAQqG,4BAA8B,GAAKU,EAEzEX,EAAsBpG,EAAQoG,oBAAsBC,EAA8B,EAclFH,GAZuBlG,EAAQmG,qBAAuB,SAA8B8B,GACpF,IAAIC,GAAS,EAAIF,EAAMG,QAAQF,GAC3BG,EAASC,MAAMC,QAAQJ,IAAU,EAAIF,EAAMO,iBAAiBL,GAAU,IAAIM,YAAYN,GACtFO,EAASJ,MAAMC,QAAQJ,IAAU,EAAIF,EAAMU,iBAAiBR,GAAU,IAAIS,YAAYT,GAGtFU,EAAQH,EAAOxC,MAAM4C,GAAkBT,EAAO,GAAK,GACnDU,EAAqB,IAAdV,EAAO,GAAWK,EAAOxC,OAHjB,GAGuCmC,EAAO,IAAM,GAAKA,EAAOnC,MAAM8C,KAAKC,MAH3E,GAGgGZ,EAAO,IAAM,IAEhI,OAAO,IAAIlC,EAAKkC,EAAO,GAAIA,EAAO,GAAIA,EAAO,GAAIA,EAAO,GAAIQ,EAAOE,EACvE,EAEW9I,EAAQkG,KAAO,WACtB,SAASA,EAAK+C,EAAcC,EAAYC,EAAWC,EAAgBR,EAAOE,IAlF9E,SAAyBO,EAAUzB,GAAe,KAAMyB,aAAoBzB,GAAgB,MAAM,IAAI0B,UAAU,oCAAwC,CAmFhJC,CAAgBC,KAAMtD,GAEtBsD,KAAKP,aAAeA,EACpBO,KAAKN,WAAaA,EAClBM,KAAKL,UAAYA,EACjBK,KAAKJ,eAAiBA,EACtBI,KAAKZ,MAAQA,EACbY,KAAKV,KAAOA,CAChB,CAuDA,OA7CA3B,EAAajB,EAAM,CAAC,CAChByB,IAAK,MACL1H,MAAO,SAAawJ,GAChB,IAAIC,OAAK,EACT,GAAID,GAAa,EAAG,CAChB,GAAIA,EAAY,OAAWA,EAAY,OAAWA,GAAa,MAM3D,OADAC,IADAA,EAAKF,KAAKZ,MAAMa,GAAavC,KACjBF,IAAuByC,EAAY7C,GACxC4C,KAAKV,KAAKY,GAGrB,GAAID,GAAa,MASb,OADAC,IADAA,EAAKF,KAAKZ,MAAM9B,GAA8B2C,EAAY,OAAUvC,MACxDF,IAAuByC,EAAY7C,GACxC4C,KAAKV,KAAKY,GAGrB,GAAID,EAAYD,KAAKL,UAOjB,OALAO,EAAKnD,EAAwBD,GAAqCmD,GAAaxC,GAC/EyC,EAAKF,KAAKZ,MAAMc,GAChBA,GAAMD,GAAavC,EAAiBd,EAEpCsD,IADAA,EAAKF,KAAKZ,MAAMc,KACJ1C,IAAuByC,EAAY7C,GACxC4C,KAAKV,KAAKY,GAErB,GAAID,GAAa,QACb,OAAOD,KAAKV,KAAKU,KAAKJ,eAE9B,CAGA,OAAOI,KAAKN,UAChB,KAGGhD,CACX,CAlE0B,G,eC1F1BpG,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEX,IAAIyF,EAAgB1F,EAAQ0F,cAAgB,CACxCiE,OAAQ,EACRhE,WAAY,GAGQ3F,EAAQ4J,kBAAoB,SAA2BC,GAC3E,MACS,eADDA,EAEOnE,EAAcC,WAGdD,EAAciE,MAEjC,C,iBChBA7J,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQ8J,YAAc9J,EAAQ+J,oBAAiBzJ,EAE/C,IAEI6G,EAAe,WAAc,SAASC,EAAiBC,EAAQC,GAAS,IAAK,IAAIC,EAAI,EAAGA,EAAID,EAAM/C,OAAQgD,IAAK,CAAE,IAAIC,EAAaF,EAAMC,GAAIC,EAAW1C,WAAa0C,EAAW1C,aAAc,EAAO0C,EAAWC,cAAe,EAAU,UAAWD,IAAYA,EAAWE,UAAW,GAAM5H,OAAOC,eAAesH,EAAQG,EAAWG,IAAKH,EAAa,CAAE,CAAE,OAAO,SAAUI,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYT,EAAiBQ,EAAYG,UAAWF,GAAiBC,GAAaV,EAAiBQ,EAAaE,GAAqBF,CAAa,CAAG,CAA7hB,GAEfoC,EAAU,EAAQ,OAElBC,EAAS,EAAQ,OAIjBC,EAAmBC,EAFD,EAAQ,QAI1BnC,EAAQ,EAAQ,OAEhBzH,EAAc,EAAQ,OAItB6J,EAAmBD,EAFD,EAAQ,QAI1BE,EAAqB,EAAQ,OAEjC,SAASF,EAAuBnF,GAAO,OAAOA,GAAOA,EAAIE,WAAaF,EAAM,CAAEsF,QAAStF,EAAO,CAI9F,IAAIuF,EAAmB,0BAEnBR,EAAiB/J,EAAQ+J,eAAiB,WAC1C,SAASA,EAAeS,EAASC,EAASC,EAAQC,EAAYC,IALlE,SAAyBvB,EAAUzB,GAAe,KAAMyB,aAAoBzB,GAAgB,MAAM,IAAI0B,UAAU,oCAAwC,CAMhJC,CAAgBC,KAAMO,GAEtBP,KAAKqB,iBAAmBL,EACxBhB,KAAKsB,iBAAmB,GACxBtB,KAAKuB,WAAaJ,EAClBnB,KAAKwB,aAAeL,EACpBnB,KAAKkB,OAASA,EACdlB,KAAKiB,QAAUA,EACfjB,KAAKoB,SAAWA,EAChBpB,KAAKyB,eAAiB,IAAIf,EAAiBI,QAAQG,EAASC,EAAQQ,QACpE1B,KAAK2B,kBAAoB,CACrBC,SAAU,CAAC,EACXC,WAAY,GAGhB7B,KAAK8B,gBAAkB9B,KAAK+B,UAAUf,EAAQgB,cAAcF,gBAChE,CAgPA,OA9OAnE,EAAa4C,EAAgB,CAAC,CAC1BpC,IAAK,kBACL1H,MAAO,SAAyBwL,GAC5B,IAAIC,EAAQlC,KAEZ,GAAIA,KAAKwB,cAAgBS,EAAM,CAC3B,IAAIvH,EAAQuH,EAAKvH,MACjByH,QAAQC,KAAI,EAAIrL,EAAY6D,sBAAsBF,EAAM2H,iBAAiBC,KAAI,SAAUD,GACnF,MAA+B,QAA3BA,EAAgBE,OACTL,EAAMT,eAAee,YAAYH,EAAgBI,KAAK,IAAIC,MAAK,SAAUC,GAC5E,OAAOA,GAA0B,iBAAZA,EAAIC,IAAmB,QAAUD,EAAIC,IAAM,KAAO,MAC3E,IAAGC,OAAM,SAAUC,GAInB,IAEGX,QAAQY,QAAQ,GAAKV,EAAgBW,OAASX,EAAgBE,OAAS,IAAMF,EAAgBI,KAAKQ,KAAK,KAAO,IACzH,KAAIP,MAAK,SAAUQ,GACXA,EAAiBnI,OAAS,IAE1BL,EAAMyI,gBAAkB,IAE5BzI,EAAM2H,gBAAkBa,EAAiBD,KAAK,IAClD,IAEIhB,aAAgBmB,kBAChBpD,KAAKyB,eAAee,YAAYP,EAAKW,KAAKF,MAAK,SAAUC,GACrD,GAAIA,GAAOV,aAAgBmB,kBAAoBnB,EAAKoB,WAAY,CAC5D,IAAIA,EAAapB,EAAKoB,WAClBC,GAAc,EAAI9E,EAAM+E,eAAetB,EAAKvH,MAAOiI,EAAIZ,WAAU,IACrEsB,EAAWG,aAAaF,EAAarB,EACzC,CACJ,IAAGY,OAAM,SAAUC,GAInB,GAER,CACJ,GACD,CACC3E,IAAK,cACL1H,MAAO,SAAqBgN,GACxB,IAAIC,EAAS1D,KAEb,OAAOmC,QAAQC,IAAIvD,MAAM8E,KAAKF,EAASG,aAAatB,KAAI,SAAUuB,GAC9D,OAAIA,EAAMC,KACCC,MAAMF,EAAMC,MAAMpB,MAAK,SAAUsB,GACpC,OAAOA,EAAIC,MACf,IAAGvB,MAAK,SAAUuB,GACd,OAAOC,EAA8BD,EAAMJ,EAAMC,KACrD,IAAGjB,OAAM,SAAUC,GAIf,MAAO,EACX,IAEGqB,EAAcN,EAAOJ,EAChC,KAAIf,MAAK,SAAU0B,GACf,OAAOA,EAAMC,QAAO,SAAUC,EAAKC,GAC/B,OAAOD,EAAIE,OAAOD,EACtB,GAAG,GACP,IAAG7B,MAAK,SAAU0B,GACd,OAAOjC,QAAQC,IAAIgC,EAAM9B,KAAI,SAAUiC,GACnC,OAAOR,MAAMQ,EAAKE,QAAQ,GAAG7B,KAAKF,MAAK,SAAUgC,GAC7C,OAAOA,EAASC,MACpB,IAAGjC,MAAK,SAAUiC,GACd,OAAO,IAAIxC,SAAQ,SAAUY,EAAS6B,GAClC,IAAIC,EAAS,IAAIC,WACjBD,EAAOE,QAAUH,EACjBC,EAAOG,OAAS,WAEZ,IAAIC,EAASJ,EAAOI,OACpBlC,EAAQkC,EACZ,EACAJ,EAAOK,cAAcP,EACzB,GACJ,IAAGjC,MAAK,SAAUyC,GAEd,OADAZ,EAAKa,SAASC,YAAY,MAAO,QAAUF,EAAU,MAC9C,eAAiBZ,EAAKa,SAASE,QAAU,GACpD,GACJ,IACJ,IAAG5C,MAAK,SAAU6C,GACd,IAAI7K,EAAQ+I,EAAS+B,cAAc,SACnC9K,EAAM+K,YAAcF,EAAQtC,KAAK,MACjCS,EAAO5B,gBAAgB4D,YAAYhL,EACvC,GACJ,GACD,CACCyD,IAAK,qBACL1H,MAAO,SAA4BwL,GAC/B,IAAI0D,EAAS3F,KAEb,GAAIA,KAAKuB,YAAcU,aAAgB2D,kBAAmB,CACtD,IAAIjD,EAAMV,EAAKD,cAAcwD,cAAc,OAC3C,IAEI,OADA7C,EAAIC,IAAMX,EAAK4D,YACRlD,CACX,CAAE,MAAOG,GAIT,CACJ,CAEA,GAAIb,aAAgB6D,kBAAmB,CACnC,IAAIC,EAAa9D,EAAKF,WAAU,GAC5BiE,EAAYC,IAChBF,EAAWG,aAAa,uCAAwCF,GAEhE,IAAIG,GAAe,EAAI3F,EAAQ4F,aAAanE,EAAM,EAAG,GACjDoE,EAAQF,EAAaE,MACrBC,EAASH,EAAaG,OAsC1B,OApCAtG,KAAKyB,eAAe8E,MAAMP,GAAaQ,EAAyBvE,EAAMjC,KAAKiB,SAASyB,MAAK,SAAUZ,GAC/F,OAAO6D,EAAOvE,SAASU,EAAiB,CACpC2E,MAAOd,EAAO1E,QAAQwF,MACtBC,WAAYf,EAAO1E,QAAQyF,WAC3BvD,gBAAiB,UACjBwD,OAAQ,KACRC,aAAcjB,EAAO1E,QAAQ2F,aAC7BC,QAASlB,EAAO1E,QAAQ4F,QACxBC,MAAOnB,EAAO1E,QAAQ6F,MACtBC,gBAAiBpB,EAAO1E,QAAQ8F,gBAChCC,MAAOrB,EAAO1E,QAAQ+F,MACtBC,uBAAwBtB,EAAO1E,QAAQgG,uBACvCC,QAASvB,EAAO1E,QAAQiG,QACxBrJ,OAAQ,IAAI+C,EAAiBE,QAC7BuF,MAAOA,EACPC,OAAQA,EACRa,EAAG,EACHC,EAAG,EACHC,YAAavF,EAAgBE,cAAcsF,YAAYC,WACvDC,aAAc1F,EAAgBE,cAAcsF,YAAYG,YACxDC,QAAS5F,EAAgBE,cAAcsF,YAAYK,YACnDC,QAAS9F,EAAgBE,cAAcsF,YAAYO,aACpDlC,EAAOzE,OAAO4G,MAAM9B,GAC3B,IAAGtD,MAAK,SAAUiE,GACd,OAAO,IAAIxE,SAAQ,SAAUY,EAAS6B,GAClC,IAAImD,EAAetE,SAAS+B,cAAc,OAC1CuC,EAAa/C,OAAS,WAClB,OAAOjC,EAAQ4D,EACnB,EACAoB,EAAahD,QAAUH,EACvBmD,EAAanF,IAAM+D,EAAOd,YACtBE,EAAW1C,YACX0C,EAAW1C,WAAWG,cAAa,EAAIhF,EAAM+E,eAAetB,EAAKD,cAAcsF,YAAYU,iBAAiB/F,GAAO8F,GAAehC,EAE1I,GACJ,IACOA,CACX,CAEA,GAAI9D,aAAgBgG,kBAAoBhG,EAAK4B,OAAS5B,EAAK4B,MAAMqE,SAAU,CACvE,IAAIC,EAAM,GAAG1L,MAAM2L,KAAKnG,EAAK4B,MAAMqE,SAAU,GAAG7D,QAAO,SAAU8D,EAAKE,GAClE,OAAOF,EAAME,EAAK/C,OACtB,GAAG,IACC5K,EAAQuH,EAAKF,WAAU,GAE3B,OADArH,EAAM+K,YAAc0C,EACbzN,CACX,CAEA,OAAOuH,EAAKF,WAAU,EAC1B,GACD,CACC5D,IAAK,YACL1H,MAAO,SAAmBwL,GACtB,IAAIqG,EAAQrG,EAAKsG,WAAaC,KAAKC,UAAYhF,SAASiF,eAAezG,EAAK0G,WAAa3I,KAAK4I,mBAAmB3G,GAE7GP,EAASO,EAAKD,cAAcsF,YAC5B5M,EAAQuH,aAAgBP,EAAOmH,YAAcnH,EAAOsG,iBAAiB/F,GAAQ,KAC7E6G,EAAc7G,aAAgBP,EAAOmH,YAAcnH,EAAOsG,iBAAiB/F,EAAM,WAAa,KAC9F8G,EAAa9G,aAAgBP,EAAOmH,YAAcnH,EAAOsG,iBAAiB/F,EAAM,UAAY,KAE5FjC,KAAKqB,mBAAqBY,GAAQqG,aAAiB5G,EAAOmH,cAC1D7I,KAAKgJ,uBAAyBV,GAG9BA,aAAiB5G,EAAOuH,iBACxBC,EAAuBZ,GAM3B,IAHA,IAAI1G,GAAW,EAAIf,EAAmBsI,mBAAmBzO,EAAOsF,KAAK2B,mBACjEyH,GAAgB,EAAIvI,EAAmBwI,sBAAsBpH,EAAM6G,EAAa9I,KAAK2B,mBAEhFmG,EAAQ7F,EAAKqH,WAAYxB,EAAOA,EAAQA,EAAMyB,YAC/CzB,EAAMS,WAAaC,KAAKgB,eAAmC,WAAnB1B,EAAM2B,UAEjD3B,EAAM4B,aAAa3I,IAA6D,mBAAhCf,KAAKiB,QAAQ0I,gBAE7D3J,KAAKiB,QAAQ0I,eAAe7B,KACpB9H,KAAKuB,YAAiC,UAAnBuG,EAAM2B,UAC1BnB,EAAM5C,YAAY1F,KAAK+B,UAAU+F,IAK7C,IAAI8B,GAAe,EAAI/I,EAAmBwI,sBAAsBpH,EAAM8G,EAAY/I,KAAK2B,mBAGvF,IAFA,EAAId,EAAmBgJ,aAAajI,EAAU5B,KAAK2B,mBAE/CM,aAAgBP,EAAOmH,aAAeP,aAAiB5G,EAAOmH,YAc9D,OAbIC,GACA9I,KAAK8J,gBAAgBC,EAAoB9H,EAAMqG,EAAOQ,EAAaM,EAAeY,IAElFjB,GACA/I,KAAK8J,gBAAgBC,EAAoB9H,EAAMqG,EAAOS,EAAYa,EAAcK,KAEhFvP,IAASsF,KAAKuB,YAAgBU,aAAgB6D,oBAC9C,EAAItH,EAAM+E,eAAe7I,EAAO4N,GAEpCtI,KAAK8J,gBAAgBxB,GACE,IAAnBrG,EAAKiI,WAAuC,IAApBjI,EAAKkI,YAC7BnK,KAAKsB,iBAAiB9E,KAAK,CAAC8L,EAAOrG,EAAKkI,WAAYlI,EAAKiI,YAErDjI,EAAKwH,UACT,IAAK,SACIzJ,KAAKuB,YACN6I,EAAoBnI,EAAMqG,GAE9B,MACJ,IAAK,WACL,IAAK,SACDA,EAAM7R,MAAQwL,EAAKxL,MACnB,MACJ,IAAK,QACGwL,EAAKoI,SAEL/B,EAAMpC,aAAa,WAAW,GAK9C,OAAOoC,CACX,KAGG/H,CACX,CAnQ8C,GAqQ1C4D,EAAgB,SAAuBN,EAAOJ,GAE9C,OAAQI,EAAMqE,SAAWrJ,MAAM8E,KAAKE,EAAMqE,UAAY,IAAIoC,QAAO,SAAUjC,GACvE,OAAOA,EAAK7N,OAAS+P,QAAQC,cACjC,IAAGlI,KAAI,SAAU+F,GAGb,IAFA,IAAIzF,GAAM,EAAI7L,EAAY6D,sBAAsByN,EAAK3N,MAAMG,iBAAiB,QACxE4J,EAAU,GACL1G,EAAI,EAAGA,EAAI6E,EAAI7H,OAAQgD,IAC5B,GAAsB,QAAlB6E,EAAI7E,GAAGwE,QAAoBK,EAAI7E,EAAI,IAA4B,WAAtB6E,EAAI7E,EAAI,GAAGwE,OAAqB,CACzE,IAAIkI,EAAIhH,EAAS+B,cAAc,KAC/BiF,EAAE3G,KAAOlB,EAAI7E,GAAG0E,KAAK,GACjBgB,EAASiH,MACTjH,EAASiH,KAAKhF,YAAY+E,GAG9B,IAAIlG,EAAO,CACP3B,IAAK6H,EAAE3G,KACP6G,OAAQ/H,EAAI7E,EAAI,GAAG0E,KAAK,IAE5BgC,EAAQjI,KAAK+H,EACjB,CAGJ,MAAO,CAGHE,QAASA,EAAQ6F,QAAO,SAAU/F,GAC9B,MAAQ,SAASqG,KAAKrG,EAAKoG,OAE/B,IACAvF,SAAUiD,EAAK3N,MAEvB,IAAG4P,QAAO,SAAU/F,GAChB,OAAOA,EAAKE,QAAQ1J,MACxB,GACJ,EAEImJ,EAAgC,SAAuCD,EAAM4G,GAC7E,IAAIC,EAAMrH,SAASsH,eAAeC,mBAAmB,IACjDC,EAAOxH,SAAS+B,cAAc,QAElCyF,EAAKnH,KAAO+G,EACZ,IAAInQ,EAAQ+I,SAAS+B,cAAc,SAUnC,OARA9K,EAAM+K,YAAcxB,EAChB6G,EAAII,MACJJ,EAAII,KAAKxF,YAAYuF,GAErBH,EAAIJ,MACJI,EAAIJ,KAAKhF,YAAYhL,GAGlBA,EAAMmJ,MAAQM,EAAczJ,EAAMmJ,MAAOiH,GAAO,EAC3D,EAQIV,EAAsB,SAA6BzD,EAAQwE,GAC3D,IACI,GAAIA,EAAc,CACdA,EAAa9E,MAAQM,EAAON,MAC5B8E,EAAa7E,OAASK,EAAOL,OAC7B,IAAI8E,EAAMzE,EAAO0E,WAAW,MACxBC,EAAYH,EAAaE,WAAW,MACpCD,EACAE,EAAUC,aAAaH,EAAII,aAAa,EAAG,EAAG7E,EAAON,MAAOM,EAAOL,QAAS,EAAG,GAE/EgF,EAAUG,UAAU9E,EAAQ,EAAG,EAEvC,CACJ,CAAE,MAAO7D,GAAI,CACjB,EAEIiH,EAAsB,SAA6B9H,EAAMqG,EAAO5N,EAAOgR,EAAcC,GACrF,GAAKjR,GAAUA,EAAMkR,SAA6B,SAAlBlR,EAAMkR,SAAwC,qBAAlBlR,EAAMkR,SAAoD,SAAlBlR,EAAMmR,QAA1G,CAIA,IAAIC,EAA2BxD,EAAMtG,cAAcwD,cAAc,4BAGjE,IAFA,EAAIhH,EAAM+E,eAAe7I,EAAOoR,GAE5BJ,EAEA,IADA,IAAIK,EAAML,EAAa3Q,OACdgD,EAAI,EAAGA,EAAIgO,EAAKhO,IAAK,CAC1B,IAAIiO,EAAON,EAAa3N,GACxB,OAAQiO,EAAKxR,MACT,KAAKqG,EAAmBoL,yBAAyBC,MAC7C,IAAIvJ,EAAM2F,EAAMtG,cAAcwD,cAAc,OAC5C7C,EAAIC,KAAM,EAAI7L,EAAY6D,sBAAsB,OAASoR,EAAKvV,MAAQ,KAAK,GAAGgM,KAAK,GACnFE,EAAIjI,MAAMyR,QAAU,IACpBL,EAAyBpG,YAAY/C,GACrC,MACJ,KAAK9B,EAAmBoL,yBAAyBG,KAC7CN,EAAyBpG,YAAY4C,EAAMtG,cAAc0G,eAAesD,EAAKvV,QAGzF,CAWJ,OARAqV,EAAyBO,UAAYC,EAAmC,IAAMC,EAC9EjE,EAAM+D,WAAaV,IAAc3B,EAAgB,IAAMsC,EAAmC,IAAMC,EAC5FZ,IAAc3B,EACd1B,EAAMkE,aAAaV,EAA0BxD,EAAMgB,YAEnDhB,EAAM5C,YAAYoG,GAGfA,CA/BP,CAgCJ,EAGI9B,EAAgB,UAChBC,EAAe,SACfqC,EAAmC,wCACnCC,EAAkC,uCAElCE,EAA4B,mEAE5BvD,EAAyB,SAAgCwB,GACzDgC,EAAahC,EAAM,IAAM4B,EAAmCtC,EAAgByC,EAA4B,eAAiBF,EAAkCtC,EAAewC,EAC9K,EAEIC,EAAe,SAAsBhC,EAAMiC,GAC3C,IAAIjS,EAAQgQ,EAAK1I,cAAcwD,cAAc,SAC7C9K,EAAMkS,UAAYD,EAClBjC,EAAKhF,YAAYhL,EACrB,EAEImS,EAAW,SAAkBC,GAC7B,IAAIC,EAta4a,SAAUC,EAAKjP,GAAK,GAAIc,MAAMC,QAAQkO,GAAQ,OAAOA,EAAY,GAAIC,OAAOC,YAAY5W,OAAO0W,GAAQ,OAAxf,SAAuBA,EAAKjP,GAAK,IAAIoP,EAAO,GAAQC,GAAK,EAAUC,GAAK,EAAWC,OAAKxW,EAAW,IAAM,IAAK,IAAiCyW,EAA7BC,EAAKR,EAAIC,OAAOC,cAAmBE,GAAMG,EAAKC,EAAGlR,QAAQC,QAAoB4Q,EAAK3Q,KAAK+Q,EAAG9W,QAAYsH,GAAKoP,EAAKpS,SAAWgD,GAA3DqP,GAAK,GAAkE,CAAE,MAAOK,GAAOJ,GAAK,EAAMC,EAAKG,CAAK,CAAE,QAAU,KAAWL,GAAMI,EAAW,QAAGA,EAAW,QAAK,CAAE,QAAU,GAAIH,EAAI,MAAMC,CAAI,CAAE,CAAE,OAAOH,CAAM,CAAuHO,CAAcV,EAAKjP,GAAa,MAAM,IAAI+B,UAAU,uDAA2D,CAsajoB6N,CAAeb,EAAM,GAC7B9L,EAAU+L,EAAM,GAChB5F,EAAI4F,EAAM,GACV3F,EAAI2F,EAAM,GAEd/L,EAAQmJ,WAAahD,EACrBnG,EAAQkJ,UAAY9C,CACxB,EAEInB,EAAoB,WACpB,OAAO1G,KAAKC,KAAKoO,KAAKC,MAAwB,IAAhBtO,KAAKuO,UAAqBC,SAAS,GACrE,EAEIC,EAAkB,oCAElBxH,EAA2B,SAAkCvE,EAAMhB,GACnE,IACI,OAAOkB,QAAQY,QAAQd,EAAKgM,cAAcxK,SAAS3B,gBACvD,CAAE,MAAOgB,GACL,OAAO7B,EAAQ6F,OAAQ,EAAIrG,EAAOyN,OAAOjM,EAAKW,IAAK3B,GAASyB,MAAK,SAAUyL,GACvE,IAAIC,EAAQD,EAAKC,MAAMJ,GACvB,OAAKI,EAIe,WAAbA,EAAM,GAAkB1M,OAAO2M,KAAKC,mBAAmBF,EAAM,KAAOE,mBAAmBF,EAAM,IAHzFjM,QAAQyC,QAIvB,IAAGlC,MAAK,SAAUyL,GACd,OAAOI,EAAsBtM,EAAKD,eAAe,EAAIxB,EAAQ4F,aAAanE,EAAM,EAAG,IAAIS,MAAK,SAAU8L,GAClG,IACIC,EADcD,EAAqBP,cACPxK,SAEhCgL,EAAcC,OACdD,EAAcE,MAAMR,GACpB,IAAIS,EAAaC,EAAaL,GAAsB9L,MAAK,WACrD,OAAO+L,EAAc3M,eACzB,IAGA,OADA2M,EAAcK,QACPF,CACX,GACJ,IAAKzM,QAAQyC,QACjB,CACJ,EAEI2J,EAAwB,SAA+BvM,EAAe+M,GACtE,IAAIP,EAAuBxM,EAAcwD,cAAc,UAYvD,OAVAgJ,EAAqBnC,UAAY,wBACjCmC,EAAqB9T,MAAMsU,WAAa,SACxCR,EAAqB9T,MAAMO,SAAW,QACtCuT,EAAqB9T,MAAMuU,KAAO,WAClCT,EAAqB9T,MAAMwU,IAAM,MACjCV,EAAqB9T,MAAMyU,OAAS,IACpCX,EAAqBnI,MAAQ0I,EAAO1I,MAAM0H,WAC1CS,EAAqBlI,OAASyI,EAAOzI,OAAOyH,WAC5CS,EAAqBY,UAAY,KACjCZ,EAAqBtI,aAAanF,EAAkB,QAC/CiB,EAAc0I,MAInB1I,EAAc0I,KAAKhF,YAAY8I,GAExBrM,QAAQY,QAAQyL,IALZrM,QAAQyC,OAA+G,GAMtI,EAEIiK,EAAe,SAAsBL,GACrC,IAAIlO,EAAckO,EAAqBP,cACnCQ,EAAgBnO,EAAYmD,SAEhC,OAAO,IAAItB,SAAQ,SAAUY,EAAS6B,GAClCtE,EAAY0E,OAASwJ,EAAqBxJ,OAASyJ,EAAcY,mBAAqB,WAClF,IAAIC,EAAWC,aAAY,WACnBd,EAAc/D,KAAK8E,WAAWzU,OAAS,GAAkC,aAA7B0T,EAAcgB,aAC1DC,cAAcJ,GACdvM,EAAQyL,GAEhB,GAAG,GACP,CACJ,GACJ,EA8CImB,GA5CcnZ,EAAQ8J,YAAc,SAAqB0B,EAAe+M,EAAQ1N,EAAkBJ,EAASC,EAAQE,GACnH,IAAIwO,EAAS,IAAIrP,EAAec,EAAkBJ,EAASC,GAAQ,EAAOE,GACtEsG,EAAU1F,EAAcsF,YAAYK,YACpCC,EAAU5F,EAAcsF,YAAYO,YAExC,OAAO0G,EAAsBvM,EAAe+M,GAAQrM,MAAK,SAAU8L,GAC/D,IAAIlO,EAAckO,EAAqBP,cACnCQ,EAAgBnO,EAAYmD,SAM5BmL,EAAaC,EAAaL,GAAsB9L,MAAK,WACrDkN,EAAOtO,iBAAiBuO,QAAQhD,GAChCvM,EAAYwP,SAASf,EAAOE,KAAMF,EAAOG,MACrC,sBAAsBtE,KAAKmF,UAAUC,YAAe1P,EAAYsH,UAAYmH,EAAOG,KAAO5O,EAAYoH,UAAYqH,EAAOE,OACzHR,EAAc3M,gBAAgBpH,MAAMwU,KAAOH,EAAOG,IAAM,KACxDT,EAAc3M,gBAAgBpH,MAAMuU,MAAQF,EAAOE,KAAO,KAC1DR,EAAc3M,gBAAgBpH,MAAMO,SAAW,YAGnD,IAAIgK,EAAS9C,QAAQY,QAAQ,CAACyL,EAAsBoB,EAAO5G,uBAAwB4G,EAAOnO,iBAEtFwO,EAAUhP,EAAQgP,QAEtB,OAAOL,EAAO5G,kCAAkC1I,EAAYuI,aAAe+G,EAAO5G,kCAAkChH,EAAcsF,YAAYuB,aAAe+G,EAAO5G,kCAAkCH,YAAiC,mBAAZoH,EAAyB9N,QAAQY,UAAUL,MAAK,WACvQ,OAAOuN,EAAQxB,EACnB,IAAG/L,MAAK,WACJ,OAAOuC,CACX,IAAKA,EAAS9C,QAAQyC,OAA8H,GACxJ,IASA,OAPA6J,EAAcC,OACdD,EAAcE,MAAMgB,EAAiBlM,SAASyM,SAAW,iBAlMxC,SAA4BlO,EAAemF,EAAGC,IAC/DpF,EAAcsF,aAAgBH,IAAMnF,EAAcsF,YAAYK,aAAeP,IAAMpF,EAAcsF,YAAYO,aAC7G7F,EAAcsF,YAAYwI,SAAS3I,EAAGC,EAE9C,CAgMQ+I,CAAmB9O,EAAiBW,cAAe0F,EAASE,GAC5D6G,EAAcjL,aAAaiL,EAAc2B,UAAUR,EAAO9N,iBAAkB2M,EAAc3M,iBAC1F2M,EAAcK,QAEPF,CACX,GACJ,EAEuB,SAA0BsB,GAC7C,IAAIvU,EAAM,GAsBV,OArBIuU,IACAvU,GAAO,aACHuU,EAAQG,OACR1U,GAAOuU,EAAQG,MAGfH,EAAQI,iBACR3U,GAAOuU,EAAQI,gBAGfJ,EAAQK,WACR5U,GAAO,IAAMuU,EAAQK,SAAW,KAGhCL,EAAQM,WACR7U,GAAO,IAAMuU,EAAQM,SAAW,KAGpC7U,GAAO,KAGJA,CACX,E,WCjkBA8U,EAAOja,QAAU,k8iD,kBCAjBF,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAGX,IAAIia,EAAyB,EAAQ,OAmFjCC,EAAe,SAAsBrR,GACrC,OAAmB,IAAZA,EAAK,IAAwB,MAAZA,EAAK,IAA0B,IAAZA,EAAK,IAAwB,MAAZA,EAAK,EACrE,EAsCIsR,EAAW,CAEX,wBAAIC,GAGA,IAAIpa,EA9HU,SAAyBgN,GAG3C,GAAIA,EAASqN,YAAa,CACtB,IAAIC,EAAQtN,EAASqN,cACrB,GAAIC,EAAMC,sBAAuB,CAC7B,IAAIC,EAAcxN,EAAS+B,cAAc,aACzCyL,EAAYvW,MAAM4L,OAAS4K,QAC3BD,EAAYvW,MAAMmR,QAAU,QAC5BpI,EAASiH,KAAKhF,YAAYuL,GAE1BF,EAAMI,WAAWF,GACjB,IAAIG,EAAcL,EAAMC,wBACpBK,EAAc9R,KAAK+R,MAAMF,EAAY9K,QAEzC,GADA7C,EAASiH,KAAK6G,YAAYN,GAbhB,MAcNI,EACA,OAAO,CAEf,CACJ,CAEA,OAAO,CACX,CAwGoBG,CAAgB/N,UAE5B,OADAnN,OAAOC,eAAeqa,EAAU,uBAAwB,CAAEna,MAAOA,IAC1DA,CACX,EAEA,uBAAIgb,GAGA,IAAIhb,EApEE,SAAiBgN,GAC3B,IAAId,EAAM,IAAI+O,MACV/K,EAASlD,EAAS+B,cAAc,UAChC4F,EAAMzE,EAAO0E,WAAW,MAC5B1I,EAAIC,IAAM,oEAEV,IACIwI,EAAIK,UAAU9I,EAAK,EAAG,GACtBgE,EAAOd,WACX,CAAE,MAAO/C,GACL,OAAO,CACX,CACA,OAAO,CACX,CAuDoB6O,CAAQlO,UAEpB,OADAnN,OAAOC,eAAeqa,EAAU,sBAAuB,CAAEna,MAAOA,IACzDA,CACX,EAEA,0BAAImb,GAGA,OAAO,SAAUhP,GACb,IAAIiP,EAtHC,SAAoBpO,EAAUb,GAC3C,IAAID,EAAM,IAAI+O,MACV/K,EAASlD,EAAS+B,cAAc,UAChC4F,EAAMzE,EAAO0E,WAAW,MAE5B,OAAO,IAAIlJ,SAAQ,SAAUY,GAEzBJ,EAAIC,IAAMA,EAEV,IAAIoC,EAAS,WACT,IACIoG,EAAIK,UAAU9I,EAAK,EAAG,GACtBgE,EAAOd,WACX,CAAE,MAAO/C,GACL,OAAOC,GAAQ,EACnB,CAEA,OAAOA,GAAQ,EACnB,EAEAJ,EAAIqC,OAASA,EACbrC,EAAIoC,QAAU,WACV,OAAOhC,GAAQ,EACnB,GAEqB,IAAjBJ,EAAImP,UACJC,YAAW,WACP/M,GACJ,GAAG,IAEX,GACJ,CAuFyBgN,CAAWvO,SAAUb,GAIlC,OAHAtM,OAAOC,eAAeqa,EAAU,yBAA0B,CAAEna,MAAO,WAC3D,OAAOob,CACX,IACGA,CACX,CACJ,EAEA,iCAAII,GAGA,IAAIxb,EAA8B,mBAAfoI,MAAM8E,MAA+C,mBAAjBjC,OAAOqC,MArE9C,SAA2BN,GAC/C,IAAIkD,EAASlD,EAAS+B,cAAc,UAChC0M,EAAO,IACXvL,EAAON,MAAQ6L,EACfvL,EAAOL,OAAS4L,EAChB,IAAI9G,EAAMzE,EAAO0E,WAAW,MAC5BD,EAAI+G,UAAY,iBAChB/G,EAAIgH,SAAS,EAAG,EAAGF,EAAMA,GAEzB,IAAIvP,EAAM,IAAI+O,MACVW,EAAgB1L,EAAOd,YAC3BlD,EAAIC,IAAMyP,EACV,IAAIC,GAAM,EAAI5B,EAAuB6B,wBAAwBL,EAAMA,EAAM,EAAG,EAAGvP,GAI/E,OAHAyI,EAAI+G,UAAY,MAChB/G,EAAIgH,SAAS,EAAG,EAAGF,EAAMA,IAElB,EAAIxB,EAAuB8B,mBAAmBF,GAAK5P,MAAK,SAAUC,GACrEyI,EAAIK,UAAU9I,EAAK,EAAG,GACtB,IAAIrD,EAAO8L,EAAII,aAAa,EAAG,EAAG0G,EAAMA,GAAM5S,KAC9C8L,EAAI+G,UAAY,MAChB/G,EAAIgH,SAAS,EAAG,EAAGF,EAAMA,GAEzB,IAAIjQ,EAAOwB,EAAS+B,cAAc,OAIlC,OAHAvD,EAAKvH,MAAM2H,gBAAkB,OAASgQ,EAAgB,IACtDpQ,EAAKvH,MAAM4L,OAAS4L,EAAO,KAEpBvB,EAAarR,IAAQ,EAAIoR,EAAuB8B,oBAAmB,EAAI9B,EAAuB6B,wBAAwBL,EAAMA,EAAM,EAAG,EAAGjQ,IAASE,QAAQyC,QAAO,EAC3K,IAAGlC,MAAK,SAAUC,GAGd,OAFAyI,EAAIK,UAAU9I,EAAK,EAAG,GAEfgO,EAAavF,EAAII,aAAa,EAAG,EAAG0G,EAAMA,GAAM5S,KAC3D,IAAGuD,OAAM,SAAUC,GACf,OAAO,CACX,GACJ,CAmC6F2P,CAAkBhP,UAAYtB,QAAQY,SAAQ,GAEnI,OADAzM,OAAOC,eAAeqa,EAAU,gCAAiC,CAAEna,MAAOA,IACnEA,CACX,EAEA,uBAAIic,GAGA,IAAIjc,OAvGkC,KAA5B,IAAIib,OAAQiB,YAyGtB,OADArc,OAAOC,eAAeqa,EAAU,sBAAuB,CAAEna,MAAOA,IACzDA,CACX,EAEA,yBAAImc,GAGA,IAAInc,EA3G4C,iBAAtC,IAAIoc,gBAAiBC,aA6G/B,OADAxc,OAAOC,eAAeqa,EAAU,wBAAyB,CAAEna,MAAOA,IAC3DA,CACX,EAEA,oBAAIsc,GAGA,IAAItc,EAAQ,oBAAqB,IAAIoc,eAErC,OADAvc,OAAOC,eAAeqa,EAAU,mBAAoB,CAAEna,MAAOA,IACtDA,CACX,GAGJD,EAAA,QAAkBoa,C,gBC9LlBta,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAYXD,EAAA,QAPW,SAASwc,EAAK3M,EAAOC,IAFhC,SAAyBzG,EAAUzB,GAAe,KAAMyB,aAAoBzB,GAAgB,MAAM,IAAI0B,UAAU,oCAAwC,CAGpJC,CAAgBC,KAAMgT,GAEtBhT,KAAKqG,MAAQA,EACbrG,KAAKsG,OAASA,CAClB,C,kBCXAhQ,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQyc,gBAAanc,EAErB,IAEIoc,EAAoBvS,EAFD,EAAQ,QAM3BwS,EAAkBxS,EAFD,EAAQ,QAMzByS,EAAkBzS,EAFD,EAAQ,QAIzB0S,EAAS,EAAQ,OAEjBC,EAAY,EAAQ,OAEpBC,EAAa,EAAQ,KAEzB,SAAS5S,EAAuBnF,GAAO,OAAOA,GAAOA,EAAIE,WAAaF,EAAM,CAAEsF,QAAStF,EAAO,CAE7EhF,EAAQyc,WAAa,SAAoBhR,EAAMR,EAAgBP,GAK5E,IAAI9B,EAAQ,EAERoU,EAAY,IAAIL,EAAgBrS,QAAQmB,EAAM,KAAMR,EAAgBrC,KACpEqU,EAAQ,IAAIP,EAAkBpS,QAAQ0S,EAAW,MAAM,GAQ3D,OANAE,EAAczR,EAAMuR,EAAWC,EAAOhS,EAHkCrC,GASjEqU,CACX,EAjBA,IAmBIE,EAAqB,CAAC,SAAU,OAAQ,QAAS,SAAU,KAAM,UAEjED,EAAgB,SAASA,EAAczR,EAAMrG,EAAQ6X,EAAOhS,EAAgBrC,GAK5E,IAAK,IAAiCwU,EAA7BC,EAAY5R,EAAKqH,WAAsBuK,EAAWA,EAAYD,EAAU,CAC7EA,EAAWC,EAAUtK,YACrB,IAAIjC,EAAcuM,EAAU7R,cAAcsF,YAC1C,GAAIuM,aAAqBvM,EAAYwM,MAAQD,aAAqBC,MAAQxM,EAAY1L,QAAUiY,aAAqBvM,EAAY1L,OAAOkY,KAChID,EAAUvU,KAAKyU,OAAOhZ,OAAS,GAC/Ba,EAAO4T,WAAWhT,KAAK4W,EAAgBtS,QAAQkT,aAAaH,EAAWjY,SAExE,GAAIiY,aAAqBvM,EAAYuB,aAAegL,aAAqBhL,aAAevB,EAAY1L,QAAUiY,aAAqBvM,EAAY1L,OAAOiN,aACzJ,IAAwD,IAApD8K,EAAmBM,QAAQJ,EAAUpK,UAAkB,CACvD,IAAI+J,EAAY,IAAIL,EAAgBrS,QAAQ+S,EAAWjY,EAAQ6F,EAAgBrC,KAC/E,GAAIoU,EAAUU,YAAa,CACG,UAAtBL,EAAUM,SAEV,EAAId,EAAOe,oBAAoBP,EAAWL,GACb,aAAtBK,EAAUM,SAEjB,EAAId,EAAOgB,uBAAuBR,EAAWL,GAChB,WAAtBK,EAAUM,SAEjB,EAAId,EAAOiB,qBAAqBT,EAAWL,GACpCA,EAAU9Y,MAAM6Z,WAAaf,EAAU9Y,MAAM6Z,UAAUzZ,gBAAkByY,EAAW3c,gBAAgBM,OAC3G,EAAIoc,EAAUkB,uBAAuBX,EAAWL,EAAW/R,GAG/D,IAAIgT,EAAiD,aAAtBZ,EAAUM,QACrCO,EAA6BC,EAA2BnB,EAAWK,GACvE,GAAIa,GAA8BE,EAAuBpB,GAAY,CAGjE,IAAIqB,EAAcH,GAA8BlB,EAAUsB,eAAiBrB,EAAMsB,+BAAiCtB,EAC9GuB,EAAa,IAAI9B,EAAkBpS,QAAQ0S,EAAWqB,EAAaH,GACvEG,EAAYI,SAASzY,KAAKwY,GACtBP,GACAf,EAAcG,EAAWL,EAAWwB,EAAYvT,EAAgBrC,EAExE,MACIqU,EAAMyB,SAAS1Y,KAAKgX,GAChBiB,GACAf,EAAcG,EAAWL,EAAWC,EAAOhS,EAAgBrC,EAGvE,CACJ,OACG,GAAIyU,aAAqBvM,EAAY6N,eAAiBtB,aAAqBsB,eAAiB7N,EAAY1L,QAAUiY,aAAqBvM,EAAY1L,OAAOuZ,cAAe,CAC5K,IAAIC,EAAa,IAAIjC,EAAgBrS,QAAQ+S,EAAWjY,EAAQ6F,EAAgBrC,KAC5EiW,EAA8BV,EAA2BS,EAAYvB,GACzE,GAAIwB,GAA+BT,EAAuBQ,GAAa,CAGnE,IAAIE,EAAeD,GAA+BD,EAAWN,eAAiBrB,EAAMsB,+BAAiCtB,EACjH8B,EAAc,IAAIrC,EAAkBpS,QAAQsU,EAAYE,EAAcD,GAC1EC,EAAaL,SAASzY,KAAK+Y,EAC/B,MACI9B,EAAMyB,SAAS1Y,KAAK4Y,EAE5B,CACJ,CACJ,EAEIT,EAA6B,SAAoCnB,EAAWvR,GAC5E,OAAOuR,EAAUgC,iBAAmBhC,EAAUiC,0BAA4BjC,EAAU9Y,MAAMyR,QAAU,GAAKqH,EAAUkC,iBAAmBC,EAA0BnC,EAAWvR,EAC/K,EAEI2S,EAAyB,SAAgCpB,GACzD,OAAOA,EAAUsB,gBAAkBtB,EAAUoC,YACjD,EAEID,EAA4B,SAAmCnC,EAAWvR,GAC1E,MAAyB,SAAlBA,EAAKwH,UAAuB+J,EAAU5X,kBAAkBuX,EAAgBrS,SAAW0S,EAAU5X,OAAOlB,MAAMmb,WAAW1S,gBAAgB2S,eAChJ,C,kBCxHAxf,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQuf,YAAcvf,EAAQwf,aAAexf,EAAQyf,kBAAenf,EAEpE,IAIgC0E,EAF5B0a,GAE4B1a,EAJnB,EAAQ,SAIgCA,EAAIE,WAAaF,EAAM,CAAEsF,QAAStF,GAEnFya,EAAezf,EAAQyf,aAAe,CACtC/e,KAAM,EACNif,MAAO,GAGPH,EAAexf,EAAQwf,aAAe,CACtCI,IAAK,EACLC,MAAO,EACPC,OAAQ,EACRC,KAAM,GAGNC,EAAQlgB,OAAOmgB,KAAKT,GAAc1T,KAAI,SAAUoU,GAChD,OAAOA,EAAEC,aACb,IAUkBngB,EAAQuf,YAAc,SAAqBrb,GACzD,OAAO8b,EAAMlU,KAAI,SAAUsU,GACvB,IAAIC,EAAc,IAAIX,EAAQpV,QAAQpG,EAAMG,iBAAiB,UAAY+b,EAAO,WAC5EE,EAXW,SAA0Bpc,GAC7C,MACS,SADDA,EAEOub,EAAa/e,KAErB+e,EAAaE,KACxB,CAK0BY,CAAiBrc,EAAMG,iBAAiB,UAAY+b,EAAO,WACzEI,EAAcC,WAAWvc,EAAMG,iBAAiB,UAAY+b,EAAO,WACvE,MAAO,CACHC,YAAaA,EACbC,YAAaA,EACbE,YAAaE,MAAMF,GAAe,EAAIA,EAE9C,GACJ,C,kBC9CA1gB,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAGX,IAAI0gB,EAAQ,EAAQ,OAwBpB3gB,EAAA,QApBa,SAAS4gB,EAAOjQ,EAAGC,EAAGiQ,IAFnC,SAAyBxX,EAAUzB,GAAe,KAAMyB,aAAoBzB,GAAgB,MAAM,IAAI0B,UAAU,oCAAwC,CAGpJC,CAAgBC,KAAMoX,GAEtBpX,KAAKxF,KAAO2c,EAAMG,KAAKlgB,OACvB4I,KAAKmH,EAAIA,EACTnH,KAAKoH,EAAIA,EACTpH,KAAKqX,OAASA,CAYlB,C,kBC1BA/gB,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQ+gB,oBAAiBzgB,EAEzB,IAIgC0E,EAF5Bgc,GAE4Bhc,EAJlB,EAAQ,SAI+BA,EAAIE,WAAaF,EAAM,CAAEsF,QAAStF,GAEnFic,EAAU,SAAiBf,GAC3B,OAAOO,WAAWP,EAAE3C,OACxB,EAEI2D,EAAS,4BAuBTC,GArBiBnhB,EAAQ+gB,eAAiB,SAAwB7c,GAClE,IAAIkd,EAAYC,EAAqBnd,EAAMkd,WAAald,EAAMod,iBAAmBpd,EAAMqd,cAEvFrd,EAAMsd,aAENtd,EAAMud,YACN,OAAkB,OAAdL,EACO,KAGJ,CACHA,UAAWA,EACXM,gBAAiBP,EAAqBjd,EAAMwd,iBAAmBxd,EAAMyd,uBAAyBzd,EAAM0d,oBAEpG1d,EAAM2d,mBAEN3d,EAAM4d,kBAEd,EAG2B,SAA8BC,GACrD,GAAsB,iBAAXA,EAAqB,CAC5B,IAAIC,EAAI,IAAIhB,EAAS1W,QAAQ,KAC7B,MAAO,CAAC0X,EAAGA,EACf,CACA,IAAIC,EAASF,EAAOG,MAAM,KAAKpW,IAAIkV,EAAS1W,QAAQ6X,QACpD,MAAO,CAACF,EAAO,GAAIA,EAAO,GAC9B,GAGIZ,EAAuB,SAA8BD,GACrD,GAAkB,SAAdA,GAA6C,iBAAdA,EAC/B,OAAO,KAGX,IAAIxJ,EAAQwJ,EAAUxJ,MAAMsJ,GAC5B,GAAItJ,EAAO,CACP,GAAiB,WAAbA,EAAM,GAAiB,CACvB,IAAIwK,EAASxK,EAAM,GAAGsK,MAAM,KAAKpW,IAAImV,GACrC,MAAO,CAACmB,EAAO,GAAIA,EAAO,GAAIA,EAAO,GAAIA,EAAO,GAAIA,EAAO,GAAIA,EAAO,GAC1E,CACI,IAAIC,EAAWzK,EAAM,GAAGsK,MAAM,KAAKpW,IAAImV,GACvC,MAAO,CAACoB,EAAS,GAAIA,EAAS,GAAIA,EAAS,GAAIA,EAAS,GAAIA,EAAS,IAAKA,EAAS,IAE3F,CACA,OAAO,IACX,C,kBChEAviB,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQsiB,iCAAmCtiB,EAAQuiB,iBAAcjiB,EAEjE,IAMgC0E,EAN5BmC,EAAe,WAAc,SAASC,EAAiBC,EAAQC,GAAS,IAAK,IAAIC,EAAI,EAAGA,EAAID,EAAM/C,OAAQgD,IAAK,CAAE,IAAIC,EAAaF,EAAMC,GAAIC,EAAW1C,WAAa0C,EAAW1C,aAAc,EAAO0C,EAAWC,cAAe,EAAU,UAAWD,IAAYA,EAAWE,UAAW,GAAM5H,OAAOC,eAAesH,EAAQG,EAAWG,IAAKH,EAAa,CAAE,CAAE,OAAO,SAAUI,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYT,EAAiBQ,EAAYG,UAAWF,GAAiBC,GAAaV,EAAiBQ,EAAaE,GAAqBF,CAAa,CAAG,CAA7hB,IAMa5C,EAJX,EAAQ,SAIwBA,EAAIE,WAIzD,IAEIqd,EAAcviB,EAAQuiB,YAAc,CACpCC,GAAI,EACJC,WAAY,GAGZC,EAAS,WACT,SAASA,EAAOziB,IAVpB,SAAyBoJ,EAAUzB,GAAe,KAAMyB,aAAoBzB,GAAgB,MAAM,IAAI0B,UAAU,oCAAwC,CAWhJC,CAAgBC,KAAMkZ,GAEtBlZ,KAAKxF,KAA0C,MAAnC/D,EAAM0iB,OAAO1iB,EAAMsE,OAAS,GAAage,EAAYE,WAAaF,EAAYC,GAC1F,IAAII,EAAcnC,WAAWxgB,GAI7BuJ,KAAKvJ,MAAQygB,MAAMkC,GAAe,EAAIA,CAC1C,CAmBA,OAjBAzb,EAAaub,EAAQ,CAAC,CAClB/a,IAAK,eACL1H,MAAO,WACH,OAAOuJ,KAAKxF,OAASue,EAAYE,UACrC,GACD,CACC9a,IAAK,mBACL1H,MAAO,SAA0B4iB,GAC7B,OAAOrZ,KAAKsZ,eAAiBD,GAAgBrZ,KAAKvJ,MAAQ,KAAOuJ,KAAKvJ,KAC1E,IACA,CAAC,CACD0H,IAAK,SACL1H,MAAO,SAAgB+hB,GACnB,OAAO,IAAIU,EAAOV,EACtB,KAGGU,CACX,CA9Ba,GAgCb1iB,EAAA,QAAkB0iB,EAGlB,IAAIK,EAAkB,SAASA,EAAgB/F,GAC3C,IAAI5X,EAAS4X,EAAU5X,OACvB,OAAOA,EAAS2d,EAAgB3d,GAAUqb,WAAWzD,EAAU9Y,MAAM6J,KAAKiV,SAC9E,EAEuChjB,EAAQsiB,iCAAmC,SAA0CtF,EAAW/c,EAAOgjB,GAC1I,OAAQA,GACJ,IAAK,KACL,IAAK,IACD,OAAO,IAAIP,EAAOziB,EAAQgjB,GAC9B,IAAK,KACL,IAAK,MACD,IAAI1e,EAAS,IAAIme,EAAOziB,GAExB,OADAsE,EAAOtE,OAAkB,OAATgjB,EAAgBxC,WAAWzD,EAAU9Y,MAAM6J,KAAKiV,UAAYD,EAAgB/F,GACrFzY,EACX,QAEI,OAAO,IAAIme,EAAO,KAE9B,C,gBC5EA5iB,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAgBKD,EAAQkjB,UAAY,SAAmBhf,GAOnD,MAAO,CACHif,WAPajf,EAAMif,WAQnBH,SAPW9e,EAAM8e,SAQjBI,UAPYlf,EAAMkf,UAQlBC,YAPcnf,EAAMmf,YAQpBC,WAxBc,SAAyBC,GAC3C,OAAQA,GACJ,IAAK,SACD,OAAO,IACX,IAAK,OACD,OAAO,IAGf,IAAItjB,EAAQujB,SAASD,EAAQ,IAC7B,OAAO7C,MAAMzgB,GAAS,IAAMA,CAChC,CAOqBwjB,CAAgBvf,EAAMof,YAS3C,C,kBC/BAxjB,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQ0jB,gBAAkB1jB,EAAQ2jB,gBAAarjB,EAE/C,IAUgC0E,EAV5BgF,EAAU,EAAQ,OAElB4Z,EAAkB,EAAQ,OAI1BC,GAI4B7e,EANjB,EAAQ,SAM8BA,EAAIE,WAAaF,EAAM,CAAEsF,QAAStF,GAFnF8e,EAAW,EAAQ,MAMnBH,EAAa3jB,EAAQ2jB,WAAa,SAASA,EAAWlW,EAAM8K,IAFhE,SAAyBlP,EAAUzB,GAAe,KAAMyB,aAAoBzB,GAAgB,MAAM,IAAI0B,UAAU,oCAAwC,CAGpJC,CAAgBC,KAAMma,GAEtBna,KAAKiE,KAAOA,EACZjE,KAAK+O,OAASA,CAClB,EA+BIwL,GA7BkB/jB,EAAQ0jB,gBAAkB,SAAyBzjB,EAAOmF,EAAQqG,GAWpF,IAVA,IACIuY,EADiD,IAA/B5e,EAAOlB,MAAM+f,eACF,EAAIH,EAASlf,cAAc3E,GAAO6L,KAAI,SAAUvE,GAC7E,OAAO,EAAIuc,EAASnf,eAAe4C,EACvC,KAAK,EAAIuc,EAASpf,YAAYzE,EAAOmF,GACjCb,EAASyf,EAASzf,OAClBuM,EAAcrF,EAAKoB,WAAapB,EAAKoB,WAAWrB,cAAcsF,YAAc,KAC5EI,EAAUJ,EAAcA,EAAYK,YAAc,EAClDC,EAAUN,EAAcA,EAAYO,YAAc,EAClD6S,EAAa,GACbC,EAAS,EACJ5c,EAAI,EAAGA,EAAIhD,EAAQgD,IAAK,CAC7B,IAAIkG,EAAOuW,EAASzc,GACpB,GAAInC,EAAOlB,MAAMkgB,iBAAmBR,EAAgBS,gBAAgB3jB,MAAQ+M,EAAK8P,OAAOhZ,OAAS,EAC7F,GAAIsf,EAAUvZ,QAAQ+P,qBAClB6J,EAAWle,KAAK,IAAI2d,EAAWlW,EAAM6W,EAAe7Y,EAAM0Y,EAAQ1W,EAAKlJ,OAAQ2M,EAASE,SACrF,CACH,IAAImT,EAAkB9Y,EAAK+Y,UAAU/W,EAAKlJ,QAC1C2f,EAAWle,KAAK,IAAI2d,EAAWlW,EAAMsW,EAAiBtY,EAAMyF,EAASE,KACrE3F,EAAO8Y,CACX,MACQV,EAAUvZ,QAAQ+P,uBAC1B5O,EAAOA,EAAK+Y,UAAU/W,EAAKlJ,SAE/B4f,GAAU1W,EAAKlJ,MACnB,CACA,OAAO2f,CACX,EAEuB,SAA0BzY,EAAMyF,EAASE,GAC5D,IAAIqT,EAAUhZ,EAAKD,cAAcwD,cAAc,sBAC/CyV,EAAQvV,YAAYzD,EAAKF,WAAU,IACnC,IAAIsB,EAAapB,EAAKoB,WACtB,GAAIA,EAAY,CACZA,EAAWG,aAAayX,EAAShZ,GACjC,IAAI8M,GAAS,EAAIvO,EAAQ4F,aAAa6U,EAASvT,EAASE,GAIxD,OAHIqT,EAAQ3R,YACRjG,EAAWG,aAAayX,EAAQ3R,WAAY2R,GAEzClM,CACX,CACA,OAAO,IAAIvO,EAAQ0a,OAAO,EAAG,EAAG,EAAG,EACvC,GAEIJ,EAAiB,SAAwB7Y,EAAM0Y,EAAQ5f,EAAQ2M,EAASE,GACxE,IAAImJ,EAAQ9O,EAAKD,cAAc8O,cAG/B,OAFAC,EAAMoK,SAASlZ,EAAM0Y,GACrB5J,EAAMqK,OAAOnZ,EAAM0Y,EAAS5f,GACrByF,EAAQ0a,OAAOG,eAAetK,EAAMC,wBAAyBtJ,EAASE,EACjF,C,kBC3EAtR,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQ8kB,kCAAoC9kB,EAAQ+kB,cAAgB/kB,EAAQglB,eAAiBhlB,EAAQilB,eAAiBjlB,EAAQklB,sBAAwBllB,EAAQmlB,mBAAgB7kB,EAE9K,IAMI8kB,GAFkBjb,EAFD,EAAQ,QAIhB,EAAQ,QAIjBuV,EAAUvV,EAFD,EAAQ,QAIjBkb,EAAU,EAAQ,OAElBrE,EAAW7W,EAAuBkb,GAElCrd,EAAQ,EAAQ,OAEpB,SAASmC,EAAuBnF,GAAO,OAAOA,GAAOA,EAAIE,WAAaF,EAAM,CAAEsF,QAAStF,EAAO,CAE9F,SAASuE,EAAgBF,EAAUzB,GAAe,KAAMyB,aAAoBzB,GAAgB,MAAM,IAAI0B,UAAU,oCAAwC,CAExJ,IAAIgc,EAAiB,8DACjBC,EAAoB,yCACpBC,EAAmB,gBACnBC,EAAoB,wDACpBC,EAA0B,yPAE1BP,EAAgBnlB,EAAQmlB,cAAgB,CACxCQ,gBAAiB,EACjBC,gBAAiB,GAGjBV,EAAwBllB,EAAQklB,sBAAwB,CACxDtkB,OAAQ,EACRilB,QAAS,GAGTC,EAAsB,CACtBrN,KAAM,IAAIuI,EAAS1W,QAAQ,MAC3BoO,IAAK,IAAIsI,EAAS1W,QAAQ,MAC1Byb,OAAQ,IAAI/E,EAAS1W,QAAQ,OAC7B0b,MAAO,IAAIhF,EAAS1W,QAAQ,QAC5B2b,OAAQ,IAAIjF,EAAS1W,QAAQ,SAG7B2a,EAAiBjlB,EAAQilB,eAAiB,SAASA,EAAeiB,EAAYC,GAC9E5c,EAAgBC,KAAMyb,GAEtBzb,KAAKxF,KAAOmhB,EAAcQ,gBAC1Bnc,KAAK0c,WAAaA,EAClB1c,KAAK2c,UAAYA,CACrB,EAEInB,EAAiBhlB,EAAQglB,eAAiB,SAASA,EAAekB,EAAYE,EAAOL,EAAQlF,GAC7FtX,EAAgBC,KAAMwb,GAEtBxb,KAAKxF,KAAOmhB,EAAcS,gBAC1Bpc,KAAK0c,WAAaA,EAClB1c,KAAK4c,MAAQA,EACb5c,KAAKuc,OAASA,EACdvc,KAAKqX,OAASA,CAClB,EAmBIwF,GAjBgBrmB,EAAQ+kB,cAAgB,SAAuB/H,EAAW1G,EAAMiC,GAChF,IAAItM,EAAOqK,EAAKrK,KACZF,EAASuK,EAAKvK,OACdS,EAAS8J,EAAK9J,OAElB,MAAe,oBAAXT,EACOua,EAAoBra,EAAMsM,IAAU/L,GACzB,aAAXT,GAAqC,WAAZE,EAAK,GAE9Bqa,EAAoB,CAAC,aAAatY,OAAOuY,EAA4Bta,EAAKhG,MAAM,KAAMsS,IAAU/L,GACrF,oBAAXT,EACAya,EAAoBxJ,EAAsB,aAAXxQ,EAAwBsY,EAAkC7Y,GAAQA,EAAMsM,GAC5F,aAAXxM,GAAqC,WAAZE,EAAK,GAC9Bua,EAAoBxJ,EAAWuJ,EAA4BzB,EAAkC7Y,EAAKhG,MAAM,KAAMsS,QADlH,CAGX,EAEsB,SAAyBtM,EAAMwa,EAAqBC,GAGtE,IAFA,IAAIR,EAAa,GAER3e,EAAIkf,EAAqBlf,EAAI0E,EAAK1H,OAAQgD,IAAK,CACpD,IAAItH,EAAQgM,EAAK1E,GACbof,EAAanB,EAAiBpR,KAAKnU,GACnC2mB,EAAiB3mB,EAAM4mB,YAAY,KACnCC,EAAS,IAAIpH,EAAQpV,QAAQqc,EAAa1mB,EAAM8mB,UAAU,EAAGH,GAAkB3mB,GAC/E+mB,EAAQL,EAAa,IAAI3F,EAAS1W,QAAQrK,EAAM8mB,UAAUH,EAAiB,IAAMrf,IAAMkf,EAAsB,IAAIzF,EAAS1W,QAAQ,MAAQ/C,IAAM0E,EAAK1H,OAAS,EAAI,IAAIyc,EAAS1W,QAAQ,QAAU,KACrM4b,EAAWlgB,KAAK,CAAEihB,MAAOH,EAAQI,KAAMF,GAC3C,CAgBA,IAdA,IAAIG,EAA2BjB,EAAWpa,KAAI,SAAUyK,GACpD,IAAI0Q,EAAQ1Q,EAAM0Q,MACdC,EAAO3Q,EAAM2Q,KAIjB,MAAO,CACHD,MAAOA,EAEPC,KAL8B,IAAfR,EAAmB,EAAIQ,EAAOA,EAAKE,iBAAiBV,GAAcA,EAAa,KAOtG,IAEIW,EAAoBF,EAAyB,GAAGD,KAC3ClQ,EAAK,EAAGA,EAAKmQ,EAAyB5iB,OAAQyS,IACnD,GAA0B,OAAtBqQ,EAA4B,CAC5B,IAAIC,EAASH,EAAyBnQ,GAAIkQ,KAC1C,GAAe,OAAXI,EAAiB,CAEjB,IADA,IAAIC,EAAIvQ,EACoC,OAArCmQ,EAAyBI,GAAGL,MAC/BK,IAKJ,IAHA,IAAIC,EAAQD,EAAIvQ,EAAK,EAEjByQ,GADgBN,EAAyBI,GAAGL,KAChBG,GAAqBG,EAC9CxQ,EAAKuQ,EAAGvQ,IACXqQ,EAAoBF,EAAyBnQ,GAAIkQ,KAAOG,EAAoBI,CAEpF,MACIJ,EAAoBC,CAE5B,CAGJ,OAAOH,CACX,GAEIb,EAAsB,SAA6Bra,EAAMsM,EAAQmP,GACjE,IAAIC,GAAQ,EAAIvC,EAAOwC,YAAY3b,EAAK,IACpC4b,EAAqBvC,EAAelR,KAAKnI,EAAK,IAC9C6b,EAAgBD,GAAgC,OAAVF,GAAkBpC,EAAkBnR,KAAKnI,EAAK,IACpFka,EAAY2B,EAA0B,OAAVH,EAAiBI,EAEjDL,EAAYC,EAAkB,GAAV5e,KAAKif,GAAWL,EAAOpP,GAAUsP,EAAqBI,EAAkBhc,EAAK,GAAIsM,GAAU2P,EAAqBjc,EAAK,GAAIsM,GAAUwP,EAA2Bhf,KAAKif,GAAIzP,GACvLkO,EAAsBqB,EAAgB,EAAI,EAG1CpB,EAAa3d,KAAKof,KAAI,EAAIngB,EAAMogB,UAAUrf,KAAKsf,IAAIlC,EAAUmC,IAAMvf,KAAKsf,IAAIlC,EAAUoC,IAAKxf,KAAKsf,IAAIlC,EAAUqC,IAAMzf,KAAKsf,IAAIlC,EAAUsC,KAAqB,EAAflQ,EAAO1I,MAA2B,EAAhB0I,EAAOzI,QAE1K,OAAO,IAAImV,EAAeoB,EAAgBpa,EAAMwa,EAAqBC,GAAaP,EACtF,EAEIK,EAAsB,SAA6BxJ,EAAW/Q,EAAMsM,GACpE,IAAImQ,EAAIzc,EAAK,GAAG2L,MAAM8N,GAClBU,EAAQsC,IAAe,WAATA,EAAE,SACXpoB,IAATooB,EAAE,SAA6BpoB,IAATooB,EAAE,IACtBxD,EAAsBtkB,OAASskB,EAAsBW,QACnDhF,EAAS,CAAC,EACVkF,EAAS,CAAC,EAEV2C,SAEapoB,IAATooB,EAAE,KACF7H,EAAOlQ,GAAI,EAAI0U,EAAQ/C,kCAAkCtF,EAAW0L,EAAE,GAAIA,EAAE,IAAItB,iBAAiB7O,EAAO1I,aAG/FvP,IAATooB,EAAE,KACF7H,EAAOjQ,GAAI,EAAIyU,EAAQ/C,kCAAkCtF,EAAW0L,EAAE,GAAIA,EAAE,IAAItB,iBAAiB7O,EAAOzI,SAIxG4Y,EAAE,GACF3C,EAAOpV,EAAImV,EAAoB4C,EAAE,GAAGvI,oBACpB7f,IAATooB,EAAE,KACT3C,EAAOpV,GAAI,EAAI0U,EAAQ/C,kCAAkCtF,EAAW0L,EAAE,GAAIA,EAAE,KAG5EA,EAAE,IACF3C,EAAOnV,EAAIkV,EAAoB4C,EAAE,IAAIvI,oBACpB7f,IAAVooB,EAAE,MACT3C,EAAOnV,GAAI,EAAIyU,EAAQ/C,kCAAkCtF,EAAW0L,EAAE,IAAKA,EAAE,OAIrF,IAAIC,EAAiB,CACjBhY,OAAgBrQ,IAAbylB,EAAOpV,EAAkB4H,EAAO1I,MAAQ,EAAIkW,EAAOpV,EAAEyW,iBAAiB7O,EAAO1I,OAChFe,OAAgBtQ,IAAbylB,EAAOnV,EAAkB2H,EAAOzI,OAAS,EAAIiW,EAAOnV,EAAEwW,iBAAiB7O,EAAOzI,SAEjF8Y,EAAiBC,EAAgBH,GAAKA,EAAE,IAAM,kBAAmBtC,EAAOuC,EAAgB9H,EAAQtI,GAEpG,OAAO,IAAIyM,EAAeqB,EAAgBpa,EAAMyc,EAAI,EAAI,EAAG3f,KAAKof,IAAIS,EAAejY,EAAGiY,EAAehY,IAAKwV,EAAOuC,EAAgBC,EACrI,EAEIb,EAA6B,SAAoCe,EAAQvQ,GACzE,IAAI1I,EAAQ0I,EAAO1I,MACfC,EAASyI,EAAOzI,OAChBiZ,EAAqB,GAARlZ,EACbmZ,EAAuB,GAATlZ,EAEdmZ,GADalgB,KAAKsf,IAAIxY,EAAQ9G,KAAKmgB,IAAIJ,IAAW/f,KAAKsf,IAAIvY,EAAS/G,KAAKogB,IAAIL,KAC7C,EAEhCR,EAAKS,EAAahgB,KAAKmgB,IAAIJ,GAAUG,EACrCT,EAAKQ,EAAcjgB,KAAKogB,IAAIL,GAAUG,EAI1C,MAAO,CAAEX,GAAIA,EAAIC,GAHR1Y,EAAQyY,EAGQE,GAAIA,EAAIC,GAFxB3Y,EAAS0Y,EAGtB,EAEIY,EAAgB,SAAuB7Q,GACvC,OAAOxP,KAAKsgB,KAAK9Q,EAAO1I,MAAQ,IAAK,EAAI7H,EAAMogB,UAAU7P,EAAO1I,MAAO0I,EAAOzI,QAAU,GAC5F,EAEImY,EAAoB,SAA2B7H,EAAM7H,GACrD,OAAQ6H,GACJ,IAAK,SACL,IAAK,SACD,OAAO2H,EAA2B,EAAGxP,GACzC,IAAK,OACL,IAAK,WACD,OAAOwP,EAA2Bhf,KAAKif,GAAK,EAAGzP,GACnD,IAAK,QACL,IAAK,UACD,OAAOwP,EAA2B,EAAIhf,KAAKif,GAAK,EAAGzP,GACvD,IAAK,YACL,IAAK,YACL,IAAK,iBACL,IAAK,iBACD,OAAOwP,EAA2Bhf,KAAKif,GAAKoB,EAAc7Q,GAASA,GACvE,IAAK,WACL,IAAK,WACL,IAAK,kBACL,IAAK,kBACD,OAAOwP,EAA2Bhf,KAAKif,GAAKoB,EAAc7Q,GAASA,GACvE,IAAK,cACL,IAAK,cACL,IAAK,eACL,IAAK,eACD,OAAOwP,EAA2BqB,EAAc7Q,GAASA,GAC7D,IAAK,eACL,IAAK,eACL,IAAK,cACL,IAAK,cACD,OAAOwP,EAA2B,EAAIhf,KAAKif,GAAKoB,EAAc7Q,GAASA,GAG3E,QACI,OAAOwP,EAA2Bhf,KAAKif,GAAIzP,GAEvD,EAEI2P,EAAuB,SAA8BP,EAAOpP,GAC5D,IACI+Q,EAtP4a,SAAU9S,EAAKjP,GAAK,GAAIc,MAAMC,QAAQkO,GAAQ,OAAOA,EAAY,GAAIC,OAAOC,YAAY5W,OAAO0W,GAAQ,OAAxf,SAAuBA,EAAKjP,GAAK,IAAIoP,EAAO,GAAQC,GAAK,EAAUC,GAAK,EAAWC,OAAKxW,EAAW,IAAM,IAAK,IAAiCyW,EAA7BC,EAAKR,EAAIC,OAAOC,cAAmBE,GAAMG,EAAKC,EAAGlR,QAAQC,QAAoB4Q,EAAK3Q,KAAK+Q,EAAG9W,QAAYsH,GAAKoP,EAAKpS,SAAWgD,GAA3DqP,GAAK,GAAkE,CAAE,MAAOK,GAAOJ,GAAK,EAAMC,EAAKG,CAAK,CAAE,QAAU,KAAWL,GAAMI,EAAW,QAAGA,EAAW,QAAK,CAAE,QAAU,GAAIH,EAAI,MAAMC,CAAI,CAAE,CAAE,OAAOH,CAAM,CAAuHO,CAAcV,EAAKjP,GAAa,MAAM,IAAI+B,UAAU,uDAA2D,CAsPrnB6N,CADDwQ,EAAMzF,MAAM,KAAKpW,IAAI2U,YACa,GACrDhI,EAAO6Q,EAAkB,GACzB5Q,EAAM4Q,EAAkB,GAExBC,EAAQ9Q,EAAO,IAAMF,EAAO1I,OAAS6I,EAAM,IAAMH,EAAOzI,QAE5D,OAAOiY,EAA2Bhf,KAAKygB,KAAK9I,MAAM6I,GAAS,EAAIA,GAASxgB,KAAKif,GAAK,EAAGzP,EACzF,EAEIkR,EAAa,SAAoBlR,EAAQ5H,EAAGC,EAAG8Y,GAI/C,MAHc,CAAC,CAAE/Y,EAAG,EAAGC,EAAG,GAAK,CAAED,EAAG,EAAGC,EAAG2H,EAAOzI,QAAU,CAAEa,EAAG4H,EAAO1I,MAAOe,EAAG,GAAK,CAAED,EAAG4H,EAAO1I,MAAOe,EAAG2H,EAAOzI,SAGpGjC,QAAO,SAAU8b,EAAMC,GAClC,IAAIC,GAAI,EAAI7hB,EAAMogB,UAAUzX,EAAIiZ,EAAOjZ,EAAGC,EAAIgZ,EAAOhZ,GACrD,OAAI8Y,EAAUG,EAAIF,EAAKG,gBAAkBD,EAAIF,EAAKG,iBACvC,CACHC,cAAeH,EACfE,gBAAiBD,GAIlBF,CACX,GAAG,CACCG,gBAAiBJ,EAAUM,KAAW,IACtCD,cAAe,OAChBA,aACP,EAEIlB,EAAkB,SAAyBoB,EAAQ7D,EAAOL,EAAQlF,EAAQtI,GAC1E,IAAI5H,EAAIoV,EAAOpV,EACXC,EAAImV,EAAOnV,EACXsZ,EAAK,EACLC,EAAK,EAET,OAAQF,GACJ,IAAK,eAGG7D,IAAUlB,EAAsBtkB,OAChCspB,EAAKC,EAAKphB,KAAKof,IAAIpf,KAAKsf,IAAI1X,GAAI5H,KAAKsf,IAAI1X,EAAI4H,EAAO1I,OAAQ9G,KAAKsf,IAAIzX,GAAI7H,KAAKsf,IAAIzX,EAAI2H,EAAOzI,SACtFsW,IAAUlB,EAAsBW,UACvCqE,EAAKnhB,KAAKof,IAAIpf,KAAKsf,IAAI1X,GAAI5H,KAAKsf,IAAI1X,EAAI4H,EAAO1I,QAC/Csa,EAAKphB,KAAKof,IAAIpf,KAAKsf,IAAIzX,GAAI7H,KAAKsf,IAAIzX,EAAI2H,EAAOzI,UAEnD,MAEJ,IAAK,iBAGD,GAAIsW,IAAUlB,EAAsBtkB,OAChCspB,EAAKC,EAAKphB,KAAKof,KAAI,EAAIngB,EAAMogB,UAAUzX,EAAGC,IAAI,EAAI5I,EAAMogB,UAAUzX,EAAGC,EAAI2H,EAAOzI,SAAS,EAAI9H,EAAMogB,UAAUzX,EAAI4H,EAAO1I,MAAOe,IAAI,EAAI5I,EAAMogB,UAAUzX,EAAI4H,EAAO1I,MAAOe,EAAI2H,EAAOzI,cACjL,GAAIsW,IAAUlB,EAAsBW,QAAS,CAEhD,IAAIuE,EAAIrhB,KAAKof,IAAIpf,KAAKsf,IAAIzX,GAAI7H,KAAKsf,IAAIzX,EAAI2H,EAAOzI,SAAW/G,KAAKof,IAAIpf,KAAKsf,IAAI1X,GAAI5H,KAAKsf,IAAI1X,EAAI4H,EAAO1I,QACnG+Z,EAASH,EAAWlR,EAAQ5H,EAAGC,GAAG,GAEtCuZ,EAAKC,GADLF,GAAK,EAAIliB,EAAMogB,UAAUwB,EAAOjZ,EAAIA,GAAIiZ,EAAOhZ,EAAIA,GAAKwZ,GAE5D,CACA,MAEJ,IAAK,gBAEGhE,IAAUlB,EAAsBtkB,OAChCspB,EAAKC,EAAKphB,KAAKshB,IAAIthB,KAAKsf,IAAI1X,GAAI5H,KAAKsf,IAAI1X,EAAI4H,EAAO1I,OAAQ9G,KAAKsf,IAAIzX,GAAI7H,KAAKsf,IAAIzX,EAAI2H,EAAOzI,SACtFsW,IAAUlB,EAAsBW,UACvCqE,EAAKnhB,KAAKshB,IAAIthB,KAAKsf,IAAI1X,GAAI5H,KAAKsf,IAAI1X,EAAI4H,EAAO1I,QAC/Csa,EAAKphB,KAAKshB,IAAIthB,KAAKsf,IAAIzX,GAAI7H,KAAKsf,IAAIzX,EAAI2H,EAAOzI,UAEnD,MAEJ,IAAK,kBAGD,GAAIsW,IAAUlB,EAAsBtkB,OAChCspB,EAAKC,EAAKphB,KAAKshB,KAAI,EAAIriB,EAAMogB,UAAUzX,EAAGC,IAAI,EAAI5I,EAAMogB,UAAUzX,EAAGC,EAAI2H,EAAOzI,SAAS,EAAI9H,EAAMogB,UAAUzX,EAAI4H,EAAO1I,MAAOe,IAAI,EAAI5I,EAAMogB,UAAUzX,EAAI4H,EAAO1I,MAAOe,EAAI2H,EAAOzI,cACjL,GAAIsW,IAAUlB,EAAsBW,QAAS,CAEhD,IAAIyE,EAAKvhB,KAAKshB,IAAIthB,KAAKsf,IAAIzX,GAAI7H,KAAKsf,IAAIzX,EAAI2H,EAAOzI,SAAW/G,KAAKshB,IAAIthB,KAAKsf,IAAI1X,GAAI5H,KAAKsf,IAAI1X,EAAI4H,EAAO1I,QACpG0a,EAAUd,EAAWlR,EAAQ5H,EAAGC,GAAG,GAEvCuZ,EAAKG,GADLJ,GAAK,EAAIliB,EAAMogB,UAAUmC,EAAQ5Z,EAAIA,GAAI4Z,EAAQ3Z,EAAIA,GAAK0Z,GAE9D,CACA,MAEJ,QAEIJ,EAAKrJ,EAAOlQ,GAAK,EACjBwZ,OAAkB7pB,IAAbugB,EAAOjQ,EAAkBiQ,EAAOjQ,EAAIsZ,EAIjD,MAAO,CACHvZ,EAAGuZ,EACHtZ,EAAGuZ,EAEX,EAEIrF,EAAoC9kB,EAAQ8kB,kCAAoC,SAA2C7Y,GAC3H,IAAIma,EAAQ,GACRvF,EAAS,GACToJ,EAAS,GACTxlB,EAAW,GACX+lB,EAAM,EAENC,EAAW,wFAEXC,EAAS,4CAETC,EAAqB1e,EAAKue,GAAK5S,MAAM6S,GACrCE,GACAH,IAGJ,IAAII,EAAmB3e,EAAKue,GAAK5S,MARV,qGASnBgT,IACAxE,EAAQwE,EAAiB,IAAM,GAEhB,aADfX,EAASW,EAAiB,IAAM,IAE5BX,EAAS,eACS,UAAXA,IACPA,EAAS,mBAEbO,KAGJ,IAAIK,EAAmB5e,EAAKue,GAAK5S,MAAM8S,GACnCG,GACAL,IAGJ,IAAIM,EAAmB7e,EAAKue,GAAK5S,MAAM6S,GACnCK,GACAN,IAGJ,IAAIO,EAAiB9e,EAAKue,GAAK5S,MAAM8S,GACjCK,GACAP,IAGJ,IAAIQ,EAAgBF,GAAoBH,EACpCK,GAAiBA,EAAc,KAC/BvmB,EAAWumB,EAAc,IAAM,QAAQ5W,KAAK4W,EAAc,IAAM,KAAO,IACnEA,EAAc,KACdvmB,GAAY,IAAMumB,EAAc,IAAM,QAAQ5W,KAAK4W,EAAc,IAAM,KAAO,MAItF,IAAIC,EAAcF,GAAkBF,EAiBpC,OAhBII,IACApK,EAASoK,EAAY,GAChBA,EAAY,KACbpK,GAAU,QAIdpc,GAAa2hB,GAAUvF,GAAWoJ,IAClCpJ,EAASpc,EACTA,EAAW,IAGXA,IACAA,EAAW,MAAQA,GAGhB,CAAC,CAAC2hB,EAAO6D,EAAQpJ,EAAQpc,GAAUqP,QAAO,SAAUoM,GACvD,QAASA,CACb,IAAGzT,KAAK,MAAMuB,OAAO/B,EAAKhG,MAAMukB,GACpC,EAEIjE,EAA8B,SAAqCta,GACnE,OAAOA,EAAKH,KAAI,SAAUmb,GACtB,OAAOA,EAAMrP,MAAM6N,EACvB,IAEC3Z,KAAI,SAAUkW,EAAGpZ,GACd,IAAKoZ,EACD,OAAO/V,EAAKrD,GAGhB,OAAQoZ,EAAE,IACN,IAAK,OACD,OAAOA,EAAE,GAAK,MAClB,IAAK,KACD,OAAOA,EAAE,GAAK,QAClB,IAAK,aACD,MAAa,MAATA,EAAE,GACKA,EAAE,GAAK,IAAMA,EAAE,GAEnBA,EAAE,GAAK,IAAyB,IAAnBvB,WAAWuB,EAAE,IAAY,IAEzD,GACJ,C,kBC5bAliB,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQkrB,uBAAoB5qB,EAE5B,IAMgC0E,EAF5Bgc,GAE4Bhc,EAJlB,EAAQ,SAI+BA,EAAIE,WAAaF,EAAM,CAAEsF,QAAStF,GAEnFgb,EAAQ,CAAC,WAAY,YAAa,eAAgB,eAE9BhgB,EAAQkrB,kBAAoB,SAA2BhnB,GAC3E,OAAO8b,EAAMlU,KAAI,SAAUsU,GACvB,IAGI+K,EAfwa,SAAU3U,EAAKjP,GAAK,GAAIc,MAAMC,QAAQkO,GAAQ,OAAOA,EAAY,GAAIC,OAAOC,YAAY5W,OAAO0W,GAAQ,OAAxf,SAAuBA,EAAKjP,GAAK,IAAIoP,EAAO,GAAQC,GAAK,EAAUC,GAAK,EAAWC,OAAKxW,EAAW,IAAM,IAAK,IAAiCyW,EAA7BC,EAAKR,EAAIC,OAAOC,cAAmBE,GAAMG,EAAKC,EAAGlR,QAAQC,QAAoB4Q,EAAK3Q,KAAK+Q,EAAG9W,QAAYsH,GAAKoP,EAAKpS,SAAWgD,GAA3DqP,GAAK,GAAkE,CAAE,MAAOK,GAAOJ,GAAK,EAAMC,EAAKG,CAAK,CAAE,QAAU,KAAWL,GAAMI,EAAW,QAAGA,EAAW,QAAK,CAAE,QAAU,GAAIH,EAAI,MAAMC,CAAI,CAAE,CAAE,OAAOH,CAAM,CAAuHO,CAAcV,EAAKjP,GAAa,MAAM,IAAI+B,UAAU,uDAA2D,CAejnB6N,CAHZjT,EAAMG,iBAAiB,UAAY+b,EAAO,WAEzB8B,MAAM,KAAKpW,IAAIkV,EAAS1W,QAAQ6X,QACJ,GACrDiJ,EAAaD,EAAkB,GAC/BE,EAAWF,EAAkB,GAEjC,YAA2B,IAAbE,EAA2B,CAACD,EAAYA,GAAc,CAACA,EAAYC,EACrF,GACJ,C,gBC1BAvrB,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEAD,EAAQ8gB,KAAO,CACtBwK,OAAQ,EACRC,aAAc,EACd3qB,OAAQ,E,gBCNZd,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEID,EAAQwrB,SAAW,SAAkBC,EAAKxrB,GACrD,SAAQwrB,EAAMxrB,EAClB,EAEeD,EAAQooB,SAAW,SAAkBnU,EAAGyX,GACnD,OAAO3iB,KAAK4iB,KAAK1X,EAAIA,EAAIyX,EAAIA,EACjC,EAEoB1rB,EAAQ+M,cAAgB,SAAuB7I,EAAOmD,GAEtE,IAAK,IAAIE,EAAIrD,EAAMK,OAAS,EAAGgD,GAAK,EAAGA,IAAK,CACxC,IAAIqkB,EAAW1nB,EAAMsR,KAAKjO,GAET,YAAbqkB,GACAvkB,EAAOnD,MAAM2K,YAAY+c,EAAU1nB,EAAMG,iBAAiBunB,GAElE,CACA,OAAOvkB,CACX,EAEkBrH,EAAQ6rB,YAAc,gF,kBCvBxC/rB,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAGT,IAAI+H,EAAQ,EAAQ,OAEpBlI,OAAOC,eAAeC,EAAS,eAA/B,CACE8E,YAAY,EACZC,IAAK,WACH,OAAOiD,EAAMpD,YACf,IAEF9E,OAAOC,eAAeC,EAAS,gBAA/B,CACE8E,YAAY,EACZC,IAAK,WACH,OAAOiD,EAAMrD,aACf,IAGF,IAAImnB,EAAa,EAAQ,OAEzBhsB,OAAOC,eAAeC,EAAS,cAA/B,CACE8E,YAAY,EACZC,IAAK,WACH,OAAO+mB,EAAWxmB,WACpB,G,kBCzBFxF,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQ+rB,mBAAgBzrB,EAExB,IAMI0rB,GAFW7hB,EAFD,EAAQ,QAIJ,EAAQ,QAItB8hB,EAAa9hB,EAFD,EAAQ,QAMpB+hB,EAA0B/hB,EAFD,EAAQ,QAMjC0Z,EAAY1Z,EAFD,EAAQ,QAInBH,EAAU,EAAQ,OAElBmiB,EAAS,EAAQ,MAEjBC,EAAQ,EAAQ,OAEhBC,EAAS,EAAQ,OAEjB3M,EAAUvV,EAAuBkiB,GAErC,SAASliB,EAAuBnF,GAAO,OAAOA,GAAOA,EAAIE,WAAaF,EAAM,CAAEsF,QAAStF,EAAO,CAE1EhF,EAAQ+rB,cAAgB,SAASA,EAAcvhB,EAASC,EAASC,GACjF,IAAIc,EAAgBhB,EAAQgB,cAExB8gB,EAAe,IAAItiB,EAAQ0a,OAAOja,EAAQyG,QAASzG,EAAQ2G,QAAS3G,EAAQoG,YAAapG,EAAQuG,cAGjGub,EAA0B/gB,EAAcF,gBAAkB,IAAIoU,EAAQpV,QAAQkH,iBAAiBhG,EAAcF,iBAAiBqB,iBAAmB0f,EAAOG,YACxJC,EAAsBjhB,EAAc0I,KAAO,IAAIwL,EAAQpV,QAAQkH,iBAAiBhG,EAAc0I,MAAMvH,iBAAmB0f,EAAOG,YAE9H7f,EAAkBnC,IAAYgB,EAAcF,gBAAkBihB,EAAwBjN,gBAAkBmN,EAAoBnN,gBAAkB7U,EAAQkC,gBAAkB,IAAI+S,EAAQpV,QAAQG,EAAQkC,iBAAmB,KAAO8f,EAAsBF,EAA0B9hB,EAAQkC,gBAAkB,IAAI+S,EAAQpV,QAAQG,EAAQkC,iBAAmB,KAE3V,OAAQlC,EAAQgG,uBAChBoT,EAAUvZ,QAAQmR,8BAAgC9P,QAAQY,SAAQ,IAAQL,MAAK,SAAUwgB,GACrF,OAAOA,GAAiCtT,EAuBtC,IAAI+S,EAAOpiB,eAAeS,EAASC,EAASC,GAAQ,EAAMqhB,IAlB1CY,YAAYnhB,GAAeU,MAAK,WAC1C,OAAOkN,EAAOnO,eAAe2hB,OACjC,IAAG1gB,MAAK,WAEJ,OADe,IAAIggB,EAAwB5hB,QAAQ8O,EAAO9N,iBAC1CuhB,OAAO,CACnBlgB,gBAAiBA,EACjBjC,OAAQA,EACR8F,MAAO/F,EAAQ+F,MACfG,EAAGlG,EAAQkG,EACXC,EAAGnG,EAAQmG,EACXf,MAAOpF,EAAQoF,MACfC,OAAQrF,EAAQqF,OAChBe,YAAapG,EAAQoG,YACrBG,aAAcvG,EAAQuG,aACtBE,QAASzG,EAAQyG,QACjBE,QAAS3G,EAAQ2G,SAEzB,KAC0E,EAAI+a,EAAOriB,aAAa0B,EAAe8gB,EAAc9hB,EAASC,EAASC,EAAQqhB,GAAe7f,MAAK,SAAUoK,GACvL,IAAIC,EArEoa,SAAUC,EAAKjP,GAAK,GAAIc,MAAMC,QAAQkO,GAAQ,OAAOA,EAAY,GAAIC,OAAOC,YAAY5W,OAAO0W,GAAQ,OAAxf,SAAuBA,EAAKjP,GAAK,IAAIoP,EAAO,GAAQC,GAAK,EAAUC,GAAK,EAAWC,OAAKxW,EAAW,IAAM,IAAK,IAAiCyW,EAA7BC,EAAKR,EAAIC,OAAOC,cAAmBE,GAAMG,EAAKC,EAAGlR,QAAQC,QAAoB4Q,EAAK3Q,KAAK+Q,EAAG9W,QAAYsH,GAAKoP,EAAKpS,SAAWgD,GAA3DqP,GAAK,GAAkE,CAAE,MAAOK,GAAOJ,GAAK,EAAMC,EAAKG,CAAK,CAAE,QAAU,KAAWL,GAAMI,EAAW,QAAGA,EAAW,QAAK,CAAE,QAAU,GAAIH,EAAI,MAAMC,CAAI,CAAE,CAAE,OAAOH,CAAM,CAAuHO,CAAcV,EAAKjP,GAAa,MAAM,IAAI+B,UAAU,uDAA2D,CAqEznB6N,CAAeb,EAAM,GAC7B0G,EAAYzG,EAAM,GAClBuW,EAAgBvW,EAAM,GACtBtL,EAAiBsL,EAAM,GAMvB0G,GAAQ,EAAI+O,EAAYvP,YAAYqQ,EAAe7hB,EAAgBP,GACnEqiB,EAAiBD,EAActhB,cAMnC,OAJImB,IAAoBsQ,EAAMD,UAAU9Y,MAAMmb,WAAW1S,kBACrDsQ,EAAMD,UAAU9Y,MAAMmb,WAAW1S,gBAAkB0f,EAAOG,aAGvDvhB,EAAe2hB,QAAQ1gB,MAAK,SAAU8gB,GACzC,IAAIC,EAAc,IAAIb,EAAMc,YAAYH,GAKpCI,EAAgB,CAChBxgB,gBAAiBA,EACjBsgB,YAAaA,EACbD,WAAYA,EACZtiB,OAAQA,EACR8F,MAAO/F,EAAQ+F,MACfG,EAAGlG,EAAQkG,EACXC,EAAGnG,EAAQmG,EACXf,MAAOpF,EAAQoF,MACfC,OAAQrF,EAAQqF,QAGpB,GAAIzH,MAAMC,QAAQmC,EAAQpD,QACtB,OAAOsE,QAAQC,IAAInB,EAAQpD,OAAOyE,KAAI,SAAUzE,GAE5C,OADe,IAAI4kB,EAAW3hB,QAAQjD,EAAQ8lB,GAC9BN,OAAO5P,EAC3B,KAEA,IACI9M,EADW,IAAI8b,EAAW3hB,QAAQG,EAAQpD,OAAQ8lB,GAChCN,OAAO5P,GAS7B,OARgC,IAA5BxS,EAAQ8F,iBACJyM,EAAUnQ,YACVmQ,EAAUnQ,WAAWkO,YAAYiC,GAMlC7M,CAEf,GACJ,IA7E8B,IAAUiJ,CA8E5C,GACJ,C,kBCjIAtZ,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAGX,IAwDgC+E,EAxD5BmC,EAAe,WAAc,SAASC,EAAiBC,EAAQC,GAAS,IAAK,IAAIC,EAAI,EAAGA,EAAID,EAAM/C,OAAQgD,IAAK,CAAE,IAAIC,EAAaF,EAAMC,GAAIC,EAAW1C,WAAa0C,EAAW1C,aAAc,EAAO0C,EAAWC,cAAe,EAAU,UAAWD,IAAYA,EAAWE,UAAW,GAAM5H,OAAOC,eAAesH,EAAQG,EAAWG,IAAKH,EAAa,CAAE,CAAE,OAAO,SAAUI,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYT,EAAiBQ,EAAYG,UAAWF,GAAiBC,GAAaV,EAAiBQ,EAAaE,GAAqBF,CAAa,CAAG,CAA7hB,GAIf8X,GAoD4B1a,EAtDnB,EAAQ,SAsDgCA,EAAIE,WAAaF,EAAM,CAAEsF,QAAStF,GAlDnFgD,EAAQ,EAAQ,OAEhBzH,EAAc,EAAQ,OAEtB6sB,EAAU,EAAQ,OAElBC,EAAgB,EAAQ,OAExBC,EAAW,EAAQ,OAEnBC,EAAS,EAAQ,OAEjBC,EAAQ,EAAQ,OAEhBC,EAAiB,EAAQ,OAEzBC,EAAa,EAAQ,OAErB3Q,EAAa,EAAQ,KAErB4Q,EAAU,EAAQ,OAElBC,EAAY,EAAQ,OAEpB3oB,EAAgB,EAAQ,MAExB4oB,EAAW,EAAQ,OAEnBC,EAAY,EAAQ,OAEpBlK,EAAkB,EAAQ,OAE1BmK,EAAc,EAAQ,OAEtBC,EAAiB,EAAQ,OAEzBC,EAAa,EAAQ,OAErBC,EAAc,EAAQ,OAEtBC,EAAa,EAAQ,OAErBC,EAAU,EAAQ,OAElBpkB,EAAU,EAAQ,OAElB6S,EAAS,EAAQ,OAEjBC,EAAY,EAAQ,OAMpBuR,EAAa,CAAC,QAAS,WAAY,UAEnCC,EAAgB,WAChB,SAASA,EAAc7iB,EAAMrG,EAAQ6F,EAAgBrC,GACjD,IAAI8C,EAAQlC,MANpB,SAAyBH,EAAUzB,GAAe,KAAMyB,aAAoBzB,GAAgB,MAAM,IAAI0B,UAAU,oCAAwC,CAQhJC,CAAgBC,KAAM8kB,GAEtB9kB,KAAKpE,OAASA,EACdoE,KAAKmU,QAAUlS,EAAKkS,QACpBnU,KAAKZ,MAAQA,EACbY,KAAKwP,WAAa,GAClBxP,KAAK+kB,UAAY,GACS,iBAAf9iB,EAAK+iB,QACZhlB,KAAKilB,UAAYhjB,EAAK+iB,OAE1B,IAAI1d,EAAcrF,EAAKD,cAAcsF,YACjCI,EAAUJ,EAAYK,YACtBC,EAAUN,EAAYO,YACtBnN,EAAQ4M,EAAYU,iBAAiB/F,EAAM,MAC3C4J,GAAU,EAAIiY,EAASoB,cAAcxqB,EAAMmR,SAE3CsZ,EAAyB,UAAdljB,EAAKzH,MAAkC,aAAdyH,EAAKzH,KAEzCS,GAAW,EAAIqpB,EAAUc,eAAe1qB,EAAMO,UAiClD,GA/BA+E,KAAKtF,MAAQ,CACTmb,WAAYsP,EAAW9R,EAAOgS,kBAAmB,EAAItuB,EAAYuuB,iBAAiB5qB,EAAO+G,GACzF0N,OAAQgW,EAAW9R,EAAOkS,eAAgB,EAAI3B,EAAQ7N,aAAarb,GACnE8qB,cAAevjB,aAAgBqF,EAAYme,kBAAoBxjB,aAAgBwjB,mBAAqBN,GAAW,EAAI9R,EAAOqS,sBAAsBzjB,IAAQ,EAAI4hB,EAAcnC,mBAAmBhnB,GAC7L+iB,MAAO0H,EAAW9R,EAAOsS,YAAc,IAAIzP,EAAQpV,QAAQpG,EAAM+iB,OACjE5R,QAASA,EACT+Z,OAAO,EAAI7B,EAAO8B,eAAenrB,EAAMkrB,OACvCrhB,MAAM,EAAIyf,EAAMtK,WAAWhf,GAC3B+f,eAAe,EAAIwJ,EAAe6B,oBAAoBprB,EAAM+f,eAC5DlG,UAAW1I,IAAYiY,EAASiC,QAAQC,WAAY,EAAIzS,EAAW7c,gBAAgBgE,GAAS,KAC5FqB,WAAW,EAAImoB,EAAW+B,gBAAgBvrB,EAAMqB,WAChDmqB,QAAQ,EAAI/B,EAAQgC,aAAazrB,GACjCyR,QAAS8K,WAAWvc,EAAMyR,SAC1B9L,UAAgD,IAAtCwkB,EAAW5Q,QAAQhS,EAAKkS,UAAkB,EAAIiQ,EAAUgC,eAAe1rB,EAAM2F,UAAY+jB,EAAUiC,SAASC,OACtHrqB,cAAc,EAAIR,EAAc2E,mBAAmB1F,EAAMuB,aAAevB,EAAMuB,aAAevB,EAAM6rB,UACnGC,SAAS,EAAInC,EAASoC,cAAc/rB,GACpCO,SAAUA,EACV2f,gBAAgB,EAAIR,EAAgBsM,qBAAqBhsB,GACzDisB,YAAY,EAAIpC,EAAYqC,iBAAiBlsB,EAAMisB,YACnDE,eAAe,EAAIrC,EAAesC,oBAAoBpsB,EAAMmsB,eAC5DjP,WAAW,EAAI6M,EAAWlN,gBAAgB7c,GAC1CsU,YAAY,EAAI0V,EAAYqC,iBAAiBrsB,EAAMsU,YACnDhT,WAAW,EAAI2oB,EAAWqC,gBAAgBtsB,EAAMsB,WAChDirB,QAAQ,EAAIrC,EAAQsC,aAAajsB,IAAaqpB,EAAUrD,SAASkG,OAASzsB,EAAMusB,OAAS,SAGzFjnB,KAAK0V,kBAELzT,EAAKvH,MAAMkd,UAAY,uBAGvB/L,IAAYiY,EAASiC,QAAQC,UAAW,CACxC,IAAIoB,GAAY,EAAI9T,EAAU+T,cAAcrnB,MAC5C,GAAIonB,EAAW,CACX,IAAIE,EAAYF,EAAUrC,UAAUhqB,OACpCqsB,EAAUrC,UAAUvoB,KAAKwD,MACzBA,KAAKsnB,UAAYrlB,EAAKyH,aAAa,UAAkC,iBAAfzH,EAAKxL,MAAqBwL,EAAKxL,MAAsB,IAAd6wB,EAAiD,iBAAxBF,EAAUnC,UAAyBmC,EAAUnC,UAAY,EAAImC,EAAUrC,UAAUuC,EAAY,GAAGA,UAAY,CACtO,CACJ,CAGqB,QAAjBrlB,EAAKkS,SACLlS,EAAKslB,iBAAiB,QAAQ,WAC1BrlB,EAAM6M,QAAS,EAAIvO,EAAQ4F,aAAanE,EAAMyF,EAASE,GACvD1F,EAAMslB,cAAe,EAAIhnB,EAAQinB,kBAAkBvlB,EAAM6M,OAAQ7M,EAAMxH,MAAMyU,OAAQjN,EAAMxH,MAAM8qB,aACrG,IAEJxlB,KAAK0nB,MAAQC,EAAS1lB,EAAMR,GAC5BzB,KAAK+O,OAASoW,GAAW,EAAI9R,EAAOuU,sBAAqB,EAAIpnB,EAAQ4F,aAAanE,EAAMyF,EAASE,KAAY,EAAIpH,EAAQ4F,aAAanE,EAAMyF,EAASE,GACrJ5H,KAAKwnB,cAAe,EAAIhnB,EAAQinB,kBAAkBznB,KAAK+O,OAAQ/O,KAAKtF,MAAMyU,OAAQnP,KAAKtF,MAAM8qB,aAOjG,CA8DA,OA5DA7nB,EAAamnB,EAAe,CAAC,CACzB3mB,IAAK,eACL1H,MAAO,WACH,IAAIoxB,EAAc7nB,KAAKpE,OAASoE,KAAKpE,OAAOksB,eAAiB,GAG7D,OAFgB9nB,KAAKtF,MAAM2F,WAAa+jB,EAAUiC,SAAS0B,QAExCF,EAAYrjB,OAAO,EAAC,EAAIhE,EAAQwnB,yBAAyBhoB,KAAKwnB,gBAAkBK,CACvG,GACD,CACC1pB,IAAK,WACL1H,MAAO,WACH,OAAOuJ,KAAKwV,kBAAoBxV,KAAK4V,eAAiB5V,KAAKioB,wBAC/D,GACD,CACC9pB,IAAK,YACL1H,MAAO,WACH,QAAQ,EAAI+H,EAAMwjB,UAAUhiB,KAAKtF,MAAMmR,QAASiY,EAASiC,QAAQ7uB,OAAS8I,KAAKtF,MAAMyR,QAAU,GAAKnM,KAAKtF,MAAMsU,aAAe0V,EAAYwD,WAAWH,OACzJ,GACD,CACC5pB,IAAK,yBACL1H,MAAO,WACH,OAAOuJ,KAAKtF,MAAMO,WAAaqpB,EAAUrD,SAASkG,QAAUnnB,KAAKtF,MAAMO,WAAaqpB,EAAUrD,SAASkH,QAC3G,GACD,CACChqB,IAAK,eACL1H,MAAO,WACH,OAAOuJ,KAAKtF,MAAMO,WAAaqpB,EAAUrD,SAASkG,MACtD,GACD,CACChpB,IAAK,aACL1H,MAAO,WACH,OAAOuJ,KAAKtF,MAAMkrB,QAAU7B,EAAOqE,MAAMlxB,IAC7C,GACD,CACCiH,IAAK,gBACL1H,MAAO,WACH,OAAuB,OAAhBuJ,KAAKpE,MAChB,GACD,CACCuC,IAAK,gBACL1H,MAAO,WACH,OAAgC,OAAzBuJ,KAAKtF,MAAMkd,SACtB,GACD,CACCzZ,IAAK,yBACL1H,MAAO,WACH,OAAOuJ,KAAK8U,iBAAmB9U,KAAKtF,MAAMusB,OAAOoB,IACrD,GACD,CACClqB,IAAK,gBACL1H,MAAO,WACH,OAAO,EAAI+H,EAAMwjB,UAAUhiB,KAAKtF,MAAMmR,QAASiY,EAASiC,QAAQuC,UAAW,EAAI9pB,EAAMwjB,UAAUhiB,KAAKtF,MAAMmR,QAASiY,EAASiC,QAAQwC,gBAAiB,EAAI/pB,EAAMwjB,UAAUhiB,KAAKtF,MAAMmR,QAASiY,EAASiC,QAAQyC,eAAgB,EAAIhqB,EAAMwjB,UAAUhiB,KAAKtF,MAAMmR,QAASiY,EAASiC,QAAQ0C,eAAgB,EAAIjqB,EAAMwjB,UAAUhiB,KAAKtF,MAAMmR,QAASiY,EAASiC,QAAQ2C,oBAAqB,EAAIlqB,EAAMwjB,UAAUhiB,KAAKtF,MAAMmR,QAASiY,EAASiC,QAAQ4C,aAClb,GACD,CACCxqB,IAAK,6BACL1H,MAAO,WACH,OAAO,EAAI+H,EAAMwjB,UAAUhiB,KAAKtF,MAAMmR,QAASiY,EAASiC,QAAQwC,gBAAiB,EAAI/pB,EAAMwjB,UAAUhiB,KAAKtF,MAAMmR,QAASiY,EAASiC,QAAQ4C,aAC9I,KAGG7D,CACX,CA/IoB,GAiJpBtuB,EAAA,QAAkBsuB,EAGlB,IAAI6C,EAAW,SAAkB1lB,EAAMR,GACnC,GAAIQ,aAAgBA,EAAKD,cAAcsF,YAAY6N,eAAiBlT,aAAgBkT,cAAe,CAC/F,IAAIpG,GAAS,EAAIvO,EAAQ4F,aAAanE,EAAM,EAAG,GAC/CA,EAAKiE,aAAa,QAAS6I,EAAO1I,MAAQ,MAC1CpE,EAAKiE,aAAa,SAAU6I,EAAOzI,OAAS,MAC5C,IAAIoQ,EAAI,IAAIkS,cACZ,OAAOnnB,EAAeonB,UAAU,sBAAwBC,mBAAmBpS,EAAEqS,kBAAkB9mB,IACnG,CACA,OAAQA,EAAKkS,SACT,IAAK,MAED,IAAIxR,EAAMV,EACV,OAAOR,EAAeonB,UAAUlmB,EAAIqmB,YAAcrmB,EAAIC,KAC1D,IAAK,SAED,IAAI+D,EAAS1E,EACb,OAAOR,EAAewnB,WAAWtiB,GACrC,IAAK,SACD,IAAIX,EAAY/D,EAAKinB,aAAa,wCAClC,GAAIljB,EACA,OAAOA,EAKnB,OAAO,IACX,C,gBChPA1P,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEX,IAAI0yB,EAAa3yB,EAAQ2yB,WAAa,CAClChpB,OAAQ,SACRipB,UAAW,YACXC,SAAU,YAGO7yB,EAAQwwB,eAAiB,SAAwBhrB,GAClE,OAAQA,GACJ,IAAK,YACD,OAAOmtB,EAAWC,UACtB,IAAK,WACD,OAAOD,EAAWE,SAEtB,QACI,OAAOF,EAAWhpB,OAE9B,C,gBCnBA7J,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEX,IAAIwqB,EAAWzqB,EAAQyqB,SAAW,CAC9BkG,OAAQ,EACRgB,SAAU,EACVmB,SAAU,EACVC,MAAO,EACPC,OAAQ,GAGQhzB,EAAQ4uB,cAAgB,SAAuBnqB,GAC/D,OAAQA,GACJ,IAAK,WACD,OAAOgmB,EAASkH,SACpB,IAAK,WACD,OAAOlH,EAASqI,SACpB,IAAK,QACD,OAAOrI,EAASsI,MACpB,IAAK,SACD,OAAOtI,EAASuI,OAGxB,OAAOvI,EAASkG,MACpB,C,kBCxBA7wB,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQ2vB,iBAAcrvB,EAEtB,IAIgC0E,EAF5Bgc,GAE4Bhc,EAJlB,EAAQ,SAI+BA,EAAIE,WAAaF,EAAM,CAAEsF,QAAStF,GAEnFgb,EAAQ,CAAC,MAAO,QAAS,SAAU,QAErBhgB,EAAQ2vB,YAAc,SAAqBzrB,GACzD,OAAO8b,EAAMlU,KAAI,SAAUsU,GACvB,OAAO,IAAIY,EAAS1W,QAAQpG,EAAMG,iBAAiB,UAAY+b,GACnE,GACJ,C,kBCjBAtgB,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAGX,IAAI0gB,EAAQ,EAAQ,OAoBpB3gB,EAAA,QAhBa,SAASizB,EAAOtiB,EAAGC,IAFhC,SAAyBvH,EAAUzB,GAAe,KAAMyB,aAAoBzB,GAAgB,MAAM,IAAI0B,UAAU,oCAAwC,CAGpJC,CAAgBC,KAAMypB,GAEtBzpB,KAAKxF,KAAO2c,EAAMG,KAAKwK,OACvB9hB,KAAKmH,EAAIA,EACTnH,KAAKoH,EAAIA,CASb,C,kBCtBA,IAAIsiB,EAAWpzB,OAAOqzB,QAAU,SAAU9rB,GAAU,IAAK,IAAIE,EAAI,EAAGA,EAAI6rB,UAAU7uB,OAAQgD,IAAK,CAAE,IAAI8rB,EAASD,UAAU7rB,GAAI,IAAK,IAAII,KAAO0rB,EAAcvzB,OAAOiI,UAAUurB,eAAe1hB,KAAKyhB,EAAQ1rB,KAAQN,EAAOM,GAAO0rB,EAAO1rB,GAAU,CAAE,OAAON,CAAQ,EAI3P+C,EAAmBD,EAFD,EAAQ,QAM1BopB,EAAWppB,EAFD,EAAQ,QAIlBqpB,EAAU,EAAQ,OAElBxpB,EAAU,EAAQ,OAEtB,SAASG,EAAuBnF,GAAO,OAAOA,GAAOA,EAAIE,WAAaF,EAAM,CAAEsF,QAAStF,EAAO,CAE9F,IAAIyuB,EAAc,SAAqBjpB,EAASkpB,GAC5C,IAAIC,EAASD,GAAQ,CAAC,EAClBhpB,EAAS,IAAI6oB,EAASjpB,QAAkC,kBAAnBqpB,EAAOtjB,SAAwBsjB,EAAOtjB,SAC/E3F,EAAOkpB,IAAI,oCAMX,IAAIpoB,EAAgBhB,EAAQgB,cAC5B,IAAKA,EACD,OAAOG,QAAQyC,OAAO,6CAE1B,IAAI0C,EAActF,EAAcsF,YAE5BI,EAAUJ,EAAYK,YACtBC,EAAUN,EAAYO,YAItBiF,EAFiC,SAApB9L,EAAQmT,SAA0C,SAApBnT,EAAQmT,SAE/B,EAAI3T,EAAQ6pB,mBAAmBroB,IAAiB,EAAIxB,EAAQ4F,aAAapF,EAAS0G,EAASE,GAC/GvB,EAAQyG,EAAKzG,MACbC,EAASwG,EAAKxG,OACd2I,EAAOnC,EAAKmC,KACZC,EAAMpC,EAAKoC,IAEXob,EAAiB,CACjB7jB,OAAO,EACPC,YAAY,EACZvD,gBAAiB,UACjByD,aAAc,KACdC,SAAS,EACTC,MAAO,KACPC,iBAAiB,EACjBE,wBAAwB,EACxBD,MAAOM,EAAYijB,kBAAoB,EACvC1sB,OAAQ,IAAI+C,EAAiBE,QAAQqpB,EAAOxjB,QAC5CO,SAAS,EACTC,EAAG8H,EACH7H,EAAG8H,EACH7I,MAAO9G,KAAKC,KAAK6G,GACjBC,OAAQ/G,KAAKC,KAAK8G,GAClBe,YAAaC,EAAYC,WACzBC,aAAcF,EAAYG,YAC1BC,QAASJ,EAAYK,YACrBC,QAASN,EAAYO,aAWzB,OARa,EAAImiB,EAAQzH,eAAevhB,EAAS0oB,EAAS,CAAC,EAAGY,EAAgBH,GAASjpB,EAS3F,EAEA+oB,EAAYO,eAAiB5pB,EAAiBE,QAE9C2P,EAAOja,QAAUyzB,C,kBC7EjB3zB,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAGX,IAQgC+E,EAR5BmC,EAAe,WAAc,SAASC,EAAiBC,EAAQC,GAAS,IAAK,IAAIC,EAAI,EAAGA,EAAID,EAAM/C,OAAQgD,IAAK,CAAE,IAAIC,EAAaF,EAAMC,GAAIC,EAAW1C,WAAa0C,EAAW1C,aAAc,EAAO0C,EAAWC,cAAe,EAAU,UAAWD,IAAYA,EAAWE,UAAW,GAAM5H,OAAOC,eAAesH,EAAQG,EAAWG,IAAKH,EAAa,CAAE,CAAE,OAAO,SAAUI,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYT,EAAiBQ,EAAYG,UAAWF,GAAiBC,GAAaV,EAAiBQ,EAAaE,GAAqBF,CAAa,CAAG,CAA7hB,GAEf+Y,EAAQ,EAAQ,OAIhBsT,GAE4BjvB,EAJlB,EAAQ,SAI+BA,EAAIE,WAAaF,EAAM,CAAEsF,QAAStF,GAInFkvB,EAAO,SAAcjgB,EAAGyX,EAAGyI,GAC3B,OAAO,IAAIF,EAAS3pB,QAAQ2J,EAAEtD,GAAK+a,EAAE/a,EAAIsD,EAAEtD,GAAKwjB,EAAGlgB,EAAErD,GAAK8a,EAAE9a,EAAIqD,EAAErD,GAAKujB,EAC3E,EAEIC,EAAc,WACd,SAASA,EAAY5F,EAAO6F,EAAcC,EAAYC,IAP1D,SAAyBlrB,EAAUzB,GAAe,KAAMyB,aAAoBzB,GAAgB,MAAM,IAAI0B,UAAU,oCAAwC,CAQhJC,CAAgBC,KAAM4qB,GAEtB5qB,KAAKxF,KAAO2c,EAAMG,KAAKyK,aACvB/hB,KAAKglB,MAAQA,EACbhlB,KAAK6qB,aAAeA,EACpB7qB,KAAK8qB,WAAaA,EAClB9qB,KAAK+qB,IAAMA,CACf,CAoBA,OAlBAptB,EAAaitB,EAAa,CAAC,CACvBzsB,IAAK,YACL1H,MAAO,SAAmBk0B,EAAGK,GACzB,IAAIC,EAAKP,EAAK1qB,KAAKglB,MAAOhlB,KAAK6qB,aAAcF,GACzCO,EAAKR,EAAK1qB,KAAK6qB,aAAc7qB,KAAK8qB,WAAYH,GAC9CQ,EAAKT,EAAK1qB,KAAK8qB,WAAY9qB,KAAK+qB,IAAKJ,GACrCS,EAAOV,EAAKO,EAAIC,EAAIP,GACpBU,EAAOX,EAAKQ,EAAIC,EAAIR,GACpBW,EAAOZ,EAAKU,EAAMC,EAAMV,GAC5B,OAAOK,EAAY,IAAIJ,EAAY5qB,KAAKglB,MAAOiG,EAAIG,EAAME,GAAQ,IAAIV,EAAYU,EAAMD,EAAMF,EAAInrB,KAAK+qB,IAC1G,GACD,CACC5sB,IAAK,UACL1H,MAAO,WACH,OAAO,IAAIm0B,EAAY5qB,KAAK+qB,IAAK/qB,KAAK8qB,WAAY9qB,KAAK6qB,aAAc7qB,KAAKglB,MAC9E,KAGG4F,CACX,CA9BkB,GAgClBp0B,EAAA,QAAkBo0B,C,kBCpDlBt0B,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAGX,IAQgC+E,EAR5BmC,EAAe,WAAc,SAASC,EAAiBC,EAAQC,GAAS,IAAK,IAAIC,EAAI,EAAGA,EAAID,EAAM/C,OAAQgD,IAAK,CAAE,IAAIC,EAAaF,EAAMC,GAAIC,EAAW1C,WAAa0C,EAAW1C,aAAc,EAAO0C,EAAWC,cAAe,EAAU,UAAWD,IAAYA,EAAWE,UAAW,GAAM5H,OAAOC,eAAesH,EAAQG,EAAWG,IAAKH,EAAa,CAAE,CAAE,OAAO,SAAUI,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYT,EAAiBQ,EAAYG,UAAWF,GAAiBC,GAAaV,EAAiBQ,EAAaE,GAAqBF,CAAa,CAAG,CAA7hB,IAQa5C,EANX,EAAQ,SAMwBA,EAAIE,WAFzC,EAAQ,OAMxB,IAAI6vB,EAAkB,WAClB,SAASA,EAAgB/X,EAAW5X,EAAQ8Y,IAHhD,SAAyB7U,EAAUzB,GAAe,KAAMyB,aAAoBzB,GAAgB,MAAM,IAAI0B,UAAU,oCAAwC,CAIhJC,CAAgBC,KAAMurB,GAEtBvrB,KAAKwT,UAAYA,EACjBxT,KAAKpE,OAASA,EACdoE,KAAKiV,SAAW,GAChBjV,KAAKkV,SAAW,GAChBlV,KAAK0U,2BAA6BA,CACtC,CAcA,OAZA/W,EAAa4tB,EAAiB,CAAC,CAC3BptB,IAAK,aACL1H,MAAO,WACH,OAAOuJ,KAAKpE,OAASoE,KAAKwT,UAAU9Y,MAAMyR,QAAUnM,KAAKpE,OAAO4vB,aAAexrB,KAAKwT,UAAU9Y,MAAMyR,OACxG,GACD,CACChO,IAAK,+BACL1H,MAAO,WACH,OAAQuJ,KAAKpE,QAAUoE,KAAK0U,2BAA6B1U,KAAOA,KAAKpE,OAAOmZ,8BAChF,KAGGwW,CACX,CAxBsB,GA0BtB/0B,EAAA,QAAkB+0B,C,gBC1ClBj1B,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEX,IAAIg1B,EAAaj1B,EAAQi1B,WAAa,CAClCtrB,OAAQ,SACRurB,OAAQ,UAGSl1B,EAAQyvB,eAAiB,SAAwBjqB,GAClE,MACS,WADDA,EAEOyvB,EAAWC,OAGXD,EAAWtrB,MAE9B,C,kBChBA7J,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQoE,qBAAuBpE,EAAQ8uB,gBAAkB9uB,EAAQm1B,8BAAgCn1B,EAAQo1B,4BAA8Bp1B,EAAQq1B,mCAAqCr1B,EAAQs1B,gCAAkCt1B,EAAQu1B,gCAAkCv1B,EAAQw1B,wBAA0Bx1B,EAAQy1B,kBAAoBz1B,EAAQ01B,gBAAkB11B,EAAQ21B,gBAAkB31B,EAAQ41B,uBAAoBt1B,EAEtZ,IAEIof,EAAUvV,EAFD,EAAQ,QAMjB6W,EAAW7W,EAFD,EAAQ,QAMlB0rB,EAAS1rB,EAFD,EAAQ,QAMhB8pB,EAAW9pB,EAFD,EAAQ,QAIlBH,EAAU,EAAQ,OAElB6jB,EAAW,EAAQ,OAEvB,SAAS1jB,EAAuBnF,GAAO,OAAOA,GAAOA,EAAIE,WAAaF,EAAM,CAAEsF,QAAStF,EAAO,CAI9F,IAAI4wB,EAAoB51B,EAAQ41B,kBAAoB,CAChDE,OAAQ,EACRC,UAAW,EACXC,SAAU,EACVC,SAAU,GAGVN,EAAkB31B,EAAQ21B,gBAAkB,CAC5CO,KAAM,EACNC,QAAS,EACTC,MAAO,EACPC,OAAQ,GAGRX,EAAkB11B,EAAQ01B,gBAAkB,CAC5CY,WAAY,EACZC,YAAa,EACbC,YAAa,GAGbf,EAAoBz1B,EAAQy1B,kBAAoBC,EAEhDQ,EAAO,OAEPO,EAAiB,SAASA,EAAe/a,GAGzC,OA7BJ,SAAyBrS,EAAUzB,GAAe,KAAMyB,aAAoBzB,GAAgB,MAAM,IAAI0B,UAAU,oCAAwC,CA2BpJC,CAAgBC,KAAMitB,GAEd/a,GACJ,IAAK,UACDlS,KAAKkS,KAAOia,EAAgBQ,QAC5B,MACJ,IAAK,QACD3sB,KAAKkS,KAAOia,EAAgBS,MAC5B,MACJ,IAAK,OACD5sB,KAAKkS,KAAOia,EAAgBO,KAC5B,MACJ,QACI1sB,KAAKvJ,MAAQ,IAAI+gB,EAAS1W,QAAQoR,GAE9C,EAuCIgb,GArC0B12B,EAAQw1B,wBAA0B,SAAiC3pB,EAAiBqlB,EAAO3Y,GACrH,IAAI1I,EAAQ,EACRC,EAAS,EACT4L,EAAO7P,EAAgB6P,KAC3B,GAAIA,EAAK,GAAGA,OAASia,EAAgBQ,SAAWza,EAAK,GAAGA,OAASia,EAAgBS,MAAO,CACpF,IAAIO,EAAcpe,EAAO1I,MAAQ0I,EAAOzI,OACpC8mB,EAAe1F,EAAMrhB,MAAQqhB,EAAMphB,OACvC,OAAO6mB,EAAcC,IAAkBlb,EAAK,GAAGA,OAASia,EAAgBS,OAAS,IAAIP,EAAOvrB,QAAQiO,EAAO1I,MAAO0I,EAAO1I,MAAQ+mB,GAAgB,IAAIf,EAAOvrB,QAAQiO,EAAOzI,OAAS8mB,EAAcre,EAAOzI,OAC7M,CAkBA,OAhBI4L,EAAK,GAAGzb,QACR4P,EAAQ6L,EAAK,GAAGzb,MAAMmnB,iBAAiB7O,EAAO1I,QAG9C6L,EAAK,GAAGA,OAASia,EAAgBO,MAAQxa,EAAK,GAAGA,OAASia,EAAgBO,KAC1EpmB,EAASohB,EAAMphB,OACR4L,EAAK,GAAGA,OAASia,EAAgBO,KACxCpmB,EAASD,EAAQqhB,EAAMrhB,MAAQqhB,EAAMphB,OAC9B4L,EAAK,GAAGzb,QACf6P,EAAS4L,EAAK,GAAGzb,MAAMmnB,iBAAiB7O,EAAOzI,SAG/C4L,EAAK,GAAGA,OAASia,EAAgBO,OACjCrmB,EAAQC,EAASohB,EAAMphB,OAASohB,EAAMrhB,OAGnC,IAAIgmB,EAAOvrB,QAAQuF,EAAOC,EACrC,EAEsC9P,EAAQu1B,gCAAkC,SAAyC1pB,EAAiB0M,GACtI,IAAImD,EAAO7P,EAAgB6P,KACvB7L,EAAQ6L,EAAK,GAAGzb,MAAQyb,EAAK,GAAGzb,MAAMmnB,iBAAiB7O,EAAO1I,OAAS0I,EAAO1I,MAC9EC,EAAS4L,EAAK,GAAGzb,MAAQyb,EAAK,GAAGzb,MAAMmnB,iBAAiB7O,EAAOzI,QAAU4L,EAAK,GAAGzb,MAAQ4P,EAAQ0I,EAAOzI,OAE5G,OAAO,IAAI+lB,EAAOvrB,QAAQuF,EAAOC,EACrC,EAEgB,IAAI2mB,EAAeP,IAyD/BW,GAvDkC72B,EAAQs1B,gCAAkC,SAAyCwB,EAAQC,GAC7H,OAAQA,IACCrB,EAAgBY,YACV,EAAItsB,EAAQgtB,wBAAwBF,IAGpC,EAAI9sB,EAAQwnB,yBAAyBsF,EAExD,EAEyC92B,EAAQq1B,mCAAqC,SAA4C4B,EAAkB1e,EAAQyX,EAASrX,GACjK,IAAIue,GAAa,EAAIltB,EAAQmtB,qBAAqB5e,EAAQI,GAE1D,OAAQse,GACJ,KAAKxB,EAAkBa,WACnB,OAAO/d,EACX,KAAKkd,EAAkBe,YACnB,IAAIY,EAAcpH,EAAQnC,EAASwJ,cAActX,MAAMqH,iBAAiB7O,EAAO1I,OAC3EynB,EAAetH,EAAQnC,EAASwJ,cAAcxX,OAAOuH,iBAAiB7O,EAAO1I,OAC7E0nB,EAAavH,EAAQnC,EAASwJ,cAAczX,KAAKwH,iBAAiB7O,EAAO1I,OACzE2nB,EAAgBxH,EAAQnC,EAASwJ,cAAcvX,QAAQsH,iBAAiB7O,EAAO1I,OACnF,OAAO,IAAI7F,EAAQ0a,OAAOwS,EAAWze,KAAO2e,EAAaF,EAAWxe,IAAM6e,EAAYL,EAAWrnB,MAAQunB,EAAcE,EAAcJ,EAAWpnB,OAASynB,EAAaC,GAE1K,QACI,OAAON,EAEnB,EAEkCl3B,EAAQo1B,4BAA8B,SAAqC3wB,EAAUiX,EAAMnD,GACzH,OAAO,IAAI0b,EAAS3pB,QAAQ7F,EAAS,GAAG2iB,iBAAiB7O,EAAO1I,MAAQ6L,EAAK7L,OAAQpL,EAAS,GAAG2iB,iBAAiB7O,EAAOzI,OAAS4L,EAAK5L,QAC3I,EAEoC9P,EAAQm1B,8BAAgC,SAAuC9V,EAAY5a,EAAUiX,EAAM+b,EAA2Blf,GAEtK,OADa8G,EAAWqY,QAEpB,KAAK9B,EAAkBI,SACnB,MAAO,CAAC,IAAI/B,EAAS3pB,QAAQvB,KAAK+R,MAAMvC,EAAOE,MAAO1P,KAAK+R,MAAM2c,EAA0B/e,IAAMjU,EAASmM,IAAK,IAAIqjB,EAAS3pB,QAAQvB,KAAK+R,MAAMvC,EAAOE,KAAOF,EAAO1I,OAAQ9G,KAAK+R,MAAM2c,EAA0B/e,IAAMjU,EAASmM,IAAK,IAAIqjB,EAAS3pB,QAAQvB,KAAK+R,MAAMvC,EAAOE,KAAOF,EAAO1I,OAAQ9G,KAAK+R,MAAMY,EAAK5L,OAAS2nB,EAA0B/e,IAAMjU,EAASmM,IAAK,IAAIqjB,EAAS3pB,QAAQvB,KAAK+R,MAAMvC,EAAOE,MAAO1P,KAAK+R,MAAMY,EAAK5L,OAAS2nB,EAA0B/e,IAAMjU,EAASmM,KAC7d,KAAKglB,EAAkBK,SACnB,MAAO,CAAC,IAAIhC,EAAS3pB,QAAQvB,KAAK+R,MAAM2c,EAA0Bhf,KAAOhU,EAASkM,GAAI5H,KAAK+R,MAAMvC,EAAOG,MAAO,IAAIub,EAAS3pB,QAAQvB,KAAK+R,MAAM2c,EAA0Bhf,KAAOhU,EAASkM,EAAI+K,EAAK7L,OAAQ9G,KAAK+R,MAAMvC,EAAOG,MAAO,IAAIub,EAAS3pB,QAAQvB,KAAK+R,MAAM2c,EAA0Bhf,KAAOhU,EAASkM,EAAI+K,EAAK7L,OAAQ9G,KAAK+R,MAAMvC,EAAOzI,OAASyI,EAAOG,MAAO,IAAIub,EAAS3pB,QAAQvB,KAAK+R,MAAM2c,EAA0Bhf,KAAOhU,EAASkM,GAAI5H,KAAK+R,MAAMvC,EAAOzI,OAASyI,EAAOG,OAC3d,KAAKkd,EAAkBG,UACnB,MAAO,CAAC,IAAI9B,EAAS3pB,QAAQvB,KAAK+R,MAAM2c,EAA0Bhf,KAAOhU,EAASkM,GAAI5H,KAAK+R,MAAM2c,EAA0B/e,IAAMjU,EAASmM,IAAK,IAAIqjB,EAAS3pB,QAAQvB,KAAK+R,MAAM2c,EAA0Bhf,KAAOhU,EAASkM,EAAI+K,EAAK7L,OAAQ9G,KAAK+R,MAAM2c,EAA0B/e,IAAMjU,EAASmM,IAAK,IAAIqjB,EAAS3pB,QAAQvB,KAAK+R,MAAM2c,EAA0Bhf,KAAOhU,EAASkM,EAAI+K,EAAK7L,OAAQ9G,KAAK+R,MAAM2c,EAA0B/e,IAAMjU,EAASmM,EAAI8K,EAAK5L,SAAU,IAAImkB,EAAS3pB,QAAQvB,KAAK+R,MAAM2c,EAA0Bhf,KAAOhU,EAASkM,GAAI5H,KAAK+R,MAAM2c,EAA0B/e,IAAMjU,EAASmM,EAAI8K,EAAK5L,UACplB,QACI,MAAO,CAAC,IAAImkB,EAAS3pB,QAAQvB,KAAK+R,MAAMvC,EAAOE,MAAO1P,KAAK+R,MAAMvC,EAAOG,MAAO,IAAIub,EAAS3pB,QAAQvB,KAAK+R,MAAMvC,EAAOE,KAAOF,EAAO1I,OAAQ9G,KAAK+R,MAAMvC,EAAOG,MAAO,IAAIub,EAAS3pB,QAAQvB,KAAK+R,MAAMvC,EAAOE,KAAOF,EAAO1I,OAAQ9G,KAAK+R,MAAMvC,EAAOzI,OAASyI,EAAOG,MAAO,IAAIub,EAAS3pB,QAAQvB,KAAK+R,MAAMvC,EAAOE,MAAO1P,KAAK+R,MAAMvC,EAAOzI,OAASyI,EAAOG,OAEvW,EAEsB1Y,EAAQ8uB,gBAAkB,SAAyB5qB,EAAO+G,GAC5E,MAAO,CACH0B,gBAAiB,IAAI+S,EAAQpV,QAAQpG,EAAMyI,iBAC3Cd,gBAAiB8rB,EAAsBzzB,EAAO+G,GAC9C2sB,eAAgBf,EAAoB3yB,EAAM0zB,gBAC1CX,iBAAkBY,EAAsB3zB,EAAM+yB,kBAEtD,EAE0B,SAA6BW,GACnD,OAAQA,GACJ,IAAK,cACD,OAAOlC,EAAgBa,YAC3B,IAAK,cACD,OAAOb,EAAgBc,YAE/B,OAAOd,EAAgBY,UAC3B,GAEIuB,EAAwB,SAA+BZ,GACvD,OAAQA,GACJ,IAAK,cACD,OAAOxB,EAAkBc,YAC7B,IAAK,cACD,OAAOd,EAAkBe,YAEjC,OAAOf,EAAkBa,UAC7B,EAEIwB,EAAwB,SAA+BC,GACvD,OAAQA,EAAiBxa,QACrB,IAAK,YACD,OAAOqY,EAAkBG,UAC7B,IAAK,WACL,IAAK,mBACD,OAAOH,EAAkBI,SAC7B,IAAK,WACL,IAAK,mBACD,OAAOJ,EAAkBK,SAC7B,IAAK,SACD,OAAOL,EAAkBE,OAOjC,OAAOF,EAAkBE,MAC7B,EAEI6B,EAAwB,SAA+BzzB,EAAO+G,GAC9D,IAAI+sB,EAAU5zB,EAAqBF,EAAM2H,iBAAiBC,KAAI,SAAUD,GACpE,GAA+B,QAA3BA,EAAgBE,OAAkB,CAClC,IAAIpE,EAAMsD,EAAeonB,UAAUxmB,EAAgBI,KAAK,IACxDJ,EAAgBI,KAAOtE,EAAM,CAACA,GAAO,EACzC,CACA,OAAOkE,CACX,IACIosB,EAAY/zB,EAAMg0B,mBAAmBhW,MAAM,KAC3CiW,EAAUj0B,EAAM6zB,iBAAiB7V,MAAM,KACvCkW,EAAQl0B,EAAMm0B,eAAenW,MAAM,KAEvC,OAAO8V,EAAQlsB,KAAI,SAAUunB,EAAQzqB,GACjC,IAAI8S,GAAQ0c,EAAMxvB,IAAUstB,GAAM3Y,OAAO2E,MAAM,KAAKpW,IAAIwsB,GACpD7zB,GAAYwzB,EAAUrvB,IAAUstB,GAAM3Y,OAAO2E,MAAM,KAAKpW,IAAIysB,GAEhE,MAAO,CACHlF,OAAQA,EACRqE,OAAQI,EAAgD,iBAAnBK,EAAQvvB,GAAsBuvB,EAAQvvB,GAASuvB,EAAQ,IAC5Fzc,KAAMA,EAAKnX,OAAS,EAAI,CAACmX,EAAK,GAAIgb,GAAa,CAAChb,EAAK,GAAIA,EAAK,IAC9DjX,SAAUA,EAASF,OAAS,EAAI,CAACE,EAAS,GAAIA,EAAS,IAAM,CAACA,EAAS,GAAIA,EAAS,IAE5F,GACJ,EAEI6zB,EAAsB,SAA6B5c,GACnD,MAAgB,SAATA,EAAkBgb,EAAY,IAAID,EAAe/a,EAC5D,EAEI6c,EAAyB,SAAgC9zB,GACzD,OAAQA,GACJ,IAAK,SACL,IAAK,QACD,OAAO,IAAIuc,EAAS1W,QAAQ,QAChC,IAAK,OACL,IAAK,MACD,OAAO,IAAI0W,EAAS1W,QAAQ,MAChC,IAAK,OACD,OAAO,IAAI0W,EAAS1W,QAAQ,KAEpC,OAAO,IAAI0W,EAAS1W,QAAQ7F,EAChC,EAEIL,EAAuBpE,EAAQoE,qBAAuB,SAA8B8sB,GACpF,IAAIsH,EAAa,OACbC,EAAU,GAEVxsB,EAAO,GACPF,EAAS,GACT2sB,EAAQ,KACRC,EAAa,GACbC,EAAO,EACPC,EAAW,EAEXC,EAAe,WACf,IAAItsB,EAAS,GACb,GAAIT,EAAQ,CACwB,MAA5B4sB,EAAWhW,OAAO,EAAG,KACrBgW,EAAaA,EAAWhW,OAAO,EAAGgW,EAAWp0B,OAAS,IAGtDo0B,GACA1sB,EAAKjG,KAAK2yB,EAAWpb,QAGzB,IAAIwb,EAAWhtB,EAAO0R,QAAQ,IAAK,GAAK,EACZ,MAAxB1R,EAAO4W,OAAO,EAAG,IAAcoW,EAAW,IAC1CvsB,EAAST,EAAO4W,OAAO,EAAGoW,GAAU5Y,cACpCpU,EAASA,EAAO4W,OAAOoW,IAGZ,UADfhtB,EAASA,EAAOoU,gBAEZsY,EAAQzyB,KAAK,CACTwG,OAAQA,EACRT,OAAQA,EACRE,KAAMA,GAGlB,CACAA,EAAO,GACPF,EAAS4sB,EAAa,EAC1B,EA8DA,OA5DAzH,EAAMhP,MAAM,IAAI7I,SAAQ,SAAU+Q,GAC9B,GAAa,IAATwO,IAAcJ,EAAWpkB,KAAKgW,GAAlC,CAGA,OAAQA,GACJ,IAAK,IACIsO,EAEMA,IAAUtO,IACjBsO,EAAQ,MAFRA,EAAQtO,EAIZ,MACJ,IAAK,IACD,GAAIsO,EACA,MACG,GAAa,IAATE,EAEP,YADAA,EAAO,GAGPC,IAEJ,MACJ,IAAK,IACD,GAAIH,EACA,MACG,GAAa,IAATE,EAAY,CACnB,GAAiB,IAAbC,EAGA,OAFAD,EAAO,OACPE,IAGAD,GAER,CACA,MAEJ,IAAK,IACD,GAAIH,EACA,MACG,GAAa,IAATE,EAEP,YADAE,IAEG,GAAa,IAATF,GACU,IAAbC,IAAmB9sB,EAAO6L,MAAM,UAGhC,OAFA3L,EAAKjG,KAAK2yB,EAAWpb,aACrBob,EAAa,IAOhB,IAATC,EACA7sB,GAAUqe,EAEVuO,GAAcvO,CApDlB,CAsDJ,IAEA0O,IACOL,CACX,C,kBC9VA34B,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQiwB,aAAejwB,EAAQq3B,mBAAgB/2B,EAE/C,IAIgC0E,EAF5Bgc,GAE4Bhc,EAJlB,EAAQ,SAI+BA,EAAIE,WAAaF,EAAM,CAAEsF,QAAStF,GAEnEhF,EAAQq3B,cAAgB,CACxCzX,IAAK,EACLC,MAAO,EACPC,OAAQ,EACRC,KAAM,GAJV,IAOIC,EAAQ,CAAC,MAAO,QAAS,SAAU,QAEpBhgB,EAAQiwB,aAAe,SAAsB/rB,GAC5D,OAAO8b,EAAMlU,KAAI,SAAUsU,GACvB,OAAO,IAAIY,EAAS1W,QAAQpG,EAAMG,iBAAiB,WAAa+b,GACpE,GACJ,C,gBCxBAtgB,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEX,IAAI+4B,EAAQ,uCAEKh5B,EAAQ4nB,WAAa,SAAoBD,GACtD,IAAI/P,EAAQ+P,EAAM/P,MAAMohB,GAExB,GAAIphB,EAAO,CACP,IAAI3X,EAAQwgB,WAAW7I,EAAM,IAC7B,OAAQA,EAAM,GAAGuI,eACb,IAAK,MACD,OAAOpX,KAAKif,GAAK/nB,EAAQ,IAC7B,IAAK,OACD,OAAO8I,KAAKif,GAAK,IAAM/nB,EAC3B,IAAK,MACD,OAAOA,EACX,IAAK,OACD,OAAiB,EAAV8I,KAAKif,GAAS/nB,EAEjC,CAEA,OAAO,IACX,C,kBCvBAH,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQixB,iBAAmBjxB,EAAQwxB,wBAA0BxxB,EAAQg3B,uBAAyBh3B,EAAQi5B,mBAAqBj5B,EAAQ6zB,kBAAoB7zB,EAAQk5B,oBAAsBl5B,EAAQm3B,oBAAsBn3B,EAAQ4P,YAAc5P,EAAQ0kB,YAASpkB,EAE1P,IAAI6G,EAAe,WAAc,SAASC,EAAiBC,EAAQC,GAAS,IAAK,IAAIC,EAAI,EAAGA,EAAID,EAAM/C,OAAQgD,IAAK,CAAE,IAAIC,EAAaF,EAAMC,GAAIC,EAAW1C,WAAa0C,EAAW1C,aAAc,EAAO0C,EAAWC,cAAe,EAAU,UAAWD,IAAYA,EAAWE,UAAW,GAAM5H,OAAOC,eAAesH,EAAQG,EAAWG,IAAKH,EAAa,CAAE,CAAE,OAAO,SAAUI,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYT,EAAiBQ,EAAYG,UAAWF,GAAiBC,GAAaV,EAAiBQ,EAAaE,GAAqBF,CAAa,CAAG,CAA7hB,GAIfqsB,EAAW9pB,EAFD,EAAQ,QAMlBgvB,EAAgBhvB,EAFD,EAAQ,QAI3B,SAASA,EAAuBnF,GAAO,OAAOA,GAAOA,EAAIE,WAAaF,EAAM,CAAEsF,QAAStF,EAAO,CAI9F,IAQI0f,EAAS1kB,EAAQ0kB,OAAS,WAC1B,SAASA,EAAO/T,EAAGC,EAAGwoB,EAAGC,IAX7B,SAAyBhwB,EAAUzB,GAAe,KAAMyB,aAAoBzB,GAAgB,MAAM,IAAI0B,UAAU,oCAAwC,CAYhJC,CAAgBC,KAAMkb,GAEtBlb,KAAKiP,KAAO9H,EACZnH,KAAKkP,IAAM9H,EACXpH,KAAKqG,MAAQupB,EACb5vB,KAAKsG,OAASupB,CAClB,CASA,OAPAlyB,EAAaud,EAAQ,KAAM,CAAC,CACxB/c,IAAK,iBACL1H,MAAO,SAAwBq5B,EAAYpoB,EAASE,GAChD,OAAO,IAAIsT,EAAO4U,EAAW7gB,KAAOvH,EAASooB,EAAW5gB,IAAMtH,EAASkoB,EAAWzpB,MAAOypB,EAAWxpB,OACxG,KAGG4U,CACX,CAlB8B,GAkE1B6U,GA9Ccv5B,EAAQ4P,YAAc,SAAqBnE,EAAMyF,EAASE,GACxE,OAAOsT,EAAOG,eAAepZ,EAAK+O,wBAAyBtJ,EAASE,EACxE,EAE0BpR,EAAQm3B,oBAAsB,SAA6B5e,EAAQihB,GACzF,OAAO,IAAI9U,EAAOnM,EAAOE,KAAO+gB,EA9BzB,GA8BuChZ,YAAajI,EAAOG,IAAM8gB,EAjClE,GAiC+EhZ,YAAajI,EAAO1I,OAAS2pB,EAhC1G,GAgCyHhZ,YAAcgZ,EA9BxI,GA8BsJhZ,aAAcjI,EAAOzI,QAAU0pB,EAjCtL,GAiCmMhZ,YAAcgZ,EA/B9M,GA+B8NhZ,aAC3O,EAE0BxgB,EAAQk5B,oBAAsB,SAA6B3gB,EAAQyX,EAASwJ,GAElG,IAAIjC,EAAavH,EAtCX,GAsCwB/vB,MAC1Bq3B,EAAetH,EAtCX,GAsC0B/vB,MAC9Bu3B,EAAgBxH,EAtCX,GAsC2B/vB,MAChCm3B,EAAcpH,EAtCX,GAsCyB/vB,MAEhC,OAAO,IAAIykB,EAAOnM,EAAOE,KAAO2e,EAAcoC,EAxCvC,GAwCqDhZ,YAAajI,EAAOG,IAAM6e,EAAaiC,EA3C7F,GA2C0GhZ,YAAajI,EAAO1I,OAAS2pB,EA1CrI,GA0CoJhZ,YAAcgZ,EAxCnK,GAwCiLhZ,YAAc4W,EAAcE,GAAe/e,EAAOzI,QAAU0pB,EA3C9O,GA2C2PhZ,YAAcgZ,EAzCtQ,GAyCsRhZ,YAAc+W,EAAaC,GAC9T,EAEwBx3B,EAAQ6zB,kBAAoB,SAA2B5mB,GAC3E,IAAIiH,EAAOjH,EAASiH,KAChB5I,EAAkB2B,EAAS3B,gBAE/B,IAAK4I,IAAS5I,EACV,MAAM,IAAImuB,MAA8E,IAE5F,IAAI5pB,EAAQ9G,KAAKshB,IAAIthB,KAAKshB,IAAInW,EAAKwlB,YAAapuB,EAAgBouB,aAAc3wB,KAAKshB,IAAInW,EAAKylB,YAAaruB,EAAgBquB,aAAc5wB,KAAKshB,IAAInW,EAAK0lB,YAAatuB,EAAgBsuB,cAE9K9pB,EAAS/G,KAAKshB,IAAIthB,KAAKshB,IAAInW,EAAK2lB,aAAcvuB,EAAgBuuB,cAAe9wB,KAAKshB,IAAInW,EAAK4lB,aAAcxuB,EAAgBwuB,cAAe/wB,KAAKshB,IAAInW,EAAK6lB,aAAczuB,EAAgByuB,eAExL,OAAO,IAAIrV,EAAO,EAAG,EAAG7U,EAAOC,EACnC,EAEyB9P,EAAQi5B,mBAAqB,SAA4BnC,EAAQkD,GACtF,OAAQA,GACJ,KA9DE,EA+DE,OAAOT,EAAqBzC,EAAOmD,aAAcnD,EAAOoD,aAAcpD,EAAOqD,cAAerD,EAAOsD,eACvG,KA/DI,EAgEA,OAAOb,EAAqBzC,EAAOqD,cAAerD,EAAOsD,cAAetD,EAAOuD,iBAAkBvD,EAAOwD,kBAC5G,KAhEK,EAiED,OAAOf,EAAqBzC,EAAOuD,iBAAkBvD,EAAOwD,iBAAkBxD,EAAOyD,gBAAiBzD,EAAO0D,iBAEjH,QACI,OAAOjB,EAAqBzC,EAAOyD,gBAAiBzD,EAAO0D,gBAAiB1D,EAAOmD,aAAcnD,EAAOoD,cAEpH,EAE2B,SAA8BO,EAAQC,EAAQC,EAAQC,GAC7E,IAAIC,EAAO,GAyBX,OAxBIJ,aAAkBtB,EAAc7uB,QAChCuwB,EAAK70B,KAAKy0B,EAAOK,UAAU,IAAK,IAEhCD,EAAK70B,KAAKy0B,GAGVE,aAAkBxB,EAAc7uB,QAChCuwB,EAAK70B,KAAK20B,EAAOG,UAAU,IAAK,IAEhCD,EAAK70B,KAAK20B,GAGVC,aAAkBzB,EAAc7uB,QAChCuwB,EAAK70B,KAAK40B,EAAOE,UAAU,IAAK,GAAMC,WAEtCF,EAAK70B,KAAK40B,GAGVF,aAAkBvB,EAAc7uB,QAChCuwB,EAAK70B,KAAK00B,EAAOI,UAAU,IAAK,GAAOC,WAEvCF,EAAK70B,KAAK00B,GAGPG,CACX,GAuDIG,GArDyBh7B,EAAQg3B,uBAAyB,SAAgCF,GAC1F,MAAO,CAACA,EAAOmD,aAAcnD,EAAOqD,cAAerD,EAAOuD,iBAAkBvD,EAAOyD,gBACvF,EAE8Bv6B,EAAQwxB,wBAA0B,SAAiCsF,GAC7F,MAAO,CAACA,EAAOoD,aAAcpD,EAAOsD,cAAetD,EAAOwD,iBAAkBxD,EAAO0D,gBACvF,EAEuBx6B,EAAQixB,iBAAmB,SAA0B1Y,EAAQihB,EAASxK,GACzF,IAAIiM,EAAMjM,EAAagM,EAAOE,UA3G1B,GA2GuC9T,iBAAiB7O,EAAO1I,OAC/DsrB,EAAMnM,EAAagM,EAAOE,UA3G1B,GA2GuC9T,iBAAiB7O,EAAOzI,QAC/DsrB,EAAMpM,EAAagM,EAAOK,WA7G1B,GA6GwCjU,iBAAiB7O,EAAO1I,OAChEyrB,EAAMtM,EAAagM,EAAOK,WA7G1B,GA6GwCjU,iBAAiB7O,EAAOzI,QAChEyrB,EAAMvM,EAAagM,EAAOQ,cA/G1B,GA+G2CpU,iBAAiB7O,EAAO1I,OACnE4rB,EAAMzM,EAAagM,EAAOQ,cA/G1B,GA+G2CpU,iBAAiB7O,EAAOzI,QACnE4rB,EAAM1M,EAAagM,EAAOW,aAjH1B,GAiH0CvU,iBAAiB7O,EAAO1I,OAClE+rB,EAAM5M,EAAagM,EAAOW,aAjH1B,GAiH0CvU,iBAAiB7O,EAAOzI,QAElE+rB,EAAU,GACdA,EAAQ71B,MAAMi1B,EAAMG,GAAO7iB,EAAO1I,OAClCgsB,EAAQ71B,MAAM01B,EAAMH,GAAOhjB,EAAO1I,OAClCgsB,EAAQ71B,MAAMm1B,EAAMS,GAAOrjB,EAAOzI,QAClC+rB,EAAQ71B,MAAMs1B,EAAMG,GAAOljB,EAAOzI,QAClC,IAAIgsB,EAAY/yB,KAAKshB,IAAI0R,MAAMhzB,KAAM8yB,GAEjCC,EAAY,IACZb,GAAOa,EACPX,GAAOW,EACPV,GAAOU,EACPR,GAAOQ,EACPP,GAAOO,EACPL,GAAOK,EACPJ,GAAOI,EACPF,GAAOE,GAGX,IAAIE,EAAWzjB,EAAO1I,MAAQurB,EAC1Ba,EAAc1jB,EAAOzI,OAAS2rB,EAC9BS,EAAc3jB,EAAO1I,MAAQ0rB,EAC7BY,EAAa5jB,EAAOzI,OAAS8rB,EAEjC,MAAO,CACH3B,aAAcgB,EAAM,GAAKE,EAAM,EAAIiB,EAAe7jB,EAAOE,KAAMF,EAAOG,IAAKuiB,EAAKE,EAAKH,EAAOE,UAAY,IAAIjH,EAAS3pB,QAAQiO,EAAOE,KAAMF,EAAOG,KACjJwhB,aAAce,EAAM,GAAKE,EAAM,EAAIiB,EAAe7jB,EAAOE,KAAO+gB,EA/I7D,GA+I2EhZ,YAAajI,EAAOG,IAAM8gB,EAlJtG,GAkJmHhZ,YAAazX,KAAKshB,IAAI,EAAG4Q,EAAMzB,EA/IjJ,GA+I+JhZ,aAAczX,KAAKshB,IAAI,EAAG8Q,EAAM3B,EAlJhM,GAkJ6MhZ,aAAcwa,EAAOE,UAAY,IAAIjH,EAAS3pB,QAAQiO,EAAOE,KAAO+gB,EA/IhR,GA+I8RhZ,YAAajI,EAAOG,IAAM8gB,EAlJzT,GAkJsUhZ,aACxU2Z,cAAeiB,EAAM,GAAKE,EAAM,EAAIc,EAAe7jB,EAAOE,KAAOujB,EAAUzjB,EAAOG,IAAK0iB,EAAKE,EAAKN,EAAOK,WAAa,IAAIpH,EAAS3pB,QAAQiO,EAAOE,KAAOF,EAAO1I,MAAO0I,EAAOG,KAC7K0hB,cAAegB,EAAM,GAAKE,EAAM,EAAIc,EAAe7jB,EAAOE,KAAO1P,KAAKof,IAAI6T,EAAUzjB,EAAO1I,MAAQ2pB,EAjJhG,GAiJ8GhZ,aAAcjI,EAAOG,IAAM8gB,EApJ1I,GAoJuJhZ,YAAawb,EAAWzjB,EAAO1I,MAAQ2pB,EAjJ7L,GAiJ2MhZ,YAAc,EAAI4a,EAAM5B,EAjJnO,GAiJiPhZ,YAAa8a,EAAM9B,EApJrQ,GAoJkRhZ,YAAawa,EAAOK,WAAa,IAAIpH,EAAS3pB,QAAQiO,EAAOE,KAAOF,EAAO1I,MAAQ2pB,EAnJnW,GAmJkXhZ,YAAajI,EAAOG,IAAM8gB,EApJ9Y,GAoJ2ZhZ,aAC7Z6Z,iBAAkBkB,EAAM,GAAKE,EAAM,EAAIW,EAAe7jB,EAAOE,KAAOyjB,EAAa3jB,EAAOG,IAAMujB,EAAaV,EAAKE,EAAKT,EAAOQ,cAAgB,IAAIvH,EAAS3pB,QAAQiO,EAAOE,KAAOF,EAAO1I,MAAO0I,EAAOG,IAAMH,EAAOzI,QACjNwqB,iBAAkBiB,EAAM,GAAKE,EAAM,EAAIW,EAAe7jB,EAAOE,KAAO1P,KAAKof,IAAI+T,EAAa3jB,EAAO1I,MAAQ2pB,EAnJtG,GAmJoHhZ,aAAcjI,EAAOG,IAAM3P,KAAKof,IAAI8T,EAAa1jB,EAAOzI,OAAS0pB,EAtJtL,GAsJmMhZ,aAAczX,KAAKshB,IAAI,EAAGkR,EAAM/B,EArJjO,GAqJgPhZ,aAAcib,EAAMjC,EApJnQ,GAoJmRhZ,YAAawa,EAAOQ,cAAgB,IAAIvH,EAAS3pB,QAAQiO,EAAOE,KAAOF,EAAO1I,MAAQ2pB,EArJ1W,GAqJyXhZ,YAAajI,EAAOG,IAAMH,EAAOzI,OAAS0pB,EApJla,GAoJkbhZ,aACvb+Z,gBAAiBmB,EAAM,GAAKE,EAAM,EAAIQ,EAAe7jB,EAAOE,KAAMF,EAAOG,IAAMyjB,EAAYT,EAAKE,EAAKZ,EAAOW,aAAe,IAAI1H,EAAS3pB,QAAQiO,EAAOE,KAAMF,EAAOG,IAAMH,EAAOzI,QACjL0qB,gBAAiBkB,EAAM,GAAKE,EAAM,EAAIQ,EAAe7jB,EAAOE,KAAO+gB,EArJhE,GAqJ8EhZ,YAAajI,EAAOG,IAAMyjB,EAAYpzB,KAAKshB,IAAI,EAAGqR,EAAMlC,EArJtI,GAqJoJhZ,aAAcob,EAAMpC,EAtJtK,GAsJsLhZ,YAAawa,EAAOW,aAAe,IAAI1H,EAAS3pB,QAAQiO,EAAOE,KAAO+gB,EArJ9P,GAqJ4QhZ,YAAajI,EAAOG,IAAMH,EAAOzI,OAAS0pB,EAtJpT,GAsJoUhZ,aAEjV,EAEa,CACT0a,SAAU,EACVG,UAAW,EACXG,aAAc,EACdG,YAAa,IAGbS,EAAiB,SAAwBzrB,EAAGC,EAAGyrB,EAAIC,EAAI73B,GACvD,IAAI83B,GAAcxzB,KAAK4iB,KAAK,GAAK,GAAK,EAA1B,EACR6Q,EAAKH,EAAKE,EACVE,EAAKH,EAAKC,EACVG,EAAK/rB,EAAI0rB,EACTM,EAAK/rB,EAAI0rB,EAEb,OAAQ73B,GACJ,KAAKu2B,EAAOE,SACR,OAAO,IAAI/B,EAAc7uB,QAAQ,IAAI2pB,EAAS3pB,QAAQqG,EAAGgsB,GAAK,IAAI1I,EAAS3pB,QAAQqG,EAAGgsB,EAAKF,GAAK,IAAIxI,EAAS3pB,QAAQoyB,EAAKF,EAAI5rB,GAAI,IAAIqjB,EAAS3pB,QAAQoyB,EAAI9rB,IAC/J,KAAKoqB,EAAOK,UACR,OAAO,IAAIlC,EAAc7uB,QAAQ,IAAI2pB,EAAS3pB,QAAQqG,EAAGC,GAAI,IAAIqjB,EAAS3pB,QAAQqG,EAAI6rB,EAAI5rB,GAAI,IAAIqjB,EAAS3pB,QAAQoyB,EAAIC,EAAKF,GAAK,IAAIxI,EAAS3pB,QAAQoyB,EAAIC,IAC9J,KAAK3B,EAAOQ,aACR,OAAO,IAAIrC,EAAc7uB,QAAQ,IAAI2pB,EAAS3pB,QAAQoyB,EAAI9rB,GAAI,IAAIqjB,EAAS3pB,QAAQoyB,EAAI9rB,EAAI6rB,GAAK,IAAIxI,EAAS3pB,QAAQqG,EAAI6rB,EAAIG,GAAK,IAAI1I,EAAS3pB,QAAQqG,EAAGgsB,IAC9J,KAAK3B,EAAOW,YACZ,QACI,OAAO,IAAIxC,EAAc7uB,QAAQ,IAAI2pB,EAAS3pB,QAAQoyB,EAAIC,GAAK,IAAI1I,EAAS3pB,QAAQoyB,EAAKF,EAAIG,GAAK,IAAI1I,EAAS3pB,QAAQqG,EAAGC,EAAI6rB,GAAK,IAAIxI,EAAS3pB,QAAQqG,EAAGC,IAEvK,C,gBCxMA9Q,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEQD,EAAQ4E,aAAe,SAAsBO,GAI5D,IAHA,IAAIy3B,EAAa,GACbr1B,EAAI,EACJhD,EAASY,EAAIZ,OACVgD,EAAIhD,GAAQ,CACf,IAAItE,EAAQkF,EAAI03B,WAAWt1B,KAC3B,GAAItH,GAAS,OAAUA,GAAS,OAAUsH,EAAIhD,EAAQ,CAClD,IAAIu4B,EAAQ33B,EAAI03B,WAAWt1B,KACF,QAAZ,MAARu1B,GACDF,EAAW52B,OAAe,KAAR/F,IAAkB,KAAe,KAAR68B,GAAiB,QAE5DF,EAAW52B,KAAK/F,GAChBsH,IAER,MACIq1B,EAAW52B,KAAK/F,EAExB,CACA,OAAO28B,CACX,EAEoB58B,EAAQ2E,cAAgB,WACxC,GAAIo4B,OAAOp4B,cACP,OAAOo4B,OAAOp4B,cAAco3B,MAAMgB,OAAQ3J,WAG9C,IAAI7uB,EAAS6uB,UAAU7uB,OACvB,IAAKA,EACD,MAAO,GAOX,IAJA,IAAIy4B,EAAY,GAEZp0B,GAAS,EACT6F,EAAS,KACJ7F,EAAQrE,GAAQ,CACrB,IAAIkF,EAAY2pB,UAAU7uB,QAAUqE,OAAQtI,EAAY8yB,UAAUxqB,GAC9Da,GAAa,MACbuzB,EAAUh3B,KAAKyD,IAEfA,GAAa,MACbuzB,EAAUh3B,KAAyB,OAAnByD,GAAa,IAAcA,EAAY,KAAQ,SAE/Db,EAAQ,IAAMrE,GAAUy4B,EAAUz4B,OAAS,SAC3CkK,GAAUsuB,OAAOE,aAAalB,MAAMgB,OAAQC,GAC5CA,EAAUz4B,OAAS,EAE3B,CACA,OAAOkK,CACX,EAMA,IAvDA,IAsDIyuB,EAA+B,oBAAfC,WAA6B,GAAK,IAAIA,WAAW,KAC5D51B,EAAI,EAAGA,EAAI61B,GAAc71B,IAC9B21B,EALQ,mEAKKL,WAAWt1B,IAAMA,EAGrBvH,EAAQmI,OAAS,SAAgBF,GAC1C,IAAIo1B,EAA+B,IAAhBp1B,EAAO1D,OACtBgR,EAAMtN,EAAO1D,OACbgD,OAAI,EACJ+1B,EAAI,EACJC,OAAW,EACXC,OAAW,EACXC,OAAW,EACXC,OAAW,EAEmB,MAA9Bz1B,EAAOA,EAAO1D,OAAS,KACvB84B,IACkC,MAA9Bp1B,EAAOA,EAAO1D,OAAS,IACvB84B,KAIR,IAAIn1B,EAAgC,oBAAhBy1B,aAAqD,oBAAfR,iBAAoE,IAA/BA,WAAWp1B,UAAU9B,MAAwB,IAAI03B,YAAYN,GAAgB,IAAIh1B,MAAMg1B,GAClLO,EAAQv1B,MAAMC,QAAQJ,GAAUA,EAAS,IAAIi1B,WAAWj1B,GAE5D,IAAKX,EAAI,EAAGA,EAAIgO,EAAKhO,GAAK,EACtBg2B,EAAWL,EAAOj1B,EAAO40B,WAAWt1B,IACpCi2B,EAAWN,EAAOj1B,EAAO40B,WAAWt1B,EAAI,IACxCk2B,EAAWP,EAAOj1B,EAAO40B,WAAWt1B,EAAI,IACxCm2B,EAAWR,EAAOj1B,EAAO40B,WAAWt1B,EAAI,IAExCq2B,EAAMN,KAAOC,GAAY,EAAIC,GAAY,EACzCI,EAAMN,MAAmB,GAAXE,IAAkB,EAAIC,GAAY,EAChDG,EAAMN,MAAmB,EAAXG,IAAiB,EAAe,GAAXC,EAGvC,OAAOx1B,CACX,EAEsBlI,EAAQ0I,gBAAkB,SAAyBR,GAGrE,IAFA,IAAI3D,EAAS2D,EAAO3D,OAChBq5B,EAAQ,GACH5mB,EAAK,EAAGA,EAAKzS,EAAQyS,GAAM,EAChC4mB,EAAM53B,KAAKkC,EAAO8O,EAAK,IAAM,EAAI9O,EAAO8O,IAE5C,OAAO4mB,CACX,EAEsB59B,EAAQuI,gBAAkB,SAAyBL,GAGrE,IAFA,IAAI3D,EAAS2D,EAAO3D,OAChBq5B,EAAQ,GACHC,EAAM,EAAGA,EAAMt5B,EAAQs5B,GAAO,EACnCD,EAAM53B,KAAKkC,EAAO21B,EAAM,IAAM,GAAK31B,EAAO21B,EAAM,IAAM,GAAK31B,EAAO21B,EAAM,IAAM,EAAI31B,EAAO21B,IAE7F,OAAOD,CACX,C,gBChHA99B,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAGX,IAAIkH,EAAe,WAAc,SAASC,EAAiBC,EAAQC,GAAS,IAAK,IAAIC,EAAI,EAAGA,EAAID,EAAM/C,OAAQgD,IAAK,CAAE,IAAIC,EAAaF,EAAMC,GAAIC,EAAW1C,WAAa0C,EAAW1C,aAAc,EAAO0C,EAAWC,cAAe,EAAU,UAAWD,IAAYA,EAAWE,UAAW,GAAM5H,OAAOC,eAAesH,EAAQG,EAAWG,IAAKH,EAAa,CAAE,CAAE,OAAO,SAAUI,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYT,EAAiBQ,EAAYG,UAAWF,GAAiBC,GAAaV,EAAiBQ,EAAaE,GAAqBF,CAAa,CAAG,CAA7hB,GAIfk2B,EAAS,WACT,SAASA,EAAOC,EAASC,EAAIxP,IAHjC,SAAyBnlB,EAAUzB,GAAe,KAAMyB,aAAoBzB,GAAgB,MAAM,IAAI0B,UAAU,oCAAwC,CAIhJC,CAAgBC,KAAMs0B,GAEtBt0B,KAAKu0B,QAA4B,oBAAX7yB,QAA0B6yB,EAChDv0B,KAAKglB,MAAQA,GAAgBpX,KAAKC,MAClC7N,KAAKw0B,GAAKA,CACd,CAqCA,OAnCA72B,EAAa22B,EAAQ,CAAC,CAClBn2B,IAAK,QACL1H,MAAO,SAAe+9B,GAClB,OAAO,IAAIF,EAAOt0B,KAAKu0B,QAASC,EAAIx0B,KAAKglB,MAC7C,GAID,CACC7mB,IAAK,MACL1H,MAAO,WACH,GAAIuJ,KAAKu0B,SAAW7yB,OAAO+yB,SAAW/yB,OAAO+yB,QAAQrK,IAAK,CACtD,IAAK,IAAIsK,EAAO9K,UAAU7uB,OAAQ0H,EAAO5D,MAAM61B,GAAOC,EAAO,EAAGA,EAAOD,EAAMC,IACzElyB,EAAKkyB,GAAQ/K,UAAU+K,GAG3BC,SAASr2B,UAAUs2B,KAAKzsB,KAAK1G,OAAO+yB,QAAQrK,IAAK1oB,OAAO+yB,SAASlC,MAAM7wB,OAAO+yB,QAAS,CAAC7mB,KAAKC,MAAQ7N,KAAKglB,MAAQ,KAAMhlB,KAAKw0B,GAAK,gBAAkBx0B,KAAKw0B,GAAK,KAAO,gBAAgBhwB,OAAO,GAAG/H,MAAM2L,KAAK3F,EAAM,IACpN,CACJ,GAID,CACCtE,IAAK,QACL1H,MAAO,WACH,GAAIuJ,KAAKu0B,SAAW7yB,OAAO+yB,SAAW/yB,OAAO+yB,QAAQK,MAAO,CACxD,IAAK,IAAIC,EAAQnL,UAAU7uB,OAAQ0H,EAAO5D,MAAMk2B,GAAQC,EAAQ,EAAGA,EAAQD,EAAOC,IAC9EvyB,EAAKuyB,GAASpL,UAAUoL,GAG5BJ,SAASr2B,UAAUs2B,KAAKzsB,KAAK1G,OAAO+yB,QAAQK,MAAOpzB,OAAO+yB,SAASlC,MAAM7wB,OAAO+yB,QAAS,CAAC7mB,KAAKC,MAAQ7N,KAAKglB,MAAQ,KAAMhlB,KAAKw0B,GAAK,gBAAkBx0B,KAAKw0B,GAAK,KAAO,gBAAgBhwB,OAAO,GAAG/H,MAAM2L,KAAK3F,EAAM,IACtN,CACJ,KAGG6xB,CACX,CA7Ca,GA+Cb99B,EAAA,QAAkB89B,C,gBCvDlBh+B,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEX,IAAIw+B,EAAiBz+B,EAAQy+B,eAAiB,CAC1C/9B,KAAM,EACNg+B,UAAW,EACXC,UAAW,EACXC,WAAY,GAGS5+B,EAAQswB,mBAAqB,SAA4BD,GAC9E,OAAQA,GACJ,IAAK,YACD,OAAOoO,EAAeE,UAC1B,IAAK,YACD,OAAOF,EAAeC,UAC1B,IAAK,aACD,OAAOD,EAAeG,WAG9B,OAAOH,EAAe/9B,IAC1B,C,kBCrBAZ,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQ0X,WAAQpX,EAEhB,IAIgC0E,EAF5B6e,GAE4B7e,EAJjB,EAAQ,SAI8BA,EAAIE,WAAaF,EAAM,CAAEsF,QAAStF,GAE3EhF,EAAQ0X,MAAQ,SAAetL,EAAK3B,GAC5C,IAAKA,EAAQ6F,MACT,OAAO3E,QAAQyC,OAAoE,MAEvF,IAAIkC,EAAQ7F,EAAQ6F,MAEpB,OAAO,IAAI3E,SAAQ,SAAUY,EAAS6B,GAClC,IAAIkO,EAAeuH,EAAUvZ,QAAQiS,kBAAoBsH,EAAUvZ,QAAQ8R,sBAAwB,OAAS,OACxGyiB,EAAMhb,EAAUvZ,QAAQiS,iBAAmB,IAAIF,eAAmB,IAAIyiB,eAiC1E,GAhCAD,EAAIrwB,OAAS,WACT,GAAIqwB,aAAexiB,eACf,GAAmB,MAAfwiB,EAAIE,OACJ,GAAqB,SAAjBziB,EACA/P,EAAQsyB,EAAI3wB,cACT,CACH,IAAIG,EAAS,IAAIC,WAEjBD,EAAO0iB,iBAAiB,QAAQ,WAC5B,OAAOxkB,EAAQ8B,EAAOI,OAC1B,IAAG,GAEHJ,EAAO0iB,iBAAiB,SAAS,SAAUzkB,GACvC,OAAO8B,EAAO9B,EAClB,IAAG,GACH+B,EAAOK,cAAcmwB,EAAI3wB,SAC7B,MAEAE,EAAyI,SAG7I7B,EAAQsyB,EAAIG,aAEpB,EAEAH,EAAItwB,QAAUH,EACdywB,EAAI3mB,KAAK,MAAO5H,EAAQ,QAAUgiB,mBAAmBlmB,GAAO,iBAAmBkQ,GAE1D,SAAjBA,GAA2BuiB,aAAexiB,iBAC1CwiB,EAAIviB,aAAeA,GAGnB7R,EAAQ2F,aAAc,CACtB,IAAI6uB,EAAUx0B,EAAQ2F,aACtByuB,EAAII,QAAUA,EACdJ,EAAIK,UAAY,WACZ,OAAO9wB,EAAmH,GAC9H,CACJ,CAEAywB,EAAIM,MACR,GACJ,C,kBC9DAr/B,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQowB,qBAAkB9vB,EAE1B,IAIgC0E,EAF5B0a,GAE4B1a,EAJnB,EAAQ,SAIgCA,EAAIE,WAAaF,EAAM,CAAEsF,QAAStF,GAEnFo6B,EAAS,kBAESp/B,EAAQowB,gBAAkB,SAAyBD,GACrE,GAAmB,SAAfA,GAA+C,iBAAfA,EAChC,OAAO,KAmCX,IAhCA,IAAIkP,EAAe,GACfC,GAAW,EACXrd,EAAS,GACTsd,EAAU,GACVC,EAAY,EACZvY,EAAQ,KAERwY,EAAc,WACVJ,EAAa96B,SACT+6B,EACArd,EAAOjc,KAAKya,WAAW4e,IAEvBpY,EAAQ,IAAIvH,EAAQpV,QAAQ+0B,IAGpCC,GAAW,EACXD,EAAe,EACnB,EAEIK,EAAe,WACXzd,EAAO1d,QAAoB,OAAV0iB,GACjBsY,EAAQv5B,KAAK,CACTihB,MAAOA,EACP0Y,QAAS1d,EAAO,IAAM,EACtB2d,QAAS3d,EAAO,IAAM,EACtB4d,KAAM5d,EAAO,IAAM,IAG3BA,EAAO6d,OAAO,EAAG7d,EAAO1d,QACxB0iB,EAAQ,IACZ,EAES1f,EAAI,EAAGA,EAAI4oB,EAAW5rB,OAAQgD,IAAK,CACxC,IAAI6iB,EAAI+F,EAAW5oB,GACnB,OAAQ6iB,GACJ,IAAK,IACDiV,GAAgBjV,EAChBoV,IACA,MACJ,IAAK,IACDH,GAAgBjV,EAChBoV,IACA,MACJ,IAAK,IACiB,IAAdA,GACAC,IACAC,KAEAL,GAAgBjV,EAEpB,MACJ,IAAK,IACiB,IAAdoV,EACAC,IAEAJ,GAAgBjV,EAEpB,MACJ,QACgC,IAAxBiV,EAAa96B,QAAgB66B,EAAOhrB,KAAKgW,KACzCkV,GAAW,GAEfD,GAAgBjV,EAE5B,CAKA,OAHAqV,IACAC,IAEuB,IAAnBH,EAAQh7B,OACD,KAGJg7B,CACX,C,kBC5FAz/B,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQoxB,oBAAsBpxB,EAAQ8d,oBAAsB9d,EAAQ6d,sBAAwB7d,EAAQ4d,mBAAqB5d,EAAQkvB,qBAAuBlvB,EAAQ6uB,iBAAmB7uB,EAAQ+uB,cAAgB/uB,EAAQmvB,iBAAc7uB,EAEjO,IAEIsc,EAAkBzS,EAFD,EAAQ,QAIzB5J,EAAc,EAAQ,OAEtB6sB,EAAU,EAAQ,OAIlB2S,EAAW51B,EAFD,EAAQ,QAMlB8pB,EAAW9pB,EAFD,EAAQ,QAMlBuV,EAAUvV,EAFD,EAAQ,QAMjB6W,EAAW7W,EAFD,EAAQ,QAQlBnC,GAJU,EAAQ,OAEJ,EAAQ,OAEd,EAAQ,QAEpB,SAASmC,EAAuBnF,GAAO,OAAOA,GAAOA,EAAIE,WAAaF,EAAM,CAAEsF,QAAStF,EAAO,CAE5EhF,EAAQmvB,YAAc,IAAIzP,EAAQpV,QAAQ,CAAC,GAAI,GAAI,KAArE,IACI01B,EAAqB,IAAItgB,EAAQpV,QAAQ,CAAC,IAAK,IAAK,MACpD21B,EAAyB,IAAIvgB,EAAQpV,QAAQ,CAAC,IAAK,IAAK,MACxD41B,EAAe,CACf1f,YAAa,EACbH,YAAa2f,EACb1f,YAAa8M,EAAQ3N,aAAaE,OAUlCwgB,GARgBngC,EAAQ+uB,cAAgB,CAACmR,EAAcA,EAAcA,EAAcA,GAChElgC,EAAQ6uB,iBAAmB,CAC9CliB,gBAAiBszB,EACjBp0B,gBAAiB,GACjB+rB,eAAgBr3B,EAAYm1B,gBAAgBa,YAC5CU,iBAAkB12B,EAAYk1B,kBAAkBc,aAG1B,IAAIvV,EAAS1W,QAAQ,QAC3C81B,EAA4B,CAACD,EAAqBA,GAClDE,EAA4B,CAACD,EAA2BA,EAA2BA,EAA2BA,GAE9GE,EAAyB,IAAItf,EAAS1W,QAAQ,OAC9Ci2B,EAA+B,CAACD,EAAwBA,GACxDE,EAA+B,CAACD,EAA8BA,EAA8BA,EAA8BA,GAqC1HE,GAnCuBzgC,EAAQkvB,qBAAuB,SAA8BzjB,GACpF,MAAqB,UAAdA,EAAKzH,KAAmBq8B,EAA4BG,CAC/D,EAEyBxgC,EAAQ4d,mBAAqB,SAA4BnS,EAAMuR,GACpF,GAAkB,UAAdvR,EAAKzH,MAAkC,aAAdyH,EAAKzH,MAC9B,GAAIyH,EAAKoI,QAAS,CACd,IAAI6H,EAAO3S,KAAKof,IAAInL,EAAUzE,OAAO1I,MAAOmN,EAAUzE,OAAOzI,QAC7DkN,EAAUhE,WAAWhT,KAAmB,aAAdyF,EAAKzH,KAAsB,CAAC,IAAIiwB,EAAS3pB,QAAQ0S,EAAUzE,OAAOE,KAAc,OAAPiD,EAAgBsB,EAAUzE,OAAOG,IAAa,IAAPgD,GAAc,IAAIuY,EAAS3pB,QAAQ0S,EAAUzE,OAAOE,KAAc,IAAPiD,EAAasB,EAAUzE,OAAOG,IAAa,MAAPgD,GAAgB,IAAIuY,EAAS3pB,QAAQ0S,EAAUzE,OAAOE,KAAc,OAAPiD,EAAgBsB,EAAUzE,OAAOG,IAAa,OAAPgD,GAAiB,IAAIuY,EAAS3pB,QAAQ0S,EAAUzE,OAAOE,KAAc,OAAPiD,EAAgBsB,EAAUzE,OAAOG,IAAa,MAAPgD,GAAgB,IAAIuY,EAAS3pB,QAAQ0S,EAAUzE,OAAOE,KAAc,OAAPiD,EAAgBsB,EAAUzE,OAAOG,IAAa,IAAPgD,GAAc,IAAIuY,EAAS3pB,QAAQ0S,EAAUzE,OAAOE,KAAc,IAAPiD,EAAasB,EAAUzE,OAAOG,IAAa,OAAPgD,GAAiB,IAAIuY,EAAS3pB,QAAQ0S,EAAUzE,OAAOE,KAAc,OAAPiD,EAAgBsB,EAAUzE,OAAOG,IAAa,IAAPgD,IAAgB,IAAIqkB,EAASz1B,QAAQ0S,EAAUzE,OAAOE,KAAOiD,EAAO,EAAGsB,EAAUzE,OAAOG,IAAMgD,EAAO,EAAGA,EAAO,GAC70B,OAEA+kB,EAAkBC,EAAcj1B,GAAOA,EAAMuR,GAAW,EAEhE,EAE4Bhd,EAAQ6d,sBAAwB,SAA+BpS,EAAMuR,GAC7FyjB,EAAkBh1B,EAAKxL,MAAOwL,EAAMuR,GAAW,EACnD,EAE0Bhd,EAAQ8d,oBAAsB,SAA6BrS,EAAMuR,GACvF,IAAI2jB,EAASl1B,EAAKhB,QAAQgB,EAAKm1B,eAAiB,GAChDH,EAAkBE,GAASA,EAAOlzB,MAAa,GAAIhC,EAAMuR,GAAW,EACxE,EAE0Bhd,EAAQoxB,oBAAsB,SAA6B7Y,GAQjF,OAPIA,EAAO1I,MAAQ0I,EAAOzI,QACtByI,EAAOE,OAASF,EAAO1I,MAAQ0I,EAAOzI,QAAU,EAChDyI,EAAO1I,MAAQ0I,EAAOzI,QACfyI,EAAO1I,MAAQ0I,EAAOzI,SAC7ByI,EAAOG,MAAQH,EAAOzI,OAASyI,EAAO1I,OAAS,EAC/C0I,EAAOzI,OAASyI,EAAO1I,OAEpB0I,CACX,EAEwB,SAA2BtY,EAAOwL,EAAMuR,EAAW6jB,GACvE,IAAI3sB,EAAOzI,EAAKD,cAAc0I,KAC9B,GAAIjU,EAAMsE,OAAS,GAAK2P,EAAM,CAC1B,IAAIuQ,EAAUhZ,EAAKD,cAAcwD,cAAc,uBAC/C,EAAIhH,EAAM+E,eAAetB,EAAKD,cAAcsF,YAAYU,iBAAiB/F,EAAM,MAAOgZ,GACtFA,EAAQvgB,MAAMO,SAAW,WACzBggB,EAAQvgB,MAAMuU,KAAOuE,EAAUzE,OAAOE,KAAO,KAC7CgM,EAAQvgB,MAAMwU,IAAMsE,EAAUzE,OAAOG,IAAM,KACtCmoB,IACDpc,EAAQvgB,MAAM48B,WAAa,UAE/B,IAAIrzB,EAAOhC,EAAKD,cAAc0G,eAAejS,GAC7CwkB,EAAQvV,YAAYzB,GACpByG,EAAKhF,YAAYuV,GACjBzH,EAAUhE,WAAWhT,KAAK4W,EAAgBtS,QAAQkT,aAAa/P,EAAMuP,IACrE9I,EAAK6G,YAAY0J,EACrB,CACJ,GAEIic,EAAgB,SAAuBj1B,GACvC,IAAIxL,EAAsB,aAAdwL,EAAKzH,KAAsB,IAAIqE,MAAMoD,EAAKxL,MAAMsE,OAAS,GAAGkI,KAAK,KAAYhB,EAAKxL,MAE9F,OAAwB,IAAjBA,EAAMsE,OAAekH,EAAKs1B,aAAe,GAAK9gC,CACzD,C,kBCvHAH,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQkwB,oBAAsBlwB,EAAQghC,qBAAuBhhC,EAAQqkB,gBAAkBrkB,EAAQihC,2BAAwB3gC,EAEvH,IAIgC0E,EAF5B0a,GAE4B1a,EAJnB,EAAQ,SAIgCA,EAAIE,WAAaF,EAAM,CAAEsF,QAAStF,GAEnFi8B,EAAwBjhC,EAAQihC,sBAAwB,CACxDthB,MAAO,EACPuhB,OAAQ,EACRC,OAAQ,EACRC,OAAQ,EACRC,KAAM,GAGNhd,EAAkBrkB,EAAQqkB,gBAAkB,CAC5C3jB,KAAM,MAGNsgC,EAAuBhhC,EAAQghC,qBAAuB,CACtDM,UAAW,EACXC,SAAU,EACVC,aAAc,EACdC,MAAO,GAGPC,EAAY,SAAmBC,GAC/B,OAAQA,GACJ,IAAK,YACD,OAAOX,EAAqBM,UAChC,IAAK,WACD,OAAON,EAAqBO,SAChC,IAAK,eACD,OAAOP,EAAqBQ,aAEpC,OAAOR,EAAqBS,KAChC,EAwB0BzhC,EAAQkwB,oBAAsB,SAA6BhsB,GACjF,IAvB2Dy9B,EAuBvDC,EAtBS,UAD8CD,EAuBVz9B,EAAM09B,mBAAqB19B,EAAM09B,mBAAqB19B,EAAMkgB,gBArBlG,KAGJud,EAAKzf,MAAM,KAAKpW,IAAI41B,GAmB3B,GAA2B,OAAvBE,EACA,OAAOvd,EAAgB3jB,KAG3B,IAAImhC,EAAsB39B,EAAM29B,oBAAsB,IAAIniB,EAAQpV,QAAQpG,EAAM29B,qBAAuB,KACnGC,EArBuB,SAAkC59B,GAC7D,OAAQA,GACJ,IAAK,SACD,OAAO+8B,EAAsBC,OACjC,IAAK,SACD,OAAOD,EAAsBE,OACjC,IAAK,SACD,OAAOF,EAAsBG,OACjC,IAAK,OACD,OAAOH,EAAsBI,KAErC,OAAOJ,EAAsBthB,KACjC,CAS8BoiB,CAAyB79B,EAAM49B,qBAEzD,MAAO,CACHF,mBAAoBA,EACpBC,oBAAqBA,EACrBC,oBAAqBA,EAE7B,C,gBC9EAhiC,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEX,IAAI2xB,EAAQ5xB,EAAQ4xB,MAAQ,CACxBlxB,KAAM,EACNqf,KAAM,EACNF,MAAO,EACPmiB,aAAc,EACdC,WAAY,GAGIjiC,EAAQqvB,cAAgB,SAAuBD,GAC/D,OAAQA,GACJ,IAAK,OACD,OAAOwC,EAAM7R,KACjB,IAAK,QACD,OAAO6R,EAAM/R,MACjB,IAAK,eACD,OAAO+R,EAAMoQ,aACjB,IAAK,aACD,OAAOpQ,EAAMqQ,WAErB,OAAOrQ,EAAMlxB,IACjB,C,gBCvBAZ,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAGX,IAAIkH,EAAe,WAAc,SAASC,EAAiBC,EAAQC,GAAS,IAAK,IAAIC,EAAI,EAAGA,EAAID,EAAM/C,OAAQgD,IAAK,CAAE,IAAIC,EAAaF,EAAMC,GAAIC,EAAW1C,WAAa0C,EAAW1C,aAAc,EAAO0C,EAAWC,cAAe,EAAU,UAAWD,IAAYA,EAAWE,UAAW,GAAM5H,OAAOC,eAAesH,EAAQG,EAAWG,IAAKH,EAAa,CAAE,CAAE,OAAO,SAAUI,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYT,EAAiBQ,EAAYG,UAAWF,GAAiBC,GAAaV,EAAiBQ,EAAaE,GAAqBF,CAAa,CAAG,CAA7hB,GAIfs6B,EAAwB,WACxB,SAASA,EAAsB13B,IAHnC,SAAyBnB,EAAUzB,GAAe,KAAMyB,aAAoBzB,GAAgB,MAAM,IAAI0B,UAAU,oCAAwC,CAIhJC,CAAgBC,KAAM04B,GAEtB14B,KAAKgB,QAAUA,CACnB,CA8BA,OA5BArD,EAAa+6B,EAAuB,CAAC,CACjCv6B,IAAK,SACL1H,MAAO,SAAgBwK,GACnB,IAAIiB,EAAQlC,KAEZA,KAAKiB,QAAUA,EACfjB,KAAK2G,OAASlD,SAAS+B,cAAc,UACrCxF,KAAKoL,IAAMpL,KAAK2G,OAAO0E,WAAW,MAClCrL,KAAK2G,OAAON,MAAQ9G,KAAKo5B,MAAM13B,EAAQoF,OAASpF,EAAQ+F,MACxDhH,KAAK2G,OAAOL,OAAS/G,KAAKo5B,MAAM13B,EAAQqF,QAAUrF,EAAQ+F,MAC1DhH,KAAK2G,OAAOjM,MAAM2L,MAAQpF,EAAQoF,MAAQ,KAC1CrG,KAAK2G,OAAOjM,MAAM4L,OAASrF,EAAQqF,OAAS,KAE5CrF,EAAQC,OAAOkpB,IAAI,uCAAyCnpB,EAAQoF,MAAQ,IAAMpF,EAAQqF,OAAS,OAASrF,EAAQkG,EAAI,IAAMlG,EAAQmG,EAAI,gBAAkBnG,EAAQ+F,OACpK,IAAIsL,EAAMC,EAAuBhT,KAAKshB,IAAI5f,EAAQoG,YAAapG,EAAQoF,OAASpF,EAAQ+F,MAAOzH,KAAKshB,IAAI5f,EAAQuG,aAAcvG,EAAQqF,QAAUrF,EAAQ+F,MAAO/F,EAAQyG,QAAUzG,EAAQ+F,MAAO/F,EAAQ2G,QAAU3G,EAAQ+F,MAAOhH,KAAKgB,SAEtO,OAAOwR,EAAkBF,GAAK5P,MAAK,SAAUC,GAOzC,OANI1B,EAAQkC,kBACRjB,EAAMkJ,IAAI+G,UAAYlR,EAAQkC,gBAAgB4K,WAC9C7L,EAAMkJ,IAAIgH,SAAS,EAAG,EAAGnR,EAAQoF,MAAQpF,EAAQ+F,MAAO/F,EAAQqF,OAASrF,EAAQ+F,QAGrF9E,EAAMkJ,IAAIK,UAAU9I,GAAM1B,EAAQkG,EAAIlG,EAAQ+F,OAAQ/F,EAAQmG,EAAInG,EAAQ+F,OACnE9E,EAAMyE,MACjB,GACJ,KAGG+xB,CACX,CApC4B,GAsC5BliC,EAAA,QAAkBkiC,EAClB,IAAInmB,EAAyB/b,EAAQ+b,uBAAyB,SAAgClM,EAAOC,EAAQa,EAAGC,EAAGnF,GAC/G,IAAI22B,EAAQ,6BACRtmB,EAAM7O,SAASo1B,gBAAgBD,EAAO,OACtCE,EAAgBr1B,SAASo1B,gBAAgBD,EAAO,iBAapD,OAZAtmB,EAAIymB,eAAe,KAAM,QAAS1yB,GAClCiM,EAAIymB,eAAe,KAAM,SAAUzyB,GAEnCwyB,EAAcC,eAAe,KAAM,QAAS,QAC5CD,EAAcC,eAAe,KAAM,SAAU,QAC7CD,EAAcC,eAAe,KAAM,IAAK5xB,GACxC2xB,EAAcC,eAAe,KAAM,IAAK3xB,GACxC0xB,EAAcC,eAAe,KAAM,4BAA6B,QAChEzmB,EAAI5M,YAAYozB,GAEhBA,EAAcpzB,YAAYzD,GAEnBqQ,CACX,EAEIE,EAAoBhc,EAAQgc,kBAAoB,SAA2BF,GAC3E,OAAO,IAAInQ,SAAQ,SAAUY,EAAS6B,GAClC,IAAIjC,EAAM,IAAI+O,MACd/O,EAAIqC,OAAS,WACT,OAAOjC,EAAQJ,EACnB,EACAA,EAAIoC,QAAUH,EAEdjC,EAAIC,IAAM,oCAAsCkmB,oBAAmB,IAAIF,eAAgBG,kBAAkBzW,GAC7G,GACJ,C,kBC5EAhc,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQsF,YAActF,EAAQwiC,yBAA2BxiC,EAAQyiC,iBAAmBziC,EAAQ0iC,6BAA+B1iC,EAAQ2iC,YAAc3iC,EAAQ4iC,cAAgB5iC,EAAQ6iC,kBAAoB7iC,EAAQ8iC,gBAAkB9iC,EAAQ+iC,QAAU/iC,EAAQgjC,4BAAyB1iC,EAElR,IAYgC0E,EAZ5BmC,EAAe,WAAc,SAASC,EAAiBC,EAAQC,GAAS,IAAK,IAAIC,EAAI,EAAGA,EAAID,EAAM/C,OAAQgD,IAAK,CAAE,IAAIC,EAAaF,EAAMC,GAAIC,EAAW1C,WAAa0C,EAAW1C,aAAc,EAAO0C,EAAWC,cAAe,EAAU,UAAWD,IAAYA,EAAWE,UAAW,GAAM5H,OAAOC,eAAesH,EAAQG,EAAWG,IAAKH,EAAa,CAAE,CAAE,OAAO,SAAUI,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYT,EAAiBQ,EAAYG,UAAWF,GAAiBC,GAAaV,EAAiBQ,EAAaE,GAAqBF,CAAa,CAAG,CAA7hB,GAEfuP,EAAgb,SAAUX,EAAKjP,GAAK,GAAIc,MAAMC,QAAQkO,GAAQ,OAAOA,EAAY,GAAIC,OAAOC,YAAY5W,OAAO0W,GAAQ,OAAxf,SAAuBA,EAAKjP,GAAK,IAAIoP,EAAO,GAAQC,GAAK,EAAUC,GAAK,EAAWC,OAAKxW,EAAW,IAAM,IAAK,IAAiCyW,EAA7BC,EAAKR,EAAIC,OAAOC,cAAmBE,GAAMG,EAAKC,EAAGlR,QAAQC,QAAoB4Q,EAAK3Q,KAAK+Q,EAAG9W,QAAYsH,GAAKoP,EAAKpS,SAAWgD,GAA3DqP,GAAK,GAAkE,CAAE,MAAOK,GAAOJ,GAAK,EAAMC,EAAKG,CAAK,CAAE,QAAU,KAAWL,GAAMI,EAAW,QAAGA,EAAW,QAAK,CAAE,QAAU,GAAIH,EAAI,MAAMC,CAAI,CAAE,CAAE,OAAOH,CAAM,CAAuHO,CAAcV,EAAKjP,GAAa,MAAM,IAAI+B,UAAU,uDAA2D,EAE7oB25B,EAAQ,EAAQ,MAIhBC,GAI4Bl+B,EANX,EAAQ,QAMwBA,EAAIE,WAAaF,EAAM,CAAEsF,QAAStF,GAFnFgD,EAAQ,EAAQ,OAQhBg7B,EAAyBhjC,EAAQgjC,uBAAyB,GAY1DG,EAAK,GAILC,EAAK,GAELC,EAAK,GAGLC,EAAK,GACLC,EAAK,GACLC,EAAK,GACLC,EAAK,GACLC,EAAK,GACLC,EAAK,GAGLC,EAAK,GACLC,EAAK,GACLC,EAAK,GACLC,EAAK,GACLC,EAAK,GAGLC,EAAK,GAELC,EAAK,GACLC,EAAK,GACLC,EAAK,GACLC,EAAK,GAELC,EAAK,GACLC,EAAK,GACLC,EAAK,GACLC,EAAK,GAELC,EAAK,GAiDL5B,GA9CU9iC,EAAQ+iC,QAAU,CAC5B4B,GAjDK,EAkDLC,GAjDK,EAkDLC,GAjDK,EAkDLC,GAjDK,EAkDLC,GAjDK,EAkDLC,GAjDK,EAkDLC,GAjDK,EAkDLC,GAjDK,EAkDLC,GAjDK,EAkDLhC,GAAIA,EACJiC,IAjDM,GAkDNC,GAhDK,GAiDLjC,GAAIA,EACJkC,GAhDK,GAiDLjC,GAAIA,EACJkC,GAhDK,GAiDLjC,GAAIA,EACJC,GAAIA,EACJC,GAAIA,EACJC,GAAIA,EACJC,GAAIA,EACJC,GAAIA,EACJ6B,GA/CK,GAgDL5B,GAAIA,EACJC,GAAIA,EACJC,GAAIA,EACJC,GAAIA,EACJC,GAAIA,EACJyB,GA7CK,GA8CLxB,GAAIA,EACJyB,GA7CK,GA8CLxB,GAAIA,EACJC,GAAIA,EACJC,GAAIA,EACJC,GAAIA,EACJsB,GA7CK,GA8CLrB,GAAIA,EACJC,GAAIA,EACJC,GAAIA,EACJC,GAAIA,EACJmB,GA7CK,GA8CLlB,GAAIA,EACJmB,GA7CK,IAgDa7lC,EAAQ8iC,gBAAkB,KAC5CD,EAAoB7iC,EAAQ6iC,kBAAoB,IAChDD,EAAgB5iC,EAAQ4iC,cAAgB,IACxCD,EAAc3iC,EAAQ2iC,aAAc,EAAIM,EAAM98B,sBAAsB+8B,EAAgB54B,SAEpFw7B,EAAc,CAAC7B,EA5DV,IA6DL8B,EAAmB,CApGd,EACA,EACA,EAEA,GAiGLC,EAAQ,CAAC7C,EA9FJ,GA+FL8C,EAAiB,CAAClC,EAAID,GACtBoC,EAAcH,EAAiB/3B,OAAOg4B,GACtCG,EAAwB,CAAC5B,EAAIC,EAAIC,EAAIL,EAAIC,GACzC+B,EAAS,CAAC/C,EAAID,GAEdV,EAA+B1iC,EAAQ0iC,6BAA+B,SAAsC9F,GAC5G,IAAIr3B,EAAY6tB,UAAU7uB,OAAS,QAAsBjE,IAAjB8yB,UAAU,GAAmBA,UAAU,GAAK,SAEhFiT,EAAQ,GACRC,EAAW,GACXC,EAAa,GAgEjB,OA/DA3J,EAAWvjB,SAAQ,SAAU5P,EAAWb,GACpC,IAAI49B,EAAY7D,EAAY59B,IAAI0E,GAQhC,GAPI+8B,EAAYxD,GACZuD,EAAWvgC,MAAK,GAChBwgC,GAAaxD,GAEbuD,EAAWvgC,MAAK,IAGoC,IAApD,CAAC,SAAU,OAAQ,SAASyX,QAAQlY,KAEyB,IAAzD,CAAC,KAAQ,KAAQ,MAAQ,OAAQkY,QAAQhU,GAEzC,OADA68B,EAAStgC,KAAK4C,GACPy9B,EAAMrgC,KA9GpB,IAkHD,GA/HC,IA+HGwgC,GAxHF,KAwHsBA,EAAmB,CAEvC,GAAc,IAAV59B,EAEA,OADA09B,EAAStgC,KAAK4C,GACPy9B,EAAMrgC,KAAKi+B,GAKtB,IAAIwC,EAAOJ,EAAMz9B,EAAQ,GACzB,OAAmC,IAA/Bs9B,EAAYzoB,QAAQgpB,IACpBH,EAAStgC,KAAKsgC,EAAS19B,EAAQ,IACxBy9B,EAAMrgC,KAAKygC,KAEtBH,EAAStgC,KAAK4C,GACPy9B,EAAMrgC,KAAKi+B,GACtB,CAIA,OAFAqC,EAAStgC,KAAK4C,GAlHb,KAoHG49B,EACOH,EAAMrgC,KAAmB,WAAdT,EAAyBm+B,EAAKY,GAGhDkC,IAAc9B,GA1HjB,KA8HG8B,EAHOH,EAAMrgC,KAAKi+B,GA7GrB,KAuHGuC,EACI/8B,GAAa,QAAWA,GAAa,QAAWA,GAAa,QAAWA,GAAa,OAC9E48B,EAAMrgC,KAAKs+B,GAEX+B,EAAMrgC,KAAKi+B,QAI1BoC,EAAMrgC,KAAKwgC,EACf,IAEO,CAACF,EAAUD,EAAOE,EAC7B,EAEIG,EAA6B,SAAoCzyB,EAAGyX,EAAGib,EAAcC,GACrF,IAAIC,EAAUD,EAAWD,GACzB,GAAIt+B,MAAMC,QAAQ2L,IAA6B,IAAxBA,EAAEwJ,QAAQopB,GAAkB5yB,IAAM4yB,EAErD,IADA,IAAIt/B,EAAIo/B,EACDp/B,GAAKq/B,EAAWriC,QAAQ,CAE3B,IAAIuB,EAAO8gC,IADXr/B,GAGA,GAAIzB,IAAS4lB,EACT,OAAO,EAGX,GAAI5lB,IAASq9B,EACT,KAER,CAGJ,GAAI0D,IAAY1D,EAGZ,IAFA,IAAInsB,EAAK2vB,EAEF3vB,EAAK,GAAG,CAEX,IAAIyvB,EAAOG,IADX5vB,GAGA,GAAI3O,MAAMC,QAAQ2L,IAA0B,IAArBA,EAAEwJ,QAAQgpB,GAAexyB,IAAMwyB,EAElD,IADA,IAAIlf,EAAIof,EACDpf,GAAKqf,EAAWriC,QAAQ,CAE3B,IAAIuiC,EAAQF,IADZrf,GAGA,GAAIuf,IAAUpb,EACV,OAAO,EAGX,GAAIob,IAAU3D,EACV,KAER,CAGJ,GAAIsD,IAAStD,EACT,KAER,CAEJ,OAAO,CACX,EAEI4D,EAA4B,SAAmCJ,EAAcC,GAE7E,IADA,IAAIr/B,EAAIo/B,EACDp/B,GAAK,GAAG,CACX,IAAIvD,EAAO4iC,EAAWr/B,GACtB,GAAIvD,IAASm/B,EAGT,OAAOn/B,EAFPuD,GAIR,CACA,OAAO,CACX,EAEIy/B,EAAoB,SAA2BpK,EAAYgK,EAAYN,EAAU19B,EAAOq+B,GACxF,GAAwB,IAApBX,EAAS19B,GACT,OAAOi6B,EAGX,IAAI8D,EAAe/9B,EAAQ,EAC3B,GAAIP,MAAMC,QAAQ2+B,KAAsD,IAAlCA,EAAgBN,GAClD,OAAO9D,EAGX,IAAIqE,EAAcP,EAAe,EAC7BQ,EAAaR,EAAe,EAC5BE,EAAUD,EAAWD,GAIrBS,EAASF,GAAe,EAAIN,EAAWM,GAAe,EACtDphC,EAAO8gC,EAAWO,GAEtB,GAnQK,IAmQDN,GAlQC,IAkQiB/gC,EAClB,OAAO+8B,EAGX,IAA2C,IAAvCkD,EAAiBtoB,QAAQopB,GACzB,OAAO/D,EAIX,IAAwC,IAApCiD,EAAiBtoB,QAAQ3X,GACzB,OAAO+8B,EAIX,IAA6B,IAAzBmD,EAAMvoB,QAAQ3X,GACd,OAAO+8B,EAIX,GAhRK,IAgRDkE,EAA0BJ,EAAcC,GACxC,OAAOhE,EAIX,GAlRM,KAkRFD,EAAY59B,IAAI63B,EAAW+J,MAA2B7gC,IAASw+B,GAAMx+B,IAASo+B,GAAMp+B,IAASq+B,GAC7F,OAAOtB,EAIX,GA3RK,IA2RDgE,GA3RC,IA2RiB/gC,EAClB,OAAO+8B,EAIX,GA9RK,IA8RDgE,EACA,OAAOhE,EAIX,IAAuC,IAAnC,CAACM,EAAIC,EAAIC,GAAI5lB,QAAQopB,IAnSpB,IAmSuC/gC,EACxC,OAAO+8B,EAIX,IAA4C,IAAxC,CAACS,EAAIC,EAAIC,EAAII,EAAII,GAAIvmB,QAAQ3X,GAC7B,OAAO+8B,EAIX,GAAIkE,EAA0BJ,EAAcC,KAAgBjD,EACxD,OAAOd,EAIX,GAAI6D,EAlSC,GAkS8B/C,EAAIgD,EAAcC,GACjD,OAAO/D,EAIX,GAAI6D,EAA2B,CAACpD,EAAIC,GAAKG,EAAIiD,EAAcC,GACvD,OAAO/D,EAIX,GAAI6D,EAxTC,MAwTkCC,EAAcC,GACjD,OAAO/D,EAIX,GAAIgE,IAAY1D,EACZ,OAAOP,EAIX,GAtTK,KAsTDiE,GAtTC,KAsTiB/gC,EAClB,OAAO+8B,EAIX,GAnUK,KAmUD/8B,GAnUC,KAmUc+gC,EACf,OAAOjE,EAIX,IAAoC,IAAhC,CAACQ,EAAIC,EAAIK,GAAIjmB,QAAQ3X,IA1UpB,KA0UoC+gC,EACrC,OAAOhE,EAIX,GAtTK,KAsTDuE,IAA8C,IAA7BhB,EAAO3oB,QAAQopB,GAChC,OAAOhE,EAIX,GAAIgE,IAAY7C,GA3TX,KA2TiBl+B,EAClB,OAAO+8B,EAIX,GAAI/8B,IAAS29B,IAAuE,IAAjEqC,EAAY93B,OAAOy1B,EAAID,EAAIK,EAAIS,EAAIJ,EAAIC,GAAI1mB,QAAQopB,GAClE,OAAOhE,EAIX,IAAmC,IAA/BiD,EAAYroB,QAAQ3X,IAAgB+gC,IAAYhD,IAAwC,IAAlCiC,EAAYroB,QAAQopB,IAAmB/gC,IAAS+9B,EACtG,OAAOhB,EAIX,GAAIgE,IAAY9C,IAAsC,IAAhC,CAACO,EAAIJ,EAAIC,GAAI1mB,QAAQ3X,KAAmD,IAAnC,CAACw+B,EAAIJ,EAAIC,GAAI1mB,QAAQopB,IAAmB/gC,IAASg+B,EACxG,OAAOjB,EAIX,IAAsC,IAAlCiD,EAAYroB,QAAQopB,KAAqD,IAAlCZ,EAAexoB,QAAQ3X,KAAqD,IAArCmgC,EAAexoB,QAAQopB,KAAkD,IAA/Bf,EAAYroB,QAAQ3X,GAC5I,OAAO+8B,EAIX,IAE+B,IAA/B,CAACkB,EAAID,GAAIrmB,QAAQopB,KAAoB/gC,IAAS+9B,IAAkC,IAA5B,CAACF,EAAIN,GAAI5lB,QAAQ3X,IAAgB8gC,EAAWO,EAAa,KAAOtD,KAErF,IAA/B,CAACF,EAAIN,GAAI5lB,QAAQopB,IAAmB/gC,IAAS+9B,GAE7CgD,IAAYhD,IAAsC,IAAhC,CAACA,EAAIG,EAAIJ,GAAInmB,QAAQ3X,GACnC,OAAO+8B,EAIX,IAA4C,IAAxC,CAACgB,EAAIG,EAAIJ,EAAIN,EAAIC,GAAI9lB,QAAQ3X,GAE7B,IADA,IAAIuhC,EAAYV,EACTU,GAAa,GAAG,CACnB,IAAIrjC,EAAO4iC,EAAWS,GACtB,GAAIrjC,IAAS6/B,EACT,OAAOhB,EACJ,IAAgC,IAA5B,CAACmB,EAAIJ,GAAInmB,QAAQzZ,GAGxB,MAFAqjC,GAIR,CAIJ,IAAgC,IAA5B,CAACtD,EAAID,GAAIrmB,QAAQ3X,GAEjB,IADA,IAAIwhC,GAA4C,IAA/B,CAAChE,EAAIC,GAAI9lB,QAAQopB,GAAkBK,EAAcP,EAC3DW,GAAc,GAAG,CACpB,IAAIC,EAAQX,EAAWU,GACvB,GAAIC,IAAU1D,EACV,OAAOhB,EACJ,IAAiC,IAA7B,CAACmB,EAAIJ,GAAInmB,QAAQ8pB,GAGxB,MAFAD,GAIR,CAIJ,GAAI/C,IAAOsC,IAA+C,IAApC,CAACtC,EAAIC,EAAIJ,EAAIC,GAAI5mB,QAAQ3X,KAA+C,IAA/B,CAAC0+B,EAAIJ,GAAI3mB,QAAQopB,KAA+C,IAA5B,CAACrC,EAAIC,GAAIhnB,QAAQ3X,KAA+C,IAA/B,CAAC2+B,EAAIJ,GAAI5mB,QAAQopB,IAAmB/gC,IAAS2+B,EAC7K,OAAO5B,EAIX,IAAgD,IAA5CsD,EAAsB1oB,QAAQopB,KAA+C,IAA5B,CAACpD,EAAIK,GAAIrmB,QAAQ3X,KAAyD,IAAzCqgC,EAAsB1oB,QAAQ3X,IAAgB+gC,IAAY9C,EAC5I,OAAOlB,EAIX,IAAsC,IAAlCiD,EAAYroB,QAAQopB,KAAkD,IAA/Bf,EAAYroB,QAAQ3X,GAC3D,OAAO+8B,EAIX,GAAIgE,IAAYjD,IAAqC,IAA/BkC,EAAYroB,QAAQ3X,GACtC,OAAO+8B,EAIX,IAAiD,IAA7CiD,EAAY93B,OAAO61B,GAAIpmB,QAAQopB,IAAmB/gC,IAAS69B,IAAgD,IAA1CmC,EAAY93B,OAAO61B,GAAIpmB,QAAQ3X,IAAgB+gC,IAAYtD,EAC5H,OAAOV,EAKX,GAlZK,KAkZDgE,GAlZC,KAkZiB/gC,EAAa,CAG/B,IAFA,IAAIyB,EAAI++B,EAASK,GACba,EAAQ,EACLjgC,EAAI,GArZV,KAuZOq/B,IADJr/B,IAEIigC,IAKR,GAAIA,EAAQ,GAAM,EACd,OAAO3E,CAEf,CAGA,OAAIgE,IAAY3C,GAAMp+B,IAASq+B,EACpBtB,EAGJD,CACX,EAqBI6E,GAnBmBznC,EAAQyiC,iBAAmB,SAA0B7F,EAAYh0B,GAEpF,GAAc,IAAVA,EACA,OAAOi6B,EAIX,GAAIj6B,GAASg0B,EAAWr4B,OACpB,OAAOu+B,EAGX,IAAI4E,EAAwBhF,EAA6B9F,GACrD+K,EAAyBxwB,EAAeuwB,EAAuB,GAC/DpB,EAAWqB,EAAuB,GAClCf,EAAae,EAAuB,GAExC,OAAOX,EAAkBpK,EAAYgK,EAAYN,EAAU19B,EAC/D,EAE0B,SAA6Bg0B,EAAYnyB,GAC1DA,IACDA,EAAU,CAAElF,UAAW,SAAUC,UAAW,WAGhD,IAAIoiC,EAAyBlF,EAA6B9F,EAAYnyB,EAAQlF,WAC1EsiC,EAAyB1wB,EAAeywB,EAAwB,GAChEtB,EAAWuB,EAAuB,GAClCjB,EAAaiB,EAAuB,GACpCC,EAAiBD,EAAuB,GAY5C,MAV0B,cAAtBp9B,EAAQjF,WAAmD,eAAtBiF,EAAQjF,YAC7CohC,EAAaA,EAAW96B,KAAI,SAAU9H,GAClC,OAAuC,IAAhC,CAAC6/B,EAAII,EAAIS,GAAIjnB,QAAQzZ,GAAesgC,EAAKtgC,CACpD,KAOG,CAACsiC,EAAUM,EAJ+B,aAAtBn8B,EAAQjF,UAA2BsiC,EAAeh8B,KAAI,SAAUg8B,EAAgBvgC,GACvG,OAAOugC,GAAkBlL,EAAWr1B,IAAM,OAAUq1B,EAAWr1B,IAAM,KACzE,IAAK,KAGT,GAmBIwgC,GAjB2B/nC,EAAQwiC,yBAA2B,SAAkCr9B,EAAKsF,GACrG,IAAImyB,GAAa,EAAI50B,EAAMpD,cAAcO,GACrC6iC,EAASnF,EAEToF,EAAuBR,EAAoB7K,EAAYnyB,GACvDy9B,EAAwB/wB,EAAe8wB,EAAsB,GAC7D3B,EAAW4B,EAAsB,GACjCtB,EAAasB,EAAsB,GACnCC,EAAuBD,EAAsB,GAMjD,OAJAtL,EAAWvjB,SAAQ,SAAU5P,EAAWlC,GACpCygC,IAAU,EAAIhgC,EAAMrD,eAAe8E,IAAclC,GAAKq1B,EAAWr4B,OAAS,EAAIu+B,EAAkBkE,EAAkBpK,EAAYgK,EAAYN,EAAU/+B,EAAI,EAAG4gC,GAC/J,IAEOH,CACX,EAEY,WACR,SAASD,EAAMnL,EAAYr3B,EAAWipB,EAAO+F,IAxhBjD,SAAyBlrB,EAAUzB,GAAe,KAAMyB,aAAoBzB,GAAgB,MAAM,IAAI0B,UAAU,oCAAwC,CAyhBhJC,CAAgBC,KAAMu+B,GAEtBv+B,KAAK4+B,YAAcxL,EACnBpzB,KAAK6+B,SAAW9iC,IAAcu9B,EAC9Bt5B,KAAKglB,MAAQA,EACbhlB,KAAK+qB,IAAMA,CACf,CASA,OAPAptB,EAAa4gC,EAAO,CAAC,CACjBpgC,IAAK,QACL1H,MAAO,WACH,OAAO+H,EAAMrD,cAAco3B,WAAMz7B,EAtiB7C,SAA4BkW,GAAO,GAAInO,MAAMC,QAAQkO,GAAM,CAAE,IAAK,IAAIjP,EAAI,EAAG+gC,EAAOjgC,MAAMmO,EAAIjS,QAASgD,EAAIiP,EAAIjS,OAAQgD,IAAO+gC,EAAK/gC,GAAKiP,EAAIjP,GAAM,OAAO+gC,CAAM,CAAS,OAAOjgC,MAAM8E,KAAKqJ,EAAQ,CAsiB1I+xB,CAAmB/+B,KAAK4+B,YAAYniC,MAAMuD,KAAKglB,MAAOhlB,KAAK+qB,MAC3G,KAGGwT,CACX,CAlBY,IAoBM/nC,EAAQsF,YAAc,SAAqBH,EAAKsF,GAC9D,IAAImyB,GAAa,EAAI50B,EAAMpD,cAAcO,GAErCqjC,EAAwBf,EAAoB7K,EAAYnyB,GACxDg+B,EAAwBtxB,EAAeqxB,EAAuB,GAC9DlC,EAAWmC,EAAsB,GACjC7B,EAAa6B,EAAsB,GACnCN,EAAuBM,EAAsB,GAE7ClkC,EAASq4B,EAAWr4B,OACpBmkC,EAAU,EACVC,EAAY,EAEhB,MAAO,CACH7iC,KAAM,WACF,GAAI6iC,GAAapkC,EACb,MAAO,CAAEwB,MAAM,GAGnB,IADA,IAAIR,EAAYs9B,EACT8F,EAAYpkC,IAAWgB,EAAYyhC,EAAkBpK,EAAYgK,EAAYN,IAAYqC,EAAWR,MAA2BtF,IAEtI,GAAIt9B,IAAcs9B,GAAqB8F,IAAcpkC,EAAQ,CACzD,IAAItE,EAAQ,IAAI8nC,EAAMnL,EAAYr3B,EAAWmjC,EAASC,GAEtD,OADAD,EAAUC,EACH,CAAE1oC,MAAOA,EAAO8F,MAAM,EACjC,CAEA,MAAO,CAAEA,MAAM,EACnB,EAER,C,kBC9lBAjG,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQ4oC,kBAAoB5oC,EAAQge,sBAAwBhe,EAAQ6wB,kBAAevwB,EAEnF,IAAI0H,EAAQ,EAAQ,OAIhB2U,EAAkBxS,EAFD,EAAQ,QAMzByS,EAAkBzS,EAFD,EAAQ,QAIzB4S,EAAa,EAAQ,KAErB+G,EAAW,EAAQ,MAEvB,SAAS3Z,EAAuBnF,GAAO,OAAOA,GAAOA,EAAIE,WAAaF,EAAM,CAAEsF,QAAStF,EAAO,CAG9F,IAEI6jC,EAAgB,CAAC,KAAM,KAAM,QAqF7BC,GAnFe9oC,EAAQ6wB,aAAe,SAAsB7T,GAC5D,IAAI5X,EAAS4X,EAAU5X,OACvB,IAAKA,EACD,OAAO,KAGX,EAAG,CAEC,IAD4D,IAA3CyjC,EAAcprB,QAAQrY,EAAOuY,SAE1C,OAAOvY,EAEXA,EAASA,EAAOA,MACpB,OAASA,GAET,OAAO4X,EAAU5X,MACrB,EAE4BpF,EAAQge,sBAAwB,SAA+BvS,EAAMuR,EAAW/R,GACxG,IAAI8S,EAAYf,EAAU9Y,MAAM6Z,UAEhC,GAAKA,EAAL,CAIA,IAAI7Z,EAAQuH,EAAKD,cAAcsF,YAAYU,iBAAiB/F,EAAM,MAC9DgZ,EAAUhZ,EAAKD,cAAcwD,cAAc,sBAQ/C,QAPA,EAAIhH,EAAM+E,eAAe7I,EAAOugB,GAEhCA,EAAQvgB,MAAMO,SAAW,WACzBggB,EAAQvgB,MAAM+hB,OAAS,OACvBxB,EAAQvgB,MAAMmR,QAAU,QACxBoP,EAAQvgB,MAAM+f,cAAgB,SAEtBlG,EAAUvZ,mBACd,KAAKuY,EAAW1c,oBAAoBI,QAChCgkB,EAAQvgB,MAAMuU,KAAO,OACrBgM,EAAQvgB,MAAM8hB,MAAQva,EAAKD,cAAcsF,YAAYC,WAAaiM,EAAUzE,OAAOE,KAAOuE,EAAU9Y,MAAMwrB,OAAO,GAAGtI,iBAAiBpK,EAAUzE,OAAO1I,OAxC/I,EAwCuK,KAC9K4U,EAAQvgB,MAAM6kC,UAAY,QAC1B,MACJ,KAAKhsB,EAAW1c,oBAAoBG,OAChCikB,EAAQvgB,MAAMuU,KAAOuE,EAAUzE,OAAOE,KAAOuE,EAAU9Y,MAAMwrB,OAAO,GAAGtI,iBAAiBpK,EAAUzE,OAAO1I,OAAS,KAClH4U,EAAQvgB,MAAM8hB,MAAQ,OACtBvB,EAAQvgB,MAAM6kC,UAAY,OAIlC,IAAIt7B,OAAO,EACPu7B,EAAahsB,EAAU9Y,MAAMwrB,OAAO,GAAGtI,iBAAiBpK,EAAUzE,OAAO1I,OACzEo5B,EAAalrB,EAAU5Z,eAC3B,GAAI8kC,EACA,GAA0B,QAAtBA,EAAWl9B,OAAkB,CAC7B,IAAImlB,EAAQzlB,EAAKD,cAAcwD,cAAc,OAC7CkiB,EAAM9kB,IAAM68B,EAAWh9B,KAAK,GAC5BwY,EAAQvgB,MAAMwU,IAAMsE,EAAUzE,OAAOG,IAAMswB,EAAa,KACxDvkB,EAAQvgB,MAAM2L,MAAQ,OACtB4U,EAAQvgB,MAAM4L,OAAS,OACvB2U,EAAQvV,YAAYgiB,EACxB,KAAO,CACH,IAAIxV,EAAmD,GAA5C+E,WAAWzD,EAAU9Y,MAAM6J,KAAKiV,UAC3CyB,EAAQvgB,MAAMwU,IAAMsE,EAAUzE,OAAOG,IAAMswB,EAAahsB,EAAUzE,OAAOzI,OAAS,IAAM4L,EAAO,KAC/F+I,EAAQvgB,MAAM2L,MAAQ6L,EAAO,KAC7B+I,EAAQvgB,MAAM4L,OAAS4L,EAAO,KAC9B+I,EAAQvgB,MAAM2H,gBAAkB3H,EAAMC,cAC1C,KACsC,iBAAxB6Y,EAAU8T,YACxBrjB,EAAOhC,EAAKD,cAAc0G,eAAe02B,EAAkB5rB,EAAU8T,UAAW/S,EAAUzZ,eAAe,IACzGmgB,EAAQvV,YAAYzB,GACpBgX,EAAQvgB,MAAMwU,IAAMsE,EAAUzE,OAAOG,IAAMswB,EAAa,MAI5D,IAAI90B,EAAOzI,EAAKD,cAAc0I,KAC9BA,EAAKhF,YAAYuV,GAEbhX,GACAuP,EAAUhE,WAAWhT,KAAK4W,EAAgBtS,QAAQkT,aAAa/P,EAAMuP,IACrE9I,EAAK6G,YAAY0J,IAGjBzH,EAAUhE,WAAWhT,KAAK,IAAI2W,EAAgBrS,QAAQma,EAASzH,EAAW/R,EAAgB,GAzD9F,CA2DJ,EAEkB,CACdi+B,SAAU,CAAC,IAAM,IAAK,IAAK,IAAK,IAAK,GAAI,GAAI,GAAI,GAAI,EAAG,EAAG,EAAG,GAC9DjnB,OAAQ,CAAC,IAAK,KAAM,IAAK,KAAM,IAAK,KAAM,IAAK,KAAM,IAAK,KAAM,IAAK,KAAM,OAG3E1gB,EAAW,CACX2nC,SAAU,CAAC,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAC1KjnB,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,MAGxL/f,EAAS,CACTgnC,SAAU,CAAC,IAAO,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAK,IAAK,IAAK,IAAK,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAC5KjnB,OAAQ,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KAAM,KAAM,KAAM,KAAM,KAAM,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,MAG5MlgB,EAAW,CACXmnC,SAAU,CAAC,IAAO,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GACjLjnB,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,MAG7LknB,EAAwB,SAA+BlpC,EAAOkoB,EAAKkC,EAAK+e,EAASC,EAAUC,GAC3F,OAAIrpC,EAAQkoB,GAAOloB,EAAQoqB,EAChBue,EAAkB3oC,EAAOopC,EAAUC,EAAO/kC,OAAS,GAGvD6kC,EAAQF,SAASr7B,QAAO,SAAU07B,EAAQC,EAAS5gC,GACtD,KAAO3I,GAASupC,GACZvpC,GAASupC,EACTD,GAAUH,EAAQnnB,OAAOrZ,GAE7B,OAAO2gC,CACX,GAAG,IAAMD,CACb,EAEIG,EAAuC,SAA8CxpC,EAAOypC,EAAsBC,EAAWC,GAC7H,IAAIL,EAAS,GAEb,GACSI,GACD1pC,IAEJspC,EAASK,EAAS3pC,GAASspC,EAC3BtpC,GAASypC,QACJzpC,EAAQypC,GAAwBA,GAEzC,OAAOH,CACX,EAEIM,EAA8B,SAAqC5pC,EAAO6pC,EAAqBC,EAAmBJ,EAAWL,GAC7H,IAAII,EAAuBK,EAAoBD,EAAsB,EAErE,OAAQ7pC,EAAQ,EAAI,IAAM,KAAOwpC,EAAqC1gC,KAAKsf,IAAIpoB,GAAQypC,EAAsBC,GAAW,SAAUlgC,GAC9H,OAAO,EAAIqa,EAASnf,eAAeoE,KAAKo5B,MAAM14B,EAAYigC,GAAwBI,EACtF,IAAKR,EACT,EAEIU,EAAgC,SAAuC/pC,EAAOmpC,GAC9E,IAAIE,EAASlW,UAAU7uB,OAAS,QAAsBjE,IAAjB8yB,UAAU,GAAmBA,UAAU,GAAK,KAE7EsW,EAAuBN,EAAQ7kC,OACnC,OAAOklC,EAAqC1gC,KAAKsf,IAAIpoB,GAAQypC,GAAsB,GAAO,SAAUjgC,GAChG,OAAO2/B,EAAQrgC,KAAKo5B,MAAM14B,EAAYigC,GAC1C,IAAKJ,CACT,EAOIW,EAAmB,SAA0BhqC,EAAOiqC,EAASC,EAAaC,EAAcd,EAAQe,GAChG,GAAIpqC,GAAS,MAAQA,EAAQ,KACzB,OAAO2oC,EAAkB3oC,EAAO8c,EAAW3c,gBAAgBW,YAAauoC,EAAO/kC,OAAS,GAE5F,IAAI+lC,EAAMvhC,KAAKsf,IAAIpoB,GACfspC,EAASD,EAEb,GAAY,IAARgB,EACA,OAAOJ,EAAQ,GAAKX,EAGxB,IAAK,IAAIgB,EAAQ,EAAGD,EAAM,GAAKC,GAAS,EAAGA,IAAS,CAChD,IAAIC,EAAcF,EAAM,GAEJ,IAAhBE,IAAqB,EAAIxiC,EAAMwjB,UAAU6e,EAnBrC,IAmBqE,KAAXd,EAC9DA,EAASW,EAAQM,GAAejB,EACzBiB,EAAc,GAAqB,IAAhBA,GAA+B,IAAVD,GAA+B,IAAhBC,GAA+B,IAAVD,IAAe,EAAIviC,EAAMwjB,UAAU6e,EApBvG,IAoBuJ,IAAhBG,GAA+B,IAAVD,IAAe,EAAIviC,EAAMwjB,UAAU6e,EAnB1L,IAmB+NpqC,EAAQ,KAAuB,IAAhBuqC,GAAqBD,EAAQ,IAAK,EAAIviC,EAAMwjB,UAAU6e,EAlBrS,GAmBnBd,EAASW,EAAQM,IAAgBD,EAAQ,EAAIJ,EAAYI,EAAQ,GAAK,IAAMhB,EACrD,IAAhBiB,GAAqBD,EAAQ,IACpChB,EAASY,EAAYI,EAAQ,GAAKhB,GAEtCe,EAAMvhC,KAAKo5B,MAAMmI,EAAM,GAC3B,CAEA,OAAQrqC,EAAQ,EAAImqC,EAAe,IAAMb,CAC7C,EAEIkB,EAA+B,OAC/BC,EAA6B,OAC7BC,EAAoB,OACpBC,EAAkB,QAElBhC,EAAoB5oC,EAAQ4oC,kBAAoB,SAA2B3oC,EAAO+D,EAAM6mC,GACxF,IAAIC,EAAgBD,EAAe,KAAO,GACtCE,EAAYF,EAAe,IAAM,GACjCG,EAAeH,EAAe,KAAO,GACzC,OAAQ7mC,GACJ,KAAK+Y,EAAW3c,gBAAgBO,KAC5B,MAAO,IACX,KAAKoc,EAAW3c,gBAAgBQ,OAC5B,MAAO,IACX,KAAKmc,EAAW3c,gBAAgBS,OAC5B,MAAO,IACX,KAAKkc,EAAW3c,gBAAgBY,qBAC5B,IAAIuoC,EAASM,EAA4B5pC,EAAO,GAAI,IAAI,EAAM6qC,GAC9D,OAAOvB,EAAOhlC,OAAS,EAAI,IAAMglC,EAASA,EAC9C,KAAKxsB,EAAW3c,gBAAgBW,YAC5B,OAAOipC,EAA8B/pC,EAAO,aAAc8qC,GAC9D,KAAKhuB,EAAW3c,gBAAgBa,YAC5B,OAAOkoC,EAAsBlpC,EAAO,EAAG,KAAM6oC,EAAa/rB,EAAW3c,gBAAgBU,QAASgqC,GAAe3qB,cACjH,KAAKpD,EAAW3c,gBAAgBc,YAC5B,OAAOioC,EAAsBlpC,EAAO,EAAG,KAAM6oC,EAAa/rB,EAAW3c,gBAAgBU,QAASgqC,GAClG,KAAK/tB,EAAW3c,gBAAgBe,YAC5B,OAAO0oC,EAA4B5pC,EAAO,IAAK,KAAK,EAAO6qC,GAC/D,KAAK/tB,EAAW3c,gBAAgBgB,YAC5B,OAAOyoC,EAA4B5pC,EAAO,GAAI,KAAK,EAAO6qC,GAC9D,KAAK/tB,EAAW3c,gBAAgBiB,YAC5B,OAAOwoC,EAA4B5pC,EAAO,GAAI,IAAI,EAAO6qC,GAC7D,KAAK/tB,EAAW3c,gBAAgBkB,aAC5B,OAAOuoC,EAA4B5pC,EAAO,KAAM,MAAM,EAAM6qC,GAChE,KAAK/tB,EAAW3c,gBAAgBmB,SAChC,KAAKwb,EAAW3c,gBAAgByD,eAC5B,OAAOslC,EAAsBlpC,EAAO,EAAG,KAAMsB,EAAUwb,EAAW3c,gBAAgBU,QAASgqC,GAC/F,KAAK/tB,EAAW3c,gBAAgB2C,eAC5B,OAAOomC,EAAsBlpC,EAAO,EAAG,KAAMsB,EAAUwb,EAAW3c,gBAAgBU,QAASgqC,GAAe3qB,cAC9G,KAAKpD,EAAW3c,gBAAgBoB,QAC5B,OAAOqoC,EAA4B5pC,EAAO,KAAM,MAAM,EAAM6qC,GAChE,KAAK/tB,EAAW3c,gBAAgBqB,UAChC,KAAKsb,EAAW3c,gBAAgBsC,MAC5B,OAAOmnC,EAA4B5pC,EAAO,KAAM,MAAM,EAAM6qC,GAChE,KAAK/tB,EAAW3c,gBAAgBsB,mBAC5B,OAAOsoC,EAA8B/pC,EAAO,eAAgB8qC,GAChE,KAAKhuB,EAAW3c,gBAAgBuB,kBAC5B,OAAOqoC,EAA8B/pC,EAAO,aAAc8qC,GAC9D,KAAKhuB,EAAW3c,gBAAgBwB,gBAChC,KAAKmb,EAAW3c,gBAAgBwD,sBAC5B,OAAOqmC,EAAiBhqC,EAAO,aAAcwqC,EAA8B,IAAKM,EAAWE,IAC/F,KAAKluB,EAAW3c,gBAAgBuD,oBAC5B,OAAOsmC,EAAiBhqC,EAAO,aAAcyqC,EAA4B,IAAKK,EAAWG,IAC7F,KAAKnuB,EAAW3c,gBAAgBkD,sBAC5B,OAAO2mC,EAAiBhqC,EAAO,aAAcwqC,EAA8B,IAAKM,EAAWE,IAC/F,KAAKluB,EAAW3c,gBAAgBiD,oBAC5B,OAAO4mC,EAAiBhqC,EAAO,aAAcyqC,EAA4B,IAAKK,EAAWG,IAC7F,KAAKnuB,EAAW3c,gBAAgBkC,kBAC5B,OAAO2nC,EAAiBhqC,EAAO,aAAc,OAAQ0qC,EAAmBI,EAAW,GACvF,KAAKhuB,EAAW3c,gBAAgBiC,gBAC5B,OAAO4nC,EAAiBhqC,EAAO,aAAc,OAAQ0qC,EAAmBI,EAAWG,GACvF,KAAKnuB,EAAW3c,gBAAgBuC,qBAC5B,OAAOsnC,EAAiBhqC,EAAO,aAAc,OAAQ2qC,EAAiBI,EAAcE,GACxF,KAAKnuB,EAAW3c,gBAAgByC,sBAC5B,OAAOonC,EAAiBhqC,EAAO,aAAc,OAAQ2qC,EAAiBI,EAAc,GACxF,KAAKjuB,EAAW3c,gBAAgBwC,oBAC5B,OAAOqnC,EAAiBhqC,EAAO,aAAc,MAAO2qC,EAAiBI,EAAcE,GACvF,KAAKnuB,EAAW3c,gBAAgByB,WAC5B,OAAOgoC,EAA4B5pC,EAAO,KAAO,MAAO,EAAM6qC,GAClE,KAAK/tB,EAAW3c,gBAAgB2B,SAC5B,OAAOonC,EAAsBlpC,EAAO,EAAG,MAAO8B,EAAUgb,EAAW3c,gBAAgBU,QAASgqC,GAChG,KAAK/tB,EAAW3c,gBAAgB4B,SAC5B,OAAO6nC,EAA4B5pC,EAAO,KAAO,MAAO,EAAM6qC,GAClE,KAAK/tB,EAAW3c,gBAAgB6B,SAC5B,OAAO4nC,EAA4B5pC,EAAO,KAAO,MAAO,EAAM6qC,GAClE,KAAK/tB,EAAW3c,gBAAgB8B,OAC5B,OAAOinC,EAAsBlpC,EAAO,EAAG,MAAOiC,EAAQ6a,EAAW3c,gBAAgBU,QAASgqC,GAC9F,KAAK/tB,EAAW3c,gBAAgB+B,SAC5B,OAAO6nC,EAA8B/pC,EAAO,oDAChD,KAAK8c,EAAW3c,gBAAgBgC,eAC5B,OAAO4nC,EAA8B/pC,EAAO,mDAChD,KAAK8c,EAAW3c,gBAAgBmC,QAC5B,OAAOsnC,EAA4B5pC,EAAO,KAAO,MAAO,EAAM6qC,GAClE,KAAK/tB,EAAW3c,gBAAgBoC,SAC5B,OAAOwnC,EAA8B/pC,EAAO,mDAAoD8qC,GACpG,KAAKhuB,EAAW3c,gBAAgBqC,eAC5B,OAAOunC,EAA8B/pC,EAAO,kDAAmD8qC,GACnG,KAAKhuB,EAAW3c,gBAAgB0C,IAC5B,OAAO+mC,EAA4B5pC,EAAO,KAAO,MAAO,EAAM6qC,GAClE,KAAK/tB,EAAW3c,gBAAgB6C,UAC5B,OAAO4mC,EAA4B5pC,EAAO,KAAQ,MAAQ,EAAM6qC,GACpE,KAAK/tB,EAAW3c,gBAAgB8C,QAC5B,OAAO2mC,EAA4B5pC,EAAO,KAAQ,MAAQ,EAAM6qC,GACpE,KAAK/tB,EAAW3c,gBAAgB+C,MAC5B,OAAO0mC,EAA4B5pC,EAAO,KAAO,MAAO,EAAM6qC,GAClE,KAAK/tB,EAAW3c,gBAAgBgD,QAC5B,OAAOymC,EAA4B5pC,EAAO,KAAO,MAAO,EAAM6qC,GAClE,KAAK/tB,EAAW3c,gBAAgBmD,MAC5B,OAAOsmC,EAA4B5pC,EAAO,KAAO,MAAO,EAAM6qC,GAClE,KAAK/tB,EAAW3c,gBAAgBoD,OAC5B,OAAOqmC,EAA4B5pC,EAAO,KAAO,MAAO,EAAM6qC,GAClE,KAAK/tB,EAAW3c,gBAAgBqD,KAC5B,OAAOomC,EAA4B5pC,EAAO,KAAO,MAAO,EAAM6qC,GAClE,KAAK/tB,EAAW3c,gBAAgBsD,QAC5B,OAAOmmC,EAA4B5pC,EAAO,KAAO,MAAO,EAAM6qC,GAClE,KAAK/tB,EAAW3c,gBAAgBU,QAChC,QACI,OAAO+oC,EAA4B5pC,EAAO,GAAI,IAAI,EAAM6qC,GAEpE,C,gBCxTAhrC,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAGX,IAEIkH,EAAe,WAAc,SAASC,EAAiBC,EAAQC,GAAS,IAAK,IAAIC,EAAI,EAAGA,EAAID,EAAM/C,OAAQgD,IAAK,CAAE,IAAIC,EAAaF,EAAMC,GAAIC,EAAW1C,WAAa0C,EAAW1C,aAAc,EAAO0C,EAAWC,cAAe,EAAU,UAAWD,IAAYA,EAAWE,UAAW,GAAM5H,OAAOC,eAAesH,EAAQG,EAAWG,IAAKH,EAAa,CAAE,CAAE,OAAO,SAAUI,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYT,EAAiBQ,EAAYG,UAAWF,GAAiBC,GAAaV,EAAiBQ,EAAaE,GAAqBF,CAAa,CAAG,CAA7hB,GAIfujC,EAAO,oBASPC,EAAO,oBASPC,EAAM,2DASNC,EAAO,8EAkBPC,EAAQ,WACR,SAASA,EAAMtrC,IAhDnB,SAAyBoJ,EAAUzB,GAAe,KAAMyB,aAAoBzB,GAAgB,MAAM,IAAI0B,UAAU,oCAAwC,CAiDhJC,CAAgBC,KAAM+hC,GAEtB,IAb2BC,EAavBl1B,EAAOjO,MAAMC,QAAQrI,IAbEurC,EAaiBvrC,EAZzC,CAAC8I,KAAKof,IAAIqjB,EAAM,GAAI,KAAMziC,KAAKof,IAAIqjB,EAAM,GAAI,KAAMziC,KAAKof,IAAIqjB,EAAM,GAAI,KAAMA,EAAMjnC,OAAS,EAAIinC,EAAM,GAAK,OApC1G,SAAcvrC,GACrB,IAAI2X,EAAQ3X,EAAM2X,MAAMuzB,GACxB,QAAIvzB,GACO,CAAC4L,SAAS5L,EAAM,GAAG,GAAKA,EAAM,GAAG,GAAI,IAAK4L,SAAS5L,EAAM,GAAG,GAAKA,EAAM,GAAG,GAAI,IAAK4L,SAAS5L,EAAM,GAAG,GAAKA,EAAM,GAAG,GAAI,IAAK,KAG3I,CA0C6D6zB,CAAKxrC,IA9BxD,SAAaA,GACnB,IAAI2X,EAAQ3X,EAAM2X,MAAMyzB,GACxB,QAAIzzB,GACO,CAAC8zB,OAAO9zB,EAAM,IAAK8zB,OAAO9zB,EAAM,IAAK8zB,OAAO9zB,EAAM,IAAK,KAGtE,CAwB4E+zB,CAAI1rC,IArBrE,SAAcA,GACrB,IAAI2X,EAAQ3X,EAAM2X,MAAM0zB,GACxB,SAAI1zB,GAASA,EAAMrT,OAAS,IACjB,CAACmnC,OAAO9zB,EAAM,IAAK8zB,OAAO9zB,EAAM,IAAK8zB,OAAO9zB,EAAM,IAAK8zB,OAAO9zB,EAAM,IAGnF,CAe0Fg0B,CAAK3rC,IAR/E4rC,EAQoG5rC,EARlFkgB,iBACP,GAhChB,SAAclgB,GACrB,IAAI2X,EAAQ3X,EAAM2X,MAAMwzB,GACxB,QAAIxzB,GACO,CAAC4L,SAAS5L,EAAM,GAAGmP,UAAU,EAAG,GAAI,IAAKvD,SAAS5L,EAAM,GAAGmP,UAAU,EAAG,GAAI,IAAKvD,SAAS5L,EAAM,GAAGmP,UAAU,EAAG,GAAI,IAAK,KAGxI,CAiC8H+kB,CAAK7rC,IAAU,CAAC,EAAG,EAAG,EAAG,MAC3IsW,EAxDwa,SAAUC,EAAKjP,GAAK,GAAIc,MAAMC,QAAQkO,GAAQ,OAAOA,EAAY,GAAIC,OAAOC,YAAY5W,OAAO0W,GAAQ,OAAxf,SAAuBA,EAAKjP,GAAK,IAAIoP,EAAO,GAAQC,GAAK,EAAUC,GAAK,EAAWC,OAAKxW,EAAW,IAAM,IAAK,IAAiCyW,EAA7BC,EAAKR,EAAIC,OAAOC,cAAmBE,GAAMG,EAAKC,EAAGlR,QAAQC,QAAoB4Q,EAAK3Q,KAAK+Q,EAAG9W,QAAYsH,GAAKoP,EAAKpS,SAAWgD,GAA3DqP,GAAK,GAAkE,CAAE,MAAOK,GAAOJ,GAAK,EAAMC,EAAKG,CAAK,CAAE,QAAU,KAAWL,GAAMI,EAAW,QAAGA,EAAW,QAAK,CAAE,QAAU,GAAIH,EAAI,MAAMC,CAAI,CAAE,CAAE,OAAOH,CAAM,CAAuHO,CAAcV,EAAKjP,GAAa,MAAM,IAAI+B,UAAU,uDAA2D,CAwD7nB6N,CAAeb,EAAM,GAC7By1B,EAAIx1B,EAAM,GACVy1B,EAAIz1B,EAAM,GACVmV,EAAInV,EAAM,GACVtC,EAAIsC,EAAM,GAEd/M,KAAKuiC,EAAIA,EACTviC,KAAKwiC,EAAIA,EACTxiC,KAAKkiB,EAAIA,EACTliB,KAAKyK,EAAIA,CACb,CAcA,OAZA9M,EAAaokC,EAAO,CAAC,CACjB5jC,IAAK,gBACL1H,MAAO,WACH,OAAkB,IAAXuJ,KAAKyK,CAChB,GACD,CACCtM,IAAK,WACL1H,MAAO,WACH,OAAkB,OAAXuJ,KAAKyK,GAAyB,IAAXzK,KAAKyK,EAAU,QAAUzK,KAAKuiC,EAAI,IAAMviC,KAAKwiC,EAAI,IAAMxiC,KAAKkiB,EAAI,IAAMliB,KAAKyK,EAAI,IAAM,OAASzK,KAAKuiC,EAAI,IAAMviC,KAAKwiC,EAAI,IAAMxiC,KAAKkiB,EAAI,GACnK,KAGG6f,CACX,CA9BY,GAgCZvrC,EAAA,QAAkBurC,EAGlB,IAAIM,EAAe,CACfI,YAAa,CAAC,EAAG,EAAG,EAAG,GACvBC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,aAAc,CAAC,IAAK,IAAK,IAAK,MAC9BC,KAAM,CAAC,EAAG,IAAK,IAAK,MACpBC,WAAY,CAAC,IAAK,IAAK,IAAK,MAC5BC,MAAO,CAAC,IAAK,IAAK,IAAK,MACvBC,MAAO,CAAC,IAAK,IAAK,IAAK,MACvBC,OAAQ,CAAC,IAAK,IAAK,IAAK,MACxBC,MAAO,CAAC,EAAG,EAAG,EAAG,MACjBC,eAAgB,CAAC,IAAK,IAAK,IAAK,MAChCC,KAAM,CAAC,EAAG,EAAG,IAAK,MAClBC,WAAY,CAAC,IAAK,GAAI,IAAK,MAC3BC,MAAO,CAAC,IAAK,GAAI,GAAI,MACrBC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,UAAW,CAAC,GAAI,IAAK,IAAK,MAC1BC,WAAY,CAAC,IAAK,IAAK,EAAG,MAC1BC,UAAW,CAAC,IAAK,IAAK,GAAI,MAC1BC,MAAO,CAAC,IAAK,IAAK,GAAI,MACtBC,eAAgB,CAAC,IAAK,IAAK,IAAK,MAChCC,SAAU,CAAC,IAAK,IAAK,IAAK,MAC1BC,QAAS,CAAC,IAAK,GAAI,GAAI,MACvBC,KAAM,CAAC,EAAG,IAAK,IAAK,MACpBC,SAAU,CAAC,EAAG,EAAG,IAAK,MACtBC,SAAU,CAAC,EAAG,IAAK,IAAK,MACxBC,cAAe,CAAC,IAAK,IAAK,GAAI,MAC9BC,SAAU,CAAC,IAAK,IAAK,IAAK,MAC1BC,UAAW,CAAC,EAAG,IAAK,EAAG,MACvBC,SAAU,CAAC,IAAK,IAAK,IAAK,MAC1BC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,YAAa,CAAC,IAAK,EAAG,IAAK,MAC3BC,eAAgB,CAAC,GAAI,IAAK,GAAI,MAC9BC,WAAY,CAAC,IAAK,IAAK,EAAG,MAC1BC,WAAY,CAAC,IAAK,GAAI,IAAK,MAC3BC,QAAS,CAAC,IAAK,EAAG,EAAG,MACrBC,WAAY,CAAC,IAAK,IAAK,IAAK,MAC5BC,aAAc,CAAC,IAAK,IAAK,IAAK,MAC9BC,cAAe,CAAC,GAAI,GAAI,IAAK,MAC7BC,cAAe,CAAC,GAAI,GAAI,GAAI,MAC5BC,cAAe,CAAC,GAAI,GAAI,GAAI,MAC5BC,cAAe,CAAC,EAAG,IAAK,IAAK,MAC7BC,WAAY,CAAC,IAAK,EAAG,IAAK,MAC1BC,SAAU,CAAC,IAAK,GAAI,IAAK,MACzBC,YAAa,CAAC,EAAG,IAAK,IAAK,MAC3BC,QAAS,CAAC,IAAK,IAAK,IAAK,MACzBC,QAAS,CAAC,IAAK,IAAK,IAAK,MACzBC,WAAY,CAAC,GAAI,IAAK,IAAK,MAC3BC,UAAW,CAAC,IAAK,GAAI,GAAI,MACzBC,YAAa,CAAC,IAAK,IAAK,IAAK,MAC7BC,YAAa,CAAC,GAAI,IAAK,GAAI,MAC3BC,QAAS,CAAC,IAAK,EAAG,IAAK,MACvBC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,WAAY,CAAC,IAAK,IAAK,IAAK,MAC5BC,KAAM,CAAC,IAAK,IAAK,EAAG,MACpBC,UAAW,CAAC,IAAK,IAAK,GAAI,MAC1BC,KAAM,CAAC,IAAK,IAAK,IAAK,MACtBC,MAAO,CAAC,EAAG,IAAK,EAAG,MACnBC,YAAa,CAAC,IAAK,IAAK,GAAI,MAC5BC,KAAM,CAAC,IAAK,IAAK,IAAK,MACtBC,SAAU,CAAC,IAAK,IAAK,IAAK,MAC1BC,QAAS,CAAC,IAAK,IAAK,IAAK,MACzBC,UAAW,CAAC,IAAK,GAAI,GAAI,MACzBC,OAAQ,CAAC,GAAI,EAAG,IAAK,MACrBC,MAAO,CAAC,IAAK,IAAK,IAAK,MACvBC,MAAO,CAAC,IAAK,IAAK,IAAK,MACvBC,SAAU,CAAC,IAAK,IAAK,IAAK,MAC1BC,cAAe,CAAC,IAAK,IAAK,IAAK,MAC/BC,UAAW,CAAC,IAAK,IAAK,EAAG,MACzBC,aAAc,CAAC,IAAK,IAAK,IAAK,MAC9BC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,WAAY,CAAC,IAAK,IAAK,IAAK,MAC5BC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,qBAAsB,CAAC,IAAK,IAAK,IAAK,MACtCC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,WAAY,CAAC,IAAK,IAAK,IAAK,MAC5BC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,YAAa,CAAC,IAAK,IAAK,IAAK,MAC7BC,cAAe,CAAC,GAAI,IAAK,IAAK,MAC9BC,aAAc,CAAC,IAAK,IAAK,IAAK,MAC9BC,eAAgB,CAAC,IAAK,IAAK,IAAK,MAChCC,eAAgB,CAAC,IAAK,IAAK,IAAK,MAChCC,eAAgB,CAAC,IAAK,IAAK,IAAK,MAChCC,YAAa,CAAC,IAAK,IAAK,IAAK,MAC7BC,KAAM,CAAC,EAAG,IAAK,EAAG,MAClBC,UAAW,CAAC,GAAI,IAAK,GAAI,MACzBC,MAAO,CAAC,IAAK,IAAK,IAAK,MACvBC,QAAS,CAAC,IAAK,EAAG,IAAK,MACvBC,OAAQ,CAAC,IAAK,EAAG,EAAG,MACpBC,iBAAkB,CAAC,IAAK,IAAK,IAAK,MAClCC,WAAY,CAAC,EAAG,EAAG,IAAK,MACxBC,aAAc,CAAC,IAAK,GAAI,IAAK,MAC7BC,aAAc,CAAC,IAAK,IAAK,IAAK,MAC9BC,eAAgB,CAAC,GAAI,IAAK,IAAK,MAC/BC,gBAAiB,CAAC,IAAK,IAAK,IAAK,MACjCC,kBAAmB,CAAC,EAAG,IAAK,IAAK,MACjCC,gBAAiB,CAAC,GAAI,IAAK,IAAK,MAChCC,gBAAiB,CAAC,IAAK,GAAI,IAAK,MAChCC,aAAc,CAAC,GAAI,GAAI,IAAK,MAC5BC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,SAAU,CAAC,IAAK,IAAK,IAAK,MAC1BC,YAAa,CAAC,IAAK,IAAK,IAAK,MAC7BC,KAAM,CAAC,EAAG,EAAG,IAAK,MAClBC,QAAS,CAAC,IAAK,IAAK,IAAK,MACzBC,MAAO,CAAC,IAAK,IAAK,EAAG,MACrBC,UAAW,CAAC,IAAK,IAAK,GAAI,MAC1BC,OAAQ,CAAC,IAAK,IAAK,EAAG,MACtBC,UAAW,CAAC,IAAK,GAAI,EAAG,MACxBC,OAAQ,CAAC,IAAK,IAAK,IAAK,MACxBC,cAAe,CAAC,IAAK,IAAK,IAAK,MAC/BC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,cAAe,CAAC,IAAK,IAAK,IAAK,MAC/BC,cAAe,CAAC,IAAK,IAAK,IAAK,MAC/BC,WAAY,CAAC,IAAK,IAAK,IAAK,MAC5BC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,KAAM,CAAC,IAAK,IAAK,GAAI,MACrBC,KAAM,CAAC,IAAK,IAAK,IAAK,MACtBC,KAAM,CAAC,IAAK,IAAK,IAAK,MACtBC,WAAY,CAAC,IAAK,IAAK,IAAK,MAC5BC,OAAQ,CAAC,IAAK,EAAG,IAAK,MACtBC,cAAe,CAAC,IAAK,GAAI,IAAK,MAC9BC,IAAK,CAAC,IAAK,EAAG,EAAG,MACjBC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,UAAW,CAAC,GAAI,IAAK,IAAK,MAC1BC,YAAa,CAAC,IAAK,GAAI,GAAI,MAC3BC,OAAQ,CAAC,IAAK,IAAK,IAAK,MACxBC,WAAY,CAAC,IAAK,IAAK,GAAI,MAC3BC,SAAU,CAAC,GAAI,IAAK,GAAI,MACxBC,SAAU,CAAC,IAAK,IAAK,IAAK,MAC1BC,OAAQ,CAAC,IAAK,GAAI,GAAI,MACtBC,OAAQ,CAAC,IAAK,IAAK,IAAK,MACxBC,QAAS,CAAC,IAAK,IAAK,IAAK,MACzBC,UAAW,CAAC,IAAK,GAAI,IAAK,MAC1BC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,KAAM,CAAC,IAAK,IAAK,IAAK,MACtBC,YAAa,CAAC,EAAG,IAAK,IAAK,MAC3BC,UAAW,CAAC,GAAI,IAAK,IAAK,MAC1BC,IAAK,CAAC,IAAK,IAAK,IAAK,MACrBC,KAAM,CAAC,EAAG,IAAK,IAAK,MACpBC,QAAS,CAAC,IAAK,IAAK,IAAK,MACzBC,OAAQ,CAAC,IAAK,GAAI,GAAI,MACtBC,UAAW,CAAC,GAAI,IAAK,IAAK,MAC1BC,OAAQ,CAAC,IAAK,IAAK,IAAK,MACxBC,MAAO,CAAC,IAAK,IAAK,IAAK,MACvBC,MAAO,CAAC,IAAK,IAAK,IAAK,MACvBC,WAAY,CAAC,IAAK,IAAK,IAAK,MAC5BC,OAAQ,CAAC,IAAK,IAAK,EAAG,MACtBC,YAAa,CAAC,IAAK,IAAK,GAAI,OAGdr1C,EAAQwsB,YAAc,IAAI+e,EAAM,CAAC,EAAG,EAAG,EAAG,G,kBCpP5DzrC,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAGX,IAAIkH,EAAe,WAAc,SAASC,EAAiBC,EAAQC,GAAS,IAAK,IAAIC,EAAI,EAAGA,EAAID,EAAM/C,OAAQgD,IAAK,CAAE,IAAIC,EAAaF,EAAMC,GAAIC,EAAW1C,WAAa0C,EAAW1C,aAAc,EAAO0C,EAAWC,cAAe,EAAU,UAAWD,IAAYA,EAAWE,UAAW,GAAM5H,OAAOC,eAAesH,EAAQG,EAAWG,IAAKH,EAAa,CAAE,CAAE,OAAO,SAAUI,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYT,EAAiBQ,EAAYG,UAAWF,GAAiBC,GAAaV,EAAiBQ,EAAaE,GAAqBF,CAAa,CAAG,CAA7hB,GAEf+Y,EAAQ,EAAQ,OAEhBiD,EAAkB,EAAQ,OAI1B0xB,EAAgB,SAAuBC,EAAUC,GACjD,IAAIC,EAAU1sC,KAAKshB,IAAI0R,MAAM,KAAMwZ,EAASrvB,WAAWpa,KAAI,SAAU4pC,GACjE,OAAOA,EAAUxuB,IACrB,KACIyuB,EAAI,EAAI5sC,KAAKshB,IAAI,EAAGorB,GACxBF,EAASrvB,WAAW7M,SAAQ,SAAUq8B,GAClCF,EAAeI,aAAaD,EAAID,EAAUxuB,KAAMwuB,EAAUzuB,MAAM1P,WACpE,GACJ,EAEIyc,EAAiB,WACjB,SAASA,EAAe7jB,IAb5B,SAAyB9G,EAAUzB,GAAe,KAAMyB,aAAoBzB,GAAgB,MAAM,IAAI0B,UAAU,oCAAwC,CAchJC,CAAgBC,KAAMwqB,GAEtBxqB,KAAK2G,OAASA,GAAkBlD,SAAS+B,cAAc,UACvDxF,KAAKqsC,eAAiB1lC,CAC1B,CA+NA,OA7NAhJ,EAAa6sB,EAAgB,CAAC,CAC1BrsB,IAAK,SACL1H,MAAO,SAAgBwK,GACnBjB,KAAKoL,IAAMpL,KAAK2G,OAAO0E,WAAW,MAClCrL,KAAKiB,QAAUA,EACVjB,KAAKqsC,eACNrsC,KAAK2G,OAAON,MAAQ9G,KAAKo5B,MAAM13B,EAAQoF,MAAQpF,EAAQ+F,OACvDhH,KAAK2G,OAAOL,OAAS/G,KAAKo5B,MAAM13B,EAAQqF,OAASrF,EAAQ+F,OACzDhH,KAAK2G,OAAOjM,MAAM2L,MAAQpF,EAAQoF,MAAQ,KAC1CrG,KAAK2G,OAAOjM,MAAM4L,OAASrF,EAAQqF,OAAS,MAGhDtG,KAAKoL,IAAIpE,MAAMhH,KAAKiB,QAAQ+F,MAAOhH,KAAKiB,QAAQ+F,OAChDhH,KAAKoL,IAAIkhC,WAAWrrC,EAAQkG,GAAIlG,EAAQmG,GACxCpH,KAAKoL,IAAImhC,aAAe,SACxBtrC,EAAQC,OAAOkpB,IAAI,gCAAkCnpB,EAAQoF,MAAQ,IAAMpF,EAAQqF,OAAS,OAASrF,EAAQkG,EAAI,IAAMlG,EAAQmG,EAAI,gBAAkBpH,KAAKiB,QAAQ+F,MACtK,GACD,CACC7I,IAAK,OACL1H,MAAO,SAAc+1C,EAAWC,GAC5B,IAAIvqC,EAAQlC,KAERwsC,EAAUzxC,SACViF,KAAKoL,IAAIshC,OACTF,EAAU38B,SAAQ,SAAUwhB,GACxBnvB,EAAMmvB,KAAKA,GACXnvB,EAAMkJ,IAAImiB,MACd,KAGJkf,IAEID,EAAUzxC,QACViF,KAAKoL,IAAIuhC,SAEjB,GACD,CACCxuC,IAAK,YACL1H,MAAO,SAAmBixB,EAAOmC,EAAQ+iB,GACrC5sC,KAAKoL,IAAIK,UAAUic,EAAOmC,EAAO5a,KAAM4a,EAAO3a,IAAK2a,EAAOxjB,MAAOwjB,EAAOvjB,OAAQsmC,EAAY39B,KAAM29B,EAAY19B,IAAK09B,EAAYvmC,MAAOumC,EAAYtmC,OACtJ,GACD,CACCnI,IAAK,YACL1H,MAAO,SAAmB46B,EAAM5T,GAC5Bzd,KAAKqxB,KAAKA,GACVrxB,KAAKoL,IAAI+G,UAAYsL,EAAM1P,WAC3B/N,KAAKoL,IAAIyhC,MACb,GACD,CACC1uC,IAAK,OACL1H,MAAO,SAAcgnB,GACjBzd,KAAKoL,IAAI+G,UAAYsL,EAAM1P,WAC3B/N,KAAKoL,IAAIyhC,MACb,GACD,CACC1uC,IAAK,YACL1H,MAAO,WACH,OAAO0L,QAAQY,QAAQ/C,KAAK2G,OAChC,GACD,CACCxI,IAAK,OACL1H,MAAO,SAAcq2C,GACjB,IAAIppC,EAAS1D,KAEbA,KAAKoL,IAAI2hC,YACLluC,MAAMC,QAAQguC,GACdA,EAAMj9B,SAAQ,SAAUm9B,EAAO5tC,GAC3B,IAAI4lB,EAAQgoB,EAAMxyC,OAAS2c,EAAMG,KAAKwK,OAASkrB,EAAQA,EAAMhoB,MAC/C,IAAV5lB,EACAsE,EAAO0H,IAAI6hC,OAAOjoB,EAAM7d,EAAG6d,EAAM5d,GAEjC1D,EAAO0H,IAAI8hC,OAAOloB,EAAM7d,EAAG6d,EAAM5d,GAGjC4lC,EAAMxyC,OAAS2c,EAAMG,KAAKyK,cAC1Bre,EAAO0H,IAAI+hC,cAAcH,EAAMniB,aAAa1jB,EAAG6lC,EAAMniB,aAAazjB,EAAG4lC,EAAMliB,WAAW3jB,EAAG6lC,EAAMliB,WAAW1jB,EAAG4lC,EAAMjiB,IAAI5jB,EAAG6lC,EAAMjiB,IAAI3jB,EAE5I,IAEApH,KAAKoL,IAAIgiC,IAAIN,EAAM3lC,EAAI2lC,EAAMz1B,OAAQy1B,EAAM1lC,EAAI0lC,EAAMz1B,OAAQy1B,EAAMz1B,OAAQ,EAAa,EAAV9X,KAAKif,IAAQ,GAG/Fxe,KAAKoL,IAAIiiC,WACb,GACD,CACClvC,IAAK,YACL1H,MAAO,SAAmB0Q,EAAGC,EAAGf,EAAOC,EAAQmX,GAC3Czd,KAAKoL,IAAI+G,UAAYsL,EAAM1P,WAC3B/N,KAAKoL,IAAIgH,SAASjL,EAAGC,EAAGf,EAAOC,EACnC,GACD,CACCnI,IAAK,uBACL1H,MAAO,SAA8BsY,EAAQg9B,GACzC,IAAIuB,EAAiBttC,KAAKoL,IAAImiC,qBAAqBx+B,EAAOE,KAAO88B,EAASpvB,UAAUoC,GAAIhQ,EAAOG,IAAM68B,EAASpvB,UAAUsC,GAAIlQ,EAAOE,KAAO88B,EAASpvB,UAAUmC,GAAI/P,EAAOG,IAAM68B,EAASpvB,UAAUqC,IAEjM8sB,EAAcC,EAAUuB,GACxBttC,KAAKoL,IAAI+G,UAAYm7B,EACrBttC,KAAKoL,IAAIgH,SAASrD,EAAOE,KAAMF,EAAOG,IAAKH,EAAO1I,MAAO0I,EAAOzI,OACpE,GACD,CACCnI,IAAK,uBACL1H,MAAO,SAA8BsY,EAAQg9B,GACzC,IAAIpmC,EAAS3F,KAETmH,EAAI4H,EAAOE,KAAO88B,EAASxvB,OAAOpV,EAClCC,EAAI2H,EAAOG,IAAM68B,EAASxvB,OAAOnV,EAEjComC,EAAiBxtC,KAAKoL,IAAIqiC,qBAAqBtmC,EAAGC,EAAG,EAAGD,EAAGC,EAAG2kC,EAAS10B,OAAOlQ,GAClF,GAAKqmC,EAOL,GAHA1B,EAAcC,EAAUyB,GACxBxtC,KAAKoL,IAAI+G,UAAYq7B,EAEjBzB,EAAS10B,OAAOlQ,IAAM4kC,EAAS10B,OAAOjQ,EAAG,CAEzC,IAAIsmC,EAAO3+B,EAAOE,KAAO,GAAMF,EAAO1I,MAClCsnC,EAAO5+B,EAAOG,IAAM,GAAMH,EAAOzI,OACjC6lC,EAAIJ,EAAS10B,OAAOjQ,EAAI2kC,EAAS10B,OAAOlQ,EACxCymC,EAAO,EAAIzB,EAEfnsC,KAAK4X,UAAU81B,EAAMC,EAAM,CAAC,EAAG,EAAG,EAAGxB,EAAG,EAAG,IAAI,WAC3C,OAAOxmC,EAAOyF,IAAIgH,SAASrD,EAAOE,KAAM2+B,GAAQ7+B,EAAOG,IAAMy+B,GAAQA,EAAM5+B,EAAO1I,MAAO0I,EAAOzI,OAASsnC,EAC7G,GACJ,MACI5tC,KAAKoL,IAAIgH,SAASrD,EAAOE,KAAMF,EAAOG,IAAKH,EAAO1I,MAAO0I,EAAOzI,OAExE,GACD,CACCnI,IAAK,eACL1H,MAAO,SAAsB46B,EAAM3J,EAAOmmB,EAAW1X,EAASC,GAC1Dp2B,KAAKqxB,KAAKA,GACVrxB,KAAKoL,IAAI+G,UAAYnS,KAAKoL,IAAI0iC,cAAc9tC,KAAK+tC,YAAYrmB,EAAOmmB,GAAY,UAChF7tC,KAAKoL,IAAIkhC,UAAUnW,EAASC,GAC5Bp2B,KAAKoL,IAAIyhC,OACT7sC,KAAKoL,IAAIkhC,WAAWnW,GAAUC,EAClC,GACD,CACCj4B,IAAK,iBACL1H,MAAO,SAAwBikB,EAAY+C,EAAOlZ,EAAMqW,EAAgBozB,GACpE,IAAIC,EAASjuC,KAEbA,KAAKoL,IAAI7G,KAAO,CAACA,EAAKqV,UAAWrV,EAAKsV,YAAatV,EAAKuV,WAAYvV,EAAKiV,SAAUjV,EAAKoV,YAAY1W,KAAK,KAEzGyX,EAAW7K,SAAQ,SAAU5L,GAezB,GAdAgqC,EAAO7iC,IAAI+G,UAAYsL,EAAM1P,WACzBigC,GAAe/pC,EAAKA,KAAK8P,OAAOhZ,OAChCizC,EAAYvxC,MAAM,GAAG80B,UAAU1hB,SAAQ,SAAU8W,GAC7CsnB,EAAO7iC,IAAI8iC,YAAcvnB,EAAWlJ,MAAM1P,WAC1CkgC,EAAO7iC,IAAI+iC,cAAgBxnB,EAAWwP,QAAU8X,EAAOhtC,QAAQ+F,MAC/DinC,EAAO7iC,IAAIgjC,cAAgBznB,EAAWyP,QAAU6X,EAAOhtC,QAAQ+F,MAC/DinC,EAAO7iC,IAAIijC,WAAa1nB,EAAW0P,KAEnC4X,EAAO7iC,IAAIkjC,SAASrqC,EAAKA,KAAMA,EAAK8K,OAAOE,KAAMhL,EAAK8K,OAAOG,IAAMjL,EAAK8K,OAAOzI,OACnF,IAEA2nC,EAAO7iC,IAAIkjC,SAASrqC,EAAKA,KAAMA,EAAK8K,OAAOE,KAAMhL,EAAK8K,OAAOG,IAAMjL,EAAK8K,OAAOzI,QAG5D,OAAnBsU,EAAyB,CACzB,IAAIyd,EAAsBzd,EAAeyd,qBAAuB5a,EAChE7C,EAAewd,mBAAmBvoB,SAAQ,SAAUuoB,GAChD,OAAQA,GACJ,KAAKhe,EAAgBod,qBAAqBM,UAItC,IACIyW,EADwBN,EAAOhtC,QAAQwiB,YAAY+qB,WAAWjqC,GAC7BgqC,SAErCN,EAAOQ,UAAUxqC,EAAK8K,OAAOE,KAAM1P,KAAK+R,MAAMrN,EAAK8K,OAAOG,IAAMq/B,GAAWtqC,EAAK8K,OAAO1I,MAAO,EAAGgyB,GACjG,MACJ,KAAKje,EAAgBod,qBAAqBO,SACtCkW,EAAOQ,UAAUxqC,EAAK8K,OAAOE,KAAM1P,KAAK+R,MAAMrN,EAAK8K,OAAOG,KAAMjL,EAAK8K,OAAO1I,MAAO,EAAGgyB,GACtF,MACJ,KAAKje,EAAgBod,qBAAqBQ,aAEtC,IACI0W,EADyBT,EAAOhtC,QAAQwiB,YAAY+qB,WAAWjqC,GAC/BmqC,OAEpCT,EAAOQ,UAAUxqC,EAAK8K,OAAOE,KAAM1P,KAAKC,KAAKyE,EAAK8K,OAAOG,IAAMw/B,GAASzqC,EAAK8K,OAAO1I,MAAO,EAAGgyB,GAG1G,GACJ,CACJ,GACJ,GACD,CACCl6B,IAAK,cACL1H,MAAO,SAAqBixB,EAAOxV,GAC/B,GAAIwV,EAAMrhB,QAAU6L,EAAK7L,OAASqhB,EAAMphB,SAAW4L,EAAK5L,OACpD,OAAOohB,EAGX,IAAI/gB,EAAS3G,KAAK2G,OAAO3E,cAAcwD,cAAc,UAKrD,OAJAmB,EAAON,MAAQ6L,EAAK7L,MACpBM,EAAOL,OAAS4L,EAAK5L,OACXK,EAAO0E,WAAW,MACxBI,UAAUic,EAAO,EAAG,EAAGA,EAAMrhB,MAAOqhB,EAAMphB,OAAQ,EAAG,EAAG4L,EAAK7L,MAAO6L,EAAK5L,QACtEK,CACX,GACD,CACCxI,IAAK,aACL1H,MAAO,SAAoB0V,GACvBnM,KAAKoL,IAAIujC,YAAcxiC,CAC3B,GACD,CACChO,IAAK,YACL1H,MAAO,SAAmB0/B,EAASC,EAASxd,EAAQ6zB,GAChDzsC,KAAKoL,IAAIshC,OACT1sC,KAAKoL,IAAIkhC,UAAUnW,EAASC,GAC5Bp2B,KAAKoL,IAAIwM,UAAUgB,EAAO,GAAIA,EAAO,GAAIA,EAAO,GAAIA,EAAO,GAAIA,EAAO,GAAIA,EAAO,IACjF5Y,KAAKoL,IAAIkhC,WAAWnW,GAAUC,GAE9BqW,IAEAzsC,KAAKoL,IAAIuhC,SACb,KAGGniB,CACX,CAtOqB,GAwOrBh0B,EAAA,QAAkBg0B,C,gBC9PlBl0B,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEX,IAAIyxB,EAAa1xB,EAAQ0xB,WAAa,CAClCH,QAAS,EACTzB,OAAQ,EACRsoB,SAAU,GAGQp4C,EAAQuwB,gBAAkB,SAAyB/X,GACrE,OAAQA,GACJ,IAAK,SACD,OAAOkZ,EAAW5B,OACtB,IAAK,WACD,OAAO4B,EAAW0mB,SAEtB,QACI,OAAO1mB,EAAWH,QAE9B,C,gBCnBAzxB,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEcD,EAAQsvB,mBAAqB,SAA4BrL,GAC9E,GAAsB,WAAlBA,EACA,OAAO,EAEX,IAAIhkB,EAAQwgB,WAAWwD,GACvB,OAAOvD,MAAMzgB,GAAS,EAAIA,CAC9B,C,kBCTAH,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAGX,IAkBgC+E,EAlB5BmS,EAAgb,SAAUX,EAAKjP,GAAK,GAAIc,MAAMC,QAAQkO,GAAQ,OAAOA,EAAY,GAAIC,OAAOC,YAAY5W,OAAO0W,GAAQ,OAAxf,SAAuBA,EAAKjP,GAAK,IAAIoP,EAAO,GAAQC,GAAK,EAAUC,GAAK,EAAWC,OAAKxW,EAAW,IAAM,IAAK,IAAiCyW,EAA7BC,EAAKR,EAAIC,OAAOC,cAAmBE,GAAMG,EAAKC,EAAGlR,QAAQC,QAAoB4Q,EAAK3Q,KAAK+Q,EAAG9W,QAAYsH,GAAKoP,EAAKpS,SAAWgD,GAA3DqP,GAAK,GAAkE,CAAE,MAAOK,GAAOJ,GAAK,EAAMC,EAAKG,CAAK,CAAE,QAAU,KAAWL,GAAMI,EAAW,QAAGA,EAAW,QAAK,CAAE,QAAU,GAAIH,EAAI,MAAMC,CAAI,CAAE,CAAE,OAAOH,CAAM,CAAuHO,CAAcV,EAAKjP,GAAa,MAAM,IAAI+B,UAAU,uDAA2D,EAE7oBnC,EAAe,WAAc,SAASC,EAAiBC,EAAQC,GAAS,IAAK,IAAIC,EAAI,EAAGA,EAAID,EAAM/C,OAAQgD,IAAK,CAAE,IAAIC,EAAaF,EAAMC,GAAIC,EAAW1C,WAAa0C,EAAW1C,aAAc,EAAO0C,EAAWC,cAAe,EAAU,UAAWD,IAAYA,EAAWE,UAAW,GAAM5H,OAAOC,eAAesH,EAAQG,EAAWG,IAAKH,EAAa,CAAE,CAAE,OAAO,SAAUI,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYT,EAAiBQ,EAAYG,UAAWF,GAAiBC,GAAaV,EAAiBQ,EAAaE,GAAqBF,CAAa,CAAG,CAA7hB,GAEfoC,EAAU,EAAQ,OAIlBquC,GAFQ,EAAQ,OAEJ,EAAQ,QAIpBz7B,GAM4B5X,EARX,EAAQ,SAQwBA,EAAIE,WAAaF,EAAM,CAAEsF,QAAStF,GAJnFzE,EAAc,EAAQ,OAEtB6sB,EAAU,EAAQ,OAMlBkrB,EAAW,WACX,SAASA,EAASjxC,EAAQoD,IAH9B,SAAyBpB,EAAUzB,GAAe,KAAMyB,aAAoBzB,GAAgB,MAAM,IAAI0B,UAAU,oCAAwC,CAIhJC,CAAgBC,KAAM8uC,GAEtB9uC,KAAKnC,OAASA,EACdmC,KAAKiB,QAAUA,EACfpD,EAAOwlB,OAAOpiB,EAClB,CA0OA,OAxOAtD,EAAamxC,EAAU,CAAC,CACpB3wC,IAAK,aACL1H,MAAO,SAAoB+c,GACnBA,EAAUU,cACVlU,KAAK+uC,+BAA+Bv7B,GACpCxT,KAAKgvC,kBAAkBx7B,GAE/B,GACD,CACCrV,IAAK,oBACL1H,MAAO,SAA2B+c,GAC9B,IAAItR,EAAQlC,KAERysC,EAAW,WAYX,GAXIj5B,EAAUhE,WAAWzU,QACrByY,EAAUhE,WAAWK,SAAQ,SAAU/H,GACnC,GAAIA,aAAiBsL,EAAgBtS,QAAS,CAC1C,IAAIpG,EAAQoN,EAAMlM,OAAOlB,MACzBwH,EAAMrE,OAAOoxC,eAAennC,EAAMiH,OAAQrU,EAAM+iB,MAAO/iB,EAAM6J,KAAM7J,EAAMkgB,eAAgBlgB,EAAMisB,WACnG,MACIzkB,EAAMrE,OAAOqxC,UAAUpnC,EAAO0L,EAAU9Y,MAAM+iB,MAEtD,IAGAjK,EAAUkU,MAAO,CACjB,IAAIynB,EAASjtC,EAAMjB,QAAQuiB,WAAWjoB,IAAIiY,EAAUkU,OACpD,GAAIynB,EAAQ,CACR,IAAIC,GAAa,EAAI5uC,EAAQkvB,qBAAqBlc,EAAUzE,OAAQyE,EAAU9Y,MAAM8rB,QAAShT,EAAU9Y,MAAMyU,QACzGkgC,EAAiC,iBAAjBF,EAAO9oC,OAAsB8oC,EAAO9oC,MAAQ,EAAI8oC,EAAO9oC,MAAQ+oC,EAAW/oC,MAC1FipC,EAAmC,iBAAlBH,EAAO7oC,QAAuB6oC,EAAO7oC,OAAS,EAAI6oC,EAAO7oC,OAAS8oC,EAAW9oC,OAC9F+oC,EAAS,GAAKC,EAAU,GACxBptC,EAAMrE,OAAO0vB,KAAK,EAAC,EAAI/sB,EAAQwnB,yBAAyBxU,EAAUgU,gBAAgB,WAC9EtlB,EAAMrE,OAAO4N,UAAU0jC,EAAQ,IAAI3uC,EAAQ0a,OAAO,EAAG,EAAGm0B,EAAQC,GAAUF,EAC9E,GAER,CACJ,CACJ,EACIG,EAAQ/7B,EAAUsU,eAClBynB,EAAMx0C,OACNiF,KAAKnC,OAAO0vB,KAAKgiB,EAAO9C,GAExBA,GAER,GACD,CACCtuC,IAAK,iCACL1H,MAAO,SAAwC+c,GAC3C,IAAI9P,EAAS1D,KAETwvC,GAAkBh8B,EAAU9Y,MAAMmb,WAAW1S,gBAAgB2S,iBAAmBtC,EAAU9Y,MAAMmb,WAAWxT,gBAAgBtH,OAE3H00C,EAAuBj8B,EAAU9Y,MAAMyU,OAAOugC,MAAK,SAAUvgC,GAC7D,OAAOA,EAAO2H,cAAgB8M,EAAQ3N,aAAa/e,OAASiY,EAAO0H,YAAYf,eACnF,IAEI22B,EAAW,WACX,IAAIkD,GAAyB,EAAI54C,EAAY+0B,iCAAiCtY,EAAUgU,aAAchU,EAAU9Y,MAAMmb,WAAWuY,gBAE7HohB,GACA9rC,EAAO7F,OAAO0vB,KAAK,CAACoiB,IAAyB,WACpCn8B,EAAU9Y,MAAMmb,WAAW1S,gBAAgB2S,iBAC5CpS,EAAO7F,OAAOgvC,KAAKr5B,EAAU9Y,MAAMmb,WAAW1S,iBAGlDO,EAAOksC,sBAAsBp8B,EACjC,IAGJA,EAAU9Y,MAAMyU,OAAOU,SAAQ,SAAUV,EAAQyH,GACzCzH,EAAO2H,cAAgB8M,EAAQ3N,aAAa/e,MAASiY,EAAO0H,YAAYf,iBACxEpS,EAAOmsC,aAAa1gC,EAAQyH,EAAMpD,EAAUgU,aAEpD,GACJ,EAEA,GAAIgoB,GAAkBC,EAAsB,CACxC,IAAIF,EAAQ/7B,EAAU5X,OAAS4X,EAAU5X,OAAOksB,eAAiB,GAC7DynB,EAAMx0C,OACNiF,KAAKnC,OAAO0vB,KAAKgiB,EAAO9C,GAExBA,GAER,CACJ,GACD,CACCtuC,IAAK,wBACL1H,MAAO,SAA+B+c,GAClC,IAAI7N,EAAS3F,KAEbwT,EAAU9Y,MAAMmb,WAAWxT,gBAAgB5F,MAAM,GAAG80B,UAAU1hB,SAAQ,SAAUxN,GACtC,QAAlCA,EAAgBwnB,OAAOtnB,QAAoBF,EAAgBwnB,OAAOpnB,KAAK1H,OACvE4K,EAAOmqC,uBAAuBt8B,EAAWnR,GAClC,YAAYuI,KAAKvI,EAAgBwnB,OAAOtnB,SAC/CoD,EAAOoqC,yBAAyBv8B,EAAWnR,EAEnD,GACJ,GACD,CACClE,IAAK,yBACL1H,MAAO,SAAgC+c,EAAWqC,GAC9C,IAAI6R,EAAQ1nB,KAAKiB,QAAQuiB,WAAWjoB,IAAIsa,EAAWgU,OAAOpnB,KAAK,IAC/D,GAAIilB,EAAO,CACP,IAAIuG,GAA4B,EAAIl3B,EAAY80B,oCAAoCrY,EAAU9Y,MAAMmb,WAAW4X,iBAAkBja,EAAUzE,OAAQyE,EAAU9Y,MAAM8rB,QAAShT,EAAU9Y,MAAMyU,QACxL6gC,GAAsB,EAAIj5C,EAAYi1B,yBAAyBnW,EAAY6R,EAAOuG,GAClFhzB,GAAW,EAAIlE,EAAY60B,6BAA6B/V,EAAW5a,SAAU+0C,EAAqB/hB,GAClG6e,GAAQ,EAAI/1C,EAAY40B,+BAA+B9V,EAAY5a,EAAU+0C,EAAqB/hB,EAA2Bza,EAAUzE,QAEvIkhC,EAAW1wC,KAAK+R,MAAM2c,EAA0Bhf,KAAOhU,EAASkM,GAChE+oC,EAAW3wC,KAAK+R,MAAM2c,EAA0B/e,IAAMjU,EAASmM,GACnEpH,KAAKnC,OAAOsyC,aAAarD,EAAOplB,EAAOsoB,EAAqBC,EAAUC,EAC1E,CACJ,GACD,CACC/xC,IAAK,2BACL1H,MAAO,SAAkC+c,EAAWqC,GAChD,IAAIoY,GAA4B,EAAIl3B,EAAY80B,oCAAoCrY,EAAU9Y,MAAMmb,WAAW4X,iBAAkBja,EAAUzE,OAAQyE,EAAU9Y,MAAM8rB,QAAShT,EAAU9Y,MAAMyU,QACxL6gC,GAAsB,EAAIj5C,EAAYg1B,iCAAiClW,EAAYoY,GACnFhzB,GAAW,EAAIlE,EAAY60B,6BAA6B/V,EAAW5a,SAAU+0C,EAAqB/hB,GAClGmiB,EAAiB,IAAI5vC,EAAQ0a,OAAO3b,KAAK+R,MAAM2c,EAA0Bhf,KAAOhU,EAASkM,GAAI5H,KAAK+R,MAAM2c,EAA0B/e,IAAMjU,EAASmM,GAAI4oC,EAAoB3pC,MAAO2pC,EAAoB1pC,QAEpMylC,GAAW,EAAI8C,EAAUtzB,eAAe/H,EAAWqC,EAAWgU,OAAQumB,GAC1E,GAAIrE,EACA,OAAQA,EAASvxC,MACb,KAAKq0C,EAAUlzB,cAAcQ,gBAEzBnc,KAAKnC,OAAOwyC,qBAAqBD,EAAgBrE,GACjD,MACJ,KAAK8C,EAAUlzB,cAAcS,gBAEzBpc,KAAKnC,OAAOyyC,qBAAqBF,EAAgBrE,GAIjE,GACD,CACC5tC,IAAK,eACL1H,MAAO,SAAsB0Y,EAAQyH,EAAM25B,GACvCvwC,KAAKnC,OAAOqxC,WAAU,EAAI1uC,EAAQivB,oBAAoB8gB,EAAa35B,GAAOzH,EAAO0H,YACrF,GACD,CACC1Y,IAAK,cACL1H,MAAO,SAAqBgd,GACxB,IAAIw6B,EAASjuC,KAEb,GAAIyT,EAAMD,UAAUU,YAAa,CAC7B,IAAIs8B,EAAW/8B,EAAM+X,aACjBglB,IAAaxwC,KAAKwwC,WAClBxwC,KAAKnC,OAAO4yC,WAAWh9B,EAAM+X,cAC7BxrB,KAAKwwC,SAAWA,GAGpB,IAAI/rB,EAAahR,EAAMD,UAAU9Y,MAAMkd,UACpB,OAAf6M,EACAzkB,KAAKnC,OAAO+Z,UAAUnE,EAAMD,UAAUzE,OAAOE,KAAOwV,EAAWvM,gBAAgB,GAAGzhB,MAAOgd,EAAMD,UAAUzE,OAAOG,IAAMuV,EAAWvM,gBAAgB,GAAGzhB,MAAOguB,EAAW7M,WAAW,WAC7K,OAAOq2B,EAAOyC,mBAAmBj9B,EACrC,IAEAzT,KAAK0wC,mBAAmBj9B,EAEhC,CACJ,GACD,CACCtV,IAAK,qBACL1H,MAAO,SAA4Bgd,GAC/B,IAAIk9B,EAAwBC,EAAsBn9B,GAC9Co9B,EAAyBljC,EAAegjC,EAAuB,GAC/DG,EAAiBD,EAAuB,GACxCE,EAAyCF,EAAuB,GAChEG,EAAiBH,EAAuB,GACxCI,EAAsBJ,EAAuB,GAC7CK,EAA2BL,EAAuB,GAElDM,EAAoBC,EAAiB39B,GACrC49B,EAAqB1jC,EAAewjC,EAAmB,GACvDG,EAAcD,EAAmB,GACjCE,EAAiBF,EAAmB,GAMxCrxC,KAAK+uC,+BAA+Bt7B,EAAMD,WAE1Cs9B,EAAeU,KAAKC,GAAc5hC,QAAQ7P,KAAK0xC,YAAa1xC,MAE5DA,KAAKgvC,kBAAkBv7B,EAAMD,WAC7B+9B,EAAe1hC,QAAQ7P,KAAK2xC,WAAY3xC,MAKxCixC,EAAoBphC,QAAQ7P,KAAK0xC,YAAa1xC,MAE9CkxC,EAAyBrhC,QAAQ7P,KAAK0xC,YAAa1xC,MACnDsxC,EAAYzhC,QAAQ7P,KAAK2xC,WAAY3xC,MAWrC+wC,EAAuClhC,QAAQ7P,KAAK0xC,YAAa1xC,MAGjEgxC,EAAeQ,KAAKC,GAAc5hC,QAAQ7P,KAAK0xC,YAAa1xC,KAChE,GACD,CACC7B,IAAK,SACL1H,MAAO,SAAgBgd,GAcnB,OAXIzT,KAAKiB,QAAQkC,iBACbnD,KAAKnC,OAAO4wC,UAAUzuC,KAAKiB,QAAQkG,EAAGnH,KAAKiB,QAAQmG,EAAGpH,KAAKiB,QAAQoF,MAAOrG,KAAKiB,QAAQqF,OAAQtG,KAAKiB,QAAQkC,iBAEhHnD,KAAK0xC,YAAYj+B,GACJzT,KAAKnC,OAAO+zC,WAQ7B,KAGG9C,CACX,CAlPe,GAoPft4C,EAAA,QAAkBs4C,EAGlB,IAAIsC,EAAmB,SAA0B39B,GAK7C,IAJA,IAAI69B,EAAc,GACdC,EAAiB,GAEjBx2C,EAAS0Y,EAAMyB,SAASna,OACnBgD,EAAI,EAAGA,EAAIhD,EAAQgD,IAAK,CAC7B,IAAI+J,EAAQ2L,EAAMyB,SAASnX,GACvB+J,EAAM+pC,gBACNP,EAAY90C,KAAKsL,GAEjBypC,EAAe/0C,KAAKsL,EAE5B,CACA,MAAO,CAACwpC,EAAaC,EACzB,EAEIX,EAAwB,SAA+Bn9B,GAOvD,IANA,IAAIq9B,EAAiB,GACjBC,EAAyC,GACzCC,EAAiB,GACjBC,EAAsB,GACtBC,EAA2B,GAC3Bn2C,EAAS0Y,EAAMwB,SAASla,OACnBgD,EAAI,EAAGA,EAAIhD,EAAQgD,IAAK,CAC7B,IAAI+J,EAAQ2L,EAAMwB,SAASlX,GACvB+J,EAAM0L,UAAUsB,gBAAkBhN,EAAM0L,UAAU9Y,MAAMyR,QAAU,GAAKrE,EAAM0L,UAAUkC,gBACnF5N,EAAM0L,UAAU9Y,MAAMusB,OAAO6qB,MAAQ,EACrChB,EAAet0C,KAAKsL,GACbA,EAAM0L,UAAU9Y,MAAMusB,OAAO6qB,MAAQ,EAC5Cd,EAAex0C,KAAKsL,GAEpBipC,EAAuCv0C,KAAKsL,GAG5CA,EAAM0L,UAAUoC,aAChBq7B,EAAoBz0C,KAAKsL,GAEzBopC,EAAyB10C,KAAKsL,EAG1C,CACA,MAAO,CAACgpC,EAAgBC,EAAwCC,EAAgBC,EAAqBC,EACzG,EAEIO,EAAe,SAAsBhnC,EAAGyX,GACxC,OAAIzX,EAAE+I,UAAU9Y,MAAMusB,OAAO6qB,MAAQ5vB,EAAE1O,UAAU9Y,MAAMusB,OAAO6qB,MACnD,EACArnC,EAAE+I,UAAU9Y,MAAMusB,OAAO6qB,MAAQ5vB,EAAE1O,UAAU9Y,MAAMusB,OAAO6qB,OACzD,EAGLrnC,EAAE+I,UAAUpU,MAAQ8iB,EAAE1O,UAAUpU,MAAQ,GAAK,CACxD,C,gBCrUA9I,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEX,IAAI4vB,EAAW7vB,EAAQ6vB,SAAW,CAC9B0B,QAAS,EACTzB,OAAQ,EACRyrB,OAAQ,EACRrlB,KAAM,GAGUl2B,EAAQ4vB,cAAgB,SAAuB/lB,GAC/D,OAAQA,GACJ,IAAK,SACD,OAAOgmB,EAASC,OACpB,IAAK,SACD,OAAOD,EAAS0rB,OACpB,IAAK,OACD,OAAO1rB,EAASqG,KAEpB,QACI,OAAOrG,EAAS0B,QAE5B,C,gBCtBAzxB,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEOD,EAAQ0wB,YAAc,SAAqBD,GACzD,IAAIoB,EAAkB,SAAXpB,EACX,MAAO,CACHoB,KAAMA,EACNypB,MAAOzpB,EAAO,EAAIrO,SAASiN,EAAQ,IAE3C,C,kBCTA3wB,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQw7C,mBAAgBl7C,EAExB,IAQgC0E,EAR5BmC,EAAe,WAAc,SAASC,EAAiBC,EAAQC,GAAS,IAAK,IAAIC,EAAI,EAAGA,EAAID,EAAM/C,OAAQgD,IAAK,CAAE,IAAIC,EAAaF,EAAMC,GAAIC,EAAW1C,WAAa0C,EAAW1C,aAAc,EAAO0C,EAAWC,cAAe,EAAU,UAAWD,IAAYA,EAAWE,UAAW,GAAM5H,OAAOC,eAAesH,EAAQG,EAAWG,IAAKH,EAAa,CAAE,CAAE,OAAO,SAAUI,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYT,EAAiBQ,EAAYG,UAAWF,GAAiBC,GAAaV,EAAiBQ,EAAaE,GAAqBF,CAAa,CAAG,CAA7hB,GAIfic,GAI4B7e,EANjB,EAAQ,SAM8BA,EAAIE,WAAaF,EAAM,CAAEsF,QAAStF,GAFnFiF,EAAS,EAAQ,OAIrB,SAASV,EAAgBF,EAAUzB,GAAe,KAAMyB,aAAoBzB,GAAgB,MAAM,IAAI0B,UAAU,oCAAwC,CAExJ,IAAImyC,EAAiB,WACjB,SAASA,EAAehxC,EAASC,EAAQQ,GACrC3B,EAAgBC,KAAMiyC,GAEtBjyC,KAAKiB,QAAUA,EACfjB,KAAKkyC,QAAUxwC,EACf1B,KAAKuY,OAASvY,KAAKmyC,UAAUzwC,EAAO0wC,SAAStuC,MAC7C9D,KAAKuG,MAAQ,CAAC,EACdvG,KAAKkB,OAASA,EACdlB,KAAKqyC,OAAS,CAClB,CA+KA,OA7KA10C,EAAas0C,EAAgB,CAAC,CAC1B9zC,IAAK,YACL1H,MAAO,SAAmBmM,GACtB,IAAIV,EAAQlC,KAEZ,GAAIA,KAAKsyC,mBAAmB1vC,GACxB,OAAOA,EAGX,IAAK2vC,EAAM3vC,IAAQyX,EAAUvZ,QAAQ2Q,oBAAqB,CACtD,IAAgC,IAA5BzR,KAAKiB,QAAQyF,YAAuB8rC,EAAc5vC,IAAQ5C,KAAKyyC,aAAa7vC,GAC5E,OAAO5C,KAAK0yC,SAAS9vC,EAAKA,GAAK,GAC5B,IAAK5C,KAAKyyC,aAAa7vC,GAAM,CAChC,GAAkC,iBAAvB5C,KAAKiB,QAAQ6F,MAIpB,OAHA9G,KAAKuG,MAAM3D,IAAO,EAAInC,EAAOyN,OAAOtL,EAAK5C,KAAKiB,SAASyB,MAAK,SAAUE,GAClE,OAAO+vC,EAAW/vC,EAAKV,EAAMjB,QAAQ2F,cAAgB,EACzD,IACOhE,EACJ,IAA6B,IAAzB5C,KAAKiB,QAAQiG,SAAoBmT,EAAUvZ,QAAQ4R,oBAC1D,OAAO1S,KAAK0yC,SAAS9vC,EAAKA,GAAK,EAEvC,CACJ,CACJ,GACD,CACCzE,IAAK,cACL1H,MAAO,SAAqBmM,GACxB,IAAIc,EAAS1D,KAEb,OAAIwyC,EAAc5vC,GACP+vC,EAAW/vC,EAAK5C,KAAKiB,QAAQ2F,cAAgB,GAEpD5G,KAAKsyC,mBAAmB1vC,GACjB5C,KAAKuG,MAAM3D,GAEjB5C,KAAKyyC,aAAa7vC,IAAsC,iBAAvB5C,KAAKiB,QAAQ6F,MAM5C9G,KAAK4yC,SAAShwC,GALV5C,KAAKuG,MAAM3D,IAAO,EAAInC,EAAOyN,OAAOtL,EAAK5C,KAAKiB,SAASyB,MAAK,SAAUE,GACzE,OAAO+vC,EAAW/vC,EAAKc,EAAOzC,QAAQ2F,cAAgB,EAC1D,GAIR,GACD,CACCzI,IAAK,WACL1H,MAAO,SAAkBmM,GACrB,IAAI+C,EAAS3F,KAoCb,OAlCAA,KAAKuG,MAAM3D,GAAO,IAAIT,SAAQ,SAAUY,EAAS6B,GAC7C,IAAIywB,EAAM,IAAIxiB,eAoBd,GAnBAwiB,EAAIhmB,mBAAqB,WACrB,GAAuB,IAAnBgmB,EAAI5lB,WACJ,GAAmB,MAAf4lB,EAAIE,OACJ3wB,EAAO,yBAA2BhC,EAAI2a,UAAU,EAAG,KAAO,qBAAuB8X,EAAIE,YAClF,CACH,IAAI1wB,EAAS,IAAIC,WACjBD,EAAO0iB,iBAAiB,QAAQ,WAE5B,IAAItiB,EAASJ,EAAOI,OACpBlC,EAAQkC,EACZ,IAAG,GACHJ,EAAO0iB,iBAAiB,SAAS,SAAUzkB,GACvC,OAAO8B,EAAO9B,EAClB,IAAG,GACH+B,EAAOK,cAAcmwB,EAAI3wB,SAC7B,CAER,EACA2wB,EAAIviB,aAAe,OACfnN,EAAO1E,QAAQ2F,aAAc,CAC7B,IAAI6uB,EAAU9vB,EAAO1E,QAAQ2F,aAC7ByuB,EAAII,QAAUA,EACdJ,EAAIK,UAAY,WACZ,OAAO9wB,EAAmH,GAC9H,CACJ,CACAywB,EAAI3mB,KAAK,MAAO9L,GAAK,GACrByyB,EAAIM,MACR,IAAGjzB,MAAK,SAAUE,GACd,OAAO+vC,EAAW/vC,EAAK+C,EAAO1E,QAAQ2F,cAAgB,EAC1D,IAEO5G,KAAKuG,MAAM3D,EACtB,GACD,CACCzE,IAAK,aACL1H,MAAO,SAAoBwL,GACvB,IAAI9D,EAAMo1B,OAAOvzB,KAAKqyC,UAEtB,OADAryC,KAAKuG,MAAMpI,GAAOgE,QAAQY,QAAQd,GAC3B9D,CACX,GACD,CACCA,IAAK,qBACL1H,MAAO,SAA4B0H,GAC/B,YAAkC,IAApB6B,KAAKuG,MAAMpI,EAC7B,GACD,CACCA,IAAK,WACL1H,MAAO,SAAkB0H,EAAKyE,EAAKsE,GAC/B,IAAI+mC,EAASjuC,KAMT6yC,EAAmB,SAA0BC,GAC7C,OAAO,IAAI3wC,SAAQ,SAAUY,EAAS6B,GAClC,IAAIjC,EAAM,IAAI+O,MAiBd,GAhBA/O,EAAIqC,OAAS,WACT,OAAOjC,EAAQJ,EACnB,EAEKmwC,IAAsB5rC,IACvBvE,EAAIgQ,YAAc,aAGtBhQ,EAAIoC,QAAUH,EACdjC,EAAIC,IAAMA,GACW,IAAjBD,EAAImP,UAEJC,YAAW,WACPhP,EAAQJ,EACZ,GAAG,KAEHsrC,EAAOhtC,QAAQ2F,aAAc,CAC7B,IAAI6uB,EAAUwY,EAAOhtC,QAAQ2F,aAC7BmL,YAAW,WACP,OAAOnN,EAAmH,GAC9H,GAAG6wB,EACP,CACJ,GACJ,EAIA,OAFAz1B,KAAKuG,MAAMpI,GAAO40C,EAAoBnwC,KAAS2vC,EAAM3vC,GACrDyX,EAAUvZ,QAAQ8Q,uBAAuBhP,GAAKF,KAAKmwC,GAAoBA,GAAiB,GACjF10C,CACX,GACD,CACCA,IAAK,eACL1H,MAAO,SAAsBu8C,GACzB,OAAOhzC,KAAKmyC,UAAUa,KAAShzC,KAAKuY,MACxC,GACD,CACCpa,IAAK,YACL1H,MAAO,SAAmBu8C,GACtB,IAAIC,EAAOjzC,KAAKkzC,QAAUlzC,KAAKkzC,MAAQlzC,KAAKkyC,QAAQzuC,SAAS+B,cAAc,MAG3E,OAFAytC,EAAKnvC,KAAOkvC,EACZC,EAAKnvC,KAAOmvC,EAAKnvC,KACVmvC,EAAKE,SAAWF,EAAKG,SAAWH,EAAKI,IAChD,GACD,CACCl1C,IAAK,QACL1H,MAAO,WACH,IAAI68C,EAAStzC,KAETyW,EAAOngB,OAAOmgB,KAAKzW,KAAKuG,OACxBkS,EAAShC,EAAKnU,KAAI,SAAU3G,GAC5B,OAAO23C,EAAO/sC,MAAM5K,GAAKkH,OAAM,SAAUC,GAIrC,OAAO,IACX,GACJ,IACA,OAAOX,QAAQC,IAAIqW,GAAQ/V,MAAK,SAAU6wC,GAItC,OAAO,IAAIvB,EAAcv7B,EAAM88B,EACnC,GACJ,KAGGtB,CACX,CA1LqB,GA4LrBz7C,EAAA,QAAkBy7C,EAElB,IAAID,EAAgBx7C,EAAQw7C,cAAgB,WACxC,SAASA,EAAcv7B,EAAM+8B,GACzBzzC,EAAgBC,KAAMgyC,GAEtBhyC,KAAKyzC,MAAQh9B,EACbzW,KAAK0zC,WAAaF,CACtB,CAUA,OARA71C,EAAaq0C,EAAe,CAAC,CACzB7zC,IAAK,MACL1H,MAAO,SAAa0H,GAChB,IAAIiB,EAAQY,KAAKyzC,MAAMx/B,QAAQ9V,GAC/B,OAAkB,IAAXiB,EAAe,KAAOY,KAAK0zC,WAAWt0C,EACjD,KAGG4yC,CACX,CAjB4C,GAmBxC2B,EAAa,yBACbC,EAAgB,2BAChBC,EAAa,mBAEbrB,EAAgB,SAAuB5vC,GACvC,OAAOixC,EAAWjpC,KAAKhI,EAC3B,EACImwC,EAAsB,SAA6BnwC,GACnD,OAAOgxC,EAAchpC,KAAKhI,EAC9B,EAEI2vC,EAAQ,SAAe3vC,GACvB,MAAwC,QAAjCA,EAAIuW,QAAQ,GAAGxC,eAA2Bg9B,EAAW/oC,KAAKhI,EACrE,EAEI+vC,EAAa,SAAoB/vC,EAAK6yB,GACtC,OAAO,IAAItzB,SAAQ,SAAUY,EAAS6B,GAClC,IAAIjC,EAAM,IAAI+O,MACd/O,EAAIqC,OAAS,WACT,OAAOjC,EAAQJ,EACnB,EACAA,EAAIoC,QAAUH,EACdjC,EAAIC,IAAMA,GACW,IAAjBD,EAAImP,UAEJC,YAAW,WACPhP,EAAQJ,EACZ,GAAG,KAEH8yB,GACA1jB,YAAW,WACP,OAAOnN,EAA+F,GAC1G,GAAG6wB,EAEX,GACJ,C,kBCrQAn/B,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQktB,iBAAc5sB,EAEtB,IAAI6G,EAAe,WAAc,SAASC,EAAiBC,EAAQC,GAAS,IAAK,IAAIC,EAAI,EAAGA,EAAID,EAAM/C,OAAQgD,IAAK,CAAE,IAAIC,EAAaF,EAAMC,GAAIC,EAAW1C,WAAa0C,EAAW1C,aAAc,EAAO0C,EAAWC,cAAe,EAAU,UAAWD,IAAYA,EAAWE,UAAW,GAAM5H,OAAOC,eAAesH,EAAQG,EAAWG,IAAKH,EAAa,CAAE,CAAE,OAAO,SAAUI,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYT,EAAiBQ,EAAYG,UAAWF,GAAiBC,GAAaV,EAAiBQ,EAAaE,GAAqBF,CAAa,CAAG,CAA7hB,GAEfI,EAAQ,EAAQ,OAIhBs1C,EAAc,cAEAt9C,EAAQktB,YAAc,WACpC,SAASA,EAAYjgB,IALzB,SAAyB5D,EAAUzB,GAAe,KAAMyB,aAAoBzB,GAAgB,MAAM,IAAI0B,UAAU,oCAAwC,CAMhJC,CAAgBC,KAAM0jB,GAEtB1jB,KAAK+zC,MAAQ,CAAC,EACd/zC,KAAKg0C,UAAYvwC,CACrB,CAgEA,OA9DA9F,EAAa+lB,EAAa,CAAC,CACvBvlB,IAAK,gBACL1H,MAAO,SAAuB8N,GAC1B,IAAIiP,EAAYxT,KAAKg0C,UAAUxuC,cAAc,OACzC7C,EAAM3C,KAAKg0C,UAAUxuC,cAAc,OACnCyuC,EAAOj0C,KAAKg0C,UAAUxuC,cAAc,QAEpCkF,EAAO1K,KAAKg0C,UAAUtpC,KAC1B,IAAKA,EACD,MAAM,IAAIulB,MAAqF,IAGnGzc,EAAU9Y,MAAMsU,WAAa,SAC7BwE,EAAU9Y,MAAMif,WAAapV,EAAKoV,WAClCnG,EAAU9Y,MAAM8e,SAAWjV,EAAKiV,SAChChG,EAAU9Y,MAAMwrB,OAAS,IACzB1S,EAAU9Y,MAAM8rB,QAAU,IAE1B9b,EAAKhF,YAAY8N,GAEjB7Q,EAAIC,IAAMpE,EAAM6jB,YAChB1f,EAAI0D,MAAQ,EACZ1D,EAAI2D,OAAS,EAEb3D,EAAIjI,MAAMwrB,OAAS,IACnBvjB,EAAIjI,MAAM8rB,QAAU,IACpB7jB,EAAIjI,MAAMw5C,cAAgB,WAE1BD,EAAKv5C,MAAMif,WAAapV,EAAKoV,WAC7Bs6B,EAAKv5C,MAAM8e,SAAWjV,EAAKiV,SAC3By6B,EAAKv5C,MAAMwrB,OAAS,IACpB+tB,EAAKv5C,MAAM8rB,QAAU,IAErBytB,EAAKvuC,YAAY1F,KAAKg0C,UAAUtrC,eAAeorC,IAC/CtgC,EAAU9N,YAAYuuC,GACtBzgC,EAAU9N,YAAY/C,GACtB,IAAI4rC,EAAW5rC,EAAIwxC,UAAYF,EAAKE,UAAY,EAEhD3gC,EAAUjC,YAAY0iC,GACtBzgC,EAAU9N,YAAY1F,KAAKg0C,UAAUtrC,eAAeorC,IAEpDtgC,EAAU9Y,MAAM05C,WAAa,SAC7BzxC,EAAIjI,MAAMw5C,cAAgB,QAE1B,IAAIxF,EAAS/rC,EAAIwxC,UAAY3gC,EAAU2gC,UAAY,EAInD,OAFAzpC,EAAK6G,YAAYiC,GAEV,CAAE+6B,SAAUA,EAAUG,OAAQA,EACzC,GACD,CACCvwC,IAAK,aACL1H,MAAO,SAAoB8N,GACvB,IAAIpG,EAAMoG,EAAKoV,WAAa,IAAMpV,EAAKiV,SAKvC,YAJwB1iB,IAApBkJ,KAAK+zC,MAAM51C,KACX6B,KAAK+zC,MAAM51C,GAAO6B,KAAKq0C,cAAc9vC,IAGlCvE,KAAK+zC,MAAM51C,EACtB,KAGGulB,CACX,CAvEwC,E,kBCbxCptB,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAGX,IAAIkH,EAAe,WAAc,SAASC,EAAiBC,EAAQC,GAAS,IAAK,IAAIC,EAAI,EAAGA,EAAID,EAAM/C,OAAQgD,IAAK,CAAE,IAAIC,EAAaF,EAAMC,GAAIC,EAAW1C,WAAa0C,EAAW1C,aAAc,EAAO0C,EAAWC,cAAe,EAAU,UAAWD,IAAYA,EAAWE,UAAW,GAAM5H,OAAOC,eAAesH,EAAQG,EAAWG,IAAKH,EAAa,CAAE,CAAE,OAAO,SAAUI,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYT,EAAiBQ,EAAYG,UAAWF,GAAiBC,GAAaV,EAAiBQ,EAAaE,GAAqBF,CAAa,CAAG,CAA7hB,GAEfomB,EAAiB,EAAQ,OAEzB8vB,EAAc,EAAQ,OAItBC,EAAgB,WAChB,SAASA,EAActwC,EAAMrI,EAAQmT,IAHzC,SAAyBlP,EAAUzB,GAAe,KAAMyB,aAAoBzB,GAAgB,MAAM,IAAI0B,UAAU,oCAAwC,CAIhJC,CAAgBC,KAAMu0C,GAEtBv0C,KAAKiE,KAAOA,EACZjE,KAAKpE,OAASA,EACdoE,KAAK+O,OAASA,CAClB,CAUA,OARApR,EAAa42C,EAAe,KAAM,CAAC,CAC/Bp2C,IAAK,eACL1H,MAAO,SAAsBwL,EAAMrG,GAC/B,IAAIqI,EAAO2T,EAAU3V,EAAK3C,KAAM1D,EAAOlB,MAAMmsB,eAC7C,OAAO,IAAI0tB,EAActwC,EAAMrI,GAAQ,EAAI04C,EAAYp6B,iBAAiBjW,EAAMrI,EAAQqG,GAC1F,KAGGsyC,CACX,CAlBoB,GAoBpB/9C,EAAA,QAAkB+9C,EAGlB,IAAInf,EAAa,2BAEbxd,EAAY,SAAmB3T,EAAMwgB,GACrC,OAAQA,GACJ,KAAKD,EAAeyQ,eAAeC,UAC/B,OAAOjxB,EAAK0S,cAChB,KAAK6N,EAAeyQ,eAAeG,WAC/B,OAAOnxB,EAAKuwC,QAAQpf,EAAYqf,GACpC,KAAKjwB,EAAeyQ,eAAeE,UAC/B,OAAOlxB,EAAKywC,cAChB,QACI,OAAOzwC,EAEnB,EAEA,SAASwwC,EAAWv1B,EAAGy1B,EAAIC,GACvB,OAAI11B,EAAEnkB,OAAS,EACJ45C,EAAKC,EAAGF,cAGZx1B,CACX,C,kBCxDA5oB,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQq+C,aAAer+C,EAAQ6S,qBAAuB7S,EAAQqT,YAAcrT,EAAQ2S,kBAAoB3S,EAAQs+C,WAAat+C,EAAQyV,8BAA2BnV,EAEhK,IAAI6W,EAAgb,SAAUX,EAAKjP,GAAK,GAAIc,MAAMC,QAAQkO,GAAQ,OAAOA,EAAY,GAAIC,OAAOC,YAAY5W,OAAO0W,GAAQ,OAAxf,SAAuBA,EAAKjP,GAAK,IAAIoP,EAAO,GAAQC,GAAK,EAAUC,GAAK,EAAWC,OAAKxW,EAAW,IAAM,IAAK,IAAiCyW,EAA7BC,EAAKR,EAAIC,OAAOC,cAAmBE,GAAMG,EAAKC,EAAGlR,QAAQC,QAAoB4Q,EAAK3Q,KAAK+Q,EAAG9W,QAAYsH,GAAKoP,EAAKpS,SAAWgD,GAA3DqP,GAAK,GAAkE,CAAE,MAAOK,GAAOJ,GAAK,EAAMC,EAAKG,CAAK,CAAE,QAAU,KAAWL,GAAMI,EAAW,QAAGA,EAAW,QAAK,CAAE,QAAU,GAAIH,EAAI,MAAMC,CAAI,CAAE,CAAE,OAAOH,CAAM,CAAuHO,CAAcV,EAAKjP,GAAa,MAAM,IAAI+B,UAAU,uDAA2D,EAE7oBwT,EAAY,EAAQ,OAEpBC,EAAa,EAAQ,KAErBtH,EAA2BzV,EAAQyV,yBAA2B,CAC9DG,KAAM,EACNF,MAAO,GAGP4oC,EAAat+C,EAAQs+C,WAAa,CAClCC,OAAQ,EACRC,UAAW,EACXC,IAAK,EACLC,QAAS,EACTC,SAAU,EACVC,UAAW,EACXC,WAAY,GAoHZR,GAjHoBr+C,EAAQ2S,kBAAoB,SAA2BzO,EAAO4E,GAClF,IAAK5E,IAAUA,EAAM46C,cAAuC,SAAvB56C,EAAM46C,aACvC,MAAO,GAOX,IAJA,IAAIC,EAAe,GACfC,EAAgB96C,EAAM46C,aAAa58B,MAAM,WACzC+8B,EAAmBD,EAAcz6C,OAE5BgD,EAAI,EAAGA,EAAI03C,EAAkB13C,IAAK,CACvC,IAAI23C,EAAwBF,EAAcz3C,GAAG2a,MAAM,OAC/Ci9B,EAAyBhoC,EAAe+nC,EAAuB,GAC/DE,EAAcD,EAAuB,GACrCl2C,EAAek2C,EAAuB,GAE1CJ,EAAa/4C,KAAKo5C,GAClB,IAAIC,EAAUv2C,EAAKsC,SAASg0C,GACvBC,IACDA,EAAUv2C,EAAKsC,SAASg0C,GAAe,IAE3CC,EAAQr5C,KAAKwd,SAASva,GAAgB,EAAG,IAC7C,CAEA,OAAO81C,CACX,EAEkB/+C,EAAQqT,YAAc,SAAqB0rC,EAAcj2C,GAEvE,IADA,IAAIw2C,EAAcP,EAAax6C,OACtBgD,EAAI,EAAGA,EAAI+3C,EAAa/3C,IAC7BuB,EAAKsC,SAAS2zC,EAAax3C,IAAIg4C,KAEvC,EAE2Bv/C,EAAQ6S,qBAAuB,SAA8BpH,EAAMvH,EAAO4E,GACjG,IAAK5E,IAAUA,EAAMkR,SAA6B,SAAlBlR,EAAMkR,SAAwC,qBAAlBlR,EAAMkR,SAAoD,SAAlBlR,EAAMmR,QACtG,OAAO,KAGX,IAAImqC,EAASnB,EAAan6C,EAAMkR,SAE5BG,EAAMiqC,EAAOj7C,OACb2Q,EAAe,GACfgL,EAAI,GAGJu/B,EAAmBv7C,EAAMu7C,iBAC7B,GAAIA,GAAyC,SAArBA,EAA6B,CACjD,IAAIC,EAAwBD,EAAiBv9B,MAAM,OAC/Cy9B,EAAyBxoC,EAAeuoC,EAAuB,GAC/DN,EAAcO,EAAuB,GACrCC,EAAiBD,EAAuB,GAExCN,EAAUv2C,EAAKsC,SAASg0C,GACxBC,IACAA,EAAQA,EAAQ96C,OAAS,SAAyBjE,IAAnBs/C,EAA+B,EAAIp8B,SAASo8B,EAAgB,IAEnG,CAGA,IAAK,IAAIr4C,EAAI,EAAGA,EAAIgO,EAAKhO,IAAK,CAC1B,IAAIs4C,EAAQL,EAAOj4C,GACnB,OAAQs4C,EAAM77C,MACV,KAAKs6C,EAAWC,OACZr+B,GAAK2/B,EAAM5/C,OAAS,GACpB,MAEJ,KAAKq+C,EAAWE,UACR/yC,aAAgB4G,aAAewtC,EAAM5/C,QACrCigB,GAAKzU,EAAKinB,aAAamtB,EAAM5/C,QAAU,IAE3C,MAEJ,KAAKq+C,EAAWI,QACZ,IAAIoB,EAAWh3C,EAAKsC,SAASy0C,EAAMhmC,MAAQ,IACvCimC,IACA5/B,GAAK6/B,EAAmB,CAACD,EAASA,EAASv7C,OAAS,IAAK,GAAIs7C,EAAM1rC,SAEvE,MAEJ,KAAKmqC,EAAWK,SACZ,IAAIqB,EAAYl3C,EAAKsC,SAASy0C,EAAMhmC,MAAQ,IACxCmmC,IACA9/B,GAAK6/B,EAAmBC,EAAWH,EAAMI,KAAMJ,EAAM1rC,SAEzD,MAEJ,KAAKmqC,EAAWM,UACZ1+B,GAAKggC,EAASh8C,GAAO,EAAM4E,EAAKuC,YAChCvC,EAAKuC,aACL,MAEJ,KAAKizC,EAAWO,WACZ/1C,EAAKuC,aACL6U,GAAKggC,EAASh8C,GAAO,EAAO4E,EAAKuC,YACjC,MAEJ,KAAKizC,EAAWG,IACRv+B,IACAhL,EAAalP,KAAK,CAAEhC,KAAMyR,EAAyBG,KAAM3V,MAAOigB,IAChEA,EAAI,IAERhL,EAAalP,KAAK,CAAEhC,KAAMyR,EAAyBC,MAAOzV,MAAO4/C,EAAM5/C,OAAS,KAG5F,CAMA,OAJIigB,GACAhL,EAAalP,KAAK,CAAEhC,KAAMyR,EAAyBG,KAAM3V,MAAOigB,IAG7DhL,CACX,EAEmBlV,EAAQq+C,aAAe,SAAsBjpC,EAASrF,GACrE,GAAIA,GAASA,EAAMqF,GACf,OAAOrF,EAAMqF,GAajB,IAVA,IAAIoqC,EAAS,GACTjqC,EAAMH,EAAQ7Q,OAEd47C,GAAW,EACXC,GAAY,EACZC,GAAa,EACbl7C,EAAM,GACNm7C,EAAe,GACfr0C,EAAO,GAEF1E,EAAI,EAAGA,EAAIgO,EAAKhO,IAAK,CAC1B,IAAI6iB,EAAIhV,EAAQmrC,OAAOh5C,GAEvB,OAAQ6iB,GACJ,IAAK,IACL,IAAK,IACGg2B,EACAj7C,GAAOilB,GAEP+1B,GAAYA,EACPE,GAAeF,IAChBX,EAAOx5C,KAAK,CAAEhC,KAAMs6C,EAAWC,OAAQt+C,MAAOkF,IAC9CA,EAAM,KAGd,MAEJ,IAAK,KACGi7C,GACAj7C,GAAOilB,EACPg2B,GAAY,GAEZA,GAAY,EAEhB,MAEJ,IAAK,IACGD,EACAh7C,GAAOilB,GAEPi2B,GAAa,EACbC,EAAen7C,EACfA,EAAM,GACN8G,EAAO,IAEX,MAEJ,IAAK,IACD,GAAIk0C,EACAh7C,GAAOilB,OACJ,GAAIi2B,EAAY,CAKnB,OAJIl7C,GACA8G,EAAKjG,KAAKb,GAGNm7C,GACJ,IAAK,OACGr0C,EAAK1H,OAAS,GACdi7C,EAAOx5C,KAAK,CAAEhC,KAAMs6C,EAAWE,UAAWv+C,MAAOgM,EAAK,KAE1D,MAEJ,IAAK,UACD,GAAIA,EAAK1H,OAAS,EAAG,CACjB,IAAI86C,EAAU,CACVr7C,KAAMs6C,EAAWI,QACjB7kC,KAAM5N,EAAK,IAEXA,EAAK1H,OAAS,IACd86C,EAAQlrC,OAASlI,EAAK,IAE1BuzC,EAAOx5C,KAAKq5C,EAChB,CACA,MAEJ,IAAK,WACD,GAAIpzC,EAAK1H,OAAS,EAAG,CACjB,IAAIi8C,EAAa,CACbx8C,KAAMs6C,EAAWK,SACjB9kC,KAAM5N,EAAK,IAEXA,EAAK1H,OAAS,IACdi8C,EAAWP,KAAOh0C,EAAK,IAEvBA,EAAK1H,OAAS,IACdi8C,EAAWrsC,OAASlI,EAAK,IAE7BuzC,EAAOx5C,KAAKw6C,EAChB,CACA,MAEJ,IAAK,MACGv0C,EAAK1H,OAAS,GACdi7C,EAAOx5C,KAAK,CAAEhC,KAAMs6C,EAAWG,IAAKx+C,MAAOgM,EAAK,KAK5Do0C,GAAa,EACbl7C,EAAM,EACV,CACA,MAEJ,IAAK,IACGg7C,EACAh7C,GAAOilB,EACAi2B,IACPp0C,EAAKjG,KAAKb,GACVA,EAAM,IAEV,MAEJ,IAAK,IACL,IAAK,KACGg7C,EACAh7C,GAAOilB,EACAjlB,IACPs7C,EAAcjB,EAAQr6C,GACtBA,EAAM,IAEV,MAEJ,QACIA,GAAOilB,EAGL,OAANA,IACAg2B,GAAY,EAEpB,CAUA,OARIj7C,GACAs7C,EAAcjB,EAAQr6C,GAGtB4K,IACAA,EAAMqF,GAAWoqC,GAGdA,CACX,GAEIiB,EAAgB,SAAuBjB,EAAQkB,GAC/C,OAAQA,GACJ,IAAK,aACDlB,EAAOx5C,KAAK,CAAEhC,KAAMs6C,EAAWM,YAC/B,MACJ,IAAK,cACDY,EAAOx5C,KAAK,CAAEhC,KAAMs6C,EAAWO,aAG3C,EAEIqB,EAAW,SAAkBh8C,EAAOy8C,EAAWt1C,GAC/C,IAAIu1C,EAAS18C,EAAM08C,OAAS18C,EAAM08C,OAAO1+B,MAAM,OAAS,CAAC,OAAQ,QAC7DsI,EAAmB,EAAbnf,EAOV,OANImf,GAAOo2B,EAAOr8C,SACdimB,EAAMo2B,EAAOr8C,OAAS,GAErBo8C,KACCn2B,EAECo2B,EAAOp2B,GAAKwzB,QAAQ,eAAgB,GAC/C,EAEI+B,EAAqB,SAA4BV,EAASY,EAAM9rC,GAIhE,IAHA,IAAIoB,EAAM8pC,EAAQ96C,OACdkK,EAAS,GAEJlH,EAAI,EAAGA,EAAIgO,EAAKhO,IACjBA,EAAI,IACJkH,GAAUwxC,GAAQ,IAEtBxxC,IAAU,EAAIqO,EAAU8rB,mBAAmByW,EAAQ93C,IAAI,EAAIwV,EAAW5c,oBAAoBgU,GAAU,YAAY,GAGpH,OAAO1F,CACX,C,gBCjUA3O,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEX,IAAIsvB,EAAUvvB,EAAQuvB,QAAU,CAC5B7uB,KAAM,EACNmgD,MAAO,EACP/uB,OAAQ,EACRgvB,OAAQ,EACRC,KAAM,GACNC,UAAW,GACXC,MAAO,GACPC,KAAM,IACNC,KAAM,IACNC,KAAM,IACNC,QAAS,KACT7xB,UAAW,KACX8xB,gBAAiB,KACjBC,mBAAoB,KACpBC,mBAAoB,MACpBC,UAAW,MACXC,WAAY,MACZC,mBAAoB,GAAK,GACzBC,aAAc,GAAK,GACnBC,cAAe,GAAK,GACpBC,UAAW,GAAK,GAChBC,UAAW,GAAK,GAChBC,oBAAqB,GAAK,GAC1BC,oBAAqB,GAAK,GAC1BC,SAAU,GAAK,GACfnwB,aAAc,GAAK,GACnBG,iBAAkB,GAAK,GACvBC,aAAc,GAAK,GACnBH,YAAa,GAAK,GAClBC,YAAa,GAAK,IAoElBkwB,EAAgB,SAAuB12B,EAAKpW,GAC5C,OAAOoW,EAlEa,SAA2BpW,GAC/C,OAAQA,GACJ,IAAK,QACD,OAAOka,EAAQsxB,MACnB,IAAK,SACD,OAAOtxB,EAAQuC,OACnB,IAAK,SACD,OAAOvC,EAAQuxB,OACnB,IAAK,OACD,OAAOvxB,EAAQwxB,KACnB,IAAK,YACD,OAAOxxB,EAAQyxB,UACnB,IAAK,QACD,OAAOzxB,EAAQ0xB,MACnB,IAAK,OACD,OAAO1xB,EAAQ2xB,KACnB,IAAK,OACD,OAAO3xB,EAAQ4xB,KACnB,IAAK,OACD,OAAO5xB,EAAQ6xB,KACnB,IAAK,UACD,OAAO7xB,EAAQ8xB,QACnB,IAAK,YACD,OAAO9xB,EAAQC,UACnB,IAAK,kBACD,OAAOD,EAAQ+xB,gBACnB,IAAK,qBACD,OAAO/xB,EAAQgyB,mBACnB,IAAK,qBACD,OAAOhyB,EAAQiyB,mBACnB,IAAK,YACD,OAAOjyB,EAAQkyB,UACnB,IAAK,aACD,OAAOlyB,EAAQmyB,WACnB,IAAK,qBACD,OAAOnyB,EAAQoyB,mBACnB,IAAK,eACD,OAAOpyB,EAAQqyB,aACnB,IAAK,gBACD,OAAOryB,EAAQsyB,cACnB,IAAK,YACD,OAAOtyB,EAAQuyB,UACnB,IAAK,YACD,OAAOvyB,EAAQwyB,UACnB,IAAK,sBACD,OAAOxyB,EAAQyyB,oBACnB,IAAK,sBACD,OAAOzyB,EAAQ0yB,oBACnB,IAAK,WACD,OAAO1yB,EAAQ2yB,SACnB,IAAK,eACD,OAAO3yB,EAAQwC,aACnB,IAAK,mBACD,OAAOxC,EAAQ2C,iBACnB,IAAK,eACD,OAAO3C,EAAQ4C,aACnB,IAAK,cACD,OAAO5C,EAAQyC,YACnB,IAAK,cACD,OAAOzC,EAAQ0C,YAGvB,OAAO1C,EAAQ7uB,IACnB,CAGiB0hD,CAAkB/sC,EACnC,EAEmBrV,EAAQ0uB,aAAe,SAAsBrZ,GAC5D,OAAOA,EAAQ6M,MAAM,KAAKrU,OAAOs0C,EAAe,EACpD,C", "sources": ["webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/parsing/listStyle.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/Unicode.js", "webpack://webviewer-ui/./node_modules/css-line-break/dist/Trie.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/parsing/overflowWrap.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/Clone.js", "webpack://webviewer-ui/./node_modules/css-line-break/dist/linebreak-trie.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/Feature.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/drawing/Size.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/NodeParser.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/parsing/border.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/drawing/Circle.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/parsing/transform.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/Length.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/parsing/font.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/TextBounds.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/Gradient.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/parsing/borderRadius.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/drawing/Path.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/Util.js", "webpack://webviewer-ui/./node_modules/css-line-break/dist/index.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/Window.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/NodeContainer.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/parsing/word-break.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/parsing/position.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/parsing/margin.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/drawing/Vector.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/index.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/drawing/BezierCurve.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/StackingContext.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/parsing/lineBreak.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/parsing/background.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/parsing/padding.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/Angle.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/Bounds.js", "webpack://webviewer-ui/./node_modules/css-line-break/dist/Util.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/Logger.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/parsing/textTransform.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/Proxy.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/parsing/textShadow.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/Input.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/parsing/textDecoration.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/parsing/float.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/renderer/ForeignObjectRenderer.js", "webpack://webviewer-ui/./node_modules/css-line-break/dist/LineBreak.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/ListItem.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/Color.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/renderer/CanvasRenderer.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/parsing/visibility.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/parsing/letterSpacing.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/Renderer.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/parsing/overflow.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/parsing/zIndex.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/ResourceLoader.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/Font.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/TextContainer.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/PseudoNodeContent.js", "webpack://webviewer-ui/./node_modules/html2canvas/dist/npm/parsing/display.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.parseListStyle = exports.parseListStyleType = exports.LIST_STYLE_TYPE = exports.LIST_STYLE_POSITION = undefined;\n\nvar _background = require('./background');\n\nvar LIST_STYLE_POSITION = exports.LIST_STYLE_POSITION = {\n    INSIDE: 0,\n    OUTSIDE: 1\n};\n\nvar LIST_STYLE_TYPE = exports.LIST_STYLE_TYPE = {\n    NONE: -1,\n    DISC: 0,\n    CIRCLE: 1,\n    SQUARE: 2,\n    DECIMAL: 3,\n    CJK_DECIMAL: 4,\n    DECIMAL_LEADING_ZERO: 5,\n    LOWER_ROMAN: 6,\n    UPPER_ROMAN: 7,\n    LOWER_GREEK: 8,\n    LOWER_ALPHA: 9,\n    UPPER_ALPHA: 10,\n    ARABIC_INDIC: 11,\n    ARMENIAN: 12,\n    BENGALI: 13,\n    CAMBODIAN: 14,\n    <PERSON><PERSON><PERSON>_EARTHLY_BRANCH: 15,\n    <PERSON><PERSON><PERSON>_HEAVENLY_STEM: 16,\n    <PERSON><PERSON><PERSON>_IDEOGRAPHIC: 17,\n    DEVANAGARI: 18,\n    <PERSON><PERSON><PERSON><PERSON><PERSON>_NUMERIC: 19,\n    GEORGIAN: 20,\n    GUJARATI: 21,\n    GURMUKHI: 22,\n    HEBREW: 22,\n    HIRAGANA: 23,\n    HIRAGANA_IROHA: 24,\n    JAPANESE_FORMAL: 25,\n    JAPANESE_INFORMAL: 26,\n    KANNADA: 27,\n    KATAKANA: 28,\n    KATAKANA_IROHA: 29,\n    KHMER: 30,\n    KOREAN_HANGUL_FORMAL: 31,\n    KOREAN_HANJA_FORMAL: 32,\n    KOREAN_HANJA_INFORMAL: 33,\n    LAO: 34,\n    LOWER_ARMENIAN: 35,\n    MALAYALAM: 36,\n    MONGOLIAN: 37,\n    MYANMAR: 38,\n    ORIYA: 39,\n    PERSIAN: 40,\n    SIMP_CHINESE_FORMAL: 41,\n    SIMP_CHINESE_INFORMAL: 42,\n    TAMIL: 43,\n    TELUGU: 44,\n    THAI: 45,\n    TIBETAN: 46,\n    TRAD_CHINESE_FORMAL: 47,\n    TRAD_CHINESE_INFORMAL: 48,\n    UPPER_ARMENIAN: 49,\n    DISCLOSURE_OPEN: 50,\n    DISCLOSURE_CLOSED: 51\n};\n\nvar parseListStyleType = exports.parseListStyleType = function parseListStyleType(type) {\n    switch (type) {\n        case 'disc':\n            return LIST_STYLE_TYPE.DISC;\n        case 'circle':\n            return LIST_STYLE_TYPE.CIRCLE;\n        case 'square':\n            return LIST_STYLE_TYPE.SQUARE;\n        case 'decimal':\n            return LIST_STYLE_TYPE.DECIMAL;\n        case 'cjk-decimal':\n            return LIST_STYLE_TYPE.CJK_DECIMAL;\n        case 'decimal-leading-zero':\n            return LIST_STYLE_TYPE.DECIMAL_LEADING_ZERO;\n        case 'lower-roman':\n            return LIST_STYLE_TYPE.LOWER_ROMAN;\n        case 'upper-roman':\n            return LIST_STYLE_TYPE.UPPER_ROMAN;\n        case 'lower-greek':\n            return LIST_STYLE_TYPE.LOWER_GREEK;\n        case 'lower-alpha':\n            return LIST_STYLE_TYPE.LOWER_ALPHA;\n        case 'upper-alpha':\n            return LIST_STYLE_TYPE.UPPER_ALPHA;\n        case 'arabic-indic':\n            return LIST_STYLE_TYPE.ARABIC_INDIC;\n        case 'armenian':\n            return LIST_STYLE_TYPE.ARMENIAN;\n        case 'bengali':\n            return LIST_STYLE_TYPE.BENGALI;\n        case 'cambodian':\n            return LIST_STYLE_TYPE.CAMBODIAN;\n        case 'cjk-earthly-branch':\n            return LIST_STYLE_TYPE.CJK_EARTHLY_BRANCH;\n        case 'cjk-heavenly-stem':\n            return LIST_STYLE_TYPE.CJK_HEAVENLY_STEM;\n        case 'cjk-ideographic':\n            return LIST_STYLE_TYPE.CJK_IDEOGRAPHIC;\n        case 'devanagari':\n            return LIST_STYLE_TYPE.DEVANAGARI;\n        case 'ethiopic-numeric':\n            return LIST_STYLE_TYPE.ETHIOPIC_NUMERIC;\n        case 'georgian':\n            return LIST_STYLE_TYPE.GEORGIAN;\n        case 'gujarati':\n            return LIST_STYLE_TYPE.GUJARATI;\n        case 'gurmukhi':\n            return LIST_STYLE_TYPE.GURMUKHI;\n        case 'hebrew':\n            return LIST_STYLE_TYPE.HEBREW;\n        case 'hiragana':\n            return LIST_STYLE_TYPE.HIRAGANA;\n        case 'hiragana-iroha':\n            return LIST_STYLE_TYPE.HIRAGANA_IROHA;\n        case 'japanese-formal':\n            return LIST_STYLE_TYPE.JAPANESE_FORMAL;\n        case 'japanese-informal':\n            return LIST_STYLE_TYPE.JAPANESE_INFORMAL;\n        case 'kannada':\n            return LIST_STYLE_TYPE.KANNADA;\n        case 'katakana':\n            return LIST_STYLE_TYPE.KATAKANA;\n        case 'katakana-iroha':\n            return LIST_STYLE_TYPE.KATAKANA_IROHA;\n        case 'khmer':\n            return LIST_STYLE_TYPE.KHMER;\n        case 'korean-hangul-formal':\n            return LIST_STYLE_TYPE.KOREAN_HANGUL_FORMAL;\n        case 'korean-hanja-formal':\n            return LIST_STYLE_TYPE.KOREAN_HANJA_FORMAL;\n        case 'korean-hanja-informal':\n            return LIST_STYLE_TYPE.KOREAN_HANJA_INFORMAL;\n        case 'lao':\n            return LIST_STYLE_TYPE.LAO;\n        case 'lower-armenian':\n            return LIST_STYLE_TYPE.LOWER_ARMENIAN;\n        case 'malayalam':\n            return LIST_STYLE_TYPE.MALAYALAM;\n        case 'mongolian':\n            return LIST_STYLE_TYPE.MONGOLIAN;\n        case 'myanmar':\n            return LIST_STYLE_TYPE.MYANMAR;\n        case 'oriya':\n            return LIST_STYLE_TYPE.ORIYA;\n        case 'persian':\n            return LIST_STYLE_TYPE.PERSIAN;\n        case 'simp-chinese-formal':\n            return LIST_STYLE_TYPE.SIMP_CHINESE_FORMAL;\n        case 'simp-chinese-informal':\n            return LIST_STYLE_TYPE.SIMP_CHINESE_INFORMAL;\n        case 'tamil':\n            return LIST_STYLE_TYPE.TAMIL;\n        case 'telugu':\n            return LIST_STYLE_TYPE.TELUGU;\n        case 'thai':\n            return LIST_STYLE_TYPE.THAI;\n        case 'tibetan':\n            return LIST_STYLE_TYPE.TIBETAN;\n        case 'trad-chinese-formal':\n            return LIST_STYLE_TYPE.TRAD_CHINESE_FORMAL;\n        case 'trad-chinese-informal':\n            return LIST_STYLE_TYPE.TRAD_CHINESE_INFORMAL;\n        case 'upper-armenian':\n            return LIST_STYLE_TYPE.UPPER_ARMENIAN;\n        case 'disclosure-open':\n            return LIST_STYLE_TYPE.DISCLOSURE_OPEN;\n        case 'disclosure-closed':\n            return LIST_STYLE_TYPE.DISCLOSURE_CLOSED;\n        case 'none':\n        default:\n            return LIST_STYLE_TYPE.NONE;\n    }\n};\n\nvar parseListStyle = exports.parseListStyle = function parseListStyle(style) {\n    var listStyleImage = (0, _background.parseBackgroundImage)(style.getPropertyValue('list-style-image'));\n    return {\n        listStyleType: parseListStyleType(style.getPropertyValue('list-style-type')),\n        listStyleImage: listStyleImage.length ? listStyleImage[0] : null,\n        listStylePosition: parseListStylePosition(style.getPropertyValue('list-style-position'))\n    };\n};\n\nvar parseListStylePosition = function parseListStylePosition(position) {\n    switch (position) {\n        case 'inside':\n            return LIST_STYLE_POSITION.INSIDE;\n        case 'outside':\n        default:\n            return LIST_STYLE_POSITION.OUTSIDE;\n    }\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.breakWords = exports.fromCodePoint = exports.toCodePoints = undefined;\n\nvar _cssLineBreak = require('css-line-break');\n\nObject.defineProperty(exports, 'toCodePoints', {\n    enumerable: true,\n    get: function get() {\n        return _cssLineBreak.toCodePoints;\n    }\n});\nObject.defineProperty(exports, 'fromCodePoint', {\n    enumerable: true,\n    get: function get() {\n        return _cssLineBreak.fromCodePoint;\n    }\n});\n\nvar _NodeContainer = require('./NodeContainer');\n\nvar _NodeContainer2 = _interopRequireDefault(_NodeContainer);\n\nvar _overflowWrap = require('./parsing/overflowWrap');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar breakWords = exports.breakWords = function breakWords(str, parent) {\n    var breaker = (0, _cssLineBreak.LineBreaker)(str, {\n        lineBreak: parent.style.lineBreak,\n        wordBreak: parent.style.overflowWrap === _overflowWrap.OVERFLOW_WRAP.BREAK_WORD ? 'break-word' : parent.style.wordBreak\n    });\n\n    var words = [];\n    var bk = void 0;\n\n    while (!(bk = breaker.next()).done) {\n        words.push(bk.value.slice());\n    }\n\n    return words;\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.Trie = exports.createTrieFromBase64 = exports.UTRIE2_INDEX_2_MASK = exports.UTRIE2_INDEX_2_BLOCK_LENGTH = exports.UTRIE2_OMITTED_BMP_INDEX_1_LENGTH = exports.UTRIE2_INDEX_1_OFFSET = exports.UTRIE2_UTF8_2B_INDEX_2_LENGTH = exports.UTRIE2_UTF8_2B_INDEX_2_OFFSET = exports.UTRIE2_INDEX_2_BMP_LENGTH = exports.UTRIE2_LSCP_INDEX_2_LENGTH = exports.UTRIE2_DATA_MASK = exports.UTRIE2_DATA_BLOCK_LENGTH = exports.UTRIE2_LSCP_INDEX_2_OFFSET = exports.UTRIE2_SHIFT_1_2 = exports.UTRIE2_INDEX_SHIFT = exports.UTRIE2_SHIFT_1 = exports.UTRIE2_SHIFT_2 = undefined;\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _Util = require('./Util');\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\n/** Shift size for getting the index-2 table offset. */\nvar UTRIE2_SHIFT_2 = exports.UTRIE2_SHIFT_2 = 5;\n\n/** Shift size for getting the index-1 table offset. */\nvar UTRIE2_SHIFT_1 = exports.UTRIE2_SHIFT_1 = 6 + 5;\n\n/**\n * Shift size for shifting left the index array values.\n * Increases possible data size with 16-bit index values at the cost\n * of compactability.\n * This requires data blocks to be aligned by UTRIE2_DATA_GRANULARITY.\n */\nvar UTRIE2_INDEX_SHIFT = exports.UTRIE2_INDEX_SHIFT = 2;\n\n/**\n * Difference between the two shift sizes,\n * for getting an index-1 offset from an index-2 offset. 6=11-5\n */\nvar UTRIE2_SHIFT_1_2 = exports.UTRIE2_SHIFT_1_2 = UTRIE2_SHIFT_1 - UTRIE2_SHIFT_2;\n\n/**\n * The part of the index-2 table for U+D800..U+DBFF stores values for\n * lead surrogate code _units_ not code _points_.\n * Values for lead surrogate code _points_ are indexed with this portion of the table.\n * Length=32=0x20=0x400>>UTRIE2_SHIFT_2. (There are 1024=0x400 lead surrogates.)\n */\nvar UTRIE2_LSCP_INDEX_2_OFFSET = exports.UTRIE2_LSCP_INDEX_2_OFFSET = 0x10000 >> UTRIE2_SHIFT_2;\n\n/** Number of entries in a data block. 32=0x20 */\nvar UTRIE2_DATA_BLOCK_LENGTH = exports.UTRIE2_DATA_BLOCK_LENGTH = 1 << UTRIE2_SHIFT_2;\n/** Mask for getting the lower bits for the in-data-block offset. */\nvar UTRIE2_DATA_MASK = exports.UTRIE2_DATA_MASK = UTRIE2_DATA_BLOCK_LENGTH - 1;\n\nvar UTRIE2_LSCP_INDEX_2_LENGTH = exports.UTRIE2_LSCP_INDEX_2_LENGTH = 0x400 >> UTRIE2_SHIFT_2;\n/** Count the lengths of both BMP pieces. 2080=0x820 */\nvar UTRIE2_INDEX_2_BMP_LENGTH = exports.UTRIE2_INDEX_2_BMP_LENGTH = UTRIE2_LSCP_INDEX_2_OFFSET + UTRIE2_LSCP_INDEX_2_LENGTH;\n/**\n * The 2-byte UTF-8 version of the index-2 table follows at offset 2080=0x820.\n * Length 32=0x20 for lead bytes C0..DF, regardless of UTRIE2_SHIFT_2.\n */\nvar UTRIE2_UTF8_2B_INDEX_2_OFFSET = exports.UTRIE2_UTF8_2B_INDEX_2_OFFSET = UTRIE2_INDEX_2_BMP_LENGTH;\nvar UTRIE2_UTF8_2B_INDEX_2_LENGTH = exports.UTRIE2_UTF8_2B_INDEX_2_LENGTH = 0x800 >> 6; /* U+0800 is the first code point after 2-byte UTF-8 */\n/**\n * The index-1 table, only used for supplementary code points, at offset 2112=0x840.\n * Variable length, for code points up to highStart, where the last single-value range starts.\n * Maximum length 512=0x200=0x100000>>UTRIE2_SHIFT_1.\n * (For 0x100000 supplementary code points U+10000..U+10ffff.)\n *\n * The part of the index-2 table for supplementary code points starts\n * after this index-1 table.\n *\n * Both the index-1 table and the following part of the index-2 table\n * are omitted completely if there is only BMP data.\n */\nvar UTRIE2_INDEX_1_OFFSET = exports.UTRIE2_INDEX_1_OFFSET = UTRIE2_UTF8_2B_INDEX_2_OFFSET + UTRIE2_UTF8_2B_INDEX_2_LENGTH;\n\n/**\n * Number of index-1 entries for the BMP. 32=0x20\n * This part of the index-1 table is omitted from the serialized form.\n */\nvar UTRIE2_OMITTED_BMP_INDEX_1_LENGTH = exports.UTRIE2_OMITTED_BMP_INDEX_1_LENGTH = 0x10000 >> UTRIE2_SHIFT_1;\n\n/** Number of entries in an index-2 block. 64=0x40 */\nvar UTRIE2_INDEX_2_BLOCK_LENGTH = exports.UTRIE2_INDEX_2_BLOCK_LENGTH = 1 << UTRIE2_SHIFT_1_2;\n/** Mask for getting the lower bits for the in-index-2-block offset. */\nvar UTRIE2_INDEX_2_MASK = exports.UTRIE2_INDEX_2_MASK = UTRIE2_INDEX_2_BLOCK_LENGTH - 1;\n\nvar createTrieFromBase64 = exports.createTrieFromBase64 = function createTrieFromBase64(base64) {\n    var buffer = (0, _Util.decode)(base64);\n    var view32 = Array.isArray(buffer) ? (0, _Util.polyUint32Array)(buffer) : new Uint32Array(buffer);\n    var view16 = Array.isArray(buffer) ? (0, _Util.polyUint16Array)(buffer) : new Uint16Array(buffer);\n    var headerLength = 24;\n\n    var index = view16.slice(headerLength / 2, view32[4] / 2);\n    var data = view32[5] === 2 ? view16.slice((headerLength + view32[4]) / 2) : view32.slice(Math.ceil((headerLength + view32[4]) / 4));\n\n    return new Trie(view32[0], view32[1], view32[2], view32[3], index, data);\n};\n\nvar Trie = exports.Trie = function () {\n    function Trie(initialValue, errorValue, highStart, highValueIndex, index, data) {\n        _classCallCheck(this, Trie);\n\n        this.initialValue = initialValue;\n        this.errorValue = errorValue;\n        this.highStart = highStart;\n        this.highValueIndex = highValueIndex;\n        this.index = index;\n        this.data = data;\n    }\n\n    /**\n     * Get the value for a code point as stored in the Trie.\n     *\n     * @param codePoint the code point\n     * @return the value\n     */\n\n\n    _createClass(Trie, [{\n        key: 'get',\n        value: function get(codePoint) {\n            var ix = void 0;\n            if (codePoint >= 0) {\n                if (codePoint < 0x0d800 || codePoint > 0x0dbff && codePoint <= 0x0ffff) {\n                    // Ordinary BMP code point, excluding leading surrogates.\n                    // BMP uses a single level lookup.  BMP index starts at offset 0 in the Trie2 index.\n                    // 16 bit data is stored in the index array itself.\n                    ix = this.index[codePoint >> UTRIE2_SHIFT_2];\n                    ix = (ix << UTRIE2_INDEX_SHIFT) + (codePoint & UTRIE2_DATA_MASK);\n                    return this.data[ix];\n                }\n\n                if (codePoint <= 0xffff) {\n                    // Lead Surrogate Code Point.  A Separate index section is stored for\n                    // lead surrogate code units and code points.\n                    //   The main index has the code unit data.\n                    //   For this function, we need the code point data.\n                    // Note: this expression could be refactored for slightly improved efficiency, but\n                    //       surrogate code points will be so rare in practice that it's not worth it.\n                    ix = this.index[UTRIE2_LSCP_INDEX_2_OFFSET + (codePoint - 0xd800 >> UTRIE2_SHIFT_2)];\n                    ix = (ix << UTRIE2_INDEX_SHIFT) + (codePoint & UTRIE2_DATA_MASK);\n                    return this.data[ix];\n                }\n\n                if (codePoint < this.highStart) {\n                    // Supplemental code point, use two-level lookup.\n                    ix = UTRIE2_INDEX_1_OFFSET - UTRIE2_OMITTED_BMP_INDEX_1_LENGTH + (codePoint >> UTRIE2_SHIFT_1);\n                    ix = this.index[ix];\n                    ix += codePoint >> UTRIE2_SHIFT_2 & UTRIE2_INDEX_2_MASK;\n                    ix = this.index[ix];\n                    ix = (ix << UTRIE2_INDEX_SHIFT) + (codePoint & UTRIE2_DATA_MASK);\n                    return this.data[ix];\n                }\n                if (codePoint <= 0x10ffff) {\n                    return this.data[this.highValueIndex];\n                }\n            }\n\n            // Fall through.  The code point is outside of the legal range of 0..0x10ffff.\n            return this.errorValue;\n        }\n    }]);\n\n    return Trie;\n}();", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nvar OVERFLOW_WRAP = exports.OVERFLOW_WRAP = {\n    NORMAL: 0,\n    BREAK_WORD: 1\n};\n\nvar parseOverflowWrap = exports.parseOverflowWrap = function parseOverflowWrap(overflow) {\n    switch (overflow) {\n        case 'break-word':\n            return OVERFLOW_WRAP.BREAK_WORD;\n        case 'normal':\n        default:\n            return OVERFLOW_WRAP.NORMAL;\n    }\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.cloneWindow = exports.DocumentCloner = undefined;\n\nvar _slicedToArray = function () { function sliceIterator(arr, i) { var _arr = []; var _n = true; var _d = false; var _e = undefined; try { for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"]) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; } return function (arr, i) { if (Array.isArray(arr)) { return arr; } else if (Symbol.iterator in Object(arr)) { return sliceIterator(arr, i); } else { throw new TypeError(\"Invalid attempt to destructure non-iterable instance\"); } }; }();\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _Bounds = require('./Bounds');\n\nvar _Proxy = require('./Proxy');\n\nvar _ResourceLoader = require('./ResourceLoader');\n\nvar _ResourceLoader2 = _interopRequireDefault(_ResourceLoader);\n\nvar _Util = require('./Util');\n\nvar _background = require('./parsing/background');\n\nvar _CanvasRenderer = require('./renderer/CanvasRenderer');\n\nvar _CanvasRenderer2 = _interopRequireDefault(_CanvasRenderer);\n\nvar _PseudoNodeContent = require('./PseudoNodeContent');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar IGNORE_ATTRIBUTE = 'data-html2canvas-ignore';\n\nvar DocumentCloner = exports.DocumentCloner = function () {\n    function DocumentCloner(element, options, logger, copyInline, renderer) {\n        _classCallCheck(this, DocumentCloner);\n\n        this.referenceElement = element;\n        this.scrolledElements = [];\n        this.copyStyles = copyInline;\n        this.inlineImages = copyInline;\n        this.logger = logger;\n        this.options = options;\n        this.renderer = renderer;\n        this.resourceLoader = new _ResourceLoader2.default(options, logger, window);\n        this.pseudoContentData = {\n            counters: {},\n            quoteDepth: 0\n        };\n        // $FlowFixMe\n        this.documentElement = this.cloneNode(element.ownerDocument.documentElement);\n    }\n\n    _createClass(DocumentCloner, [{\n        key: 'inlineAllImages',\n        value: function inlineAllImages(node) {\n            var _this = this;\n\n            if (this.inlineImages && node) {\n                var style = node.style;\n                Promise.all((0, _background.parseBackgroundImage)(style.backgroundImage).map(function (backgroundImage) {\n                    if (backgroundImage.method === 'url') {\n                        return _this.resourceLoader.inlineImage(backgroundImage.args[0]).then(function (img) {\n                            return img && typeof img.src === 'string' ? 'url(\"' + img.src + '\")' : 'none';\n                        }).catch(function (e) {\n                            if (process.env.NODE_ENV !== 'production') {\n                                _this.logger.log('Unable to load image', e);\n                            }\n                        });\n                    }\n                    return Promise.resolve('' + backgroundImage.prefix + backgroundImage.method + '(' + backgroundImage.args.join(',') + ')');\n                })).then(function (backgroundImages) {\n                    if (backgroundImages.length > 1) {\n                        // TODO Multiple backgrounds somehow broken in Chrome\n                        style.backgroundColor = '';\n                    }\n                    style.backgroundImage = backgroundImages.join(',');\n                });\n\n                if (node instanceof HTMLImageElement) {\n                    this.resourceLoader.inlineImage(node.src).then(function (img) {\n                        if (img && node instanceof HTMLImageElement && node.parentNode) {\n                            var parentNode = node.parentNode;\n                            var clonedChild = (0, _Util.copyCSSStyles)(node.style, img.cloneNode(false));\n                            parentNode.replaceChild(clonedChild, node);\n                        }\n                    }).catch(function (e) {\n                        if (process.env.NODE_ENV !== 'production') {\n                            _this.logger.log('Unable to load image', e);\n                        }\n                    });\n                }\n            }\n        }\n    }, {\n        key: 'inlineFonts',\n        value: function inlineFonts(document) {\n            var _this2 = this;\n\n            return Promise.all(Array.from(document.styleSheets).map(function (sheet) {\n                if (sheet.href) {\n                    return fetch(sheet.href).then(function (res) {\n                        return res.text();\n                    }).then(function (text) {\n                        return createStyleSheetFontsFromText(text, sheet.href);\n                    }).catch(function (e) {\n                        if (process.env.NODE_ENV !== 'production') {\n                            _this2.logger.log('Unable to load stylesheet', e);\n                        }\n                        return [];\n                    });\n                }\n                return getSheetFonts(sheet, document);\n            })).then(function (fonts) {\n                return fonts.reduce(function (acc, font) {\n                    return acc.concat(font);\n                }, []);\n            }).then(function (fonts) {\n                return Promise.all(fonts.map(function (font) {\n                    return fetch(font.formats[0].src).then(function (response) {\n                        return response.blob();\n                    }).then(function (blob) {\n                        return new Promise(function (resolve, reject) {\n                            var reader = new FileReader();\n                            reader.onerror = reject;\n                            reader.onload = function () {\n                                // $FlowFixMe\n                                var result = reader.result;\n                                resolve(result);\n                            };\n                            reader.readAsDataURL(blob);\n                        });\n                    }).then(function (dataUri) {\n                        font.fontFace.setProperty('src', 'url(\"' + dataUri + '\")');\n                        return '@font-face {' + font.fontFace.cssText + ' ';\n                    });\n                }));\n            }).then(function (fontCss) {\n                var style = document.createElement('style');\n                style.textContent = fontCss.join('\\n');\n                _this2.documentElement.appendChild(style);\n            });\n        }\n    }, {\n        key: 'createElementClone',\n        value: function createElementClone(node) {\n            var _this3 = this;\n\n            if (this.copyStyles && node instanceof HTMLCanvasElement) {\n                var img = node.ownerDocument.createElement('img');\n                try {\n                    img.src = node.toDataURL();\n                    return img;\n                } catch (e) {\n                    if (process.env.NODE_ENV !== 'production') {\n                        this.logger.log('Unable to clone canvas contents, canvas is tainted');\n                    }\n                }\n            }\n\n            if (node instanceof HTMLIFrameElement) {\n                var tempIframe = node.cloneNode(false);\n                var iframeKey = generateIframeKey();\n                tempIframe.setAttribute('data-html2canvas-internal-iframe-key', iframeKey);\n\n                var _parseBounds = (0, _Bounds.parseBounds)(node, 0, 0),\n                    width = _parseBounds.width,\n                    height = _parseBounds.height;\n\n                this.resourceLoader.cache[iframeKey] = getIframeDocumentElement(node, this.options).then(function (documentElement) {\n                    return _this3.renderer(documentElement, {\n                        async: _this3.options.async,\n                        allowTaint: _this3.options.allowTaint,\n                        backgroundColor: '#ffffff',\n                        canvas: null,\n                        imageTimeout: _this3.options.imageTimeout,\n                        logging: _this3.options.logging,\n                        proxy: _this3.options.proxy,\n                        removeContainer: _this3.options.removeContainer,\n                        scale: _this3.options.scale,\n                        foreignObjectRendering: _this3.options.foreignObjectRendering,\n                        useCORS: _this3.options.useCORS,\n                        target: new _CanvasRenderer2.default(),\n                        width: width,\n                        height: height,\n                        x: 0,\n                        y: 0,\n                        windowWidth: documentElement.ownerDocument.defaultView.innerWidth,\n                        windowHeight: documentElement.ownerDocument.defaultView.innerHeight,\n                        scrollX: documentElement.ownerDocument.defaultView.pageXOffset,\n                        scrollY: documentElement.ownerDocument.defaultView.pageYOffset\n                    }, _this3.logger.child(iframeKey));\n                }).then(function (canvas) {\n                    return new Promise(function (resolve, reject) {\n                        var iframeCanvas = document.createElement('img');\n                        iframeCanvas.onload = function () {\n                            return resolve(canvas);\n                        };\n                        iframeCanvas.onerror = reject;\n                        iframeCanvas.src = canvas.toDataURL();\n                        if (tempIframe.parentNode) {\n                            tempIframe.parentNode.replaceChild((0, _Util.copyCSSStyles)(node.ownerDocument.defaultView.getComputedStyle(node), iframeCanvas), tempIframe);\n                        }\n                    });\n                });\n                return tempIframe;\n            }\n\n            if (node instanceof HTMLStyleElement && node.sheet && node.sheet.cssRules) {\n                var css = [].slice.call(node.sheet.cssRules, 0).reduce(function (css, rule) {\n                    return css + rule.cssText;\n                }, '');\n                var style = node.cloneNode(false);\n                style.textContent = css;\n                return style;\n            }\n\n            return node.cloneNode(false);\n        }\n    }, {\n        key: 'cloneNode',\n        value: function cloneNode(node) {\n            var clone = node.nodeType === Node.TEXT_NODE ? document.createTextNode(node.nodeValue) : this.createElementClone(node);\n\n            var window = node.ownerDocument.defaultView;\n            var style = node instanceof window.HTMLElement ? window.getComputedStyle(node) : null;\n            var styleBefore = node instanceof window.HTMLElement ? window.getComputedStyle(node, ':before') : null;\n            var styleAfter = node instanceof window.HTMLElement ? window.getComputedStyle(node, ':after') : null;\n\n            if (this.referenceElement === node && clone instanceof window.HTMLElement) {\n                this.clonedReferenceElement = clone;\n            }\n\n            if (clone instanceof window.HTMLBodyElement) {\n                createPseudoHideStyles(clone);\n            }\n\n            var counters = (0, _PseudoNodeContent.parseCounterReset)(style, this.pseudoContentData);\n            var contentBefore = (0, _PseudoNodeContent.resolvePseudoContent)(node, styleBefore, this.pseudoContentData);\n\n            for (var child = node.firstChild; child; child = child.nextSibling) {\n                if (child.nodeType !== Node.ELEMENT_NODE || child.nodeName !== 'SCRIPT' &&\n                // $FlowFixMe\n                !child.hasAttribute(IGNORE_ATTRIBUTE) && (typeof this.options.ignoreElements !== 'function' ||\n                // $FlowFixMe\n                !this.options.ignoreElements(child))) {\n                    if (!this.copyStyles || child.nodeName !== 'STYLE') {\n                        clone.appendChild(this.cloneNode(child));\n                    }\n                }\n            }\n\n            var contentAfter = (0, _PseudoNodeContent.resolvePseudoContent)(node, styleAfter, this.pseudoContentData);\n            (0, _PseudoNodeContent.popCounters)(counters, this.pseudoContentData);\n\n            if (node instanceof window.HTMLElement && clone instanceof window.HTMLElement) {\n                if (styleBefore) {\n                    this.inlineAllImages(inlinePseudoElement(node, clone, styleBefore, contentBefore, PSEUDO_BEFORE));\n                }\n                if (styleAfter) {\n                    this.inlineAllImages(inlinePseudoElement(node, clone, styleAfter, contentAfter, PSEUDO_AFTER));\n                }\n                if (style && this.copyStyles && !(node instanceof HTMLIFrameElement)) {\n                    (0, _Util.copyCSSStyles)(style, clone);\n                }\n                this.inlineAllImages(clone);\n                if (node.scrollTop !== 0 || node.scrollLeft !== 0) {\n                    this.scrolledElements.push([clone, node.scrollLeft, node.scrollTop]);\n                }\n                switch (node.nodeName) {\n                    case 'CANVAS':\n                        if (!this.copyStyles) {\n                            cloneCanvasContents(node, clone);\n                        }\n                        break;\n                    case 'TEXTAREA':\n                    case 'SELECT':\n                        clone.value = node.value;\n                        break;\n                    case 'INPUT':\n                        if (node.checked) {\n                            // required for IE9 and 10\n                            clone.setAttribute('checked', true);\n                        }\n                        break;\n                }\n            }\n            return clone;\n        }\n    }]);\n\n    return DocumentCloner;\n}();\n\nvar getSheetFonts = function getSheetFonts(sheet, document) {\n    // $FlowFixMe\n    return (sheet.cssRules ? Array.from(sheet.cssRules) : []).filter(function (rule) {\n        return rule.type === CSSRule.FONT_FACE_RULE;\n    }).map(function (rule) {\n        var src = (0, _background.parseBackgroundImage)(rule.style.getPropertyValue('src'));\n        var formats = [];\n        for (var i = 0; i < src.length; i++) {\n            if (src[i].method === 'url' && src[i + 1] && src[i + 1].method === 'format') {\n                var a = document.createElement('a');\n                a.href = src[i].args[0];\n                if (document.body) {\n                    document.body.appendChild(a);\n                }\n\n                var font = {\n                    src: a.href,\n                    format: src[i + 1].args[0]\n                };\n                formats.push(font);\n            }\n        }\n\n        return {\n            // TODO select correct format for browser),\n\n            formats: formats.filter(function (font) {\n                return (/^woff/i.test(font.format)\n                );\n            }),\n            fontFace: rule.style\n        };\n    }).filter(function (font) {\n        return font.formats.length;\n    });\n};\n\nvar createStyleSheetFontsFromText = function createStyleSheetFontsFromText(text, baseHref) {\n    var doc = document.implementation.createHTMLDocument('');\n    var base = document.createElement('base');\n    // $FlowFixMe\n    base.href = baseHref;\n    var style = document.createElement('style');\n\n    style.textContent = text;\n    if (doc.head) {\n        doc.head.appendChild(base);\n    }\n    if (doc.body) {\n        doc.body.appendChild(style);\n    }\n\n    return style.sheet ? getSheetFonts(style.sheet, doc) : [];\n};\n\nvar restoreOwnerScroll = function restoreOwnerScroll(ownerDocument, x, y) {\n    if (ownerDocument.defaultView && (x !== ownerDocument.defaultView.pageXOffset || y !== ownerDocument.defaultView.pageYOffset)) {\n        ownerDocument.defaultView.scrollTo(x, y);\n    }\n};\n\nvar cloneCanvasContents = function cloneCanvasContents(canvas, clonedCanvas) {\n    try {\n        if (clonedCanvas) {\n            clonedCanvas.width = canvas.width;\n            clonedCanvas.height = canvas.height;\n            var ctx = canvas.getContext('2d');\n            var clonedCtx = clonedCanvas.getContext('2d');\n            if (ctx) {\n                clonedCtx.putImageData(ctx.getImageData(0, 0, canvas.width, canvas.height), 0, 0);\n            } else {\n                clonedCtx.drawImage(canvas, 0, 0);\n            }\n        }\n    } catch (e) {}\n};\n\nvar inlinePseudoElement = function inlinePseudoElement(node, clone, style, contentItems, pseudoElt) {\n    if (!style || !style.content || style.content === 'none' || style.content === '-moz-alt-content' || style.display === 'none') {\n        return;\n    }\n\n    var anonymousReplacedElement = clone.ownerDocument.createElement('html2canvaspseudoelement');\n    (0, _Util.copyCSSStyles)(style, anonymousReplacedElement);\n\n    if (contentItems) {\n        var len = contentItems.length;\n        for (var i = 0; i < len; i++) {\n            var item = contentItems[i];\n            switch (item.type) {\n                case _PseudoNodeContent.PSEUDO_CONTENT_ITEM_TYPE.IMAGE:\n                    var img = clone.ownerDocument.createElement('img');\n                    img.src = (0, _background.parseBackgroundImage)('url(' + item.value + ')')[0].args[0];\n                    img.style.opacity = '1';\n                    anonymousReplacedElement.appendChild(img);\n                    break;\n                case _PseudoNodeContent.PSEUDO_CONTENT_ITEM_TYPE.TEXT:\n                    anonymousReplacedElement.appendChild(clone.ownerDocument.createTextNode(item.value));\n                    break;\n            }\n        }\n    }\n\n    anonymousReplacedElement.className = PSEUDO_HIDE_ELEMENT_CLASS_BEFORE + ' ' + PSEUDO_HIDE_ELEMENT_CLASS_AFTER;\n    clone.className += pseudoElt === PSEUDO_BEFORE ? ' ' + PSEUDO_HIDE_ELEMENT_CLASS_BEFORE : ' ' + PSEUDO_HIDE_ELEMENT_CLASS_AFTER;\n    if (pseudoElt === PSEUDO_BEFORE) {\n        clone.insertBefore(anonymousReplacedElement, clone.firstChild);\n    } else {\n        clone.appendChild(anonymousReplacedElement);\n    }\n\n    return anonymousReplacedElement;\n};\n\nvar URL_REGEXP = /^url\\((.+)\\)$/i;\nvar PSEUDO_BEFORE = ':before';\nvar PSEUDO_AFTER = ':after';\nvar PSEUDO_HIDE_ELEMENT_CLASS_BEFORE = '___html2canvas___pseudoelement_before';\nvar PSEUDO_HIDE_ELEMENT_CLASS_AFTER = '___html2canvas___pseudoelement_after';\n\nvar PSEUDO_HIDE_ELEMENT_STYLE = '{\\n    content: \"\" !important;\\n    display: none !important;\\n}';\n\nvar createPseudoHideStyles = function createPseudoHideStyles(body) {\n    createStyles(body, '.' + PSEUDO_HIDE_ELEMENT_CLASS_BEFORE + PSEUDO_BEFORE + PSEUDO_HIDE_ELEMENT_STYLE + '\\n         .' + PSEUDO_HIDE_ELEMENT_CLASS_AFTER + PSEUDO_AFTER + PSEUDO_HIDE_ELEMENT_STYLE);\n};\n\nvar createStyles = function createStyles(body, styles) {\n    var style = body.ownerDocument.createElement('style');\n    style.innerHTML = styles;\n    body.appendChild(style);\n};\n\nvar initNode = function initNode(_ref) {\n    var _ref2 = _slicedToArray(_ref, 3),\n        element = _ref2[0],\n        x = _ref2[1],\n        y = _ref2[2];\n\n    element.scrollLeft = x;\n    element.scrollTop = y;\n};\n\nvar generateIframeKey = function generateIframeKey() {\n    return Math.ceil(Date.now() + Math.random() * 10000000).toString(16);\n};\n\nvar DATA_URI_REGEXP = /^data:text\\/(.+);(base64)?,(.*)$/i;\n\nvar getIframeDocumentElement = function getIframeDocumentElement(node, options) {\n    try {\n        return Promise.resolve(node.contentWindow.document.documentElement);\n    } catch (e) {\n        return options.proxy ? (0, _Proxy.Proxy)(node.src, options).then(function (html) {\n            var match = html.match(DATA_URI_REGEXP);\n            if (!match) {\n                return Promise.reject();\n            }\n\n            return match[2] === 'base64' ? window.atob(decodeURIComponent(match[3])) : decodeURIComponent(match[3]);\n        }).then(function (html) {\n            return createIframeContainer(node.ownerDocument, (0, _Bounds.parseBounds)(node, 0, 0)).then(function (cloneIframeContainer) {\n                var cloneWindow = cloneIframeContainer.contentWindow;\n                var documentClone = cloneWindow.document;\n\n                documentClone.open();\n                documentClone.write(html);\n                var iframeLoad = iframeLoader(cloneIframeContainer).then(function () {\n                    return documentClone.documentElement;\n                });\n\n                documentClone.close();\n                return iframeLoad;\n            });\n        }) : Promise.reject();\n    }\n};\n\nvar createIframeContainer = function createIframeContainer(ownerDocument, bounds) {\n    var cloneIframeContainer = ownerDocument.createElement('iframe');\n\n    cloneIframeContainer.className = 'html2canvas-container';\n    cloneIframeContainer.style.visibility = 'hidden';\n    cloneIframeContainer.style.position = 'fixed';\n    cloneIframeContainer.style.left = '-10000px';\n    cloneIframeContainer.style.top = '0px';\n    cloneIframeContainer.style.border = '0';\n    cloneIframeContainer.width = bounds.width.toString();\n    cloneIframeContainer.height = bounds.height.toString();\n    cloneIframeContainer.scrolling = 'no'; // ios won't scroll without it\n    cloneIframeContainer.setAttribute(IGNORE_ATTRIBUTE, 'true');\n    if (!ownerDocument.body) {\n        return Promise.reject(process.env.NODE_ENV !== 'production' ? 'Body element not found in Document that is getting rendered' : '');\n    }\n\n    ownerDocument.body.appendChild(cloneIframeContainer);\n\n    return Promise.resolve(cloneIframeContainer);\n};\n\nvar iframeLoader = function iframeLoader(cloneIframeContainer) {\n    var cloneWindow = cloneIframeContainer.contentWindow;\n    var documentClone = cloneWindow.document;\n\n    return new Promise(function (resolve, reject) {\n        cloneWindow.onload = cloneIframeContainer.onload = documentClone.onreadystatechange = function () {\n            var interval = setInterval(function () {\n                if (documentClone.body.childNodes.length > 0 && documentClone.readyState === 'complete') {\n                    clearInterval(interval);\n                    resolve(cloneIframeContainer);\n                }\n            }, 50);\n        };\n    });\n};\n\nvar cloneWindow = exports.cloneWindow = function cloneWindow(ownerDocument, bounds, referenceElement, options, logger, renderer) {\n    var cloner = new DocumentCloner(referenceElement, options, logger, false, renderer);\n    var scrollX = ownerDocument.defaultView.pageXOffset;\n    var scrollY = ownerDocument.defaultView.pageYOffset;\n\n    return createIframeContainer(ownerDocument, bounds).then(function (cloneIframeContainer) {\n        var cloneWindow = cloneIframeContainer.contentWindow;\n        var documentClone = cloneWindow.document;\n\n        /* Chrome doesn't detect relative background-images assigned in inline <style> sheets when fetched through getComputedStyle\n             if window url is about:blank, we can assign the url to current by writing onto the document\n             */\n\n        var iframeLoad = iframeLoader(cloneIframeContainer).then(function () {\n            cloner.scrolledElements.forEach(initNode);\n            cloneWindow.scrollTo(bounds.left, bounds.top);\n            if (/(iPad|iPhone|iPod)/g.test(navigator.userAgent) && (cloneWindow.scrollY !== bounds.top || cloneWindow.scrollX !== bounds.left)) {\n                documentClone.documentElement.style.top = -bounds.top + 'px';\n                documentClone.documentElement.style.left = -bounds.left + 'px';\n                documentClone.documentElement.style.position = 'absolute';\n            }\n\n            var result = Promise.resolve([cloneIframeContainer, cloner.clonedReferenceElement, cloner.resourceLoader]);\n\n            var onclone = options.onclone;\n\n            return cloner.clonedReferenceElement instanceof cloneWindow.HTMLElement || cloner.clonedReferenceElement instanceof ownerDocument.defaultView.HTMLElement || cloner.clonedReferenceElement instanceof HTMLElement ? typeof onclone === 'function' ? Promise.resolve().then(function () {\n                return onclone(documentClone);\n            }).then(function () {\n                return result;\n            }) : result : Promise.reject(process.env.NODE_ENV !== 'production' ? 'Error finding the ' + referenceElement.nodeName + ' in the cloned document' : '');\n        });\n\n        documentClone.open();\n        documentClone.write(serializeDoctype(document.doctype) + '<html></html>');\n        // Chrome scrolls the parent document for some reason after the write to the cloned window???\n        restoreOwnerScroll(referenceElement.ownerDocument, scrollX, scrollY);\n        documentClone.replaceChild(documentClone.adoptNode(cloner.documentElement), documentClone.documentElement);\n        documentClone.close();\n\n        return iframeLoad;\n    });\n};\n\nvar serializeDoctype = function serializeDoctype(doctype) {\n    var str = '';\n    if (doctype) {\n        str += '<!DOCTYPE ';\n        if (doctype.name) {\n            str += doctype.name;\n        }\n\n        if (doctype.internalSubset) {\n            str += doctype.internalSubset;\n        }\n\n        if (doctype.publicId) {\n            str += '\"' + doctype.publicId + '\"';\n        }\n\n        if (doctype.systemId) {\n            str += '\"' + doctype.systemId + '\"';\n        }\n\n        str += '>';\n    }\n\n    return str;\n};", "'use strict';\n\nmodule.exports = '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';", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\nvar _ForeignObjectRenderer = require('./renderer/ForeignObjectRenderer');\n\nvar testRangeBounds = function testRangeBounds(document) {\n    var TEST_HEIGHT = 123;\n\n    if (document.createRange) {\n        var range = document.createRange();\n        if (range.getBoundingClientRect) {\n            var testElement = document.createElement('boundtest');\n            testElement.style.height = TEST_HEIGHT + 'px';\n            testElement.style.display = 'block';\n            document.body.appendChild(testElement);\n\n            range.selectNode(testElement);\n            var rangeBounds = range.getBoundingClientRect();\n            var rangeHeight = Math.round(rangeBounds.height);\n            document.body.removeChild(testElement);\n            if (rangeHeight === TEST_HEIGHT) {\n                return true;\n            }\n        }\n    }\n\n    return false;\n};\n\n// iOS 10.3 taints canvas with base64 images unless crossOrigin = 'anonymous'\nvar testBase64 = function testBase64(document, src) {\n    var img = new Image();\n    var canvas = document.createElement('canvas');\n    var ctx = canvas.getContext('2d');\n\n    return new Promise(function (resolve) {\n        // Single pixel base64 image renders fine on iOS 10.3???\n        img.src = src;\n\n        var onload = function onload() {\n            try {\n                ctx.drawImage(img, 0, 0);\n                canvas.toDataURL();\n            } catch (e) {\n                return resolve(false);\n            }\n\n            return resolve(true);\n        };\n\n        img.onload = onload;\n        img.onerror = function () {\n            return resolve(false);\n        };\n\n        if (img.complete === true) {\n            setTimeout(function () {\n                onload();\n            }, 500);\n        }\n    });\n};\n\nvar testCORS = function testCORS() {\n    return typeof new Image().crossOrigin !== 'undefined';\n};\n\nvar testResponseType = function testResponseType() {\n    return typeof new XMLHttpRequest().responseType === 'string';\n};\n\nvar testSVG = function testSVG(document) {\n    var img = new Image();\n    var canvas = document.createElement('canvas');\n    var ctx = canvas.getContext('2d');\n    img.src = 'data:image/svg+xml,<svg xmlns=\\'http://www.w3.org/2000/svg\\'></svg>';\n\n    try {\n        ctx.drawImage(img, 0, 0);\n        canvas.toDataURL();\n    } catch (e) {\n        return false;\n    }\n    return true;\n};\n\nvar isGreenPixel = function isGreenPixel(data) {\n    return data[0] === 0 && data[1] === 255 && data[2] === 0 && data[3] === 255;\n};\n\nvar testForeignObject = function testForeignObject(document) {\n    var canvas = document.createElement('canvas');\n    var size = 100;\n    canvas.width = size;\n    canvas.height = size;\n    var ctx = canvas.getContext('2d');\n    ctx.fillStyle = 'rgb(0, 255, 0)';\n    ctx.fillRect(0, 0, size, size);\n\n    var img = new Image();\n    var greenImageSrc = canvas.toDataURL();\n    img.src = greenImageSrc;\n    var svg = (0, _ForeignObjectRenderer.createForeignObjectSVG)(size, size, 0, 0, img);\n    ctx.fillStyle = 'red';\n    ctx.fillRect(0, 0, size, size);\n\n    return (0, _ForeignObjectRenderer.loadSerializedSVG)(svg).then(function (img) {\n        ctx.drawImage(img, 0, 0);\n        var data = ctx.getImageData(0, 0, size, size).data;\n        ctx.fillStyle = 'red';\n        ctx.fillRect(0, 0, size, size);\n\n        var node = document.createElement('div');\n        node.style.backgroundImage = 'url(' + greenImageSrc + ')';\n        node.style.height = size + 'px';\n        // Firefox 55 does not render inline <img /> tags\n        return isGreenPixel(data) ? (0, _ForeignObjectRenderer.loadSerializedSVG)((0, _ForeignObjectRenderer.createForeignObjectSVG)(size, size, 0, 0, node)) : Promise.reject(false);\n    }).then(function (img) {\n        ctx.drawImage(img, 0, 0);\n        // Edge does not render background-images\n        return isGreenPixel(ctx.getImageData(0, 0, size, size).data);\n    }).catch(function (e) {\n        return false;\n    });\n};\n\nvar FEATURES = {\n    // $FlowFixMe - get/set properties not yet supported\n    get SUPPORT_RANGE_BOUNDS() {\n        'use strict';\n\n        var value = testRangeBounds(document);\n        Object.defineProperty(FEATURES, 'SUPPORT_RANGE_BOUNDS', { value: value });\n        return value;\n    },\n    // $FlowFixMe - get/set properties not yet supported\n    get SUPPORT_SVG_DRAWING() {\n        'use strict';\n\n        var value = testSVG(document);\n        Object.defineProperty(FEATURES, 'SUPPORT_SVG_DRAWING', { value: value });\n        return value;\n    },\n    // $FlowFixMe - get/set properties not yet supported\n    get SUPPORT_BASE64_DRAWING() {\n        'use strict';\n\n        return function (src) {\n            var _value = testBase64(document, src);\n            Object.defineProperty(FEATURES, 'SUPPORT_BASE64_DRAWING', { value: function value() {\n                    return _value;\n                } });\n            return _value;\n        };\n    },\n    // $FlowFixMe - get/set properties not yet supported\n    get SUPPORT_FOREIGNOBJECT_DRAWING() {\n        'use strict';\n\n        var value = typeof Array.from === 'function' && typeof window.fetch === 'function' ? testForeignObject(document) : Promise.resolve(false);\n        Object.defineProperty(FEATURES, 'SUPPORT_FOREIGNOBJECT_DRAWING', { value: value });\n        return value;\n    },\n    // $FlowFixMe - get/set properties not yet supported\n    get SUPPORT_CORS_IMAGES() {\n        'use strict';\n\n        var value = testCORS();\n        Object.defineProperty(FEATURES, 'SUPPORT_CORS_IMAGES', { value: value });\n        return value;\n    },\n    // $FlowFixMe - get/set properties not yet supported\n    get SUPPORT_RESPONSE_TYPE() {\n        'use strict';\n\n        var value = testResponseType();\n        Object.defineProperty(FEATURES, 'SUPPORT_RESPONSE_TYPE', { value: value });\n        return value;\n    },\n    // $FlowFixMe - get/set properties not yet supported\n    get SUPPORT_CORS_XHR() {\n        'use strict';\n\n        var value = 'withCredentials' in new XMLHttpRequest();\n        Object.defineProperty(FEATURES, 'SUPPORT_CORS_XHR', { value: value });\n        return value;\n    }\n};\n\nexports.default = FEATURES;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar Size = function Size(width, height) {\n    _classCallCheck(this, Size);\n\n    this.width = width;\n    this.height = height;\n};\n\nexports.default = Size;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.NodeParser = undefined;\n\nvar _StackingContext = require('./StackingContext');\n\nvar _StackingContext2 = _interopRequireDefault(_StackingContext);\n\nvar _NodeContainer = require('./NodeContainer');\n\nvar _NodeContainer2 = _interopRequireDefault(_NodeContainer);\n\nvar _TextContainer = require('./TextContainer');\n\nvar _TextContainer2 = _interopRequireDefault(_TextContainer);\n\nvar _Input = require('./Input');\n\nvar _ListItem = require('./ListItem');\n\nvar _listStyle = require('./parsing/listStyle');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar NodeParser = exports.NodeParser = function NodeParser(node, resourceLoader, logger) {\n    if (process.env.NODE_ENV !== 'production') {\n        logger.log('Starting node parsing');\n    }\n\n    var index = 0;\n\n    var container = new _NodeContainer2.default(node, null, resourceLoader, index++);\n    var stack = new _StackingContext2.default(container, null, true);\n\n    parseNodeTree(node, container, stack, resourceLoader, index);\n\n    if (process.env.NODE_ENV !== 'production') {\n        logger.log('Finished parsing node tree');\n    }\n\n    return stack;\n};\n\nvar IGNORED_NODE_NAMES = ['SCRIPT', 'HEAD', 'TITLE', 'OBJECT', 'BR', 'OPTION'];\n\nvar parseNodeTree = function parseNodeTree(node, parent, stack, resourceLoader, index) {\n    if (process.env.NODE_ENV !== 'production' && index > 50000) {\n        throw new Error('Recursion error while parsing node tree');\n    }\n\n    for (var childNode = node.firstChild, nextNode; childNode; childNode = nextNode) {\n        nextNode = childNode.nextSibling;\n        var defaultView = childNode.ownerDocument.defaultView;\n        if (childNode instanceof defaultView.Text || childNode instanceof Text || defaultView.parent && childNode instanceof defaultView.parent.Text) {\n            if (childNode.data.trim().length > 0) {\n                parent.childNodes.push(_TextContainer2.default.fromTextNode(childNode, parent));\n            }\n        } else if (childNode instanceof defaultView.HTMLElement || childNode instanceof HTMLElement || defaultView.parent && childNode instanceof defaultView.parent.HTMLElement) {\n            if (IGNORED_NODE_NAMES.indexOf(childNode.nodeName) === -1) {\n                var container = new _NodeContainer2.default(childNode, parent, resourceLoader, index++);\n                if (container.isVisible()) {\n                    if (childNode.tagName === 'INPUT') {\n                        // $FlowFixMe\n                        (0, _Input.inlineInputElement)(childNode, container);\n                    } else if (childNode.tagName === 'TEXTAREA') {\n                        // $FlowFixMe\n                        (0, _Input.inlineTextAreaElement)(childNode, container);\n                    } else if (childNode.tagName === 'SELECT') {\n                        // $FlowFixMe\n                        (0, _Input.inlineSelectElement)(childNode, container);\n                    } else if (container.style.listStyle && container.style.listStyle.listStyleType !== _listStyle.LIST_STYLE_TYPE.NONE) {\n                        (0, _ListItem.inlineListItemElement)(childNode, container, resourceLoader);\n                    }\n\n                    var SHOULD_TRAVERSE_CHILDREN = childNode.tagName !== 'TEXTAREA';\n                    var treatAsRealStackingContext = createsRealStackingContext(container, childNode);\n                    if (treatAsRealStackingContext || createsStackingContext(container)) {\n                        // for treatAsRealStackingContext:false, any positioned descendants and descendants\n                        // which actually create a new stacking context should be considered part of the parent stacking context\n                        var parentStack = treatAsRealStackingContext || container.isPositioned() ? stack.getRealParentStackingContext() : stack;\n                        var childStack = new _StackingContext2.default(container, parentStack, treatAsRealStackingContext);\n                        parentStack.contexts.push(childStack);\n                        if (SHOULD_TRAVERSE_CHILDREN) {\n                            parseNodeTree(childNode, container, childStack, resourceLoader, index);\n                        }\n                    } else {\n                        stack.children.push(container);\n                        if (SHOULD_TRAVERSE_CHILDREN) {\n                            parseNodeTree(childNode, container, stack, resourceLoader, index);\n                        }\n                    }\n                }\n            }\n        } else if (childNode instanceof defaultView.SVGSVGElement || childNode instanceof SVGSVGElement || defaultView.parent && childNode instanceof defaultView.parent.SVGSVGElement) {\n            var _container = new _NodeContainer2.default(childNode, parent, resourceLoader, index++);\n            var _treatAsRealStackingContext = createsRealStackingContext(_container, childNode);\n            if (_treatAsRealStackingContext || createsStackingContext(_container)) {\n                // for treatAsRealStackingContext:false, any positioned descendants and descendants\n                // which actually create a new stacking context should be considered part of the parent stacking context\n                var _parentStack = _treatAsRealStackingContext || _container.isPositioned() ? stack.getRealParentStackingContext() : stack;\n                var _childStack = new _StackingContext2.default(_container, _parentStack, _treatAsRealStackingContext);\n                _parentStack.contexts.push(_childStack);\n            } else {\n                stack.children.push(_container);\n            }\n        }\n    }\n};\n\nvar createsRealStackingContext = function createsRealStackingContext(container, node) {\n    return container.isRootElement() || container.isPositionedWithZIndex() || container.style.opacity < 1 || container.isTransformed() || isBodyWithTransparentRoot(container, node);\n};\n\nvar createsStackingContext = function createsStackingContext(container) {\n    return container.isPositioned() || container.isFloating();\n};\n\nvar isBodyWithTransparentRoot = function isBodyWithTransparentRoot(container, node) {\n    return node.nodeName === 'BODY' && container.parent instanceof _NodeContainer2.default && container.parent.style.background.backgroundColor.isTransparent();\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.parseBorder = exports.BORDER_SIDES = exports.BORDER_STYLE = undefined;\n\nvar _Color = require('../Color');\n\nvar _Color2 = _interopRequireDefault(_Color);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar BORDER_STYLE = exports.BORDER_STYLE = {\n    NONE: 0,\n    SOLID: 1\n};\n\nvar BORDER_SIDES = exports.BORDER_SIDES = {\n    TOP: 0,\n    RIGHT: 1,\n    BOTTOM: 2,\n    LEFT: 3\n};\n\nvar SIDES = Object.keys(BORDER_SIDES).map(function (s) {\n    return s.toLowerCase();\n});\n\nvar parseBorderStyle = function parseBorderStyle(style) {\n    switch (style) {\n        case 'none':\n            return BORDER_STYLE.NONE;\n    }\n    return BORDER_STYLE.SOLID;\n};\n\nvar parseBorder = exports.parseBorder = function parseBorder(style) {\n    return SIDES.map(function (side) {\n        var borderColor = new _Color2.default(style.getPropertyValue('border-' + side + '-color'));\n        var borderStyle = parseBorderStyle(style.getPropertyValue('border-' + side + '-style'));\n        var borderWidth = parseFloat(style.getPropertyValue('border-' + side + '-width'));\n        return {\n            borderColor: borderColor,\n            borderStyle: borderStyle,\n            borderWidth: isNaN(borderWidth) ? 0 : borderWidth\n        };\n    });\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\nvar _Path = require('./Path');\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar Circle = function Circle(x, y, radius) {\n    _classCallCheck(this, Circle);\n\n    this.type = _Path.PATH.CIRCLE;\n    this.x = x;\n    this.y = y;\n    this.radius = radius;\n    if (process.env.NODE_ENV !== 'production') {\n        if (isNaN(x)) {\n            console.error('Invalid x value given for Circle');\n        }\n        if (isNaN(y)) {\n            console.error('Invalid y value given for Circle');\n        }\n        if (isNaN(radius)) {\n            console.error('Invalid radius value given for Circle');\n        }\n    }\n};\n\nexports.default = Circle;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.parseTransform = undefined;\n\nvar _Length = require('../Length');\n\nvar _Length2 = _interopRequireDefault(_Length);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar toFloat = function toFloat(s) {\n    return parseFloat(s.trim());\n};\n\nvar MATRIX = /(matrix|matrix3d)\\((.+)\\)/;\n\nvar parseTransform = exports.parseTransform = function parseTransform(style) {\n    var transform = parseTransformMatrix(style.transform || style.webkitTransform || style.mozTransform ||\n    // $FlowFixMe\n    style.msTransform ||\n    // $FlowFixMe\n    style.oTransform);\n    if (transform === null) {\n        return null;\n    }\n\n    return {\n        transform: transform,\n        transformOrigin: parseTransformOrigin(style.transformOrigin || style.webkitTransformOrigin || style.mozTransformOrigin ||\n        // $FlowFixMe\n        style.msTransformOrigin ||\n        // $FlowFixMe\n        style.oTransformOrigin)\n    };\n};\n\n// $FlowFixMe\nvar parseTransformOrigin = function parseTransformOrigin(origin) {\n    if (typeof origin !== 'string') {\n        var v = new _Length2.default('0');\n        return [v, v];\n    }\n    var values = origin.split(' ').map(_Length2.default.create);\n    return [values[0], values[1]];\n};\n\n// $FlowFixMe\nvar parseTransformMatrix = function parseTransformMatrix(transform) {\n    if (transform === 'none' || typeof transform !== 'string') {\n        return null;\n    }\n\n    var match = transform.match(MATRIX);\n    if (match) {\n        if (match[1] === 'matrix') {\n            var matrix = match[2].split(',').map(toFloat);\n            return [matrix[0], matrix[1], matrix[2], matrix[3], matrix[4], matrix[5]];\n        } else {\n            var matrix3d = match[2].split(',').map(toFloat);\n            return [matrix3d[0], matrix3d[1], matrix3d[4], matrix3d[5], matrix3d[12], matrix3d[13]];\n        }\n    }\n    return null;\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.calculateLengthFromValueWithUnit = exports.LENGTH_TYPE = undefined;\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _NodeContainer = require('./NodeContainer');\n\nvar _NodeContainer2 = _interopRequireDefault(_NodeContainer);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar LENGTH_WITH_UNIT = /([\\d.]+)(px|r?em|%)/i;\n\nvar LENGTH_TYPE = exports.LENGTH_TYPE = {\n    PX: 0,\n    PERCENTAGE: 1\n};\n\nvar Length = function () {\n    function Length(value) {\n        _classCallCheck(this, Length);\n\n        this.type = value.substr(value.length - 1) === '%' ? LENGTH_TYPE.PERCENTAGE : LENGTH_TYPE.PX;\n        var parsedValue = parseFloat(value);\n        if (process.env.NODE_ENV !== 'production' && isNaN(parsedValue)) {\n            console.error('Invalid value given for Length: \"' + value + '\"');\n        }\n        this.value = isNaN(parsedValue) ? 0 : parsedValue;\n    }\n\n    _createClass(Length, [{\n        key: 'isPercentage',\n        value: function isPercentage() {\n            return this.type === LENGTH_TYPE.PERCENTAGE;\n        }\n    }, {\n        key: 'getAbsoluteValue',\n        value: function getAbsoluteValue(parentLength) {\n            return this.isPercentage() ? parentLength * (this.value / 100) : this.value;\n        }\n    }], [{\n        key: 'create',\n        value: function create(v) {\n            return new Length(v);\n        }\n    }]);\n\n    return Length;\n}();\n\nexports.default = Length;\n\n\nvar getRootFontSize = function getRootFontSize(container) {\n    var parent = container.parent;\n    return parent ? getRootFontSize(parent) : parseFloat(container.style.font.fontSize);\n};\n\nvar calculateLengthFromValueWithUnit = exports.calculateLengthFromValueWithUnit = function calculateLengthFromValueWithUnit(container, value, unit) {\n    switch (unit) {\n        case 'px':\n        case '%':\n            return new Length(value + unit);\n        case 'em':\n        case 'rem':\n            var length = new Length(value);\n            length.value *= unit === 'em' ? parseFloat(container.style.font.fontSize) : getRootFontSize(container);\n            return length;\n        default:\n            // TODO: handle correctly if unknown unit is used\n            return new Length('0');\n    }\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\n\nvar parseFontWeight = function parseFontWeight(weight) {\n    switch (weight) {\n        case 'normal':\n            return 400;\n        case 'bold':\n            return 700;\n    }\n\n    var value = parseInt(weight, 10);\n    return isNaN(value) ? 400 : value;\n};\n\nvar parseFont = exports.parseFont = function parseFont(style) {\n    var fontFamily = style.fontFamily;\n    var fontSize = style.fontSize;\n    var fontStyle = style.fontStyle;\n    var fontVariant = style.fontVariant;\n    var fontWeight = parseFontWeight(style.fontWeight);\n\n    return {\n        fontFamily: fontFamily,\n        fontSize: fontSize,\n        fontStyle: fontStyle,\n        fontVariant: fontVariant,\n        fontWeight: fontWeight\n    };\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.parseTextBounds = exports.TextBounds = undefined;\n\nvar _Bounds = require('./Bounds');\n\nvar _textDecoration = require('./parsing/textDecoration');\n\nvar _Feature = require('./Feature');\n\nvar _Feature2 = _interopRequireDefault(_Feature);\n\nvar _Unicode = require('./Unicode');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar TextBounds = exports.TextBounds = function TextBounds(text, bounds) {\n    _classCallCheck(this, TextBounds);\n\n    this.text = text;\n    this.bounds = bounds;\n};\n\nvar parseTextBounds = exports.parseTextBounds = function parseTextBounds(value, parent, node) {\n    var letterRendering = parent.style.letterSpacing !== 0;\n    var textList = letterRendering ? (0, _Unicode.toCodePoints)(value).map(function (i) {\n        return (0, _Unicode.fromCodePoint)(i);\n    }) : (0, _Unicode.breakWords)(value, parent);\n    var length = textList.length;\n    var defaultView = node.parentNode ? node.parentNode.ownerDocument.defaultView : null;\n    var scrollX = defaultView ? defaultView.pageXOffset : 0;\n    var scrollY = defaultView ? defaultView.pageYOffset : 0;\n    var textBounds = [];\n    var offset = 0;\n    for (var i = 0; i < length; i++) {\n        var text = textList[i];\n        if (parent.style.textDecoration !== _textDecoration.TEXT_DECORATION.NONE || text.trim().length > 0) {\n            if (_Feature2.default.SUPPORT_RANGE_BOUNDS) {\n                textBounds.push(new TextBounds(text, getRangeBounds(node, offset, text.length, scrollX, scrollY)));\n            } else {\n                var replacementNode = node.splitText(text.length);\n                textBounds.push(new TextBounds(text, getWrapperBounds(node, scrollX, scrollY)));\n                node = replacementNode;\n            }\n        } else if (!_Feature2.default.SUPPORT_RANGE_BOUNDS) {\n            node = node.splitText(text.length);\n        }\n        offset += text.length;\n    }\n    return textBounds;\n};\n\nvar getWrapperBounds = function getWrapperBounds(node, scrollX, scrollY) {\n    var wrapper = node.ownerDocument.createElement('html2canvaswrapper');\n    wrapper.appendChild(node.cloneNode(true));\n    var parentNode = node.parentNode;\n    if (parentNode) {\n        parentNode.replaceChild(wrapper, node);\n        var bounds = (0, _Bounds.parseBounds)(wrapper, scrollX, scrollY);\n        if (wrapper.firstChild) {\n            parentNode.replaceChild(wrapper.firstChild, wrapper);\n        }\n        return bounds;\n    }\n    return new _Bounds.Bounds(0, 0, 0, 0);\n};\n\nvar getRangeBounds = function getRangeBounds(node, offset, length, scrollX, scrollY) {\n    var range = node.ownerDocument.createRange();\n    range.setStart(node, offset);\n    range.setEnd(node, offset + length);\n    return _Bounds.Bounds.fromClientRect(range.getBoundingClientRect(), scrollX, scrollY);\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.transformWebkitRadialGradientArgs = exports.parseGradient = exports.RadialGradient = exports.LinearGradient = exports.RADIAL_GRADIENT_SHAPE = exports.GRADIENT_TYPE = undefined;\n\nvar _slicedToArray = function () { function sliceIterator(arr, i) { var _arr = []; var _n = true; var _d = false; var _e = undefined; try { for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"]) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; } return function (arr, i) { if (Array.isArray(arr)) { return arr; } else if (Symbol.iterator in Object(arr)) { return sliceIterator(arr, i); } else { throw new TypeError(\"Invalid attempt to destructure non-iterable instance\"); } }; }();\n\nvar _NodeContainer = require('./NodeContainer');\n\nvar _NodeContainer2 = _interopRequireDefault(_NodeContainer);\n\nvar _Angle = require('./Angle');\n\nvar _Color = require('./Color');\n\nvar _Color2 = _interopRequireDefault(_Color);\n\nvar _Length = require('./Length');\n\nvar _Length2 = _interopRequireDefault(_Length);\n\nvar _Util = require('./Util');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar SIDE_OR_CORNER = /^(to )?(left|top|right|bottom)( (left|top|right|bottom))?$/i;\nvar PERCENTAGE_ANGLES = /^([+-]?\\d*\\.?\\d+)% ([+-]?\\d*\\.?\\d+)%$/i;\nvar ENDS_WITH_LENGTH = /(px)|%|( 0)$/i;\nvar FROM_TO_COLORSTOP = /^(from|to|color-stop)\\((?:([\\d.]+)(%)?,\\s*)?(.+?)\\)$/i;\nvar RADIAL_SHAPE_DEFINITION = /^\\s*(circle|ellipse)?\\s*((?:([\\d.]+)(px|r?em|%)\\s*(?:([\\d.]+)(px|r?em|%))?)|closest-side|closest-corner|farthest-side|farthest-corner)?\\s*(?:at\\s*(?:(left|center|right)|([\\d.]+)(px|r?em|%))\\s+(?:(top|center|bottom)|([\\d.]+)(px|r?em|%)))?(?:\\s|$)/i;\n\nvar GRADIENT_TYPE = exports.GRADIENT_TYPE = {\n    LINEAR_GRADIENT: 0,\n    RADIAL_GRADIENT: 1\n};\n\nvar RADIAL_GRADIENT_SHAPE = exports.RADIAL_GRADIENT_SHAPE = {\n    CIRCLE: 0,\n    ELLIPSE: 1\n};\n\nvar LENGTH_FOR_POSITION = {\n    left: new _Length2.default('0%'),\n    top: new _Length2.default('0%'),\n    center: new _Length2.default('50%'),\n    right: new _Length2.default('100%'),\n    bottom: new _Length2.default('100%')\n};\n\nvar LinearGradient = exports.LinearGradient = function LinearGradient(colorStops, direction) {\n    _classCallCheck(this, LinearGradient);\n\n    this.type = GRADIENT_TYPE.LINEAR_GRADIENT;\n    this.colorStops = colorStops;\n    this.direction = direction;\n};\n\nvar RadialGradient = exports.RadialGradient = function RadialGradient(colorStops, shape, center, radius) {\n    _classCallCheck(this, RadialGradient);\n\n    this.type = GRADIENT_TYPE.RADIAL_GRADIENT;\n    this.colorStops = colorStops;\n    this.shape = shape;\n    this.center = center;\n    this.radius = radius;\n};\n\nvar parseGradient = exports.parseGradient = function parseGradient(container, _ref, bounds) {\n    var args = _ref.args,\n        method = _ref.method,\n        prefix = _ref.prefix;\n\n    if (method === 'linear-gradient') {\n        return parseLinearGradient(args, bounds, !!prefix);\n    } else if (method === 'gradient' && args[0] === 'linear') {\n        // TODO handle correct angle\n        return parseLinearGradient(['to bottom'].concat(transformObsoleteColorStops(args.slice(3))), bounds, !!prefix);\n    } else if (method === 'radial-gradient') {\n        return parseRadialGradient(container, prefix === '-webkit-' ? transformWebkitRadialGradientArgs(args) : args, bounds);\n    } else if (method === 'gradient' && args[0] === 'radial') {\n        return parseRadialGradient(container, transformObsoleteColorStops(transformWebkitRadialGradientArgs(args.slice(1))), bounds);\n    }\n};\n\nvar parseColorStops = function parseColorStops(args, firstColorStopIndex, lineLength) {\n    var colorStops = [];\n\n    for (var i = firstColorStopIndex; i < args.length; i++) {\n        var value = args[i];\n        var HAS_LENGTH = ENDS_WITH_LENGTH.test(value);\n        var lastSpaceIndex = value.lastIndexOf(' ');\n        var _color = new _Color2.default(HAS_LENGTH ? value.substring(0, lastSpaceIndex) : value);\n        var _stop = HAS_LENGTH ? new _Length2.default(value.substring(lastSpaceIndex + 1)) : i === firstColorStopIndex ? new _Length2.default('0%') : i === args.length - 1 ? new _Length2.default('100%') : null;\n        colorStops.push({ color: _color, stop: _stop });\n    }\n\n    var absoluteValuedColorStops = colorStops.map(function (_ref2) {\n        var color = _ref2.color,\n            stop = _ref2.stop;\n\n        var absoluteStop = lineLength === 0 ? 0 : stop ? stop.getAbsoluteValue(lineLength) / lineLength : null;\n\n        return {\n            color: color,\n            // $FlowFixMe\n            stop: absoluteStop\n        };\n    });\n\n    var previousColorStop = absoluteValuedColorStops[0].stop;\n    for (var _i = 0; _i < absoluteValuedColorStops.length; _i++) {\n        if (previousColorStop !== null) {\n            var _stop2 = absoluteValuedColorStops[_i].stop;\n            if (_stop2 === null) {\n                var n = _i;\n                while (absoluteValuedColorStops[n].stop === null) {\n                    n++;\n                }\n                var steps = n - _i + 1;\n                var nextColorStep = absoluteValuedColorStops[n].stop;\n                var stepSize = (nextColorStep - previousColorStop) / steps;\n                for (; _i < n; _i++) {\n                    previousColorStop = absoluteValuedColorStops[_i].stop = previousColorStop + stepSize;\n                }\n            } else {\n                previousColorStop = _stop2;\n            }\n        }\n    }\n\n    return absoluteValuedColorStops;\n};\n\nvar parseLinearGradient = function parseLinearGradient(args, bounds, hasPrefix) {\n    var angle = (0, _Angle.parseAngle)(args[0]);\n    var HAS_SIDE_OR_CORNER = SIDE_OR_CORNER.test(args[0]);\n    var HAS_DIRECTION = HAS_SIDE_OR_CORNER || angle !== null || PERCENTAGE_ANGLES.test(args[0]);\n    var direction = HAS_DIRECTION ? angle !== null ? calculateGradientDirection(\n    // if there is a prefix, the 0° angle points due East (instead of North per W3C)\n    hasPrefix ? angle - Math.PI * 0.5 : angle, bounds) : HAS_SIDE_OR_CORNER ? parseSideOrCorner(args[0], bounds) : parsePercentageAngle(args[0], bounds) : calculateGradientDirection(Math.PI, bounds);\n    var firstColorStopIndex = HAS_DIRECTION ? 1 : 0;\n\n    // TODO: Fix some inaccuracy with color stops with px values\n    var lineLength = Math.min((0, _Util.distance)(Math.abs(direction.x0) + Math.abs(direction.x1), Math.abs(direction.y0) + Math.abs(direction.y1)), bounds.width * 2, bounds.height * 2);\n\n    return new LinearGradient(parseColorStops(args, firstColorStopIndex, lineLength), direction);\n};\n\nvar parseRadialGradient = function parseRadialGradient(container, args, bounds) {\n    var m = args[0].match(RADIAL_SHAPE_DEFINITION);\n    var shape = m && (m[1] === 'circle' || // explicit shape specification\n    m[3] !== undefined && m[5] === undefined) // only one radius coordinate\n    ? RADIAL_GRADIENT_SHAPE.CIRCLE : RADIAL_GRADIENT_SHAPE.ELLIPSE;\n    var radius = {};\n    var center = {};\n\n    if (m) {\n        // Radius\n        if (m[3] !== undefined) {\n            radius.x = (0, _Length.calculateLengthFromValueWithUnit)(container, m[3], m[4]).getAbsoluteValue(bounds.width);\n        }\n\n        if (m[5] !== undefined) {\n            radius.y = (0, _Length.calculateLengthFromValueWithUnit)(container, m[5], m[6]).getAbsoluteValue(bounds.height);\n        }\n\n        // Position\n        if (m[7]) {\n            center.x = LENGTH_FOR_POSITION[m[7].toLowerCase()];\n        } else if (m[8] !== undefined) {\n            center.x = (0, _Length.calculateLengthFromValueWithUnit)(container, m[8], m[9]);\n        }\n\n        if (m[10]) {\n            center.y = LENGTH_FOR_POSITION[m[10].toLowerCase()];\n        } else if (m[11] !== undefined) {\n            center.y = (0, _Length.calculateLengthFromValueWithUnit)(container, m[11], m[12]);\n        }\n    }\n\n    var gradientCenter = {\n        x: center.x === undefined ? bounds.width / 2 : center.x.getAbsoluteValue(bounds.width),\n        y: center.y === undefined ? bounds.height / 2 : center.y.getAbsoluteValue(bounds.height)\n    };\n    var gradientRadius = calculateRadius(m && m[2] || 'farthest-corner', shape, gradientCenter, radius, bounds);\n\n    return new RadialGradient(parseColorStops(args, m ? 1 : 0, Math.min(gradientRadius.x, gradientRadius.y)), shape, gradientCenter, gradientRadius);\n};\n\nvar calculateGradientDirection = function calculateGradientDirection(radian, bounds) {\n    var width = bounds.width;\n    var height = bounds.height;\n    var HALF_WIDTH = width * 0.5;\n    var HALF_HEIGHT = height * 0.5;\n    var lineLength = Math.abs(width * Math.sin(radian)) + Math.abs(height * Math.cos(radian));\n    var HALF_LINE_LENGTH = lineLength / 2;\n\n    var x0 = HALF_WIDTH + Math.sin(radian) * HALF_LINE_LENGTH;\n    var y0 = HALF_HEIGHT - Math.cos(radian) * HALF_LINE_LENGTH;\n    var x1 = width - x0;\n    var y1 = height - y0;\n\n    return { x0: x0, x1: x1, y0: y0, y1: y1 };\n};\n\nvar parseTopRight = function parseTopRight(bounds) {\n    return Math.acos(bounds.width / 2 / ((0, _Util.distance)(bounds.width, bounds.height) / 2));\n};\n\nvar parseSideOrCorner = function parseSideOrCorner(side, bounds) {\n    switch (side) {\n        case 'bottom':\n        case 'to top':\n            return calculateGradientDirection(0, bounds);\n        case 'left':\n        case 'to right':\n            return calculateGradientDirection(Math.PI / 2, bounds);\n        case 'right':\n        case 'to left':\n            return calculateGradientDirection(3 * Math.PI / 2, bounds);\n        case 'top right':\n        case 'right top':\n        case 'to bottom left':\n        case 'to left bottom':\n            return calculateGradientDirection(Math.PI + parseTopRight(bounds), bounds);\n        case 'top left':\n        case 'left top':\n        case 'to bottom right':\n        case 'to right bottom':\n            return calculateGradientDirection(Math.PI - parseTopRight(bounds), bounds);\n        case 'bottom left':\n        case 'left bottom':\n        case 'to top right':\n        case 'to right top':\n            return calculateGradientDirection(parseTopRight(bounds), bounds);\n        case 'bottom right':\n        case 'right bottom':\n        case 'to top left':\n        case 'to left top':\n            return calculateGradientDirection(2 * Math.PI - parseTopRight(bounds), bounds);\n        case 'top':\n        case 'to bottom':\n        default:\n            return calculateGradientDirection(Math.PI, bounds);\n    }\n};\n\nvar parsePercentageAngle = function parsePercentageAngle(angle, bounds) {\n    var _angle$split$map = angle.split(' ').map(parseFloat),\n        _angle$split$map2 = _slicedToArray(_angle$split$map, 2),\n        left = _angle$split$map2[0],\n        top = _angle$split$map2[1];\n\n    var ratio = left / 100 * bounds.width / (top / 100 * bounds.height);\n\n    return calculateGradientDirection(Math.atan(isNaN(ratio) ? 1 : ratio) + Math.PI / 2, bounds);\n};\n\nvar findCorner = function findCorner(bounds, x, y, closest) {\n    var corners = [{ x: 0, y: 0 }, { x: 0, y: bounds.height }, { x: bounds.width, y: 0 }, { x: bounds.width, y: bounds.height }];\n\n    // $FlowFixMe\n    return corners.reduce(function (stat, corner) {\n        var d = (0, _Util.distance)(x - corner.x, y - corner.y);\n        if (closest ? d < stat.optimumDistance : d > stat.optimumDistance) {\n            return {\n                optimumCorner: corner,\n                optimumDistance: d\n            };\n        }\n\n        return stat;\n    }, {\n        optimumDistance: closest ? Infinity : -Infinity,\n        optimumCorner: null\n    }).optimumCorner;\n};\n\nvar calculateRadius = function calculateRadius(extent, shape, center, radius, bounds) {\n    var x = center.x;\n    var y = center.y;\n    var rx = 0;\n    var ry = 0;\n\n    switch (extent) {\n        case 'closest-side':\n            // The ending shape is sized so that that it exactly meets the side of the gradient box closest to the gradient’s center.\n            // If the shape is an ellipse, it exactly meets the closest side in each dimension.\n            if (shape === RADIAL_GRADIENT_SHAPE.CIRCLE) {\n                rx = ry = Math.min(Math.abs(x), Math.abs(x - bounds.width), Math.abs(y), Math.abs(y - bounds.height));\n            } else if (shape === RADIAL_GRADIENT_SHAPE.ELLIPSE) {\n                rx = Math.min(Math.abs(x), Math.abs(x - bounds.width));\n                ry = Math.min(Math.abs(y), Math.abs(y - bounds.height));\n            }\n            break;\n\n        case 'closest-corner':\n            // The ending shape is sized so that that it passes through the corner of the gradient box closest to the gradient’s center.\n            // If the shape is an ellipse, the ending shape is given the same aspect-ratio it would have if closest-side were specified.\n            if (shape === RADIAL_GRADIENT_SHAPE.CIRCLE) {\n                rx = ry = Math.min((0, _Util.distance)(x, y), (0, _Util.distance)(x, y - bounds.height), (0, _Util.distance)(x - bounds.width, y), (0, _Util.distance)(x - bounds.width, y - bounds.height));\n            } else if (shape === RADIAL_GRADIENT_SHAPE.ELLIPSE) {\n                // Compute the ratio ry/rx (which is to be the same as for \"closest-side\")\n                var c = Math.min(Math.abs(y), Math.abs(y - bounds.height)) / Math.min(Math.abs(x), Math.abs(x - bounds.width));\n                var corner = findCorner(bounds, x, y, true);\n                rx = (0, _Util.distance)(corner.x - x, (corner.y - y) / c);\n                ry = c * rx;\n            }\n            break;\n\n        case 'farthest-side':\n            // Same as closest-side, except the ending shape is sized based on the farthest side(s)\n            if (shape === RADIAL_GRADIENT_SHAPE.CIRCLE) {\n                rx = ry = Math.max(Math.abs(x), Math.abs(x - bounds.width), Math.abs(y), Math.abs(y - bounds.height));\n            } else if (shape === RADIAL_GRADIENT_SHAPE.ELLIPSE) {\n                rx = Math.max(Math.abs(x), Math.abs(x - bounds.width));\n                ry = Math.max(Math.abs(y), Math.abs(y - bounds.height));\n            }\n            break;\n\n        case 'farthest-corner':\n            // Same as closest-corner, except the ending shape is sized based on the farthest corner.\n            // If the shape is an ellipse, the ending shape is given the same aspect ratio it would have if farthest-side were specified.\n            if (shape === RADIAL_GRADIENT_SHAPE.CIRCLE) {\n                rx = ry = Math.max((0, _Util.distance)(x, y), (0, _Util.distance)(x, y - bounds.height), (0, _Util.distance)(x - bounds.width, y), (0, _Util.distance)(x - bounds.width, y - bounds.height));\n            } else if (shape === RADIAL_GRADIENT_SHAPE.ELLIPSE) {\n                // Compute the ratio ry/rx (which is to be the same as for \"farthest-side\")\n                var _c = Math.max(Math.abs(y), Math.abs(y - bounds.height)) / Math.max(Math.abs(x), Math.abs(x - bounds.width));\n                var _corner = findCorner(bounds, x, y, false);\n                rx = (0, _Util.distance)(_corner.x - x, (_corner.y - y) / _c);\n                ry = _c * rx;\n            }\n            break;\n\n        default:\n            // pixel or percentage values\n            rx = radius.x || 0;\n            ry = radius.y !== undefined ? radius.y : rx;\n            break;\n    }\n\n    return {\n        x: rx,\n        y: ry\n    };\n};\n\nvar transformWebkitRadialGradientArgs = exports.transformWebkitRadialGradientArgs = function transformWebkitRadialGradientArgs(args) {\n    var shape = '';\n    var radius = '';\n    var extent = '';\n    var position = '';\n    var idx = 0;\n\n    var POSITION = /^(left|center|right|\\d+(?:px|r?em|%)?)(?:\\s+(top|center|bottom|\\d+(?:px|r?em|%)?))?$/i;\n    var SHAPE_AND_EXTENT = /^(circle|ellipse)?\\s*(closest-side|closest-corner|farthest-side|farthest-corner|contain|cover)?$/i;\n    var RADIUS = /^\\d+(px|r?em|%)?(?:\\s+\\d+(px|r?em|%)?)?$/i;\n\n    var matchStartPosition = args[idx].match(POSITION);\n    if (matchStartPosition) {\n        idx++;\n    }\n\n    var matchShapeExtent = args[idx].match(SHAPE_AND_EXTENT);\n    if (matchShapeExtent) {\n        shape = matchShapeExtent[1] || '';\n        extent = matchShapeExtent[2] || '';\n        if (extent === 'contain') {\n            extent = 'closest-side';\n        } else if (extent === 'cover') {\n            extent = 'farthest-corner';\n        }\n        idx++;\n    }\n\n    var matchStartRadius = args[idx].match(RADIUS);\n    if (matchStartRadius) {\n        idx++;\n    }\n\n    var matchEndPosition = args[idx].match(POSITION);\n    if (matchEndPosition) {\n        idx++;\n    }\n\n    var matchEndRadius = args[idx].match(RADIUS);\n    if (matchEndRadius) {\n        idx++;\n    }\n\n    var matchPosition = matchEndPosition || matchStartPosition;\n    if (matchPosition && matchPosition[1]) {\n        position = matchPosition[1] + (/^\\d+$/.test(matchPosition[1]) ? 'px' : '');\n        if (matchPosition[2]) {\n            position += ' ' + matchPosition[2] + (/^\\d+$/.test(matchPosition[2]) ? 'px' : '');\n        }\n    }\n\n    var matchRadius = matchEndRadius || matchStartRadius;\n    if (matchRadius) {\n        radius = matchRadius[0];\n        if (!matchRadius[1]) {\n            radius += 'px';\n        }\n    }\n\n    if (position && !shape && !radius && !extent) {\n        radius = position;\n        position = '';\n    }\n\n    if (position) {\n        position = 'at ' + position;\n    }\n\n    return [[shape, extent, radius, position].filter(function (s) {\n        return !!s;\n    }).join(' ')].concat(args.slice(idx));\n};\n\nvar transformObsoleteColorStops = function transformObsoleteColorStops(args) {\n    return args.map(function (color) {\n        return color.match(FROM_TO_COLORSTOP);\n    })\n    // $FlowFixMe\n    .map(function (v, index) {\n        if (!v) {\n            return args[index];\n        }\n\n        switch (v[1]) {\n            case 'from':\n                return v[4] + ' 0%';\n            case 'to':\n                return v[4] + ' 100%';\n            case 'color-stop':\n                if (v[3] === '%') {\n                    return v[4] + ' ' + v[2];\n                }\n                return v[4] + ' ' + parseFloat(v[2]) * 100 + '%';\n        }\n    });\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.parseBorderRadius = undefined;\n\nvar _slicedToArray = function () { function sliceIterator(arr, i) { var _arr = []; var _n = true; var _d = false; var _e = undefined; try { for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"]) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; } return function (arr, i) { if (Array.isArray(arr)) { return arr; } else if (Symbol.iterator in Object(arr)) { return sliceIterator(arr, i); } else { throw new TypeError(\"Invalid attempt to destructure non-iterable instance\"); } }; }();\n\nvar _Length = require('../Length');\n\nvar _Length2 = _interopRequireDefault(_Length);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar SIDES = ['top-left', 'top-right', 'bottom-right', 'bottom-left'];\n\nvar parseBorderRadius = exports.parseBorderRadius = function parseBorderRadius(style) {\n    return SIDES.map(function (side) {\n        var value = style.getPropertyValue('border-' + side + '-radius');\n\n        var _value$split$map = value.split(' ').map(_Length2.default.create),\n            _value$split$map2 = _slicedToArray(_value$split$map, 2),\n            horizontal = _value$split$map2[0],\n            vertical = _value$split$map2[1];\n\n        return typeof vertical === 'undefined' ? [horizontal, horizontal] : [horizontal, vertical];\n    });\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nvar PATH = exports.PATH = {\n    VECTOR: 0,\n    BEZIER_CURVE: 1,\n    CIRCLE: 2\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nvar contains = exports.contains = function contains(bit, value) {\n    return (bit & value) !== 0;\n};\n\nvar distance = exports.distance = function distance(a, b) {\n    return Math.sqrt(a * a + b * b);\n};\n\nvar copyCSSStyles = exports.copyCSSStyles = function copyCSSStyles(style, target) {\n    // Edge does not provide value for cssText\n    for (var i = style.length - 1; i >= 0; i--) {\n        var property = style.item(i);\n        // Safari shows pseudoelements if content is set\n        if (property !== 'content') {\n            target.style.setProperty(property, style.getPropertyValue(property));\n        }\n    }\n    return target;\n};\n\nvar SMALL_IMAGE = exports.SMALL_IMAGE = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _Util = require('./Util');\n\nObject.defineProperty(exports, 'toCodePoints', {\n  enumerable: true,\n  get: function get() {\n    return _Util.toCodePoints;\n  }\n});\nObject.defineProperty(exports, 'fromCodePoint', {\n  enumerable: true,\n  get: function get() {\n    return _Util.fromCodePoint;\n  }\n});\n\nvar _LineBreak = require('./LineBreak');\n\nObject.defineProperty(exports, 'LineBreaker', {\n  enumerable: true,\n  get: function get() {\n    return _LineBreak.LineBreaker;\n  }\n});", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.renderElement = undefined;\n\nvar _slicedToArray = function () { function sliceIterator(arr, i) { var _arr = []; var _n = true; var _d = false; var _e = undefined; try { for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"]) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; } return function (arr, i) { if (Array.isArray(arr)) { return arr; } else if (Symbol.iterator in Object(arr)) { return sliceIterator(arr, i); } else { throw new TypeError(\"Invalid attempt to destructure non-iterable instance\"); } }; }();\n\nvar _Logger = require('./Logger');\n\nvar _Logger2 = _interopRequireDefault(_Logger);\n\nvar _NodeParser = require('./NodeParser');\n\nvar _Renderer = require('./Renderer');\n\nvar _Renderer2 = _interopRequireDefault(_Renderer);\n\nvar _ForeignObjectRenderer = require('./renderer/ForeignObjectRenderer');\n\nvar _ForeignObjectRenderer2 = _interopRequireDefault(_ForeignObjectRenderer);\n\nvar _Feature = require('./Feature');\n\nvar _Feature2 = _interopRequireDefault(_Feature);\n\nvar _Bounds = require('./Bounds');\n\nvar _Clone = require('./Clone');\n\nvar _Font = require('./Font');\n\nvar _Color = require('./Color');\n\nvar _Color2 = _interopRequireDefault(_Color);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar renderElement = exports.renderElement = function renderElement(element, options, logger) {\n    var ownerDocument = element.ownerDocument;\n\n    var windowBounds = new _Bounds.Bounds(options.scrollX, options.scrollY, options.windowWidth, options.windowHeight);\n\n    // http://www.w3.org/TR/css3-background/#special-backgrounds\n    var documentBackgroundColor = ownerDocument.documentElement ? new _Color2.default(getComputedStyle(ownerDocument.documentElement).backgroundColor) : _Color.TRANSPARENT;\n    var bodyBackgroundColor = ownerDocument.body ? new _Color2.default(getComputedStyle(ownerDocument.body).backgroundColor) : _Color.TRANSPARENT;\n\n    var backgroundColor = element === ownerDocument.documentElement ? documentBackgroundColor.isTransparent() ? bodyBackgroundColor.isTransparent() ? options.backgroundColor ? new _Color2.default(options.backgroundColor) : null : bodyBackgroundColor : documentBackgroundColor : options.backgroundColor ? new _Color2.default(options.backgroundColor) : null;\n\n    return (options.foreignObjectRendering ? // $FlowFixMe\n    _Feature2.default.SUPPORT_FOREIGNOBJECT_DRAWING : Promise.resolve(false)).then(function (supportForeignObject) {\n        return supportForeignObject ? function (cloner) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.log('Document cloned, using foreignObject rendering');\n            }\n\n            return cloner.inlineFonts(ownerDocument).then(function () {\n                return cloner.resourceLoader.ready();\n            }).then(function () {\n                var renderer = new _ForeignObjectRenderer2.default(cloner.documentElement);\n                return renderer.render({\n                    backgroundColor: backgroundColor,\n                    logger: logger,\n                    scale: options.scale,\n                    x: options.x,\n                    y: options.y,\n                    width: options.width,\n                    height: options.height,\n                    windowWidth: options.windowWidth,\n                    windowHeight: options.windowHeight,\n                    scrollX: options.scrollX,\n                    scrollY: options.scrollY\n                });\n            });\n        }(new _Clone.DocumentCloner(element, options, logger, true, renderElement)) : (0, _Clone.cloneWindow)(ownerDocument, windowBounds, element, options, logger, renderElement).then(function (_ref) {\n            var _ref2 = _slicedToArray(_ref, 3),\n                container = _ref2[0],\n                clonedElement = _ref2[1],\n                resourceLoader = _ref2[2];\n\n            if (process.env.NODE_ENV !== 'production') {\n                logger.log('Document cloned, using computed rendering');\n            }\n\n            var stack = (0, _NodeParser.NodeParser)(clonedElement, resourceLoader, logger);\n            var clonedDocument = clonedElement.ownerDocument;\n\n            if (backgroundColor === stack.container.style.background.backgroundColor) {\n                stack.container.style.background.backgroundColor = _Color.TRANSPARENT;\n            }\n\n            return resourceLoader.ready().then(function (imageStore) {\n                var fontMetrics = new _Font.FontMetrics(clonedDocument);\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.log('Starting renderer');\n                }\n\n                var renderOptions = {\n                    backgroundColor: backgroundColor,\n                    fontMetrics: fontMetrics,\n                    imageStore: imageStore,\n                    logger: logger,\n                    scale: options.scale,\n                    x: options.x,\n                    y: options.y,\n                    width: options.width,\n                    height: options.height\n                };\n\n                if (Array.isArray(options.target)) {\n                    return Promise.all(options.target.map(function (target) {\n                        var renderer = new _Renderer2.default(target, renderOptions);\n                        return renderer.render(stack);\n                    }));\n                } else {\n                    var renderer = new _Renderer2.default(options.target, renderOptions);\n                    var canvas = renderer.render(stack);\n                    if (options.removeContainer === true) {\n                        if (container.parentNode) {\n                            container.parentNode.removeChild(container);\n                        } else if (process.env.NODE_ENV !== 'production') {\n                            logger.log('Cannot detach cloned iframe as it is not in the DOM anymore');\n                        }\n                    }\n\n                    return canvas;\n                }\n            });\n        });\n    });\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _Color = require('./Color');\n\nvar _Color2 = _interopRequireDefault(_Color);\n\nvar _Util = require('./Util');\n\nvar _background = require('./parsing/background');\n\nvar _border = require('./parsing/border');\n\nvar _borderRadius = require('./parsing/borderRadius');\n\nvar _display = require('./parsing/display');\n\nvar _float = require('./parsing/float');\n\nvar _font = require('./parsing/font');\n\nvar _letterSpacing = require('./parsing/letterSpacing');\n\nvar _lineBreak = require('./parsing/lineBreak');\n\nvar _listStyle = require('./parsing/listStyle');\n\nvar _margin = require('./parsing/margin');\n\nvar _overflow = require('./parsing/overflow');\n\nvar _overflowWrap = require('./parsing/overflowWrap');\n\nvar _padding = require('./parsing/padding');\n\nvar _position = require('./parsing/position');\n\nvar _textDecoration = require('./parsing/textDecoration');\n\nvar _textShadow = require('./parsing/textShadow');\n\nvar _textTransform = require('./parsing/textTransform');\n\nvar _transform = require('./parsing/transform');\n\nvar _visibility = require('./parsing/visibility');\n\nvar _wordBreak = require('./parsing/word-break');\n\nvar _zIndex = require('./parsing/zIndex');\n\nvar _Bounds = require('./Bounds');\n\nvar _Input = require('./Input');\n\nvar _ListItem = require('./ListItem');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar INPUT_TAGS = ['INPUT', 'TEXTAREA', 'SELECT'];\n\nvar NodeContainer = function () {\n    function NodeContainer(node, parent, resourceLoader, index) {\n        var _this = this;\n\n        _classCallCheck(this, NodeContainer);\n\n        this.parent = parent;\n        this.tagName = node.tagName;\n        this.index = index;\n        this.childNodes = [];\n        this.listItems = [];\n        if (typeof node.start === 'number') {\n            this.listStart = node.start;\n        }\n        var defaultView = node.ownerDocument.defaultView;\n        var scrollX = defaultView.pageXOffset;\n        var scrollY = defaultView.pageYOffset;\n        var style = defaultView.getComputedStyle(node, null);\n        var display = (0, _display.parseDisplay)(style.display);\n\n        var IS_INPUT = node.type === 'radio' || node.type === 'checkbox';\n\n        var position = (0, _position.parsePosition)(style.position);\n\n        this.style = {\n            background: IS_INPUT ? _Input.INPUT_BACKGROUND : (0, _background.parseBackground)(style, resourceLoader),\n            border: IS_INPUT ? _Input.INPUT_BORDERS : (0, _border.parseBorder)(style),\n            borderRadius: (node instanceof defaultView.HTMLInputElement || node instanceof HTMLInputElement) && IS_INPUT ? (0, _Input.getInputBorderRadius)(node) : (0, _borderRadius.parseBorderRadius)(style),\n            color: IS_INPUT ? _Input.INPUT_COLOR : new _Color2.default(style.color),\n            display: display,\n            float: (0, _float.parseCSSFloat)(style.float),\n            font: (0, _font.parseFont)(style),\n            letterSpacing: (0, _letterSpacing.parseLetterSpacing)(style.letterSpacing),\n            listStyle: display === _display.DISPLAY.LIST_ITEM ? (0, _listStyle.parseListStyle)(style) : null,\n            lineBreak: (0, _lineBreak.parseLineBreak)(style.lineBreak),\n            margin: (0, _margin.parseMargin)(style),\n            opacity: parseFloat(style.opacity),\n            overflow: INPUT_TAGS.indexOf(node.tagName) === -1 ? (0, _overflow.parseOverflow)(style.overflow) : _overflow.OVERFLOW.HIDDEN,\n            overflowWrap: (0, _overflowWrap.parseOverflowWrap)(style.overflowWrap ? style.overflowWrap : style.wordWrap),\n            padding: (0, _padding.parsePadding)(style),\n            position: position,\n            textDecoration: (0, _textDecoration.parseTextDecoration)(style),\n            textShadow: (0, _textShadow.parseTextShadow)(style.textShadow),\n            textTransform: (0, _textTransform.parseTextTransform)(style.textTransform),\n            transform: (0, _transform.parseTransform)(style),\n            visibility: (0, _visibility.parseVisibility)(style.visibility),\n            wordBreak: (0, _wordBreak.parseWordBreak)(style.wordBreak),\n            zIndex: (0, _zIndex.parseZIndex)(position !== _position.POSITION.STATIC ? style.zIndex : 'auto')\n        };\n\n        if (this.isTransformed()) {\n            // getBoundingClientRect provides values post-transform, we want them without the transformation\n            node.style.transform = 'matrix(1,0,0,1,0,0)';\n        }\n\n        if (display === _display.DISPLAY.LIST_ITEM) {\n            var listOwner = (0, _ListItem.getListOwner)(this);\n            if (listOwner) {\n                var listIndex = listOwner.listItems.length;\n                listOwner.listItems.push(this);\n                this.listIndex = node.hasAttribute('value') && typeof node.value === 'number' ? node.value : listIndex === 0 ? typeof listOwner.listStart === 'number' ? listOwner.listStart : 1 : listOwner.listItems[listIndex - 1].listIndex + 1;\n            }\n        }\n\n        // TODO move bound retrieval for all nodes to a later stage?\n        if (node.tagName === 'IMG') {\n            node.addEventListener('load', function () {\n                _this.bounds = (0, _Bounds.parseBounds)(node, scrollX, scrollY);\n                _this.curvedBounds = (0, _Bounds.parseBoundCurves)(_this.bounds, _this.style.border, _this.style.borderRadius);\n            });\n        }\n        this.image = getImage(node, resourceLoader);\n        this.bounds = IS_INPUT ? (0, _Input.reformatInputBounds)((0, _Bounds.parseBounds)(node, scrollX, scrollY)) : (0, _Bounds.parseBounds)(node, scrollX, scrollY);\n        this.curvedBounds = (0, _Bounds.parseBoundCurves)(this.bounds, this.style.border, this.style.borderRadius);\n\n        if (process.env.NODE_ENV !== 'production') {\n            this.name = '' + node.tagName.toLowerCase() + (node.id ? '#' + node.id : '') + node.className.toString().split(' ').map(function (s) {\n                return s.length ? '.' + s : '';\n            }).join('');\n        }\n    }\n\n    _createClass(NodeContainer, [{\n        key: 'getClipPaths',\n        value: function getClipPaths() {\n            var parentClips = this.parent ? this.parent.getClipPaths() : [];\n            var isClipped = this.style.overflow !== _overflow.OVERFLOW.VISIBLE;\n\n            return isClipped ? parentClips.concat([(0, _Bounds.calculatePaddingBoxPath)(this.curvedBounds)]) : parentClips;\n        }\n    }, {\n        key: 'isInFlow',\n        value: function isInFlow() {\n            return this.isRootElement() && !this.isFloating() && !this.isAbsolutelyPositioned();\n        }\n    }, {\n        key: 'isVisible',\n        value: function isVisible() {\n            return !(0, _Util.contains)(this.style.display, _display.DISPLAY.NONE) && this.style.opacity > 0 && this.style.visibility === _visibility.VISIBILITY.VISIBLE;\n        }\n    }, {\n        key: 'isAbsolutelyPositioned',\n        value: function isAbsolutelyPositioned() {\n            return this.style.position !== _position.POSITION.STATIC && this.style.position !== _position.POSITION.RELATIVE;\n        }\n    }, {\n        key: 'isPositioned',\n        value: function isPositioned() {\n            return this.style.position !== _position.POSITION.STATIC;\n        }\n    }, {\n        key: 'isFloating',\n        value: function isFloating() {\n            return this.style.float !== _float.FLOAT.NONE;\n        }\n    }, {\n        key: 'isRootElement',\n        value: function isRootElement() {\n            return this.parent === null;\n        }\n    }, {\n        key: 'isTransformed',\n        value: function isTransformed() {\n            return this.style.transform !== null;\n        }\n    }, {\n        key: 'isPositionedWithZIndex',\n        value: function isPositionedWithZIndex() {\n            return this.isPositioned() && !this.style.zIndex.auto;\n        }\n    }, {\n        key: 'isInlineLevel',\n        value: function isInlineLevel() {\n            return (0, _Util.contains)(this.style.display, _display.DISPLAY.INLINE) || (0, _Util.contains)(this.style.display, _display.DISPLAY.INLINE_BLOCK) || (0, _Util.contains)(this.style.display, _display.DISPLAY.INLINE_FLEX) || (0, _Util.contains)(this.style.display, _display.DISPLAY.INLINE_GRID) || (0, _Util.contains)(this.style.display, _display.DISPLAY.INLINE_LIST_ITEM) || (0, _Util.contains)(this.style.display, _display.DISPLAY.INLINE_TABLE);\n        }\n    }, {\n        key: 'isInlineBlockOrInlineTable',\n        value: function isInlineBlockOrInlineTable() {\n            return (0, _Util.contains)(this.style.display, _display.DISPLAY.INLINE_BLOCK) || (0, _Util.contains)(this.style.display, _display.DISPLAY.INLINE_TABLE);\n        }\n    }]);\n\n    return NodeContainer;\n}();\n\nexports.default = NodeContainer;\n\n\nvar getImage = function getImage(node, resourceLoader) {\n    if (node instanceof node.ownerDocument.defaultView.SVGSVGElement || node instanceof SVGSVGElement) {\n        var bounds = (0, _Bounds.parseBounds)(node, 0, 0);\n        node.setAttribute('width', bounds.width + 'px');\n        node.setAttribute('height', bounds.height + 'px');\n        var s = new XMLSerializer();\n        return resourceLoader.loadImage('data:image/svg+xml,' + encodeURIComponent(s.serializeToString(node)));\n    }\n    switch (node.tagName) {\n        case 'IMG':\n            // $FlowFixMe\n            var img = node;\n            return resourceLoader.loadImage(img.currentSrc || img.src);\n        case 'CANVAS':\n            // $FlowFixMe\n            var canvas = node;\n            return resourceLoader.loadCanvas(canvas);\n        case 'IFRAME':\n            var iframeKey = node.getAttribute('data-html2canvas-internal-iframe-key');\n            if (iframeKey) {\n                return iframeKey;\n            }\n            break;\n    }\n\n    return null;\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nvar WORD_BREAK = exports.WORD_BREAK = {\n    NORMAL: 'normal',\n    BREAK_ALL: 'break-all',\n    KEEP_ALL: 'keep-all'\n};\n\nvar parseWordBreak = exports.parseWordBreak = function parseWordBreak(wordBreak) {\n    switch (wordBreak) {\n        case 'break-all':\n            return WORD_BREAK.BREAK_ALL;\n        case 'keep-all':\n            return WORD_BREAK.KEEP_ALL;\n        case 'normal':\n        default:\n            return WORD_BREAK.NORMAL;\n    }\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nvar POSITION = exports.POSITION = {\n    STATIC: 0,\n    RELATIVE: 1,\n    ABSOLUTE: 2,\n    FIXED: 3,\n    STICKY: 4\n};\n\nvar parsePosition = exports.parsePosition = function parsePosition(position) {\n    switch (position) {\n        case 'relative':\n            return POSITION.RELATIVE;\n        case 'absolute':\n            return POSITION.ABSOLUTE;\n        case 'fixed':\n            return POSITION.FIXED;\n        case 'sticky':\n            return POSITION.STICKY;\n    }\n\n    return POSITION.STATIC;\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.parseMargin = undefined;\n\nvar _Length = require('../Length');\n\nvar _Length2 = _interopRequireDefault(_Length);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar SIDES = ['top', 'right', 'bottom', 'left'];\n\nvar parseMargin = exports.parseMargin = function parseMargin(style) {\n    return SIDES.map(function (side) {\n        return new _Length2.default(style.getPropertyValue('margin-' + side));\n    });\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\nvar _Path = require('./Path');\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar Vector = function Vector(x, y) {\n    _classCallCheck(this, Vector);\n\n    this.type = _Path.PATH.VECTOR;\n    this.x = x;\n    this.y = y;\n    if (process.env.NODE_ENV !== 'production') {\n        if (isNaN(x)) {\n            console.error('Invalid x value given for Vector');\n        }\n        if (isNaN(y)) {\n            console.error('Invalid y value given for Vector');\n        }\n    }\n};\n\nexports.default = Vector;", "'use strict';\n\nvar _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; };\n\nvar _CanvasRenderer = require('./renderer/CanvasRenderer');\n\nvar _CanvasRenderer2 = _interopRequireDefault(_CanvasRenderer);\n\nvar _Logger = require('./Logger');\n\nvar _Logger2 = _interopRequireDefault(_Logger);\n\nvar _Window = require('./Window');\n\nvar _Bounds = require('./Bounds');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar html2canvas = function html2canvas(element, conf) {\n    var config = conf || {};\n    var logger = new _Logger2.default(typeof config.logging === 'boolean' ? config.logging : true);\n    logger.log('html2canvas ' + \"$npm_package_version\");\n\n    if (process.env.NODE_ENV !== 'production' && typeof config.onrendered === 'function') {\n        logger.error('onrendered option is deprecated, html2canvas returns a Promise with the canvas as the value');\n    }\n\n    var ownerDocument = element.ownerDocument;\n    if (!ownerDocument) {\n        return Promise.reject('Provided element is not within a Document');\n    }\n    var defaultView = ownerDocument.defaultView;\n\n    var scrollX = defaultView.pageXOffset;\n    var scrollY = defaultView.pageYOffset;\n\n    var isDocument = element.tagName === 'HTML' || element.tagName === 'BODY';\n\n    var _ref = isDocument ? (0, _Bounds.parseDocumentSize)(ownerDocument) : (0, _Bounds.parseBounds)(element, scrollX, scrollY),\n        width = _ref.width,\n        height = _ref.height,\n        left = _ref.left,\n        top = _ref.top;\n\n    var defaultOptions = {\n        async: true,\n        allowTaint: false,\n        backgroundColor: '#ffffff',\n        imageTimeout: 15000,\n        logging: true,\n        proxy: null,\n        removeContainer: true,\n        foreignObjectRendering: false,\n        scale: defaultView.devicePixelRatio || 1,\n        target: new _CanvasRenderer2.default(config.canvas),\n        useCORS: false,\n        x: left,\n        y: top,\n        width: Math.ceil(width),\n        height: Math.ceil(height),\n        windowWidth: defaultView.innerWidth,\n        windowHeight: defaultView.innerHeight,\n        scrollX: defaultView.pageXOffset,\n        scrollY: defaultView.pageYOffset\n    };\n\n    var result = (0, _Window.renderElement)(element, _extends({}, defaultOptions, config), logger);\n\n    if (process.env.NODE_ENV !== 'production') {\n        return result.catch(function (e) {\n            logger.error(e);\n            throw e;\n        });\n    }\n    return result;\n};\n\nhtml2canvas.CanvasRenderer = _CanvasRenderer2.default;\n\nmodule.exports = html2canvas;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _Path = require('./Path');\n\nvar _Vector = require('./Vector');\n\nvar _Vector2 = _interopRequireDefault(_Vector);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar lerp = function lerp(a, b, t) {\n    return new _Vector2.default(a.x + (b.x - a.x) * t, a.y + (b.y - a.y) * t);\n};\n\nvar BezierCurve = function () {\n    function BezierCurve(start, startControl, endControl, end) {\n        _classCallCheck(this, BezierCurve);\n\n        this.type = _Path.PATH.BEZIER_CURVE;\n        this.start = start;\n        this.startControl = startControl;\n        this.endControl = endControl;\n        this.end = end;\n    }\n\n    _createClass(BezierCurve, [{\n        key: 'subdivide',\n        value: function subdivide(t, firstHalf) {\n            var ab = lerp(this.start, this.startControl, t);\n            var bc = lerp(this.startControl, this.endControl, t);\n            var cd = lerp(this.endControl, this.end, t);\n            var abbc = lerp(ab, bc, t);\n            var bccd = lerp(bc, cd, t);\n            var dest = lerp(abbc, bccd, t);\n            return firstHalf ? new BezierCurve(this.start, ab, abbc, dest) : new BezierCurve(dest, bccd, cd, this.end);\n        }\n    }, {\n        key: 'reverse',\n        value: function reverse() {\n            return new BezierCurve(this.end, this.endControl, this.startControl, this.start);\n        }\n    }]);\n\n    return BezierCurve;\n}();\n\nexports.default = BezierCurve;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _NodeContainer = require('./NodeContainer');\n\nvar _NodeContainer2 = _interopRequireDefault(_NodeContainer);\n\nvar _position = require('./parsing/position');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar StackingContext = function () {\n    function StackingContext(container, parent, treatAsRealStackingContext) {\n        _classCallCheck(this, StackingContext);\n\n        this.container = container;\n        this.parent = parent;\n        this.contexts = [];\n        this.children = [];\n        this.treatAsRealStackingContext = treatAsRealStackingContext;\n    }\n\n    _createClass(StackingContext, [{\n        key: 'getOpacity',\n        value: function getOpacity() {\n            return this.parent ? this.container.style.opacity * this.parent.getOpacity() : this.container.style.opacity;\n        }\n    }, {\n        key: 'getRealParentStackingContext',\n        value: function getRealParentStackingContext() {\n            return !this.parent || this.treatAsRealStackingContext ? this : this.parent.getRealParentStackingContext();\n        }\n    }]);\n\n    return StackingContext;\n}();\n\nexports.default = StackingContext;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nvar LINE_BREAK = exports.LINE_BREAK = {\n    NORMAL: 'normal',\n    STRICT: 'strict'\n};\n\nvar parseLineBreak = exports.parseLineBreak = function parseLineBreak(wordBreak) {\n    switch (wordBreak) {\n        case 'strict':\n            return LINE_BREAK.STRICT;\n        case 'normal':\n        default:\n            return LINE_BREAK.NORMAL;\n    }\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.parseBackgroundImage = exports.parseBackground = exports.calculateBackgroundRepeatPath = exports.calculateBackgroundPosition = exports.calculateBackgroungPositioningArea = exports.calculateBackgroungPaintingArea = exports.calculateGradientBackgroundSize = exports.calculateBackgroundSize = exports.BACKGROUND_ORIGIN = exports.BACKGROUND_CLIP = exports.BACKGROUND_SIZE = exports.BACKGROUND_REPEAT = undefined;\n\nvar _Color = require('../Color');\n\nvar _Color2 = _interopRequireDefault(_Color);\n\nvar _Length = require('../Length');\n\nvar _Length2 = _interopRequireDefault(_Length);\n\nvar _Size = require('../drawing/Size');\n\nvar _Size2 = _interopRequireDefault(_Size);\n\nvar _Vector = require('../drawing/Vector');\n\nvar _Vector2 = _interopRequireDefault(_Vector);\n\nvar _Bounds = require('../Bounds');\n\nvar _padding = require('./padding');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar BACKGROUND_REPEAT = exports.BACKGROUND_REPEAT = {\n    REPEAT: 0,\n    NO_REPEAT: 1,\n    REPEAT_X: 2,\n    REPEAT_Y: 3\n};\n\nvar BACKGROUND_SIZE = exports.BACKGROUND_SIZE = {\n    AUTO: 0,\n    CONTAIN: 1,\n    COVER: 2,\n    LENGTH: 3\n};\n\nvar BACKGROUND_CLIP = exports.BACKGROUND_CLIP = {\n    BORDER_BOX: 0,\n    PADDING_BOX: 1,\n    CONTENT_BOX: 2\n};\n\nvar BACKGROUND_ORIGIN = exports.BACKGROUND_ORIGIN = BACKGROUND_CLIP;\n\nvar AUTO = 'auto';\n\nvar BackgroundSize = function BackgroundSize(size) {\n    _classCallCheck(this, BackgroundSize);\n\n    switch (size) {\n        case 'contain':\n            this.size = BACKGROUND_SIZE.CONTAIN;\n            break;\n        case 'cover':\n            this.size = BACKGROUND_SIZE.COVER;\n            break;\n        case 'auto':\n            this.size = BACKGROUND_SIZE.AUTO;\n            break;\n        default:\n            this.value = new _Length2.default(size);\n    }\n};\n\nvar calculateBackgroundSize = exports.calculateBackgroundSize = function calculateBackgroundSize(backgroundImage, image, bounds) {\n    var width = 0;\n    var height = 0;\n    var size = backgroundImage.size;\n    if (size[0].size === BACKGROUND_SIZE.CONTAIN || size[0].size === BACKGROUND_SIZE.COVER) {\n        var targetRatio = bounds.width / bounds.height;\n        var currentRatio = image.width / image.height;\n        return targetRatio < currentRatio !== (size[0].size === BACKGROUND_SIZE.COVER) ? new _Size2.default(bounds.width, bounds.width / currentRatio) : new _Size2.default(bounds.height * currentRatio, bounds.height);\n    }\n\n    if (size[0].value) {\n        width = size[0].value.getAbsoluteValue(bounds.width);\n    }\n\n    if (size[0].size === BACKGROUND_SIZE.AUTO && size[1].size === BACKGROUND_SIZE.AUTO) {\n        height = image.height;\n    } else if (size[1].size === BACKGROUND_SIZE.AUTO) {\n        height = width / image.width * image.height;\n    } else if (size[1].value) {\n        height = size[1].value.getAbsoluteValue(bounds.height);\n    }\n\n    if (size[0].size === BACKGROUND_SIZE.AUTO) {\n        width = height / image.height * image.width;\n    }\n\n    return new _Size2.default(width, height);\n};\n\nvar calculateGradientBackgroundSize = exports.calculateGradientBackgroundSize = function calculateGradientBackgroundSize(backgroundImage, bounds) {\n    var size = backgroundImage.size;\n    var width = size[0].value ? size[0].value.getAbsoluteValue(bounds.width) : bounds.width;\n    var height = size[1].value ? size[1].value.getAbsoluteValue(bounds.height) : size[0].value ? width : bounds.height;\n\n    return new _Size2.default(width, height);\n};\n\nvar AUTO_SIZE = new BackgroundSize(AUTO);\n\nvar calculateBackgroungPaintingArea = exports.calculateBackgroungPaintingArea = function calculateBackgroungPaintingArea(curves, clip) {\n    switch (clip) {\n        case BACKGROUND_CLIP.BORDER_BOX:\n            return (0, _Bounds.calculateBorderBoxPath)(curves);\n        case BACKGROUND_CLIP.PADDING_BOX:\n        default:\n            return (0, _Bounds.calculatePaddingBoxPath)(curves);\n    }\n};\n\nvar calculateBackgroungPositioningArea = exports.calculateBackgroungPositioningArea = function calculateBackgroungPositioningArea(backgroundOrigin, bounds, padding, border) {\n    var paddingBox = (0, _Bounds.calculatePaddingBox)(bounds, border);\n\n    switch (backgroundOrigin) {\n        case BACKGROUND_ORIGIN.BORDER_BOX:\n            return bounds;\n        case BACKGROUND_ORIGIN.CONTENT_BOX:\n            var paddingLeft = padding[_padding.PADDING_SIDES.LEFT].getAbsoluteValue(bounds.width);\n            var paddingRight = padding[_padding.PADDING_SIDES.RIGHT].getAbsoluteValue(bounds.width);\n            var paddingTop = padding[_padding.PADDING_SIDES.TOP].getAbsoluteValue(bounds.width);\n            var paddingBottom = padding[_padding.PADDING_SIDES.BOTTOM].getAbsoluteValue(bounds.width);\n            return new _Bounds.Bounds(paddingBox.left + paddingLeft, paddingBox.top + paddingTop, paddingBox.width - paddingLeft - paddingRight, paddingBox.height - paddingTop - paddingBottom);\n        case BACKGROUND_ORIGIN.PADDING_BOX:\n        default:\n            return paddingBox;\n    }\n};\n\nvar calculateBackgroundPosition = exports.calculateBackgroundPosition = function calculateBackgroundPosition(position, size, bounds) {\n    return new _Vector2.default(position[0].getAbsoluteValue(bounds.width - size.width), position[1].getAbsoluteValue(bounds.height - size.height));\n};\n\nvar calculateBackgroundRepeatPath = exports.calculateBackgroundRepeatPath = function calculateBackgroundRepeatPath(background, position, size, backgroundPositioningArea, bounds) {\n    var repeat = background.repeat;\n    switch (repeat) {\n        case BACKGROUND_REPEAT.REPEAT_X:\n            return [new _Vector2.default(Math.round(bounds.left), Math.round(backgroundPositioningArea.top + position.y)), new _Vector2.default(Math.round(bounds.left + bounds.width), Math.round(backgroundPositioningArea.top + position.y)), new _Vector2.default(Math.round(bounds.left + bounds.width), Math.round(size.height + backgroundPositioningArea.top + position.y)), new _Vector2.default(Math.round(bounds.left), Math.round(size.height + backgroundPositioningArea.top + position.y))];\n        case BACKGROUND_REPEAT.REPEAT_Y:\n            return [new _Vector2.default(Math.round(backgroundPositioningArea.left + position.x), Math.round(bounds.top)), new _Vector2.default(Math.round(backgroundPositioningArea.left + position.x + size.width), Math.round(bounds.top)), new _Vector2.default(Math.round(backgroundPositioningArea.left + position.x + size.width), Math.round(bounds.height + bounds.top)), new _Vector2.default(Math.round(backgroundPositioningArea.left + position.x), Math.round(bounds.height + bounds.top))];\n        case BACKGROUND_REPEAT.NO_REPEAT:\n            return [new _Vector2.default(Math.round(backgroundPositioningArea.left + position.x), Math.round(backgroundPositioningArea.top + position.y)), new _Vector2.default(Math.round(backgroundPositioningArea.left + position.x + size.width), Math.round(backgroundPositioningArea.top + position.y)), new _Vector2.default(Math.round(backgroundPositioningArea.left + position.x + size.width), Math.round(backgroundPositioningArea.top + position.y + size.height)), new _Vector2.default(Math.round(backgroundPositioningArea.left + position.x), Math.round(backgroundPositioningArea.top + position.y + size.height))];\n        default:\n            return [new _Vector2.default(Math.round(bounds.left), Math.round(bounds.top)), new _Vector2.default(Math.round(bounds.left + bounds.width), Math.round(bounds.top)), new _Vector2.default(Math.round(bounds.left + bounds.width), Math.round(bounds.height + bounds.top)), new _Vector2.default(Math.round(bounds.left), Math.round(bounds.height + bounds.top))];\n    }\n};\n\nvar parseBackground = exports.parseBackground = function parseBackground(style, resourceLoader) {\n    return {\n        backgroundColor: new _Color2.default(style.backgroundColor),\n        backgroundImage: parseBackgroundImages(style, resourceLoader),\n        backgroundClip: parseBackgroundClip(style.backgroundClip),\n        backgroundOrigin: parseBackgroundOrigin(style.backgroundOrigin)\n    };\n};\n\nvar parseBackgroundClip = function parseBackgroundClip(backgroundClip) {\n    switch (backgroundClip) {\n        case 'padding-box':\n            return BACKGROUND_CLIP.PADDING_BOX;\n        case 'content-box':\n            return BACKGROUND_CLIP.CONTENT_BOX;\n    }\n    return BACKGROUND_CLIP.BORDER_BOX;\n};\n\nvar parseBackgroundOrigin = function parseBackgroundOrigin(backgroundOrigin) {\n    switch (backgroundOrigin) {\n        case 'padding-box':\n            return BACKGROUND_ORIGIN.PADDING_BOX;\n        case 'content-box':\n            return BACKGROUND_ORIGIN.CONTENT_BOX;\n    }\n    return BACKGROUND_ORIGIN.BORDER_BOX;\n};\n\nvar parseBackgroundRepeat = function parseBackgroundRepeat(backgroundRepeat) {\n    switch (backgroundRepeat.trim()) {\n        case 'no-repeat':\n            return BACKGROUND_REPEAT.NO_REPEAT;\n        case 'repeat-x':\n        case 'repeat no-repeat':\n            return BACKGROUND_REPEAT.REPEAT_X;\n        case 'repeat-y':\n        case 'no-repeat repeat':\n            return BACKGROUND_REPEAT.REPEAT_Y;\n        case 'repeat':\n            return BACKGROUND_REPEAT.REPEAT;\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n        console.error('Invalid background-repeat value \"' + backgroundRepeat + '\"');\n    }\n\n    return BACKGROUND_REPEAT.REPEAT;\n};\n\nvar parseBackgroundImages = function parseBackgroundImages(style, resourceLoader) {\n    var sources = parseBackgroundImage(style.backgroundImage).map(function (backgroundImage) {\n        if (backgroundImage.method === 'url') {\n            var key = resourceLoader.loadImage(backgroundImage.args[0]);\n            backgroundImage.args = key ? [key] : [];\n        }\n        return backgroundImage;\n    });\n    var positions = style.backgroundPosition.split(',');\n    var repeats = style.backgroundRepeat.split(',');\n    var sizes = style.backgroundSize.split(',');\n\n    return sources.map(function (source, index) {\n        var size = (sizes[index] || AUTO).trim().split(' ').map(parseBackgroundSize);\n        var position = (positions[index] || AUTO).trim().split(' ').map(parseBackgoundPosition);\n\n        return {\n            source: source,\n            repeat: parseBackgroundRepeat(typeof repeats[index] === 'string' ? repeats[index] : repeats[0]),\n            size: size.length < 2 ? [size[0], AUTO_SIZE] : [size[0], size[1]],\n            position: position.length < 2 ? [position[0], position[0]] : [position[0], position[1]]\n        };\n    });\n};\n\nvar parseBackgroundSize = function parseBackgroundSize(size) {\n    return size === 'auto' ? AUTO_SIZE : new BackgroundSize(size);\n};\n\nvar parseBackgoundPosition = function parseBackgoundPosition(position) {\n    switch (position) {\n        case 'bottom':\n        case 'right':\n            return new _Length2.default('100%');\n        case 'left':\n        case 'top':\n            return new _Length2.default('0%');\n        case 'auto':\n            return new _Length2.default('0');\n    }\n    return new _Length2.default(position);\n};\n\nvar parseBackgroundImage = exports.parseBackgroundImage = function parseBackgroundImage(image) {\n    var whitespace = /^\\s$/;\n    var results = [];\n\n    var args = [];\n    var method = '';\n    var quote = null;\n    var definition = '';\n    var mode = 0;\n    var numParen = 0;\n\n    var appendResult = function appendResult() {\n        var prefix = '';\n        if (method) {\n            if (definition.substr(0, 1) === '\"') {\n                definition = definition.substr(1, definition.length - 2);\n            }\n\n            if (definition) {\n                args.push(definition.trim());\n            }\n\n            var prefix_i = method.indexOf('-', 1) + 1;\n            if (method.substr(0, 1) === '-' && prefix_i > 0) {\n                prefix = method.substr(0, prefix_i).toLowerCase();\n                method = method.substr(prefix_i);\n            }\n            method = method.toLowerCase();\n            if (method !== 'none') {\n                results.push({\n                    prefix: prefix,\n                    method: method,\n                    args: args\n                });\n            }\n        }\n        args = [];\n        method = definition = '';\n    };\n\n    image.split('').forEach(function (c) {\n        if (mode === 0 && whitespace.test(c)) {\n            return;\n        }\n        switch (c) {\n            case '\"':\n                if (!quote) {\n                    quote = c;\n                } else if (quote === c) {\n                    quote = null;\n                }\n                break;\n            case '(':\n                if (quote) {\n                    break;\n                } else if (mode === 0) {\n                    mode = 1;\n                    return;\n                } else {\n                    numParen++;\n                }\n                break;\n            case ')':\n                if (quote) {\n                    break;\n                } else if (mode === 1) {\n                    if (numParen === 0) {\n                        mode = 0;\n                        appendResult();\n                        return;\n                    } else {\n                        numParen--;\n                    }\n                }\n                break;\n\n            case ',':\n                if (quote) {\n                    break;\n                } else if (mode === 0) {\n                    appendResult();\n                    return;\n                } else if (mode === 1) {\n                    if (numParen === 0 && !method.match(/^url$/i)) {\n                        args.push(definition.trim());\n                        definition = '';\n                        return;\n                    }\n                }\n                break;\n        }\n\n        if (mode === 0) {\n            method += c;\n        } else {\n            definition += c;\n        }\n    });\n\n    appendResult();\n    return results;\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.parsePadding = exports.PADDING_SIDES = undefined;\n\nvar _Length = require('../Length');\n\nvar _Length2 = _interopRequireDefault(_Length);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar PADDING_SIDES = exports.PADDING_SIDES = {\n    TOP: 0,\n    RIGHT: 1,\n    BOTTOM: 2,\n    LEFT: 3\n};\n\nvar SIDES = ['top', 'right', 'bottom', 'left'];\n\nvar parsePadding = exports.parsePadding = function parsePadding(style) {\n    return SIDES.map(function (side) {\n        return new _Length2.default(style.getPropertyValue('padding-' + side));\n    });\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nvar ANGLE = /([+-]?\\d*\\.?\\d+)(deg|grad|rad|turn)/i;\n\nvar parseAngle = exports.parseAngle = function parseAngle(angle) {\n    var match = angle.match(ANGLE);\n\n    if (match) {\n        var value = parseFloat(match[1]);\n        switch (match[2].toLowerCase()) {\n            case 'deg':\n                return Math.PI * value / 180;\n            case 'grad':\n                return Math.PI / 200 * value;\n            case 'rad':\n                return value;\n            case 'turn':\n                return Math.PI * 2 * value;\n        }\n    }\n\n    return null;\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.parseBoundCurves = exports.calculatePaddingBoxPath = exports.calculateBorderBoxPath = exports.parsePathForBorder = exports.parseDocumentSize = exports.calculateContentBox = exports.calculatePaddingBox = exports.parseBounds = exports.Bounds = undefined;\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _Vector = require('./drawing/Vector');\n\nvar _Vector2 = _interopRequireDefault(_Vector);\n\nvar _BezierCurve = require('./drawing/BezierCurve');\n\nvar _BezierCurve2 = _interopRequireDefault(_BezierCurve);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar TOP = 0;\nvar RIGHT = 1;\nvar BOTTOM = 2;\nvar LEFT = 3;\n\nvar H = 0;\nvar V = 1;\n\nvar Bounds = exports.Bounds = function () {\n    function Bounds(x, y, w, h) {\n        _classCallCheck(this, Bounds);\n\n        this.left = x;\n        this.top = y;\n        this.width = w;\n        this.height = h;\n    }\n\n    _createClass(Bounds, null, [{\n        key: 'fromClientRect',\n        value: function fromClientRect(clientRect, scrollX, scrollY) {\n            return new Bounds(clientRect.left + scrollX, clientRect.top + scrollY, clientRect.width, clientRect.height);\n        }\n    }]);\n\n    return Bounds;\n}();\n\nvar parseBounds = exports.parseBounds = function parseBounds(node, scrollX, scrollY) {\n    return Bounds.fromClientRect(node.getBoundingClientRect(), scrollX, scrollY);\n};\n\nvar calculatePaddingBox = exports.calculatePaddingBox = function calculatePaddingBox(bounds, borders) {\n    return new Bounds(bounds.left + borders[LEFT].borderWidth, bounds.top + borders[TOP].borderWidth, bounds.width - (borders[RIGHT].borderWidth + borders[LEFT].borderWidth), bounds.height - (borders[TOP].borderWidth + borders[BOTTOM].borderWidth));\n};\n\nvar calculateContentBox = exports.calculateContentBox = function calculateContentBox(bounds, padding, borders) {\n    // TODO support percentage paddings\n    var paddingTop = padding[TOP].value;\n    var paddingRight = padding[RIGHT].value;\n    var paddingBottom = padding[BOTTOM].value;\n    var paddingLeft = padding[LEFT].value;\n\n    return new Bounds(bounds.left + paddingLeft + borders[LEFT].borderWidth, bounds.top + paddingTop + borders[TOP].borderWidth, bounds.width - (borders[RIGHT].borderWidth + borders[LEFT].borderWidth + paddingLeft + paddingRight), bounds.height - (borders[TOP].borderWidth + borders[BOTTOM].borderWidth + paddingTop + paddingBottom));\n};\n\nvar parseDocumentSize = exports.parseDocumentSize = function parseDocumentSize(document) {\n    var body = document.body;\n    var documentElement = document.documentElement;\n\n    if (!body || !documentElement) {\n        throw new Error(process.env.NODE_ENV !== 'production' ? 'Unable to get document size' : '');\n    }\n    var width = Math.max(Math.max(body.scrollWidth, documentElement.scrollWidth), Math.max(body.offsetWidth, documentElement.offsetWidth), Math.max(body.clientWidth, documentElement.clientWidth));\n\n    var height = Math.max(Math.max(body.scrollHeight, documentElement.scrollHeight), Math.max(body.offsetHeight, documentElement.offsetHeight), Math.max(body.clientHeight, documentElement.clientHeight));\n\n    return new Bounds(0, 0, width, height);\n};\n\nvar parsePathForBorder = exports.parsePathForBorder = function parsePathForBorder(curves, borderSide) {\n    switch (borderSide) {\n        case TOP:\n            return createPathFromCurves(curves.topLeftOuter, curves.topLeftInner, curves.topRightOuter, curves.topRightInner);\n        case RIGHT:\n            return createPathFromCurves(curves.topRightOuter, curves.topRightInner, curves.bottomRightOuter, curves.bottomRightInner);\n        case BOTTOM:\n            return createPathFromCurves(curves.bottomRightOuter, curves.bottomRightInner, curves.bottomLeftOuter, curves.bottomLeftInner);\n        case LEFT:\n        default:\n            return createPathFromCurves(curves.bottomLeftOuter, curves.bottomLeftInner, curves.topLeftOuter, curves.topLeftInner);\n    }\n};\n\nvar createPathFromCurves = function createPathFromCurves(outer1, inner1, outer2, inner2) {\n    var path = [];\n    if (outer1 instanceof _BezierCurve2.default) {\n        path.push(outer1.subdivide(0.5, false));\n    } else {\n        path.push(outer1);\n    }\n\n    if (outer2 instanceof _BezierCurve2.default) {\n        path.push(outer2.subdivide(0.5, true));\n    } else {\n        path.push(outer2);\n    }\n\n    if (inner2 instanceof _BezierCurve2.default) {\n        path.push(inner2.subdivide(0.5, true).reverse());\n    } else {\n        path.push(inner2);\n    }\n\n    if (inner1 instanceof _BezierCurve2.default) {\n        path.push(inner1.subdivide(0.5, false).reverse());\n    } else {\n        path.push(inner1);\n    }\n\n    return path;\n};\n\nvar calculateBorderBoxPath = exports.calculateBorderBoxPath = function calculateBorderBoxPath(curves) {\n    return [curves.topLeftOuter, curves.topRightOuter, curves.bottomRightOuter, curves.bottomLeftOuter];\n};\n\nvar calculatePaddingBoxPath = exports.calculatePaddingBoxPath = function calculatePaddingBoxPath(curves) {\n    return [curves.topLeftInner, curves.topRightInner, curves.bottomRightInner, curves.bottomLeftInner];\n};\n\nvar parseBoundCurves = exports.parseBoundCurves = function parseBoundCurves(bounds, borders, borderRadius) {\n    var tlh = borderRadius[CORNER.TOP_LEFT][H].getAbsoluteValue(bounds.width);\n    var tlv = borderRadius[CORNER.TOP_LEFT][V].getAbsoluteValue(bounds.height);\n    var trh = borderRadius[CORNER.TOP_RIGHT][H].getAbsoluteValue(bounds.width);\n    var trv = borderRadius[CORNER.TOP_RIGHT][V].getAbsoluteValue(bounds.height);\n    var brh = borderRadius[CORNER.BOTTOM_RIGHT][H].getAbsoluteValue(bounds.width);\n    var brv = borderRadius[CORNER.BOTTOM_RIGHT][V].getAbsoluteValue(bounds.height);\n    var blh = borderRadius[CORNER.BOTTOM_LEFT][H].getAbsoluteValue(bounds.width);\n    var blv = borderRadius[CORNER.BOTTOM_LEFT][V].getAbsoluteValue(bounds.height);\n\n    var factors = [];\n    factors.push((tlh + trh) / bounds.width);\n    factors.push((blh + brh) / bounds.width);\n    factors.push((tlv + blv) / bounds.height);\n    factors.push((trv + brv) / bounds.height);\n    var maxFactor = Math.max.apply(Math, factors);\n\n    if (maxFactor > 1) {\n        tlh /= maxFactor;\n        tlv /= maxFactor;\n        trh /= maxFactor;\n        trv /= maxFactor;\n        brh /= maxFactor;\n        brv /= maxFactor;\n        blh /= maxFactor;\n        blv /= maxFactor;\n    }\n\n    var topWidth = bounds.width - trh;\n    var rightHeight = bounds.height - brv;\n    var bottomWidth = bounds.width - brh;\n    var leftHeight = bounds.height - blv;\n\n    return {\n        topLeftOuter: tlh > 0 || tlv > 0 ? getCurvePoints(bounds.left, bounds.top, tlh, tlv, CORNER.TOP_LEFT) : new _Vector2.default(bounds.left, bounds.top),\n        topLeftInner: tlh > 0 || tlv > 0 ? getCurvePoints(bounds.left + borders[LEFT].borderWidth, bounds.top + borders[TOP].borderWidth, Math.max(0, tlh - borders[LEFT].borderWidth), Math.max(0, tlv - borders[TOP].borderWidth), CORNER.TOP_LEFT) : new _Vector2.default(bounds.left + borders[LEFT].borderWidth, bounds.top + borders[TOP].borderWidth),\n        topRightOuter: trh > 0 || trv > 0 ? getCurvePoints(bounds.left + topWidth, bounds.top, trh, trv, CORNER.TOP_RIGHT) : new _Vector2.default(bounds.left + bounds.width, bounds.top),\n        topRightInner: trh > 0 || trv > 0 ? getCurvePoints(bounds.left + Math.min(topWidth, bounds.width + borders[LEFT].borderWidth), bounds.top + borders[TOP].borderWidth, topWidth > bounds.width + borders[LEFT].borderWidth ? 0 : trh - borders[LEFT].borderWidth, trv - borders[TOP].borderWidth, CORNER.TOP_RIGHT) : new _Vector2.default(bounds.left + bounds.width - borders[RIGHT].borderWidth, bounds.top + borders[TOP].borderWidth),\n        bottomRightOuter: brh > 0 || brv > 0 ? getCurvePoints(bounds.left + bottomWidth, bounds.top + rightHeight, brh, brv, CORNER.BOTTOM_RIGHT) : new _Vector2.default(bounds.left + bounds.width, bounds.top + bounds.height),\n        bottomRightInner: brh > 0 || brv > 0 ? getCurvePoints(bounds.left + Math.min(bottomWidth, bounds.width - borders[LEFT].borderWidth), bounds.top + Math.min(rightHeight, bounds.height + borders[TOP].borderWidth), Math.max(0, brh - borders[RIGHT].borderWidth), brv - borders[BOTTOM].borderWidth, CORNER.BOTTOM_RIGHT) : new _Vector2.default(bounds.left + bounds.width - borders[RIGHT].borderWidth, bounds.top + bounds.height - borders[BOTTOM].borderWidth),\n        bottomLeftOuter: blh > 0 || blv > 0 ? getCurvePoints(bounds.left, bounds.top + leftHeight, blh, blv, CORNER.BOTTOM_LEFT) : new _Vector2.default(bounds.left, bounds.top + bounds.height),\n        bottomLeftInner: blh > 0 || blv > 0 ? getCurvePoints(bounds.left + borders[LEFT].borderWidth, bounds.top + leftHeight, Math.max(0, blh - borders[LEFT].borderWidth), blv - borders[BOTTOM].borderWidth, CORNER.BOTTOM_LEFT) : new _Vector2.default(bounds.left + borders[LEFT].borderWidth, bounds.top + bounds.height - borders[BOTTOM].borderWidth)\n    };\n};\n\nvar CORNER = {\n    TOP_LEFT: 0,\n    TOP_RIGHT: 1,\n    BOTTOM_RIGHT: 2,\n    BOTTOM_LEFT: 3\n};\n\nvar getCurvePoints = function getCurvePoints(x, y, r1, r2, position) {\n    var kappa = 4 * ((Math.sqrt(2) - 1) / 3);\n    var ox = r1 * kappa; // control point offset horizontal\n    var oy = r2 * kappa; // control point offset vertical\n    var xm = x + r1; // x-middle\n    var ym = y + r2; // y-middle\n\n    switch (position) {\n        case CORNER.TOP_LEFT:\n            return new _BezierCurve2.default(new _Vector2.default(x, ym), new _Vector2.default(x, ym - oy), new _Vector2.default(xm - ox, y), new _Vector2.default(xm, y));\n        case CORNER.TOP_RIGHT:\n            return new _BezierCurve2.default(new _Vector2.default(x, y), new _Vector2.default(x + ox, y), new _Vector2.default(xm, ym - oy), new _Vector2.default(xm, ym));\n        case CORNER.BOTTOM_RIGHT:\n            return new _BezierCurve2.default(new _Vector2.default(xm, y), new _Vector2.default(xm, y + oy), new _Vector2.default(x + ox, ym), new _Vector2.default(x, ym));\n        case CORNER.BOTTOM_LEFT:\n        default:\n            return new _BezierCurve2.default(new _Vector2.default(xm, ym), new _Vector2.default(xm - ox, ym), new _Vector2.default(x, y + oy), new _Vector2.default(x, y));\n    }\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nvar toCodePoints = exports.toCodePoints = function toCodePoints(str) {\n    var codePoints = [];\n    var i = 0;\n    var length = str.length;\n    while (i < length) {\n        var value = str.charCodeAt(i++);\n        if (value >= 0xd800 && value <= 0xdbff && i < length) {\n            var extra = str.charCodeAt(i++);\n            if ((extra & 0xfc00) === 0xdc00) {\n                codePoints.push(((value & 0x3ff) << 10) + (extra & 0x3ff) + 0x10000);\n            } else {\n                codePoints.push(value);\n                i--;\n            }\n        } else {\n            codePoints.push(value);\n        }\n    }\n    return codePoints;\n};\n\nvar fromCodePoint = exports.fromCodePoint = function fromCodePoint() {\n    if (String.fromCodePoint) {\n        return String.fromCodePoint.apply(String, arguments);\n    }\n\n    var length = arguments.length;\n    if (!length) {\n        return '';\n    }\n\n    var codeUnits = [];\n\n    var index = -1;\n    var result = '';\n    while (++index < length) {\n        var codePoint = arguments.length <= index ? undefined : arguments[index];\n        if (codePoint <= 0xffff) {\n            codeUnits.push(codePoint);\n        } else {\n            codePoint -= 0x10000;\n            codeUnits.push((codePoint >> 10) + 0xd800, codePoint % 0x400 + 0xdc00);\n        }\n        if (index + 1 === length || codeUnits.length > 0x4000) {\n            result += String.fromCharCode.apply(String, codeUnits);\n            codeUnits.length = 0;\n        }\n    }\n    return result;\n};\n\nvar chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n\n// Use a lookup table to find the index.\nvar lookup = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\nfor (var i = 0; i < chars.length; i++) {\n    lookup[chars.charCodeAt(i)] = i;\n}\n\nvar decode = exports.decode = function decode(base64) {\n    var bufferLength = base64.length * 0.75,\n        len = base64.length,\n        i = void 0,\n        p = 0,\n        encoded1 = void 0,\n        encoded2 = void 0,\n        encoded3 = void 0,\n        encoded4 = void 0;\n\n    if (base64[base64.length - 1] === '=') {\n        bufferLength--;\n        if (base64[base64.length - 2] === '=') {\n            bufferLength--;\n        }\n    }\n\n    var buffer = typeof ArrayBuffer !== 'undefined' && typeof Uint8Array !== 'undefined' && typeof Uint8Array.prototype.slice !== 'undefined' ? new ArrayBuffer(bufferLength) : new Array(bufferLength);\n    var bytes = Array.isArray(buffer) ? buffer : new Uint8Array(buffer);\n\n    for (i = 0; i < len; i += 4) {\n        encoded1 = lookup[base64.charCodeAt(i)];\n        encoded2 = lookup[base64.charCodeAt(i + 1)];\n        encoded3 = lookup[base64.charCodeAt(i + 2)];\n        encoded4 = lookup[base64.charCodeAt(i + 3)];\n\n        bytes[p++] = encoded1 << 2 | encoded2 >> 4;\n        bytes[p++] = (encoded2 & 15) << 4 | encoded3 >> 2;\n        bytes[p++] = (encoded3 & 3) << 6 | encoded4 & 63;\n    }\n\n    return buffer;\n};\n\nvar polyUint16Array = exports.polyUint16Array = function polyUint16Array(buffer) {\n    var length = buffer.length;\n    var bytes = [];\n    for (var _i = 0; _i < length; _i += 2) {\n        bytes.push(buffer[_i + 1] << 8 | buffer[_i]);\n    }\n    return bytes;\n};\n\nvar polyUint32Array = exports.polyUint32Array = function polyUint32Array(buffer) {\n    var length = buffer.length;\n    var bytes = [];\n    for (var _i2 = 0; _i2 < length; _i2 += 4) {\n        bytes.push(buffer[_i2 + 3] << 24 | buffer[_i2 + 2] << 16 | buffer[_i2 + 1] << 8 | buffer[_i2]);\n    }\n    return bytes;\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar Logger = function () {\n    function Logger(enabled, id, start) {\n        _classCallCheck(this, Logger);\n\n        this.enabled = typeof window !== 'undefined' && enabled;\n        this.start = start ? start : Date.now();\n        this.id = id;\n    }\n\n    _createClass(Logger, [{\n        key: 'child',\n        value: function child(id) {\n            return new Logger(this.enabled, id, this.start);\n        }\n\n        // eslint-disable-next-line flowtype/no-weak-types\n\n    }, {\n        key: 'log',\n        value: function log() {\n            if (this.enabled && window.console && window.console.log) {\n                for (var _len = arguments.length, args = Array(_len), _key = 0; _key < _len; _key++) {\n                    args[_key] = arguments[_key];\n                }\n\n                Function.prototype.bind.call(window.console.log, window.console).apply(window.console, [Date.now() - this.start + 'ms', this.id ? 'html2canvas (' + this.id + '):' : 'html2canvas:'].concat([].slice.call(args, 0)));\n            }\n        }\n\n        // eslint-disable-next-line flowtype/no-weak-types\n\n    }, {\n        key: 'error',\n        value: function error() {\n            if (this.enabled && window.console && window.console.error) {\n                for (var _len2 = arguments.length, args = Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n                    args[_key2] = arguments[_key2];\n                }\n\n                Function.prototype.bind.call(window.console.error, window.console).apply(window.console, [Date.now() - this.start + 'ms', this.id ? 'html2canvas (' + this.id + '):' : 'html2canvas:'].concat([].slice.call(args, 0)));\n            }\n        }\n    }]);\n\n    return Logger;\n}();\n\nexports.default = Logger;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nvar TEXT_TRANSFORM = exports.TEXT_TRANSFORM = {\n    NONE: 0,\n    LOWERCASE: 1,\n    UPPERCASE: 2,\n    CAPITALIZE: 3\n};\n\nvar parseTextTransform = exports.parseTextTransform = function parseTextTransform(textTransform) {\n    switch (textTransform) {\n        case 'uppercase':\n            return TEXT_TRANSFORM.UPPERCASE;\n        case 'lowercase':\n            return TEXT_TRANSFORM.LOWERCASE;\n        case 'capitalize':\n            return TEXT_TRANSFORM.CAPITALIZE;\n    }\n\n    return TEXT_TRANSFORM.NONE;\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.Proxy = undefined;\n\nvar _Feature = require('./Feature');\n\nvar _Feature2 = _interopRequireDefault(_Feature);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar Proxy = exports.Proxy = function Proxy(src, options) {\n    if (!options.proxy) {\n        return Promise.reject(process.env.NODE_ENV !== 'production' ? 'No proxy defined' : null);\n    }\n    var proxy = options.proxy;\n\n    return new Promise(function (resolve, reject) {\n        var responseType = _Feature2.default.SUPPORT_CORS_XHR && _Feature2.default.SUPPORT_RESPONSE_TYPE ? 'blob' : 'text';\n        var xhr = _Feature2.default.SUPPORT_CORS_XHR ? new XMLHttpRequest() : new XDomainRequest();\n        xhr.onload = function () {\n            if (xhr instanceof XMLHttpRequest) {\n                if (xhr.status === 200) {\n                    if (responseType === 'text') {\n                        resolve(xhr.response);\n                    } else {\n                        var reader = new FileReader();\n                        // $FlowFixMe\n                        reader.addEventListener('load', function () {\n                            return resolve(reader.result);\n                        }, false);\n                        // $FlowFixMe\n                        reader.addEventListener('error', function (e) {\n                            return reject(e);\n                        }, false);\n                        reader.readAsDataURL(xhr.response);\n                    }\n                } else {\n                    reject(process.env.NODE_ENV !== 'production' ? 'Failed to proxy resource ' + src.substring(0, 256) + ' with status code ' + xhr.status : '');\n                }\n            } else {\n                resolve(xhr.responseText);\n            }\n        };\n\n        xhr.onerror = reject;\n        xhr.open('GET', proxy + '?url=' + encodeURIComponent(src) + '&responseType=' + responseType);\n\n        if (responseType !== 'text' && xhr instanceof XMLHttpRequest) {\n            xhr.responseType = responseType;\n        }\n\n        if (options.imageTimeout) {\n            var timeout = options.imageTimeout;\n            xhr.timeout = timeout;\n            xhr.ontimeout = function () {\n                return reject(process.env.NODE_ENV !== 'production' ? 'Timed out (' + timeout + 'ms) proxying ' + src.substring(0, 256) : '');\n            };\n        }\n\n        xhr.send();\n    });\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.parseTextShadow = undefined;\n\nvar _Color = require('../Color');\n\nvar _Color2 = _interopRequireDefault(_Color);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar NUMBER = /^([+-]|\\d|\\.)$/i;\n\nvar parseTextShadow = exports.parseTextShadow = function parseTextShadow(textShadow) {\n    if (textShadow === 'none' || typeof textShadow !== 'string') {\n        return null;\n    }\n\n    var currentValue = '';\n    var isLength = false;\n    var values = [];\n    var shadows = [];\n    var numParens = 0;\n    var color = null;\n\n    var appendValue = function appendValue() {\n        if (currentValue.length) {\n            if (isLength) {\n                values.push(parseFloat(currentValue));\n            } else {\n                color = new _Color2.default(currentValue);\n            }\n        }\n        isLength = false;\n        currentValue = '';\n    };\n\n    var appendShadow = function appendShadow() {\n        if (values.length && color !== null) {\n            shadows.push({\n                color: color,\n                offsetX: values[0] || 0,\n                offsetY: values[1] || 0,\n                blur: values[2] || 0\n            });\n        }\n        values.splice(0, values.length);\n        color = null;\n    };\n\n    for (var i = 0; i < textShadow.length; i++) {\n        var c = textShadow[i];\n        switch (c) {\n            case '(':\n                currentValue += c;\n                numParens++;\n                break;\n            case ')':\n                currentValue += c;\n                numParens--;\n                break;\n            case ',':\n                if (numParens === 0) {\n                    appendValue();\n                    appendShadow();\n                } else {\n                    currentValue += c;\n                }\n                break;\n            case ' ':\n                if (numParens === 0) {\n                    appendValue();\n                } else {\n                    currentValue += c;\n                }\n                break;\n            default:\n                if (currentValue.length === 0 && NUMBER.test(c)) {\n                    isLength = true;\n                }\n                currentValue += c;\n        }\n    }\n\n    appendValue();\n    appendShadow();\n\n    if (shadows.length === 0) {\n        return null;\n    }\n\n    return shadows;\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.reformatInputBounds = exports.inlineSelectElement = exports.inlineTextAreaElement = exports.inlineInputElement = exports.getInputBorderRadius = exports.INPUT_BACKGROUND = exports.INPUT_BORDERS = exports.INPUT_COLOR = undefined;\n\nvar _TextContainer = require('./TextContainer');\n\nvar _TextContainer2 = _interopRequireDefault(_TextContainer);\n\nvar _background = require('./parsing/background');\n\nvar _border = require('./parsing/border');\n\nvar _Circle = require('./drawing/Circle');\n\nvar _Circle2 = _interopRequireDefault(_Circle);\n\nvar _Vector = require('./drawing/Vector');\n\nvar _Vector2 = _interopRequireDefault(_Vector);\n\nvar _Color = require('./Color');\n\nvar _Color2 = _interopRequireDefault(_Color);\n\nvar _Length = require('./Length');\n\nvar _Length2 = _interopRequireDefault(_Length);\n\nvar _Bounds = require('./Bounds');\n\nvar _TextBounds = require('./TextBounds');\n\nvar _Util = require('./Util');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar INPUT_COLOR = exports.INPUT_COLOR = new _Color2.default([42, 42, 42]);\nvar INPUT_BORDER_COLOR = new _Color2.default([165, 165, 165]);\nvar INPUT_BACKGROUND_COLOR = new _Color2.default([222, 222, 222]);\nvar INPUT_BORDER = {\n    borderWidth: 1,\n    borderColor: INPUT_BORDER_COLOR,\n    borderStyle: _border.BORDER_STYLE.SOLID\n};\nvar INPUT_BORDERS = exports.INPUT_BORDERS = [INPUT_BORDER, INPUT_BORDER, INPUT_BORDER, INPUT_BORDER];\nvar INPUT_BACKGROUND = exports.INPUT_BACKGROUND = {\n    backgroundColor: INPUT_BACKGROUND_COLOR,\n    backgroundImage: [],\n    backgroundClip: _background.BACKGROUND_CLIP.PADDING_BOX,\n    backgroundOrigin: _background.BACKGROUND_ORIGIN.PADDING_BOX\n};\n\nvar RADIO_BORDER_RADIUS = new _Length2.default('50%');\nvar RADIO_BORDER_RADIUS_TUPLE = [RADIO_BORDER_RADIUS, RADIO_BORDER_RADIUS];\nvar INPUT_RADIO_BORDER_RADIUS = [RADIO_BORDER_RADIUS_TUPLE, RADIO_BORDER_RADIUS_TUPLE, RADIO_BORDER_RADIUS_TUPLE, RADIO_BORDER_RADIUS_TUPLE];\n\nvar CHECKBOX_BORDER_RADIUS = new _Length2.default('3px');\nvar CHECKBOX_BORDER_RADIUS_TUPLE = [CHECKBOX_BORDER_RADIUS, CHECKBOX_BORDER_RADIUS];\nvar INPUT_CHECKBOX_BORDER_RADIUS = [CHECKBOX_BORDER_RADIUS_TUPLE, CHECKBOX_BORDER_RADIUS_TUPLE, CHECKBOX_BORDER_RADIUS_TUPLE, CHECKBOX_BORDER_RADIUS_TUPLE];\n\nvar getInputBorderRadius = exports.getInputBorderRadius = function getInputBorderRadius(node) {\n    return node.type === 'radio' ? INPUT_RADIO_BORDER_RADIUS : INPUT_CHECKBOX_BORDER_RADIUS;\n};\n\nvar inlineInputElement = exports.inlineInputElement = function inlineInputElement(node, container) {\n    if (node.type === 'radio' || node.type === 'checkbox') {\n        if (node.checked) {\n            var size = Math.min(container.bounds.width, container.bounds.height);\n            container.childNodes.push(node.type === 'checkbox' ? [new _Vector2.default(container.bounds.left + size * 0.39363, container.bounds.top + size * 0.79), new _Vector2.default(container.bounds.left + size * 0.16, container.bounds.top + size * 0.5549), new _Vector2.default(container.bounds.left + size * 0.27347, container.bounds.top + size * 0.44071), new _Vector2.default(container.bounds.left + size * 0.39694, container.bounds.top + size * 0.5649), new _Vector2.default(container.bounds.left + size * 0.72983, container.bounds.top + size * 0.23), new _Vector2.default(container.bounds.left + size * 0.84, container.bounds.top + size * 0.34085), new _Vector2.default(container.bounds.left + size * 0.39363, container.bounds.top + size * 0.79)] : new _Circle2.default(container.bounds.left + size / 4, container.bounds.top + size / 4, size / 4));\n        }\n    } else {\n        inlineFormElement(getInputValue(node), node, container, false);\n    }\n};\n\nvar inlineTextAreaElement = exports.inlineTextAreaElement = function inlineTextAreaElement(node, container) {\n    inlineFormElement(node.value, node, container, true);\n};\n\nvar inlineSelectElement = exports.inlineSelectElement = function inlineSelectElement(node, container) {\n    var option = node.options[node.selectedIndex || 0];\n    inlineFormElement(option ? option.text || '' : '', node, container, false);\n};\n\nvar reformatInputBounds = exports.reformatInputBounds = function reformatInputBounds(bounds) {\n    if (bounds.width > bounds.height) {\n        bounds.left += (bounds.width - bounds.height) / 2;\n        bounds.width = bounds.height;\n    } else if (bounds.width < bounds.height) {\n        bounds.top += (bounds.height - bounds.width) / 2;\n        bounds.height = bounds.width;\n    }\n    return bounds;\n};\n\nvar inlineFormElement = function inlineFormElement(value, node, container, allowLinebreak) {\n    var body = node.ownerDocument.body;\n    if (value.length > 0 && body) {\n        var wrapper = node.ownerDocument.createElement('html2canvaswrapper');\n        (0, _Util.copyCSSStyles)(node.ownerDocument.defaultView.getComputedStyle(node, null), wrapper);\n        wrapper.style.position = 'absolute';\n        wrapper.style.left = container.bounds.left + 'px';\n        wrapper.style.top = container.bounds.top + 'px';\n        if (!allowLinebreak) {\n            wrapper.style.whiteSpace = 'nowrap';\n        }\n        var text = node.ownerDocument.createTextNode(value);\n        wrapper.appendChild(text);\n        body.appendChild(wrapper);\n        container.childNodes.push(_TextContainer2.default.fromTextNode(text, container));\n        body.removeChild(wrapper);\n    }\n};\n\nvar getInputValue = function getInputValue(node) {\n    var value = node.type === 'password' ? new Array(node.value.length + 1).join('\\u2022') : node.value;\n\n    return value.length === 0 ? node.placeholder || '' : value;\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.parseTextDecoration = exports.TEXT_DECORATION_LINE = exports.TEXT_DECORATION = exports.TEXT_DECORATION_STYLE = undefined;\n\nvar _Color = require('../Color');\n\nvar _Color2 = _interopRequireDefault(_Color);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar TEXT_DECORATION_STYLE = exports.TEXT_DECORATION_STYLE = {\n    SOLID: 0,\n    DOUBLE: 1,\n    DOTTED: 2,\n    DASHED: 3,\n    WAVY: 4\n};\n\nvar TEXT_DECORATION = exports.TEXT_DECORATION = {\n    NONE: null\n};\n\nvar TEXT_DECORATION_LINE = exports.TEXT_DECORATION_LINE = {\n    UNDERLINE: 1,\n    OVERLINE: 2,\n    LINE_THROUGH: 3,\n    BLINK: 4\n};\n\nvar parseLine = function parseLine(line) {\n    switch (line) {\n        case 'underline':\n            return TEXT_DECORATION_LINE.UNDERLINE;\n        case 'overline':\n            return TEXT_DECORATION_LINE.OVERLINE;\n        case 'line-through':\n            return TEXT_DECORATION_LINE.LINE_THROUGH;\n    }\n    return TEXT_DECORATION_LINE.BLINK;\n};\n\nvar parseTextDecorationLine = function parseTextDecorationLine(line) {\n    if (line === 'none') {\n        return null;\n    }\n\n    return line.split(' ').map(parseLine);\n};\n\nvar parseTextDecorationStyle = function parseTextDecorationStyle(style) {\n    switch (style) {\n        case 'double':\n            return TEXT_DECORATION_STYLE.DOUBLE;\n        case 'dotted':\n            return TEXT_DECORATION_STYLE.DOTTED;\n        case 'dashed':\n            return TEXT_DECORATION_STYLE.DASHED;\n        case 'wavy':\n            return TEXT_DECORATION_STYLE.WAVY;\n    }\n    return TEXT_DECORATION_STYLE.SOLID;\n};\n\nvar parseTextDecoration = exports.parseTextDecoration = function parseTextDecoration(style) {\n    var textDecorationLine = parseTextDecorationLine(style.textDecorationLine ? style.textDecorationLine : style.textDecoration);\n    if (textDecorationLine === null) {\n        return TEXT_DECORATION.NONE;\n    }\n\n    var textDecorationColor = style.textDecorationColor ? new _Color2.default(style.textDecorationColor) : null;\n    var textDecorationStyle = parseTextDecorationStyle(style.textDecorationStyle);\n\n    return {\n        textDecorationLine: textDecorationLine,\n        textDecorationColor: textDecorationColor,\n        textDecorationStyle: textDecorationStyle\n    };\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nvar FLOAT = exports.FLOAT = {\n    NONE: 0,\n    LEFT: 1,\n    RIGHT: 2,\n    INLINE_START: 3,\n    INLINE_END: 4\n};\n\nvar parseCSSFloat = exports.parseCSSFloat = function parseCSSFloat(float) {\n    switch (float) {\n        case 'left':\n            return FLOAT.LEFT;\n        case 'right':\n            return FLOAT.RIGHT;\n        case 'inline-start':\n            return FLOAT.INLINE_START;\n        case 'inline-end':\n            return FLOAT.INLINE_END;\n    }\n    return FLOAT.NONE;\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar ForeignObjectRenderer = function () {\n    function ForeignObjectRenderer(element) {\n        _classCallCheck(this, ForeignObjectRenderer);\n\n        this.element = element;\n    }\n\n    _createClass(ForeignObjectRenderer, [{\n        key: 'render',\n        value: function render(options) {\n            var _this = this;\n\n            this.options = options;\n            this.canvas = document.createElement('canvas');\n            this.ctx = this.canvas.getContext('2d');\n            this.canvas.width = Math.floor(options.width) * options.scale;\n            this.canvas.height = Math.floor(options.height) * options.scale;\n            this.canvas.style.width = options.width + 'px';\n            this.canvas.style.height = options.height + 'px';\n\n            options.logger.log('ForeignObject renderer initialized (' + options.width + 'x' + options.height + ' at ' + options.x + ',' + options.y + ') with scale ' + options.scale);\n            var svg = createForeignObjectSVG(Math.max(options.windowWidth, options.width) * options.scale, Math.max(options.windowHeight, options.height) * options.scale, options.scrollX * options.scale, options.scrollY * options.scale, this.element);\n\n            return loadSerializedSVG(svg).then(function (img) {\n                if (options.backgroundColor) {\n                    _this.ctx.fillStyle = options.backgroundColor.toString();\n                    _this.ctx.fillRect(0, 0, options.width * options.scale, options.height * options.scale);\n                }\n\n                _this.ctx.drawImage(img, -options.x * options.scale, -options.y * options.scale);\n                return _this.canvas;\n            });\n        }\n    }]);\n\n    return ForeignObjectRenderer;\n}();\n\nexports.default = ForeignObjectRenderer;\nvar createForeignObjectSVG = exports.createForeignObjectSVG = function createForeignObjectSVG(width, height, x, y, node) {\n    var xmlns = 'http://www.w3.org/2000/svg';\n    var svg = document.createElementNS(xmlns, 'svg');\n    var foreignObject = document.createElementNS(xmlns, 'foreignObject');\n    svg.setAttributeNS(null, 'width', width);\n    svg.setAttributeNS(null, 'height', height);\n\n    foreignObject.setAttributeNS(null, 'width', '100%');\n    foreignObject.setAttributeNS(null, 'height', '100%');\n    foreignObject.setAttributeNS(null, 'x', x);\n    foreignObject.setAttributeNS(null, 'y', y);\n    foreignObject.setAttributeNS(null, 'externalResourcesRequired', 'true');\n    svg.appendChild(foreignObject);\n\n    foreignObject.appendChild(node);\n\n    return svg;\n};\n\nvar loadSerializedSVG = exports.loadSerializedSVG = function loadSerializedSVG(svg) {\n    return new Promise(function (resolve, reject) {\n        var img = new Image();\n        img.onload = function () {\n            return resolve(img);\n        };\n        img.onerror = reject;\n\n        img.src = 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(new XMLSerializer().serializeToString(svg));\n    });\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.LineBreaker = exports.inlineBreakOpportunities = exports.lineBreakAtIndex = exports.codePointsToCharacterClasses = exports.UnicodeTrie = exports.BREAK_ALLOWED = exports.BREAK_NOT_ALLOWED = exports.BREAK_MANDATORY = exports.classes = exports.LETTER_NUMBER_MODIFIER = undefined;\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _slicedToArray = function () { function sliceIterator(arr, i) { var _arr = []; var _n = true; var _d = false; var _e = undefined; try { for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"]) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; } return function (arr, i) { if (Array.isArray(arr)) { return arr; } else if (Symbol.iterator in Object(arr)) { return sliceIterator(arr, i); } else { throw new TypeError(\"Invalid attempt to destructure non-iterable instance\"); } }; }();\n\nvar _Trie = require('./Trie');\n\nvar _linebreakTrie = require('./linebreak-trie');\n\nvar _linebreakTrie2 = _interopRequireDefault(_linebreakTrie);\n\nvar _Util = require('./Util');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _toConsumableArray(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } else { return Array.from(arr); } }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar LETTER_NUMBER_MODIFIER = exports.LETTER_NUMBER_MODIFIER = 50;\n\n// Non-tailorable Line Breaking Classes\nvar BK = 1; //  Cause a line break (after)\nvar CR = 2; //  Cause a line break (after), except between CR and LF\nvar LF = 3; //  Cause a line break (after)\nvar CM = 4; //  Prohibit a line break between the character and the preceding character\nvar NL = 5; //  Cause a line break (after)\nvar SG = 6; //  Do not occur in well-formed text\nvar WJ = 7; //  Prohibit line breaks before and after\nvar ZW = 8; //  Provide a break opportunity\nvar GL = 9; //  Prohibit line breaks before and after\nvar SP = 10; // Enable indirect line breaks\nvar ZWJ = 11; // Prohibit line breaks within joiner sequences\n// Break Opportunities\nvar B2 = 12; //  Provide a line break opportunity before and after the character\nvar BA = 13; //  Generally provide a line break opportunity after the character\nvar BB = 14; //  Generally provide a line break opportunity before the character\nvar HY = 15; //  Provide a line break opportunity after the character, except in numeric context\nvar CB = 16; //   Provide a line break opportunity contingent on additional information\n// Characters Prohibiting Certain Breaks\nvar CL = 17; //  Prohibit line breaks before\nvar CP = 18; //  Prohibit line breaks before\nvar EX = 19; //  Prohibit line breaks before\nvar IN = 20; //  Allow only indirect line breaks between pairs\nvar NS = 21; //  Allow only indirect line breaks before\nvar OP = 22; //  Prohibit line breaks after\nvar QU = 23; //  Act like they are both opening and closing\n// Numeric Context\nvar IS = 24; //  Prevent breaks after any and before numeric\nvar NU = 25; //  Form numeric expressions for line breaking purposes\nvar PO = 26; //  Do not break following a numeric expression\nvar PR = 27; //  Do not break in front of a numeric expression\nvar SY = 28; //  Prevent a break before; and allow a break after\n// Other Characters\nvar AI = 29; //  Act like AL when the resolvedEAW is N; otherwise; act as ID\nvar AL = 30; //  Are alphabetic characters or symbols that are used with alphabetic characters\nvar CJ = 31; //  Treat as NS or ID for strict or normal breaking.\nvar EB = 32; //  Do not break from following Emoji Modifier\nvar EM = 33; //  Do not break from preceding Emoji Base\nvar H2 = 34; //  Form Korean syllable blocks\nvar H3 = 35; //  Form Korean syllable blocks\nvar HL = 36; //  Do not break around a following hyphen; otherwise act as Alphabetic\nvar ID = 37; //  Break before or after; except in some numeric context\nvar JL = 38; //  Form Korean syllable blocks\nvar JV = 39; //  Form Korean syllable blocks\nvar JT = 40; //  Form Korean syllable blocks\nvar RI = 41; //  Keep pairs together. For pairs; break before and after other classes\nvar SA = 42; //  Provide a line break opportunity contingent on additional, language-specific context analysis\nvar XX = 43; //  Have as yet unknown line breaking behavior or unassigned code positions\n\nvar classes = exports.classes = {\n    BK: BK,\n    CR: CR,\n    LF: LF,\n    CM: CM,\n    NL: NL,\n    SG: SG,\n    WJ: WJ,\n    ZW: ZW,\n    GL: GL,\n    SP: SP,\n    ZWJ: ZWJ,\n    B2: B2,\n    BA: BA,\n    BB: BB,\n    HY: HY,\n    CB: CB,\n    CL: CL,\n    CP: CP,\n    EX: EX,\n    IN: IN,\n    NS: NS,\n    OP: OP,\n    QU: QU,\n    IS: IS,\n    NU: NU,\n    PO: PO,\n    PR: PR,\n    SY: SY,\n    AI: AI,\n    AL: AL,\n    CJ: CJ,\n    EB: EB,\n    EM: EM,\n    H2: H2,\n    H3: H3,\n    HL: HL,\n    ID: ID,\n    JL: JL,\n    JV: JV,\n    JT: JT,\n    RI: RI,\n    SA: SA,\n    XX: XX\n};\n\nvar BREAK_MANDATORY = exports.BREAK_MANDATORY = '!';\nvar BREAK_NOT_ALLOWED = exports.BREAK_NOT_ALLOWED = '×';\nvar BREAK_ALLOWED = exports.BREAK_ALLOWED = '÷';\nvar UnicodeTrie = exports.UnicodeTrie = (0, _Trie.createTrieFromBase64)(_linebreakTrie2.default);\n\nvar ALPHABETICS = [AL, HL];\nvar HARD_LINE_BREAKS = [BK, CR, LF, NL];\nvar SPACE = [SP, ZW];\nvar PREFIX_POSTFIX = [PR, PO];\nvar LINE_BREAKS = HARD_LINE_BREAKS.concat(SPACE);\nvar KOREAN_SYLLABLE_BLOCK = [JL, JV, JT, H2, H3];\nvar HYPHEN = [HY, BA];\n\nvar codePointsToCharacterClasses = exports.codePointsToCharacterClasses = function codePointsToCharacterClasses(codePoints) {\n    var lineBreak = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'strict';\n\n    var types = [];\n    var indicies = [];\n    var categories = [];\n    codePoints.forEach(function (codePoint, index) {\n        var classType = UnicodeTrie.get(codePoint);\n        if (classType > LETTER_NUMBER_MODIFIER) {\n            categories.push(true);\n            classType -= LETTER_NUMBER_MODIFIER;\n        } else {\n            categories.push(false);\n        }\n\n        if (['normal', 'auto', 'loose'].indexOf(lineBreak) !== -1) {\n            // U+2010, – U+2013, 〜 U+301C, ゠ U+30A0\n            if ([0x2010, 0x2013, 0x301c, 0x30a0].indexOf(codePoint) !== -1) {\n                indicies.push(index);\n                return types.push(CB);\n            }\n        }\n\n        if (classType === CM || classType === ZWJ) {\n            // LB10 Treat any remaining combining mark or ZWJ as AL.\n            if (index === 0) {\n                indicies.push(index);\n                return types.push(AL);\n            }\n\n            // LB9 Do not break a combining character sequence; treat it as if it has the line breaking class of\n            // the base character in all of the following rules. Treat ZWJ as if it were CM.\n            var prev = types[index - 1];\n            if (LINE_BREAKS.indexOf(prev) === -1) {\n                indicies.push(indicies[index - 1]);\n                return types.push(prev);\n            }\n            indicies.push(index);\n            return types.push(AL);\n        }\n\n        indicies.push(index);\n\n        if (classType === CJ) {\n            return types.push(lineBreak === 'strict' ? NS : ID);\n        }\n\n        if (classType === SA) {\n            return types.push(AL);\n        }\n\n        if (classType === AI) {\n            return types.push(AL);\n        }\n\n        // For supplementary characters, a useful default is to treat characters in the range 10000..1FFFD as AL\n        // and characters in the ranges 20000..2FFFD and 30000..3FFFD as ID, until the implementation can be revised\n        // to take into account the actual line breaking properties for these characters.\n        if (classType === XX) {\n            if (codePoint >= 0x20000 && codePoint <= 0x2fffd || codePoint >= 0x30000 && codePoint <= 0x3fffd) {\n                return types.push(ID);\n            } else {\n                return types.push(AL);\n            }\n        }\n\n        types.push(classType);\n    });\n\n    return [indicies, types, categories];\n};\n\nvar isAdjacentWithSpaceIgnored = function isAdjacentWithSpaceIgnored(a, b, currentIndex, classTypes) {\n    var current = classTypes[currentIndex];\n    if (Array.isArray(a) ? a.indexOf(current) !== -1 : a === current) {\n        var i = currentIndex;\n        while (i <= classTypes.length) {\n            i++;\n            var next = classTypes[i];\n\n            if (next === b) {\n                return true;\n            }\n\n            if (next !== SP) {\n                break;\n            }\n        }\n    }\n\n    if (current === SP) {\n        var _i = currentIndex;\n\n        while (_i > 0) {\n            _i--;\n            var prev = classTypes[_i];\n\n            if (Array.isArray(a) ? a.indexOf(prev) !== -1 : a === prev) {\n                var n = currentIndex;\n                while (n <= classTypes.length) {\n                    n++;\n                    var _next = classTypes[n];\n\n                    if (_next === b) {\n                        return true;\n                    }\n\n                    if (_next !== SP) {\n                        break;\n                    }\n                }\n            }\n\n            if (prev !== SP) {\n                break;\n            }\n        }\n    }\n    return false;\n};\n\nvar previousNonSpaceClassType = function previousNonSpaceClassType(currentIndex, classTypes) {\n    var i = currentIndex;\n    while (i >= 0) {\n        var type = classTypes[i];\n        if (type === SP) {\n            i--;\n        } else {\n            return type;\n        }\n    }\n    return 0;\n};\n\nvar _lineBreakAtIndex = function _lineBreakAtIndex(codePoints, classTypes, indicies, index, forbiddenBreaks) {\n    if (indicies[index] === 0) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    var currentIndex = index - 1;\n    if (Array.isArray(forbiddenBreaks) && forbiddenBreaks[currentIndex] === true) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    var beforeIndex = currentIndex - 1;\n    var afterIndex = currentIndex + 1;\n    var current = classTypes[currentIndex];\n\n    // LB4 Always break after hard line breaks.\n    // LB5 Treat CR followed by LF, as well as CR, LF, and NL as hard line breaks.\n    var before = beforeIndex >= 0 ? classTypes[beforeIndex] : 0;\n    var next = classTypes[afterIndex];\n\n    if (current === CR && next === LF) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    if (HARD_LINE_BREAKS.indexOf(current) !== -1) {\n        return BREAK_MANDATORY;\n    }\n\n    // LB6 Do not break before hard line breaks.\n    if (HARD_LINE_BREAKS.indexOf(next) !== -1) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB7 Do not break before spaces or zero width space.\n    if (SPACE.indexOf(next) !== -1) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB8 Break before any character following a zero-width space, even if one or more spaces intervene.\n    if (previousNonSpaceClassType(currentIndex, classTypes) === ZW) {\n        return BREAK_ALLOWED;\n    }\n\n    // LB8a Do not break between a zero width joiner and an ideograph, emoji base or emoji modifier.\n    if (UnicodeTrie.get(codePoints[currentIndex]) === ZWJ && (next === ID || next === EB || next === EM)) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB11 Do not break before or after Word joiner and related characters.\n    if (current === WJ || next === WJ) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB12 Do not break after NBSP and related characters.\n    if (current === GL) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB12a Do not break before NBSP and related characters, except after spaces and hyphens.\n    if ([SP, BA, HY].indexOf(current) === -1 && next === GL) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB13 Do not break before ‘]’ or ‘!’ or ‘;’ or ‘/’, even after spaces.\n    if ([CL, CP, EX, IS, SY].indexOf(next) !== -1) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB14 Do not break after ‘[’, even after spaces.\n    if (previousNonSpaceClassType(currentIndex, classTypes) === OP) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB15 Do not break within ‘”[’, even with intervening spaces.\n    if (isAdjacentWithSpaceIgnored(QU, OP, currentIndex, classTypes)) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB16 Do not break between closing punctuation and a nonstarter (lb=NS), even with intervening spaces.\n    if (isAdjacentWithSpaceIgnored([CL, CP], NS, currentIndex, classTypes)) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB17 Do not break within ‘——’, even with intervening spaces.\n    if (isAdjacentWithSpaceIgnored(B2, B2, currentIndex, classTypes)) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB18 Break after spaces.\n    if (current === SP) {\n        return BREAK_ALLOWED;\n    }\n\n    // LB19 Do not break before or after quotation marks, such as ‘ ” ’.\n    if (current === QU || next === QU) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB20 Break before and after unresolved CB.\n    if (next === CB || current === CB) {\n        return BREAK_ALLOWED;\n    }\n\n    // LB21 Do not break before hyphen-minus, other hyphens, fixed-width spaces, small kana, and other non-starters, or after acute accents.\n    if ([BA, HY, NS].indexOf(next) !== -1 || current === BB) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB21a Don't break after Hebrew + Hyphen.\n    if (before === HL && HYPHEN.indexOf(current) !== -1) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB21b Don’t break between Solidus and Hebrew letters.\n    if (current === SY && next === HL) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB22 Do not break between two ellipses, or between letters, numbers or exclamations and ellipsis.\n    if (next === IN && ALPHABETICS.concat(IN, EX, NU, ID, EB, EM).indexOf(current) !== -1) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB23 Do not break between digits and letters.\n    if (ALPHABETICS.indexOf(next) !== -1 && current === NU || ALPHABETICS.indexOf(current) !== -1 && next === NU) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB23a Do not break between numeric prefixes and ideographs, or between ideographs and numeric postfixes.\n    if (current === PR && [ID, EB, EM].indexOf(next) !== -1 || [ID, EB, EM].indexOf(current) !== -1 && next === PO) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB24 Do not break between numeric prefix/postfix and letters, or between letters and prefix/postfix.\n    if (ALPHABETICS.indexOf(current) !== -1 && PREFIX_POSTFIX.indexOf(next) !== -1 || PREFIX_POSTFIX.indexOf(current) !== -1 && ALPHABETICS.indexOf(next) !== -1) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB25 Do not break between the following pairs of classes relevant to numbers:\n    if (\n    // (PR | PO) × ( OP | HY )? NU\n    [PR, PO].indexOf(current) !== -1 && (next === NU || [OP, HY].indexOf(next) !== -1 && classTypes[afterIndex + 1] === NU) ||\n    // ( OP | HY ) × NU\n    [OP, HY].indexOf(current) !== -1 && next === NU ||\n    // NU ×\t(NU | SY | IS)\n    current === NU && [NU, SY, IS].indexOf(next) !== -1) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // NU (NU | SY | IS)* × (NU | SY | IS | CL | CP)\n    if ([NU, SY, IS, CL, CP].indexOf(next) !== -1) {\n        var prevIndex = currentIndex;\n        while (prevIndex >= 0) {\n            var type = classTypes[prevIndex];\n            if (type === NU) {\n                return BREAK_NOT_ALLOWED;\n            } else if ([SY, IS].indexOf(type) !== -1) {\n                prevIndex--;\n            } else {\n                break;\n            }\n        }\n    }\n\n    // NU (NU | SY | IS)* (CL | CP)? × (PO | PR))\n    if ([PR, PO].indexOf(next) !== -1) {\n        var _prevIndex = [CL, CP].indexOf(current) !== -1 ? beforeIndex : currentIndex;\n        while (_prevIndex >= 0) {\n            var _type = classTypes[_prevIndex];\n            if (_type === NU) {\n                return BREAK_NOT_ALLOWED;\n            } else if ([SY, IS].indexOf(_type) !== -1) {\n                _prevIndex--;\n            } else {\n                break;\n            }\n        }\n    }\n\n    // LB26 Do not break a Korean syllable.\n    if (JL === current && [JL, JV, H2, H3].indexOf(next) !== -1 || [JV, H2].indexOf(current) !== -1 && [JV, JT].indexOf(next) !== -1 || [JT, H3].indexOf(current) !== -1 && next === JT) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB27 Treat a Korean Syllable Block the same as ID.\n    if (KOREAN_SYLLABLE_BLOCK.indexOf(current) !== -1 && [IN, PO].indexOf(next) !== -1 || KOREAN_SYLLABLE_BLOCK.indexOf(next) !== -1 && current === PR) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB28 Do not break between alphabetics (“at”).\n    if (ALPHABETICS.indexOf(current) !== -1 && ALPHABETICS.indexOf(next) !== -1) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB29 Do not break between numeric punctuation and alphabetics (“e.g.”).\n    if (current === IS && ALPHABETICS.indexOf(next) !== -1) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB30 Do not break between letters, numbers, or ordinary symbols and opening or closing parentheses.\n    if (ALPHABETICS.concat(NU).indexOf(current) !== -1 && next === OP || ALPHABETICS.concat(NU).indexOf(next) !== -1 && current === CP) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB30a Break between two regional indicator symbols if and only if there are an even number of regional\n    // indicators preceding the position of the break.\n    if (current === RI && next === RI) {\n        var i = indicies[currentIndex];\n        var count = 1;\n        while (i > 0) {\n            i--;\n            if (classTypes[i] === RI) {\n                count++;\n            } else {\n                break;\n            }\n        }\n        if (count % 2 !== 0) {\n            return BREAK_NOT_ALLOWED;\n        }\n    }\n\n    // LB30b Do not break between an emoji base and an emoji modifier.\n    if (current === EB && next === EM) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    return BREAK_ALLOWED;\n};\n\nvar lineBreakAtIndex = exports.lineBreakAtIndex = function lineBreakAtIndex(codePoints, index) {\n    // LB2 Never break at the start of text.\n    if (index === 0) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB3 Always break at the end of text.\n    if (index >= codePoints.length) {\n        return BREAK_MANDATORY;\n    }\n\n    var _codePointsToCharacte = codePointsToCharacterClasses(codePoints),\n        _codePointsToCharacte2 = _slicedToArray(_codePointsToCharacte, 2),\n        indicies = _codePointsToCharacte2[0],\n        classTypes = _codePointsToCharacte2[1];\n\n    return _lineBreakAtIndex(codePoints, classTypes, indicies, index);\n};\n\nvar cssFormattedClasses = function cssFormattedClasses(codePoints, options) {\n    if (!options) {\n        options = { lineBreak: 'normal', wordBreak: 'normal' };\n    }\n\n    var _codePointsToCharacte3 = codePointsToCharacterClasses(codePoints, options.lineBreak),\n        _codePointsToCharacte4 = _slicedToArray(_codePointsToCharacte3, 3),\n        indicies = _codePointsToCharacte4[0],\n        classTypes = _codePointsToCharacte4[1],\n        isLetterNumber = _codePointsToCharacte4[2];\n\n    if (options.wordBreak === 'break-all' || options.wordBreak === 'break-word') {\n        classTypes = classTypes.map(function (type) {\n            return [NU, AL, SA].indexOf(type) !== -1 ? ID : type;\n        });\n    }\n\n    var forbiddenBreakpoints = options.wordBreak === 'keep-all' ? isLetterNumber.map(function (isLetterNumber, i) {\n        return isLetterNumber && codePoints[i] >= 0x4e00 && codePoints[i] <= 0x9fff;\n    }) : null;\n\n    return [indicies, classTypes, forbiddenBreakpoints];\n};\n\nvar inlineBreakOpportunities = exports.inlineBreakOpportunities = function inlineBreakOpportunities(str, options) {\n    var codePoints = (0, _Util.toCodePoints)(str);\n    var output = BREAK_NOT_ALLOWED;\n\n    var _cssFormattedClasses = cssFormattedClasses(codePoints, options),\n        _cssFormattedClasses2 = _slicedToArray(_cssFormattedClasses, 3),\n        indicies = _cssFormattedClasses2[0],\n        classTypes = _cssFormattedClasses2[1],\n        forbiddenBreakpoints = _cssFormattedClasses2[2];\n\n    codePoints.forEach(function (codePoint, i) {\n        output += (0, _Util.fromCodePoint)(codePoint) + (i >= codePoints.length - 1 ? BREAK_MANDATORY : _lineBreakAtIndex(codePoints, classTypes, indicies, i + 1, forbiddenBreakpoints));\n    });\n\n    return output;\n};\n\nvar Break = function () {\n    function Break(codePoints, lineBreak, start, end) {\n        _classCallCheck(this, Break);\n\n        this._codePoints = codePoints;\n        this.required = lineBreak === BREAK_MANDATORY;\n        this.start = start;\n        this.end = end;\n    }\n\n    _createClass(Break, [{\n        key: 'slice',\n        value: function slice() {\n            return _Util.fromCodePoint.apply(undefined, _toConsumableArray(this._codePoints.slice(this.start, this.end)));\n        }\n    }]);\n\n    return Break;\n}();\n\nvar LineBreaker = exports.LineBreaker = function LineBreaker(str, options) {\n    var codePoints = (0, _Util.toCodePoints)(str);\n\n    var _cssFormattedClasses3 = cssFormattedClasses(codePoints, options),\n        _cssFormattedClasses4 = _slicedToArray(_cssFormattedClasses3, 3),\n        indicies = _cssFormattedClasses4[0],\n        classTypes = _cssFormattedClasses4[1],\n        forbiddenBreakpoints = _cssFormattedClasses4[2];\n\n    var length = codePoints.length;\n    var lastEnd = 0;\n    var nextIndex = 0;\n\n    return {\n        next: function next() {\n            if (nextIndex >= length) {\n                return { done: true };\n            }\n            var lineBreak = BREAK_NOT_ALLOWED;\n            while (nextIndex < length && (lineBreak = _lineBreakAtIndex(codePoints, classTypes, indicies, ++nextIndex, forbiddenBreakpoints)) === BREAK_NOT_ALLOWED) {}\n\n            if (lineBreak !== BREAK_NOT_ALLOWED || nextIndex === length) {\n                var value = new Break(codePoints, lineBreak, lastEnd, nextIndex);\n                lastEnd = nextIndex;\n                return { value: value, done: false };\n            }\n\n            return { done: true };\n        }\n    };\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.createCounterText = exports.inlineListItemElement = exports.getListOwner = undefined;\n\nvar _Util = require('./Util');\n\nvar _NodeContainer = require('./NodeContainer');\n\nvar _NodeContainer2 = _interopRequireDefault(_NodeContainer);\n\nvar _TextContainer = require('./TextContainer');\n\nvar _TextContainer2 = _interopRequireDefault(_TextContainer);\n\nvar _listStyle = require('./parsing/listStyle');\n\nvar _Unicode = require('./Unicode');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n// Margin between the enumeration and the list item content\nvar MARGIN_RIGHT = 7;\n\nvar ancestorTypes = ['OL', 'UL', 'MENU'];\n\nvar getListOwner = exports.getListOwner = function getListOwner(container) {\n    var parent = container.parent;\n    if (!parent) {\n        return null;\n    }\n\n    do {\n        var isAncestor = ancestorTypes.indexOf(parent.tagName) !== -1;\n        if (isAncestor) {\n            return parent;\n        }\n        parent = parent.parent;\n    } while (parent);\n\n    return container.parent;\n};\n\nvar inlineListItemElement = exports.inlineListItemElement = function inlineListItemElement(node, container, resourceLoader) {\n    var listStyle = container.style.listStyle;\n\n    if (!listStyle) {\n        return;\n    }\n\n    var style = node.ownerDocument.defaultView.getComputedStyle(node, null);\n    var wrapper = node.ownerDocument.createElement('html2canvaswrapper');\n    (0, _Util.copyCSSStyles)(style, wrapper);\n\n    wrapper.style.position = 'absolute';\n    wrapper.style.bottom = 'auto';\n    wrapper.style.display = 'block';\n    wrapper.style.letterSpacing = 'normal';\n\n    switch (listStyle.listStylePosition) {\n        case _listStyle.LIST_STYLE_POSITION.OUTSIDE:\n            wrapper.style.left = 'auto';\n            wrapper.style.right = node.ownerDocument.defaultView.innerWidth - container.bounds.left - container.style.margin[1].getAbsoluteValue(container.bounds.width) + MARGIN_RIGHT + 'px';\n            wrapper.style.textAlign = 'right';\n            break;\n        case _listStyle.LIST_STYLE_POSITION.INSIDE:\n            wrapper.style.left = container.bounds.left - container.style.margin[3].getAbsoluteValue(container.bounds.width) + 'px';\n            wrapper.style.right = 'auto';\n            wrapper.style.textAlign = 'left';\n            break;\n    }\n\n    var text = void 0;\n    var MARGIN_TOP = container.style.margin[0].getAbsoluteValue(container.bounds.width);\n    var styleImage = listStyle.listStyleImage;\n    if (styleImage) {\n        if (styleImage.method === 'url') {\n            var image = node.ownerDocument.createElement('img');\n            image.src = styleImage.args[0];\n            wrapper.style.top = container.bounds.top - MARGIN_TOP + 'px';\n            wrapper.style.width = 'auto';\n            wrapper.style.height = 'auto';\n            wrapper.appendChild(image);\n        } else {\n            var size = parseFloat(container.style.font.fontSize) * 0.5;\n            wrapper.style.top = container.bounds.top - MARGIN_TOP + container.bounds.height - 1.5 * size + 'px';\n            wrapper.style.width = size + 'px';\n            wrapper.style.height = size + 'px';\n            wrapper.style.backgroundImage = style.listStyleImage;\n        }\n    } else if (typeof container.listIndex === 'number') {\n        text = node.ownerDocument.createTextNode(createCounterText(container.listIndex, listStyle.listStyleType, true));\n        wrapper.appendChild(text);\n        wrapper.style.top = container.bounds.top - MARGIN_TOP + 'px';\n    }\n\n    // $FlowFixMe\n    var body = node.ownerDocument.body;\n    body.appendChild(wrapper);\n\n    if (text) {\n        container.childNodes.push(_TextContainer2.default.fromTextNode(text, container));\n        body.removeChild(wrapper);\n    } else {\n        // $FlowFixMe\n        container.childNodes.push(new _NodeContainer2.default(wrapper, container, resourceLoader, 0));\n    }\n};\n\nvar ROMAN_UPPER = {\n    integers: [1000, 900, 500, 400, 100, 90, 50, 40, 10, 9, 5, 4, 1],\n    values: ['M', 'CM', 'D', 'CD', 'C', 'XC', 'L', 'XL', 'X', 'IX', 'V', 'IV', 'I']\n};\n\nvar ARMENIAN = {\n    integers: [9000, 8000, 7000, 6000, 5000, 4000, 3000, 2000, 1000, 900, 800, 700, 600, 500, 400, 300, 200, 100, 90, 80, 70, 60, 50, 40, 30, 20, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1],\n    values: ['Ք', 'Փ', 'Ւ', 'Ց', 'Ր', 'Տ', 'Վ', 'Ս', 'Ռ', 'Ջ', 'Պ', 'Չ', 'Ո', 'Շ', 'Ն', 'Յ', 'Մ', 'Ճ', 'Ղ', 'Ձ', 'Հ', 'Կ', 'Ծ', 'Խ', 'Լ', 'Ի', 'Ժ', 'Թ', 'Ը', 'Է', 'Զ', 'Ե', 'Դ', 'Գ', 'Բ', 'Ա']\n};\n\nvar HEBREW = {\n    integers: [10000, 9000, 8000, 7000, 6000, 5000, 4000, 3000, 2000, 1000, 400, 300, 200, 100, 90, 80, 70, 60, 50, 40, 30, 20, 19, 18, 17, 16, 15, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1],\n    values: ['י׳', 'ט׳', 'ח׳', 'ז׳', 'ו׳', 'ה׳', 'ד׳', 'ג׳', 'ב׳', 'א׳', 'ת', 'ש', 'ר', 'ק', 'צ', 'פ', 'ע', 'ס', 'נ', 'מ', 'ל', 'כ', 'יט', 'יח', 'יז', 'טז', 'טו', 'י', 'ט', 'ח', 'ז', 'ו', 'ה', 'ד', 'ג', 'ב', 'א']\n};\n\nvar GEORGIAN = {\n    integers: [10000, 9000, 8000, 7000, 6000, 5000, 4000, 3000, 2000, 1000, 900, 800, 700, 600, 500, 400, 300, 200, 100, 90, 80, 70, 60, 50, 40, 30, 20, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1],\n    values: ['ჵ', 'ჰ', 'ჯ', 'ჴ', 'ხ', 'ჭ', 'წ', 'ძ', 'ც', 'ჩ', 'შ', 'ყ', 'ღ', 'ქ', 'ფ', 'ჳ', 'ტ', 'ს', 'რ', 'ჟ', 'პ', 'ო', 'ჲ', 'ნ', 'მ', 'ლ', 'კ', 'ი', 'თ', 'ჱ', 'ზ', 'ვ', 'ე', 'დ', 'გ', 'ბ', 'ა']\n};\n\nvar createAdditiveCounter = function createAdditiveCounter(value, min, max, symbols, fallback, suffix) {\n    if (value < min || value > max) {\n        return createCounterText(value, fallback, suffix.length > 0);\n    }\n\n    return symbols.integers.reduce(function (string, integer, index) {\n        while (value >= integer) {\n            value -= integer;\n            string += symbols.values[index];\n        }\n        return string;\n    }, '') + suffix;\n};\n\nvar createCounterStyleWithSymbolResolver = function createCounterStyleWithSymbolResolver(value, codePointRangeLength, isNumeric, resolver) {\n    var string = '';\n\n    do {\n        if (!isNumeric) {\n            value--;\n        }\n        string = resolver(value) + string;\n        value /= codePointRangeLength;\n    } while (value * codePointRangeLength >= codePointRangeLength);\n\n    return string;\n};\n\nvar createCounterStyleFromRange = function createCounterStyleFromRange(value, codePointRangeStart, codePointRangeEnd, isNumeric, suffix) {\n    var codePointRangeLength = codePointRangeEnd - codePointRangeStart + 1;\n\n    return (value < 0 ? '-' : '') + (createCounterStyleWithSymbolResolver(Math.abs(value), codePointRangeLength, isNumeric, function (codePoint) {\n        return (0, _Unicode.fromCodePoint)(Math.floor(codePoint % codePointRangeLength) + codePointRangeStart);\n    }) + suffix);\n};\n\nvar createCounterStyleFromSymbols = function createCounterStyleFromSymbols(value, symbols) {\n    var suffix = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : '. ';\n\n    var codePointRangeLength = symbols.length;\n    return createCounterStyleWithSymbolResolver(Math.abs(value), codePointRangeLength, false, function (codePoint) {\n        return symbols[Math.floor(codePoint % codePointRangeLength)];\n    }) + suffix;\n};\n\nvar CJK_ZEROS = 1 << 0;\nvar CJK_TEN_COEFFICIENTS = 1 << 1;\nvar CJK_TEN_HIGH_COEFFICIENTS = 1 << 2;\nvar CJK_HUNDRED_COEFFICIENTS = 1 << 3;\n\nvar createCJKCounter = function createCJKCounter(value, numbers, multipliers, negativeSign, suffix, flags) {\n    if (value < -9999 || value > 9999) {\n        return createCounterText(value, _listStyle.LIST_STYLE_TYPE.CJK_DECIMAL, suffix.length > 0);\n    }\n    var tmp = Math.abs(value);\n    var string = suffix;\n\n    if (tmp === 0) {\n        return numbers[0] + string;\n    }\n\n    for (var digit = 0; tmp > 0 && digit <= 4; digit++) {\n        var coefficient = tmp % 10;\n\n        if (coefficient === 0 && (0, _Util.contains)(flags, CJK_ZEROS) && string !== '') {\n            string = numbers[coefficient] + string;\n        } else if (coefficient > 1 || coefficient === 1 && digit === 0 || coefficient === 1 && digit === 1 && (0, _Util.contains)(flags, CJK_TEN_COEFFICIENTS) || coefficient === 1 && digit === 1 && (0, _Util.contains)(flags, CJK_TEN_HIGH_COEFFICIENTS) && value > 100 || coefficient === 1 && digit > 1 && (0, _Util.contains)(flags, CJK_HUNDRED_COEFFICIENTS)) {\n            string = numbers[coefficient] + (digit > 0 ? multipliers[digit - 1] : '') + string;\n        } else if (coefficient === 1 && digit > 0) {\n            string = multipliers[digit - 1] + string;\n        }\n        tmp = Math.floor(tmp / 10);\n    }\n\n    return (value < 0 ? negativeSign : '') + string;\n};\n\nvar CHINESE_INFORMAL_MULTIPLIERS = '十百千萬';\nvar CHINESE_FORMAL_MULTIPLIERS = '拾佰仟萬';\nvar JAPANESE_NEGATIVE = 'マイナス';\nvar KOREAN_NEGATIVE = '마이너스 ';\n\nvar createCounterText = exports.createCounterText = function createCounterText(value, type, appendSuffix) {\n    var defaultSuffix = appendSuffix ? '. ' : '';\n    var cjkSuffix = appendSuffix ? '、' : '';\n    var koreanSuffix = appendSuffix ? ', ' : '';\n    switch (type) {\n        case _listStyle.LIST_STYLE_TYPE.DISC:\n            return '•';\n        case _listStyle.LIST_STYLE_TYPE.CIRCLE:\n            return '◦';\n        case _listStyle.LIST_STYLE_TYPE.SQUARE:\n            return '◾';\n        case _listStyle.LIST_STYLE_TYPE.DECIMAL_LEADING_ZERO:\n            var string = createCounterStyleFromRange(value, 48, 57, true, defaultSuffix);\n            return string.length < 4 ? '0' + string : string;\n        case _listStyle.LIST_STYLE_TYPE.CJK_DECIMAL:\n            return createCounterStyleFromSymbols(value, '〇一二三四五六七八九', cjkSuffix);\n        case _listStyle.LIST_STYLE_TYPE.LOWER_ROMAN:\n            return createAdditiveCounter(value, 1, 3999, ROMAN_UPPER, _listStyle.LIST_STYLE_TYPE.DECIMAL, defaultSuffix).toLowerCase();\n        case _listStyle.LIST_STYLE_TYPE.UPPER_ROMAN:\n            return createAdditiveCounter(value, 1, 3999, ROMAN_UPPER, _listStyle.LIST_STYLE_TYPE.DECIMAL, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.LOWER_GREEK:\n            return createCounterStyleFromRange(value, 945, 969, false, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.LOWER_ALPHA:\n            return createCounterStyleFromRange(value, 97, 122, false, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.UPPER_ALPHA:\n            return createCounterStyleFromRange(value, 65, 90, false, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.ARABIC_INDIC:\n            return createCounterStyleFromRange(value, 1632, 1641, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.ARMENIAN:\n        case _listStyle.LIST_STYLE_TYPE.UPPER_ARMENIAN:\n            return createAdditiveCounter(value, 1, 9999, ARMENIAN, _listStyle.LIST_STYLE_TYPE.DECIMAL, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.LOWER_ARMENIAN:\n            return createAdditiveCounter(value, 1, 9999, ARMENIAN, _listStyle.LIST_STYLE_TYPE.DECIMAL, defaultSuffix).toLowerCase();\n        case _listStyle.LIST_STYLE_TYPE.BENGALI:\n            return createCounterStyleFromRange(value, 2534, 2543, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.CAMBODIAN:\n        case _listStyle.LIST_STYLE_TYPE.KHMER:\n            return createCounterStyleFromRange(value, 6112, 6121, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.CJK_EARTHLY_BRANCH:\n            return createCounterStyleFromSymbols(value, '子丑寅卯辰巳午未申酉戌亥', cjkSuffix);\n        case _listStyle.LIST_STYLE_TYPE.CJK_HEAVENLY_STEM:\n            return createCounterStyleFromSymbols(value, '甲乙丙丁戊己庚辛壬癸', cjkSuffix);\n        case _listStyle.LIST_STYLE_TYPE.CJK_IDEOGRAPHIC:\n        case _listStyle.LIST_STYLE_TYPE.TRAD_CHINESE_INFORMAL:\n            return createCJKCounter(value, '零一二三四五六七八九', CHINESE_INFORMAL_MULTIPLIERS, '負', cjkSuffix, CJK_TEN_COEFFICIENTS | CJK_TEN_HIGH_COEFFICIENTS | CJK_HUNDRED_COEFFICIENTS);\n        case _listStyle.LIST_STYLE_TYPE.TRAD_CHINESE_FORMAL:\n            return createCJKCounter(value, '零壹貳參肆伍陸柒捌玖', CHINESE_FORMAL_MULTIPLIERS, '負', cjkSuffix, CJK_ZEROS | CJK_TEN_COEFFICIENTS | CJK_TEN_HIGH_COEFFICIENTS | CJK_HUNDRED_COEFFICIENTS);\n        case _listStyle.LIST_STYLE_TYPE.SIMP_CHINESE_INFORMAL:\n            return createCJKCounter(value, '零一二三四五六七八九', CHINESE_INFORMAL_MULTIPLIERS, '负', cjkSuffix, CJK_TEN_COEFFICIENTS | CJK_TEN_HIGH_COEFFICIENTS | CJK_HUNDRED_COEFFICIENTS);\n        case _listStyle.LIST_STYLE_TYPE.SIMP_CHINESE_FORMAL:\n            return createCJKCounter(value, '零壹贰叁肆伍陆柒捌玖', CHINESE_FORMAL_MULTIPLIERS, '负', cjkSuffix, CJK_ZEROS | CJK_TEN_COEFFICIENTS | CJK_TEN_HIGH_COEFFICIENTS | CJK_HUNDRED_COEFFICIENTS);\n        case _listStyle.LIST_STYLE_TYPE.JAPANESE_INFORMAL:\n            return createCJKCounter(value, '〇一二三四五六七八九', '十百千万', JAPANESE_NEGATIVE, cjkSuffix, 0);\n        case _listStyle.LIST_STYLE_TYPE.JAPANESE_FORMAL:\n            return createCJKCounter(value, '零壱弐参四伍六七八九', '拾百千万', JAPANESE_NEGATIVE, cjkSuffix, CJK_ZEROS | CJK_TEN_COEFFICIENTS | CJK_TEN_HIGH_COEFFICIENTS);\n        case _listStyle.LIST_STYLE_TYPE.KOREAN_HANGUL_FORMAL:\n            return createCJKCounter(value, '영일이삼사오육칠팔구', '십백천만', KOREAN_NEGATIVE, koreanSuffix, CJK_ZEROS | CJK_TEN_COEFFICIENTS | CJK_TEN_HIGH_COEFFICIENTS);\n        case _listStyle.LIST_STYLE_TYPE.KOREAN_HANJA_INFORMAL:\n            return createCJKCounter(value, '零一二三四五六七八九', '十百千萬', KOREAN_NEGATIVE, koreanSuffix, 0);\n        case _listStyle.LIST_STYLE_TYPE.KOREAN_HANJA_FORMAL:\n            return createCJKCounter(value, '零壹貳參四五六七八九', '拾百千', KOREAN_NEGATIVE, koreanSuffix, CJK_ZEROS | CJK_TEN_COEFFICIENTS | CJK_TEN_HIGH_COEFFICIENTS);\n        case _listStyle.LIST_STYLE_TYPE.DEVANAGARI:\n            return createCounterStyleFromRange(value, 0x966, 0x96f, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.GEORGIAN:\n            return createAdditiveCounter(value, 1, 19999, GEORGIAN, _listStyle.LIST_STYLE_TYPE.DECIMAL, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.GUJARATI:\n            return createCounterStyleFromRange(value, 0xae6, 0xaef, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.GURMUKHI:\n            return createCounterStyleFromRange(value, 0xa66, 0xa6f, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.HEBREW:\n            return createAdditiveCounter(value, 1, 10999, HEBREW, _listStyle.LIST_STYLE_TYPE.DECIMAL, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.HIRAGANA:\n            return createCounterStyleFromSymbols(value, 'あいうえおかきくけこさしすせそたちつてとなにぬねのはひふへほまみむめもやゆよらりるれろわゐゑをん');\n        case _listStyle.LIST_STYLE_TYPE.HIRAGANA_IROHA:\n            return createCounterStyleFromSymbols(value, 'いろはにほへとちりぬるをわかよたれそつねならむうゐのおくやまけふこえてあさきゆめみしゑひもせす');\n        case _listStyle.LIST_STYLE_TYPE.KANNADA:\n            return createCounterStyleFromRange(value, 0xce6, 0xcef, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.KATAKANA:\n            return createCounterStyleFromSymbols(value, 'アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヰヱヲン', cjkSuffix);\n        case _listStyle.LIST_STYLE_TYPE.KATAKANA_IROHA:\n            return createCounterStyleFromSymbols(value, 'イロハニホヘトチリヌルヲワカヨタレソツネナラムウヰノオクヤマケフコエテアサキユメミシヱヒモセス', cjkSuffix);\n        case _listStyle.LIST_STYLE_TYPE.LAO:\n            return createCounterStyleFromRange(value, 0xed0, 0xed9, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.MONGOLIAN:\n            return createCounterStyleFromRange(value, 0x1810, 0x1819, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.MYANMAR:\n            return createCounterStyleFromRange(value, 0x1040, 0x1049, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.ORIYA:\n            return createCounterStyleFromRange(value, 0xb66, 0xb6f, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.PERSIAN:\n            return createCounterStyleFromRange(value, 0x6f0, 0x6f9, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.TAMIL:\n            return createCounterStyleFromRange(value, 0xbe6, 0xbef, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.TELUGU:\n            return createCounterStyleFromRange(value, 0xc66, 0xc6f, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.THAI:\n            return createCounterStyleFromRange(value, 0xe50, 0xe59, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.TIBETAN:\n            return createCounterStyleFromRange(value, 0xf20, 0xf29, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.DECIMAL:\n        default:\n            return createCounterStyleFromRange(value, 48, 57, true, defaultSuffix);\n    }\n};", "'use strict';\n\n// http://dev.w3.org/csswg/css-color/\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\nvar _slicedToArray = function () { function sliceIterator(arr, i) { var _arr = []; var _n = true; var _d = false; var _e = undefined; try { for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"]) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; } return function (arr, i) { if (Array.isArray(arr)) { return arr; } else if (Symbol.iterator in Object(arr)) { return sliceIterator(arr, i); } else { throw new TypeError(\"Invalid attempt to destructure non-iterable instance\"); } }; }();\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar HEX3 = /^#([a-f0-9]{3})$/i;\nvar hex3 = function hex3(value) {\n    var match = value.match(HEX3);\n    if (match) {\n        return [parseInt(match[1][0] + match[1][0], 16), parseInt(match[1][1] + match[1][1], 16), parseInt(match[1][2] + match[1][2], 16), null];\n    }\n    return false;\n};\n\nvar HEX6 = /^#([a-f0-9]{6})$/i;\nvar hex6 = function hex6(value) {\n    var match = value.match(HEX6);\n    if (match) {\n        return [parseInt(match[1].substring(0, 2), 16), parseInt(match[1].substring(2, 4), 16), parseInt(match[1].substring(4, 6), 16), null];\n    }\n    return false;\n};\n\nvar RGB = /^rgb\\(\\s*(\\d{1,3})\\s*,\\s*(\\d{1,3})\\s*,\\s*(\\d{1,3})\\s*\\)$/;\nvar rgb = function rgb(value) {\n    var match = value.match(RGB);\n    if (match) {\n        return [Number(match[1]), Number(match[2]), Number(match[3]), null];\n    }\n    return false;\n};\n\nvar RGBA = /^rgba\\(\\s*(\\d{1,3})\\s*,\\s*(\\d{1,3})\\s*,\\s*(\\d{1,3})\\s*,\\s*(\\d?\\.?\\d+)\\s*\\)$/;\nvar rgba = function rgba(value) {\n    var match = value.match(RGBA);\n    if (match && match.length > 4) {\n        return [Number(match[1]), Number(match[2]), Number(match[3]), Number(match[4])];\n    }\n    return false;\n};\n\nvar fromArray = function fromArray(array) {\n    return [Math.min(array[0], 255), Math.min(array[1], 255), Math.min(array[2], 255), array.length > 3 ? array[3] : null];\n};\n\nvar namedColor = function namedColor(name) {\n    var color = NAMED_COLORS[name.toLowerCase()];\n    return color ? color : false;\n};\n\nvar Color = function () {\n    function Color(value) {\n        _classCallCheck(this, Color);\n\n        var _ref = Array.isArray(value) ? fromArray(value) : hex3(value) || rgb(value) || rgba(value) || namedColor(value) || hex6(value) || [0, 0, 0, null],\n            _ref2 = _slicedToArray(_ref, 4),\n            r = _ref2[0],\n            g = _ref2[1],\n            b = _ref2[2],\n            a = _ref2[3];\n\n        this.r = r;\n        this.g = g;\n        this.b = b;\n        this.a = a;\n    }\n\n    _createClass(Color, [{\n        key: 'isTransparent',\n        value: function isTransparent() {\n            return this.a === 0;\n        }\n    }, {\n        key: 'toString',\n        value: function toString() {\n            return this.a !== null && this.a !== 1 ? 'rgba(' + this.r + ',' + this.g + ',' + this.b + ',' + this.a + ')' : 'rgb(' + this.r + ',' + this.g + ',' + this.b + ')';\n        }\n    }]);\n\n    return Color;\n}();\n\nexports.default = Color;\n\n\nvar NAMED_COLORS = {\n    transparent: [0, 0, 0, 0],\n    aliceblue: [240, 248, 255, null],\n    antiquewhite: [250, 235, 215, null],\n    aqua: [0, 255, 255, null],\n    aquamarine: [127, 255, 212, null],\n    azure: [240, 255, 255, null],\n    beige: [245, 245, 220, null],\n    bisque: [255, 228, 196, null],\n    black: [0, 0, 0, null],\n    blanchedalmond: [255, 235, 205, null],\n    blue: [0, 0, 255, null],\n    blueviolet: [138, 43, 226, null],\n    brown: [165, 42, 42, null],\n    burlywood: [222, 184, 135, null],\n    cadetblue: [95, 158, 160, null],\n    chartreuse: [127, 255, 0, null],\n    chocolate: [210, 105, 30, null],\n    coral: [255, 127, 80, null],\n    cornflowerblue: [100, 149, 237, null],\n    cornsilk: [255, 248, 220, null],\n    crimson: [220, 20, 60, null],\n    cyan: [0, 255, 255, null],\n    darkblue: [0, 0, 139, null],\n    darkcyan: [0, 139, 139, null],\n    darkgoldenrod: [184, 134, 11, null],\n    darkgray: [169, 169, 169, null],\n    darkgreen: [0, 100, 0, null],\n    darkgrey: [169, 169, 169, null],\n    darkkhaki: [189, 183, 107, null],\n    darkmagenta: [139, 0, 139, null],\n    darkolivegreen: [85, 107, 47, null],\n    darkorange: [255, 140, 0, null],\n    darkorchid: [153, 50, 204, null],\n    darkred: [139, 0, 0, null],\n    darksalmon: [233, 150, 122, null],\n    darkseagreen: [143, 188, 143, null],\n    darkslateblue: [72, 61, 139, null],\n    darkslategray: [47, 79, 79, null],\n    darkslategrey: [47, 79, 79, null],\n    darkturquoise: [0, 206, 209, null],\n    darkviolet: [148, 0, 211, null],\n    deeppink: [255, 20, 147, null],\n    deepskyblue: [0, 191, 255, null],\n    dimgray: [105, 105, 105, null],\n    dimgrey: [105, 105, 105, null],\n    dodgerblue: [30, 144, 255, null],\n    firebrick: [178, 34, 34, null],\n    floralwhite: [255, 250, 240, null],\n    forestgreen: [34, 139, 34, null],\n    fuchsia: [255, 0, 255, null],\n    gainsboro: [220, 220, 220, null],\n    ghostwhite: [248, 248, 255, null],\n    gold: [255, 215, 0, null],\n    goldenrod: [218, 165, 32, null],\n    gray: [128, 128, 128, null],\n    green: [0, 128, 0, null],\n    greenyellow: [173, 255, 47, null],\n    grey: [128, 128, 128, null],\n    honeydew: [240, 255, 240, null],\n    hotpink: [255, 105, 180, null],\n    indianred: [205, 92, 92, null],\n    indigo: [75, 0, 130, null],\n    ivory: [255, 255, 240, null],\n    khaki: [240, 230, 140, null],\n    lavender: [230, 230, 250, null],\n    lavenderblush: [255, 240, 245, null],\n    lawngreen: [124, 252, 0, null],\n    lemonchiffon: [255, 250, 205, null],\n    lightblue: [173, 216, 230, null],\n    lightcoral: [240, 128, 128, null],\n    lightcyan: [224, 255, 255, null],\n    lightgoldenrodyellow: [250, 250, 210, null],\n    lightgray: [211, 211, 211, null],\n    lightgreen: [144, 238, 144, null],\n    lightgrey: [211, 211, 211, null],\n    lightpink: [255, 182, 193, null],\n    lightsalmon: [255, 160, 122, null],\n    lightseagreen: [32, 178, 170, null],\n    lightskyblue: [135, 206, 250, null],\n    lightslategray: [119, 136, 153, null],\n    lightslategrey: [119, 136, 153, null],\n    lightsteelblue: [176, 196, 222, null],\n    lightyellow: [255, 255, 224, null],\n    lime: [0, 255, 0, null],\n    limegreen: [50, 205, 50, null],\n    linen: [250, 240, 230, null],\n    magenta: [255, 0, 255, null],\n    maroon: [128, 0, 0, null],\n    mediumaquamarine: [102, 205, 170, null],\n    mediumblue: [0, 0, 205, null],\n    mediumorchid: [186, 85, 211, null],\n    mediumpurple: [147, 112, 219, null],\n    mediumseagreen: [60, 179, 113, null],\n    mediumslateblue: [123, 104, 238, null],\n    mediumspringgreen: [0, 250, 154, null],\n    mediumturquoise: [72, 209, 204, null],\n    mediumvioletred: [199, 21, 133, null],\n    midnightblue: [25, 25, 112, null],\n    mintcream: [245, 255, 250, null],\n    mistyrose: [255, 228, 225, null],\n    moccasin: [255, 228, 181, null],\n    navajowhite: [255, 222, 173, null],\n    navy: [0, 0, 128, null],\n    oldlace: [253, 245, 230, null],\n    olive: [128, 128, 0, null],\n    olivedrab: [107, 142, 35, null],\n    orange: [255, 165, 0, null],\n    orangered: [255, 69, 0, null],\n    orchid: [218, 112, 214, null],\n    palegoldenrod: [238, 232, 170, null],\n    palegreen: [152, 251, 152, null],\n    paleturquoise: [175, 238, 238, null],\n    palevioletred: [219, 112, 147, null],\n    papayawhip: [255, 239, 213, null],\n    peachpuff: [255, 218, 185, null],\n    peru: [205, 133, 63, null],\n    pink: [255, 192, 203, null],\n    plum: [221, 160, 221, null],\n    powderblue: [176, 224, 230, null],\n    purple: [128, 0, 128, null],\n    rebeccapurple: [102, 51, 153, null],\n    red: [255, 0, 0, null],\n    rosybrown: [188, 143, 143, null],\n    royalblue: [65, 105, 225, null],\n    saddlebrown: [139, 69, 19, null],\n    salmon: [250, 128, 114, null],\n    sandybrown: [244, 164, 96, null],\n    seagreen: [46, 139, 87, null],\n    seashell: [255, 245, 238, null],\n    sienna: [160, 82, 45, null],\n    silver: [192, 192, 192, null],\n    skyblue: [135, 206, 235, null],\n    slateblue: [106, 90, 205, null],\n    slategray: [112, 128, 144, null],\n    slategrey: [112, 128, 144, null],\n    snow: [255, 250, 250, null],\n    springgreen: [0, 255, 127, null],\n    steelblue: [70, 130, 180, null],\n    tan: [210, 180, 140, null],\n    teal: [0, 128, 128, null],\n    thistle: [216, 191, 216, null],\n    tomato: [255, 99, 71, null],\n    turquoise: [64, 224, 208, null],\n    violet: [238, 130, 238, null],\n    wheat: [245, 222, 179, null],\n    white: [255, 255, 255, null],\n    whitesmoke: [245, 245, 245, null],\n    yellow: [255, 255, 0, null],\n    yellowgreen: [154, 205, 50, null]\n};\n\nvar TRANSPARENT = exports.TRANSPARENT = new Color([0, 0, 0, 0]);", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _Path = require('../drawing/Path');\n\nvar _textDecoration = require('../parsing/textDecoration');\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar addColorStops = function addColorStops(gradient, canvasGradient) {\n    var maxStop = Math.max.apply(null, gradient.colorStops.map(function (colorStop) {\n        return colorStop.stop;\n    }));\n    var f = 1 / Math.max(1, maxStop);\n    gradient.colorStops.forEach(function (colorStop) {\n        canvasGradient.addColorStop(f * colorStop.stop, colorStop.color.toString());\n    });\n};\n\nvar CanvasRenderer = function () {\n    function CanvasRenderer(canvas) {\n        _classCallCheck(this, CanvasRenderer);\n\n        this.canvas = canvas ? canvas : document.createElement('canvas');\n        this.customCanvas = !!canvas;\n    }\n\n    _createClass(CanvasRenderer, [{\n        key: 'render',\n        value: function render(options) {\n            this.ctx = this.canvas.getContext('2d');\n            this.options = options;\n            if (!this.customCanvas) {\n                this.canvas.width = Math.floor(options.width * options.scale);\n                this.canvas.height = Math.floor(options.height * options.scale);\n                this.canvas.style.width = options.width + 'px';\n                this.canvas.style.height = options.height + 'px';\n            }\n\n            this.ctx.scale(this.options.scale, this.options.scale);\n            this.ctx.translate(-options.x, -options.y);\n            this.ctx.textBaseline = 'bottom';\n            options.logger.log('Canvas renderer initialized (' + options.width + 'x' + options.height + ' at ' + options.x + ',' + options.y + ') with scale ' + this.options.scale);\n        }\n    }, {\n        key: 'clip',\n        value: function clip(clipPaths, callback) {\n            var _this = this;\n\n            if (clipPaths.length) {\n                this.ctx.save();\n                clipPaths.forEach(function (path) {\n                    _this.path(path);\n                    _this.ctx.clip();\n                });\n            }\n\n            callback();\n\n            if (clipPaths.length) {\n                this.ctx.restore();\n            }\n        }\n    }, {\n        key: 'drawImage',\n        value: function drawImage(image, source, destination) {\n            this.ctx.drawImage(image, source.left, source.top, source.width, source.height, destination.left, destination.top, destination.width, destination.height);\n        }\n    }, {\n        key: 'drawShape',\n        value: function drawShape(path, color) {\n            this.path(path);\n            this.ctx.fillStyle = color.toString();\n            this.ctx.fill();\n        }\n    }, {\n        key: 'fill',\n        value: function fill(color) {\n            this.ctx.fillStyle = color.toString();\n            this.ctx.fill();\n        }\n    }, {\n        key: 'getTarget',\n        value: function getTarget() {\n            return Promise.resolve(this.canvas);\n        }\n    }, {\n        key: 'path',\n        value: function path(_path) {\n            var _this2 = this;\n\n            this.ctx.beginPath();\n            if (Array.isArray(_path)) {\n                _path.forEach(function (point, index) {\n                    var start = point.type === _Path.PATH.VECTOR ? point : point.start;\n                    if (index === 0) {\n                        _this2.ctx.moveTo(start.x, start.y);\n                    } else {\n                        _this2.ctx.lineTo(start.x, start.y);\n                    }\n\n                    if (point.type === _Path.PATH.BEZIER_CURVE) {\n                        _this2.ctx.bezierCurveTo(point.startControl.x, point.startControl.y, point.endControl.x, point.endControl.y, point.end.x, point.end.y);\n                    }\n                });\n            } else {\n                this.ctx.arc(_path.x + _path.radius, _path.y + _path.radius, _path.radius, 0, Math.PI * 2, true);\n            }\n\n            this.ctx.closePath();\n        }\n    }, {\n        key: 'rectangle',\n        value: function rectangle(x, y, width, height, color) {\n            this.ctx.fillStyle = color.toString();\n            this.ctx.fillRect(x, y, width, height);\n        }\n    }, {\n        key: 'renderLinearGradient',\n        value: function renderLinearGradient(bounds, gradient) {\n            var linearGradient = this.ctx.createLinearGradient(bounds.left + gradient.direction.x1, bounds.top + gradient.direction.y1, bounds.left + gradient.direction.x0, bounds.top + gradient.direction.y0);\n\n            addColorStops(gradient, linearGradient);\n            this.ctx.fillStyle = linearGradient;\n            this.ctx.fillRect(bounds.left, bounds.top, bounds.width, bounds.height);\n        }\n    }, {\n        key: 'renderRadialGradient',\n        value: function renderRadialGradient(bounds, gradient) {\n            var _this3 = this;\n\n            var x = bounds.left + gradient.center.x;\n            var y = bounds.top + gradient.center.y;\n\n            var radialGradient = this.ctx.createRadialGradient(x, y, 0, x, y, gradient.radius.x);\n            if (!radialGradient) {\n                return;\n            }\n\n            addColorStops(gradient, radialGradient);\n            this.ctx.fillStyle = radialGradient;\n\n            if (gradient.radius.x !== gradient.radius.y) {\n                // transforms for elliptical radial gradient\n                var midX = bounds.left + 0.5 * bounds.width;\n                var midY = bounds.top + 0.5 * bounds.height;\n                var f = gradient.radius.y / gradient.radius.x;\n                var invF = 1 / f;\n\n                this.transform(midX, midY, [1, 0, 0, f, 0, 0], function () {\n                    return _this3.ctx.fillRect(bounds.left, invF * (bounds.top - midY) + midY, bounds.width, bounds.height * invF);\n                });\n            } else {\n                this.ctx.fillRect(bounds.left, bounds.top, bounds.width, bounds.height);\n            }\n        }\n    }, {\n        key: 'renderRepeat',\n        value: function renderRepeat(path, image, imageSize, offsetX, offsetY) {\n            this.path(path);\n            this.ctx.fillStyle = this.ctx.createPattern(this.resizeImage(image, imageSize), 'repeat');\n            this.ctx.translate(offsetX, offsetY);\n            this.ctx.fill();\n            this.ctx.translate(-offsetX, -offsetY);\n        }\n    }, {\n        key: 'renderTextNode',\n        value: function renderTextNode(textBounds, color, font, textDecoration, textShadows) {\n            var _this4 = this;\n\n            this.ctx.font = [font.fontStyle, font.fontVariant, font.fontWeight, font.fontSize, font.fontFamily].join(' ');\n\n            textBounds.forEach(function (text) {\n                _this4.ctx.fillStyle = color.toString();\n                if (textShadows && text.text.trim().length) {\n                    textShadows.slice(0).reverse().forEach(function (textShadow) {\n                        _this4.ctx.shadowColor = textShadow.color.toString();\n                        _this4.ctx.shadowOffsetX = textShadow.offsetX * _this4.options.scale;\n                        _this4.ctx.shadowOffsetY = textShadow.offsetY * _this4.options.scale;\n                        _this4.ctx.shadowBlur = textShadow.blur;\n\n                        _this4.ctx.fillText(text.text, text.bounds.left, text.bounds.top + text.bounds.height);\n                    });\n                } else {\n                    _this4.ctx.fillText(text.text, text.bounds.left, text.bounds.top + text.bounds.height);\n                }\n\n                if (textDecoration !== null) {\n                    var textDecorationColor = textDecoration.textDecorationColor || color;\n                    textDecoration.textDecorationLine.forEach(function (textDecorationLine) {\n                        switch (textDecorationLine) {\n                            case _textDecoration.TEXT_DECORATION_LINE.UNDERLINE:\n                                // Draws a line at the baseline of the font\n                                // TODO As some browsers display the line as more than 1px if the font-size is big,\n                                // need to take that into account both in position and size\n                                var _options$fontMetrics$ = _this4.options.fontMetrics.getMetrics(font),\n                                    baseline = _options$fontMetrics$.baseline;\n\n                                _this4.rectangle(text.bounds.left, Math.round(text.bounds.top + baseline), text.bounds.width, 1, textDecorationColor);\n                                break;\n                            case _textDecoration.TEXT_DECORATION_LINE.OVERLINE:\n                                _this4.rectangle(text.bounds.left, Math.round(text.bounds.top), text.bounds.width, 1, textDecorationColor);\n                                break;\n                            case _textDecoration.TEXT_DECORATION_LINE.LINE_THROUGH:\n                                // TODO try and find exact position for line-through\n                                var _options$fontMetrics$2 = _this4.options.fontMetrics.getMetrics(font),\n                                    middle = _options$fontMetrics$2.middle;\n\n                                _this4.rectangle(text.bounds.left, Math.ceil(text.bounds.top + middle), text.bounds.width, 1, textDecorationColor);\n                                break;\n                        }\n                    });\n                }\n            });\n        }\n    }, {\n        key: 'resizeImage',\n        value: function resizeImage(image, size) {\n            if (image.width === size.width && image.height === size.height) {\n                return image;\n            }\n\n            var canvas = this.canvas.ownerDocument.createElement('canvas');\n            canvas.width = size.width;\n            canvas.height = size.height;\n            var ctx = canvas.getContext('2d');\n            ctx.drawImage(image, 0, 0, image.width, image.height, 0, 0, size.width, size.height);\n            return canvas;\n        }\n    }, {\n        key: 'setOpacity',\n        value: function setOpacity(opacity) {\n            this.ctx.globalAlpha = opacity;\n        }\n    }, {\n        key: 'transform',\n        value: function transform(offsetX, offsetY, matrix, callback) {\n            this.ctx.save();\n            this.ctx.translate(offsetX, offsetY);\n            this.ctx.transform(matrix[0], matrix[1], matrix[2], matrix[3], matrix[4], matrix[5]);\n            this.ctx.translate(-offsetX, -offsetY);\n\n            callback();\n\n            this.ctx.restore();\n        }\n    }]);\n\n    return CanvasRenderer;\n}();\n\nexports.default = CanvasRenderer;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nvar VISIBILITY = exports.VISIBILITY = {\n    VISIBLE: 0,\n    HIDDEN: 1,\n    COLLAPSE: 2\n};\n\nvar parseVisibility = exports.parseVisibility = function parseVisibility(visibility) {\n    switch (visibility) {\n        case 'hidden':\n            return VISIBILITY.HIDDEN;\n        case 'collapse':\n            return VISIBILITY.COLLAPSE;\n        case 'visible':\n        default:\n            return VISIBILITY.VISIBLE;\n    }\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nvar parseLetterSpacing = exports.parseLetterSpacing = function parseLetterSpacing(letterSpacing) {\n    if (letterSpacing === 'normal') {\n        return 0;\n    }\n    var value = parseFloat(letterSpacing);\n    return isNaN(value) ? 0 : value;\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\nvar _slicedToArray = function () { function sliceIterator(arr, i) { var _arr = []; var _n = true; var _d = false; var _e = undefined; try { for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"]) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; } return function (arr, i) { if (Array.isArray(arr)) { return arr; } else if (Symbol.iterator in Object(arr)) { return sliceIterator(arr, i); } else { throw new TypeError(\"Invalid attempt to destructure non-iterable instance\"); } }; }();\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _Bounds = require('./Bounds');\n\nvar _Font = require('./Font');\n\nvar _Gradient = require('./Gradient');\n\nvar _TextContainer = require('./TextContainer');\n\nvar _TextContainer2 = _interopRequireDefault(_TextContainer);\n\nvar _background = require('./parsing/background');\n\nvar _border = require('./parsing/border');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar Renderer = function () {\n    function Renderer(target, options) {\n        _classCallCheck(this, Renderer);\n\n        this.target = target;\n        this.options = options;\n        target.render(options);\n    }\n\n    _createClass(Renderer, [{\n        key: 'renderNode',\n        value: function renderNode(container) {\n            if (container.isVisible()) {\n                this.renderNodeBackgroundAndBorders(container);\n                this.renderNodeContent(container);\n            }\n        }\n    }, {\n        key: 'renderNodeContent',\n        value: function renderNodeContent(container) {\n            var _this = this;\n\n            var callback = function callback() {\n                if (container.childNodes.length) {\n                    container.childNodes.forEach(function (child) {\n                        if (child instanceof _TextContainer2.default) {\n                            var style = child.parent.style;\n                            _this.target.renderTextNode(child.bounds, style.color, style.font, style.textDecoration, style.textShadow);\n                        } else {\n                            _this.target.drawShape(child, container.style.color);\n                        }\n                    });\n                }\n\n                if (container.image) {\n                    var _image = _this.options.imageStore.get(container.image);\n                    if (_image) {\n                        var contentBox = (0, _Bounds.calculateContentBox)(container.bounds, container.style.padding, container.style.border);\n                        var _width = typeof _image.width === 'number' && _image.width > 0 ? _image.width : contentBox.width;\n                        var _height = typeof _image.height === 'number' && _image.height > 0 ? _image.height : contentBox.height;\n                        if (_width > 0 && _height > 0) {\n                            _this.target.clip([(0, _Bounds.calculatePaddingBoxPath)(container.curvedBounds)], function () {\n                                _this.target.drawImage(_image, new _Bounds.Bounds(0, 0, _width, _height), contentBox);\n                            });\n                        }\n                    }\n                }\n            };\n            var paths = container.getClipPaths();\n            if (paths.length) {\n                this.target.clip(paths, callback);\n            } else {\n                callback();\n            }\n        }\n    }, {\n        key: 'renderNodeBackgroundAndBorders',\n        value: function renderNodeBackgroundAndBorders(container) {\n            var _this2 = this;\n\n            var HAS_BACKGROUND = !container.style.background.backgroundColor.isTransparent() || container.style.background.backgroundImage.length;\n\n            var hasRenderableBorders = container.style.border.some(function (border) {\n                return border.borderStyle !== _border.BORDER_STYLE.NONE && !border.borderColor.isTransparent();\n            });\n\n            var callback = function callback() {\n                var backgroundPaintingArea = (0, _background.calculateBackgroungPaintingArea)(container.curvedBounds, container.style.background.backgroundClip);\n\n                if (HAS_BACKGROUND) {\n                    _this2.target.clip([backgroundPaintingArea], function () {\n                        if (!container.style.background.backgroundColor.isTransparent()) {\n                            _this2.target.fill(container.style.background.backgroundColor);\n                        }\n\n                        _this2.renderBackgroundImage(container);\n                    });\n                }\n\n                container.style.border.forEach(function (border, side) {\n                    if (border.borderStyle !== _border.BORDER_STYLE.NONE && !border.borderColor.isTransparent()) {\n                        _this2.renderBorder(border, side, container.curvedBounds);\n                    }\n                });\n            };\n\n            if (HAS_BACKGROUND || hasRenderableBorders) {\n                var paths = container.parent ? container.parent.getClipPaths() : [];\n                if (paths.length) {\n                    this.target.clip(paths, callback);\n                } else {\n                    callback();\n                }\n            }\n        }\n    }, {\n        key: 'renderBackgroundImage',\n        value: function renderBackgroundImage(container) {\n            var _this3 = this;\n\n            container.style.background.backgroundImage.slice(0).reverse().forEach(function (backgroundImage) {\n                if (backgroundImage.source.method === 'url' && backgroundImage.source.args.length) {\n                    _this3.renderBackgroundRepeat(container, backgroundImage);\n                } else if (/gradient/i.test(backgroundImage.source.method)) {\n                    _this3.renderBackgroundGradient(container, backgroundImage);\n                }\n            });\n        }\n    }, {\n        key: 'renderBackgroundRepeat',\n        value: function renderBackgroundRepeat(container, background) {\n            var image = this.options.imageStore.get(background.source.args[0]);\n            if (image) {\n                var backgroundPositioningArea = (0, _background.calculateBackgroungPositioningArea)(container.style.background.backgroundOrigin, container.bounds, container.style.padding, container.style.border);\n                var backgroundImageSize = (0, _background.calculateBackgroundSize)(background, image, backgroundPositioningArea);\n                var position = (0, _background.calculateBackgroundPosition)(background.position, backgroundImageSize, backgroundPositioningArea);\n                var _path = (0, _background.calculateBackgroundRepeatPath)(background, position, backgroundImageSize, backgroundPositioningArea, container.bounds);\n\n                var _offsetX = Math.round(backgroundPositioningArea.left + position.x);\n                var _offsetY = Math.round(backgroundPositioningArea.top + position.y);\n                this.target.renderRepeat(_path, image, backgroundImageSize, _offsetX, _offsetY);\n            }\n        }\n    }, {\n        key: 'renderBackgroundGradient',\n        value: function renderBackgroundGradient(container, background) {\n            var backgroundPositioningArea = (0, _background.calculateBackgroungPositioningArea)(container.style.background.backgroundOrigin, container.bounds, container.style.padding, container.style.border);\n            var backgroundImageSize = (0, _background.calculateGradientBackgroundSize)(background, backgroundPositioningArea);\n            var position = (0, _background.calculateBackgroundPosition)(background.position, backgroundImageSize, backgroundPositioningArea);\n            var gradientBounds = new _Bounds.Bounds(Math.round(backgroundPositioningArea.left + position.x), Math.round(backgroundPositioningArea.top + position.y), backgroundImageSize.width, backgroundImageSize.height);\n\n            var gradient = (0, _Gradient.parseGradient)(container, background.source, gradientBounds);\n            if (gradient) {\n                switch (gradient.type) {\n                    case _Gradient.GRADIENT_TYPE.LINEAR_GRADIENT:\n                        // $FlowFixMe\n                        this.target.renderLinearGradient(gradientBounds, gradient);\n                        break;\n                    case _Gradient.GRADIENT_TYPE.RADIAL_GRADIENT:\n                        // $FlowFixMe\n                        this.target.renderRadialGradient(gradientBounds, gradient);\n                        break;\n                }\n            }\n        }\n    }, {\n        key: 'renderBorder',\n        value: function renderBorder(border, side, curvePoints) {\n            this.target.drawShape((0, _Bounds.parsePathForBorder)(curvePoints, side), border.borderColor);\n        }\n    }, {\n        key: 'renderStack',\n        value: function renderStack(stack) {\n            var _this4 = this;\n\n            if (stack.container.isVisible()) {\n                var _opacity = stack.getOpacity();\n                if (_opacity !== this._opacity) {\n                    this.target.setOpacity(stack.getOpacity());\n                    this._opacity = _opacity;\n                }\n\n                var _transform = stack.container.style.transform;\n                if (_transform !== null) {\n                    this.target.transform(stack.container.bounds.left + _transform.transformOrigin[0].value, stack.container.bounds.top + _transform.transformOrigin[1].value, _transform.transform, function () {\n                        return _this4.renderStackContent(stack);\n                    });\n                } else {\n                    this.renderStackContent(stack);\n                }\n            }\n        }\n    }, {\n        key: 'renderStackContent',\n        value: function renderStackContent(stack) {\n            var _splitStackingContext = splitStackingContexts(stack),\n                _splitStackingContext2 = _slicedToArray(_splitStackingContext, 5),\n                negativeZIndex = _splitStackingContext2[0],\n                zeroOrAutoZIndexOrTransformedOrOpacity = _splitStackingContext2[1],\n                positiveZIndex = _splitStackingContext2[2],\n                nonPositionedFloats = _splitStackingContext2[3],\n                nonPositionedInlineLevel = _splitStackingContext2[4];\n\n            var _splitDescendants = splitDescendants(stack),\n                _splitDescendants2 = _slicedToArray(_splitDescendants, 2),\n                inlineLevel = _splitDescendants2[0],\n                nonInlineLevel = _splitDescendants2[1];\n\n            // https://www.w3.org/TR/css-position-3/#painting-order\n            // 1. the background and borders of the element forming the stacking context.\n\n\n            this.renderNodeBackgroundAndBorders(stack.container);\n            // 2. the child stacking contexts with negative stack levels (most negative first).\n            negativeZIndex.sort(sortByZIndex).forEach(this.renderStack, this);\n            // 3. For all its in-flow, non-positioned, block-level descendants in tree order:\n            this.renderNodeContent(stack.container);\n            nonInlineLevel.forEach(this.renderNode, this);\n            // 4. All non-positioned floating descendants, in tree order. For each one of these,\n            // treat the element as if it created a new stacking context, but any positioned descendants and descendants\n            // which actually create a new stacking context should be considered part of the parent stacking context,\n            // not this new one.\n            nonPositionedFloats.forEach(this.renderStack, this);\n            // 5. the in-flow, inline-level, non-positioned descendants, including inline tables and inline blocks.\n            nonPositionedInlineLevel.forEach(this.renderStack, this);\n            inlineLevel.forEach(this.renderNode, this);\n            // 6. All positioned, opacity or transform descendants, in tree order that fall into the following categories:\n            //  All positioned descendants with 'z-index: auto' or 'z-index: 0', in tree order.\n            //  For those with 'z-index: auto', treat the element as if it created a new stacking context,\n            //  but any positioned descendants and descendants which actually create a new stacking context should be\n            //  considered part of the parent stacking context, not this new one. For those with 'z-index: 0',\n            //  treat the stacking context generated atomically.\n            //\n            //  All opacity descendants with opacity less than 1\n            //\n            //  All transform descendants with transform other than none\n            zeroOrAutoZIndexOrTransformedOrOpacity.forEach(this.renderStack, this);\n            // 7. Stacking contexts formed by positioned descendants with z-indices greater than or equal to 1 in z-index\n            // order (smallest first) then tree order.\n            positiveZIndex.sort(sortByZIndex).forEach(this.renderStack, this);\n        }\n    }, {\n        key: 'render',\n        value: function render(stack) {\n            var _this5 = this;\n\n            if (this.options.backgroundColor) {\n                this.target.rectangle(this.options.x, this.options.y, this.options.width, this.options.height, this.options.backgroundColor);\n            }\n            this.renderStack(stack);\n            var target = this.target.getTarget();\n            if (process.env.NODE_ENV !== 'production') {\n                return target.then(function (output) {\n                    _this5.options.logger.log('Render completed');\n                    return output;\n                });\n            }\n            return target;\n        }\n    }]);\n\n    return Renderer;\n}();\n\nexports.default = Renderer;\n\n\nvar splitDescendants = function splitDescendants(stack) {\n    var inlineLevel = [];\n    var nonInlineLevel = [];\n\n    var length = stack.children.length;\n    for (var i = 0; i < length; i++) {\n        var child = stack.children[i];\n        if (child.isInlineLevel()) {\n            inlineLevel.push(child);\n        } else {\n            nonInlineLevel.push(child);\n        }\n    }\n    return [inlineLevel, nonInlineLevel];\n};\n\nvar splitStackingContexts = function splitStackingContexts(stack) {\n    var negativeZIndex = [];\n    var zeroOrAutoZIndexOrTransformedOrOpacity = [];\n    var positiveZIndex = [];\n    var nonPositionedFloats = [];\n    var nonPositionedInlineLevel = [];\n    var length = stack.contexts.length;\n    for (var i = 0; i < length; i++) {\n        var child = stack.contexts[i];\n        if (child.container.isPositioned() || child.container.style.opacity < 1 || child.container.isTransformed()) {\n            if (child.container.style.zIndex.order < 0) {\n                negativeZIndex.push(child);\n            } else if (child.container.style.zIndex.order > 0) {\n                positiveZIndex.push(child);\n            } else {\n                zeroOrAutoZIndexOrTransformedOrOpacity.push(child);\n            }\n        } else {\n            if (child.container.isFloating()) {\n                nonPositionedFloats.push(child);\n            } else {\n                nonPositionedInlineLevel.push(child);\n            }\n        }\n    }\n    return [negativeZIndex, zeroOrAutoZIndexOrTransformedOrOpacity, positiveZIndex, nonPositionedFloats, nonPositionedInlineLevel];\n};\n\nvar sortByZIndex = function sortByZIndex(a, b) {\n    if (a.container.style.zIndex.order > b.container.style.zIndex.order) {\n        return 1;\n    } else if (a.container.style.zIndex.order < b.container.style.zIndex.order) {\n        return -1;\n    }\n\n    return a.container.index > b.container.index ? 1 : -1;\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nvar OVERFLOW = exports.OVERFLOW = {\n    VISIBLE: 0,\n    HIDDEN: 1,\n    SCROLL: 2,\n    AUTO: 3\n};\n\nvar parseOverflow = exports.parseOverflow = function parseOverflow(overflow) {\n    switch (overflow) {\n        case 'hidden':\n            return OVERFLOW.HIDDEN;\n        case 'scroll':\n            return OVERFLOW.SCROLL;\n        case 'auto':\n            return OVERFLOW.AUTO;\n        case 'visible':\n        default:\n            return OVERFLOW.VISIBLE;\n    }\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nvar parseZIndex = exports.parseZIndex = function parseZIndex(zIndex) {\n    var auto = zIndex === 'auto';\n    return {\n        auto: auto,\n        order: auto ? 0 : parseInt(zIndex, 10)\n    };\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.ResourceStore = undefined;\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _Feature = require('./Feature');\n\nvar _Feature2 = _interopRequireDefault(_Feature);\n\nvar _Proxy = require('./Proxy');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar ResourceLoader = function () {\n    function ResourceLoader(options, logger, window) {\n        _classCallCheck(this, ResourceLoader);\n\n        this.options = options;\n        this._window = window;\n        this.origin = this.getOrigin(window.location.href);\n        this.cache = {};\n        this.logger = logger;\n        this._index = 0;\n    }\n\n    _createClass(ResourceLoader, [{\n        key: 'loadImage',\n        value: function loadImage(src) {\n            var _this = this;\n\n            if (this.hasResourceInCache(src)) {\n                return src;\n            }\n\n            if (!isSVG(src) || _Feature2.default.SUPPORT_SVG_DRAWING) {\n                if (this.options.allowTaint === true || isInlineImage(src) || this.isSameOrigin(src)) {\n                    return this.addImage(src, src, false);\n                } else if (!this.isSameOrigin(src)) {\n                    if (typeof this.options.proxy === 'string') {\n                        this.cache[src] = (0, _Proxy.Proxy)(src, this.options).then(function (src) {\n                            return _loadImage(src, _this.options.imageTimeout || 0);\n                        });\n                        return src;\n                    } else if (this.options.useCORS === true && _Feature2.default.SUPPORT_CORS_IMAGES) {\n                        return this.addImage(src, src, true);\n                    }\n                }\n            }\n        }\n    }, {\n        key: 'inlineImage',\n        value: function inlineImage(src) {\n            var _this2 = this;\n\n            if (isInlineImage(src)) {\n                return _loadImage(src, this.options.imageTimeout || 0);\n            }\n            if (this.hasResourceInCache(src)) {\n                return this.cache[src];\n            }\n            if (!this.isSameOrigin(src) && typeof this.options.proxy === 'string') {\n                return this.cache[src] = (0, _Proxy.Proxy)(src, this.options).then(function (src) {\n                    return _loadImage(src, _this2.options.imageTimeout || 0);\n                });\n            }\n\n            return this.xhrImage(src);\n        }\n    }, {\n        key: 'xhrImage',\n        value: function xhrImage(src) {\n            var _this3 = this;\n\n            this.cache[src] = new Promise(function (resolve, reject) {\n                var xhr = new XMLHttpRequest();\n                xhr.onreadystatechange = function () {\n                    if (xhr.readyState === 4) {\n                        if (xhr.status !== 200) {\n                            reject('Failed to fetch image ' + src.substring(0, 256) + ' with status code ' + xhr.status);\n                        } else {\n                            var reader = new FileReader();\n                            reader.addEventListener('load', function () {\n                                // $FlowFixMe\n                                var result = reader.result;\n                                resolve(result);\n                            }, false);\n                            reader.addEventListener('error', function (e) {\n                                return reject(e);\n                            }, false);\n                            reader.readAsDataURL(xhr.response);\n                        }\n                    }\n                };\n                xhr.responseType = 'blob';\n                if (_this3.options.imageTimeout) {\n                    var timeout = _this3.options.imageTimeout;\n                    xhr.timeout = timeout;\n                    xhr.ontimeout = function () {\n                        return reject(process.env.NODE_ENV !== 'production' ? 'Timed out (' + timeout + 'ms) fetching ' + src.substring(0, 256) : '');\n                    };\n                }\n                xhr.open('GET', src, true);\n                xhr.send();\n            }).then(function (src) {\n                return _loadImage(src, _this3.options.imageTimeout || 0);\n            });\n\n            return this.cache[src];\n        }\n    }, {\n        key: 'loadCanvas',\n        value: function loadCanvas(node) {\n            var key = String(this._index++);\n            this.cache[key] = Promise.resolve(node);\n            return key;\n        }\n    }, {\n        key: 'hasResourceInCache',\n        value: function hasResourceInCache(key) {\n            return typeof this.cache[key] !== 'undefined';\n        }\n    }, {\n        key: 'addImage',\n        value: function addImage(key, src, useCORS) {\n            var _this4 = this;\n\n            if (process.env.NODE_ENV !== 'production') {\n                this.logger.log('Added image ' + key.substring(0, 256));\n            }\n\n            var imageLoadHandler = function imageLoadHandler(supportsDataImages) {\n                return new Promise(function (resolve, reject) {\n                    var img = new Image();\n                    img.onload = function () {\n                        return resolve(img);\n                    };\n                    //ios safari 10.3 taints canvas with data urls unless crossOrigin is set to anonymous\n                    if (!supportsDataImages || useCORS) {\n                        img.crossOrigin = 'anonymous';\n                    }\n\n                    img.onerror = reject;\n                    img.src = src;\n                    if (img.complete === true) {\n                        // Inline XML images may fail to parse, throwing an Error later on\n                        setTimeout(function () {\n                            resolve(img);\n                        }, 500);\n                    }\n                    if (_this4.options.imageTimeout) {\n                        var timeout = _this4.options.imageTimeout;\n                        setTimeout(function () {\n                            return reject(process.env.NODE_ENV !== 'production' ? 'Timed out (' + timeout + 'ms) fetching ' + src.substring(0, 256) : '');\n                        }, timeout);\n                    }\n                });\n            };\n\n            this.cache[key] = isInlineBase64Image(src) && !isSVG(src) ? // $FlowFixMe\n            _Feature2.default.SUPPORT_BASE64_DRAWING(src).then(imageLoadHandler) : imageLoadHandler(true);\n            return key;\n        }\n    }, {\n        key: 'isSameOrigin',\n        value: function isSameOrigin(url) {\n            return this.getOrigin(url) === this.origin;\n        }\n    }, {\n        key: 'getOrigin',\n        value: function getOrigin(url) {\n            var link = this._link || (this._link = this._window.document.createElement('a'));\n            link.href = url;\n            link.href = link.href; // IE9, LOL! - http://jsfiddle.net/niklasvh/2e48b/\n            return link.protocol + link.hostname + link.port;\n        }\n    }, {\n        key: 'ready',\n        value: function ready() {\n            var _this5 = this;\n\n            var keys = Object.keys(this.cache);\n            var values = keys.map(function (str) {\n                return _this5.cache[str].catch(function (e) {\n                    if (process.env.NODE_ENV !== 'production') {\n                        _this5.logger.log('Unable to load image', e);\n                    }\n                    return null;\n                });\n            });\n            return Promise.all(values).then(function (images) {\n                if (process.env.NODE_ENV !== 'production') {\n                    _this5.logger.log('Finished loading ' + images.length + ' images', images);\n                }\n                return new ResourceStore(keys, images);\n            });\n        }\n    }]);\n\n    return ResourceLoader;\n}();\n\nexports.default = ResourceLoader;\n\nvar ResourceStore = exports.ResourceStore = function () {\n    function ResourceStore(keys, resources) {\n        _classCallCheck(this, ResourceStore);\n\n        this._keys = keys;\n        this._resources = resources;\n    }\n\n    _createClass(ResourceStore, [{\n        key: 'get',\n        value: function get(key) {\n            var index = this._keys.indexOf(key);\n            return index === -1 ? null : this._resources[index];\n        }\n    }]);\n\n    return ResourceStore;\n}();\n\nvar INLINE_SVG = /^data:image\\/svg\\+xml/i;\nvar INLINE_BASE64 = /^data:image\\/.*;base64,/i;\nvar INLINE_IMG = /^data:image\\/.*/i;\n\nvar isInlineImage = function isInlineImage(src) {\n    return INLINE_IMG.test(src);\n};\nvar isInlineBase64Image = function isInlineBase64Image(src) {\n    return INLINE_BASE64.test(src);\n};\n\nvar isSVG = function isSVG(src) {\n    return src.substr(-3).toLowerCase() === 'svg' || INLINE_SVG.test(src);\n};\n\nvar _loadImage = function _loadImage(src, timeout) {\n    return new Promise(function (resolve, reject) {\n        var img = new Image();\n        img.onload = function () {\n            return resolve(img);\n        };\n        img.onerror = reject;\n        img.src = src;\n        if (img.complete === true) {\n            // Inline XML images may fail to parse, throwing an Error later on\n            setTimeout(function () {\n                resolve(img);\n            }, 500);\n        }\n        if (timeout) {\n            setTimeout(function () {\n                return reject(process.env.NODE_ENV !== 'production' ? 'Timed out (' + timeout + 'ms) loading image' : '');\n            }, timeout);\n        }\n    });\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.FontMetrics = undefined;\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _Util = require('./Util');\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar SAMPLE_TEXT = 'Hidden Text';\n\nvar FontMetrics = exports.FontMetrics = function () {\n    function FontMetrics(document) {\n        _classCallCheck(this, FontMetrics);\n\n        this._data = {};\n        this._document = document;\n    }\n\n    _createClass(FontMetrics, [{\n        key: '_parseMetrics',\n        value: function _parseMetrics(font) {\n            var container = this._document.createElement('div');\n            var img = this._document.createElement('img');\n            var span = this._document.createElement('span');\n\n            var body = this._document.body;\n            if (!body) {\n                throw new Error(process.env.NODE_ENV !== 'production' ? 'No document found for font metrics' : '');\n            }\n\n            container.style.visibility = 'hidden';\n            container.style.fontFamily = font.fontFamily;\n            container.style.fontSize = font.fontSize;\n            container.style.margin = '0';\n            container.style.padding = '0';\n\n            body.appendChild(container);\n\n            img.src = _Util.SMALL_IMAGE;\n            img.width = 1;\n            img.height = 1;\n\n            img.style.margin = '0';\n            img.style.padding = '0';\n            img.style.verticalAlign = 'baseline';\n\n            span.style.fontFamily = font.fontFamily;\n            span.style.fontSize = font.fontSize;\n            span.style.margin = '0';\n            span.style.padding = '0';\n\n            span.appendChild(this._document.createTextNode(SAMPLE_TEXT));\n            container.appendChild(span);\n            container.appendChild(img);\n            var baseline = img.offsetTop - span.offsetTop + 2;\n\n            container.removeChild(span);\n            container.appendChild(this._document.createTextNode(SAMPLE_TEXT));\n\n            container.style.lineHeight = 'normal';\n            img.style.verticalAlign = 'super';\n\n            var middle = img.offsetTop - container.offsetTop + 2;\n\n            body.removeChild(container);\n\n            return { baseline: baseline, middle: middle };\n        }\n    }, {\n        key: 'getMetrics',\n        value: function getMetrics(font) {\n            var key = font.fontFamily + ' ' + font.fontSize;\n            if (this._data[key] === undefined) {\n                this._data[key] = this._parseMetrics(font);\n            }\n\n            return this._data[key];\n        }\n    }]);\n\n    return FontMetrics;\n}();", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _textTransform = require('./parsing/textTransform');\n\nvar _TextBounds = require('./TextBounds');\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar TextContainer = function () {\n    function TextContainer(text, parent, bounds) {\n        _classCallCheck(this, TextContainer);\n\n        this.text = text;\n        this.parent = parent;\n        this.bounds = bounds;\n    }\n\n    _createClass(TextContainer, null, [{\n        key: 'fromTextNode',\n        value: function fromTextNode(node, parent) {\n            var text = transform(node.data, parent.style.textTransform);\n            return new TextContainer(text, parent, (0, _TextBounds.parseTextBounds)(text, parent, node));\n        }\n    }]);\n\n    return TextContainer;\n}();\n\nexports.default = TextContainer;\n\n\nvar CAPITALIZE = /(^|\\s|:|-|\\(|\\))([a-z])/g;\n\nvar transform = function transform(text, _transform) {\n    switch (_transform) {\n        case _textTransform.TEXT_TRANSFORM.LOWERCASE:\n            return text.toLowerCase();\n        case _textTransform.TEXT_TRANSFORM.CAPITALIZE:\n            return text.replace(CAPITALIZE, capitalize);\n        case _textTransform.TEXT_TRANSFORM.UPPERCASE:\n            return text.toUpperCase();\n        default:\n            return text;\n    }\n};\n\nfunction capitalize(m, p1, p2) {\n    if (m.length > 0) {\n        return p1 + p2.toUpperCase();\n    }\n\n    return m;\n}", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.parseContent = exports.resolvePseudoContent = exports.popCounters = exports.parseCounterReset = exports.TOKEN_TYPE = exports.PSEUDO_CONTENT_ITEM_TYPE = undefined;\n\nvar _slicedToArray = function () { function sliceIterator(arr, i) { var _arr = []; var _n = true; var _d = false; var _e = undefined; try { for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"]) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; } return function (arr, i) { if (Array.isArray(arr)) { return arr; } else if (Symbol.iterator in Object(arr)) { return sliceIterator(arr, i); } else { throw new TypeError(\"Invalid attempt to destructure non-iterable instance\"); } }; }();\n\nvar _ListItem = require('./ListItem');\n\nvar _listStyle = require('./parsing/listStyle');\n\nvar PSEUDO_CONTENT_ITEM_TYPE = exports.PSEUDO_CONTENT_ITEM_TYPE = {\n    TEXT: 0,\n    IMAGE: 1\n};\n\nvar TOKEN_TYPE = exports.TOKEN_TYPE = {\n    STRING: 0,\n    ATTRIBUTE: 1,\n    URL: 2,\n    COUNTER: 3,\n    COUNTERS: 4,\n    OPENQUOTE: 5,\n    CLOSEQUOTE: 6\n};\n\nvar parseCounterReset = exports.parseCounterReset = function parseCounterReset(style, data) {\n    if (!style || !style.counterReset || style.counterReset === 'none') {\n        return [];\n    }\n\n    var counterNames = [];\n    var counterResets = style.counterReset.split(/\\s*,\\s*/);\n    var lenCounterResets = counterResets.length;\n\n    for (var i = 0; i < lenCounterResets; i++) {\n        var _counterResets$i$spli = counterResets[i].split(/\\s+/),\n            _counterResets$i$spli2 = _slicedToArray(_counterResets$i$spli, 2),\n            counterName = _counterResets$i$spli2[0],\n            initialValue = _counterResets$i$spli2[1];\n\n        counterNames.push(counterName);\n        var counter = data.counters[counterName];\n        if (!counter) {\n            counter = data.counters[counterName] = [];\n        }\n        counter.push(parseInt(initialValue || 0, 10));\n    }\n\n    return counterNames;\n};\n\nvar popCounters = exports.popCounters = function popCounters(counterNames, data) {\n    var lenCounters = counterNames.length;\n    for (var i = 0; i < lenCounters; i++) {\n        data.counters[counterNames[i]].pop();\n    }\n};\n\nvar resolvePseudoContent = exports.resolvePseudoContent = function resolvePseudoContent(node, style, data) {\n    if (!style || !style.content || style.content === 'none' || style.content === '-moz-alt-content' || style.display === 'none') {\n        return null;\n    }\n\n    var tokens = parseContent(style.content);\n\n    var len = tokens.length;\n    var contentItems = [];\n    var s = '';\n\n    // increment the counter (if there is a \"counter-increment\" declaration)\n    var counterIncrement = style.counterIncrement;\n    if (counterIncrement && counterIncrement !== 'none') {\n        var _counterIncrement$spl = counterIncrement.split(/\\s+/),\n            _counterIncrement$spl2 = _slicedToArray(_counterIncrement$spl, 2),\n            counterName = _counterIncrement$spl2[0],\n            incrementValue = _counterIncrement$spl2[1];\n\n        var counter = data.counters[counterName];\n        if (counter) {\n            counter[counter.length - 1] += incrementValue === undefined ? 1 : parseInt(incrementValue, 10);\n        }\n    }\n\n    // build the content string\n    for (var i = 0; i < len; i++) {\n        var token = tokens[i];\n        switch (token.type) {\n            case TOKEN_TYPE.STRING:\n                s += token.value || '';\n                break;\n\n            case TOKEN_TYPE.ATTRIBUTE:\n                if (node instanceof HTMLElement && token.value) {\n                    s += node.getAttribute(token.value) || '';\n                }\n                break;\n\n            case TOKEN_TYPE.COUNTER:\n                var _counter = data.counters[token.name || ''];\n                if (_counter) {\n                    s += formatCounterValue([_counter[_counter.length - 1]], '', token.format);\n                }\n                break;\n\n            case TOKEN_TYPE.COUNTERS:\n                var _counters = data.counters[token.name || ''];\n                if (_counters) {\n                    s += formatCounterValue(_counters, token.glue, token.format);\n                }\n                break;\n\n            case TOKEN_TYPE.OPENQUOTE:\n                s += getQuote(style, true, data.quoteDepth);\n                data.quoteDepth++;\n                break;\n\n            case TOKEN_TYPE.CLOSEQUOTE:\n                data.quoteDepth--;\n                s += getQuote(style, false, data.quoteDepth);\n                break;\n\n            case TOKEN_TYPE.URL:\n                if (s) {\n                    contentItems.push({ type: PSEUDO_CONTENT_ITEM_TYPE.TEXT, value: s });\n                    s = '';\n                }\n                contentItems.push({ type: PSEUDO_CONTENT_ITEM_TYPE.IMAGE, value: token.value || '' });\n                break;\n        }\n    }\n\n    if (s) {\n        contentItems.push({ type: PSEUDO_CONTENT_ITEM_TYPE.TEXT, value: s });\n    }\n\n    return contentItems;\n};\n\nvar parseContent = exports.parseContent = function parseContent(content, cache) {\n    if (cache && cache[content]) {\n        return cache[content];\n    }\n\n    var tokens = [];\n    var len = content.length;\n\n    var isString = false;\n    var isEscaped = false;\n    var isFunction = false;\n    var str = '';\n    var functionName = '';\n    var args = [];\n\n    for (var i = 0; i < len; i++) {\n        var c = content.charAt(i);\n\n        switch (c) {\n            case \"'\":\n            case '\"':\n                if (isEscaped) {\n                    str += c;\n                } else {\n                    isString = !isString;\n                    if (!isFunction && !isString) {\n                        tokens.push({ type: TOKEN_TYPE.STRING, value: str });\n                        str = '';\n                    }\n                }\n                break;\n\n            case '\\\\':\n                if (isEscaped) {\n                    str += c;\n                    isEscaped = false;\n                } else {\n                    isEscaped = true;\n                }\n                break;\n\n            case '(':\n                if (isString) {\n                    str += c;\n                } else {\n                    isFunction = true;\n                    functionName = str;\n                    str = '';\n                    args = [];\n                }\n                break;\n\n            case ')':\n                if (isString) {\n                    str += c;\n                } else if (isFunction) {\n                    if (str) {\n                        args.push(str);\n                    }\n\n                    switch (functionName) {\n                        case 'attr':\n                            if (args.length > 0) {\n                                tokens.push({ type: TOKEN_TYPE.ATTRIBUTE, value: args[0] });\n                            }\n                            break;\n\n                        case 'counter':\n                            if (args.length > 0) {\n                                var counter = {\n                                    type: TOKEN_TYPE.COUNTER,\n                                    name: args[0]\n                                };\n                                if (args.length > 1) {\n                                    counter.format = args[1];\n                                }\n                                tokens.push(counter);\n                            }\n                            break;\n\n                        case 'counters':\n                            if (args.length > 0) {\n                                var _counters2 = {\n                                    type: TOKEN_TYPE.COUNTERS,\n                                    name: args[0]\n                                };\n                                if (args.length > 1) {\n                                    _counters2.glue = args[1];\n                                }\n                                if (args.length > 2) {\n                                    _counters2.format = args[2];\n                                }\n                                tokens.push(_counters2);\n                            }\n                            break;\n\n                        case 'url':\n                            if (args.length > 0) {\n                                tokens.push({ type: TOKEN_TYPE.URL, value: args[0] });\n                            }\n                            break;\n                    }\n\n                    isFunction = false;\n                    str = '';\n                }\n                break;\n\n            case ',':\n                if (isString) {\n                    str += c;\n                } else if (isFunction) {\n                    args.push(str);\n                    str = '';\n                }\n                break;\n\n            case ' ':\n            case '\\t':\n                if (isString) {\n                    str += c;\n                } else if (str) {\n                    addOtherToken(tokens, str);\n                    str = '';\n                }\n                break;\n\n            default:\n                str += c;\n        }\n\n        if (c !== '\\\\') {\n            isEscaped = false;\n        }\n    }\n\n    if (str) {\n        addOtherToken(tokens, str);\n    }\n\n    if (cache) {\n        cache[content] = tokens;\n    }\n\n    return tokens;\n};\n\nvar addOtherToken = function addOtherToken(tokens, identifier) {\n    switch (identifier) {\n        case 'open-quote':\n            tokens.push({ type: TOKEN_TYPE.OPENQUOTE });\n            break;\n        case 'close-quote':\n            tokens.push({ type: TOKEN_TYPE.CLOSEQUOTE });\n            break;\n    }\n};\n\nvar getQuote = function getQuote(style, isOpening, quoteDepth) {\n    var quotes = style.quotes ? style.quotes.split(/\\s+/) : [\"'\\\"'\", \"'\\\"'\"];\n    var idx = quoteDepth * 2;\n    if (idx >= quotes.length) {\n        idx = quotes.length - 2;\n    }\n    if (!isOpening) {\n        ++idx;\n    }\n    return quotes[idx].replace(/^[\"']|[\"']$/g, '');\n};\n\nvar formatCounterValue = function formatCounterValue(counter, glue, format) {\n    var len = counter.length;\n    var result = '';\n\n    for (var i = 0; i < len; i++) {\n        if (i > 0) {\n            result += glue || '';\n        }\n        result += (0, _ListItem.createCounterText)(counter[i], (0, _listStyle.parseListStyleType)(format || 'decimal'), false);\n    }\n\n    return result;\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nvar DISPLAY = exports.DISPLAY = {\n    NONE: 1 << 0,\n    BLOCK: 1 << 1,\n    INLINE: 1 << 2,\n    RUN_IN: 1 << 3,\n    FLOW: 1 << 4,\n    FLOW_ROOT: 1 << 5,\n    TABLE: 1 << 6,\n    FLEX: 1 << 7,\n    GRID: 1 << 8,\n    RUBY: 1 << 9,\n    SUBGRID: 1 << 10,\n    LIST_ITEM: 1 << 11,\n    TABLE_ROW_GROUP: 1 << 12,\n    TABLE_HEADER_GROUP: 1 << 13,\n    TABLE_FOOTER_GROUP: 1 << 14,\n    TABLE_ROW: 1 << 15,\n    TABLE_CELL: 1 << 16,\n    TABLE_COLUMN_GROUP: 1 << 17,\n    TABLE_COLUMN: 1 << 18,\n    TABLE_CAPTION: 1 << 19,\n    RUBY_BASE: 1 << 20,\n    RUBY_TEXT: 1 << 21,\n    RUBY_BASE_CONTAINER: 1 << 22,\n    RUBY_TEXT_CONTAINER: 1 << 23,\n    CONTENTS: 1 << 24,\n    INLINE_BLOCK: 1 << 25,\n    INLINE_LIST_ITEM: 1 << 26,\n    INLINE_TABLE: 1 << 27,\n    INLINE_FLEX: 1 << 28,\n    INLINE_GRID: 1 << 29\n};\n\nvar parseDisplayValue = function parseDisplayValue(display) {\n    switch (display) {\n        case 'block':\n            return DISPLAY.BLOCK;\n        case 'inline':\n            return DISPLAY.INLINE;\n        case 'run-in':\n            return DISPLAY.RUN_IN;\n        case 'flow':\n            return DISPLAY.FLOW;\n        case 'flow-root':\n            return DISPLAY.FLOW_ROOT;\n        case 'table':\n            return DISPLAY.TABLE;\n        case 'flex':\n            return DISPLAY.FLEX;\n        case 'grid':\n            return DISPLAY.GRID;\n        case 'ruby':\n            return DISPLAY.RUBY;\n        case 'subgrid':\n            return DISPLAY.SUBGRID;\n        case 'list-item':\n            return DISPLAY.LIST_ITEM;\n        case 'table-row-group':\n            return DISPLAY.TABLE_ROW_GROUP;\n        case 'table-header-group':\n            return DISPLAY.TABLE_HEADER_GROUP;\n        case 'table-footer-group':\n            return DISPLAY.TABLE_FOOTER_GROUP;\n        case 'table-row':\n            return DISPLAY.TABLE_ROW;\n        case 'table-cell':\n            return DISPLAY.TABLE_CELL;\n        case 'table-column-group':\n            return DISPLAY.TABLE_COLUMN_GROUP;\n        case 'table-column':\n            return DISPLAY.TABLE_COLUMN;\n        case 'table-caption':\n            return DISPLAY.TABLE_CAPTION;\n        case 'ruby-base':\n            return DISPLAY.RUBY_BASE;\n        case 'ruby-text':\n            return DISPLAY.RUBY_TEXT;\n        case 'ruby-base-container':\n            return DISPLAY.RUBY_BASE_CONTAINER;\n        case 'ruby-text-container':\n            return DISPLAY.RUBY_TEXT_CONTAINER;\n        case 'contents':\n            return DISPLAY.CONTENTS;\n        case 'inline-block':\n            return DISPLAY.INLINE_BLOCK;\n        case 'inline-list-item':\n            return DISPLAY.INLINE_LIST_ITEM;\n        case 'inline-table':\n            return DISPLAY.INLINE_TABLE;\n        case 'inline-flex':\n            return DISPLAY.INLINE_FLEX;\n        case 'inline-grid':\n            return DISPLAY.INLINE_GRID;\n    }\n\n    return DISPLAY.NONE;\n};\n\nvar setDisplayBit = function setDisplayBit(bit, display) {\n    return bit | parseDisplayValue(display);\n};\n\nvar parseDisplay = exports.parseDisplay = function parseDisplay(display) {\n    return display.split(' ').reduce(setDisplayBit, 0);\n};"], "names": ["Object", "defineProperty", "exports", "value", "parseListStyle", "parseListStyleType", "LIST_STYLE_TYPE", "LIST_STYLE_POSITION", "undefined", "_background", "INSIDE", "OUTSIDE", "NONE", "DISC", "CIRCLE", "SQUARE", "DECIMAL", "CJK_DECIMAL", "DECIMAL_LEADING_ZERO", "LOWER_ROMAN", "UPPER_ROMAN", "LOWER_GREEK", "LOWER_ALPHA", "UPPER_ALPHA", "ARABIC_INDIC", "ARMENIAN", "BENGALI", "CAMBODIAN", "CJK_EARTHLY_BRANCH", "CJK_HEAVENLY_STEM", "CJK_IDEOGRAPHIC", "DEVANAGARI", "ETHIOPIC_NUMERIC", "GEORGIAN", "GUJARATI", "GURMUKHI", "HEBREW", "HIRAGANA", "HIRAGANA_IROHA", "JAPANESE_FORMAL", "JAPANESE_INFORMAL", "KANNADA", "KATAKANA", "KATAKANA_IROHA", "KHMER", "KOREAN_HANGUL_FORMAL", "KOREAN_HANJA_FORMAL", "KOREAN_HANJA_INFORMAL", "LAO", "LOWER_ARMENIAN", "MALAYALAM", "MONGOLIAN", "MYANMAR", "ORIYA", "PERSIAN", "SIMP_CHINESE_FORMAL", "SIMP_CHINESE_INFORMAL", "TAMIL", "TELUGU", "THAI", "TIBETAN", "TRAD_CHINESE_FORMAL", "TRAD_CHINESE_INFORMAL", "UPPER_ARMENIAN", "DISCLOSURE_OPEN", "DISCLOSURE_CLOSED", "type", "parseListStylePosition", "style", "listStyleImage", "parseBackgroundImage", "getPropertyValue", "listStyleType", "length", "listStylePosition", "position", "breakWords", "fromCodePoint", "toCodePoints", "_cssLineBreak", "enumerable", "get", "obj", "_overflowWrap", "__esModule", "str", "parent", "breaker", "LineBreaker", "lineBreak", "wordBreak", "overflowWrap", "OVERFLOW_WRAP", "BREAK_WORD", "words", "bk", "next", "done", "push", "slice", "<PERSON><PERSON>", "createTrieFromBase64", "UTRIE2_INDEX_2_MASK", "UTRIE2_INDEX_2_BLOCK_LENGTH", "UTRIE2_OMITTED_BMP_INDEX_1_LENGTH", "UTRIE2_INDEX_1_OFFSET", "UTRIE2_UTF8_2B_INDEX_2_LENGTH", "UTRIE2_UTF8_2B_INDEX_2_OFFSET", "UTRIE2_INDEX_2_BMP_LENGTH", "UTRIE2_LSCP_INDEX_2_LENGTH", "UTRIE2_DATA_MASK", "UTRIE2_DATA_BLOCK_LENGTH", "UTRIE2_LSCP_INDEX_2_OFFSET", "UTRIE2_SHIFT_1_2", "UTRIE2_INDEX_SHIFT", "UTRIE2_SHIFT_1", "UTRIE2_SHIFT_2", "_createClass", "defineProperties", "target", "props", "i", "descriptor", "configurable", "writable", "key", "<PERSON><PERSON><PERSON><PERSON>", "protoProps", "staticProps", "prototype", "_Util", "base64", "buffer", "decode", "view32", "Array", "isArray", "polyUint32Array", "Uint32Array", "view16", "polyUint16Array", "Uint16Array", "index", "<PERSON><PERSON><PERSON><PERSON>", "data", "Math", "ceil", "initialValue", "errorValue", "highStart", "highValueIndex", "instance", "TypeError", "_classCallCheck", "this", "codePoint", "ix", "NORMAL", "parseOverflowWrap", "overflow", "cloneWindow", "DocumentCloner", "_Bounds", "_Proxy", "_ResourceLoader2", "_interopRequireDefault", "_CanvasRenderer2", "_P<PERSON>udo<PERSON><PERSON><PERSON><PERSON><PERSON>", "default", "IGNORE_ATTRIBUTE", "element", "options", "logger", "copyInline", "renderer", "referenceElement", "scrolledElements", "copyStyles", "inlineImages", "resourceLoader", "window", "pseudoContentData", "counters", "quote<PERSON><PERSON><PERSON>", "documentElement", "cloneNode", "ownerDocument", "node", "_this", "Promise", "all", "backgroundImage", "map", "method", "inlineImage", "args", "then", "img", "src", "catch", "e", "resolve", "prefix", "join", "backgroundImages", "backgroundColor", "HTMLImageElement", "parentNode", "clone<PERSON><PERSON><PERSON><PERSON>", "copyCSSStyles", "<PERSON><PERSON><PERSON><PERSON>", "document", "_this2", "from", "styleSheets", "sheet", "href", "fetch", "res", "text", "createStyleSheetFontsFromText", "getSheetFonts", "fonts", "reduce", "acc", "font", "concat", "formats", "response", "blob", "reject", "reader", "FileReader", "onerror", "onload", "result", "readAsDataURL", "dataUri", "fontFace", "setProperty", "cssText", "fontCss", "createElement", "textContent", "append<PERSON><PERSON><PERSON>", "_this3", "HTMLCanvasElement", "toDataURL", "HTMLIFrameElement", "tempIframe", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "generateIframeKey", "setAttribute", "_parseBounds", "parseBounds", "width", "height", "cache", "getIframeDocumentElement", "async", "<PERSON><PERSON><PERSON><PERSON>", "canvas", "imageTimeout", "logging", "proxy", "remove<PERSON><PERSON><PERSON>", "scale", "foreignObjectRendering", "useCORS", "x", "y", "windowWidth", "defaultView", "innerWidth", "windowHeight", "innerHeight", "scrollX", "pageXOffset", "scrollY", "pageYOffset", "child", "<PERSON>rame<PERSON><PERSON><PERSON>", "getComputedStyle", "HTMLStyleElement", "cssRules", "css", "call", "rule", "clone", "nodeType", "Node", "TEXT_NODE", "createTextNode", "nodeValue", "createElementClone", "HTMLElement", "styleBefore", "styleAfter", "clonedReferenceElement", "HTMLBodyElement", "createPseudoHideStyles", "parseCounterReset", "contentBefore", "resolvePseudoContent", "<PERSON><PERSON><PERSON><PERSON>", "nextS<PERSON>ling", "ELEMENT_NODE", "nodeName", "hasAttribute", "ignoreElements", "contentAfter", "popCounters", "inlineAllImages", "inlinePseudoElement", "PSEUDO_BEFORE", "PSEUDO_AFTER", "scrollTop", "scrollLeft", "cloneCanvasContents", "checked", "filter", "CSSRule", "FONT_FACE_RULE", "a", "body", "format", "test", "baseHref", "doc", "implementation", "createHTMLDocument", "base", "head", "clonedCan<PERSON>", "ctx", "getContext", "clonedCtx", "putImageData", "getImageData", "drawImage", "contentItems", "pseudoElt", "content", "display", "anonymousReplacedElement", "len", "item", "PSEUDO_CONTENT_ITEM_TYPE", "IMAGE", "opacity", "TEXT", "className", "PSEUDO_HIDE_ELEMENT_CLASS_BEFORE", "PSEUDO_HIDE_ELEMENT_CLASS_AFTER", "insertBefore", "PSEUDO_HIDE_ELEMENT_STYLE", "createStyles", "styles", "innerHTML", "initNode", "_ref", "_ref2", "arr", "Symbol", "iterator", "_arr", "_n", "_d", "_e", "_s", "_i", "err", "sliceIterator", "_slicedToArray", "Date", "now", "random", "toString", "DATA_URI_REGEXP", "contentWindow", "Proxy", "html", "match", "atob", "decodeURIComponent", "createIframeContainer", "cloneIframeContainer", "documentClone", "open", "write", "iframeLoad", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "close", "bounds", "visibility", "left", "top", "border", "scrolling", "onreadystatechange", "interval", "setInterval", "childNodes", "readyState", "clearInterval", "serializeDoctype", "cloner", "for<PERSON>ach", "scrollTo", "navigator", "userAgent", "onclone", "doctype", "restoreOwnerScroll", "adoptNode", "name", "internalSubset", "publicId", "systemId", "module", "_ForeignObject<PERSON><PERSON>er", "isGreenPixel", "FEATURES", "SUPPORT_RANGE_BOUNDS", "createRange", "range", "getBoundingClientRect", "testElement", "TEST_HEIGHT", "selectNode", "rangeBounds", "rangeHeight", "round", "<PERSON><PERSON><PERSON><PERSON>", "testRangeBounds", "SUPPORT_SVG_DRAWING", "Image", "testSVG", "SUPPORT_BASE64_DRAWING", "_value", "complete", "setTimeout", "testBase64", "SUPPORT_FOREIGNOBJECT_DRAWING", "size", "fillStyle", "fillRect", "greenImageSrc", "svg", "createForeignObjectSVG", "loadSerializedSVG", "testForeignObject", "SUPPORT_CORS_IMAGES", "crossOrigin", "SUPPORT_RESPONSE_TYPE", "XMLHttpRequest", "responseType", "SUPPORT_CORS_XHR", "Size", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_StackingContext2", "_NodeContainer2", "_TextContainer2", "_Input", "_ListItem", "_listStyle", "container", "stack", "parseNodeTree", "IGNORED_NODE_NAMES", "nextNode", "childNode", "Text", "trim", "fromTextNode", "indexOf", "isVisible", "tagName", "inlineInputElement", "inlineTextAreaElement", "inlineSelectElement", "listStyle", "inlineListItemElement", "SHOULD_TRAVERSE_CHILDREN", "treatAsRealStackingContext", "createsRealStackingContext", "createsStackingContext", "parentStack", "isPositioned", "getRealParentStackingContext", "childS<PERSON>ck", "contexts", "children", "SVGSVGElement", "_container", "_treatAsRealStackingContext", "_parentStack", "_childStack", "isRootElement", "isPositionedWithZIndex", "isTransformed", "isBodyWithTransparentRoot", "isFloating", "background", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parseBorder", "BORDER_SIDES", "BORDER_STYLE", "_Color2", "SOLID", "TOP", "RIGHT", "BOTTOM", "LEFT", "SIDES", "keys", "s", "toLowerCase", "side", "borderColor", "borderStyle", "parseBorderStyle", "borderWidth", "parseFloat", "isNaN", "_Path", "Circle", "radius", "PATH", "parseTransform", "_Length2", "toFloat", "MATRIX", "parseTransformOrigin", "transform", "parseTransformMatrix", "webkitTransform", "mozTransform", "msTransform", "oTransform", "transform<PERSON><PERSON>in", "webkitTransformOrigin", "mozTransformOrigin", "msTransformOrigin", "oTransformOrigin", "origin", "v", "values", "split", "create", "matrix", "matrix3d", "calculateLengthFromValueWithUnit", "LENGTH_TYPE", "PX", "PERCENTAGE", "Length", "substr", "parsedValue", "parent<PERSON>ength", "isPercentage", "getRootFontSize", "fontSize", "unit", "parseFont", "fontFamily", "fontStyle", "fontVariant", "fontWeight", "weight", "parseInt", "parseFontWeight", "parseTextBounds", "TextBounds", "_textDecoration", "_Feature2", "_Unicode", "getWrapperBounds", "textList", "letterSpacing", "textBounds", "offset", "textDecoration", "TEXT_DECORATION", "getRangeBounds", "replacementNode", "splitText", "wrapper", "Bounds", "setStart", "setEnd", "fromClientRect", "transformWebkitRadialGradientArgs", "parseGradient", "RadialGrad<PERSON>", "LinearGradient", "RADIAL_GRADIENT_SHAPE", "GRADIENT_TYPE", "_<PERSON>le", "_Length", "SIDE_OR_CORNER", "PERCENTAGE_ANGLES", "ENDS_WITH_LENGTH", "FROM_TO_COLORSTOP", "RADIAL_SHAPE_DEFINITION", "LINEAR_GRADIENT", "RADIAL_GRADIENT", "ELLIPSE", "LENGTH_FOR_POSITION", "center", "right", "bottom", "colorStops", "direction", "shape", "parseColorStops", "parseLinearGradient", "transformObsoleteColorStops", "parseRadialGradient", "firstColorStopIndex", "lineLength", "HAS_LENGTH", "lastSpaceIndex", "lastIndexOf", "_color", "substring", "_stop", "color", "stop", "absoluteValuedColorStops", "getAbsoluteValue", "previousColorStop", "_stop2", "n", "steps", "stepSize", "hasPrefix", "angle", "parseAngle", "HAS_SIDE_OR_CORNER", "HAS_DIRECTION", "calculateGradientDirection", "PI", "parseSide<PERSON><PERSON><PERSON><PERSON><PERSON>", "parsePercentageAngle", "min", "distance", "abs", "x0", "x1", "y0", "y1", "m", "gradientCenter", "gradientRadius", "calculateRadius", "radian", "HALF_WIDTH", "HALF_HEIGHT", "HALF_LINE_LENGTH", "sin", "cos", "parseTopRight", "acos", "_angle$split$map2", "ratio", "atan", "<PERSON><PERSON><PERSON><PERSON>", "closest", "stat", "corner", "d", "optimumDistance", "opti<PERSON><PERSON><PERSON><PERSON>", "Infinity", "extent", "rx", "ry", "c", "max", "_c", "_corner", "idx", "POSITION", "RADIUS", "matchStartPosition", "matchShapeExtent", "matchStartRadius", "matchEndPosition", "matchEndRadius", "matchPosition", "matchRadius", "parseBorderRadius", "_value$split$map2", "horizontal", "vertical", "VECTOR", "BEZIER_CURVE", "contains", "bit", "b", "sqrt", "property", "SMALL_IMAGE", "_LineBreak", "renderElement", "_<PERSON><PERSON><PERSON><PERSON><PERSON>", "_Renderer2", "_ForeignObjectRenderer2", "_<PERSON><PERSON>", "_Font", "_Color", "windowBounds", "documentBackgroundColor", "TRANSPARENT", "bodyBackgroundColor", "supportForeignObject", "inlineFonts", "ready", "render", "clonedElement", "clonedDocument", "imageStore", "fontMetrics", "FontMetrics", "renderOptions", "_border", "_borderRadius", "_display", "_float", "_font", "_letterSpacing", "_lineBreak", "_margin", "_overflow", "_padding", "_position", "_textShadow", "_textTransform", "_transform", "_visibility", "_wordBreak", "_zIndex", "INPUT_TAGS", "NodeContainer", "listItems", "start", "listStart", "parseDisplay", "IS_INPUT", "parsePosition", "INPUT_BACKGROUND", "parseBackground", "INPUT_BORDERS", "borderRadius", "HTMLInputElement", "getInputBorderRadius", "INPUT_COLOR", "float", "parseCSSFloat", "parseLetterSpacing", "DISPLAY", "LIST_ITEM", "parseLineBreak", "margin", "parse<PERSON><PERSON><PERSON>", "parseOverflow", "OVERFLOW", "HIDDEN", "wordWrap", "padding", "parsePadding", "parseTextDecoration", "textShadow", "parseTextShadow", "textTransform", "parseTextTransform", "parseVisibility", "parseWordBreak", "zIndex", "parseZIndex", "STATIC", "listOwner", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "listIndex", "addEventListener", "curvedBounds", "parseBoundCurves", "image", "getImage", "reformatInputBounds", "parentClips", "getClipPaths", "VISIBLE", "calculatePaddingBoxPath", "isAbsolutelyPositioned", "VISIBILITY", "RELATIVE", "FLOAT", "auto", "INLINE", "INLINE_BLOCK", "INLINE_FLEX", "INLINE_GRID", "INLINE_LIST_ITEM", "INLINE_TABLE", "XMLSerializer", "loadImage", "encodeURIComponent", "serializeToString", "currentSrc", "loadCanvas", "getAttribute", "WORD_BREAK", "BREAK_ALL", "KEEP_ALL", "ABSOLUTE", "FIXED", "STICKY", "Vector", "_extends", "assign", "arguments", "source", "hasOwnProperty", "_Logger2", "_Window", "html2canvas", "conf", "config", "log", "parseDocumentSize", "defaultOptions", "devicePixelRatio", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_Vector2", "lerp", "t", "BezierCurve", "startControl", "endControl", "end", "firstHalf", "ab", "bc", "cd", "abbc", "bccd", "dest", "StackingContext", "getOpacity", "LINE_BREAK", "STRICT", "calculateBackgroundRepeatPath", "calculateBackgroundPosition", "calculateBackgroungPositioningArea", "calculateBackgroungPaintingArea", "calculateGradientBackgroundSize", "calculateBackgroundSize", "BACKGROUND_ORIGIN", "BACKGROUND_CLIP", "BACKGROUND_SIZE", "BACKGROUND_REPEAT", "_Size2", "REPEAT", "NO_REPEAT", "REPEAT_X", "REPEAT_Y", "AUTO", "CONTAIN", "COVER", "LENGTH", "BORDER_BOX", "PADDING_BOX", "CONTENT_BOX", "BackgroundSize", "AUTO_SIZE", "targetRatio", "currentRatio", "parseBackgroundClip", "curves", "clip", "calculateBorderBoxPath", "<PERSON><PERSON><PERSON><PERSON>", "paddingBox", "calculatePaddingBox", "paddingLeft", "PADDING_SIDES", "paddingRight", "paddingTop", "paddingBottom", "backgroundPositioningArea", "repeat", "parseBackgroundImages", "backgroundClip", "parseBackgroundOrigin", "parseBackgroundRepeat", "backgroundRepeat", "sources", "positions", "backgroundPosition", "repeats", "sizes", "backgroundSize", "parseBackgroundSize", "parseBackgoundPosition", "whitespace", "results", "quote", "definition", "mode", "numParen", "appendResult", "prefix_i", "ANGLE", "parsePathForBorder", "calculateContentBox", "_BezierCurve2", "w", "h", "clientRect", "createPathFromCurves", "borders", "Error", "scrollWidth", "offsetWidth", "clientWidth", "scrollHeight", "offsetHeight", "clientHeight", "borderSide", "topLeftOuter", "topLeftInner", "topRightOuter", "topRightInner", "bottomRightOuter", "bottomRightInner", "bottomLeftOuter", "bottomLeftInner", "outer1", "inner1", "outer2", "inner2", "path", "subdivide", "reverse", "CORNER", "tlh", "TOP_LEFT", "tlv", "trh", "TOP_RIGHT", "trv", "brh", "BOTTOM_RIGHT", "brv", "blh", "BOTTOM_LEFT", "blv", "factors", "maxFactor", "apply", "topWidth", "rightHeight", "bottomWidth", "leftHeight", "getCurvePoints", "r1", "r2", "kappa", "ox", "oy", "xm", "ym", "codePoints", "charCodeAt", "extra", "String", "codeUnits", "fromCharCode", "lookup", "Uint8Array", "chars", "bufferLength", "p", "encoded1", "encoded2", "encoded3", "encoded4", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bytes", "_i2", "<PERSON><PERSON>", "enabled", "id", "console", "_len", "_key", "Function", "bind", "error", "_len2", "_key2", "TEXT_TRANSFORM", "LOWERCASE", "UPPERCASE", "CAPITALIZE", "xhr", "XDomainRequest", "status", "responseText", "timeout", "ontimeout", "send", "NUMBER", "currentValue", "<PERSON><PERSON><PERSON><PERSON>", "shadows", "numParens", "appendValue", "appendShadow", "offsetX", "offsetY", "blur", "splice", "_Circle2", "INPUT_BORDER_COLOR", "INPUT_BACKGROUND_COLOR", "INPUT_BORDER", "RADIO_BORDER_RADIUS", "RADIO_BORDER_RADIUS_TUPLE", "INPUT_RADIO_BORDER_RADIUS", "CHECKBOX_BORDER_RADIUS", "CHECKBOX_BORDER_RADIUS_TUPLE", "INPUT_CHECKBOX_BORDER_RADIUS", "inlineFormElement", "getInputValue", "option", "selectedIndex", "allowLinebreak", "whiteSpace", "placeholder", "TEXT_DECORATION_LINE", "TEXT_DECORATION_STYLE", "DOUBLE", "DOTTED", "DASHED", "WAVY", "UNDERLINE", "OVERLINE", "LINE_THROUGH", "BLINK", "parseLine", "line", "textDecorationLine", "textDecorationColor", "textDecorationStyle", "parseTextDecorationStyle", "INLINE_START", "INLINE_END", "ForeignObject<PERSON><PERSON><PERSON>", "floor", "xmlns", "createElementNS", "foreignObject", "setAttributeNS", "inlineBreakOpportunities", "lineBreakAtIndex", "codePointsToCharacterClasses", "UnicodeTrie", "BREAK_ALLOWED", "BREAK_NOT_ALLOWED", "BREAK_MANDATORY", "classes", "LETTER_NUMBER_MODIFIER", "_Trie", "_linebreakTrie2", "SP", "BA", "HY", "CL", "CP", "EX", "IN", "NS", "OP", "IS", "NU", "PO", "PR", "SY", "AL", "EB", "EM", "H2", "H3", "ID", "JL", "JV", "JT", "SA", "BK", "CR", "LF", "CM", "NL", "SG", "WJ", "ZW", "GL", "ZWJ", "B2", "BB", "CB", "QU", "AI", "CJ", "HL", "RI", "XX", "ALPHABETICS", "HARD_LINE_BREAKS", "SPACE", "PREFIX_POSTFIX", "LINE_BREAKS", "KOREAN_SYLLABLE_BLOCK", "HYPHEN", "types", "indicies", "categories", "classType", "prev", "isAdjacentWithSpaceIgnored", "currentIndex", "classTypes", "current", "_next", "previousNonSpaceClassType", "_lineBreakAtIndex", "forbiddenBreaks", "beforeIndex", "afterIndex", "before", "prevIndex", "_prevIndex", "_type", "count", "cssFormattedClasses", "_codePointsToCharacte", "_codePointsToCharacte2", "_codePointsToCharacte3", "_codePointsToCharacte4", "isLetterNumber", "Break", "output", "_cssFormattedClasses", "_cssFormattedClasses2", "forbiddenBreakpoints", "_codePoints", "required", "arr2", "_toConsumableArray", "_cssFormattedClasses3", "_cssFormattedClasses4", "lastEnd", "nextIndex", "createCounterText", "ancestorTypes", "ROMAN_UPPER", "textAlign", "MARGIN_TOP", "styleImage", "integers", "createAdditiveCounter", "symbols", "fallback", "suffix", "string", "integer", "createCounterStyleWithSymbolResolver", "codePointRangeLength", "isNumeric", "resolver", "createCounterStyleFromRange", "codePointRangeStart", "codePointRangeEnd", "createCounterStyleFromSymbols", "createCJKCounter", "numbers", "multipliers", "negativeSign", "flags", "tmp", "digit", "coefficient", "CHINESE_INFORMAL_MULTIPLIERS", "CHINESE_FORMAL_MULTIPLIERS", "JAPANESE_NEGATIVE", "KOREAN_NEGATIVE", "appendSuffix", "defaultSuffix", "cjkSuffix", "koreanSuffix", "CJK_TEN_COEFFICIENTS", "CJK_ZEROS", "HEX3", "HEX6", "RGB", "RGBA", "Color", "array", "hex3", "Number", "rgb", "rgba", "NAMED_COLORS", "hex6", "r", "g", "transparent", "aliceblue", "antiquewhite", "aqua", "aquamarine", "azure", "beige", "bisque", "black", "blanche<PERSON><PERSON>", "blue", "blueviolet", "brown", "burlywood", "cadetblue", "chartreuse", "chocolate", "coral", "cornflowerblue", "cornsilk", "crimson", "cyan", "darkblue", "dark<PERSON>an", "darkgoldenrod", "darkgray", "darkgreen", "<PERSON><PERSON>rey", "<PERSON><PERSON><PERSON>", "darkmagenta", "darkolivegreen", "darkorange", "darkorchid", "darkred", "<PERSON><PERSON><PERSON>", "darkseagreen", "darkslateblue", "darkslategray", "darkslateg<PERSON>", "darkturquoise", "darkviolet", "deeppink", "deepskyblue", "dimgray", "<PERSON><PERSON><PERSON>", "dodgerblue", "firebrick", "<PERSON><PERSON><PERSON><PERSON>", "forestgreen", "fuchsia", "gainsboro", "ghostwhite", "gold", "goldenrod", "gray", "green", "greenyellow", "grey", "honeydew", "hotpink", "indianred", "indigo", "ivory", "khaki", "lavender", "lavenderblush", "lawngreen", "lemon<PERSON>ffon", "lightblue", "lightcoral", "lightcyan", "lightgoldenrodyellow", "lightgray", "lightgreen", "<PERSON><PERSON>rey", "lightpink", "<PERSON><PERSON><PERSON>", "lightseagreen", "lightskyblue", "lightslategray", "lightslategrey", "lightsteelblue", "lightyellow", "lime", "limegreen", "linen", "magenta", "maroon", "mediumaquamarine", "mediumblue", "mediumorchid", "mediumpurple", "mediumseagreen", "mediumslateblue", "mediumspringgreen", "mediumturquoise", "mediumvioletred", "midnightblue", "mintcream", "mistyrose", "moccasin", "navajowhite", "navy", "oldlace", "olive", "<PERSON><PERSON><PERSON>", "orange", "orangered", "orchid", "palegoldenrod", "palegreen", "paleturquoise", "palevioletred", "papayawhip", "peachpuff", "peru", "pink", "plum", "powderblue", "purple", "rebeccapurple", "red", "rosybrown", "royalblue", "saddlebrown", "salmon", "sandybrown", "seagreen", "seashell", "sienna", "silver", "skyblue", "slateblue", "slategray", "<PERSON><PERSON><PERSON>", "snow", "springgreen", "steelblue", "tan", "teal", "thistle", "tomato", "turquoise", "violet", "wheat", "white", "whitesmoke", "yellow", "yellowgreen", "addColorStops", "gradient", "canvasGradient", "maxStop", "colorStop", "f", "addColorStop", "customCanvas", "translate", "textBaseline", "clipPaths", "callback", "save", "restore", "destination", "fill", "_path", "beginPath", "point", "moveTo", "lineTo", "bezierCurveTo", "arc", "closePath", "linearGradient", "createLinearGradient", "radialGradient", "createRadialGradient", "midX", "midY", "invF", "imageSize", "createPattern", "resizeImage", "textShadows", "_this4", "shadowColor", "shadowOffsetX", "shadowOffsetY", "<PERSON><PERSON><PERSON><PERSON>", "fillText", "baseline", "getMetrics", "rectangle", "middle", "globalAlpha", "COLLAPSE", "_Gradient", "<PERSON><PERSON><PERSON>", "renderNodeBackgroundAndBorders", "renderNodeContent", "renderTextNode", "drawShape", "_image", "contentBox", "_width", "_height", "paths", "HAS_BACKGROUND", "hasRenderableBorders", "some", "backgroundPaintingArea", "renderBackgroundImage", "renderBorder", "renderBackgroundRepeat", "renderBackgroundGradient", "backgroundImageSize", "_offsetX", "_offsetY", "renderRepeat", "gradientBounds", "renderLinearGradient", "renderRadialGradient", "curvePoints", "_opacity", "setOpacity", "renderStackContent", "_splitStackingContext", "splitStackingContexts", "_splitStackingContext2", "negativeZIndex", "zeroOrAutoZIndexOrTransformedOrOpacity", "positiveZIndex", "nonPositionedFloats", "nonPositionedInlineLevel", "_splitDescendants", "splitDescendants", "_splitDescendants2", "inlineLevel", "nonInlineLevel", "sort", "sortByZIndex", "renderStack", "renderNode", "get<PERSON><PERSON><PERSON>", "isInlineLevel", "order", "SCROLL", "ResourceStore", "Resource<PERSON><PERSON>der", "_window", "<PERSON><PERSON><PERSON><PERSON>", "location", "_index", "hasResourceInCache", "isSVG", "isInlineImage", "isSameOrigin", "addImage", "_loadImage", "xhrImage", "imageLoadHandler", "supportsDataImages", "isInlineBase64Image", "url", "link", "_link", "protocol", "hostname", "port", "_this5", "images", "resources", "_keys", "_resources", "INLINE_SVG", "INLINE_BASE64", "INLINE_IMG", "SAMPLE_TEXT", "_data", "_document", "span", "verticalAlign", "offsetTop", "lineHeight", "_parseMetrics", "_TextBounds", "TextContainer", "replace", "capitalize", "toUpperCase", "p1", "p2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TOKEN_TYPE", "STRING", "ATTRIBUTE", "URL", "COUNTER", "COUNTERS", "OPENQUOTE", "CLOSEQUOTE", "counterReset", "counterNames", "counterResets", "lenCounterResets", "_counterResets$i$spli", "_counterResets$i$spli2", "counterName", "counter", "len<PERSON><PERSON><PERSON>s", "pop", "tokens", "counterIncrement", "_counterIncrement$spl", "_counterIncrement$spl2", "incrementValue", "token", "_counter", "formatCounterValue", "_counters", "glue", "getQuote", "isString", "isEscaped", "isFunction", "functionName", "char<PERSON>t", "_counters2", "addOtherToken", "identifier", "isOpening", "quotes", "BLOCK", "RUN_IN", "FLOW", "FLOW_ROOT", "TABLE", "FLEX", "GRID", "RUBY", "SUBGRID", "TABLE_ROW_GROUP", "TABLE_HEADER_GROUP", "TABLE_FOOTER_GROUP", "TABLE_ROW", "TABLE_CELL", "TABLE_COLUMN_GROUP", "TABLE_COLUMN", "TABLE_CAPTION", "RUBY_BASE", "RUBY_TEXT", "RUBY_BASE_CONTAINER", "RUBY_TEXT_CONTAINER", "CONTENTS", "setDisplayBit", "parseDisplayValue"], "sourceRoot": ""}