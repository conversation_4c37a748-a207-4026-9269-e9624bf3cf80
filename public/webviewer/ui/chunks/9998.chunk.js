(self.webpackChunkwebviewer_ui=self.webpackChunkwebviewer_ui||[]).push([[9998],{39998:function(e,n,t){e.exports=function(e){"use strict";var n=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(e);function t(e){return e%100==2}function r(e){return e%100==3||e%100==4}function a(e,n,a,u){var m=e+" ";switch(a){case"s":return n||u?"nekaj sekund":"nekaj sekundami";case"m":return n?"ena minuta":"eno minuto";case"mm":return t(e)?m+(n||u?"minuti":"minutama"):r(e)?m+(n||u?"minute":"minutami"):m+(n||u?"minut":"minutami");case"h":return n?"ena ura":"eno uro";case"hh":return t(e)?m+(n||u?"uri":"urama"):r(e)?m+(n||u?"ure":"urami"):m+(n||u?"ur":"urami");case"d":return n||u?"en dan":"enim dnem";case"dd":return t(e)?m+(n||u?"dneva":"dnevoma"):m+(n||u?"dni":"dnevi");case"M":return n||u?"en mesec":"enim mesecem";case"MM":return t(e)?m+(n||u?"meseca":"mesecema"):r(e)?m+(n||u?"mesece":"meseci"):m+(n||u?"mesecev":"meseci");case"y":return n||u?"eno leto":"enim letom";case"yy":return t(e)?m+(n||u?"leti":"letoma"):r(e)?m+(n||u?"leta":"leti"):m+(n||u?"let":"leti")}}var u={name:"sl",weekdays:"nedelja_ponedeljek_torek_sreda_četrtek_petek_sobota".split("_"),months:"januar_februar_marec_april_maj_junij_julij_avgust_september_oktober_november_december".split("_"),weekStart:1,weekdaysShort:"ned._pon._tor._sre._čet._pet._sob.".split("_"),monthsShort:"jan._feb._mar._apr._maj._jun._jul._avg._sep._okt._nov._dec.".split("_"),weekdaysMin:"ne_po_to_sr_če_pe_so".split("_"),ordinal:function(e){return e+"."},formats:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd, D. MMMM YYYY H:mm",l:"D. M. YYYY"},relativeTime:{future:"čez %s",past:"pred %s",s:a,m:a,mm:a,h:a,hh:a,d:a,dd:a,M:a,MM:a,y:a,yy:a}};return n.default.locale(u,null,!0),u}(t(74353))}}]);
//# sourceMappingURL=9998.chunk.js.map