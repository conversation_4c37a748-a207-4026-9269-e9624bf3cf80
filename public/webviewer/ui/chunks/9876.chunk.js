/*! For license information please see 9876.chunk.js.LICENSE.txt */
(self.webpackChunkwebviewer_ui=self.webpackChunkwebviewer_ui||[]).push([[9876],{23431:(e,t,n)=>{(t=n(76314)(!1)).push([e.id,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.MainHeader.OfficeEditorTools{padding:0 8px;align-items:center;flex-direction:row}.MainHeader.OfficeEditorTools .HeaderItems{width:auto}.MainHeader.OfficeEditorTools .HeaderItems .Dropdown{margin-left:8px}.MainHeader.OfficeEditorTools .HeaderItems .Dropdown .picked-option{text-align:left}.MainHeader.OfficeEditorTools .HeaderItems .action-button-wrapper{display:flex;align-items:center;position:relative}.MainHeader.OfficeEditorTools .HeaderItems .action-button-wrapper .more-tools{position:absolute;top:46px;right:6px;width:auto;padding:0}.MainHeader.OfficeEditorTools .Button .Icon svg{vertical-align:middle}.MainHeader.OfficeEditorTools .icon-text-color{display:flex;align-items:center;justify-content:center}.MainHeader.OfficeEditorTools .list-style-button{margin-right:0!important}.MainHeader.OfficeEditorTools .list-style-dropdown{background:transparent;border:none;width:12px!important;height:32px!important;margin-left:0!important;z-index:0}.MainHeader.OfficeEditorTools .list-style-dropdown.Dropdown__wrapper:hover{border-radius:4px;background:var(--tools-button-hover)}.MainHeader.OfficeEditorTools .list-style-dropdown .Dropdown{padding:0}.MainHeader.OfficeEditorTools .list-style-dropdown .Dropdown svg rect{stroke:none}.MainHeader.OfficeEditorTools .list-style-dropdown .picked-option-text{display:none}.MainHeader.OfficeEditorTools .list-style-dropdown .arrow{padding-left:0}.MainHeader.OfficeEditorTools .list-style-dropdown .Dropdown__items{overflow:hidden}.MainHeader.OfficeEditorTools .list-style-dropdown .Dropdown__items .Dropdown__item{height:74px!important;padding:15px 0;margin:3px;cursor:pointer}.MainHeader.OfficeEditorTools .list-style-dropdown .Dropdown__items .Dropdown__item .officeEditor-list-style-icon{width:60px!important;height:74px!important}.MainHeader.OfficeEditorTools .insert-table-dropdown{margin:0 6px}.MainHeader.OfficeEditorTools .insert-table-dropdown.open{background-color:var(--tools-button-hover);border-radius:4px}.MainHeader.OfficeEditorTools .insert-table-dropdown .display-button{display:flex;align-items:center;cursor:pointer}.MainHeader.OfficeEditorTools .insert-table-dropdown .display-button:hover{background-color:var(--tools-button-hover);border-radius:4px}.MainHeader.OfficeEditorTools .insert-table-dropdown .display-button .Button{background-color:transparent!important}.MainHeader.OfficeEditorTools .insert-table-dropdown .display-button .Icon.arrow{width:12px;height:12px;margin:0 2px}",""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"},e.exports=t},30114:(e,t,n)=>{var r=n(85072),o=n(56383);"string"==typeof(o=o.__esModule?o.default:o)&&(o=[[e.id,o,""]]),r(o,{insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const r=[];return n.querySelectorAll(t).forEach((e=>r.push(e))),n.querySelectorAll("*").forEach((n=>{n.shadowRoot&&r.push(...e(t,n.shadowRoot))})),r}("apryse-webviewer"));const n=[];for(let r=0;r<t.length;r++){const o=t[r];if(0===r)o.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach((t=>{t.innerHTML=e.innerHTML}))};else{const t=e.cloneNode(!0);o.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1}),e.exports=o.locals||{}},39876:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>V}),n(52675),n(89463),n(2259),n(45700),n(2008),n(50113),n(51629),n(23418),n(74423),n(64346),n(23792),n(62062),n(72712),n(94490),n(34782),n(89572),n(23288),n(62010),n(2892),n(59904),n(67945),n(84185),n(83851),n(81278),n(40875),n(79432),n(10287),n(26099),n(16034),n(58940),n(3362),n(27495),n(38781),n(21699),n(47764),n(5745),n(20781),n(23500),n(62953);var r=n(96540),o=n(61113),i=n(26247),a=n(75710),c=n(40367),l=n(28854),s=n(20714),u=n(59163),d=n(48822),f=n(21012),p=(n(48598),n(59844)),m=n(97160);function h(e){return h="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},h(e)}function y(){y=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function s(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,n){return e[t]=n}}function u(e,t,n,r){var i=t&&t.prototype instanceof w?t:w,a=Object.create(i.prototype),c=new j(r||[]);return o(a,"_invoke",{value:C(e,n,c)}),a}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=u;var f="suspendedStart",p="suspendedYield",m="executing",v="completed",g={};function w(){}function E(){}function b(){}var x={};s(x,a,(function(){return this}));var A=Object.getPrototypeOf,O=A&&A(A(I([])));O&&O!==n&&r.call(O,a)&&(x=O);var k=b.prototype=w.prototype=Object.create(x);function S(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function L(e,t){function n(o,i,a,c){var l=d(e[o],e,i);if("throw"!==l.type){var s=l.arg,u=s.value;return u&&"object"==h(u)&&r.call(u,"__await")?t.resolve(u.__await).then((function(e){n("next",e,a,c)}),(function(e){n("throw",e,a,c)})):t.resolve(u).then((function(e){s.value=e,a(s)}),(function(e){return n("throw",e,a,c)}))}c(l.arg)}var i;o(this,"_invoke",{value:function(e,r){function o(){return new t((function(t,o){n(e,r,t,o)}))}return i=i?i.then(o,o):o()}})}function C(t,n,r){var o=f;return function(i,a){if(o===m)throw Error("Generator is already running");if(o===v){if("throw"===i)throw a;return{value:e,done:!0}}for(r.method=i,r.arg=a;;){var c=r.delegate;if(c){var l=P(c,r);if(l){if(l===g)continue;return l}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===f)throw o=v,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=m;var s=d(t,n,r);if("normal"===s.type){if(o=r.done?v:p,s.arg===g)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(o=v,r.method="throw",r.arg=s.arg)}}}function P(t,n){var r=n.method,o=t.iterator[r];if(o===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,P(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),g;var i=d(o,t.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,g;var a=i.arg;return a?a.done?(n[t.resultName]=a.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,g):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,g)}function T(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function _(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function j(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(T,this),this.reset(!0)}function I(t){if(t||""===t){var n=t[a];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,i=function n(){for(;++o<t.length;)if(r.call(t,o))return n.value=t[o],n.done=!1,n;return n.value=e,n.done=!0,n};return i.next=i}}throw new TypeError(h(t)+" is not iterable")}return E.prototype=b,o(k,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:E,configurable:!0}),E.displayName=s(b,l,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===E||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,b):(e.__proto__=b,s(e,l,"GeneratorFunction")),e.prototype=Object.create(k),e},t.awrap=function(e){return{__await:e}},S(L.prototype),s(L.prototype,c,(function(){return this})),t.AsyncIterator=L,t.async=function(e,n,r,o,i){void 0===i&&(i=Promise);var a=new L(u(e,n,r,o),i);return t.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},S(k),s(k,l,"Generator"),s(k,a,(function(){return this})),s(k,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=I,j.prototype={constructor:j,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(_),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function o(r,o){return c.type="throw",c.arg=t,n.next=r,o&&(n.method="next",n.arg=e),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var l=r.call(a,"catchLoc"),s=r.call(a,"finallyLoc");if(l&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,g):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),_(n),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;_(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:I(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),g}},t}function v(e,t,n,r,o,i,a){try{var c=e[i](a),l=c.value}catch(e){return void n(e)}c.done?t(l):Promise.resolve(l).then(r,o)}n(25702);var g=["jpg","jpeg","png","bmp"].map((function(e){return".".concat(e)})).join(", "),w=function(e){return new Promise((function(t,n){var r=new FileReader;r.readAsDataURL(e),r.onload=function(){return t(r.result)},r.onerror=n}))};const E=function(){var e=(0,o.wA)(),t=function(){var t,n=(t=y().mark((function t(n){var r,o,a;return y().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!(r=n.target.files[0])){t.next=18;break}return t.prev=2,e(i.A.openElement(m.A.LOADING_MODAL)),t.next=6,w(r);case 6:return o=t.sent,t.next=9,l.A.getOfficeEditor().insertImageAtCursor(o);case 9:e(i.A.closeElement(m.A.LOADING_MODAL)),t.next=16;break;case 12:t.prev=12,t.t0=t.catch(2),e(i.A.closeElement(m.A.LOADING_MODAL)),e(i.A.showWarningMessage({title:"Error",message:t.t0}));case 16:(a=(0,p.Ay)().querySelector("#office-editor-file-picker"))&&(a.value="");case 18:case"end":return t.stop()}}),t,null,[[2,12]])})),function(){var e=this,n=arguments;return new Promise((function(r,o){var i=t.apply(e,n);function a(e){v(i,r,o,a,c,"next",e)}function c(e){v(i,r,o,a,c,"throw",e)}a(void 0)}))});return function(e){return n.apply(this,arguments)}}();return r.createElement("div",{className:"FilePickerHandler"},r.createElement("input",{id:"office-editor-file-picker",type:"file",accept:g,onChange:t}))};var b=n(40961),x=n(64226),A=n(33100),O=n(42688),k=n(18439),S=n(46942),L=n.n(S);function C(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,c=[],l=!0,s=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=i.call(n)).done)&&(c.push(r.value),c.length!==t);l=!0);}catch(e){s=!0,o=e}finally{try{if(!l&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}(e,t)||function(e,t){if(e){if("string"==typeof e)return P(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?P(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function P(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}n(30114);const T=function(e){var t=e.color,n=e.onStyleChange,c=e.portalElementId,l=void 0===c?"app":c,s=C((0,r.useState)((function(){return{left:"555px",right:"auto",top:"auto"}})),2),u=s[0],d=s[1],m=(0,r.useRef)(null),h=C((0,o.d4)((function(e){return[a.A.isElementOpen(e,"colorPickerOverlay")]})),1)[0],y=(0,o.wA)();return(0,O.A)(m,(function(e){var t=document.querySelector('[data-element="textColorButton"]');(null==t?void 0:t.contains(e.target))||y(i.A.closeElements(["colorPickerOverlay"]))})),(0,r.useLayoutEffect)((function(){if(h){var e=function(){var e=(0,k.A)("textColorButton",m);d(e)};return e(),window.addEventListener("resize",e),function(){return window.removeEventListener("resize",e)}}}),[h]),(0,b.createPortal)(r.createElement(f.A,{"data-element":"colorPickerOverlay",className:L()({ColorPickerOverlay:!0,Popup:!0,open:h,closed:!h}),style:u,ref:m},r.createElement(x.A,{color:t,property:"TextColor",onStyleChange:n,useMobileMinMaxWidth:!0}),r.createElement(A.A,{color:t,onStyleChange:n,enableEdit:!0})),(0,p.Ay)().getElementById(l))};var _=n(11623),j=n(79318),I=n(14713),H=n(33311),N=n(88219);n(99238),n(46674),n(14422);var M=n(30107);function D(e){return D="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},D(e)}function F(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function R(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?F(Object(n),!0).forEach((function(t){Y(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):F(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function B(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,c=[],l=!0,s=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=i.call(n)).done)&&(c.push(r.value),c.length!==t);l=!0);}catch(e){s=!0,o=e}finally{try{if(!l&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}(e,t)||function(e,t){if(e){if("string"==typeof e)return G(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?G(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function G(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function z(){z=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function s(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,n){return e[t]=n}}function u(e,t,n,r){var i=t&&t.prototype instanceof v?t:v,a=Object.create(i.prototype),c=new T(r||[]);return o(a,"_invoke",{value:S(e,n,c)}),a}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=u;var f="suspendedStart",p="suspendedYield",m="executing",h="completed",y={};function v(){}function g(){}function w(){}var E={};s(E,a,(function(){return this}));var b=Object.getPrototypeOf,x=b&&b(b(_([])));x&&x!==n&&r.call(x,a)&&(E=x);var A=w.prototype=v.prototype=Object.create(E);function O(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function k(e,t){function n(o,i,a,c){var l=d(e[o],e,i);if("throw"!==l.type){var s=l.arg,u=s.value;return u&&"object"==D(u)&&r.call(u,"__await")?t.resolve(u.__await).then((function(e){n("next",e,a,c)}),(function(e){n("throw",e,a,c)})):t.resolve(u).then((function(e){s.value=e,a(s)}),(function(e){return n("throw",e,a,c)}))}c(l.arg)}var i;o(this,"_invoke",{value:function(e,r){function o(){return new t((function(t,o){n(e,r,t,o)}))}return i=i?i.then(o,o):o()}})}function S(t,n,r){var o=f;return function(i,a){if(o===m)throw Error("Generator is already running");if(o===h){if("throw"===i)throw a;return{value:e,done:!0}}for(r.method=i,r.arg=a;;){var c=r.delegate;if(c){var l=L(c,r);if(l){if(l===y)continue;return l}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===f)throw o=h,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=m;var s=d(t,n,r);if("normal"===s.type){if(o=r.done?h:p,s.arg===y)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(o=h,r.method="throw",r.arg=s.arg)}}}function L(t,n){var r=n.method,o=t.iterator[r];if(o===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,L(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),y;var i=d(o,t.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,y;var a=i.arg;return a?a.done?(n[t.resultName]=a.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,y):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,y)}function C(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function P(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function T(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(C,this),this.reset(!0)}function _(t){if(t||""===t){var n=t[a];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,i=function n(){for(;++o<t.length;)if(r.call(t,o))return n.value=t[o],n.done=!1,n;return n.value=e,n.done=!0,n};return i.next=i}}throw new TypeError(D(t)+" is not iterable")}return g.prototype=w,o(A,"constructor",{value:w,configurable:!0}),o(w,"constructor",{value:g,configurable:!0}),g.displayName=s(w,l,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===g||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,s(e,l,"GeneratorFunction")),e.prototype=Object.create(A),e},t.awrap=function(e){return{__await:e}},O(k.prototype),s(k.prototype,c,(function(){return this})),t.AsyncIterator=k,t.async=function(e,n,r,o,i){void 0===i&&(i=Promise);var a=new k(u(e,n,r,o),i);return t.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},O(A),s(A,l,"Generator"),s(A,a,(function(){return this})),s(A,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=_,T.prototype={constructor:T,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(P),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function o(r,o){return c.type="throw",c.arg=t,n.next=r,o&&(n.method="next",n.arg=e),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var l=r.call(a,"catchLoc"),s=r.call(a,"finallyLoc");if(l&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,y):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),P(n),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;P(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:_(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),y}},t}function U(e,t,n,r,o,i,a){try{var c=e[i](a),l=c.value}catch(e){return void n(e)}c.done?t(l):Promise.resolve(l).then(r,o)}function W(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var i=e.apply(t,n);function a(e){U(i,r,o,a,c,"next",e)}function c(e){U(i,r,o,a,c,"throw",e)}a(void 0)}))}}function Y(e,t,n){return(t=function(e){var t=function(e){if("object"!=D(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=D(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==D(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var q=window.Core.Document.OfficeEditorToggleableStyles,Q=function(e){var t=e.activeStates;return Object.values(q).map((function(e){return r.createElement(u.A,{key:e,isActive:t[e],onClick:function(){l.A.getOfficeEditor().updateSelectionAndCursorStyle(Y({},e,!0))},dataElement:"office-editor-".concat(e),title:"officeEditor.".concat(e),img:"icon-text-".concat(e)})}))},Z=function(e){var t=e.justification;return r.createElement(r.Fragment,null,r.createElement(u.A,{isActive:t===H.O2.Left,dataElement:"office-editor-left-align",title:"officeEditor.leftAlign",img:"icon-menu-left-align",onClick:function(){l.A.getOfficeEditor().updateParagraphStyle({justification:"left"})}}),r.createElement(u.A,{isActive:t===H.O2.Center,dataElement:"office-editor-center-align",title:"officeEditor.centerAlign",img:"icon-menu-centre-align",onClick:function(){l.A.getOfficeEditor().updateParagraphStyle({justification:"center"})}}),r.createElement(u.A,{isActive:t===H.O2.Right,dataElement:"office-editor-right-align",title:"officeEditor.rightAlign",img:"icon-menu-right-align",onClick:function(){l.A.getOfficeEditor().updateParagraphStyle({justification:"right"})}}),r.createElement(u.A,{isActive:t===H.O2.Both,dataElement:"office-editor-justify",title:"officeEditor.justify",img:"icon-menu-both-align",onClick:function(){l.A.getOfficeEditor().updateParagraphStyle({justification:"both"})}}))},K=function(e){var t=e.listType,n=H.f0.map((function(e){return{className:"officeEditor-list-style-icon",key:e.enum,src:e.img}})),o=H.i3.map((function(e){return{className:"officeEditor-list-style-icon",key:e.enum,src:e.img}}));return r.createElement(r.Fragment,null,r.createElement(u.A,{isActive:t===H.yU.Unordered,dataElement:"office-editor-bullet-list",title:"officeEditor.bulletList",img:"icon-office-editor-bullet-list",className:"list-style-button",onClick:function(){l.A.getOfficeEditor().toggleListSelection(H.yU.Unordered)}}),r.createElement(s.A,{dataElement:"office-editor-bullet-list-dropdown",images:n,columns:3,onClickItem:function(e){l.A.getOfficeEditor().setListPreset(e)},className:"list-style-dropdown"}),r.createElement(u.A,{isActive:t===H.yU.Ordered,dataElement:"office-editor-number-list",title:"officeEditor.numberList",img:"icon-office-editor-number-list",className:"list-style-button",onClick:function(){l.A.getOfficeEditor().toggleListSelection(H.yU.Ordered)}}),r.createElement(s.A,{dataElement:"office-editor-number-list-dropdown",images:o,columns:3,onClickItem:function(e){l.A.getOfficeEditor().setListPreset(e)},className:"list-style-dropdown"}),r.createElement(u.A,{dataElement:"office-editor-decrease-indent",title:"officeEditor.decreaseIndent",img:"ic-indent-decrease",onClick:W(z().mark((function e(){return z().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,l.A.getOfficeEditor().decreaseIndent();case 2:case"end":return e.stop()}}),e)})))}),r.createElement(u.A,{dataElement:"office-editor-increase-indent",title:"officeEditor.increaseIndent",img:"ic-indent-increase",onClick:W(z().mark((function e(){return z().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,l.A.getOfficeEditor().increaseIndent();case 2:case"end":return e.stop()}}),e)})))}))};const V=function(){var e,t=(0,o.wA)(),n=B((0,o.d4)((function(e){return[a.A.isElementOpen(e,m.A.OFFICE_EDITOR_TOOLS_HEADER),a.A.getOfficeEditorCursorProperties(e),a.A.getOfficeEditorSelectionProperties(e),a.A.getAvailableFontFaces(e),a.A.getActiveTheme(e),a.A.getCSSFontValues(e)]}),o.bN),6),h=n[0],y=n[1],v=n[2],g=n[3],w=n[4],b=n[5],x=B((0,r.useState)(0),2),A=x[0],O=x[1],k=B((0,r.useState)(0),2),S=k[0],L=k[1],C=B((0,r.useState)(6),2),P=C[0],D=C[1],F=B((0,r.useState)(!1),2),G=F[0],U=F[1];(0,r.useEffect)((function(){var e,n=function(){var e=W(z().mark((function e(n){return z().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t(i.A.setOfficeEditorCursorProperties(n));case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),r=function(e){t(i.A.setOfficeEditorSelectionProperties(e))};return l.A.getDocument().addEventListener("cursorPropertiesUpdated",n),null===(e=l.A.getDocument())||void 0===e||e.addEventListener("selectionPropertiesUpdated",r),function(){l.A.getDocument().removeEventListener("selectionPropertiesUpdated",r),l.A.getDocument().removeEventListener("cursorPropertiesUpdated",n)}}),[]),(0,r.useEffect)((function(){y.fontFace&&!g.includes(y.fontFace)&&t(i.A.addOfficeEditorAvailableFontFace(y.fontFace))}),[g,y]),(0,r.useEffect)((function(){if(0!==A&&0!==S){var e=A-16;D(e>=S?6:e<S&&e>=S-121+77?5:e<S-121+77&&e>=S-121-209+77&&e>=858?4:3)}}),[A,S]);var Y,q=l.A.getOfficeEditor().isTextSelected()?v:y,V=q.bold,X=q.italic,$="single"===q.underlineStyle,J=q.fontFace||"",ee=q.pointSize,te=void 0===ee?"":ee.toString(),ne=q.paragraphProperties.justification,re=function(e,t,n){var r=t?t/n:e;switch(Object.values(H.WU).reduce((function(e,t){var n=Math.abs(e-r),o=Math.abs(t-r);return n===o?e<t?e:t:o<n?t:e}))){case 1:default:return"Single";case 1.15:return"1.15";case 1.5:return"1.5";case 2:return"Double"}}(q.paragraphProperties.lineHeightMultiplier,q.paragraphProperties.lineHeight,y.paragraphProperties.fontPointSize||H.P5),oe=q.paragraphProperties.listType,ie=w===I.A.LIGHT,ae=(Y=q.color)?new window.Core.Annotations.Color(Y.r,Y.g,Y.b,1):new window.Core.Annotations.Color(0,0,0,1),ce=ie?"rgba(255,255,255,1)"===ae.toString():"rgba(0,0,0,1)"===ae.toString(),le=null==ae||null===(e=ae.toHexString)||void 0===e?void 0:e.call(ae),se=function(e){var t=e||{},n=t.pointSize,r=t.color,o="Normal Text";if(!n||!r)return o;var i="".concat(n,"pt"),a=M.zb.black;return a&&(a=(0,N.H)(r.r,r.g,r.b).slice(0,-2)),Object.keys(H.ZQ).find((function(e){return H.ZQ[e].fontSize===i&&H.ZQ[e].color===a}))||o};return h?r.createElement(f.A,{dataElement:m.A.OFFICE_EDITOR_TOOLS_HEADER,className:"HeaderToolsContainer"},r.createElement(c.A,{bounds:!0,onResize:function(e){var t=e.bounds;O(t.width)}},(function(e){var n=e.measureRef;return r.createElement("div",{className:"MainHeader Tools OfficeEditorTools",ref:n},r.createElement(c.A,{bounds:!0,onResize:function(e){var t=e.bounds;0===S&&L(t.width)}},(function(e){var n=e.measureRef;return r.createElement("div",{className:"HeaderItems",ref:n},r.createElement(s.A,{items:Object.keys(H.ZQ),onOpened:function(){return U(!1)},onClickItem:function(){var e=W(z().mark((function e(t){var n,r,o,i,a;return z().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=H.ZQ[t],r=parseInt(n.fontSize,10),o=new window.Core.Annotations.Color(n.color),i={r:o.R,g:o.G,b:o.B,a:255},a={bold:!1,italic:!1,underline:!1,pointSize:r,color:i},e.next=7,l.A.getOfficeEditor().updateParagraphStylePresets(a);case 7:return e.next=9,l.A.getOfficeEditor().setMainCursorStyle(a);case 9:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),getCustomItemStyle:function(e){return R(R({},H.ZQ[e]),{},{padding:"20px 10px",color:"var(--gray-8)"})},applyCustomStyleToButton:!1,currentSelectionKey:se(q),width:160,dataElement:"office-editor-text-format"}),r.createElement(s.A,{items:g,onOpened:function(){return U(!1)},onClickItem:function(e){"string"==typeof e&&l.A.getOfficeEditor().updateSelectionAndCursorStyle({fontFace:e})},getCustomItemStyle:function(e){return R({},b[e])},maxHeight:500,customDataValidator:function(e){return g.includes(e)},width:160,dataElement:"office-editor-font",currentSelectionKey:J,hasInput:!0}),r.createElement(s.A,{items:H.eI,onOpened:function(){return U(!1)},onClickItem:function(e){var t=parseInt(e,10);isNaN(t)&&(t=H.P5),t>H.SG.MAX?t=H.SG.MAX:t<H.SG.MIN&&(t=H.SG.MIN),l.A.getOfficeEditor().updateSelectionAndCursorStyle({pointSize:t})},currentSelectionKey:te,width:80,dataElement:"office-editor-font-size",hasInput:!0,isSearchEnabled:!1}),P>=4&&r.createElement(r.Fragment,null,r.createElement("div",{className:"divider"}),r.createElement(Q,{activeStates:{bold:V,italic:X,underline:$}})),r.createElement("div",{className:"divider"}),r.createElement(d.A,{onClick:function(){return U(!1)},dataElement:"textColorButton",title:"officeEditor.textColor",ariaLabel:le,img:"icon-office-editor-circle",element:"colorPickerOverlay",color:ae.toString(),iconClassName:"".concat(ce?"icon-border":""," icon-text-color")}),r.createElement(T,{onStyleChange:function(e,n){var r={r:n.R,g:n.G,b:n.B,a:255};l.A.getOfficeEditor().updateSelectionAndCursorStyle({color:r}),t(i.A.closeElements(["colorPickerOverlay"]))},color:ae}),P>=5&&r.createElement(r.Fragment,null,r.createElement("div",{className:"divider"}),r.createElement(Z,{justification:ne})),r.createElement("div",{className:"divider"}),r.createElement(s.A,{items:Object.keys(H.WU),onClickItem:function(e){var t=H.WU[e];l.A.getOfficeEditor().updateParagraphStyle({lineHeightMultiplier:t}),l.A.getOfficeEditor().setMainCursorStyle({lineHeight:re})},currentSelectionKey:re,width:80,dataElement:"office-editor-line-spacing",displayButton:function(e){return r.createElement(u.A,{title:"officeEditor.lineSpacing",img:"icon-office-editor-line-spacing",isActive:e,onClick:function(){return U(!1)}})}}),r.createElement("div",{className:"divider"}),r.createElement(s.A,{dataElement:m.A.OFFICE_EDITOR_TOOLS_HEADER_INSERT_TABLE,className:"insert-table-dropdown",displayButton:function(e){return r.createElement(r.Fragment,null,r.createElement(u.A,{title:"officeEditor.table",img:"ic-table",isActive:e}),r.createElement(_.A,{className:"arrow",glyph:"icon-chevron-".concat(e?"up":"down")}))}},r.createElement(j.A,null)),r.createElement(r.Fragment,null,r.createElement(u.A,{className:"tool-group-button",dataElement:m.A.OFFICE_EDITOR_TOOLS_HEADER_INSERT_IMAGE,title:"officeEditor.insertImage",img:"icon-tool-image-line",onClick:function(){var e;null===(e=(0,p.Ay)().querySelector("#office-editor-file-picker"))||void 0===e||e.click()}}),r.createElement(E,null)),6===P&&r.createElement(r.Fragment,null,r.createElement("div",{className:"divider"}),r.createElement(K,{listType:oe})),P<6&&r.createElement(r.Fragment,null,r.createElement("div",{className:"divider"}),r.createElement("div",{className:"action-button-wrapper"},r.createElement(u.A,{className:"tool-group-button",isActive:G,dataElement:"office-editor-more-tools",title:"action.more",img:"icon-tools-more-vertical",onClick:function(){return U(!G)}}),G&&r.createElement("div",{className:"more-tools MainHeader Tools OfficeEditorTools"},r.createElement("div",{className:"HeaderItems"},P<4&&r.createElement(r.Fragment,null,r.createElement(Q,{activeStates:{bold:V,italic:X,underline:$}}),r.createElement("div",{className:"divider"})),P<5&&r.createElement(r.Fragment,null,r.createElement(Z,{justification:ne}),r.createElement("div",{className:"divider"})),P<6&&r.createElement(K,{listType:oe}))))))})))}))):null}},46674:(e,t,n)=>{var r=n(85072),o=n(23431);"string"==typeof(o=o.__esModule?o.default:o)&&(o=[[e.id,o,""]]),r(o,{insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const r=[];return n.querySelectorAll(t).forEach((e=>r.push(e))),n.querySelectorAll("*").forEach((n=>{n.shadowRoot&&r.push(...e(t,n.shadowRoot))})),r}("apryse-webviewer"));const n=[];for(let r=0;r<t.length;r++){const o=t[r];if(0===r)o.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach((t=>{t.innerHTML=e.innerHTML}))};else{const t=e.cloneNode(!0);o.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1}),e.exports=o.locals||{}},56383:(e,t,n)=>{(t=n(76314)(!1)).push([e.id,".open.ColorPickerOverlay{visibility:visible}.closed.ColorPickerOverlay{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.ColorPickerOverlay{position:absolute;z-index:70;justify-content:center;align-items:center}.ColorPickerOverlay:empty{padding:0}.ColorPickerOverlay .buttons{display:flex}.ColorPickerOverlay .Button{margin:4px;width:32px;height:32px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ColorPickerOverlay .Button{width:42px;height:42px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .ColorPickerOverlay .Button{width:42px;height:42px}}.ColorPickerOverlay .Button:hover{background:var(--popup-button-hover)}.ColorPickerOverlay .Button:hover:disabled{background:none}.ColorPickerOverlay .Button .Icon{width:18px;height:18px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ColorPickerOverlay .Button .Icon{width:24px;height:24px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .ColorPickerOverlay .Button .Icon{width:24px;height:24px}}.is-vertical.ColorPickerOverlay .Button.main-menu-button{width:100%;border-radius:0;justify-content:flex-start;padding-left:var(--padding-small);padding-right:var(--padding-small);margin:0 0 var(--padding-tiny) 0}.is-vertical.ColorPickerOverlay .Button.main-menu-button:first-child{margin-top:var(--padding-tiny)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .is-vertical.ColorPickerOverlay .Button.main-menu-button{width:100%;height:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .is-vertical.ColorPickerOverlay .Button.main-menu-button{width:100%;height:32px}}.is-vertical.ColorPickerOverlay .Button.main-menu-button .Icon{margin-right:10px}.is-vertical.ColorPickerOverlay .Button.main-menu-button span{white-space:nowrap}@keyframes bottom-up{0%{transform:translateY(100%)}to{transform:translateY(0)}}@keyframes up-bottom{0%{transform:translateY(0)}to{transform:translateY(100%)}}.ColorPickerOverlay{border-radius:4px;box-shadow:0 0 3px 0 var(--document-box-shadow);background:var(--component-background);display:flex;flex-direction:column;padding:16px}",""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"},e.exports=t}}]);
//# sourceMappingURL=9876.chunk.js.map