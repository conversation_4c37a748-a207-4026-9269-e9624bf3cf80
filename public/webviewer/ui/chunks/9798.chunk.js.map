{"version": 3, "file": "chunks/9798.chunk.js", "mappings": "gGAEAA,EADkC,EAAQ,MAChCC,EAA4B,IAE9BC,KAAK,CAACC,EAAOC,GAAI,ulBAAwlB,KAEjnBD,EAAOH,QAAUA,C,ioCCH6B,IAExCK,EAAoB,SAAAC,GAAA,SAAAD,IAAA,IAAAE,E,mGAAAC,CAAA,KAAAH,GAAA,QAAAI,EAAAC,UAAAC,OAAAC,EAAA,IAAAC,MAAAJ,GAAAK,EAAA,EAAAA,EAAAL,EAAAK,IAAAF,EAAAE,GAAAJ,UAAAI,GAGvB,OAHuBP,E,qYAAAQ,CAAA,KAAAV,EAAA,GAAAW,OAAAJ,I,EAAAL,E,EACH,SAACU,GACpB,OAAOC,EAAAA,EAAAA,GAAY,CAAED,MAAAA,EAAOE,UAAWZ,EAAKa,MAAMD,WACpD,G,MAHwB,yB,wFAGvBZ,CAAA,Q,qRAAAc,CAAAhB,EAAAC,G,EAAAD,E,0DAAA,CAHuB,CAASiB,EAAAA,IAcnC,MCjBA,EDSwC,SAAHC,GAAA,IAAMC,EAAQD,EAARC,SAAQ,OACjDC,OAAOC,8BACLC,EAAAA,cAACtB,EAAoB,CAACc,WAAWS,EAAAA,EAAAA,OAC9BJ,GAEDA,CAAQ,C,mBEddxB,EADkC,EAAQ,MAChCC,EAA4B,IAE9BC,KAAK,CAACC,EAAOC,GAAI,m+KAAs+K,KAE//KJ,EAAQ6B,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,QAEvB1B,EAAOH,QAAUA,C,mBCRjBA,EADkC,EAAQ,MAChCC,EAA4B,IAE9BC,KAAK,CAACC,EAAOC,GAAI,83BAA+3B,KAEx5BD,EAAOH,QAAUA,C,kBCNjB,IAAI8B,EAAM,EAAQ,OACFC,EAAU,EAAQ,OAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAAC5B,EAAOC,GAAI2B,EAAS,MAwDjCD,EAAIC,EArDH,CAEdG,OAAiB,SAAUC,GAgBX,IAAKV,OAAOC,8BAEV,YADAU,SAASC,KAAKC,YAAYH,GAI5B,IAAII,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAc5B,SACjB4B,EAzBF,SAASE,EAAwBC,EAASC,EAAOP,UAC/C,MAAMQ,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,SAAQC,GAAMH,EAAS1C,KAAK6C,KAG3DJ,EAAKE,iBAAiB,KAAKC,SAAQC,IAC7BA,EAAGC,YACLJ,EAAS1C,QAAQuC,EAAwBC,EAASK,EAAGC,YACvD,IAGKJ,CACT,CAWkBH,CAAwB,qBAG1C,MAAMQ,EAAkB,GACxB,IAAK,IAAIC,EAAI,EAAGA,EAAIX,EAAc5B,OAAQuC,IAAK,CAC7C,MAAMC,EAAeZ,EAAcW,GACnC,GAAU,IAANA,EACFC,EAAaH,WAAWV,YAAYH,GACpCA,EAASiB,OAAS,WACZH,EAAgBtC,OAAS,GAC3BsC,EAAgBH,SAASO,IAEvBA,EAAUC,UAAYnB,EAASmB,SAAS,GAG9C,MACK,CACL,MAAMD,EAAYlB,EAASoB,WAAU,GACrCJ,EAAaH,WAAWV,YAAYe,GACpCJ,EAAgB/C,KAAKmD,EACvB,CACF,CACF,EACdnB,WAAoB,IAMpB/B,EAAOH,QAAU+B,EAAQF,QAAU,CAAC,C,kBClEpC,IAAIC,EAAM,EAAQ,OACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAAC5B,EAAOC,GAAI2B,EAAS,MAwDjCD,EAAIC,EArDH,CAEdG,OAAiB,SAAUC,GAgBX,IAAKV,OAAOC,8BAEV,YADAU,SAASC,KAAKC,YAAYH,GAI5B,IAAII,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAc5B,SACjB4B,EAzBF,SAASE,EAAwBC,EAASC,EAAOP,UAC/C,MAAMQ,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,SAAQC,GAAMH,EAAS1C,KAAK6C,KAG3DJ,EAAKE,iBAAiB,KAAKC,SAAQC,IAC7BA,EAAGC,YACLJ,EAAS1C,QAAQuC,EAAwBC,EAASK,EAAGC,YACvD,IAGKJ,CACT,CAWkBH,CAAwB,qBAG1C,MAAMQ,EAAkB,GACxB,IAAK,IAAIC,EAAI,EAAGA,EAAIX,EAAc5B,OAAQuC,IAAK,CAC7C,MAAMC,EAAeZ,EAAcW,GACnC,GAAU,IAANA,EACFC,EAAaH,WAAWV,YAAYH,GACpCA,EAASiB,OAAS,WACZH,EAAgBtC,OAAS,GAC3BsC,EAAgBH,SAASO,IAEvBA,EAAUC,UAAYnB,EAASmB,SAAS,GAG9C,MACK,CACL,MAAMD,EAAYlB,EAASoB,WAAU,GACrCJ,EAAaH,WAAWV,YAAYe,GACpCJ,EAAgB/C,KAAKmD,EACvB,CACF,CACF,EACdnB,WAAoB,IAMpB/B,EAAOH,QAAU+B,EAAQF,QAAU,CAAC,C,mBChEpC7B,EADkC,EAAQ,MAChCC,EAA4B,IAE9BC,KAAK,CAACC,EAAOC,GAAI,83CAA+3C,KAEx5CJ,EAAQ6B,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,QAEvB1B,EAAOH,QAAUA,C,kBCVjB,IAAI8B,EAAM,EAAQ,OACFC,EAAU,EAAQ,OAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAAC5B,EAAOC,GAAI2B,EAAS,MAwDjCD,EAAIC,EArDH,CAEdG,OAAiB,SAAUC,GAgBX,IAAKV,OAAOC,8BAEV,YADAU,SAASC,KAAKC,YAAYH,GAI5B,IAAII,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAc5B,SACjB4B,EAzBF,SAASE,EAAwBC,EAASC,EAAOP,UAC/C,MAAMQ,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,SAAQC,GAAMH,EAAS1C,KAAK6C,KAG3DJ,EAAKE,iBAAiB,KAAKC,SAAQC,IAC7BA,EAAGC,YACLJ,EAAS1C,QAAQuC,EAAwBC,EAASK,EAAGC,YACvD,IAGKJ,CACT,CAWkBH,CAAwB,qBAG1C,MAAMQ,EAAkB,GACxB,IAAK,IAAIC,EAAI,EAAGA,EAAIX,EAAc5B,OAAQuC,IAAK,CAC7C,MAAMC,EAAeZ,EAAcW,GACnC,GAAU,IAANA,EACFC,EAAaH,WAAWV,YAAYH,GACpCA,EAASiB,OAAS,WACZH,EAAgBtC,OAAS,GAC3BsC,EAAgBH,SAASO,IAEvBA,EAAUC,UAAYnB,EAASmB,SAAS,GAG9C,MACK,CACL,MAAMD,EAAYlB,EAASoB,WAAU,GACrCJ,EAAaH,WAAWV,YAAYe,GACpCJ,EAAgB/C,KAAKmD,EACvB,CACF,CACF,EACdnB,WAAoB,IAMpB/B,EAAOH,QAAU+B,EAAQF,QAAU,CAAC,C,kBClEpC,IAAIC,EAAM,EAAQ,OACFC,EAAU,EAAQ,OAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAAC5B,EAAOC,GAAI2B,EAAS,MAwDjCD,EAAIC,EArDH,CAEdG,OAAiB,SAAUC,GAgBX,IAAKV,OAAOC,8BAEV,YADAU,SAASC,KAAKC,YAAYH,GAI5B,IAAII,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAc5B,SACjB4B,EAzBF,SAASE,EAAwBC,EAASC,EAAOP,UAC/C,MAAMQ,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,SAAQC,GAAMH,EAAS1C,KAAK6C,KAG3DJ,EAAKE,iBAAiB,KAAKC,SAAQC,IAC7BA,EAAGC,YACLJ,EAAS1C,QAAQuC,EAAwBC,EAASK,EAAGC,YACvD,IAGKJ,CACT,CAWkBH,CAAwB,qBAG1C,MAAMQ,EAAkB,GACxB,IAAK,IAAIC,EAAI,EAAGA,EAAIX,EAAc5B,OAAQuC,IAAK,CAC7C,MAAMC,EAAeZ,EAAcW,GACnC,GAAU,IAANA,EACFC,EAAaH,WAAWV,YAAYH,GACpCA,EAASiB,OAAS,WACZH,EAAgBtC,OAAS,GAC3BsC,EAAgBH,SAASO,IAEvBA,EAAUC,UAAYnB,EAASmB,SAAS,GAG9C,MACK,CACL,MAAMD,EAAYlB,EAASoB,WAAU,GACrCJ,EAAaH,WAAWV,YAAYe,GACpCJ,EAAgB/C,KAAKmD,EACvB,CACF,CACF,EACdnB,WAAoB,IAMpB/B,EAAOH,QAAU+B,EAAQF,QAAU,CAAC,C,mBChEpC7B,EADkC,EAAQ,MAChCC,EAA4B,IAE9BC,KAAK,CAACC,EAAOC,GAAI,uhDAAwhD,KAEjjDJ,EAAQ6B,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,QAEvB1B,EAAOH,QAAUA,C,uICHjB,SAASwD,IAAS,CAElB,iBAAgBC,GAAW,IAAEC,EAAoBhD,UAAAC,OAAA,QAAAgD,IAAAjD,UAAA,GAAAA,UAAA,GAAG8C,EAAMI,EAAuBlD,UAAAC,OAAA,QAAAgD,IAAAjD,UAAA,GAAAA,UAAA,GAAG,EAAC,OAAK,SAACmD,GACzF,OAAIC,EAAAA,EAAKC,4BAEAC,EAAqBP,EAAaI,EAAUD,GAE9CK,EAAeR,EAAaC,EAAsBG,EAAUD,EACrE,CAAC,EAED,IAAMI,EAAuB,SAACP,EAAaI,EAAUD,GAAuB,OAAKE,EAAAA,EAAKI,gBAAgBT,EAAaG,GAAyBO,MAAK,SAACC,GAChJ,GAAIA,GAAWA,EAAQC,IACrB,OAAOC,EAAAA,EAAAA,GAAYT,EAAU,CAC3BU,SAAU,eACVC,oBAAoB,EACpBC,YAAaL,EAAQC,MAGzBK,EAAQC,KAAK,iDACf,GAAE,EAEIV,EAAiB,SAACR,EAAaC,EAAsBG,EAAUD,GACnE,IAIMgB,EAAU,CACdC,QALcC,EAAAA,EAAQC,EAAE,kCAMxBC,MALYF,EAAAA,EAAQC,EAAE,+BAMtBE,eALqBH,EAAAA,EAAQC,EAAE,gBAM/BG,UAAW,WAMT,OALApB,EAAAA,EAAKI,gBAAgBT,EAAaG,GAC/BO,MAAK,WACJT,GACF,IAAE,OACK,SAACyB,GAAG,OAAKC,EAAAA,EAAAA,GAAUD,EAAI,IACzBE,QAAQC,SACjB,GAGF,OAAOzB,EAAS0B,EAAAA,EAAQC,mBAAmBZ,GAC7C,C,kBChDA,IAAI9C,EAAM,EAAQ,OACFC,EAAU,EAAQ,OAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAAC5B,EAAOC,GAAI2B,EAAS,MAwDjCD,EAAIC,EArDH,CAEdG,OAAiB,SAAUC,GAgBX,IAAKV,OAAOC,8BAEV,YADAU,SAASC,KAAKC,YAAYH,GAI5B,IAAII,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAc5B,SACjB4B,EAzBF,SAASE,EAAwBC,EAASC,EAAOP,UAC/C,MAAMQ,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,SAAQC,GAAMH,EAAS1C,KAAK6C,KAG3DJ,EAAKE,iBAAiB,KAAKC,SAAQC,IAC7BA,EAAGC,YACLJ,EAAS1C,QAAQuC,EAAwBC,EAASK,EAAGC,YACvD,IAGKJ,CACT,CAWkBH,CAAwB,qBAG1C,MAAMQ,EAAkB,GACxB,IAAK,IAAIC,EAAI,EAAGA,EAAIX,EAAc5B,OAAQuC,IAAK,CAC7C,MAAMC,EAAeZ,EAAcW,GACnC,GAAU,IAANA,EACFC,EAAaH,WAAWV,YAAYH,GACpCA,EAASiB,OAAS,WACZH,EAAgBtC,OAAS,GAC3BsC,EAAgBH,SAASO,IAEvBA,EAAUC,UAAYnB,EAASmB,SAAS,GAG9C,MACK,CACL,MAAMD,EAAYlB,EAASoB,WAAU,GACrCJ,EAAaH,WAAWV,YAAYe,GACpCJ,EAAgB/C,KAAKmD,EACvB,CACF,CACF,EACdnB,WAAoB,IAMpB/B,EAAOH,QAAU+B,EAAQF,QAAU,CAAC,C,mBChEpC7B,EADkC,EAAQ,MAChCC,EAA4B,IAE9BC,KAAK,CAACC,EAAOC,GAAI,s5QAAu5Q,KAEh7QJ,EAAQ6B,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,QAEvB1B,EAAOH,QAAUA,C,kBCVjB,IAAI8B,EAAM,EAAQ,OACFC,EAAU,EAAQ,OAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAAC5B,EAAOC,GAAI2B,EAAS,MAwDjCD,EAAIC,EArDH,CAEdG,OAAiB,SAAUC,GAgBX,IAAKV,OAAOC,8BAEV,YADAU,SAASC,KAAKC,YAAYH,GAI5B,IAAII,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAc5B,SACjB4B,EAzBF,SAASE,EAAwBC,EAASC,EAAOP,UAC/C,MAAMQ,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,SAAQC,GAAMH,EAAS1C,KAAK6C,KAG3DJ,EAAKE,iBAAiB,KAAKC,SAAQC,IAC7BA,EAAGC,YACLJ,EAAS1C,QAAQuC,EAAwBC,EAASK,EAAGC,YACvD,IAGKJ,CACT,CAWkBH,CAAwB,qBAG1C,MAAMQ,EAAkB,GACxB,IAAK,IAAIC,EAAI,EAAGA,EAAIX,EAAc5B,OAAQuC,IAAK,CAC7C,MAAMC,EAAeZ,EAAcW,GACnC,GAAU,IAANA,EACFC,EAAaH,WAAWV,YAAYH,GACpCA,EAASiB,OAAS,WACZH,EAAgBtC,OAAS,GAC3BsC,EAAgBH,SAASO,IAEvBA,EAAUC,UAAYnB,EAASmB,SAAS,GAG9C,MACK,CACL,MAAMD,EAAYlB,EAASoB,WAAU,GACrCJ,EAAaH,WAAWV,YAAYe,GACpCJ,EAAgB/C,KAAKmD,EACvB,CACF,CACF,EACdnB,WAAoB,IAMpB/B,EAAOH,QAAU+B,EAAQF,QAAU,CAAC,C,mBChEpC7B,EADkC,EAAQ,MAChCC,EAA4B,IAE9BC,KAAK,CAACC,EAAOC,GAAI,2XAA4X,KAErZD,EAAOH,QAAUA,C,mBCJjBA,EADkC,EAAQ,MAChCC,EAA4B,IAE9BC,KAAK,CAACC,EAAOC,GAAI,yjEAA0jE,KAEnlEJ,EAAQ6B,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,QAEvB1B,EAAOH,QAAUA,C,w7CCNjB,IAAMyF,EAAwB9D,EAAAA,gBAExB+D,EAAyB,SAAHnE,GAAqB,IAAfC,EAAQD,EAARC,SAC4CmE,EAAAC,GAAdC,EAAAA,EAAAA,UAAS,MAAK,GAArEC,EAAuBH,EAAA,GAAEI,EAA0BJ,EAAA,GACmBK,EAAAJ,GAAfC,EAAAA,EAAAA,WAAS,GAAM,GAAtEI,EAAuBD,EAAA,GAAEE,EAA0BF,EAAA,GACgBG,EAAAP,GAAZC,EAAAA,EAAAA,WAAU,GAAE,GAAnEO,EAAuBD,EAAA,GAAEE,EAA0BF,EAAA,IAE1DG,EAAAA,EAAAA,YAAU,WACR,IAAMC,EAAuB,SAAC9C,EAAa+C,GACzC,GAAe,aAAXA,EAAuB,CACzB,IAAMC,EAAuBhD,EAAYiD,QAAO,SAACC,GAAU,MAA4B,WAAvBA,EAAWC,OAAoB,IAEzFC,EAAuBJ,EAAqB9F,OAAS,EAAI8F,EAAqB,GAAGK,GAAK,KAC5Ff,EAA2Bc,EAC7B,MACEd,EAA2B,KAE/B,EAEMgB,EAA4B,SAACC,GACjC,GAAKA,EAAL,CAGA,IACMC,GADoBnD,EAAAA,EAAKoD,wBAA0B,IACJC,WAAU,SAACC,GAC9D,OAAOtD,EAAAA,EAAKuD,oBAAoBD,EAAcJ,EAChD,IACAX,EAA2BY,EAL3B,CAMF,EAKA,OAHAnD,EAAAA,EAAKwD,iBAAiB,qBAAsBf,GAC5CzC,EAAAA,EAAKwD,iBAAiB,4BAA6BP,GAE5C,WACLjD,EAAAA,EAAKyD,oBAAoB,qBAAsBhB,GAC/CzC,EAAAA,EAAKyD,oBAAoB,4BAA6BR,EACxD,CACF,GAAG,IAEH,IAAMS,EAAQ,CACZ1B,wBAAAA,EACAC,2BAAAA,EACAE,wBAAAA,EACAC,2BAAAA,EACAE,wBAAAA,GAGF,OAAOzE,EAAAA,cAAC8D,EAAsBgC,SAAQ,CAACD,MAAOA,GAAQhG,EACxD,E,ibClCA,MChBA,EDGsC,SAACJ,GACrC,I,IAAOsG,G,GAAuBC,EAAAA,EAAAA,KAC5B,SAACC,GAAK,MAAK,CACTC,EAAAA,EAAUC,uBAAuBF,GAClC,GACDG,EAAAA,I,EACD,E,o4BALyB,GAO1B,OACEpG,EAAAA,cAACqG,EAAAA,EAAeC,EAAA,GAAK7G,EAAK,CAAE8G,WAAYR,EAAqBS,SAAO,IAExE,E,sGEHA,IAAMC,EAAgB,SAAChH,GAErB,I,IAAOiH,G,GAAcV,EAAAA,EAAAA,KACnB,SAACC,GAAK,IAAAU,EAAA,MAAK,CACuB,QADvBA,EACTT,EAAAA,EAAUU,gBAAgBX,UAAM,IAAAU,OAAA,EAAhCA,EAAkCE,eACnC,I,EACF,E,o4BAJgB,GAMfC,EAUErH,EAVFqH,UACA9B,EASEvF,EATFuF,WACA+B,EAQEtH,EARFsH,OACAC,EAOEvH,EAPFuH,WACAC,EAMExH,EANFwH,SACAC,EAKEzH,EALFyH,sBACAC,EAIE1H,EAJF0H,yBACAC,EAGE3H,EAHF2H,YACAC,EAEE5H,EAFF4H,WACAC,EACE7H,EADF6H,SAEMlE,GAAMmE,EAAAA,EAAAA,KAANnE,EAEJoE,GAAOC,EAAAA,EAAAA,GAAsBzC,GAEjC,GAAIsC,EAAU,CACZ,IAAMI,EAAcF,EAAKG,eAAe,QAAS,CAAEC,SAAUN,IAC7DE,EAAO,IAAIK,KAAKH,EAClB,CAEA,IASII,EATEC,EAAgBP,EAAOQ,IAAMR,GAAMS,OAAOhB,GAAUiB,OAAOlB,GAAc5D,EAAE,wCAC3E+E,EAAgB,GAAH9I,OAAM0H,EAAM,OAAA1H,OAAM0I,GAC/BK,EAAYC,IAAW,iBAAkB,CAAE,0BAA2BhB,GAAc,CAAE,aAAcX,IAExG4B,EAGEtD,EAHFsD,MAAKC,EAGHvD,EAFFwD,KAAAA,OAAI,IAAAD,EAAG,uBAAsBA,EAC7BE,EACEzD,EADFyD,cA4BF,OAtBEX,EADEW,IAAkBC,EAAAA,GAAuB,KAEzC1I,EAAAA,cAAC2I,EAAoB,CAACC,aAAc,GACjCxB,GAGLqB,IAAkBC,EAAAA,GAA4B,WAC3CD,IAAkBC,EAAAA,GAAmC,kBACrDD,IAAkBC,EAAAA,GAAyB,QAC3CD,IAAkBC,EAAAA,GAAkC,iBACpDD,IAAkBC,EAAAA,GAA6C,2BAE/CtF,EAAEkF,GAEFtD,EAAW6D,cAU9B7I,EAAAA,cAAA,OAAK8I,KAAK,WAAWV,UAAWA,EAAWW,QAAS5B,EAA0B6B,QAPhE,SAACC,GACG,UAAdA,EAAMC,KACR/B,GAEJ,EAGkGgC,SAAU,GACxGnJ,EAAAA,cAAA,OAAKoI,UAAU,4BACbpI,EAAAA,cAACoJ,EAAAA,EAAI,CAACC,MAAOb,EAAMc,MAAOxC,KAE5B9G,EAAAA,cAAA,OAAKoI,UAAU,uBACbpI,EAAAA,cAAA,OAAKoI,UAAU,0BACZN,GAGD9C,EAAWuE,YACTvJ,EAAAA,cAAA,OAAKoI,UAAU,6BACZhF,EAAE,+BAA+B,KAAG4B,EAAWuE,aACzC,KAEbvJ,EAAAA,cAAA,OAAKoI,UAAU,8BACZD,IAGLnI,EAAAA,cAACwJ,EAAAA,EAAM,CACLpB,UAAU,wBACVqB,MAAO,CAAEC,WAAY,QACrBC,IAAK,aACLZ,QAAS7B,EACT0C,UAAS,GAAAvK,OAAKyI,EAAgB,KAAAzI,OAAI8I,EAAa,KAAA9I,OAAI+D,EAAE,iBAAgB,OAI7E,EAEA,QAAepD,EAAAA,KAAWyG,G,sGC9C1B,MCzDA,EDK+B,SAAChH,GAC9B,I,IAAQuF,EAAevF,EAAfuF,WAER6E,GAAgEC,EAAAA,EAAAA,YAAWhG,GAAnEK,EAAuB0F,EAAvB1F,wBAAyBC,EAA0ByF,EAA1BzF,2BAehC2F,G,GARG/D,EAAAA,EAAAA,KACF,SAACC,GAAK,MAAK,CACTC,EAAAA,EAAU8D,kBAAkB/D,GAC5BC,EAAAA,EAAU+D,mBAAmBhE,GAC7BC,EAAAA,EAAUgE,+BAA+BjE,GACzCC,EAAAA,EAAUiE,YAAYlE,GACvB,GACDG,EAAAA,I,EACD,E,o4BAZCY,EAAU+C,EAAA,GACV9C,EAAQ8C,EAAA,GACRK,EAA2BL,EAAA,GAC3BzC,EAAQyC,EAAA,GAWJ3C,EAAcpC,EAAWqF,cAAc,qBACvCvD,EAAY9B,EAAWsF,YAAYC,WACnCxD,EAAS5E,EAAAA,EAAKqI,iBAAiBxF,EAAmB,QAElDmC,GAA2BsD,EAAAA,EAAAA,cAAY,WAC3CL,GAA+BA,EAA4BpF,GAC3D7C,EAAAA,EAAKuI,yBACLvI,EAAAA,EAAKwI,iBAAiB3F,GACtB7C,EAAAA,EAAKyI,iBAAiB5F,GACtBZ,EAA2BY,EAAWG,GACxC,GAAG,CAACH,IAEEkC,GAAwBuD,EAAAA,EAAAA,cAAY,WACxCtI,EAAAA,EAAK0I,kBAAkB,CAAC7F,GAC1B,GAAG,CAACA,IAEJ,OACEhF,EAAAA,cAACyG,EAAa,CACZO,WAAYA,EACZC,SAAUA,EACVK,SAAUA,EACVP,OAAQA,EACR/B,WAAYA,EACZ8B,UAAWA,EACXM,YAAaA,EACbF,sBAAuBA,EACvBC,yBAA0BA,EAC1BE,WAAYlD,IAA4Ba,EAAWG,IAGzD,E,eEjDM2F,G,SAAqB,SAACrL,GAC1B,IACEsL,EAEEtL,EAFFsL,WACAC,EACEvL,EADFuL,eAGM5H,GAAMmE,EAAAA,EAAAA,KAANnE,EAQR,OACEpD,EAAAA,cAACiL,EAAAA,EAAkB,CACjB7C,UAAU,uBACV8C,OATW,WACb,MAAO,GAAP7L,OACK+D,EAAE,sBAAqB,KAAA/D,OAAI0L,EAElC,EAMII,qBAAoB,GAAA9L,OAAK+D,EAAE,sBAAqB,KAAA/D,OAAI0L,EAAU,KAAA1L,OAAI+D,EAAE,kCACpEgI,aAAc,GAEdpL,EAAAA,cAAA,OAAK8I,KAAK,OAAOV,UAAU,mBACxB4C,EAAeK,KAAI,SAACC,GAAa,OAChCtL,EAAAA,cAACyG,EAAa,CACZzB,WAAYsG,EACZpC,IAAG,GAAA7J,OAAKiM,EAAcnG,GAAE,KAAA9F,OAAI0L,IAC5B,KAKZ,GAEAD,EAAmBS,UAAY,CAC7BR,WAAYS,IAAAA,UAAoB,CAACA,IAAAA,OAAkBA,IAAAA,SACnDR,eAAgBQ,IAAAA,OAGlB,U,6UCzBA,MCnBA,EDEoC,SAAC/L,GAGnC,I,IAAQuL,EAAmBvL,EAAnBuL,eAE4DhH,G,GAAZE,EAAAA,EAAAA,UAAS,I,EAAG,E,o4BAA7DuH,EAAoBzH,EAAA,GAAE0H,EAAuB1H,EAAA,GAIpD,OAHAW,EAAAA,EAAAA,YAAU,WACR+G,GAAwBC,EAAAA,EAAAA,MAA8B,SAAEC,eAAeZ,GACzE,GAAG,CAACA,IAEFhL,EAAAA,cAAC8K,EAAkBxE,EAAA,CACjB0E,eAAgBS,GACZhM,GAGV,E,2tCE8FA,QArGuB,SAACA,GACtB,IACEqF,EAIErF,EAJFqF,qBACA+G,EAGEpM,EAHFoM,mBACAC,EAEErM,EAFFqM,8BACAC,EACEtM,EADFsM,yBAGM3I,GAAMmE,EAAAA,EAAAA,KAANnE,EACoDY,EAAAC,GAAZC,EAAAA,EAAAA,UAAS,CAAC,GAAE,GAArD8H,EAAgBhI,EAAA,GAAEiI,EAAmBjI,EAAA,GACwBK,EAAAJ,GAAZC,EAAAA,EAAAA,UAAS,IAAG,GAA7DgI,EAAoB7H,EAAA,GAAE8H,EAAuB9H,EAAA,GAG5C+H,GAAetC,EAAAA,EAAAA,YAAWhG,GAA1BsI,YAERzH,EAAAA,EAAAA,YAAU,WACR,IAAMqH,EAAmB,CAAC,EAC1BlH,EAAqB3D,SAAQ,SAAC6D,GAC5B,IAAMyD,GAAgB4D,EAAAA,EAAAA,IAA6BrH,GACnDpF,EAAwBmM,EAAyBtD,IAAkB,CACjED,KAAM,2BACNF,MAAO,gDAFDA,EAAK1I,EAAL0I,MAAOE,EAAI5I,EAAJ4I,KAIfxD,EAAWsD,MAAQA,EACnBtD,EAAWwD,KAAOA,EAClBxD,EAAWyD,cAAgBA,EAE3B,I,EAAMsC,EAAa/F,EAAWsH,gBACOtK,IAAjCgK,EAAiBjB,GACnBiB,EAAiBjB,GAAc,CAAC/F,GAEhCgH,EAAiBjB,GAAc,CAAC/F,GAAU3F,O,+CAAK2M,EAAiBjB,K,qSAEpE,IAEAkB,EAAoBD,GACpBG,EAAwBI,OAAOC,KAAKR,GACtC,GAAG,CAAClH,IAEJ,IAGQ2H,EAmBFC,EACJ1M,EAAAA,cAAA,OAAKoI,UAAU,wBACbpI,EAAAA,cAAA,WACEA,EAAAA,cAACoJ,EAAAA,EAAI,CAAChB,UAAU,aAAaiB,MAAM,+BAErCrJ,EAAAA,cAAA,OAAKoI,UAAU,OAAOhF,EAAE,uCAItBuJ,EAA2BtE,IAAW,oBAAqB,CAAEuE,SAA0C,IAAhC9H,EAAqB9F,SAC5F6N,EAA0BxE,IAAW,mBAAoB,CAAEuE,SAA0C,IAAhC9H,EAAqB9F,SAEhG,OACEgB,EAAAA,cAAAA,EAAAA,SAAA,KACEA,EAAAA,cAAA,OAAKoI,UAAU,4BACbpI,EAAAA,cAAA,YAAOoD,EAAE,oCAA0C,IAAC,IAAA/D,OAAKyF,EAAqB9F,OAAM,MAErFkN,EAAqBlN,OAAS,GApC3ByN,EAAgBL,EAAa,CAAEU,iBAAkBZ,EAAqBlN,QAAW,CAAC,EAEtFgB,EAAAA,cAAA,OAAKoI,UAAU,6BACbpI,EAAAA,cAAC+M,EAAAA,GAAQzG,EAAA,CACP0G,KAAMd,EACNe,YAAa,SAACC,EAAOnC,GACnB,OACE/K,EAAAA,cAAC8K,EAAkB,CACjB5B,IAAKgE,EACLnC,WAAYA,EACZC,eAAgBgB,EAAiBjB,IAEvC,GACI0B,MAuByDC,EACjE1M,EAAAA,cAAA,OAAKoI,UAAU,4BACbpI,EAAAA,cAACwJ,EAAAA,EAAM,CACLoD,SAA0C,IAAhC9H,EAAqB9F,OAC/BoJ,UAAWyE,EACX9D,QAAS+C,EACTxD,MAAOlF,EAAE,+BAERA,EAAE,+BAELpD,EAAAA,cAACwJ,EAAAA,EAAM,CACLoD,SAA0C,IAAhC9H,EAAqB9F,OAC/BoJ,UAAWuE,EACX5D,QAAS8C,EACTvD,MAAOlF,EAAE,mCAERA,EAAE,oCAKb,E,sVCzGA,IAAM+J,EAAuB,SAAHvN,GAA+B,IAAzBnB,EAAEmB,EAAFnB,GAAI6J,EAAK1I,EAAL0I,MAAU8E,E,6WAAIC,CAAAzN,EAAA0N,GAChD,OACEtN,EAAAA,cAACuN,EAAAA,EAA+B,KAC9BvN,EAAAA,cAAA,SAAOwN,QAAS/O,EAAI2J,UAAU,gCAAgCE,GAC9DtI,EAAAA,cAACyN,EAAAA,GAAenH,EAAA,CACdoH,SAAO,GACHN,EAAI,CACRO,QAASlP,KAIjB,EAEA0O,EAAqB5B,UAAY,CAC/B9M,GAAI+M,IAAAA,OACJlD,MAAOkD,IAAAA,QAGT2B,EAAqBS,aAAe,CAClCnP,GAAI,GACJ6J,MAAO,IAGT,MC3BA,GD2BA,E,o8CEnBA,IAAMuF,GAAkB,SAACC,EAAYC,EAAeC,GAClD,OAD2EjP,UAAAC,OAAA,QAAAgD,IAAAjD,UAAA,KAAAA,UAAA,GAIpE,cAFE+O,EAAaC,EAAgBC,CAGxC,EAuHMC,GAAkB,SAACxO,GACvB,IAAQuN,EAASvN,EAATuN,KACA5J,GAAMmE,EAAAA,EAAAA,KAANnE,EACR,OACEpD,EAAAA,cAACkO,EAAAA,EAAWC,OAAW1O,EACpBuN,EAAKxE,MAAQxI,EAAAA,cAACoJ,EAAAA,EAAI,CAACC,MAAO2D,EAAKxE,OAC/BpF,EAAE4J,EAAK1E,OAGd,EAEA2F,GAAgB1C,UAAY,CAC1ByB,KAAMxB,IAAAA,OAAiB4C,YAGzB,IAAMC,GAAkB,SAAHzO,GAAiB,IAAXoN,EAAIpN,EAAJoN,KACjB5J,GAAMmE,EAAAA,EAAAA,KAANnE,EAER,OACEpD,EAAAA,cAAA,OAAKyJ,MAAO,CAAE6E,QAAS,OAAQC,OAAQ,SACpCvB,EAAKxE,MAAQxI,EAAAA,cAACoJ,EAAAA,EAAI,CAACC,MAAO2D,EAAKxE,OAC/BpF,EAAE4J,EAAK1E,OAGd,EAEA+F,GAAgB9C,UAAY,CAC1ByB,KAAMxB,IAAAA,OAAiB4C,YAGzB,IAAMI,GAAgB,SAAHC,GAAA,IAAM5O,EAAQ4O,EAAR5O,SAAaJ,E,6WAAK4N,CAAAoB,EAAAnB,IAAA,OACzCtN,EAAAA,cAACkO,EAAAA,EAAWQ,QAAYjP,EACtBO,EAAAA,cAAA,OAAKoI,UAAU,uDACbpI,EAAAA,cAACoJ,EAAAA,EAAI,CAAChB,UAAU,4CAA4CiB,MAAM,wBAEnExJ,EACkB,EAGvB2O,GAAcjD,UAAY,CACxB1L,SAAU2L,IAAAA,MAGZ,IAAMmD,GAA6B,SAAClP,GAClC,IA1JiBqO,EA0JT1K,GAAMmE,EAAAA,EAAAA,KAANnE,EACAwL,EAAwCnP,EAAxCmP,YAAaC,EAA2BpP,EAA3BoP,uBAEfC,EAAiB,CACrB,CACExG,MAAOlF,EAAE,iCACT7C,QAASsO,IAKPE,GArKWjB,EAoKkB,SAAhBc,EApKc,CACjCI,aAAc,SAACC,GAAI,OAAAC,GAAAA,GAAA,GACdD,GAAI,IACPE,cAAe,OACfC,SAAU,OACVC,WAAY,OACZ/F,MAAOuE,GAAgBC,EAAYwB,GAAAA,GAAqB,MAAGC,GAAAA,GAAe,eAC1EC,cAAe,MACfC,YAAa,MACbC,WAAY,QAAM,EAEpBC,MAAO,SAACV,GAAI,OAAAC,GAAAA,GAAA,GACPD,GAAI,IACPW,QAAS,OAAK,EAEhBC,KAAM,SAACZ,GAAI,OAAAC,GAAAA,GAAA,GACND,GAAI,IACPW,QAAS,kBACTE,aAAc,MACdC,UAAW,UACXC,OAAQ,KAAG,EAEbC,SAAU,SAAChB,GAAI,OAAAC,GAAAA,GAAA,GACVD,GAAI,IACPW,QAAS,MACTM,gBAAiBrC,GAAgBC,EAAYwB,GAAAA,GAAqB,MAAGA,GAAAA,GAAqB,OAC1FS,UAAW,UACXD,aAAc,OAAK,EAErBK,WAAY,SAAClB,GAAI,OAAAC,GAAAA,GAAA,GACZD,GAAI,IACPiB,gBAAiBrC,GAAgBC,EAAYwB,GAAAA,GAA6B,cAAGA,GAAAA,GAAqB,OAClGM,QAAS,UACTR,SAAU,OACVU,aAAc,MACdC,UAAW,SACXK,WAAY,SACZ9G,MAAOuE,GAAgBC,EAAYwB,GAAAA,GAAqB,MAAGC,GAAAA,GAAe,gBAAc,EAE1Fc,iBAAkB,SAACpB,GAAI,OAAAC,GAAAA,GAAA,GAClBD,GAAI,IACP3F,MAAOgG,GAAAA,GAAqB,MAC5BQ,aAAc,MACdpG,WAAY,MACZkG,QAAS,MACT,UAAW,CACTM,gBAAiBZ,GAAAA,GAAqB,MACtCgB,UAAW,mBAAFjR,OAAqBiQ,GAAAA,GAAqB,OACnDhG,MAAOgG,GAAAA,GAAqB,OAE9B,IAAO,CACLf,OAAQ,OACRgC,MAAO,SACR,EAEHC,OAAQ,SAACvB,GAAI,OAAAC,GAAAA,GAAA,GACRD,GAAI,IACPX,QAAS,OACTc,SAAU,OACVQ,QAAS,YACT,UAAW,CACTM,gBAAiBrC,GAAgBC,EAAYwB,GAAAA,GAA6B,cAAGC,GAAAA,GAAe,yBAC5FjG,MAAOgG,GAAAA,GAAqB,OAE9BY,gBAAiBrC,GAAgBC,EAAYwB,GAAAA,GAA6B,cAAGA,GAAAA,GAAqB,OAClGS,UAAW,UACXK,WAAY,SACZ,eAAgB,CACdN,aAAc,cACdN,cAAe,QAChB,EAEHiB,iBAAkB,SAACxB,GAAI,OAAAC,GAAAA,GAAA,GAClBD,GAAI,IACP3F,MAAOiG,GAAAA,GAAe,eAAa,EAErCmB,eAAgB,SAACzB,GAAI,OAAAC,GAAAA,GAAA,GAChBD,GAAI,IACPW,QAAS,MACTe,WArFEC,EAAAA,EAAAA,MACK,OAEF,OAmFLb,UAAW,UAAQ,EAErBc,QAAS,SAAC5B,GAAI,OAAAC,GAAAA,GAAA,GACTD,GAAI,IACPiB,gBAAiBrC,GAAgBC,EAAYwB,GAAAA,GAAsB,OAAGA,GAAAA,GAAqB,OAC3FwB,UAAW,OACXC,YAAalD,GAAgBC,EAAYwB,GAAAA,GAAqB,MAAGA,GAAAA,GAAqB,OACtF,iBAAkB,CAChByB,YAAalD,GAAgBC,EAAYwB,GAAAA,GAAqB,MAAGA,GAAAA,GAAqB,QAGxF,UAAW,CACTyB,YAAalD,GAAgBC,EAAYwB,GAAAA,GAAqB,MAAGA,GAAAA,GAAqB,QAExFgB,UAAW,mBAAiB,EAE9BU,YAAa,SAAC/B,GAAI,OAAAC,GAAAA,GAAA,GACbD,GAAI,IACPG,SAAU,OACV9F,MAAOuE,GAAgBC,EAAYwB,GAAAA,GAAqB,MAAGA,GAAAA,GAAqB,OAChFG,YAAa,OAAK,EAEpBwB,MAAO,SAAChC,GAAI,OAAAC,GAAAA,GAAA,GACPD,GAAI,IACPG,SAAU,OACV9F,MAAOuE,GAAgBC,EAAYwB,GAAAA,GAAqB,MAAGC,GAAAA,GAAe,eAC1EE,YAAa,OAAK,IA6DpB,OACEzP,EAAAA,cAACmN,GAAoB7G,GAAA,CACnB/F,QAASuO,EACTC,OAAQA,EACRb,WAAY,CAAEC,OAAQF,GAAiBI,gBAAAA,GAAiB6C,oBAAqB,WAAF,OAAQ,IAAI,EAAExC,QAASF,IAClGwC,YAAa,GACbG,kBAAmB,SAACtL,GAAK,SAAAxG,OAAQ+D,EAAE,yBAAwB,KAAA/D,OAAIwG,EAAK,EACpEpH,GAAG,gCACH6J,MAAOlF,EAAE,8CACL3D,GAGV,EAEAkP,GAA2BpD,UAAY,CACrCqD,YAAapD,IAAAA,OAAiB4C,WAC9BS,uBAAwBrD,IAAAA,MAAgB4C,YAG1C,MChNA,GDgNA,G,gHE5MA,IAAMgD,GAAqB,SAACC,GAC1B,IAAM9Q,EAAU,CACd+Q,WAAY,GACZC,eAAe,GAGjB,OAAKF,GAILA,EAAYlQ,SAAQ,SAACqQ,GACnB,IAAQC,EAASD,EAATC,KACJA,IAAS/I,EAAAA,GAAuB,KAClCnI,EAAQ+Q,WAAW/S,KAAKiT,EAAWlJ,OAEnC/H,EAAQkR,IAAQ,EAEdD,EAAWE,QACbnR,EAAQgR,cAAgBhR,EAAQgR,gBAAkBC,EAAWE,MAAMC,WAEvE,IAEOpR,GAfEA,CAgBX,EAoDA,SAlD+B,SAACd,GAC9B,IACE8E,EAME9E,EANF8E,2BACA8M,EAKE5R,EALF4R,YACAO,EAIEnS,EAJFmS,eACAC,EAGEpS,EAHFoS,uBACAjD,EAEEnP,EAFFmP,YACAC,EACEpP,EADFoP,uBAwBF,OACE7O,EAAAA,cAAC8R,EAAAA,EAAkB,CACjB1J,UAAU,yBACV2J,YAAY,0BAEZ/R,EAAAA,cAAC2O,GAA0B,CACzBqD,QAAS,WAAF,OAAQzN,GAA2B,EAAK,EAC/CsB,MAAOwL,EACPY,eAvBe,SAACC,GACpB,I,EAAMC,EAAW,CACf7J,MAAO4J,EACPrM,MAAOqM,EACPT,KAAM/I,EAAAA,GAAuB,MAIzB0J,EAAqB,GAAH/S,O,gDADGgS,GAAe,K,ojBACO,CAAEc,IACnDP,EAAeQ,GACf,IAAM7R,EAAU6Q,GAAmBgB,GACnC7R,EAAQgR,eAAgB,EACxBM,EAAuBtR,EACzB,EAWM8R,SA9Be,SAACD,GACpBR,EAAeQ,GACf,IAAM7R,EAAU6Q,GAAmBgB,GACnCP,EAAuBtR,EACzB,EA2BMqO,YAAaA,EACbC,uBAAwBA,IAMhC,E,gIC/BA,SA3CA,SAAqByD,GACnB,OAAO,SAAqBjB,GAC1B,I,EACMpL,GAAQsM,EADOD,EAAbC,YAEFC,EAA0BtM,EAAAA,EAAUuM,2BAA2BxM,GAE/DyM,EAAmBnG,OAAOC,KAAKgG,GAAyBG,QAAO,SAACtH,EAAKnC,GACzE,IAAA0J,EAAwBJ,EAAwBtJ,GAAxCwI,EAAKkB,EAALlB,MAER,OADArG,EADmBuH,EAAJnB,MACHC,EACLrG,CACT,GAAG,CAAC,GAGE9K,EAAU,CACdmR,OAAO,EACPH,cAAeF,EAAYE,eAIvBsB,E,gDADiBxB,EAAfC,a,ojBAIR/E,OAAOC,KAAK6E,GAAalQ,SAAQ,SAAC2R,GAChC,IAAMC,EAAcL,EAAiBI,GACjCC,GACFF,EAAYtU,KAAKwU,EAAYC,OAEjC,IAEA,IAAMC,EAAeJ,EAAYK,KAAK,KAIjB,KAAjBD,GAKmBE,EAAAA,GAAAA,IACvBC,CAAeH,EAAc1S,GAL3B4B,EAAAA,EAAKkR,oBAMT,CACF,E,y5CCZA,MChCA,GDYwC,SAAC5T,GACvC,IAAQ8E,GAA+BuF,EAAAA,EAAAA,YAAWhG,GAA1CS,2BACF+N,GAAQgB,EAAAA,EAAAA,MACR1E,GAAc5I,EAAAA,EAAAA,KAAY,SAACC,GAAK,OAAKC,EAAAA,EAAUqN,eAAetN,EAAM,IACpEuM,GAA0BxM,EAAAA,EAAAA,KAAY,SAACC,GAAK,OAAKC,EAAAA,EAAUuM,2BAA2BxM,EAAM,GAAEG,EAAAA,IAC9FyI,EAAyBtC,OAAOiH,OAAOhB,GAAyBnH,KAAI,SAACoI,GAAO,OAAAvE,GAAAA,GAAA,GAC7EuE,GAAO,IACV5N,MAAO4N,EAAQhC,MAAI,IAGrB,OACEzR,EAAAA,cAAC0T,GAAsBpN,GAAA,CACrB/B,2BAA4BA,EAC5BsN,uBAAwB,WAAa,OAlB3C,SAAgCtR,EAAS+R,GACnBqB,GAAmBrB,EACvCsB,CAAYrT,EACd,CAegDsR,CAAV9S,UAAAC,OAAA,QAAAgD,IAAAjD,UAAA,GAAAA,UAAA,GAAG,CAAC,EAAsCuT,EAAM,EAChF1D,YAAaA,EACbC,uBAAwBA,GACpBpP,GAEV,E,kCENMoU,I,SAAwB,SAACpU,GAC7B,IACEqU,EAMErU,EANFqU,UACAzB,EAKE5S,EALF4S,SACA0B,EAIEtU,EAJFsU,cACAC,EAGEvU,EAHFuU,SACAxL,EAEE/I,EAFF+I,KACAyL,EACExU,EADFwU,WAGIC,EA3B6B,SAACzU,GACpC,IAAQwU,EAA8DxU,EAA9DwU,WAAYE,EAAkD1U,EAAlD0U,eAAgBC,EAAkC3U,EAAlC2U,aAAcC,EAAoB5U,EAApB4U,UAClD,GADsE5U,EAATgS,OAChD/I,EAAAA,GAAuB,KAAG,CACrC,IAAM4L,EAA6B,KAAfL,EAAoBI,EAAYJ,EAAWM,MAAMJ,EAAgBC,GAC/EI,EAAwBP,EAAWM,MAAM,EAAGJ,GAC5CM,EAAuBR,EAAWM,MAAMH,GAC9C,OACEpU,EAAAA,cAAAA,EAAAA,SAAA,KACGwU,EACDxU,EAAAA,cAAA,QAAMoI,UAAU,gBAAgBkM,GAC/BG,EAGP,CACA,OAAOJ,CACT,CAYwBK,CAA6BjV,GAC7CkV,EAAwBtM,IAAW,0BAA2B,CAAEuM,OAAQZ,IAE9E,OACEhU,EAAAA,cAAA,OAAKoI,UAAWuM,EAAuB7L,KAAK,WAAWC,QAASgL,GAC9D/T,EAAAA,cAAA,OAAKyJ,MAAO,CAAEoL,aAAc,SAC1B7U,EAAAA,cAAC8U,GAAAA,EAAM,CACL,gBAAAzV,OAAe4U,GACfc,QAASjB,EACTzB,SAAUA,KAGdrS,EAAAA,cAAA,OAAKyJ,MAAO,CAAEoL,aAAc,SAC1B7U,EAAAA,cAACoJ,EAAAA,EAAI,CAACC,MAAOb,KAEfxI,EAAAA,cAAA,OAAKoI,UAAU,gCACZ8L,GAIT,GAEAL,GAAsBtI,UAAY,CAChCuI,UAAWtI,IAAAA,KACX6G,SAAU7G,IAAAA,KACVuI,cAAevI,IAAAA,KACfwI,SAAUxI,IAAAA,KACVhD,KAAMgD,IAAAA,OACNyI,WAAYzI,IAAAA,QAGd,SAAexL,EAAAA,KAAW6T,ICjE1B,GCGuC,SAACpU,GACtC,IACEgG,EAGEhG,EAHFgG,aACAsP,EAEEtV,EAFFsV,QACAC,EACEvV,EADFuV,YAGMvQ,GAA4BqF,EAAAA,EAAAA,YAAWhG,GAAvCW,wBAEAwP,EAA2ExO,EAA3EwO,WAAYE,EAA+D1O,EAA/D0O,eAAgBC,EAA+C3O,EAA/C2O,aAAcC,EAAiC5O,EAAjC4O,UAAW7L,EAAsB/C,EAAtB+C,KAAM0E,EAAgBzH,EAAhByH,MAAOuE,EAAShM,EAATgM,KAEpEY,GAAW5H,EAAAA,EAAAA,cAAY,SAACxB,GAC5B+L,EAAY/L,EAAOiE,EACrB,GAAG,CAACA,EAAO8H,IAELjB,GAAgBtJ,EAAAA,EAAAA,cAAY,WAChCtI,EAAAA,EAAK8S,sBAAsBxP,EAC7B,GAAG,CAACA,IAEJ,OACEzF,EAAAA,cAAC6T,GAAqB,CACpBI,WAAYA,EACZE,eAAgBA,EAChBC,aAAcA,EACdC,UAAWA,EACX7L,KAAMA,EACNiJ,KAAMA,EACNqC,UAAWiB,EACX1C,SAAUA,EACV0B,cAAeA,EACfC,SAAUvP,IAA4ByI,GAG5C,E,8yCCyCA,MC7EA,GDKmC,SAACzN,GAClC,I,IACEsL,EAIEtL,EAJFsL,WACAmK,EAGEzV,EAHFyV,cACAC,EAEE1V,EAFF0V,4BACAC,EACE3V,EADF2V,+BAGMhS,GAAMmE,EAAAA,EAAAA,KAANnE,EACFiS,EAAqBH,EAAc7J,KAAI,SAACiK,GAAM,OAAKA,EAAOpI,KAAK,IACRlJ,G,GAAfE,EAAAA,EAAAA,WAAS,G,EAAM,E,s4BAAtDqR,EAAevR,EAAA,GAAEwR,EAAkBxR,EAAA,IAE1CW,EAAAA,EAAAA,YAAU,WACR,IAAM8Q,EAAqBJ,EAAmB1C,QAAO,SAAC+C,EAAaC,GACjE,OAAOR,EAA4BQ,IAAiBD,CACtD,IAAG,GAEHF,EAAmBC,EACrB,GAAG,CAACN,EAA6BE,IAEjC,IAAMO,GAAkBnL,EAAAA,EAAAA,cAAY,SAACxB,GACnC,IAAM8L,EAAU9L,EAAM4M,OAAOd,QAC7BM,EAAmBlU,SAAQ,SAAC2U,GAC1BX,EAA4BW,GAAef,CAC7C,IACAS,EAAmBT,GACnBK,EAA8BlG,GAAC,CAAC,EAAIiG,GACtC,GAAG,CAACA,EAA6BE,IAE3BL,GAAcvK,EAAAA,EAAAA,cAAY,SAACxB,EAAOiE,GACtC,IAAM6H,EAAU9L,EAAM4M,OAAOd,QAC7BI,EAA4BjI,GAAS6H,EACrCK,EAA8BlG,GAAC,CAAC,EAAIiG,GACtC,GAAG,CAACA,IAYJ,OACEnV,EAAAA,cAAA,OAAKoI,UAAU,wCACbpI,EAAAA,cAAC8U,GAAAA,EAAM,CACL1M,UAAU,gDACV,gBAAA/I,OAAe+D,EAAE,sBAAqB,KAAA/D,OAAI0L,GAC1CgK,QAASQ,EACTlD,SAAU,SAACpJ,GACTA,EAAM8M,kBACNH,EAAgB3M,EAClB,IAEFjJ,EAAAA,cAACiL,EAAAA,EAAkB,CAACC,OArBT,WACb,MAAO,GAAP7L,OACK+D,EAAE,sBAAqB,KAAA/D,OAAI0L,EAElC,EAiBwCtB,MAf1B,CACZ8G,MAAO,QAc6CpF,qBAAoB,GAAA9L,OAAK+D,EAAE,sBAAqB,KAAA/D,OAAI0L,IACpG/K,EAAAA,cAAA,OAAK8I,KAAK,QACPoM,EAAc7J,KAAI,SAAC5F,EAAcyH,GAAK,OACrClN,EAAAA,cAAC6T,GAAqB,CACpBkB,QAASI,EAA4B1P,EAAayH,OAClD8H,YAAaA,EACbvP,aAAcA,EACdyD,IAAG,GAAA7J,OAAK6N,EAAK,KAAA7N,OAAI0L,IACjB,MAMd,E,yBE7EA,SACwB,uBADxB,GAEsB,qBAFtB,GAGe,c,ouCC+Mf,SAvMA,SAAgCtL,GAC9B,IACEuW,EAMEvW,EANFuW,uBACAC,EAKExW,EALFwW,aACAC,EAIEzW,EAJFyW,eACAC,EAGE1W,EAHF0W,6BACAC,EAEE3W,EAFF2W,gCACAC,EACE5W,EADF4W,sBAGMjT,GAAMmE,EAAAA,EAAAA,KAANnE,EAC4EY,EAAAC,IAAZC,EAAAA,EAAAA,UAAS,CAAC,GAAE,GAA7EoS,EAA4BtS,EAAA,GAAEuS,EAA+BvS,EAAA,GACoBK,EAAAJ,IAAZC,EAAAA,EAAAA,UAAS,CAAC,GAAE,GAAjFsS,EAA8BnS,EAAA,GAAEoS,EAAiCpS,EAAA,GACdG,EAAAP,IAAZC,EAAAA,EAAAA,UAAS,IAAG,GAAnDwS,EAAelS,EAAA,GAAEmS,EAAkBnS,EAAA,GAGlC4H,GAAetC,EAAAA,EAAAA,YAAWhG,GAA1BsI,YAGRzH,EAAAA,EAAAA,YAAU,WACR,IAAM2R,EAA+B,CAAC,EACtCN,EAAuB7U,SAAQ,SAACmU,EAAQpI,GACtC,I,EAAMnC,EAAauK,EAAOsB,QAC1BtB,EAAOpI,MAAQA,OACkClL,IAA7CsU,EAA6BvL,GAC/BuL,EAA6BvL,GAAc,CAACuK,GAE5CgB,EAA6BvL,GAAc,GAAH1L,O,gDAAOiX,EAA6BvL,K,sSAAW,CAAEuK,GAE7F,IAEAiB,EAAgCD,GAEhC,IAAMO,EAAqB,CAAC,EAC5Bb,EAAuB7U,SAAQ,SAAC0E,EAAOqH,GACrC2J,EAAmB3J,IAAS,CAC9B,IACAuJ,EAAkCI,EACpC,GAAG,CAACb,KAGJrR,EAAAA,EAAAA,YAAU,WACR,IAAM+R,EAAkBV,EAAuBjR,QAAO,SAAC+R,EAAuB5J,GAC5E,OAAOsJ,EAA+BtJ,EACxC,IAEAyJ,EAAmBD,EACrB,GAAG,CAACF,IAGJ,IA6BMO,EACJ/W,EAAAA,cAAA,OAAK,aAAYoD,EAAE,sBACjBpD,EAAAA,cAAA,KAAG,YAAU,SAASoI,UAAU,aAAahF,EAAE,uBAwC7C4T,EAAgD,IAAlChB,EAAuBhX,OAErCiY,EAAwB5O,IAAW,qCAAsC,CAAE6O,UAAWF,IACtFG,EAAuB9O,IAAW,sBAAuB,CAAEuE,SAAqC,IAA3B8J,EAAgB1X,SACrFoY,EAAiC/O,IAAW,oBAAqB,CAAEuE,SAAqC,IAA3B8J,EAAgB1X,SAC7FqY,EAAmCpB,IAAiBqB,KAAgCnB,GAAiCF,IAAiBqB,GAE5I,OACEtX,EAAAA,cAAAA,EAAAA,SAAA,KACEA,EAAAA,cAAA,OAAKoI,UAAU,qCACZ6N,IAAiBqB,IAChBtX,EAAAA,cAAA,OAAKyJ,MAAO,CAAE8N,SAAU,IACtBvX,EAAAA,cAACwX,GAAAA,EAAO,CAACjJ,OAAO,OAAOgC,MAAM,UAEhC8G,GACCrX,EAAAA,cAAAA,EAAAA,SAAA,KACEA,EAAAA,cAAA,OAAKoI,UAAU,oCACbpI,EAAAA,cAAA,KAAG,YAAU,YAAYoI,UAAU,aACjCpI,EAAAA,cAAA,YAAOoD,EAAE,iCAAuC,KAAG4S,EAAuBhX,OAAO,MAGrFgB,EAAAA,cAACwJ,EAAAA,EAAM,CACLpB,UAAWC,IAAW,CACpB,SAAYqO,EAAgB1X,OAAS,IAEvC+J,QAlDa,WACvB,IAAM0O,EAAuB,CAAC,EAC9BzB,EAAuB7U,SAAQ,SAAC0E,EAAOqH,GACrCuK,EAAqBvK,IAAS,CAChC,IACAuJ,EAAkCgB,EACpC,EA6CY7K,SAAUoK,EACV1O,MAAOlF,EAAE,qBAERA,EAAE,qBAELpD,EAAAA,cAACwJ,EAAAA,EAAM,CACLpB,UAAWC,IAAW,CACpB,SAAYqO,EAAgB1X,OAAS,IAEvC4N,SAAUoK,EACVjO,QArDe,WACzB,IAAM0O,EAAuB,CAAC,EAC9BzB,EAAuB7U,SAAQ,SAAC0E,EAAOqH,GACrCuK,EAAqBvK,IAAS,CAChC,IACAuJ,EAAkCgB,EACpC,EAgDYnP,MAAOlF,EAAE,oBAERA,EAAE,sBAIXpD,EAAAA,cAAA,OAAKoI,UAAW6O,EAAuBnO,KAAK,QACzCmN,IAAiBqB,IA3FtBtX,EAAAA,cAAA,OAAK,aAAYoD,EAAE,gCAChBA,EAAE,gCA2FC6S,IAAiBqB,IAAsCN,GAAeb,GAhF5EnW,EAAAA,cAAA,WACEA,EAAAA,cAACwX,GAAAA,EAAO,CAACjJ,OAAO,OAAOgC,MAAM,UAgF1B0F,IAAiBqB,IAA+BN,IAAgBb,GAAgCY,GAC/Fd,IAAiBqB,IAAsCrB,IAAiBqB,KAtHpD,WAC1B,IAAMI,EAAyBnL,OAAOC,KAAK8J,GAC3C,GAAIoB,EAAuB1Y,OAAS,EAAG,CAErC,IAAMyN,EAAgBL,EAAa,CAAEU,iBAAkB4K,EAAuB1Y,QAAW,CAAC,EAC1F,OACEgB,EAAAA,cAAC+M,EAAAA,GAAQzG,GAAA,CACP0G,KAAM0K,EACNzK,YAAa,SAACC,EAAOnC,GACnB,OACE/K,EAAAA,cAAC2X,GAA0B,CACzBzO,IAAKgE,EACLnC,WAAYA,EACZmK,cAAeoB,EAA6BvL,GAC5CoK,4BAA6BqB,EAC7BpB,+BAAgCqB,GAEtC,GACIhK,GAEV,CACF,CAiGgHmL,IAE5G5X,EAAAA,cAAA,OAAKoI,UAAU,mCACbpI,EAAAA,cAACwJ,EAAAA,EAAM,CACLT,QAjFgB,WACtBwN,EAAgC,CAAC,GACjCL,GACF,EA+EQ5N,MAAOlF,EAAE,iBACTgF,UAAU,UAEThF,EAAE,kBAELpD,EAAAA,cAACwJ,EAAAA,EAAM,CACLoD,SAAqC,IAA3B8J,EAAgB1X,OAC1BsJ,MAAOlF,EAAE,qBACTgF,UAAW+O,EACXpO,QAjEwB,WAC9BsN,EAAsBK,EACxB,GAiEStT,EAAE,sBAELpD,EAAAA,cAACwJ,EAAAA,EAAM,CACLoD,SAAqC,IAA3B8J,EAAgB1X,OAC1BsJ,MAAOlF,EAAE,kBACTgF,UAAWgP,EACXrO,QA9EsB,WAC5BqN,EAAgCM,GAChCR,GACF,GA6ES9S,EAAE,oBAKb,E,kUCzMA,IAAQyU,GAAc/X,OAAOgY,KAAKC,MAA1BF,UAEKG,GAAyB,CACpCzO,YAAa,GACbe,YAAa,IAAIxK,OAAOgY,KAAKG,YAAYC,MAAM,IAAK,EAAG,GACvDC,UAAW,IAAIrY,OAAOgY,KAAKG,YAAYC,MAAM,IAAK,EAAG,EAAG,GACxDE,KAAM,aAGD,SAASC,GAA2BnD,GAA0D,IAA3CoD,EAAgBvZ,UAAAC,OAAA,QAAAgD,IAAAjD,UAAA,GAAAA,UAAA,GAAGiZ,GAEzE1N,EAMEgO,EANFhO,YACAf,EAKE+O,EALF/O,YACAgP,EAIED,EAJFC,UAASC,EAIPF,EAHFF,KAAAA,OAAI,IAAAI,EAAG,YAAWA,EAClBL,EAEEG,EAFFH,UACAM,EACEH,EADFG,SAyBF,OAvB6BvD,EAAc7J,KAAI,SAACiK,GAC9C,IAAMoD,EAAY,IAAI5Y,OAAOgY,KAAKG,YAAYU,oBAmB9C,OAlBAD,EAAUpM,WAAagJ,EAAOsD,SAC9BF,EAAUG,MAAQvD,EAAOwD,MAAMzN,KAAI,SAAC0N,GAAI,OAAKA,EAAKC,WAAW,IAC7DN,EAAUpO,YAAcA,EACxBoO,EAAUnP,YAAcA,EACxBmP,EAAUH,UAAYA,EACtBG,EAAUN,KAAOA,EACjBM,EAAUD,SAAWA,EACrBC,EAAUP,UAAYA,EACtBO,EAAUO,YAAY3D,EAAO4D,YAC7BR,EAAUjH,KAAO6D,EAAO7D,KACxBiH,EAAUS,OAAShX,EAAAA,EAAKiX,iBAEJ,SAAhB9D,EAAO7D,MACTiH,EAAUW,cAAc,oBAAqB/D,EAAO4D,YAGtDR,EAAUW,cAAc,qBAAsB/D,EAAO7D,MAE9CiH,CACT,GAGF,CAkCA,MCjFA,GDiDA,SAAyCjZ,GACvC,I,IAAQyW,EAAmBzW,EAAnByW,eACFhU,GAAWoX,EAAAA,EAAAA,MAMCvP,G,GAJyB/D,EAAAA,EAAAA,KACzC,SAACC,GAAK,MAAK,CACTC,EAAAA,EAAUqT,oBAAoBtT,GAC9BC,EAAAA,EAAUsT,kBAAkBvT,GAC7B,GAAEG,EAAAA,I,EAAa,E,s4BAJXkS,EAAgBvO,EAAA,GAAE0P,EAAc1P,EAAA,GAWjCqM,GAAkC3L,EAAAA,EAAAA,cAAY,SAACyK,GACnD,IAAMwE,EAAOvX,EAAAA,EAAKwX,QAAQ9B,GAAU+B,WAC9BC,EAA4BH,GAAQA,EAAKI,SAAYJ,EAAKI,SAAW9B,GAErElT,EAAuBuT,GAA2BnD,EADhCuE,EAAeM,SAAS,aAAezB,EAAmBuB,GAExD1X,EAAAA,EAAK6X,uBACbC,eAAenV,EACnC,GAAG,CAACwT,EAAkBmB,IAEtB,OACEzZ,EAAAA,cAACka,GAAsB5T,GAAA,CACrB8P,gCAAiCA,EACjCC,sBAjB0B,SAACnB,GAC7B,IAAMpQ,EAAuBuT,GAA2BnD,EAAe8C,IACvE9V,GAASK,EAAAA,EAAAA,GAAgBuC,EAAsBoR,GACjD,GAeQzW,GAEV,E,uGEjBA,SAvD6B,SAACA,GAC5B,I,IAAMyC,GAAWoX,EAAAA,EAAAA,MACiCtV,G,GAAZE,EAAAA,EAAAA,UAAS,I,EAAG,E,s4BAA3CmN,EAAWrN,EAAA,GAAE4N,EAAc5N,EAAA,GAClC6F,GAAgEC,EAAAA,EAAAA,YAAWhG,GAAnEQ,EAAuBuF,EAAvBvF,wBAAyBC,EAA0BsF,EAA1BtF,2BAQ/ByR,EAIEvW,EAJFuW,uBACAG,EAGE1W,EAHF0W,6BACAgE,EAEE1a,EAFF0a,4BACAlE,EACExW,EADFwW,aAGImE,GAAWxJ,EAAAA,EAAAA,MAMjB,OACE5Q,EAAAA,cAAAA,EAAAA,SAAA,KACGoa,GACCpa,EAAAA,cAAA,OACEoI,UAAU,mBAEVpI,EAAAA,cAAA,UACEoI,UAAU,uBACVW,QAZiB,WACzB7G,EAAS0B,EAAAA,EAAQyW,aAAa,kBAChC,GAYUra,EAAAA,cAACoJ,EAAAA,EAAI,CACHC,MAAM,sBACNjB,UAAU,iBAIlBpI,EAAAA,cAAC0T,GAAsB,CACrBrC,YAAaA,EACbO,eAAgBA,IAEjBtN,GACCtE,EAAAA,cAACka,GAAsB,CACrBlE,uBAAwBA,EACxBE,eA1Ce,WACrBtE,EAAe,IACfuI,IACA5V,GAA2B,EAC7B,EAuCQ0R,aAAcA,EACdE,6BAA8BA,IAKxC,E,6gCCgCA,MC5FA,GCEqC,WACnC,IAAAmE,EFEF,WACE,IAAsFtW,EAAAC,IAA9CC,EAAAA,EAAAA,UAASoT,IAAqC,GAA/ErB,EAAYjS,EAAA,GAAEuW,EAAevW,EAAA,GACoCK,EAAAJ,IAAZC,EAAAA,EAAAA,UAAS,IAAG,GAAjE8R,EAAsB3R,EAAA,GAAEmW,EAAyBnW,EAAA,GAC+BG,EAAAP,IAAfC,EAAAA,EAAAA,WAAS,GAAM,GAAhFiS,EAA4B3R,EAAA,GAAEiW,EAA+BjW,EAAA,GAC9DgO,GAA0BxM,EAAAA,EAAAA,KAAY,SAACC,GAAK,OAAKC,EAAAA,EAAUuM,2BAA2BxM,EAAM,GAAEG,EAAAA,IAE9FsU,GAAiBC,EAAAA,EAAAA,UAAQ,WAC7B,OAAOpO,OAAOC,KAAKgG,GAAyBG,QAAO,SAACtH,EAAKnC,GACvD,IAAA0J,EAA8BJ,EAAwBtJ,GAA9CwI,EAAKkB,EAALlB,MAAOD,EAAImB,EAAJnB,KAAMjJ,EAAIoK,EAAJpK,KAKrB,OAJA6C,EAAIoG,GAAQ,CACVC,MAAAA,EACAlJ,KAAAA,GAEK6C,CACT,GAAG,CAAC,EACN,GAAG,CAACmH,IAEEoI,GAAkBnQ,EAAAA,EAAAA,cAAY,SAAC6K,GAEnC,IAAQjB,EAAciB,EAAdjB,UAGFwG,EAFoBtO,OAAOC,KAAKkO,GAEDI,MAAK,SAAC5R,GAEzC,OADkBwR,EAAexR,GAAzBwI,MACKqJ,KAAK1G,EACpB,IAGAiB,EAAO7D,UAAsBzP,IAAf6Y,EAA2BnS,EAAAA,GAAuB,KAAImS,EAEpE,IAA2EG,GAAjCN,EAAepF,EAAO7D,OAAS,CAAC,GAAlEjJ,KAAAA,OAAI,IAAAwS,EAAG,uBAAsBA,EAErC,OADA1F,EAAO9M,KAAOA,EACP8M,CACT,GAAG,CAACoF,IAEEP,GAA8B1P,EAAAA,EAAAA,cAAY,WAC9C+P,EAA0B,IAC1BrY,EAAAA,EAAKkR,qBACLoH,GAAgC,EAClC,IAwCA,OAtCA9V,EAAAA,EAAAA,YAAU,WACR,SAASsW,EAAuBxY,GAC9B,IAAMyY,EAAgBzY,EAAQ4I,IAAIuP,GAClCH,GAAgC,GAChCD,EAA0BU,EAC5B,CAGA,OADA/Y,EAAAA,EAAKwD,iBAAiB,uBAAwBsV,GACvC,WACL9Y,EAAAA,EAAKyD,oBAAoB,uBAAwBqV,EACnD,CACF,GAAG,CAAChF,KAEJtR,EAAAA,EAAAA,YAAU,WACR,SAASwW,EAA6BC,GAChCA,QAEFb,EAAgBjD,IACP8D,EACTb,EAAgBjD,KAEhBiD,EAAgBjD,IAIhB+D,YAAW,WACTZ,GAAgC,EAClC,GAAG,KAEP,CAIA,OAFAtY,EAAAA,EAAKwD,iBAAiB,mBAAoBwV,GAEnC,WACLhZ,EAAAA,EAAKyD,oBAAoB,mBAAoBuV,EAC/C,CACF,GAAG,IAEI,CACLnF,uBAAAA,EACAG,6BAAAA,EACAgE,4BAAAA,EACAlE,aAAAA,EAEJ,CElFMqF,GAJFtF,EAAsBsE,EAAtBtE,uBACAG,EAA4BmE,EAA5BnE,6BACAgE,EAA2BG,EAA3BH,4BACAlE,EAAYqE,EAAZrE,aAGF,OACEjW,EAAAA,cAACub,GAAoB,CACnBvF,uBAAwBA,EACxBG,6BAA8BA,EAC9BgE,4BAA6BA,EAC7BlE,aAAcA,GAGpB,E,ksECLO,IAAMuF,GAA0B,SAAC/b,GACtC,IAiBCsK,EAAA9F,IAVG+B,EAAAA,EAAAA,KACF,SAACC,GAAK,MAAK,CACTC,EAAAA,EAAUuV,cAAcxV,EAAO,kBAC/BC,EAAAA,EAAUwV,kBAAkBzV,EAAO,kBACnCC,EAAAA,EAAUC,uBAAuBF,GACjCC,EAAAA,EAAUyV,oBAAoB1V,GAC9BC,EAAAA,EAAU0V,gCAAgC3V,GAC1CC,EAAAA,EAAUuM,2BAA2BxM,GACtC,GACDG,EAAAA,IACD,GAhBCyV,EAAM9R,EAAA,GACN+R,EAAU/R,EAAA,GACVhE,EAAmBgE,EAAA,GACnB4R,EAAmB5R,EAAA,GACnBgS,EAA4BhS,EAAA,GAC5ByI,EAAuBzI,EAAA,GAanBqQ,GAAWxJ,EAAAA,EAAAA,MAEToL,EAAyDvc,EAAzDuc,yBAA0BC,EAA+Bxc,EAA/Bwc,cAAelK,EAAgBtS,EAAhBsS,YAE3ChG,GAA2B4O,EAAAA,EAAAA,UAAQ,WAUvC,OAAAzL,GAAAA,GAAA,GAT6B3C,OAAOC,KAAKgG,GAAyBG,QAAO,SAACtH,EAAKnC,GAC7E,IAAA0J,EAA8BJ,EAAwBtJ,GAA9CZ,EAAKsK,EAALtK,MAAOmJ,EAAImB,EAAJnB,KAAMjJ,EAAIoK,EAAJpK,KAKrB,OAJA6C,EAAIoG,GAAQ,CACVnJ,MAAAA,EACAE,KAAAA,GAEK6C,CACT,GAAG,CAAC,IAEiC6Q,EAAAA,GACvC,GAAG,CAAC1J,IAMEtQ,GAAWoX,EAAAA,EAAAA,MAaX6C,EAAsB,WAC1B,IAAMC,EAAkBH,EAAgBlK,EAAc,iBACtD7P,EAAS0B,EAAAA,EAAQyW,aAAa+B,GAChC,EAcM3S,EAAQwS,IAAmBN,GAAuBvB,EACpD,CAAC,EACD,CAAE7J,MAAO,GAAFlR,OAAK0G,EAAmB,MAAMsW,SAAU,GAAFhd,OAAK0G,EAAmB,OAEjEzB,GAA4BwF,EAAAA,EAAAA,YAAWhG,GAAvCQ,wBAE2CN,EAAAC,IAAfC,EAAAA,EAAAA,WAAS,GAAM,GAA5CoY,EAAUtY,EAAA,GAAEuY,EAAavY,EAAA,GAWhC,IATAW,EAAAA,EAAAA,YAAU,WACR,IAAM6X,EAAUnB,YAAW,WACzBkB,GAAeV,EACjB,GAAG,KACH,OAAO,WACLY,aAAaD,EACf,CACF,GAAG,CAACX,IAEAC,IAAgBD,GAAUS,IAAeL,EAC3C,OAAO,KAGT,IAAMS,EAAmBT,EAAgBlK,EAAc,iBAEvD,OACE/R,EAAAA,cAAC8R,EAAAA,EAAkB,CAACC,YAAa2K,EAAkBtU,UAAU,uBAAuBqB,MAAOA,IACvFkS,GAAuBvB,IAnCxB6B,GACCjc,EAAAA,cAAA,OAAKoI,UAAU,mBACbpI,EAAAA,cAAA,OAAKoI,UAAU,uBAAuBW,QAASoT,GAC7Cnc,EAAAA,cAACoJ,EAAAA,EAAI,CAACC,MAAM,sBAAsBjB,UAAU,iBAiClDpI,EAAAA,cAACub,GAAoB,OACnBjX,GACAtE,EAAAA,cAAC2c,EAAc,CACb7X,qBAAsBkX,EACtBjQ,yBAA0BA,EAC1BF,mBA5DmB,WACzB,IAAM+Q,EAA0B,WAC9B,IAAMC,EAA2BZ,EAAgBE,EAAsB,WAAO,EAC9Eja,GAASK,EAAAA,EAAAA,GAAgByZ,EAA0Ba,GACrD,EACId,EACFA,EAA6BC,EAA0BY,GAEvDA,GAEJ,EAmDQ9Q,8BAlE8B,WACpC3J,EAAAA,EAAK0I,kBAAkBmR,EACzB,IAqEF,EAEAR,GAAwBjQ,UAAY,CAClCyQ,yBAA0BxQ,IAAAA,MAC1ByQ,cAAezQ,IAAAA,KACfuG,YAAavG,IAAAA,QAGfgQ,GAAwB5N,aAAe,CACrCqO,eAAe,EACflK,YAAa,IAWf,MC9IA,GDsI4C,SAACtS,GAC3C,OACEO,EAAAA,cAAC+D,EAAsB,KACrB/D,EAAAA,cAACwb,GAA4B/b,GAGnC,C,mBE5IApB,EADkC,EAAQ,MAChCC,EAA4B,IAE9BC,KAAK,CAACC,EAAOC,GAAI,yGAA0G,KAEnID,EAAOH,QAAUA,C,kBCNjB,IAAI8B,EAAM,EAAQ,OACFC,EAAU,EAAQ,OAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAAC5B,EAAOC,GAAI2B,EAAS,MAwDjCD,EAAIC,EArDH,CAEdG,OAAiB,SAAUC,GAgBX,IAAKV,OAAOC,8BAEV,YADAU,SAASC,KAAKC,YAAYH,GAI5B,IAAII,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAc5B,SACjB4B,EAzBF,SAASE,EAAwBC,EAASC,EAAOP,UAC/C,MAAMQ,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,SAAQC,GAAMH,EAAS1C,KAAK6C,KAG3DJ,EAAKE,iBAAiB,KAAKC,SAAQC,IAC7BA,EAAGC,YACLJ,EAAS1C,QAAQuC,EAAwBC,EAASK,EAAGC,YACvD,IAGKJ,CACT,CAWkBH,CAAwB,qBAG1C,MAAMQ,EAAkB,GACxB,IAAK,IAAIC,EAAI,EAAGA,EAAIX,EAAc5B,OAAQuC,IAAK,CAC7C,MAAMC,EAAeZ,EAAcW,GACnC,GAAU,IAANA,EACFC,EAAaH,WAAWV,YAAYH,GACpCA,EAASiB,OAAS,WACZH,EAAgBtC,OAAS,GAC3BsC,EAAgBH,SAASO,IAEvBA,EAAUC,UAAYnB,EAASmB,SAAS,GAG9C,MACK,CACL,MAAMD,EAAYlB,EAASoB,WAAU,GACrCJ,EAAaH,WAAWV,YAAYe,GACpCJ,EAAgB/C,KAAKmD,EACvB,CACF,CACF,EACdnB,WAAoB,IAMpB/B,EAAOH,QAAU+B,EAAQF,QAAU,CAAC,C,kBClEpC,IAAIC,EAAM,EAAQ,OACFC,EAAU,EAAQ,OAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAAC5B,EAAOC,GAAI2B,EAAS,MAwDjCD,EAAIC,EArDH,CAEdG,OAAiB,SAAUC,GAgBX,IAAKV,OAAOC,8BAEV,YADAU,SAASC,KAAKC,YAAYH,GAI5B,IAAII,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAc5B,SACjB4B,EAzBF,SAASE,EAAwBC,EAASC,EAAOP,UAC/C,MAAMQ,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,SAAQC,GAAMH,EAAS1C,KAAK6C,KAG3DJ,EAAKE,iBAAiB,KAAKC,SAAQC,IAC7BA,EAAGC,YACLJ,EAAS1C,QAAQuC,EAAwBC,EAASK,EAAGC,YACvD,IAGKJ,CACT,CAWkBH,CAAwB,qBAG1C,MAAMQ,EAAkB,GACxB,IAAK,IAAIC,EAAI,EAAGA,EAAIX,EAAc5B,OAAQuC,IAAK,CAC7C,MAAMC,EAAeZ,EAAcW,GACnC,GAAU,IAANA,EACFC,EAAaH,WAAWV,YAAYH,GACpCA,EAASiB,OAAS,WACZH,EAAgBtC,OAAS,GAC3BsC,EAAgBH,SAASO,IAEvBA,EAAUC,UAAYnB,EAASmB,SAAS,GAG9C,MACK,CACL,MAAMD,EAAYlB,EAASoB,WAAU,GACrCJ,EAAaH,WAAWV,YAAYe,GACpCJ,EAAgB/C,KAAKmD,EACvB,CACF,CACF,EACdnB,WAAoB,IAMpB/B,EAAOH,QAAU+B,EAAQF,QAAU,CAAC,C,kBClEpC,IAAIC,EAAM,EAAQ,OACFC,EAAU,EAAQ,OAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAAC5B,EAAOC,GAAI2B,EAAS,MAwDjCD,EAAIC,EArDH,CAEdG,OAAiB,SAAUC,GAgBX,IAAKV,OAAOC,8BAEV,YADAU,SAASC,KAAKC,YAAYH,GAI5B,IAAII,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAc5B,SACjB4B,EAzBF,SAASE,EAAwBC,EAASC,EAAOP,UAC/C,MAAMQ,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,SAAQC,GAAMH,EAAS1C,KAAK6C,KAG3DJ,EAAKE,iBAAiB,KAAKC,SAAQC,IAC7BA,EAAGC,YACLJ,EAAS1C,QAAQuC,EAAwBC,EAASK,EAAGC,YACvD,IAGKJ,CACT,CAWkBH,CAAwB,qBAG1C,MAAMQ,EAAkB,GACxB,IAAK,IAAIC,EAAI,EAAGA,EAAIX,EAAc5B,OAAQuC,IAAK,CAC7C,MAAMC,EAAeZ,EAAcW,GACnC,GAAU,IAANA,EACFC,EAAaH,WAAWV,YAAYH,GACpCA,EAASiB,OAAS,WACZH,EAAgBtC,OAAS,GAC3BsC,EAAgBH,SAASO,IAEvBA,EAAUC,UAAYnB,EAASmB,SAAS,GAG9C,MACK,CACL,MAAMD,EAAYlB,EAASoB,WAAU,GACrCJ,EAAaH,WAAWV,YAAYe,GACpCJ,EAAgB/C,KAAKmD,EACvB,CACF,CACF,EACdnB,WAAoB,IAMpB/B,EAAOH,QAAU+B,EAAQF,QAAU,CAAC,C", "sources": ["webpack://webviewer-ui/./src/components/RedactionSearchResultGroup/RedactionSearchResult/RedactionSearchResult.scss", "webpack://webviewer-ui/./src/components/ReactSelectWebComponentProvider/ReactSelectWebComponentProvider.js", "webpack://webviewer-ui/./src/components/ReactSelectWebComponentProvider/index.js", "webpack://webviewer-ui/./src/components/RedactionSearchResults/RedactionSearchResults.scss", "webpack://webviewer-ui/./src/components/RedactionPageGroup/RedactionItem/RedactionItem.scss", "webpack://webviewer-ui/./src/components/CreatableMultiSelect/CreatableMultiSelect.scss?b5b1", "webpack://webviewer-ui/./src/components/RedactionSearchResultGroup/RedactionSearchResult/RedactionSearchResult.scss?cdc1", "webpack://webviewer-ui/./src/components/RedactionSearchOverlay/RedactionSearchMultiSelect/RedactionSearchMultiSelect.scss", "webpack://webviewer-ui/./src/components/RedactionPageGroup/RedactionPageGroup.scss?75f8", "webpack://webviewer-ui/./src/components/RedactionSearchOverlay/RedactionSearchMultiSelect/RedactionSearchMultiSelect.scss?f8e0", "webpack://webviewer-ui/./src/components/RedactionSearchOverlay/RedactionSearchOverlay.scss", "webpack://webviewer-ui/./src/helpers/applyRedactions.js", "webpack://webviewer-ui/./src/components/RedactionSearchOverlay/RedactionSearchOverlay.scss?16bc", "webpack://webviewer-ui/./src/components/RedactionPanel/RedactionPanel.scss", "webpack://webviewer-ui/./src/components/RedactionPanel/RedactionPanel.scss?bca9", "webpack://webviewer-ui/./src/components/RedactionSearchResultGroup/RedactionSearchResultGroup.scss", "webpack://webviewer-ui/./src/components/RedactionPageGroup/RedactionPageGroup.scss", "webpack://webviewer-ui/./src/components/RedactionPanel/RedactionPanelContext.js", "webpack://webviewer-ui/./src/components/RedactionTextPreview/RedactionTextPreviewContainer.js", "webpack://webviewer-ui/./src/components/RedactionTextPreview/index.js", "webpack://webviewer-ui/./src/components/RedactionPageGroup/RedactionItem/RedactionItem.js", "webpack://webviewer-ui/./src/components/RedactionPageGroup/RedactionItem/RedactionItemContainer.js", "webpack://webviewer-ui/./src/components/RedactionPageGroup/RedactionItem/index.js", "webpack://webviewer-ui/./src/components/RedactionPageGroup/RedactionPageGroup.js", "webpack://webviewer-ui/./src/components/RedactionPageGroup/RedactionPageGroupContainer.js", "webpack://webviewer-ui/./src/components/RedactionPageGroup/index.js", "webpack://webviewer-ui/./src/components/RedactionPanel/RedactionPanel.js", "webpack://webviewer-ui/./src/components/CreatableMultiSelect/CreatableMultiSelect.js", "webpack://webviewer-ui/./src/components/CreatableMultiSelect/index.js", "webpack://webviewer-ui/./src/components/RedactionSearchOverlay/RedactionSearchMultiSelect/RedactionSearchMultiSelect.js", "webpack://webviewer-ui/./src/components/RedactionSearchOverlay/RedactionSearchMultiSelect/index.js", "webpack://webviewer-ui/./src/components/RedactionSearchOverlay/RedactionSearchOverlay.js", "webpack://webviewer-ui/./src/helpers/multiSearch.js", "webpack://webviewer-ui/./src/components/RedactionSearchOverlay/RedactionSearchOverlayContainer.js", "webpack://webviewer-ui/./src/components/RedactionSearchOverlay/index.js", "webpack://webviewer-ui/./src/components/RedactionSearchResultGroup/RedactionSearchResult/RedactionSearchResult.js", "webpack://webviewer-ui/./src/components/RedactionSearchResultGroup/RedactionSearchResult/index.js", "webpack://webviewer-ui/./src/components/RedactionSearchResultGroup/RedactionSearchResult/RedactionSearchResultContainer.js", "webpack://webviewer-ui/./src/components/RedactionSearchResultGroup/RedactionSearchResultGroup.js", "webpack://webviewer-ui/./src/components/RedactionSearchResultGroup/index.js", "webpack://webviewer-ui/./src/constants/searchStatus.js", "webpack://webviewer-ui/./src/components/RedactionSearchResults/RedactionSearchResults.js", "webpack://webviewer-ui/./src/components/RedactionSearchResults/RedactionSearchResultsContainer.js", "webpack://webviewer-ui/./src/components/RedactionSearchResults/index.js", "webpack://webviewer-ui/./src/components/RedactionSearchPanel/RedactionSearchPanel.js", "webpack://webviewer-ui/./src/hooks/useOnRedactionSearchCompleted/useOnRedactionSearchCompleted.js", "webpack://webviewer-ui/./src/components/RedactionSearchPanel/index.js", "webpack://webviewer-ui/./src/components/RedactionSearchPanel/RedactionSearchPanelContainer.js", "webpack://webviewer-ui/./src/components/RedactionPanel/RedactionPanelContainer.js", "webpack://webviewer-ui/./src/components/RedactionPanel/index.js", "webpack://webviewer-ui/./src/components/CreatableMultiSelect/CreatableMultiSelect.scss", "webpack://webviewer-ui/./src/components/RedactionSearchResults/RedactionSearchResults.scss?b7ae", "webpack://webviewer-ui/./src/components/RedactionSearchResultGroup/RedactionSearchResultGroup.scss?b0dc", "webpack://webviewer-ui/./src/components/RedactionPageGroup/RedactionItem/RedactionItem.scss?6c02"], "sourcesContent": ["// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../../node_modules/css-loader/dist/runtime/api.js\");\nexports = ___CSS_LOADER_API_IMPORT___(false);\n// Module\nexports.push([module.id, \".redaction-search-result{display:flex;align-items:center;padding:12px;background-color:var(--component-background);border:1px solid transparent;border-radius:4px;box-shadow:0 0 3px var(--document-box-shadow);margin:8px 0;cursor:pointer}.redaction-search-result .Icon svg{transform:scale(1.2);padding-top:2px}.redaction-search-result .search-value{word-break:break-all;color:var(--secondary-button-text);font-weight:700}.redaction-search-result.active{background-color:transparent!important;border:1px solid var(--focus-border)}.redaction-search-result-info{font-size:13px;color:var(--text-color)}\", \"\"]);\n// Exports\nmodule.exports = exports;\n", "import React from 'react';\nimport { NonceProvider } from 'react-select';\nimport createCache from '@emotion/cache';\nimport getRootNode from 'helpers/getRootNode';\n\nclass EmotionNonceProvider extends NonceProvider {\n  createEmotionCache = (nonce) => {\n    return createCache({ nonce, container: this.props.container });\n  };\n}\n\nconst ReactSelectWebComponentProvider = ({ children }) => (\n  window.isApryseWebViewerWebComponent ?\n    <EmotionNonceProvider container={getRootNode()}>\n      {children}\n    </EmotionNonceProvider>\n    : children\n);\n\nexport default ReactSelectWebComponentProvider;", "import ReactSelectWebComponentProvider from './ReactSelectWebComponentProvider';\n\nexport default ReactSelectWebComponentProvider;", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../node_modules/css-loader/dist/runtime/api.js\");\nexports = ___CSS_LOADER_API_IMPORT___(false);\n// Module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.redaction-search-counter-controls{display:flex;flex-direction:row;margin-top:36px;font-size:var(--font-size-default);padding:16px;border:1px solid var(--lighter-border);background-color:var(--gray-0);border-radius:4px 4px 0 0;max-height:50px;min-height:50px;grid-column-gap:var(--padding-medium);-moz-column-gap:var(--padding-medium);column-gap:var(--padding-medium)}.redaction-search-counter-controls .redaction-search-results-counter{flex:2 1 auto}.redaction-search-counter-controls .redaction-search-results-counter span{font-weight:700}.redaction-search-counter-controls .spinner{margin:auto;flex:3 1 \\\"25px\\\"}.redaction-search-counter-controls button{padding:0;background-color:transparent;flex:1 1 auto;color:var(--secondary-button-text);border:none;cursor:pointer;height:100%;white-space:nowrap}:host(:not([data-tabbing=true])) .redaction-search-counter-controls button,html:not([data-tabbing=true]) .redaction-search-counter-controls button{outline:none}.redaction-search-counter-controls button:hover:not(:disabled){color:var(--secondary-button-hover)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .redaction-search-counter-controls button{font-size:var(--font-size-default)}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .redaction-search-counter-controls button{font-size:var(--font-size-default)}}.redaction-search-counter-controls button.disabled{opacity:.5}.redaction-search-counter-controls button.disabled span{color:var(--secondary-button-text)}.redaction-search-results-container{flex:1 1 auto;background-color:var(--gray-2);color:var(--faded-text);font-size:13px;border-left:1px solid var(--lighter-border);border-right:1px solid var(--lighter-border);display:flex;flex-direction:column}.redaction-search-no-results,.redaction-search-results-container.emptyList{justify-content:center;align-items:center}.redaction-search-panel-controls{display:flex;flex-direction:row;flex:0 1 52px;padding:12px;background-color:var(--component-background);border:1px solid var(--lighter-border);margin-bottom:16px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .redaction-search-panel-controls{margin-bottom:30px;font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .redaction-search-panel-controls{margin-bottom:30px;font-size:13px}}.redaction-search-panel-controls button{border:none;background-color:transparent;height:28px;padding:0 16px;cursor:pointer}:host(:not([data-tabbing=true])) .redaction-search-panel-controls button,html:not([data-tabbing=true]) .redaction-search-panel-controls button{outline:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .redaction-search-panel-controls button{height:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .redaction-search-panel-controls button{height:32px}}.redaction-search-panel-controls .Button{white-space:nowrap}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .redaction-search-panel-controls .Button{font-size:var(--font-size-default)}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .redaction-search-panel-controls .Button{font-size:var(--font-size-default)}}.redaction-search-panel-controls .Button.cancel{flex:2 1 auto;color:var(--secondary-button-text);border:none;cursor:pointer;margin-right:20px}.redaction-search-panel-controls .Button.cancel:hover:not(.disabled) span{color:var(--secondary-button-hover)}.redaction-search-panel-controls .Button.redact-all-selected{flex:1 1 auto;border:1px solid var(--secondary-button-text);border-radius:4px;margin-right:8px}.redaction-search-panel-controls .Button.redact-all-selected span{color:var(--secondary-button-text)}.redaction-search-panel-controls .Button.redact-all-selected.disabled{opacity:.5}.redaction-search-panel-controls .Button.redact-all-selected:hover:not(.disabled){border-color:var(--secondary-button-hover)}.redaction-search-panel-controls .Button.redact-all-selected:hover:not(.disabled) span{color:var(--secondary-button-hover)}.redaction-search-panel-controls .Button.mark-all-selected{flex:2 1 auto;background-color:var(--primary-button)!important;border:1px solid var(--primary-button);border-radius:4px}.redaction-search-panel-controls .Button.mark-all-selected span{color:var(--primary-button-text)}.redaction-search-panel-controls .Button.mark-all-selected:hover:not(.disabled){border-color:var(--primary-button-hover);background-color:var(--primary-button-hover)!important}.redaction-search-panel-controls .Button.mark-all-selected.disabled{opacity:.5}\", \"\"]);\n// Exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};\nmodule.exports = exports;\n", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../../node_modules/css-loader/dist/runtime/api.js\");\nexports = ___CSS_LOADER_API_IMPORT___(false);\n// Module\nexports.push([module.id, \".redaction-item{display:flex;align-items:center;padding:12px 16px}.redaction-item:hover{background-color:var(--view-header-button-hover);cursor:pointer}.redaction-item.focus-visible,.redaction-item:focus-visible{outline:var(--focus-visible-outline)}.redaction-item.modular-ui:hover:not(:disabled):not(.disabled){background-color:transparent;box-shadow:inset 0 0 0 1px var(--hover-border)}.redaction-item-selected{background-color:var(--view-header-button-active)!important}.redaction-item-selected.modular-ui{box-shadow:inset 0 0 0 1px var(--focus-border)}.redaction-item-info{flex:1;padding-left:18px;padding-right:20px}.redaction-item-preview{font-size:13px;color:var(--text-color)}.redaction-item-date-author{font-size:10px;color:var(--faded-text)}.redaction-item-label-text{font-size:10px;margin:2px 0}.redaction-item-delete.customUI:hover{box-shadow:inset 0 0 0 1px var(--hover-border)}\", \"\"]);\n// Exports\nmodule.exports = exports;\n", "var api = require(\"!../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../node_modules/css-loader/dist/cjs.js!../../../node_modules/postcss-loader/src/index.js??ruleSet[1].rules[1].use[2]!../../../node_modules/sass-loader/dist/cjs.js!./CreatableMultiSelect.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "var api = require(\"!../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../node_modules/css-loader/dist/cjs.js!../../../../node_modules/postcss-loader/src/index.js??ruleSet[1].rules[1].use[2]!../../../../node_modules/sass-loader/dist/cjs.js!./RedactionSearchResult.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../../node_modules/css-loader/dist/runtime/api.js\");\nexports = ___CSS_LOADER_API_IMPORT___(false);\n// Module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .redaction-search-multi-select{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .redaction-search-multi-select{font-size:13px}}.redaction-search-multi-select-search-icon-container{height:28px;align-self:flex-start;display:flex;align-items:center;margin:0 var(--padding-tiny)}.redaction-search-multi-select-search-icon-container .Icon{width:16px;height:16px}\", \"\"]);\n// Exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};\nmodule.exports = exports;\n", "var api = require(\"!../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../node_modules/css-loader/dist/cjs.js!../../../node_modules/postcss-loader/src/index.js??ruleSet[1].rules[1].use[2]!../../../node_modules/sass-loader/dist/cjs.js!./RedactionPageGroup.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "var api = require(\"!../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../node_modules/css-loader/dist/cjs.js!../../../../node_modules/postcss-loader/src/index.js??ruleSet[1].rules[1].use[2]!../../../../node_modules/sass-loader/dist/cjs.js!./RedactionSearchMultiSelect.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../node_modules/css-loader/dist/runtime/api.js\");\nexports = ___CSS_LOADER_API_IMPORT___(false);\n// Module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionSearchOverlay{margin-left:16px;margin-right:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionSearchOverlay{margin-left:16px;margin-right:16px}}.RedactionSearchOverlay input{width:100%;padding:6px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionSearchOverlay .creatable-multi-select-label{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionSearchOverlay .creatable-multi-select-label{font-size:13px}}\", \"\"]);\n// Exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};\nmodule.exports = exports;\n", "import core from 'core';\nimport i18next from 'i18next';\n\nimport actions from 'actions';\nimport { fireError } from 'helpers/fireEvent';\nimport downloadPdf from 'helpers/downloadPdf';\n\nfunction noop() { }\n\nexport default (annotations, onRedactionCompleted = noop, activeDocumentViewerKey = 1) => (dispatch) => {\n  if (core.isWebViewerServerDocument()) {\n    // when are using Webviewer Server, it'll download the redacted document\n    return webViewerServerApply(annotations, dispatch, activeDocumentViewerKey);\n  }\n  return webViewerApply(annotations, onRedactionCompleted, dispatch, activeDocumentViewerKey);\n};\n\nconst webViewerServerApply = (annotations, dispatch, activeDocumentViewerKey) => core.applyRedactions(annotations, activeDocumentViewerKey).then((results) => {\n  if (results && results.url) {\n    return downloadPdf(dispatch, {\n      filename: 'redacted.pdf',\n      includeAnnotations: true,\n      externalURL: results.url,\n    });\n  }\n  console.warn('WebViewer Server did not return a valid result');\n});\n\nconst webViewerApply = (annotations, onRedactionCompleted, dispatch, activeDocumentViewerKey) => {\n  const message = i18next.t('warning.redaction.applyMessage');\n  const title = i18next.t('warning.redaction.applyTile');\n  const confirmBtnText = i18next.t('action.apply');\n\n  const warning = {\n    message,\n    title,\n    confirmBtnText,\n    onConfirm: () => {\n      core.applyRedactions(annotations, activeDocumentViewerKey)\n        .then(() => {\n          onRedactionCompleted();\n        })\n        .catch((err) => fireError(err));\n      return Promise.resolve();\n    },\n  };\n\n  return dispatch(actions.showWarningMessage(warning));\n};\n", "var api = require(\"!../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../node_modules/css-loader/dist/cjs.js!../../../node_modules/postcss-loader/src/index.js??ruleSet[1].rules[1].use[2]!../../../node_modules/sass-loader/dist/cjs.js!./RedactionSearchOverlay.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../node_modules/css-loader/dist/runtime/api.js\");\nexports = ___CSS_LOADER_API_IMPORT___(false);\n// Module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.RedactionPanel{padding:16px 16px 0;display:flex;flex-direction:column}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel{width:100%;height:100%;min-width:100%;padding:8px 0 0}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .close-container{display:flex;align-items:center;justify-content:flex-end;height:32px;width:100%;padding-right:12px}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .close-container .close-icon-container{padding:0;border:none;background-color:transparent;z-index:3;cursor:pointer}:host(:not([data-tabbing=true])) .App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .close-container .close-icon-container,html:not([data-tabbing=true]) .App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .close-container .close-icon-container{outline:none}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .close-container .close-icon-container .close-icon{width:24px;height:24px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel{width:100%;height:100%;min-width:100%;padding:8px 0 0}.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .close-container{display:flex;align-items:center;justify-content:flex-end;height:32px;width:100%;padding-right:12px}.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .close-container .close-icon-container{padding:0;border:none;background-color:transparent;z-index:3;cursor:pointer}:host(:not([data-tabbing=true])) .App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .close-container .close-icon-container,html:not([data-tabbing=true]) .App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .close-container .close-icon-container{outline:none}.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .close-container .close-icon-container .close-icon{width:24px;height:24px}}.RedactionPanel .marked-redaction-counter{flex:0 1 19px;margin-top:24px;margin-bottom:12px;font-size:16px}.RedactionPanel .marked-redaction-counter span{font-weight:700}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .marked-redaction-counter{margin:16px;font-size:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .marked-redaction-counter{margin:16px;font-size:16px}}.RedactionPanel .no-marked-redactions{display:flex;flex-direction:column;align-items:center;flex:1 1 auto}.RedactionPanel .no-marked-redactions .msg{text-align:center}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .no-marked-redactions .msg{line-height:15px;width:146px}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .no-marked-redactions .msg{line-height:15px;width:146px}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .no-marked-redactions .msg{line-height:15px;width:146px;margin-bottom:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .no-marked-redactions .msg{line-height:15px;width:146px;margin-bottom:32px}}.RedactionPanel .no-marked-redactions .empty-icon,.RedactionPanel .no-marked-redactions .empty-icon svg{width:65px;height:83px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .no-marked-redactions .empty-icon,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .no-marked-redactions .empty-icon svg{width:60px;height:60px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .no-marked-redactions .empty-icon,.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .no-marked-redactions .empty-icon svg{width:60px;height:60px}}.RedactionPanel .no-marked-redactions .empty-icon *{fill:var(--gray-5);color:var(--gray-5)}.RedactionPanel .redaction-panel-controls{flex:0 0 57px;margin-left:-16px;padding-top:13px;border-top:1px solid var(--divider);display:flex;background-color:var(--component-background);width:inherit;justify-content:flex-end;padding-right:16px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .redaction-panel-controls{margin:0 0 16px;padding:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .redaction-panel-controls{margin:0 0 16px;padding:16px}}.RedactionPanel .redaction-panel-controls .Button.redact-all-marked{padding:0;border:none;background-color:transparent;background-color:var(--primary-button);color:var(--primary-button-text);border-radius:4px;height:32px;width:90px}:host(:not([data-tabbing=true])) .RedactionPanel .redaction-panel-controls .Button.redact-all-marked,html:not([data-tabbing=true]) .RedactionPanel .redaction-panel-controls .Button.redact-all-marked{outline:none}.RedactionPanel .redaction-panel-controls .Button.redact-all-marked:hover:not(.disabled){background-color:var(--primary-button-hover)}.RedactionPanel .redaction-panel-controls .Button.redact-all-marked.disabled{opacity:.5}.RedactionPanel .redaction-panel-controls .Button.redact-all-marked.disabled span{color:var(--primary-button-text)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .redaction-panel-controls .Button.redact-all-marked{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .redaction-panel-controls .Button.redact-all-marked{font-size:13px}}.RedactionPanel .redaction-panel-controls .clear-all-marked{padding:0;color:var(--secondary-button-text);background-color:transparent;border:none;height:32px;width:70px;margin-right:8px;cursor:pointer}:host(:not([data-tabbing=true])) .RedactionPanel .redaction-panel-controls .clear-all-marked,html:not([data-tabbing=true]) .RedactionPanel .redaction-panel-controls .clear-all-marked{outline:none}.RedactionPanel .redaction-panel-controls .clear-all-marked:hover:not(.disabled){color:var(--secondary-button-hover)}.RedactionPanel .redaction-panel-controls .clear-all-marked.disabled{opacity:.5;cursor:not-allowed}.RedactionPanel .redaction-panel-controls .clear-all-marked.disabled span{color:var(--secondary-button-text)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .redaction-panel-controls .clear-all-marked{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .redaction-panel-controls .clear-all-marked{font-size:13px}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .redaction-panel-controls{left:0;margin-bottom:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .redaction-panel-controls{left:0;margin-bottom:16px}}.RedactionPanel .redaction-group-container{flex:1 1 auto}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .redaction-group-container{margin-right:4px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .redaction-group-container{margin-right:4px}}.RedactionPanel button.focus-visible,.RedactionPanel button:focus-visible{outline:var(--focus-visible-outline)}.ModularPanel-container .RedactionPanel{height:100%;padding:unset}.ModularPanel-container .RedactionPanel .redaction-panel-controls{margin-right:-16px;padding-bottom:16px}\", \"\"]);\n// Exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};\nmodule.exports = exports;\n", "var api = require(\"!../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../node_modules/css-loader/dist/cjs.js!../../../node_modules/postcss-loader/src/index.js??ruleSet[1].rules[1].use[2]!../../../node_modules/sass-loader/dist/cjs.js!./RedactionPanel.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../node_modules/css-loader/dist/runtime/api.js\");\nexports = ___CSS_LOADER_API_IMPORT___(false);\n// Module\nexports.push([module.id, \".redaction-search-results-page-number{display:flex;align-items:start;padding:8px 12px 4px}.redaction-search-results-page-number .redaction-search-results-page-number-checkbox{position:absolute;left:24px}.redaction-search-results-page-number .collapsible-page-group-header button{font-size:13px;font-weight:400;color:var(--faded-text);margin:0 0 0 32px;width:calc(100% - 32px)}\", \"\"]);\n// Exports\nmodule.exports = exports;\n", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../node_modules/css-loader/dist/runtime/api.js\");\nexports = ___CSS_LOADER_API_IMPORT___(false);\n// Module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.redaction-items{margin:8px 2px 1px;background-color:var(--component-background);box-shadow:0 0 3px 0 var(--box-shadow);border-radius:4px}.redaction-items>:first-child{padding-top:16px;border-radius:4px 4px 0 0}.redaction-items>:last-child{padding-bottom:16px;border-radius:0 0 4px 4px}.redaction-items>:only-child{padding-top:16px;padding-bottom:16px;border-radius:4px}.redaction-page-group{padding-top:12px;padding-bottom:12px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .redaction-page-group{padding-top:8px;padding-right:4px;padding-left:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .redaction-page-group{padding-top:8px;padding-right:4px;padding-left:16px}}.redaction-page-group h2{margin:0}.redaction-page-group h2 button{margin:0;font-size:13px;font-weight:400;color:var(--faded-text)}.redaction-page-group-header{display:flex;justify-content:space-between;align-items:baseline}.expand-arrow{height:16px;width:16px;display:flex;align-items:center;cursor:pointer}.expand-arrow .Icon{width:12px;height:12px}.expand-arrow.Button.custom-ui.icon-only:hover{box-shadow:inset 0 0 0 1px var(--hover-border)}\", \"\"]);\n// Exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};\nmodule.exports = exports;\n", "import React, { useState, useEffect } from 'react';\nimport core from 'core';\n\n\nconst RedactionPanelContext = React.createContext();\n\nconst RedactionPanelProvider = ({ children }) => {\n  const [selectedRedactionItemId, setSelectedRedactionItemId] = useState(null);\n  const [isRedactionSearchActive, setIsRedactionSearchActive] = useState(false);\n  const [activeSearchResultIndex, setActiveSearchResultIndex] = useState(-1);\n\n  useEffect(() => {\n    const onAnnotationSelected = (annotations, action) => {\n      if (action === 'selected') {\n        const redactionAnnotations = annotations.filter((annotation) => annotation.Subject === 'Redact');\n        // If multiple ones selected, we only use the first one\n        const selectedAnnotationId = redactionAnnotations.length > 0 ? redactionAnnotations[0].Id : null;\n        setSelectedRedactionItemId(selectedAnnotationId);\n      } else {\n        setSelectedRedactionItemId(null);\n      }\n    };\n\n    const activeSearchResultChanged = (newActiveSearchResult) => {\n      if (!newActiveSearchResult) {\n        return;\n      }\n      const coreSearchResults = core.getPageSearchResults() || [];\n      const newActiveSearchResultIndex = coreSearchResults.findIndex((searchResult) => {\n        return core.isSearchResultEqual(searchResult, newActiveSearchResult);\n      });\n      setActiveSearchResultIndex(newActiveSearchResultIndex);\n    };\n\n    core.addEventListener('annotationSelected', onAnnotationSelected);\n    core.addEventListener('activeSearchResultChanged', activeSearchResultChanged);\n\n    return () => {\n      core.removeEventListener('annotationSelected', onAnnotationSelected);\n      core.removeEventListener('activeSearchResultChanged', activeSearchResultChanged);\n    };\n  }, []);\n\n  const value = {\n    selectedRedactionItemId,\n    setSelectedRedactionItemId,\n    isRedactionSearchActive,\n    setIsRedactionSearchActive,\n    activeSearchResultIndex\n  };\n\n  return <RedactionPanelContext.Provider value={value}>{children}</RedactionPanelContext.Provider>;\n};\n\nexport { RedactionPanelProvider, RedactionPanelContext };\n", "import React from 'react';\nimport { useSelector, shallowEqual } from 'react-redux';\nimport selectors from 'selectors';\nimport NoteTextPreview from '../NoteTextPreview/NoteTextPreview';\n\nconst RedactionTextPreviewContainer = (props) => {\n  const [redactionPanelWidth] = useSelector(\n    (state) => [\n      selectors.getRedactionPanelWidth(state),\n    ],\n    shallowEqual,\n  );\n\n  return (\n    <NoteTextPreview {...props} panelWidth={redactionPanelWidth} comment />\n  );\n};\n\nexport default RedactionTextPreviewContainer;", "import RedactionTextPreviewContainer from './RedactionTextPreviewContainer';\n\nexport default RedactionTextPreviewContainer;", "import React from 'react';\nimport { useTranslation } from 'react-i18next';\nimport Icon from 'src/components/Icon';\nimport getLatestActivityDate from 'helpers/getLatestActivityDate';\nimport dayjs from 'dayjs';\nimport Button from 'components/Button';\nimport './RedactionItem.scss';\nimport RedactionTextPreview from 'components/RedactionTextPreview';\nimport classNames from 'classnames';\nimport { redactionTypeMap } from 'constants/redactionTypes';\nimport { useSelector } from 'react-redux';\nimport selectors from 'selectors';\n\nconst RedactionItem = (props) => {\n  // Remove if we get rid of legacy UI along with stylesheet changes\n  const [isCustomUI] = useSelector(\n    (state) => [\n      selectors.getFeatureFlags(state)?.customizableUI,\n    ]\n  );\n  const {\n    iconColor,\n    annotation,\n    author,\n    dateFormat,\n    language,\n    onRedactionItemDelete,\n    onRedactionItemSelection,\n    textPreview,\n    isSelected,\n    timezone,\n  } = props;\n  const { t } = useTranslation();\n\n  let date = getLatestActivityDate(annotation);\n\n  if (timezone) {\n    const datetimeStr = date.toLocaleString('en-US', { timeZone: timezone });\n    date = new Date(datetimeStr);\n  }\n\n  const formattedDate = date ? dayjs(date).locale(language).format(dateFormat) : t('option.notesPanel.noteContent.noDate');\n  const dateAndAuthor = `${author} - ${formattedDate}`;\n  const className = classNames('redaction-item', { 'redaction-item-selected': isSelected }, { 'modular-ui': isCustomUI });\n  const {\n    label,\n    icon = 'icon-form-field-text', // Default icon if none provided\n    redactionType\n  } = annotation;\n\n  let redactionPreview;\n\n  if (redactionType === redactionTypeMap['TEXT']) {\n    redactionPreview = (\n      <RedactionTextPreview linesToBreak={2}>\n        {textPreview}\n      </RedactionTextPreview>);\n  } else if (\n    redactionType === redactionTypeMap['FULL_PAGE']\n    || redactionType === redactionTypeMap['FULL_VIDEO_FRAME']\n    || redactionType === redactionTypeMap['REGION']\n    || redactionType === redactionTypeMap['AUDIO_REDACTION']\n    || redactionType === redactionTypeMap['FULL_VIDEO_FRAME_AND_AUDIO']\n  ) {\n    redactionPreview = t(label);\n  } else {\n    redactionPreview = annotation.getContents();\n  }\n\n  const onKeyUp = (event) => {\n    if (event.key === 'Enter') {\n      onRedactionItemSelection();\n    }\n  };\n\n  return (\n    <div role=\"listitem\" className={className} onClick={onRedactionItemSelection} onKeyUp={onKeyUp} tabIndex={0}>\n      <div className=\"redaction-icon-container\">\n        <Icon glyph={icon} color={iconColor} />\n      </div>\n      <div className=\"redaction-item-info\">\n        <div className=\"redaction-item-preview\">\n          {redactionPreview}\n        </div>\n        {\n          annotation.OverlayText ?\n            <div className=\"redaction-item-label-text\">\n              {t('option.stylePopup.labelText')}: {annotation.OverlayText}\n            </div> : null\n        }\n        <div className=\"redaction-item-date-author\">\n          {dateAndAuthor}\n        </div>\n      </div>\n      <Button\n        className='redaction-item-delete'\n        style={{ marginLeft: 'auto' }}\n        img={'icon-close'}\n        onClick={onRedactionItemDelete}\n        ariaLabel={`${redactionPreview} ${dateAndAuthor} ${t('action.delete')} `}\n      />\n    </div>\n  );\n};\n\nexport default React.memo(RedactionItem);", "import React, { useCallback, useContext } from 'react';\nimport { shallowEqual, useSelector } from 'react-redux';\nimport selectors from 'selectors';\nimport RedactionItem from './RedactionItem';\nimport core from 'core';\nimport { RedactionPanelContext } from '../../RedactionPanel/RedactionPanelContext';\n\nconst RedactionItemContainer = (props) => {\n  const { annotation } = props;\n\n  const { selectedRedactionItemId, setSelectedRedactionItemId } = useContext(RedactionPanelContext);\n\n  const [\n    dateFormat,\n    language,\n    customNoteSelectionFunction,\n    timezone,\n  ] = useSelector(\n    (state) => [\n      selectors.getNoteDateFormat(state),\n      selectors.getCurrentLanguage(state),\n      selectors.getCustomNoteSelectionFunction(state),\n      selectors.getTimezone(state)\n    ],\n    shallowEqual,\n  );\n\n  const textPreview = annotation.getCustomData('trn-annot-preview');\n  const iconColor = annotation.StrokeColor.toString();\n  const author = core.getDisplayAuthor(annotation['Author']);\n\n  const onRedactionItemSelection = useCallback(() => {\n    customNoteSelectionFunction && customNoteSelectionFunction(annotation);\n    core.deselectAllAnnotations();\n    core.selectAnnotation(annotation);\n    core.jumpToAnnotation(annotation);\n    setSelectedRedactionItemId(annotation.Id);\n  }, [annotation]);\n\n  const onRedactionItemDelete = useCallback(() => {\n    core.deleteAnnotations([annotation]);\n  }, [annotation]);\n\n  return (\n    <RedactionItem\n      dateFormat={dateFormat}\n      language={language}\n      timezone={timezone}\n      author={author}\n      annotation={annotation}\n      iconColor={iconColor}\n      textPreview={textPreview}\n      onRedactionItemDelete={onRedactionItemDelete}\n      onRedactionItemSelection={onRedactionItemSelection}\n      isSelected={selectedRedactionItemId === annotation.Id}\n    />\n  );\n};\n\nexport default RedactionItemContainer;", "import RedactionItemContainer from './RedactionItemContainer';\n\nexport default RedactionItemContainer;", "import React from 'react';\nimport PropTypes from 'prop-types';\nimport RedactionItem from './RedactionItem';\nimport CollapsibleSection from 'components/CollapsibleSection';\nimport { useTranslation } from 'react-i18next';\n\nimport './RedactionPageGroup.scss';\n\nconst RedactionPageGroup = (props) => {\n  const {\n    pageNumber,\n    redactionItems,\n  } = props;\n\n  const { t } = useTranslation();\n\n  const header = () => {\n    return (\n      `${t('option.shared.page')} ${pageNumber}`\n    );\n  };\n\n  return (\n    <CollapsibleSection\n      className=\"redaction-page-group\"\n      header={header}\n      expansionDescription={`${t('option.shared.page')} ${pageNumber} ${t('redactionPanel.redactionItems')}`}\n      headingLevel={2}\n    >\n      <div role=\"list\" className=\"redaction-items\">\n        {redactionItems.map((redactionItem) => (\n          <RedactionItem\n            annotation={redactionItem}\n            key={`${redactionItem.Id}-${pageNumber}`}\n          />\n        ))}\n      </div>\n    </CollapsibleSection>\n  );\n};\n\nRedactionPageGroup.propTypes = {\n  pageNumber: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  redactionItems: PropTypes.array,\n};\n\nexport default RedactionPageGroup;", "import React, { useEffect, useState } from 'react';\nimport RedactionPageGroup from './RedactionPageGroup';\nimport { getSortStrategies } from 'constants/sortStrategies';\n\nconst RedactionPageGroupContainer = (props) => {\n  // Putting this in the container in case we want to allow users to change sort strategies\n  // which are stored in the application state\n  const { redactionItems } = props;\n  // Sorting strategies can be applied to any list of annotations\n  const [sortedRedactionItems, setSortedRedactionItems] = useState([]);\n  useEffect(() => {\n    setSortedRedactionItems(getSortStrategies()['position'].getSortedNotes(redactionItems));\n  }, [redactionItems]);\n  return (\n    <RedactionPageGroup\n      redactionItems={sortedRedactionItems}\n      {...props}\n    />\n  );\n};\n\nexport default RedactionPageGroupContainer;", "import RedactionPageGroupContainer from './RedactionPageGroupContainer';\n\nexport default RedactionPageGroupContainer;", "import React, { useEffect, useState, useContext } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport classNames from 'classnames';\nimport Icon from 'components/Icon';\nimport { Virtuoso } from 'react-virtuoso';\nimport { RedactionPanelContext } from './RedactionPanelContext';\nimport { mapAnnotationToRedactionType } from 'constants/redactionTypes';\nimport Button from 'components/Button';\n\nimport './RedactionPanel.scss';\nimport RedactionPageGroup from '../RedactionPageGroup';\n\nconst RedactionPanel = (props) => {\n  const {\n    redactionAnnotations,\n    applyAllRedactions,\n    deleteAllRedactionAnnotations,\n    redactionTypesDictionary,\n  } = props;\n\n  const { t } = useTranslation();\n  const [redactionPageMap, setRedactionPageMap] = useState({});\n  const [redactionPageNumbers, setRedactionPageNumbers] = useState([]);\n  // The following prop is needed only for the tests to actually render a list of results\n  // it only is ever injected in the tests\n  const { isTestMode } = useContext(RedactionPanelContext);\n\n  useEffect(() => {\n    const redactionPageMap = {};\n    redactionAnnotations.forEach((annotation) => {\n      const redactionType = mapAnnotationToRedactionType(annotation);\n      const { label, icon } = redactionTypesDictionary[redactionType] || {\n        icon: 'icon-tool-redaction-area',\n        label: 'redactionPanel.redactionItem.regionRedaction'\n      };\n      annotation.label = label;\n      annotation.icon = icon;\n      annotation.redactionType = redactionType;\n\n      const pageNumber = annotation.PageNumber;\n      if (redactionPageMap[pageNumber] === undefined) {\n        redactionPageMap[pageNumber] = [annotation];\n      } else {\n        redactionPageMap[pageNumber] = [annotation, ...redactionPageMap[pageNumber]];\n      }\n    });\n\n    setRedactionPageMap(redactionPageMap);\n    setRedactionPageNumbers(Object.keys(redactionPageMap));\n  }, [redactionAnnotations]);\n\n  const renderRedactionPageGroups = () => {\n    // Needed for the tests to actually render a list of results\n    // Not needed for the actual app; if we set it it kills performance when there are a lot of annotations\n    const testModeProps = isTestMode ? { initialItemCount: redactionPageNumbers.length } : {};\n    return (\n      <div className=\"redaction-group-container\">\n        <Virtuoso\n          data={redactionPageNumbers}\n          itemContent={(index, pageNumber) => {\n            return (\n              <RedactionPageGroup\n                key={index}\n                pageNumber={pageNumber}\n                redactionItems={redactionPageMap[pageNumber]}\n              />);\n          }}\n          {...testModeProps}\n        />\n      </div>\n    );\n  };\n\n  const noRedactionAnnotations = (\n    <div className=\"no-marked-redactions\">\n      <div>\n        <Icon className=\"empty-icon\" glyph=\"icon-no-marked-redactions\" />\n      </div>\n      <div className=\"msg\">{t('redactionPanel.noMarkedRedactions')}</div>\n    </div>\n  );\n\n  const redactAllButtonClassName = classNames('redact-all-marked', { disabled: redactionAnnotations.length === 0 });\n  const clearAllButtonClassName = classNames('clear-all-marked', { disabled: redactionAnnotations.length === 0 });\n\n  return (\n    <>\n      <div className=\"marked-redaction-counter\">\n        <span>{t('redactionPanel.redactionCounter')}</span> {`(${redactionAnnotations.length})`}\n      </div>\n      {redactionPageNumbers.length > 0 ? renderRedactionPageGroups() : noRedactionAnnotations}\n      <div className=\"redaction-panel-controls\">\n        <Button\n          disabled={redactionAnnotations.length === 0}\n          className={clearAllButtonClassName}\n          onClick={deleteAllRedactionAnnotations}\n          label={t('redactionPanel.clearMarked')}\n        >\n          {t('redactionPanel.clearMarked')}\n        </Button>\n        <Button\n          disabled={redactionAnnotations.length === 0}\n          className={redactAllButtonClassName}\n          onClick={applyAllRedactions}\n          label={t('redactionPanel.redactAllMarked')}\n        >\n          {t('redactionPanel.redactAllMarked')}\n        </Button>\n      </div>\n    </>\n  );\n};\n\nexport default RedactionPanel;", "import React from 'react';\nimport CreatableSelect from 'react-select/creatable';\nimport ReactSelectWebComponentProvider from '../ReactSelectWebComponentProvider';\nimport './CreatableMultiSelect.scss';\nimport PropTypes from 'prop-types';\n\nconst CreatableMultiSelect = ({ id, label, ...rest }) => {\n  return (\n    <ReactSelectWebComponentProvider>\n      <label htmlFor={id} className=\"creatable-multi-select-label\">{label}</label>\n      <CreatableSelect\n        isMulti\n        {...rest}\n        inputId={id}\n      />\n    </ReactSelectWebComponentProvider>\n  );\n};\n\nCreatableMultiSelect.propTypes = {\n  id: PropTypes.string,\n  label: PropTypes.string,\n};\n\nCreatableMultiSelect.defaultProps = {\n  id: '',\n  label: '',\n};\n\nexport default CreatableMultiSelect;", "import CreatableMultiSelect from './CreatableMultiSelect';\n\nexport default CreatableMultiSelect;", "import React from 'react';\nimport { components } from 'react-select';\nimport Icon from 'components/Icon';\nimport CreatableMultiSelect from 'components/CreatableMultiSelect';\nimport { useTranslation } from 'react-i18next';\nimport { COMMON_COLORS, CUSTOM_UI_VARS } from 'constants/commonColors';\nimport PropTypes from 'prop-types';\nimport './RedactionSearchMultiSelect.scss';\nimport { isMobileSize } from 'helpers/getDeviceSize';\n\nconst getColorForMode = (isDarkMode, darkModeColor, lightModeColor, isFocused = true) => {\n  if (isFocused) {\n    return isDarkMode ? darkModeColor : lightModeColor;\n  }\n  return 'transparent';\n};\n\nconst getContainerMaxHeight = () => {\n  if (isMobileSize()) {\n    return '55px';\n  }\n  return '70px';\n};\n\nconst getStyles = (isDarkMode) => ({\n  groupHeading: (base) => ({\n    ...base,\n    textTransform: 'none',\n    fontSize: '13px',\n    fontWeight: 'bold',\n    color: getColorForMode(isDarkMode, COMMON_COLORS['white'], CUSTOM_UI_VARS['text-color']),\n    paddingBottom: '8px',\n    paddingLeft: '8px',\n    paddingTop: '10px',\n  }),\n  group: (base) => ({\n    ...base,\n    padding: '0px',\n  }),\n  menu: (base) => ({\n    ...base,\n    padding: '0px 0px 0px 0px',\n    borderRadius: '4px',\n    overflowY: 'visible',\n    margin: '0'\n  }),\n  menuList: (base) => ({\n    ...base,\n    padding: '0px',\n    backgroundColor: getColorForMode(isDarkMode, COMMON_COLORS['black'], COMMON_COLORS['gray0']),\n    overflowY: 'visible',\n    borderRadius: '4px',\n  }),\n  multiValue: (base) => ({\n    ...base,\n    backgroundColor: getColorForMode(isDarkMode, COMMON_COLORS['blue1DarkMode'], COMMON_COLORS['gray2']),\n    padding: '2px 8px',\n    fontSize: '13px',\n    borderRadius: '4px',\n    overflowY: 'hidden',\n    whiteSpace: 'nowrap',\n    color: getColorForMode(isDarkMode, COMMON_COLORS['white'], CUSTOM_UI_VARS['text-color'])\n  }),\n  multiValueRemove: (base) => ({\n    ...base,\n    color: COMMON_COLORS['gray6'],\n    borderRadius: '4px',\n    marginLeft: '4px',\n    padding: '0px',\n    '&:hover': {\n      backgroundColor: COMMON_COLORS['gray2'],\n      boxShadow: `inset 0 0 0 1px ${COMMON_COLORS['blue6']}`,\n      color: COMMON_COLORS['gray6'],\n    },\n    'svg': {\n      height: '16px',\n      width: '16px',\n    },\n  }),\n  option: (base) => ({\n    ...base,\n    display: 'flex',\n    fontSize: '13px',\n    padding: '6px 8px 0',\n    '&:hover': {\n      backgroundColor: getColorForMode(isDarkMode, COMMON_COLORS['blue1DarkMode'], CUSTOM_UI_VARS['primary-button-hover']),\n      color: COMMON_COLORS['gray0'],\n    },\n    backgroundColor: getColorForMode(isDarkMode, COMMON_COLORS['blue1DarkMode'], COMMON_COLORS['gray0']),\n    overflowY: 'visible',\n    whiteSpace: 'normal',\n    '&:last-child': {\n      borderRadius: '0 0 4px 4px',\n      paddingBottom: '6px',\n    },\n  }),\n  noOptionsMessage: (base) => ({\n    ...base,\n    color: CUSTOM_UI_VARS['text-color'],\n  }),\n  valueContainer: (base) => ({\n    ...base,\n    padding: '1px',\n    maxHeight: getContainerMaxHeight(),\n    overflowY: 'scroll',\n  }),\n  control: (base) => ({\n    ...base,\n    backgroundColor: getColorForMode(isDarkMode, COMMON_COLORS['gray10'], COMMON_COLORS['white']),\n    minHeight: '28px',\n    borderColor: getColorForMode(isDarkMode, COMMON_COLORS['gray8'], COMMON_COLORS['gray6']),\n    '&:focus-within': {\n      borderColor: getColorForMode(isDarkMode, COMMON_COLORS['gray8'], COMMON_COLORS['blue5']),\n    },\n    // override the default border color on focus\n    '&:hover': {\n      borderColor: getColorForMode(isDarkMode, COMMON_COLORS['gray8'], COMMON_COLORS['gray6']),\n    },\n    boxShadow: 'none !important',\n  }),\n  placeholder: (base) => ({\n    ...base,\n    fontSize: '13px',\n    color: getColorForMode(isDarkMode, COMMON_COLORS['gray7'], COMMON_COLORS['gray5']),\n    paddingLeft: '4px',\n  }),\n  input: (base) => ({\n    ...base,\n    fontSize: '13px',\n    color: getColorForMode(isDarkMode, COMMON_COLORS['white'], CUSTOM_UI_VARS['text-color']),\n    paddingLeft: '3px',\n  }),\n});\n\nconst RedactionOption = (props) => {\n  const { data } = props;\n  const { t } = useTranslation();\n  return (\n    <components.Option {...props}>\n      {data.icon && <Icon glyph={data.icon} />}\n      {t(data.label)}\n    </components.Option>\n  );\n};\n\nRedactionOption.propTypes = {\n  data: PropTypes.object.isRequired,\n};\n\nconst MultiValueLabel = ({ data }) => {\n  const { t } = useTranslation();\n\n  return (\n    <div style={{ display: 'flex', height: '18px' }}>\n      {data.icon && <Icon glyph={data.icon} />}\n      {t(data.label)}\n    </div>\n  );\n};\n\nMultiValueLabel.propTypes = {\n  data: PropTypes.object.isRequired,\n};\n\nconst CustomControl = ({ children, ...props }) => (\n  <components.Control {...props}>\n    <div className=\"redaction-search-multi-select-search-icon-container\">\n      <Icon className=\"redaction-search-multi-select-search-icon\" glyph=\"icon-header-search\" />\n    </div>\n    {children}\n  </components.Control>\n);\n\nCustomControl.propTypes = {\n  children: PropTypes.node,\n};\n\nconst RedactionSearchMultiSelect = (props) => {\n  const { t } = useTranslation();\n  const { activeTheme, redactionSearchOptions } = props;\n\n  const redactionGroup = [\n    {\n      label: t('redactionPanel.search.pattern'),\n      options: redactionSearchOptions,\n    },\n  ];\n\n  const isDarkMode = activeTheme === 'dark';\n  const styles = getStyles(isDarkMode);\n\n  return (\n    <CreatableMultiSelect\n      options={redactionGroup}\n      styles={styles}\n      components={{ Option: RedactionOption, MultiValueLabel, IndicatorsContainer: () => null, Control: CustomControl }}\n      placeholder={''}\n      formatCreateLabel={(value) => `${t('component.searchPanel')} ${value}`}\n      id=\"redaction-search-multi-select\"\n      label={t('redactionPanel.redactionSearchPlaceholder')}\n      {...props}\n    />\n  );\n};\n\nRedactionSearchMultiSelect.propTypes = {\n  activeTheme: PropTypes.string.isRequired,\n  redactionSearchOptions: PropTypes.array.isRequired,\n};\n\nexport default RedactionSearchMultiSelect;\n", "import RedactionSearchMultiSelect from './RedactionSearchMultiSelect';\n\nexport default RedactionSearchMultiSelect;", "import React from 'react';\nimport DataElementWrapper from '../DataElementWrapper';\nimport RedactionSearchMultiSelect from './RedactionSearchMultiSelect';\nimport { redactionTypeMap } from 'constants/redactionTypes';\nimport './RedactionSearchOverlay.scss';\n\nconst buildSearchOptions = (searchTerms) => {\n  const options = {\n    textSearch: [],\n    caseSensitive: true,\n  };\n\n  if (!searchTerms) {\n    return options;\n  }\n\n  searchTerms.forEach((searchTerm) => {\n    const { type } = searchTerm;\n    if (type === redactionTypeMap['TEXT']) {\n      options.textSearch.push(searchTerm.label);\n    } else {\n      options[type] = true;\n    }\n    if (searchTerm.regex) {\n      options.caseSensitive = options.caseSensitive && !searchTerm.regex.ignoreCase;\n    }\n  });\n\n  return options;\n};\n\nconst RedactionSearchOverlay = (props) => {\n  const {\n    setIsRedactionSearchActive,\n    searchTerms,\n    setSearchTerms,\n    executeRedactionSearch,\n    activeTheme,\n    redactionSearchOptions,\n  } = props;\n\n  const handleChange = (updatedSearchTerms) => {\n    setSearchTerms(updatedSearchTerms);\n    const options = buildSearchOptions(updatedSearchTerms);\n    executeRedactionSearch(options);\n  };\n\n  const handleCreate = (newValue) => {\n    const textTerm = {\n      label: newValue,\n      value: newValue,\n      type: redactionTypeMap['TEXT']\n    };\n    // Initially search terms are null so we safeguard against this\n    const nonNullSearchTerms = searchTerms || [];\n    const updatedSearchTerms = [...nonNullSearchTerms, textTerm];\n    setSearchTerms(updatedSearchTerms);\n    const options = buildSearchOptions(updatedSearchTerms);\n    options.caseSensitive = false;\n    executeRedactionSearch(options);\n  };\n\n  return (\n    <DataElementWrapper\n      className=\"RedactionSearchOverlay\"\n      dataElement=\"redactionSearchOverlay\"\n    >\n      <RedactionSearchMultiSelect\n        onFocus={() => setIsRedactionSearchActive(true)}\n        value={searchTerms}\n        onCreateOption={handleCreate}\n        onChange={handleChange}\n        activeTheme={activeTheme}\n        redactionSearchOptions={redactionSearchOptions}\n      />\n\n    </DataElementWrapper>\n\n  );\n};\n\nexport default RedactionSearchOverlay;", "import searchTextFullFactory from '../apis/searchTextFull';\nimport selectors from 'selectors';\nimport core from 'core';\n\n\nfunction multiSearch(store) {\n  return function multiSearch(searchTerms) {\n    const { getState } = store;\n    const state = getState();\n    const redactionSearchPatterns = selectors.getRedactionSearchPatterns(state);\n    // collect all regexes into an array\n    const searchOptionsMap = Object.keys(redactionSearchPatterns).reduce((map, key) => {\n      const { regex, type } = redactionSearchPatterns[key];\n      map[type] = regex;\n      return map;\n    }, {});\n\n\n    const options = {\n      regex: true,\n      caseSensitive: searchTerms.caseSensitive,\n    };\n\n    const { textSearch } = searchTerms;\n    const searchArray = [...textSearch];\n\n    // Now we can map type to regex\n    Object.keys(searchTerms).forEach((searchType) => {\n      const searchRegex = searchOptionsMap[searchType];\n      if (searchRegex) {\n        searchArray.push(searchRegex.source);\n      }\n    });\n\n    const searchString = searchArray.join('|');\n\n    // If search string is empty we return and clear searches or we send the search logic\n    // into an infinte loop\n    if (searchString === '') {\n      core.clearSearchResults();\n      return;\n    }\n\n    const searchTextFull = searchTextFullFactory();\n    searchTextFull(searchString, options);\n  };\n}\n\nexport default multiSearch;", "import React, { useContext } from 'react';\nimport { useStore, useSelector, shallowEqual } from 'react-redux';\nimport selectors from 'selectors';\n\nimport RedactionSearchOverlay from './RedactionSearchOverlay';\nimport { RedactionPanelContext } from '../RedactionPanel/RedactionPanelContext';\nimport multiSearchFactory from '../../helpers/multiSearch';\n\n\nfunction executeRedactionSearch(options, store) {\n  const multiSearch = multiSearchFactory(store);\n  multiSearch(options);\n}\n\nconst RedactionSearchOverlayContainer = (props) => {\n  const { setIsRedactionSearchActive } = useContext(RedactionPanelContext);\n  const store = useStore();\n  const activeTheme = useSelector((state) => selectors.getActiveTheme(state));\n  const redactionSearchPatterns = useSelector((state) => selectors.getRedactionSearchPatterns(state), shallowEqual);\n  const redactionSearchOptions = Object.values(redactionSearchPatterns).map((pattern) => ({\n    ...pattern,\n    value: pattern.type,\n  }));\n\n  return (\n    <RedactionSearchOverlay\n      setIsRedactionSearchActive={setIsRedactionSearchActive}\n      executeRedactionSearch={(options = {}) => executeRedactionSearch(options, store)}\n      activeTheme={activeTheme}\n      redactionSearchOptions={redactionSearchOptions}\n      {...props}\n    />);\n};\n\nexport default RedactionSearchOverlayContainer;", "import RedactionSearchOverlayContainer from './RedactionSearchOverlayContainer';\n\nexport default RedactionSearchOverlayContainer;", "import React from 'react';\nimport { Choice } from '@pdftron/webviewer-react-toolkit';\nimport Icon from 'components/Icon';\nimport './RedactionSearchResult.scss';\nimport classNames from 'classnames';\nimport { redactionTypeMap } from 'constants/redactionTypes';\nimport PropTypes from 'prop-types';\n\n// Alternatively wrap this in useCallback and declare inside component\nconst displayRedactionSearchResult = (props) => {\n  const { ambientStr, resultStrStart, resultStrEnd, resultStr, type } = props;\n  if (type === redactionTypeMap['TEXT']) {\n    const searchValue = ambientStr === '' ? resultStr : ambientStr.slice(resultStrStart, resultStrEnd);\n    const textBeforeSearchValue = ambientStr.slice(0, resultStrStart);\n    const textAfterSearchValue = ambientStr.slice(resultStrEnd);\n    return (\n      <>\n        {textBeforeSearchValue}\n        <span className=\"search-value\">{searchValue}</span>\n        {textAfterSearchValue}\n      </>\n    );\n  }\n  return resultStr;\n};\n\nconst RedactionSearchResult = (props) => {\n  const {\n    isChecked,\n    onChange,\n    onClickResult,\n    isActive,\n    icon,\n    ambientStr\n  } = props;\n\n  const displayResult = displayRedactionSearchResult(props);\n  const searchResultClassname = classNames('redaction-search-result', { active: isActive });\n\n  return (\n    <div className={searchResultClassname} role=\"listitem\" onClick={onClickResult}>\n      <div style={{ paddingRight: '14px' }}>\n        <Choice\n          aria-label={`${ambientStr}`}\n          checked={isChecked}\n          onChange={onChange}\n        />\n      </div>\n      <div style={{ paddingRight: '14px' }}>\n        <Icon glyph={icon} />\n      </div>\n      <div className=\"redaction-search-result-info\">\n        {displayResult}\n      </div>\n    </div >\n  );\n};\n\nRedactionSearchResult.propTypes = {\n  isChecked: PropTypes.bool,\n  onChange: PropTypes.func,\n  onClickResult: PropTypes.func,\n  isActive: PropTypes.bool,\n  icon: PropTypes.string,\n  ambientStr: PropTypes.string,\n};\n\nexport default React.memo(RedactionSearchResult);", "import RedactionSearchResultContainer from './RedactionSearchResultContainer';\n\nexport default RedactionSearchResultContainer;", "import React, { useCallback, useContext } from 'react';\nimport RedactionSearchResult from './RedactionSearchResult';\nimport { RedactionPanelContext } from 'components/RedactionPanel/RedactionPanelContext';\nimport core from 'core';\n\nconst RedactionSearchResultContainer = (props) => {\n  const {\n    searchResult,\n    checked,\n    checkResult,\n  } = props;\n\n  const { activeSearchResultIndex } = useContext(RedactionPanelContext);\n\n  const { ambientStr, resultStrStart, resultStrEnd, resultStr, icon, index, type } = searchResult;\n\n  const onChange = useCallback((event) => {\n    checkResult(event, index);\n  }, [index, checkResult]);\n\n  const onClickResult = useCallback(() => {\n    core.setActiveSearchResult(searchResult);\n  }, [searchResult]);\n\n  return (\n    <RedactionSearchResult\n      ambientStr={ambientStr}\n      resultStrStart={resultStrStart}\n      resultStrEnd={resultStrEnd}\n      resultStr={resultStr}\n      icon={icon}\n      type={type}\n      isChecked={checked}\n      onChange={onChange}\n      onClickResult={onClickResult}\n      isActive={activeSearchResultIndex === index}\n    />\n  );\n};\n\nexport default RedactionSearchResultContainer;\n", "import React, { useCallback, useEffect, useState } from 'react';\nimport RedactionSearchResult from './RedactionSearchResult';\nimport { Choice } from '@pdftron/webviewer-react-toolkit';\nimport { useTranslation } from 'react-i18next';\nimport CollapsibleSection from 'components/CollapsibleSection';\nimport './RedactionSearchResultGroup.scss';\n\nconst RedactionSearchResultGroup = (props) => {\n  const {\n    pageNumber,\n    searchResults,\n    selectedSearchResultIndexes,\n    setSelectedSearchResultIndexes,\n  } = props;\n\n  const { t } = useTranslation();\n  const groupResultIndexes = searchResults.map((result) => result.index);\n  const [allItemsChecked, setAllItemsChecked] = useState(false);\n\n  useEffect(() => {\n    const allResultsSelected = groupResultIndexes.reduce((allSelected, currentIndex) => {\n      return selectedSearchResultIndexes[currentIndex] && allSelected;\n    }, true);\n\n    setAllItemsChecked(allResultsSelected);\n  }, [selectedSearchResultIndexes, groupResultIndexes]);\n\n  const checkAllResults = useCallback((event) => {\n    const checked = event.target.checked;\n    groupResultIndexes.forEach((resultIndex) => {\n      selectedSearchResultIndexes[resultIndex] = checked;\n    });\n    setAllItemsChecked(checked);\n    setSelectedSearchResultIndexes({ ...selectedSearchResultIndexes });\n  }, [selectedSearchResultIndexes, groupResultIndexes]);\n\n  const checkResult = useCallback((event, index) => {\n    const checked = event.target.checked;\n    selectedSearchResultIndexes[index] = checked;\n    setSelectedSearchResultIndexes({ ...selectedSearchResultIndexes });\n  }, [selectedSearchResultIndexes]);\n\n  const header = () => {\n    return (\n      `${t('option.shared.page')} ${pageNumber}`\n    );\n  };\n\n  const style = {\n    width: '100%',\n  };\n\n  return (\n    <div className=\"redaction-search-results-page-number\">\n      <Choice\n        className=\"redaction-search-results-page-number-checkbox\"\n        aria-label={`${t('option.shared.page')} ${pageNumber}`}\n        checked={allItemsChecked}\n        onChange={(event) => {\n          event.stopPropagation();\n          checkAllResults(event);\n        }}\n      />\n      <CollapsibleSection header={header} style={style} expansionDescription={`${t('option.shared.page')} ${pageNumber}`}>\n        <div role=\"list\">\n          {searchResults.map((searchResult, index) => (\n            <RedactionSearchResult\n              checked={selectedSearchResultIndexes[searchResult.index]}\n              checkResult={checkResult}\n              searchResult={searchResult}\n              key={`${index}-${pageNumber}`}\n            />)\n          )}\n        </div>\n      </CollapsibleSection>\n    </div>\n  );\n};\n\nexport default RedactionSearchResultGroup;", "import RedactionSearchResultGroup from './RedactionSearchResultGroup';\n\nexport default RedactionSearchResultGroup;", "export default {\n  SEARCH_NOT_INITIATED: 'SEARCH_NOT_INITIATED',\n  SEARCH_IN_PROGRESS: 'SEARCH_IN_PROGRESS',\n  SEARCH_DONE: 'SEARCH_DONE',\n};", "import React, { useState, useEffect, useContext } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport RedactionSearchResultGroup from 'components/RedactionSearchResultGroup';\nimport Spinner from 'components/Spinner';\nimport './RedactionSearchResults.scss';\nimport classNames from 'classnames';\nimport { Virtuoso } from 'react-virtuoso';\nimport SearchStatus from 'constants/searchStatus';\nimport { RedactionPanelContext } from '../RedactionPanel/RedactionPanelContext';\nimport Button from 'components/Button';\n\nfunction RedactionSearchResults(props) {\n  const {\n    redactionSearchResults,\n    searchStatus,\n    onCancelSearch,\n    isProcessingRedactionResults,\n    markSelectedResultsForRedaction,\n    redactSelectedResults,\n  } = props;\n\n  const { t } = useTranslation();\n  const [redactionSearchResultPageMap, setRedactionSearchResultPageMap] = useState({});\n  const [selectedSearchResultIndexesMap, setSelectedSearchResultIndexesMap] = useState({});\n  const [selectedIndexes, setSelectedIndexes] = useState([]);\n  // The following prop is needed only for the tests to actually render a list of results\n  // it only is ever injected in the tests\n  const { isTestMode } = useContext(RedactionPanelContext);\n\n\n  useEffect(() => {\n    const redactionSearchResultPageMap = {};\n    redactionSearchResults.forEach((result, index) => {\n      const pageNumber = result.pageNum;\n      result.index = index;\n      if (redactionSearchResultPageMap[pageNumber] === undefined) {\n        redactionSearchResultPageMap[pageNumber] = [result];\n      } else {\n        redactionSearchResultPageMap[pageNumber] = [...redactionSearchResultPageMap[pageNumber], result];\n      }\n    });\n\n    setRedactionSearchResultPageMap(redactionSearchResultPageMap);\n\n    const selectedIndexesMap = {};\n    redactionSearchResults.forEach((value, index) => {\n      selectedIndexesMap[index] = false;\n    });\n    setSelectedSearchResultIndexesMap(selectedIndexesMap);\n  }, [redactionSearchResults]);\n\n\n  useEffect(() => {\n    const selectedIndexes = redactionSearchResults.filter((redactionSearchResult, index) => {\n      return selectedSearchResultIndexesMap[index];\n    });\n\n    setSelectedIndexes(selectedIndexes);\n  }, [selectedSearchResultIndexesMap]);\n\n\n  const renderSearchResults = () => {\n    const resultGroupPageNumbers = Object.keys(redactionSearchResultPageMap);\n    if (resultGroupPageNumbers.length > 0) {\n      // Needed for the tests to actually render a list of results\n      const testModeProps = isTestMode ? { initialItemCount: resultGroupPageNumbers.length } : {};\n      return (\n        <Virtuoso\n          data={resultGroupPageNumbers}\n          itemContent={(index, pageNumber) => {\n            return (\n              <RedactionSearchResultGroup\n                key={index}\n                pageNumber={pageNumber}\n                searchResults={redactionSearchResultPageMap[pageNumber]}\n                selectedSearchResultIndexes={selectedSearchResultIndexesMap}\n                setSelectedSearchResultIndexes={setSelectedSearchResultIndexesMap}\n              />);\n          }}\n          {...testModeProps}\n        />);\n    }\n  };\n\n  const renderStartSearch = () => (\n    <div aria-label={t('redactionPanel.search.start')}>\n      {t('redactionPanel.search.start')}\n    </div>\n  );\n\n  const noResults = (\n    <div aria-label={t('message.noResults')}>\n      <p aria-live=\"polite\" className=\"no-margin\">{t('message.noResults')}</p>\n    </div>\n  );\n\n  const renderSearchInProgress = () => (\n    <div >\n      <Spinner height=\"25px\" width=\"25px\" />\n    </div>\n  );\n\n  const onCancelHandler = () => {\n    setRedactionSearchResultPageMap({});\n    onCancelSearch();\n  };\n\n  const selectAllResults = () => {\n    const searchResultIndexMap = {};\n    redactionSearchResults.forEach((value, index) => {\n      searchResultIndexMap[index] = true;\n    });\n    setSelectedSearchResultIndexesMap(searchResultIndexMap);\n  };\n\n  const unselectAllResults = () => {\n    const searchResultIndexMap = {};\n    redactionSearchResults.forEach((value, index) => {\n      searchResultIndexMap[index] = false;\n    });\n    setSelectedSearchResultIndexesMap(searchResultIndexMap);\n  };\n\n  const onMarkAllForRedaction = () => {\n    markSelectedResultsForRedaction(selectedIndexes);\n    onCancelSearch();\n  };\n\n  const onRedactSelectedResults = () => {\n    redactSelectedResults(selectedIndexes);\n  };\n\n  const isEmptyList = redactionSearchResults.length === 0;\n\n  const resultsContainerClass = classNames('redaction-search-results-container', { emptyList: isEmptyList });\n  const redactAllButtonClass = classNames('redact-all-selected', { disabled: selectedIndexes.length === 0 });\n  const markAllForRedactionButtonClass = classNames('mark-all-selected', { disabled: selectedIndexes.length === 0 });\n  const shouldShowResultsCounterOptions = (searchStatus === SearchStatus['SEARCH_DONE'] && !isProcessingRedactionResults) || searchStatus === SearchStatus['SEARCH_NOT_INITIATED'];\n\n  return (\n    <>\n      <div className=\"redaction-search-counter-controls\">\n        {searchStatus === SearchStatus['SEARCH_IN_PROGRESS'] && (\n          <div style={{ flexGrow: 1 }}>\n            <Spinner height=\"18px\" width=\"18px\" />\n          </div>)}\n        {shouldShowResultsCounterOptions && (\n          <>\n            <div className=\"redaction-search-results-counter\">\n              <p aria-live=\"assertive\" className=\"no-margin\">\n                <span>{t('redactionPanel.searchResults')}</span> ({redactionSearchResults.length})\n              </p>\n            </div>\n            <Button\n              className={classNames({\n                'inactive': selectedIndexes.length < 1\n              })}\n              onClick={selectAllResults}\n              disabled={isEmptyList}\n              label={t('action.selectAll')}\n            >\n              {t('action.selectAll')}\n            </Button>\n            <Button\n              className={classNames({\n                'inactive': selectedIndexes.length < 1\n              })}\n              disabled={isEmptyList}\n              onClick={unselectAllResults}\n              label={t('action.unselect')}\n            >\n              {t('action.unselect')}\n            </Button>\n          </>)}\n      </div>\n      <div className={resultsContainerClass} role=\"list\">\n        {searchStatus === SearchStatus['SEARCH_NOT_INITIATED'] && renderStartSearch()}\n        {(searchStatus === SearchStatus['SEARCH_IN_PROGRESS'] && isEmptyList && isProcessingRedactionResults) && renderSearchInProgress()}\n        {searchStatus === SearchStatus['SEARCH_DONE'] && isEmptyList && !isProcessingRedactionResults && noResults}\n        {(searchStatus === SearchStatus['SEARCH_IN_PROGRESS'] || searchStatus === SearchStatus['SEARCH_DONE']) && renderSearchResults()}\n      </div>\n      <div className=\"redaction-search-panel-controls\" >\n        <Button\n          onClick={onCancelHandler}\n          label={t('action.cancel')}\n          className=\"cancel\"\n        >\n          {t('action.cancel')}\n        </Button>\n        <Button\n          disabled={selectedIndexes.length === 0}\n          label={t('annotation.redact')}\n          className={redactAllButtonClass}\n          onClick={onRedactSelectedResults}\n        >\n          {t('annotation.redact')}\n        </Button>\n        <Button\n          disabled={selectedIndexes.length === 0}\n          label={t('action.addMark')}\n          className={markAllForRedactionButtonClass}\n          onClick={onMarkAllForRedaction}\n        >\n          {t('action.addMark')}\n        </Button>\n      </div >\n    </>\n  );\n}\n\nexport default RedactionSearchResults;\n", "import React, { useCallback } from 'react';\nimport RedactionSearchResults from './RedactionSearchResults';\nimport { useDispatch, useSelector, shallowEqual } from 'react-redux';\nimport selectors from 'selectors';\nimport applyRedactions from 'helpers/applyRedactions';\nimport core from 'core';\n\nconst { ToolNames } = window.Core.Tools;\n\nexport const defaultRedactionStyles = {\n  OverlayText: '',\n  StrokeColor: new window.Core.Annotations.Color(255, 0, 0),\n  TextColor: new window.Core.Annotations.Color(255, 0, 0, 1),\n  Font: 'Helvetica',\n};\n\nexport function createRedactionAnnotations(searchResults, activeToolStyles = defaultRedactionStyles) {\n  const {\n    StrokeColor,\n    OverlayText,\n    FillColor,\n    Font = 'Helvetica',\n    TextColor,\n    FontSize,\n  } = activeToolStyles;\n  const redactionAnnotations = searchResults.map((result) => {\n    const redaction = new window.Core.Annotations.RedactionAnnotation();\n    redaction.PageNumber = result.page_num;\n    redaction.Quads = result.quads.map((quad) => quad.getPoints());\n    redaction.StrokeColor = StrokeColor;\n    redaction.OverlayText = OverlayText;\n    redaction.FillColor = FillColor;\n    redaction.Font = Font;\n    redaction.FontSize = FontSize;\n    redaction.TextColor = TextColor;\n    redaction.setContents(result.result_str);\n    redaction.type = result.type;\n    redaction.Author = core.getCurrentUser();\n\n    if (result.type === 'text') {\n      redaction.setCustomData('trn-annot-preview', result.result_str);\n    }\n\n    redaction.setCustomData('trn-redaction-type', result.type);\n\n    return redaction;\n  });\n\n  return redactionAnnotations;\n}\n\nfunction RedactionSearchResultsContainer(props) {\n  const { onCancelSearch } = props;\n  const dispatch = useDispatch();\n  // activeToolStyles is an object so we do a shallowEqual to check equality\n  const [activeToolStyles, activeToolName] = useSelector(\n    (state) => [\n      selectors.getActiveToolStyles(state),\n      selectors.getActiveToolName(state)\n    ], shallowEqual);\n\n  const redactSelectedResults = (searchResults) => {\n    const redactionAnnotations = createRedactionAnnotations(searchResults, defaultRedactionStyles);\n    dispatch(applyRedactions(redactionAnnotations, onCancelSearch));\n  };\n\n  const markSelectedResultsForRedaction = useCallback((searchResults) => {\n    const tool = core.getTool(ToolNames.REDACTION);\n    const alternativeDefaultStyles = (tool && tool.defaults) ? tool.defaults : defaultRedactionStyles;\n    const redactionStyles = activeToolName.includes('Redaction') ? activeToolStyles : alternativeDefaultStyles;\n    const redactionAnnotations = createRedactionAnnotations(searchResults, redactionStyles);\n    const annotationManager = core.getAnnotationManager();\n    annotationManager.addAnnotations(redactionAnnotations);\n  }, [activeToolStyles, activeToolName]);\n\n  return (\n    <RedactionSearchResults\n      markSelectedResultsForRedaction={markSelectedResultsForRedaction}\n      redactSelectedResults={redactSelectedResults}\n      {...props}\n    />);\n}\n\nexport default RedactionSearchResultsContainer;", "import RedactionSearchResultsContainer from './RedactionSearchResultsContainer';\n\nexport default RedactionSearchResultsContainer;", "import React, { useContext, useState } from 'react';\nimport { useDispatch } from 'react-redux';\nimport actions from 'actions';\nimport RedactionSearchOverlay from 'src/components/RedactionSearchOverlay';\nimport { RedactionPanelContext } from 'components/RedactionPanel/RedactionPanelContext';\nimport RedactionSearchResults from 'components/RedactionSearchResults';\nimport Icon from 'components/Icon';\nimport { isMobileSize } from 'helpers/getDeviceSize';\n\nconst RedactionSearchPanel = (props) => {\n  const dispatch = useDispatch();\n  const [searchTerms, setSearchTerms] = useState([]);\n  const { isRedactionSearchActive, setIsRedactionSearchActive } = useContext(RedactionPanelContext);\n  const onCancelSearch = () => {\n    setSearchTerms([]);\n    clearRedactionSearchResults();\n    setIsRedactionSearchActive(false);\n  };\n\n  const {\n    redactionSearchResults,\n    isProcessingRedactionResults,\n    clearRedactionSearchResults,\n    searchStatus,\n  } = props;\n\n  const isMobile = isMobileSize();\n\n  const onCloseButtonClick = () => {\n    dispatch(actions.closeElement('redactionPanel'));\n  };\n\n  return (\n    <>\n      {isMobile &&\n        <div\n          className=\"close-container\"\n        >\n          <button\n            className=\"close-icon-container\"\n            onClick={onCloseButtonClick}\n          >\n            <Icon\n              glyph=\"ic_close_black_24px\"\n              className=\"close-icon\"\n            />\n          </button>\n        </div>}\n      <RedactionSearchOverlay\n        searchTerms={searchTerms}\n        setSearchTerms={setSearchTerms}\n      />\n      {isRedactionSearchActive &&\n        <RedactionSearchResults\n          redactionSearchResults={redactionSearchResults}\n          onCancelSearch={onCancelSearch}\n          searchStatus={searchStatus}\n          isProcessingRedactionResults={isProcessingRedactionResults}\n        />\n      }\n    </>\n  );\n};\n\nexport default RedactionSearchPanel;", "import { useEffect, useState, useCallback, useMemo } from 'react';\nimport { useSelector, shallowEqual } from 'react-redux';\nimport selectors from 'selectors';\nimport core from 'core';\nimport { redactionTypeMap } from 'constants/redactionTypes';\nimport SearchStatus from 'constants/searchStatus';\n\nfunction useOnRedactionSearchCompleted() {\n  const [searchStatus, setSearchStatus] = useState(SearchStatus['SEARCH_NOT_INITIATED']);\n  const [redactionSearchResults, setRedactionSearchResults] = useState([]);\n  const [isProcessingRedactionResults, setIsProcessingRedactionResults] = useState(false);\n  const redactionSearchPatterns = useSelector((state) => selectors.getRedactionSearchPatterns(state), shallowEqual);\n\n  const searchPatterns = useMemo(() => {\n    return Object.keys(redactionSearchPatterns).reduce((map, key) => {\n      const { regex, type, icon } = redactionSearchPatterns[key];\n      map[type] = {\n        regex,\n        icon\n      };\n      return map;\n    }, {});\n  }, [redactionSearchPatterns]);\n\n  const mapResultToType = useCallback((result) => {\n    // Iterate through the patterns and return the first match\n    const { resultStr } = result;\n    const searchPatternKeys = Object.keys(searchPatterns);\n\n    const resultType = searchPatternKeys.find((key) => {\n      const { regex } = searchPatterns[key];\n      return regex.test(resultStr);\n    });\n\n    // If it didn't match any of the patterns, return the default type which is text\n    result.type = resultType === undefined ? redactionTypeMap['TEXT'] : resultType;\n    // And also set the icon to display in the panel. If no icon provided use the text icon\n    const { icon = 'icon-form-field-text' } = searchPatterns[result.type] || {};\n    result.icon = icon;\n    return result;\n  }, [searchPatterns]);// Dependency is an object but it is memoized so it will not re-create unless the patterns change\n\n  const clearRedactionSearchResults = useCallback(() => {\n    setRedactionSearchResults([]);\n    core.clearSearchResults();\n    setIsProcessingRedactionResults(false);\n  });\n\n  useEffect(() => {\n    function onSearchResultsChanged(results) {\n      const mappedResults = results.map(mapResultToType);\n      setIsProcessingRedactionResults(true);\n      setRedactionSearchResults(mappedResults);\n    }\n\n    core.addEventListener('searchResultsChanged', onSearchResultsChanged);\n    return () => {\n      core.removeEventListener('searchResultsChanged', onSearchResultsChanged);\n    };\n  }, [searchStatus]);\n\n  useEffect(() => {\n    function searchInProgressEventHandler(isSearching) {\n      if (isSearching === undefined || isSearching === null) {\n        // if isSearching is not passed at all, we consider that to mean that search was reset to original state\n        setSearchStatus(SearchStatus['SEARCH_NOT_INITIATED']);\n      } else if (isSearching) {\n        setSearchStatus(SearchStatus['SEARCH_IN_PROGRESS']);\n      } else {\n        setSearchStatus(SearchStatus['SEARCH_DONE']);\n        // Need a timeout due to timing issue as SEARCH_DONE is fired\n        // before final call to onSearchResultsChanged, otherwise we briefly show\n        // the NO RESULTS message before showing actual results.\n        setTimeout(() => {\n          setIsProcessingRedactionResults(false);\n        }, 100);\n      }\n    }\n\n    core.addEventListener('searchInProgress', searchInProgressEventHandler);\n\n    return () => {\n      core.removeEventListener('searchInProgress', searchInProgressEventHandler);\n    };\n  }, []);\n\n  return {\n    redactionSearchResults,\n    isProcessingRedactionResults,\n    clearRedactionSearchResults,\n    searchStatus,\n  };\n}\n\nexport default useOnRedactionSearchCompleted;", "import ReactionSearchPanelContainer from './RedactionSearchPanelContainer';\n\nexport default ReactionSearchPanelContainer;", "import React from 'react';\nimport RedactionSearchPanel from './RedactionSearchPanel';\nimport useOnRedactionSearchCompleted from 'hooks/useOnRedactionSearchCompleted';\n\nconst ReactionSearchPanelContainer = () => {\n  const {\n    redactionSearchResults,\n    isProcessingRedactionResults,\n    clearRedactionSearchResults,\n    searchStatus,\n  } = useOnRedactionSearchCompleted();\n\n  return (\n    <RedactionSearchPanel\n      redactionSearchResults={redactionSearchResults}\n      isProcessingRedactionResults={isProcessingRedactionResults}\n      clearRedactionSearchResults={clearRedactionSearchResults}\n      searchStatus={searchStatus}\n    />\n  );\n};\n\nexport default ReactionSearchPanelContainer;", "import React, { useContext, useEffect, useMemo, useState } from 'react';\nimport PropTypes from 'prop-types';\nimport RedactionPanel from './RedactionPanel';\nimport { useSelector, useDispatch, shallowEqual } from 'react-redux';\nimport selectors from 'selectors';\nimport actions from 'actions';\nimport core from 'core';\nimport applyRedactions from 'helpers/applyRedactions';\nimport { RedactionPanelContext, RedactionPanelProvider } from './RedactionPanelContext';\nimport { isMobileSize } from 'helpers/getDeviceSize';\nimport DataElementWrapper from '../DataElementWrapper';\nimport Icon from 'components/Icon';\nimport RedactionSearchPanel from 'components/RedactionSearchPanel';\nimport { defaultRedactionTypes } from 'constants/redactionTypes';\n\nexport const RedactionPanelContainer = (props) => {\n  const [\n    isOpen,\n    isDisabled,\n    redactionPanelWidth,\n    isInDesktopOnlyMode,\n    customApplyRedactionsHandler,\n    redactionSearchPatterns,\n  ] = useSelector(\n    (state) => [\n      selectors.isElementOpen(state, 'redactionPanel'),\n      selectors.isElementDisabled(state, 'redactionPanel'),\n      selectors.getRedactionPanelWidth(state),\n      selectors.isInDesktopOnlyMode(state),\n      selectors.getCustomApplyRedactionsHandler(state),\n      selectors.getRedactionSearchPatterns(state),\n    ],\n    shallowEqual,\n  );\n\n  const isMobile = isMobileSize();\n\n  const { redactionAnnotationsList, isCustomPanel, dataElement } = props;\n\n  const redactionTypesDictionary = useMemo(() => {\n    const storedRedactionTypes = Object.keys(redactionSearchPatterns).reduce((map, key) => {\n      const { label, type, icon } = redactionSearchPatterns[key];\n      map[type] = {\n        label,\n        icon,\n      };\n      return map;\n    }, {});\n\n    return { ...storedRedactionTypes, ...defaultRedactionTypes };\n  }, [redactionSearchPatterns]);\n\n  const deleteAllRedactionAnnotations = () => {\n    core.deleteAnnotations(redactionAnnotationsList);\n  };\n\n  const dispatch = useDispatch();\n  const applyAllRedactions = () => {\n    const originalApplyRedactions = () => {\n      const callOnRedactionCompleted = isCustomPanel ? closeRedactionPanel : () => {};\n      dispatch(applyRedactions(redactionAnnotationsList, callOnRedactionCompleted));\n    };\n    if (customApplyRedactionsHandler) {\n      customApplyRedactionsHandler(redactionAnnotationsList, originalApplyRedactions);\n    } else {\n      originalApplyRedactions();\n    }\n  };\n\n  const closeRedactionPanel = () => {\n    const tempDataElement = isCustomPanel ? dataElement : 'redactionPanel';\n    dispatch(actions.closeElement(tempDataElement));\n  };\n\n  const renderMobileCloseButton = () => {\n    return (\n      !isCustomPanel && (\n        <div className=\"close-container\">\n          <div className=\"close-icon-container\" onClick={closeRedactionPanel}>\n            <Icon glyph=\"ic_close_black_24px\" className=\"close-icon\" />\n          </div>\n        </div>\n      )\n    );\n  };\n\n  const style = isCustomPanel || (!isInDesktopOnlyMode && isMobile)\n    ? {}\n    : { width: `${redactionPanelWidth}px`, minWidth: `${redactionPanelWidth}px` };\n\n  const { isRedactionSearchActive } = useContext(RedactionPanelContext);\n\n  const [renderNull, setRenderNull] = useState(false);\n\n  useEffect(() => {\n    const timeout = setTimeout(() => {\n      setRenderNull(!isOpen);\n    }, 500);\n    return () => {\n      clearTimeout(timeout);\n    };\n  }, [isOpen]);\n\n  if (isDisabled || (!isOpen && renderNull && !isCustomPanel)) {\n    return null;\n  }\n\n  const dataElementToUse = isCustomPanel ? dataElement : 'redactionPanel';\n\n  return (\n    <DataElementWrapper dataElement={dataElementToUse} className=\"Panel RedactionPanel\" style={style}>\n      {!isInDesktopOnlyMode && isMobile && renderMobileCloseButton()}\n      <RedactionSearchPanel />\n      {!isRedactionSearchActive && (\n        <RedactionPanel\n          redactionAnnotations={redactionAnnotationsList}\n          redactionTypesDictionary={redactionTypesDictionary}\n          applyAllRedactions={applyAllRedactions}\n          deleteAllRedactionAnnotations={deleteAllRedactionAnnotations}\n        />\n      )}\n    </DataElementWrapper>\n  );\n};\n\nRedactionPanelContainer.propTypes = {\n  redactionAnnotationsList: PropTypes.array,\n  isCustomPanel: PropTypes.bool,\n  dataElement: PropTypes.string,\n};\n\nRedactionPanelContainer.defaultProps = {\n  isCustomPanel: false,\n  dataElement: '',\n};\n\nconst RedactionPanelContainerWithProvider = (props) => {\n  return (\n    <RedactionPanelProvider>\n      <RedactionPanelContainer {...props} />\n    </RedactionPanelProvider>\n  );\n};\n\nexport default RedactionPanelContainerWithProvider;\n", "import RedactionPanelContainerWithProvider from './RedactionPanelContainer';\n\nexport default RedactionPanelContainerWithProvider;", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../node_modules/css-loader/dist/runtime/api.js\");\nexports = ___CSS_LOADER_API_IMPORT___(false);\n// Module\nexports.push([module.id, \".creatable-multi-select-label{display:inline-block;font-weight:700;margin-bottom:var(--padding-small)}\", \"\"]);\n// Exports\nmodule.exports = exports;\n", "var api = require(\"!../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../node_modules/css-loader/dist/cjs.js!../../../node_modules/postcss-loader/src/index.js??ruleSet[1].rules[1].use[2]!../../../node_modules/sass-loader/dist/cjs.js!./RedactionSearchResults.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "var api = require(\"!../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../node_modules/css-loader/dist/cjs.js!../../../node_modules/postcss-loader/src/index.js??ruleSet[1].rules[1].use[2]!../../../node_modules/sass-loader/dist/cjs.js!./RedactionSearchResultGroup.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "var api = require(\"!../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../node_modules/css-loader/dist/cjs.js!../../../../node_modules/postcss-loader/src/index.js??ruleSet[1].rules[1].use[2]!../../../../node_modules/sass-loader/dist/cjs.js!./RedactionItem.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};"], "names": ["exports", "___CSS_LOADER_API_IMPORT___", "push", "module", "id", "EmotionNonceProvider", "_<PERSON>ce<PERSON><PERSON><PERSON>", "_this", "_classCallCheck", "_len", "arguments", "length", "args", "Array", "_key", "_callSuper", "concat", "nonce", "createCache", "container", "props", "_inherits", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref", "children", "window", "isApryseWebViewerWebComponent", "React", "getRootNode", "locals", "api", "content", "__esModule", "default", "options", "styleTag", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "shadowRoot", "clonedStyleTags", "i", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "noop", "annotations", "onRedactionCompleted", "undefined", "activeDocumentViewerKey", "dispatch", "core", "isWebViewerServerDocument", "webViewerServerApply", "webViewerApply", "applyRedactions", "then", "results", "url", "downloadPdf", "filename", "includeAnnotations", "externalURL", "console", "warn", "warning", "message", "i18next", "t", "title", "confirmBtnText", "onConfirm", "err", "fireError", "Promise", "resolve", "actions", "showWarningMessage", "RedactionPanelContext", "RedactionPanelProvider", "_useState2", "_slicedToArray", "useState", "selectedRedactionItemId", "setSelectedRedactionItemId", "_useState4", "isRedactionSearchActive", "setIsRedactionSearchActive", "_useState6", "activeSearchResultIndex", "setActiveSearchResultIndex", "useEffect", "onAnnotationSelected", "action", "redactionAnnotations", "filter", "annotation", "Subject", "selectedAnnotationId", "Id", "activeSearchResultChanged", "newActiveSearchResult", "newActiveSearchResultIndex", "getPageSearchResults", "findIndex", "searchResult", "isSearchResultEqual", "addEventListener", "removeEventListener", "value", "Provider", "redactionPanelWidth", "useSelector", "state", "selectors", "getRedactionPanelWidth", "shallowEqual", "NoteTextPreview", "_extends", "panelWidth", "comment", "RedactionItem", "isCustomUI", "_selectors$getFeature", "getFeatureFlags", "customizableUI", "iconColor", "author", "dateFormat", "language", "onRedactionItemDelete", "onRedactionItemSelection", "textPreview", "isSelected", "timezone", "useTranslation", "date", "getLatestActivityDate", "datetimeStr", "toLocaleString", "timeZone", "Date", "redactionPreview", "formattedDate", "dayjs", "locale", "format", "dateAndAuthor", "className", "classNames", "label", "_annotation$icon", "icon", "redactionType", "redactionTypeMap", "RedactionTextPreview", "linesToBreak", "getContents", "role", "onClick", "onKeyUp", "event", "key", "tabIndex", "Icon", "glyph", "color", "OverlayText", "<PERSON><PERSON>", "style", "marginLeft", "img", "aria<PERSON><PERSON><PERSON>", "_useContext", "useContext", "_useSelector2", "getNoteDateFormat", "getCurrentLanguage", "getCustomNoteSelectionFunction", "getTimezone", "customNoteSelectionFunction", "getCustomData", "StrokeColor", "toString", "getDisplayAuthor", "useCallback", "deselectAllAnnotations", "selectAnnotation", "jumpToAnnotation", "deleteAnnotations", "RedactionPageGroup", "pageNumber", "redactionItems", "CollapsibleSection", "header", "expansionDescription", "headingLevel", "map", "redactionItem", "propTypes", "PropTypes", "sortedRedactionItems", "setSortedRedactionItems", "getSortStrategies", "getSortedNotes", "applyAllRedactions", "deleteAllRedactionAnnotations", "redactionTypesDictionary", "redactionPageMap", "setRedactionPageMap", "redactionPageNumbers", "setRedactionPageNumbers", "isTestMode", "mapAnnotationToRedactionType", "PageNumber", "Object", "keys", "testModeProps", "noRedactionAnnotations", "redactAllButtonClassName", "disabled", "clearAllButtonClassName", "initialItemCount", "Virtuoso", "data", "itemContent", "index", "CreatableMultiSelect", "rest", "_objectWithoutProperties", "_excluded", "ReactSelectWebComponentProvider", "htmlFor", "CreatableSelect", "is<PERSON><PERSON><PERSON>", "inputId", "defaultProps", "getColorForMode", "isDarkMode", "darkModeColor", "lightModeColor", "RedactionOption", "components", "Option", "isRequired", "MultiValueLabel", "display", "height", "CustomControl", "_ref2", "Control", "RedactionSearchMultiSelect", "activeTheme", "redactionSearchOptions", "redactionGroup", "styles", "groupHeading", "base", "_objectSpread", "textTransform", "fontSize", "fontWeight", "COMMON_COLORS", "CUSTOM_UI_VARS", "paddingBottom", "paddingLeft", "paddingTop", "group", "padding", "menu", "borderRadius", "overflowY", "margin", "menuList", "backgroundColor", "multiValue", "whiteSpace", "multiValueRemove", "boxShadow", "width", "option", "noOptionsMessage", "valueContainer", "maxHeight", "isMobileSize", "control", "minHeight", "borderColor", "placeholder", "input", "IndicatorsContainer", "formatCreateLabel", "buildSearchOptions", "searchTerms", "textSearch", "caseSensitive", "searchTerm", "type", "regex", "ignoreCase", "setSearchTerms", "executeRedactionSearch", "DataElementWrapper", "dataElement", "onFocus", "onCreateOption", "newValue", "textTerm", "updatedSearchTerms", "onChange", "store", "getState", "redactionSearchPatterns", "getRedactionSearchPatterns", "searchOptionsMap", "reduce", "_redactionSearchPatte", "searchArray", "searchType", "searchRegex", "source", "searchString", "join", "searchTextFullFactory", "searchTextFull", "clearSearchResults", "useStore", "getActiveTheme", "values", "pattern", "RedactionSearchOverlay", "multiSearchFactory", "multiSearch", "RedactionSearchResult", "isChecked", "onClickResult", "isActive", "ambientStr", "displayResult", "resultStrStart", "resultStrEnd", "resultStr", "searchValue", "slice", "textBeforeSearchValue", "textAfterSearchValue", "displayRedactionSearchResult", "searchResultClassname", "active", "paddingRight", "Choice", "checked", "checkResult", "setActiveSearchResult", "searchResults", "selectedSearchResultIndexes", "setSelectedSearchResultIndexes", "groupResultIndexes", "result", "allItemsChecked", "setAllItemsChecked", "allResultsSelected", "allSelected", "currentIndex", "checkAllResults", "target", "resultIndex", "stopPropagation", "redactionSearchResults", "searchStatus", "onCancelSearch", "isProcessingRedactionResults", "markSelectedResultsForRedaction", "redactSelectedResults", "redactionSearchResultPageMap", "setRedactionSearchResultPageMap", "selectedSearchResultIndexesMap", "setSelectedSearchResultIndexesMap", "selectedIndexes", "setSelectedIndexes", "pageNum", "selectedIndexesMap", "redactionSearchResult", "noResults", "isEmptyList", "resultsContainerClass", "emptyList", "redactAllButtonClass", "markAllForRedactionButtonClass", "shouldShowResultsCounterOptions", "SearchStatus", "flexGrow", "Spinner", "searchResultIndexMap", "resultGroupPageNumbers", "RedactionSearchResultGroup", "renderSearchResults", "ToolNames", "Core", "Tools", "defaultRedactionStyles", "Annotations", "Color", "TextColor", "Font", "createRedactionAnnotations", "activeToolStyles", "FillColor", "_activeToolStyles$Fon", "FontSize", "redaction", "RedactionAnnotation", "page_num", "Quads", "quads", "quad", "getPoints", "setContents", "result_str", "Author", "getCurrentUser", "setCustomData", "useDispatch", "getActiveToolStyles", "getActiveToolName", "activeToolName", "tool", "getTool", "REDACTION", "alternativeDefaultStyles", "defaults", "includes", "getAnnotationManager", "addAnnotations", "RedactionSearchResults", "clearRedactionSearchResults", "isMobile", "closeElement", "_useOnRedactionSearch", "setSearchStatus", "setRedactionSearchResults", "setIsProcessingRedactionResults", "searchPatterns", "useMemo", "mapResultToType", "resultType", "find", "test", "_ref$icon", "onSearchResultsChanged", "mappedResults", "searchInProgressEventHandler", "isSearching", "setTimeout", "useOnRedactionSearchCompleted", "RedactionSearchPanel", "RedactionPanelContainer", "isElementOpen", "isElementDisabled", "isInDesktopOnlyMode", "getCustomApplyRedactionsHandler", "isOpen", "isDisabled", "customApplyRedactionsHandler", "redactionAnnotationsList", "isCustomPanel", "defaultRedactionTypes", "closeRedactionPanel", "tempDataElement", "min<PERSON><PERSON><PERSON>", "renderNull", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timeout", "clearTimeout", "dataElementToUse", "RedactionPanel", "originalApplyRedactions", "callOnRedactionCompleted"], "sourceRoot": ""}