/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[0],{571:function(xa,ta,h){h.r(ta);h.d(ta,"ByteRangeRequest",function(){return w});var qa=h(0),oa=h(1);h.n(oa);var na=h(3),ja=h(194);xa=h(121);var ka=h(327),ha=h(95),x=h(100),z=h(326),r=h(216);h=h(494);var n=[],f=[],b=window,a=function(){return function(){this.rq=1}}(),e;(function(ba){ba[ba.UNSENT=0]="UNSENT";ba[ba.DONE=4]="DONE"})(e||(e={}));var w=function(){function ba(fa,aa,ea,ca){var ma=this;this.url=fa;this.range=aa;this.Lf=
ea;this.withCredentials=ca;this.Wia=e;this.request=new XMLHttpRequest;this.request.open("GET",this.url,!0);b.Uint8Array&&(this.request.responseType="arraybuffer");ca&&(this.request.withCredentials=ca);y.DISABLE_RANGE_HEADER||(Object(oa.isUndefined)(aa.stop)?this.request.setRequestHeader("Range","bytes=".concat(aa.start)):this.request.setRequestHeader("Range",["bytes=",aa.start,"-",aa.stop-1].join("")));ea&&Object.keys(ea).forEach(function(la){ma.request.setRequestHeader(la,ea[la])});this.request.overrideMimeType?
this.request.overrideMimeType("text/plain; charset=x-user-defined"):this.request.setRequestHeader("Accept-Charset","x-user-defined");this.status=z.a.NOT_STARTED}ba.prototype.start=function(fa){var aa=this,ea=this.request;ea.onreadystatechange=function(){if(aa.aborted)return aa.status=z.a.ABORTED,fa({code:z.a.ABORTED});if(this.readyState===aa.Wia.DONE){aa.GK();var ca=0===window.document.URL.indexOf("file:///");200===ea.status||206===ea.status||ca&&0===ea.status?(ca=b.w7(this),aa.OY(ca,fa)):(aa.status=
z.a.ERROR,fa({code:aa.status,status:aa.status}))}};this.request.send(null);this.status=z.a.STARTED};ba.prototype.OY=function(fa,aa){this.status=z.a.SUCCESS;if(aa)return aa(!1,fa)};ba.prototype.abort=function(){this.GK();this.aborted=!0;this.request.abort()};ba.prototype.GK=function(){var fa=Object(r.c)(this.url,this.range,f);-1!==fa&&f.splice(fa,1);if(0<n.length){fa=n.shift();var aa=new ba(fa.url,fa.range,this.Lf,this.withCredentials);fa.request=aa;f.push(fa);aa.start(Object(r.d)(fa))}};ba.prototype.extend=
function(fa){var aa=Object.assign({},this,fa.prototype);aa.constructor=fa;return aa};return ba}(),y=function(ba){function fa(aa,ea,ca,ma,la){ca=ba.call(this,aa,ca,ma)||this;ca.dm={};ca.QI=ea;ca.url=aa;ca.DISABLE_RANGE_HEADER=!1;ca.XE=w;ca.g_=3;ca.Lf=la||{};return ca}Object(qa.c)(fa,ba);fa.prototype.gC=function(aa,ea,ca){var ma=-1===aa.indexOf("?")?"?":"&";switch(ca){case !1:case x.a.NEVER_CACHE:aa="".concat(aa+ma,"_=").concat(Object(oa.uniqueId)());break;case !0:case x.a.CACHE:aa="".concat(aa+ma,
"_=").concat(ea.start,",").concat(Object(oa.isUndefined)(ea.stop)?"":ea.stop)}return aa};fa.prototype.C4=function(aa,ea,ca,ma){void 0===ca&&(ca={});return new this.XE(aa,ea,ca,ma)};fa.prototype.hta=function(aa,ea,ca,ma,la){for(var ia=0;ia<n.length;ia++)if(Object(oa.isEqual)(n[ia].range,ea)&&Object(oa.isEqual)(n[ia].url,aa))return n[ia].bj.push(ma),n[ia].dM++,null;for(ia=0;ia<f.length;ia++)if(Object(oa.isEqual)(f[ia].range,ea)&&Object(oa.isEqual)(f[ia].url,aa))return f[ia].bj.push(ma),f[ia].dM++,null;
ca={url:aa,range:ea,QI:ca,bj:[ma],dM:1};if(0===n.length&&f.length<this.g_)return f.push(ca),ca.request=this.C4(aa,ea,la,this.withCredentials),ca;n.push(ca);return null};fa.prototype.us=function(aa,ea,ca){var ma=this.gC(aa,ea,this.QI);(aa=this.hta(ma,ea,this.QI,ca,this.Lf))&&aa.request.start(Object(r.d)(aa));return function(){var la=Object(r.c)(ma,ea,f);if(-1!==la){var ia=--f[la].dM;0===ia&&f[la].request&&f[la].request.abort()}else la=Object(r.c)(ma,ea,n),-1!==la&&(ia=--n[la].dM,0===ia&&n.splice(la,
1))}};fa.prototype.N6=function(){return{start:-ja.a}};fa.prototype.aya=function(){var aa=-(ja.a+ja.e);return{start:aa-ja.d,end:aa}};fa.prototype.Vy=function(aa){var ea=this;this.YI=!0;var ca=ja.a;this.us(this.url,this.N6(),function(ma,la,ia){function ra(){var pa=ea.Re.K6();ea.us(ea.url,pa,function(sa,ua){if(sa)return Object(na.j)("Error loading central directory: ".concat(sa)),aa(sa);ua=Object(ha.a)(ua);if(ua.length!==pa.stop-pa.start)return aa("Invalid XOD file: Zip central directory data is wrong size! Should be ".concat(pa.stop-
pa.start," but is ").concat(ua.length));ea.Re.nba(ua);ea.hR=!0;ea.YI=!1;return aa(!1)})}if(ma)return Object(na.j)("Error loading end header: ".concat(ma)),aa(ma,la,ia);la=Object(ha.a)(la);if(la.length!==ca)return aa("Invalid XOD file: Zip end header data is wrong size!");try{ea.Re=new ka.a(la)}catch(pa){return aa(pa)}ea.Re.yAa?ea.us(ea.url,ea.aya(),function(pa,sa){if(pa)return Object(na.j)("Error loading zip64 header: ".concat(pa)),aa(pa);sa=Object(ha.a)(sa);ea.Re.bBa(sa);ra()}):ra()})};fa.prototype.p7=
function(aa){aa(Object.keys(this.Re.Qr))};fa.prototype.YW=function(aa,ea){var ca=this;if(this.Re.j4(aa)){var ma=this.Re.YC(aa);if(ma in this.dm){var la=this.yj[aa];la.hx=this.dm[ma];la.hx.rq++;la.cancel=la.hx.cancel}else{var ia=this.Re.Gva(aa),ra=this.us(this.url,ia,function(sa,ua){sa?(Object(na.j)('Error loading part "'.concat(aa,'": ').concat(sa)),ca.us(ca.url,ia,function(wa,Ba){if(wa)return ea(wa,aa);ca.rba(Ba,ia,ma,aa,ea)})):ca.rba(ua,ia,ma,aa,ea)}),pa=this.yj[aa];pa&&(pa.pea=!0,pa.cancel=function(){pa.hx.rq--;
0===pa.hx.rq&&(ra(),delete ca.dm[ma])},this.dm[ma]=new a(ma),pa.hx=this.dm[ma],pa.hx.cancel=pa.cancel)}}else delete this.yj[aa],ea(Error('File not found: "'.concat(aa,'"')),aa)};fa.prototype.rba=function(aa,ea,ca,ma,la){if(aa.length!==ea.stop-ea.start)la(Error("Part data is wrong size!"),ma);else{do{if(!this.dm[ca])return;ma=this.dm[ca].rq;for(var ia=ea.vv.length,ra=0;ra<ia;++ra){var pa=ea.vv[ra];la(!1,pa.sv,aa["string"===typeof aa?"substring":"subarray"](pa.start,pa.stop),this.Re.O8(pa.sv));pa.sv in
this.yj&&delete this.yj[pa.sv]}}while(ma!==this.dm[ca].rq);delete this.dm[ca]}};fa.DISABLE_RANGE_HEADER=!1;fa.g_=3;return fa}(xa.a);(function(ba){function fa(aa,ea,ca){var ma=ba.call(this)||this,la;for(la in aa)ma[la]=aa[la];ma.WPa=aa;ma.startOffset=ea;ma.endOffset=ca;ma.C4=function(ia,ra,pa,sa){Object(oa.isUndefined)(ra.stop)?(ra.start+=ma.endOffset,ra.stop=ma.endOffset):(ra.start+=ma.startOffset,ra.stop+=ma.startOffset);ia=ma.gC(ma.url,ra,ma.QI);return new aa.XE(ia,ra,pa,sa)};return ma}Object(qa.c)(fa,
ba);return fa})(y);Object(h.a)(y);Object(h.b)(y);ta["default"]=y}}]);}).call(this || window)
