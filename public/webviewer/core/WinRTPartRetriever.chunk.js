/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[14],{581:function(xa,ta,h){h.r(ta);var qa=h(0),oa=h(326);xa=h(571);var na=h(121);h=h(494);var ja={},ka=function(ha){function x(z,r){var n=ha.call(this,z,r)||this;n.url=z;n.range=r;n.status=oa.a.NOT_STARTED;return n}Object(qa.c)(x,ha);x.prototype.start=function(z){var r=this;"undefined"===typeof ja[this.range.start]&&(ja[this.range.start]={OY:function(n){var f=atob(n),b,a=f.length;n=new Uint8Array(a);for(b=0;b<a;++b)n[b]=f.charCodeAt(b);
f=n.length;b="";for(var e=0;e<f;)a=n.subarray(e,e+1024),e+=1024,b+=String.fromCharCode.apply(null,a);r.OY(b,z)},TOa:function(){r.status=oa.a.ERROR;z({code:r.status})}},window.external.notify(this.url),this.status=oa.a.STARTED);r.GK()};return x}(xa.ByteRangeRequest);xa=function(ha){function x(z,r,n,f){z=ha.call(this,z,n,f)||this;z.XE=ka;return z}Object(qa.c)(x,ha);x.prototype.gC=function(z,r){return"".concat(z,"?").concat(r.start,"&").concat(r.stop?r.stop:"")};return x}(na.a);Object(h.a)(xa);Object(h.b)(xa);
ta["default"]=xa}}]);}).call(this || window)
