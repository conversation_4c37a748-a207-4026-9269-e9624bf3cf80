/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[7],{573:function(xa,ta,h){h.r(ta);var qa=h(0),oa=h(3),na=h(194);xa=h(121);var ja=h(327);h=h(494);var ka=window;xa=function(ha){function x(z,r,n){r=ha.call(this,z,r,n)||this;if(z.name&&"xod"!==z.name.toLowerCase().split(".").pop())throw Error("Not an XOD file");if(!ka.FileReader||!ka.File||!ka.Blob)throw Error("File API is not supported in this browser");r.file=z;r.bJ=[];r.DR=0;return r}Object(qa.c)(x,ha);x.prototype.zV=function(z,
r,n){var f=this,b=new FileReader;b.onloadend=function(a){if(0<f.bJ.length){var e=f.bJ.shift();e.EEa.readAsBinaryString(e.file)}else f.DR--;if(b.error){a=b.error;if(a.code===a.ABORT_ERR){Object(oa.j)("Request for chunk ".concat(r.start,"-").concat(r.stop," was aborted"));return}return n(a)}if(a=b.content||a.target.result)return n(!1,a);Object(oa.j)("No data was returned from FileReader.")};r&&(z=(z.slice||z.webkitSlice||z.mozSlice||z.UOa).call(z,r.start,r.stop));0===f.bJ.length&&50>f.DR?(b.readAsBinaryString(z),
f.DR++):f.bJ.push({EEa:b,file:z});return function(){b.abort()}};x.prototype.Vy=function(z){var r=this;r.YI=!0;var n=na.a;r.zV(r.file,{start:-n,stop:r.file.size},function(f,b){if(f)return Object(oa.j)("Error loading end header: %s ".concat(f)),z(f);if(b.length!==n)throw Error("Zip end header data is wrong size!");r.Re=new ja.a(b);var a=r.Re.K6();r.zV(r.file,a,function(e,w){if(e)return Object(oa.j)("Error loading central directory: %s ".concat(e)),z(e);if(w.length!==a.stop-a.start)throw Error("Zip central directory data is wrong size!");
r.Re.nba(w);r.hR=!0;r.YI=!1;return z(!1)})})};x.prototype.YW=function(z,r){var n=this,f=n.yj[z];if(n.Re.j4(z)){var b=n.Re.ZC(z),a=n.zV(n.file,b,function(e,w){delete n.yj[z];if(e)return Object(oa.j)('Error loading part "%s": %s, '.concat(z,", ").concat(e)),r(e);if(w.length!==b.stop-b.start)throw Error("Part data is wrong size!");r(!1,z,w,n.Re.O8(z))});f.pea=!0;f.cancel=a}else r(Error('File not found: "'.concat(z,'"')),z)};return x}(xa.a);Object(h.a)(xa);Object(h.b)(xa);ta["default"]=xa}}]);}).call(this || window)
