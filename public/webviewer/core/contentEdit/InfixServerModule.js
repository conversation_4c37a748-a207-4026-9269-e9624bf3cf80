(function(){/*
 The buffer module from node.js, for the browser.

 <AUTHOR> <http://feross.org>
 @license  MIT
*/
var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.arrayIteratorImpl=function(q){var m=0;return function(){return m<q.length?{done:!1,value:q[m++]}:{done:!0}}};$jscomp.arrayIterator=function(q){return{next:$jscomp.arrayIteratorImpl(q)}};$jscomp.makeIterator=function(q){var m="undefined"!=typeof Symbol&&Symbol.iterator&&q[Symbol.iterator];if(m)return m.call(q);if("number"==typeof q.length)return $jscomp.arrayIterator(q);throw Error(String(q)+" is not an iterable or ArrayLike");};$jscomp.ASSUME_ES5=!1;
$jscomp.ASSUME_NO_NATIVE_MAP=!1;$jscomp.ASSUME_NO_NATIVE_SET=!1;$jscomp.SIMPLE_FROUND_POLYFILL=!1;$jscomp.ISOLATE_POLYFILLS=!1;$jscomp.FORCE_POLYFILL_PROMISE=!1;$jscomp.FORCE_POLYFILL_PROMISE_WHEN_NO_UNHANDLED_REJECTION=!1;$jscomp.getGlobal=function(q){q=["object"==typeof globalThis&&globalThis,q,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var m=0;m<q.length;++m){var l=q[m];if(l&&l.Math==Math)return l}throw Error("Cannot find global object");};
$jscomp.global=$jscomp.getGlobal(this);$jscomp.defineProperty=$jscomp.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(q,m,l){if(q==Array.prototype||q==Object.prototype)return q;q[m]=l.value;return q};$jscomp.IS_SYMBOL_NATIVE="function"===typeof Symbol&&"symbol"===typeof Symbol("x");$jscomp.TRUST_ES6_POLYFILLS=!$jscomp.ISOLATE_POLYFILLS||$jscomp.IS_SYMBOL_NATIVE;$jscomp.polyfills={};$jscomp.propertyToPolyfillSymbol={};$jscomp.POLYFILL_PREFIX="$jscp$";
var $jscomp$lookupPolyfilledValue=function(q,m,l){if(!l||null!=q){l=$jscomp.propertyToPolyfillSymbol[m];if(null==l)return q[m];l=q[l];return void 0!==l?l:q[m]}};$jscomp.polyfill=function(q,m,l,k){m&&($jscomp.ISOLATE_POLYFILLS?$jscomp.polyfillIsolated(q,m,l,k):$jscomp.polyfillUnisolated(q,m,l,k))};
$jscomp.polyfillUnisolated=function(q,m,l,k){l=$jscomp.global;q=q.split(".");for(k=0;k<q.length-1;k++){var g=q[k];if(!(g in l))return;l=l[g]}q=q[q.length-1];k=l[q];m=m(k);m!=k&&null!=m&&$jscomp.defineProperty(l,q,{configurable:!0,writable:!0,value:m})};
$jscomp.polyfillIsolated=function(q,m,l,k){var g=q.split(".");q=1===g.length;k=g[0];k=!q&&k in $jscomp.polyfills?$jscomp.polyfills:$jscomp.global;for(var p=0;p<g.length-1;p++){var d=g[p];if(!(d in k))return;k=k[d]}g=g[g.length-1];l=$jscomp.IS_SYMBOL_NATIVE&&"es6"===l?k[g]:null;m=m(l);null!=m&&(q?$jscomp.defineProperty($jscomp.polyfills,g,{configurable:!0,writable:!0,value:m}):m!==l&&(void 0===$jscomp.propertyToPolyfillSymbol[g]&&(l=1E9*Math.random()>>>0,$jscomp.propertyToPolyfillSymbol[g]=$jscomp.IS_SYMBOL_NATIVE?
$jscomp.global.Symbol(g):$jscomp.POLYFILL_PREFIX+l+"$"+g),$jscomp.defineProperty(k,$jscomp.propertyToPolyfillSymbol[g],{configurable:!0,writable:!0,value:m})))};
$jscomp.polyfill("Promise",function(q){function m(){this.batch_=null}function l(d){return d instanceof g?d:new g(function(h,r){h(d)})}if(q&&(!($jscomp.FORCE_POLYFILL_PROMISE||$jscomp.FORCE_POLYFILL_PROMISE_WHEN_NO_UNHANDLED_REJECTION&&"undefined"===typeof $jscomp.global.PromiseRejectionEvent)||!$jscomp.global.Promise||-1===$jscomp.global.Promise.toString().indexOf("[native code]")))return q;m.prototype.asyncExecute=function(d){if(null==this.batch_){this.batch_=[];var h=this;this.asyncExecuteFunction(function(){h.executeBatch_()})}this.batch_.push(d)};
var k=$jscomp.global.setTimeout;m.prototype.asyncExecuteFunction=function(d){k(d,0)};m.prototype.executeBatch_=function(){for(;this.batch_&&this.batch_.length;){var d=this.batch_;this.batch_=[];for(var h=0;h<d.length;++h){var r=d[h];d[h]=null;try{r()}catch(t){this.asyncThrow_(t)}}}this.batch_=null};m.prototype.asyncThrow_=function(d){this.asyncExecuteFunction(function(){throw d;})};var g=function(d){this.state_=0;this.result_=void 0;this.onSettledCallbacks_=[];this.isRejectionHandled_=!1;var h=this.createResolveAndReject_();
try{d(h.resolve,h.reject)}catch(r){h.reject(r)}};g.prototype.createResolveAndReject_=function(){function d(t){return function(w){r||(r=!0,t.call(h,w))}}var h=this,r=!1;return{resolve:d(this.resolveTo_),reject:d(this.reject_)}};g.prototype.resolveTo_=function(d){if(d===this)this.reject_(new TypeError("A Promise cannot resolve to itself"));else if(d instanceof g)this.settleSameAsPromise_(d);else{a:switch(typeof d){case "object":var h=null!=d;break a;case "function":h=!0;break a;default:h=!1}h?this.resolveToNonPromiseObj_(d):
this.fulfill_(d)}};g.prototype.resolveToNonPromiseObj_=function(d){var h=void 0;try{h=d.then}catch(r){this.reject_(r);return}"function"==typeof h?this.settleSameAsThenable_(h,d):this.fulfill_(d)};g.prototype.reject_=function(d){this.settle_(2,d)};g.prototype.fulfill_=function(d){this.settle_(1,d)};g.prototype.settle_=function(d,h){if(0!=this.state_)throw Error("Cannot settle("+d+", "+h+"): Promise already settled in state"+this.state_);this.state_=d;this.result_=h;2===this.state_&&this.scheduleUnhandledRejectionCheck_();
this.executeOnSettledCallbacks_()};g.prototype.scheduleUnhandledRejectionCheck_=function(){var d=this;k(function(){if(d.notifyUnhandledRejection_()){var h=$jscomp.global.console;"undefined"!==typeof h&&h.error(d.result_)}},1)};g.prototype.notifyUnhandledRejection_=function(){if(this.isRejectionHandled_)return!1;var d=$jscomp.global.CustomEvent,h=$jscomp.global.Event,r=$jscomp.global.dispatchEvent;if("undefined"===typeof r)return!0;"function"===typeof d?d=new d("unhandledrejection",{cancelable:!0}):
"function"===typeof h?d=new h("unhandledrejection",{cancelable:!0}):(d=$jscomp.global.document.createEvent("CustomEvent"),d.initCustomEvent("unhandledrejection",!1,!0,d));d.promise=this;d.reason=this.result_;return r(d)};g.prototype.executeOnSettledCallbacks_=function(){if(null!=this.onSettledCallbacks_){for(var d=0;d<this.onSettledCallbacks_.length;++d)p.asyncExecute(this.onSettledCallbacks_[d]);this.onSettledCallbacks_=null}};var p=new m;g.prototype.settleSameAsPromise_=function(d){var h=this.createResolveAndReject_();
d.callWhenSettled_(h.resolve,h.reject)};g.prototype.settleSameAsThenable_=function(d,h){var r=this.createResolveAndReject_();try{d.call(h,r.resolve,r.reject)}catch(t){r.reject(t)}};g.prototype.then=function(d,h){function r(x,E){return"function"==typeof x?function(B){try{t(x(B))}catch(A){w(A)}}:E}var t,w,z=new g(function(x,E){t=x;w=E});this.callWhenSettled_(r(d,t),r(h,w));return z};g.prototype.catch=function(d){return this.then(void 0,d)};g.prototype.callWhenSettled_=function(d,h){function r(){switch(t.state_){case 1:d(t.result_);
break;case 2:h(t.result_);break;default:throw Error("Unexpected state: "+t.state_);}}var t=this;null==this.onSettledCallbacks_?p.asyncExecute(r):this.onSettledCallbacks_.push(r);this.isRejectionHandled_=!0};g.resolve=l;g.reject=function(d){return new g(function(h,r){r(d)})};g.race=function(d){return new g(function(h,r){for(var t=$jscomp.makeIterator(d),w=t.next();!w.done;w=t.next())l(w.value).callWhenSettled_(h,r)})};g.all=function(d){var h=$jscomp.makeIterator(d),r=h.next();return r.done?l([]):new g(function(t,
w){function z(B){return function(A){x[B]=A;E--;0==E&&t(x)}}var x=[],E=0;do x.push(void 0),E++,l(r.value).callWhenSettled_(z(x.length-1),w),r=h.next();while(!r.done)})};return g},"es6","es3");$jscomp.checkStringArgs=function(q,m,l){if(null==q)throw new TypeError("The 'this' value for String.prototype."+l+" must not be null or undefined");if(m instanceof RegExp)throw new TypeError("First argument to String.prototype."+l+" must not be a regular expression");return q+""};
$jscomp.polyfill("String.prototype.endsWith",function(q){return q?q:function(m,l){var k=$jscomp.checkStringArgs(this,m,"endsWith");m+="";void 0===l&&(l=k.length);l=Math.max(0,Math.min(l|0,k.length));for(var g=m.length;0<g&&0<l;)if(k[--l]!=m[--g])return!1;return 0>=g}},"es6","es3");$jscomp.initSymbol=function(){};
$jscomp.polyfill("Symbol",function(q){if(q)return q;var m=function(p,d){this.$jscomp$symbol$id_=p;$jscomp.defineProperty(this,"description",{configurable:!0,writable:!0,value:d})};m.prototype.toString=function(){return this.$jscomp$symbol$id_};var l="jscomp_symbol_"+(1E9*Math.random()>>>0)+"_",k=0,g=function(p){if(this instanceof g)throw new TypeError("Symbol is not a constructor");return new m(l+(p||"")+"_"+k++,p)};return g},"es6","es3");
$jscomp.polyfill("Symbol.iterator",function(q){if(q)return q;q=Symbol("Symbol.iterator");for(var m="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),l=0;l<m.length;l++){var k=$jscomp.global[m[l]];"function"===typeof k&&"function"!=typeof k.prototype[q]&&$jscomp.defineProperty(k.prototype,q,{configurable:!0,writable:!0,value:function(){return $jscomp.iteratorPrototype($jscomp.arrayIteratorImpl(this))}})}return q},"es6",
"es3");$jscomp.iteratorPrototype=function(q){q={next:q};q[Symbol.iterator]=function(){return this};return q};$jscomp.checkEs6ConformanceViaProxy=function(){try{var q={},m=Object.create(new $jscomp.global.Proxy(q,{get:function(l,k,g){return l==q&&"q"==k&&g==m}}));return!0===m.q}catch(l){return!1}};$jscomp.USE_PROXY_FOR_ES6_CONFORMANCE_CHECKS=!1;$jscomp.ES6_CONFORMANCE=$jscomp.USE_PROXY_FOR_ES6_CONFORMANCE_CHECKS&&$jscomp.checkEs6ConformanceViaProxy();
$jscomp.owns=function(q,m){return Object.prototype.hasOwnProperty.call(q,m)};$jscomp.MapEntry=function(){};$jscomp.underscoreProtoCanBeSet=function(){var q={a:!0},m={};try{return m.__proto__=q,m.a}catch(l){}return!1};$jscomp.setPrototypeOf=$jscomp.TRUST_ES6_POLYFILLS&&"function"==typeof Object.setPrototypeOf?Object.setPrototypeOf:$jscomp.underscoreProtoCanBeSet()?function(q,m){q.__proto__=m;if(q.__proto__!==m)throw new TypeError(q+" is not extensible");return q}:null;
$jscomp.assign=$jscomp.TRUST_ES6_POLYFILLS&&"function"==typeof Object.assign?Object.assign:function(q,m){for(var l=1;l<arguments.length;l++){var k=arguments[l];if(k)for(var g in k)$jscomp.owns(k,g)&&(q[g]=k[g])}return q};$jscomp.polyfill("Array.prototype.fill",function(q){return q?q:function(m,l,k){var g=this.length||0;0>l&&(l=Math.max(0,g+l));if(null==k||k>g)k=g;k=Number(k);0>k&&(k=Math.max(0,g+k));for(l=Number(l||0);l<k;l++)this[l]=m;return this}},"es6","es3");
$jscomp.typedArrayFill=function(q){return q?q:Array.prototype.fill};$jscomp.polyfill("Int8Array.prototype.fill",$jscomp.typedArrayFill,"es6","es5");$jscomp.polyfill("Uint8Array.prototype.fill",$jscomp.typedArrayFill,"es6","es5");$jscomp.polyfill("Uint8ClampedArray.prototype.fill",$jscomp.typedArrayFill,"es6","es5");$jscomp.polyfill("Int16Array.prototype.fill",$jscomp.typedArrayFill,"es6","es5");$jscomp.polyfill("Uint16Array.prototype.fill",$jscomp.typedArrayFill,"es6","es5");
$jscomp.polyfill("Int32Array.prototype.fill",$jscomp.typedArrayFill,"es6","es5");$jscomp.polyfill("Uint32Array.prototype.fill",$jscomp.typedArrayFill,"es6","es5");$jscomp.polyfill("Float32Array.prototype.fill",$jscomp.typedArrayFill,"es6","es5");$jscomp.polyfill("Float64Array.prototype.fill",$jscomp.typedArrayFill,"es6","es5");
(function(q){function m(k){if(l[k])return l[k].exports;var g=l[k]={i:k,l:!1,exports:{}};q[k].call(g.exports,g,g.exports,m);g.l=!0;return g.exports}var l={};m.m=q;m.c=l;m.d=function(k,g,p){m.o(k,g)||Object.defineProperty(k,g,{enumerable:!0,get:p})};m.r=function(k){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(k,Symbol.toStringTag,{value:"Module"});Object.defineProperty(k,"__esModule",{value:!0})};m.t=function(k,g){g&1&&(k=m(k));if(g&8||g&4&&"object"===typeof k&&k&&k.__esModule)return k;
var p=Object.create(null);m.r(p);Object.defineProperty(p,"default",{enumerable:!0,value:k});if(g&2&&"string"!=typeof k)for(var d in k)m.d(p,d,function(h){return k[h]}.bind(null,d));return p};m.n=function(k){var g=k&&k.__esModule?function(){return k["default"]}:function(){return k};m.d(g,"a",g);return g};m.o=function(k,g){return Object.prototype.hasOwnProperty.call(k,g)};m.p="/core/contentEdit";return m(m.s=9)})([function(q,m,l){l.d(m,"b",function(){return g});l.d(m,"a",function(){return p});var k=
l(2),g=function(d,h){Object(k.a)("disableLogs")||(h?console.warn("".concat(d,": ").concat(h)):console.warn(d))},p=function(d,h){}},function(q,m,l){l.d(m,"a",function(){return L});l.d(m,"b",function(){return y});l.d(m,"c",function(){return P});var k=l(6),g=l(0),p=l(4),d=l(3),h="undefined"===typeof window?self:window,r=h.importScripts,t=!1,w=function(e,u){t||(r("".concat(h.basePath,"decode.min.js")),t=!0);e=self.BrotliDecode(Object(d.b)(e));return u?e:Object(d.a)(e)},z=function(e,u){return Object(k.a)(void 0,
void 0,Promise,function(){var v;return Object(k.b)(this,function(D){switch(D.label){case 0:return t?[3,2]:[4,Object(p.a)("".concat(self.Core.getWorkerPath(),"external/decode.min.js"),"Failed to download decode.min.js",window)];case 1:D.sent(),t=!0,D.label=2;case 2:return v=self.BrotliDecode(Object(d.b)(e)),[2,u?v:Object(d.a)(v)]}})})};(function(){function e(){this.remainingDataArrays=[]}e.prototype.processRaw=function(u){return u};e.prototype.processBrotli=function(u){this.remainingDataArrays.push(u);
return null};e.prototype.GetNextChunk=function(u){this.decodeFunction||(this.decodeFunction=0===u[0]&&97===u[1]&&115===u[2]&&109===u[3]?this.processRaw:this.processBrotli);return this.decodeFunction(u)};e.prototype.End=function(){if(this.remainingDataArrays.length){for(var u=this.arrays,v=0,D=0;D<u.length;++D)v+=u[D].length;v=new Uint8Array(v);var H=0;for(D=0;D<u.length;++D){var K=u[D];v.set(K,H);H+=K.length}return w(v,!0)}return null};return e})();var x=!1,E=function(e){x||(r("".concat(h.basePath,
"pako_inflate.min.js")),x=!0);var u=10;if("string"===typeof e){if(e.charCodeAt(3)&8){for(;0!==e.charCodeAt(u);++u);++u}}else if(e[3]&8){for(;0!==e[u];++u);++u}e=Object(d.b)(e);e=e.subarray(u,e.length-8);return h.pako.inflate(e,{windowBits:-15})},B=function(e,u){return u?e:Object(d.a)(e)},A=function(e){var u=!e.shouldOutputArray,v=new XMLHttpRequest;v.open("GET",e.url,e.isAsync);var D=u&&v.overrideMimeType;v.responseType=D?"text":"arraybuffer";D&&v.overrideMimeType("text/plain; charset=x-user-defined");
v.send();var H=function(){var N=Date.now();var I=D?v.responseText:new Uint8Array(v.response);Object(g.a)("worker","Result length is ".concat(I.length));I.length<e.compressedMaximum?(I=e.decompressFunction(I,e.shouldOutputArray),Object(g.b)("There may be some degradation of performance. Your server has not been configured to serve .gz. and .br. files with the expected Content-Encoding. See https://docs.apryse.com/documentation/web/faq/content-encoding/ for instructions on how to resolve this."),r&&
Object(g.a)("worker","Decompressed length is ".concat(I.length))):u&&(I=Object(d.a)(I));r&&Object(g.a)("worker","".concat(e.url," Decompression took ").concat(Date.now()-N));return I};if(e.isAsync)var K=new Promise(function(N,I){v.onload=function(){200===this.status||0===this.status?N(H()):I("Download Failed ".concat(e.url))};v.onerror=function(){I("Network error occurred ".concat(e.url))}});else{if(200===v.status||0===v.status)return H();throw Error("Failed to load ".concat(e.url));}return K},L=
function(e){var u=e.lastIndexOf("/");-1===u&&(u=0);var v=e.slice(u).replace(".",".br.");r||(v.endsWith(".js.mem")?v=v.replace(".js.mem",".mem"):v.endsWith(".js")&&(v=v.concat(".mem")));return e.slice(0,u)+v},Q=function(e,u){var v=e.lastIndexOf("/");-1===v&&(v=0);var D=e.slice(v).replace(".",".gz.");u.url=e.slice(0,v)+D;u.decompressFunction=E;return A(u)},R=function(e,u){u.url=L(e);u.decompressFunction=r?w:z;return A(u)},F=function(e,u){e.endsWith(".js.mem")?e=e.slice(0,-4):e.endsWith(".mem")&&(e=
"".concat(e.slice(0,-4),".js.mem"));u.url=e;u.decompressFunction=B;return A(u)},M=function(e,u,v,D){return e.catch(function(H){Object(g.b)(H);return D(u,v)})},S=function(e,u,v){var D;if(v.isAsync){var H=u[0](e,v);for(D=1;D<u.length;++D)H=M(H,e,v,u[D]);return H}for(D=0;D<u.length;++D)try{return u[D](e,v)}catch(K){Object(g.b)(K.message)}throw Error("");},P=function(e,u,v,D){return S(e,[Q,R,F],{compressedMaximum:u,isAsync:v,shouldOutputArray:D})},y=function(e,u,v,D){return S(e,[R,Q,F],{compressedMaximum:u,
isAsync:v,shouldOutputArray:D})}},function(q,m,l){l.d(m,"a",function(){return p});l.d(m,"b",function(){return d});var k={},g={flattenedResources:!1,CANVAS_CACHE_SIZE:void 0,maxPagesBefore:void 0,maxPagesAhead:void 0,disableLogs:!1,wvsQueryParameters:{},_trnDebugMode:!1,_logFiltersEnabled:null},p=function(h){return g[h]},d=function(h,r){var t;g[h]=r;null===(t=k[h])||void 0===t?void 0:t.forEach(function(w){w(r)})}},function(q,m,l){l.d(m,"b",function(){return k});l.d(m,"a",function(){return g});var k=
function(p){if("string"===typeof p){for(var d=new Uint8Array(p.length),h=p.length,r=0;r<h;r++)d[r]=p.charCodeAt(r);return d}return p},g=function(p){if("string"!==typeof p){for(var d="",h=0,r=p.length,t;h<r;)t=p.subarray(h,h+1024),h+=1024,d+=String.fromCharCode.apply(null,t);return d}return p}},function(q,m,l){function k(p,d,h){return new Promise(function(r){if(!p)return r();var t=h.document.createElement("script");t.type="text/javascript";t.onload=function(){r()};t.onerror=function(){d&&Object(g.b)(d);
r()};t.src=p;h.document.getElementsByTagName("head")[0].appendChild(t)})}l.d(m,"a",function(){return k});var g=l(0)},function(q,m,l){function k(h,r,t){function w(E){x=x||Date.now();return E?(Object(g.a)("load","Try instantiateStreaming"),fetch(Object(p.a)(h)).then(function(B){return WebAssembly.instantiateStreaming(B,r)}).catch(function(B){Object(g.a)("load","instantiateStreaming Failed ".concat(h," message ").concat(B.message));return w(!1)})):Object(p.b)(h,t,!0,!0).then(function(B){z=Date.now();
Object(g.a)("load","Request took ".concat(z-x," ms"));return WebAssembly.instantiate(B,r)})}var z,x;return w(!!WebAssembly.instantiateStreaming).then(function(E){Object(g.a)("load","WASM compilation took ".concat(Date.now()-(z||x)," ms"));return E})}l.d(m,"a",function(){return k});var g=l(0),p=l(1),d=l(4);l.d(m,"b",function(){return d.a})},function(q,m,l){function k(p,d,h,r){function t(w){return w instanceof h?w:new h(function(z){z(w)})}return new (h||(h=Promise))(function(w,z){function x(A){try{B(r.next(A))}catch(L){z(L)}}
function E(A){try{B(r["throw"](A))}catch(L){z(L)}}function B(A){A.done?w(A.value):t(A.value).then(x,E)}B((r=r.apply(p,d||[])).next())})}function g(p,d){function h(B){return function(A){return r([B,A])}}function r(B){if(w)throw new TypeError("Generator is already executing.");for(;E&&(E=0,B[0]&&(t=0)),t;)try{if(w=1,z&&(x=B[0]&2?z["return"]:B[0]?z["throw"]||((x=z["return"])&&x.call(z),0):z.next)&&!(x=x.call(z,B[1])).done)return x;if(z=0,x)B=[B[0]&2,x.value];switch(B[0]){case 0:case 1:x=B;break;case 4:return t.label++,
{value:B[1],done:!1};case 5:t.label++;z=B[1];B=[0];continue;case 7:B=t.ops.pop();t.trys.pop();continue;default:if(!(x=t.trys,x=0<x.length&&x[x.length-1])&&(6===B[0]||2===B[0])){t=0;continue}if(3===B[0]&&(!x||B[1]>x[0]&&B[1]<x[3]))t.label=B[1];else if(6===B[0]&&t.label<x[1])t.label=x[1],x=B;else if(x&&t.label<x[2])t.label=x[2],t.ops.push(B);else{x[2]&&t.ops.pop();t.trys.pop();continue}}B=d.call(p,t)}catch(A){B=[6,A],z=0}finally{w=x=0}if(B[0]&5)throw B[1];return{value:B[0]?B[1]:void 0,done:!0}}var t=
{label:0,sent:function(){if(x[0]&1)throw x[1];return x[1]},trys:[],ops:[]},w,z,x,E;return E={next:h(0),"throw":h(1),"return":h(2)},"function"===typeof Symbol&&(E[Symbol.iterator]=function(){return this}),E}l.d(m,"a",function(){return k});l.d(m,"b",function(){return g})},function(q,m,l){l.d(m,"a",function(){return h});var k=l(1),g=l(5),p=l(8),d=function(){function r(t){var w=this;this.promise=t.then(function(z){w.response=z;w.status=200})}r.prototype.addEventListener=function(t,w){this.promise.then(w)};
return r}(),h=function(r,t,w){if(Object(p.a)()&&!w){self.Module.instantiateWasm=function(x,E){return Object(g.a)("".concat(r,"Wasm.wasm"),x,t["Wasm.wasm"]).then(function(B){E(B.instance)})};if(t.disableObjectURLBlobs){importScripts("".concat(r,"Wasm.js"));return}w=Object(k.b)("".concat(r,"Wasm.js.mem"),t["Wasm.js.mem"],!1,!1)}else{if(t.disableObjectURLBlobs){importScripts("".concat((self.Module.asmjsPrefix?self.Module.asmjsPrefix:"")+r,".js"));return}w=Object(k.b)("".concat((self.Module.asmjsPrefix?
self.Module.asmjsPrefix:"")+r,".js.mem"),t[".js.mem"],!1);var z=Object(k.c)("".concat((self.Module.memoryInitializerPrefixURL?self.Module.memoryInitializerPrefixURL:"")+r,".mem"),t[".mem"],!0,!0);self.Module.memoryInitializerRequest=new d(z)}w=new Blob([w],{type:"application/javascript"});importScripts(URL.createObjectURL(w))}},function(q,m,l){l.d(m,"a",function(){return B});var k,g="undefined"===typeof window?self:window;q=function(){var A=navigator.userAgent.toLowerCase();return(A=/(msie) ([\w.]+)/.exec(A)||
/(trident)(?:.*? rv:([\w.]+)|)/.exec(A))?parseInt(A[2],10):A}();var p=function(){var A=g.navigator.userAgent.match(/OPR/),L=g.navigator.userAgent.match(/Maxthon/),Q=g.navigator.userAgent.match(/Edge/);return g.navigator.userAgent.match(/Chrome\/(.*?) /)&&!A&&!L&&!Q}();(function(){if(!p)return null;var A=g.navigator.userAgent.match(/Chrome\/([0-9]+)\./);return A?parseInt(A[1],10):A})();var d=!!navigator.userAgent.match(/Edge/i)||navigator.userAgent.match(/Edg\/(.*?)/)&&g.navigator.userAgent.match(/Chrome\/(.*?) /);
(function(){if(!d)return null;var A=g.navigator.userAgent.match(/Edg\/([0-9]+)\./);return A?parseInt(A[1],10):A})();m=/iPad|iPhone|iPod/.test(g.navigator.platform)||"MacIntel"===navigator.platform&&1<navigator.maxTouchPoints||/iPad|iPhone|iPod/.test(g.navigator.userAgent);var h=function(){var A=g.navigator.userAgent.match(/.*\/([0-9\.]+)\s(Safari|Mobile).*/i);return A?parseFloat(A[1]):A}(),r=/^((?!chrome|android).)*safari/i.test(g.navigator.userAgent)||/^((?!chrome|android).)*$/.test(g.navigator.userAgent)&&
m;r&&/^((?!chrome|android).)*safari/i.test(navigator.userAgent)&&parseInt(null===(k=navigator.userAgent.match(/Version\/(\d+)/))||void 0===k?void 0:k[1],10);var t=g.navigator.userAgent.match(/Firefox/);(function(){if(!t)return null;var A=g.navigator.userAgent.match(/Firefox\/([0-9]+)\./);return A?parseInt(A[1],10):A})();q||/Android|webOS|Touch|IEMobile|Silk/i.test(navigator.userAgent);navigator.userAgent.match(/(iPad|iPhone|iPod)/i);g.navigator.userAgent.indexOf("Android");var w=/Mac OS X 10_13_6.*\(KHTML, like Gecko\)$/.test(g.navigator.userAgent),
z=g.navigator.userAgent.match(/(iPad|iPhone).+\sOS\s((\d+)(_\d)*)/i)?14<=parseInt(g.navigator.userAgent.match(/(iPad|iPhone).+\sOS\s((\d+)(_\d)*)/i)[3],10):!1,x=!(!self.WebAssembly||!self.WebAssembly.validate),E=-1<g.navigator.userAgent.indexOf("Edge/16")||-1<g.navigator.userAgent.indexOf("MSAppHost"),B=function(){return x&&!E&&!(!z&&(r&&14>h||w))}},function(q,m,l){q.exports=l(10)},function(q,m,l){l.r(m);(function(k){function g(y){return y.split("<File>")[1].split("</File>")[0]}function p(y,e,u){var v=
3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;y="<InfixServer>".concat(y,"</InfixServer>");null==v&&(v="importCommand"+u+".xml");FS.writeFile(v,y);P.ccall("wasmRunXML","number",["string","string"],[v,e]);FS.unlink(v)}function d(y){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:!0,u=FS.readFile(y).buffer;e&&FS.unlink(y);u||console.warn("No buffer results found for: ".concat(y));return u}function h(y){1==Q?postMessage({cmd:"isReady",taskId:y}):setTimeout(function(){return h(y)},
300)}function r(y,e){var u=y.galleyId,v=y.cmd,D=y.subCmd,H=y.caretStart,K=y.caretEnd,N=y.resultsFile,I=y.commandXML,T=y.commandFile,V=y.pageNumber;y=y.taskId;if("editText"===v)var U=D;else if("performUndoRedo"===v||"transformTextBox"===v)U=v;p(I,N,0,T);v=d(N);postMessage({cmd:"editText",subCmd:U,caretStart:H,caretEnd:K,galleyId:u,commandXML:!1===e?null:I,resultsXML:v,pageNumber:V,taskId:y},[v])}function t(y,e,u,v){p(y,u,0,e);e=d(u);v||(y=null);postMessage({cmd:"insertTextBox",commandXML:y,resultsXML:e},
[e])}function w(y,e){var u=y.galleyId,v=y.resultsFile,D=y.willTriggerTextContentUpdated,H=y.pageNumber;p(y.commandXML,v,0,y.commandFile);v=d(v);postMessage({pageNumber:H,cmd:e,resultsXML:v,galleyId:u,willTriggerTextContentUpdated:D,taskId:y.taskId},[v])}function z(y,e,u,v,D,H){if(e){u=new Uint8Array(u);e="inputFile"+y+".pdf";FS.writeFile(e,u);v=new Uint8Array(v);var K=(new TextDecoder("utf-8")).decode(v);u="objects"+y+".xml";v="results"+y+".xml";K='\n  <Commands>\n    <Command Name="LoadPDF"><File>'.concat(e,
'</File></Command>\n    <Command Name="Page BBox"><StartPage>1</StartPage><EndPage>1</EndPage></Command>\n    ').concat(K?'<Command Name="AddTableBoxes">'.concat(K,"</Command>"):"",'\n    <Command Name="Edit Page">\n      <WebFontURL>').concat(S||"https://www.pdftron.com/webfonts/v2/","</WebFontURL>\n      <Output>").concat(u,"</Output>\n      <ImagesAndText/>\n      <IgnoreRotation>false</IgnoreRotation>\n      ").concat(L?"<ForceReflow/>":"","\n    </Command>\n  </Commands>");p(K,v,1);F=y;D&&(D=
d(e,!1),e=d(u),v=d(v),postMessage({cmd:"exportFile",pageNumber:y,exportPerformed:!0,pdfBuffer:D,objectXML:e,resultsXML:v,taskId:H},[D,v]))}else postMessage({cmd:"exportFile",pageNumber:y,exportPerformed:!1})}function x(y){var e=y.pdfFile,u=y.tableData,v=y.subCmd,D=y.pageNumber,H=y.commandXML,K=y.objectID,N=y.isText,I=y.isUpdatingRect,T=y.oid,V=y.canUndoRedo,U=y.outputFName;y=y.taskId;D!==F&&z(D,!0,e,u,!1);var a="results"+D+".xml";e=N?"transformTextBox":"transformObject";p(H,a,D);u=d(U);a=d(a);postMessage({cmd:e,
pageNumber:D,pdfBuffer:u,resultsXML:a,id:K,isUpdatingRect:I,isText:N,commandXML:H,subCmd:v,canUndoRedo:V,oid:T,outputFName:U,taskId:y},[u,a])}l.d(m,"extractFileNameFromCommand",function(){return g});var E=l(7),B=l(1),A="undefined"===typeof window?self:window;A.Core=A.Core||{};var L=!0,Q=!1,R=null,F=-1,M=!1,S,P={noInitialRun:!0,onRuntimeInitialized:function(){Q=!0},fetchSelf:function(){Object(E.a)("InfixServer",{"Wasm.wasm":1E8,"Wasm.js.mem":1E5,".js.mem":5E6,".mem":3E6,disableObjectURLBlobs:M},!!navigator.userAgent.match(/Edge/i))},
locateFile:function(y){return y},getPreloadedPackage:function(y,e){"InfixServerWasm.br.mem"==y&&(y="InfixServerWasm.mem");return Object(B.b)("".concat(R||"").concat(y),e,!1,!0).buffer}};self.Module=P;self.basePath="../external/";onmessage=function(y){var e=y.data;switch(e.cmd){case "isReady":R=e.resourcePath;P.fetchSelf();h(e.taskId);break;case "initialiseInfixServer":var u=e.l,v=e.taskId;P.callMain([""]);P.ccall("wasmInitInfixServer","number",["string","string","string"],["infixcore.cfg",u,"results.xml"]);
var D=d("results.xml");postMessage({cmd:"initialiseInfixServer",resultsXML:D,taskId:v},[D]);break;case "disableObjectURLBlobs":M=e.disableURLBlobs;break;case "loadAvailableFonts":var H=e.commandXML,K=e.taskId;S=e.webFontURL;p(H,"results0.xml",0);var N=d("results0.xml");postMessage({cmd:"loadAvailableFonts",resultsXML:N,taskId:K},[N]);break;case "exportFile":z(e.pageNumber,e.performExport,e.pdfFile,e.tableData,!0,e.taskId);break;case "applyTransformMatrix":var I=e.oid,T=e.pageNumber,V=e.taskId;p(e.commandXML,
"results"+T+".xml",T);postMessage({cmd:"applyTransformMatrix",pageNumber:T,id:I,taskId:V});break;case "transformObject":x(e);break;case "deleteObject":var U=e.pdfFile,a=e.pageNumber,b=e.objectID,c=e.tableData,f=e.isUndoRedo,n=e.isPageDeleted,C=e.taskId;a!==F&&z(a,!0,U,c,!1);var G="outputFile"+a+".pdf",J="results"+a+".xml";var O='<Commands><Command Name="DeleteObject">'+"<OID>".concat(b,"</OID></Command>");O+='<Command Name="SavePDF"><File>'.concat(G,"</File>");p(O+"</Command></Commands>",J,a);var W=
d(G),Z=d(J);postMessage({cmd:"deleteObject",pageNumber:a,pdfBuffer:W,resultsXML:Z,oid:b,isUndoRedo:f,isPageDeleted:n,taskId:C},[W,Z]);break;case "insertTextBox":t(e.commandXML,e.commandFile,e.resultsFile,!0);break;case "insertNewTextBox":var Bb=e.pdfFile,X=e.pageNumber,Cb=e.topVal,Db=e.leftVal,Eb=e.bottomVal,Fb=e.rightVal,Gb=e.font,Hb=e.fontSize,Ib=e.importData,Jb=e.content,Kb=e.canUndoRedo,Lb=e.taskId,Mb=(new TextEncoder).encode("").buffer;X!=F&&z(X,!0,Bb,Mb,!1);var qa="results"+X+".xml",ra="exported"+
X+".xml",sa="outputFile"+X+".pdf";var Y='<Commands><Command Name="Insert Text Box">'+"<Rect><Top>".concat(Cb,"</Top><Left>").concat(Db,"</Left>");Y+="<Bottom>".concat(Eb,"</Bottom><Right>").concat(Fb,"</Right></Rect>");Y+="<Size>".concat(Hb,"</Size><FontName>").concat(Gb,"</FontName>");var ta="editText"+X+".xml";FS.writeFile(ta,Ib);Y+="<File>".concat(ta,"</File><TransXML>coreTransXML.cfg</TransXML>");Y+="<ExportFile>".concat(ra,"</ExportFile><TransXML>coreTransXML.cfg</TransXML>");Y=Y+'<StartPage>1</StartPage><EndPage>LastPage</EndPage><AutoSubstitute/><AutoDeleteParas/><Fitting><Shrink><FontSize Min="0.65">true</FontSize><Leading>False</Leading></Shrink><Stretch><FontSize>False</FontSize><Leading>False</Leading></Stretch></Fitting><ResetLetterSpacing/><IgnoreFlightCheck/><MissingFont>Noto Sans Regular</MissingFont><SubstituteAllChars/><TargetLang>en</TargetLang></Command>'+
'<Command Name="SavePDF"><File>'.concat(sa,"</File></Command></Commands>");p(Y,qa,X);var ua=d(sa),va=d(qa),wa=d(ra);postMessage({cmd:"insertNewTextBox",pageNumber:X,pdfBuffer:ua,exportXML:wa,resultsXML:va,contentHTML:Jb,commandXML:Y,canUndoRedo:Kb,taskId:Lb},[ua,wa,va]);break;case "AlignContentBox":var Nb=e.pdfFile,fa=e.pageNumber,Ob=e.galleyId,Pb=e.tableData,ka=e.commandXML,Qb=e.taskId;fa!=F&&z(fa,!0,Nb,Pb,!1);var Rb=g(ka);p(ka,"results.xml",fa);var xa=d("results.xml"),ya=d(Rb);postMessage({cmd:"updateContentBox",
subCmd:"Set Para Attribs",pageNumber:fa,pdfBuffer:ya,resultsXML:xa,galleyId:Ob,commandXML:ka,taskId:Qb},[ya,xa]);break;case "RenderContentBox":var Sb=e.pdfFile,ha=e.pageNumber,Tb=e.galleyId,Ub=e.tableData,za=e.commandXML,Vb=e.taskId;ha!=F&&z(ha,!0,Sb,Ub,!1);var Wb=g(za);p(za,"results.xml",ha);var Aa=d(Wb),Ba=d("results.xml");postMessage({cmd:"renderContentBox",pageNumber:ha,pdfBuffer:Aa,resultsXML:Ba,galleyId:Tb,taskId:Vb},[Aa,Ba]);break;case "AlignParagraph":var Xb=e.pdfFile,ia=e.pageNumber,Yb=e.galleyId,
Zb=e.tableData,Ca=e.commandXML,$b=e.taskId;ia!=F&&z(ia,!0,Xb,Zb,!1);p(Ca,"results.xml",ia);var Da=d("results.xml");postMessage({cmd:"editText",subCmd:"Set Para Attribs",galleyId:Yb,commandXML:Ca,pageNumber:ia,taskId:$b,resultsXML:Da},[Da]);break;case "DecorateContentBox":var la=e.commandXML,ac=e.pdfFile,ja=e.pageNumber,Ea=e.galleyId,bc=e.tableData,cc=e.taskId,dc=e.subCmd;ja!=F&&z(ja,!0,ac,bc,!1);var ec=g(la);p(la,"results.xml",ja);var Fa=d("results.xml"),Ga=d(ec);postMessage({cmd:"updateContentBox",
pageNumber:ja,pdfBuffer:Ga,commandXML:la,resultsXML:Fa,subCmd:dc,id:Ea,galleyId:Ea,taskId:cc},[Ga,Fa]);break;case "insertImage":var fc=e.pdfFile,aa=e.pageNumber,ma=e.newImage,gc=e.canUndoRedo,hc=e.taskId,Ha=e.commandXML,ic=e.imageFileName,jc=e.outputFileName,kc=(new TextEncoder).encode("").buffer;aa!=F&&z(aa,!0,fc,kc,!1);var Ia="results"+aa+".xml";FS.writeFile(ic,k.from(ma));p(Ha,Ia,aa);var Ja=d(jc),Ka=d(Ia);postMessage({cmd:"insertImage",pageNumber:aa,pdfBuffer:Ja,resultsXML:Ka,commandXML:Ha,canUndoRedo:gc,
newImage:ma,taskId:hc},[Ja,Ka,ma]);break;case "runCommand":var lc=e.subCmd,La=e.resultsFile;p(e.commandXML,La,0,e.commandFile);var Ma=d(La);postMessage({cmd:"runCommand",subCmd:lc,resultsXML:Ma},[Ma]);break;case "renderEditGalley":var Na=e.resultsFile;p(e.commandXML,Na,0,e.commandFile);var Oa=d(Na),Pa=d(e.imageFName);postMessage({cmd:"renderEditGalley",resultsXML:Oa,imageData:Pa,galleyId:e.galleyId,taskId:e.taskId},[Oa,Pa]);break;case "renderFullPage":var Qa=e.resultsFile;p(e.commandXML,Qa,0,e.commandFile);
var Ra=d(Qa),Sa=d(e.imageFName);postMessage({cmd:"renderFullPage",resultsXML:Ra,imageData:Sa,outputWidth:e.width,outputHeight:e.height},[Ra,Sa]);break;case "textAttributes":var mc=e.id,nc=e.numChars,Ta=e.resultsFile,oc=e.taskId;p(e.commandXML,Ta,0,e.commandFile);var Ua=d(Ta);postMessage({cmd:"textAttributes",id:mc,numChars:nc,resultsXML:Ua,taskId:oc},[Ua]);break;case "editText":r(e,!0);break;case "editObject":var pc=e.subCmd,qc=e.oid,Va=e.resultsFile,Wa=e.commandXML;p(Wa,Va,0,e.commandFile);var Xa=
d(Va);postMessage({cmd:"editObject",subCmd:pc,oid:qc,commandXML:Wa,resultsXML:Xa},[Xa]);break;case "performUndoRedo":switch(e.subCmd){case "editText":r(e,!1);break;case "transformObject":e.subCmd="performUndoRedo";x(e);break;case "insertTextBoxRedo":var Ya=e.commandXML,ba=e.pageNumber,rc=e.taskId,Za="results"+ba+".xml",sc="exported"+ba+".xml",tc="outputFile"+ba+".pdf";p(Ya,Za,ba);var $a=d(tc),ab=d(Za),bb=d(sc);postMessage({cmd:"insertNewTextBox",subCmd:"performUndoRedo",pageNumber:ba,pdfBuffer:$a,
exportXML:bb,resultsXML:ab,commandXML:Ya,taskId:rc},[$a,bb,ab]);break;case "insertImageRedo":var cb=e.commandXML,ca=e.pageNumber,db=e.newImage,uc="outputFile"+ca+".pdf",eb="results"+ca+".xml",fb="imageFile"+ca+".jpg";FS.writeFile(fb,k.from(db));p(cb,eb,ca);var gb=d(uc),hb=d(eb);FS.unlink(fb);postMessage({cmd:"insertImage",pageNumber:ca,pdfBuffer:gb,resultsXML:hb,commandXML:cb,newImage:db},[gb,hb])}break;case "insertTextBoxRedo":t(e.commandXML,e.commandFile,e.resultsFile,!1);break;case "copyText":w(e,
"copyText");break;case "getUpdatedText":w(e,"getUpdatedText");break;case "dumpTextBox":var vc=e.galleyId,ib=e.resultsFile,wc=e.taskId;p(e.commandXML,ib,0,e.commandFile);var jb=d(ib);postMessage({cmd:"dumpTextBox",galleyId:vc,resultsXML:jb,taskId:wc},[jb]);break;case "transformTextBox":r(e,!1);break;case "savePDF":var kb=e.resultsFile,xc=e.pdfFileName,yc=new Uint8Array(e.pdfFile);FS.writeFile(e.pdfFileName,yc);p(e.commandXML,kb,0,e.commandFile);var lb=d(xc),mb=d(kb);postMessage({cmd:"savePDF",pdfBuffer:lb,
resultsXML:mb},[lb,mb]);break;case "loadPDF":var nb=e.resultsFile,zc=new Uint8Array(e.pdfFile);FS.writeFile(e.pdfFileName,zc);p(e.commandXML,nb,0,e.commandFile);var ob=d(nb);postMessage({cmd:"loadPDF",resultsXML:ob},[ob]);break;case "loadHyperlinkURL":var Ac=e.id,na=e.resultsFile,Bc=e.taskId;p(e.commandXML,na,0,e.commandFile);var pb=FS.readFile(na).buffer;FS.unlink(na);postMessage({id:Ac,cmd:"loadHyperlinkURL",resultsXML:pb,taskId:Bc},[pb]);break;case "setTypographyContentBox":var Cc=e.pdfFile,da=
e.pageNumber,qb=e.galleyId,Dc=e.subCmd,Ec=e.tableData,Fc=e.taskId,oa=e.commandXML;da!=F&&z(da,!0,Cc,Ec,!1);var rb="results"+da+".xml";p(oa,rb,da);var Gc=g(oa),sb=d(Gc),tb=d(rb);postMessage({cmd:"setTypographyContentBox",subCmd:Dc,pageNumber:da,pdfBuffer:sb,commandXML:oa,resultsXML:tb,id:qb,galleyId:qb,taskId:Fc},[sb,tb]);break;case "updateDocumentContent":var ea=e.pageNumber,Hc=e.galleyId,Ic=e.outputFileName,ub=e.commandXML,Jc=e.isSearchReplace,Kc=e.callbackMapId,Lc=e.pdfPage,Mc=e.tableArray,Nc=e.taskId;
ea!=F&&z(ea,!0,Lc,Mc,!1);var vb="results"+ea+".xml";p(ub,vb,ea);var wb=d(Ic),xb=d(vb);postMessage({cmd:"updateContentBox",pageNumber:ea,pdfBuffer:wb,commandXML:ub,resultsXML:xb,galleyId:Hc,callbackMapId:Kc,isSearchReplace:Jc,taskId:Nc},[wb,xb]);break;case "getInfixVersion":var Oc=e.taskId,yb=e.commandXML;p(yb,"results1.xml",1);var zb=d("results1.xml");postMessage({cmd:"getInfixVersion",commandXML:yb,resultsXML:zb,taskId:Oc},[zb]);break;case "reloadPage":var pa=e.pageNumber,Pc=new Uint8Array(e.pdfFile),
Ab="inputFile"+pa+".pdf";FS.writeFile(Ab,Pc);var Qc="objects"+pa+".xml",Rc="results"+pa+".xml",Sc='\n  <Commands>\n    <Command Name="LoadPDF"><File>'.concat(Ab,'</File></Command>\n    <Command Name="Page BBox"><StartPage>1</StartPage><EndPage>1</EndPage></Command>\n    <Command Name="Edit Page">\n    <Output>').concat(Qc,"</Output><ImagesOnly/>").concat(L?"<ForceReflow/>":"","</Command>\n  </Commands>");p(Sc,Rc,1);break;case "setTextReflow":L=e.textReflow;postMessage({taskId:e.taskId});break;case "getTextReflow":postMessage({taskId:e.taskId,
textReflow:L})}}}).call(this,l(11).Buffer)},function(q,m,l){(function(k){function g(){try{var a=new Uint8Array(1);a.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}};return 42===a.foo()&&"function"===typeof a.subarray&&0===a.subarray(1,1).byteLength}catch(b){return!1}}function p(a,b){if((d.TYPED_ARRAY_SUPPORT?2147483647:1073741823)<b)throw new RangeError("Invalid typed array length");d.TYPED_ARRAY_SUPPORT?(a=new Uint8Array(b),a.__proto__=d.prototype):(null===a&&(a=new d(b)),a.length=
b);return a}function d(a,b,c){if(!(d.TYPED_ARRAY_SUPPORT||this instanceof d))return new d(a,b,c);if("number"===typeof a){if("string"===typeof b)throw Error("If encoding is specified then the first argument must be a string");return t(this,a)}return h(this,a,b,c)}function h(a,b,c,f){if("number"===typeof b)throw new TypeError('"value" argument must not be a number');if("undefined"!==typeof ArrayBuffer&&b instanceof ArrayBuffer){b.byteLength;if(0>c||b.byteLength<c)throw new RangeError("'offset' is out of bounds");
if(b.byteLength<c+(f||0))throw new RangeError("'length' is out of bounds");b=void 0===c&&void 0===f?new Uint8Array(b):void 0===f?new Uint8Array(b,c):new Uint8Array(b,c,f);d.TYPED_ARRAY_SUPPORT?(a=b,a.__proto__=d.prototype):a=w(a,b);return a}if("string"===typeof b){f=a;a=c;if("string"!==typeof a||""===a)a="utf8";if(!d.isEncoding(a))throw new TypeError('"encoding" must be a valid string encoding');c=E(b,a)|0;f=p(f,c);b=f.write(b,a);b!==c&&(f=f.slice(0,b));return f}return z(a,b)}function r(a){if("number"!==
typeof a)throw new TypeError('"size" argument must be a number');if(0>a)throw new RangeError('"size" argument must not be negative');}function t(a,b){r(b);a=p(a,0>b?0:x(b)|0);if(!d.TYPED_ARRAY_SUPPORT)for(var c=0;c<b;++c)a[c]=0;return a}function w(a,b){var c=0>b.length?0:x(b.length)|0;a=p(a,c);for(var f=0;f<c;f+=1)a[f]=b[f]&255;return a}function z(a,b){if(d.isBuffer(b)){var c=x(b.length)|0;a=p(a,c);if(0===a.length)return a;b.copy(a,0,0,c);return a}if(b){if("undefined"!==typeof ArrayBuffer&&b.buffer instanceof
ArrayBuffer||"length"in b)return(c="number"!==typeof b.length)||(c=b.length,c=c!==c),c?p(a,0):w(a,b);if("Buffer"===b.type&&T(b.data))return w(a,b.data)}throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.");}function x(a){if(a>=(d.TYPED_ARRAY_SUPPORT?2147483647:1073741823))throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+(d.TYPED_ARRAY_SUPPORT?2147483647:1073741823).toString(16)+" bytes");return a|0}function E(a,b){if(d.isBuffer(a))return a.length;
if("undefined"!==typeof ArrayBuffer&&"function"===typeof ArrayBuffer.isView&&(ArrayBuffer.isView(a)||a instanceof ArrayBuffer))return a.byteLength;"string"!==typeof a&&(a=""+a);var c=a.length;if(0===c)return 0;for(var f=!1;;)switch(b){case "ascii":case "latin1":case "binary":return c;case "utf8":case "utf-8":case void 0:return v(a).length;case "ucs2":case "ucs-2":case "utf16le":case "utf-16le":return 2*c;case "hex":return c>>>1;case "base64":return H(a).length;default:if(f)return v(a).length;b=(""+
b).toLowerCase();f=!0}}function B(a,b,c){var f=!1;if(void 0===b||0>b)b=0;if(b>this.length)return"";if(void 0===c||c>this.length)c=this.length;if(0>=c)return"";c>>>=0;b>>>=0;if(c<=b)return"";for(a||(a="utf8");;)switch(a){case "hex":a=b;b=c;c=this.length;if(!a||0>a)a=0;if(!b||0>b||b>c)b=c;f="";for(c=a;c<b;++c)a=f,f=this[c],f=16>f?"0"+f.toString(16):f.toString(16),f=a+f;return f;case "utf8":case "utf-8":return R(this,b,c);case "ascii":a="";for(c=Math.min(this.length,c);b<c;++b)a+=String.fromCharCode(this[b]&
127);return a;case "latin1":case "binary":a="";for(c=Math.min(this.length,c);b<c;++b)a+=String.fromCharCode(this[b]);return a;case "base64":return b=0===b&&c===this.length?N.fromByteArray(this):N.fromByteArray(this.slice(b,c)),b;case "ucs2":case "ucs-2":case "utf16le":case "utf-16le":b=this.slice(b,c);c="";for(a=0;a<b.length;a+=2)c+=String.fromCharCode(b[a]+256*b[a+1]);return c;default:if(f)throw new TypeError("Unknown encoding: "+a);a=(a+"").toLowerCase();f=!0}}function A(a,b,c){var f=a[b];a[b]=
a[c];a[c]=f}function L(a,b,c,f,n){if(0===a.length)return-1;"string"===typeof c?(f=c,c=0):2147483647<c?c=2147483647:-2147483648>c&&(c=-2147483648);c=+c;isNaN(c)&&(c=n?0:a.length-1);0>c&&(c=a.length+c);if(c>=a.length){if(n)return-1;c=a.length-1}else if(0>c)if(n)c=0;else return-1;"string"===typeof b&&(b=d.from(b,f));if(d.isBuffer(b))return 0===b.length?-1:Q(a,b,c,f,n);if("number"===typeof b)return b&=255,d.TYPED_ARRAY_SUPPORT&&"function"===typeof Uint8Array.prototype.indexOf?n?Uint8Array.prototype.indexOf.call(a,
b,c):Uint8Array.prototype.lastIndexOf.call(a,b,c):Q(a,[b],c,f,n);throw new TypeError("val must be string, number or Buffer");}function Q(a,b,c,f,n){function C(W,Z){return 1===G?W[Z]:W.readUInt16BE(Z*G)}var G=1,J=a.length,O=b.length;if(void 0!==f&&(f=String(f).toLowerCase(),"ucs2"===f||"ucs-2"===f||"utf16le"===f||"utf-16le"===f)){if(2>a.length||2>b.length)return-1;G=2;J/=2;O/=2;c/=2}if(n)for(f=-1;c<J;c++)if(C(a,c)===C(b,-1===f?0:c-f)){if(-1===f&&(f=c),c-f+1===O)return f*G}else-1!==f&&(c-=c-f),f=-1;
else for(c+O>J&&(c=J-O);0<=c;c--){J=!0;for(f=0;f<O;f++)if(C(a,c+f)!==C(b,f)){J=!1;break}if(J)return c}return-1}function R(a,b,c){c=Math.min(a.length,c);for(var f=[];b<c;){var n=a[b],C=null,G=239<n?4:223<n?3:191<n?2:1;if(b+G<=c)switch(G){case 1:128>n&&(C=n);break;case 2:var J=a[b+1];128===(J&192)&&(n=(n&31)<<6|J&63,127<n&&(C=n));break;case 3:J=a[b+1];var O=a[b+2];128===(J&192)&&128===(O&192)&&(n=(n&15)<<12|(J&63)<<6|O&63,2047<n&&(55296>n||57343<n)&&(C=n));break;case 4:J=a[b+1];O=a[b+2];var W=a[b+3];
128===(J&192)&&128===(O&192)&&128===(W&192)&&(n=(n&15)<<18|(J&63)<<12|(O&63)<<6|W&63,65535<n&&1114112>n&&(C=n))}null===C?(C=65533,G=1):65535<C&&(C-=65536,f.push(C>>>10&1023|55296),C=56320|C&1023);f.push(C);b+=G}a=f.length;if(a<=V)f=String.fromCharCode.apply(String,f);else{c="";for(b=0;b<a;)c+=String.fromCharCode.apply(String,f.slice(b,b+=V));f=c}return f}function F(a,b,c){if(0!==a%1||0>a)throw new RangeError("offset is not uint");if(a+b>c)throw new RangeError("Trying to access beyond buffer length");
}function M(a,b,c,f,n,C){if(!d.isBuffer(a))throw new TypeError('"buffer" argument must be a Buffer instance');if(b>n||b<C)throw new RangeError('"value" argument is out of bounds');if(c+f>a.length)throw new RangeError("Index out of range");}function S(a,b,c,f){0>b&&(b=65535+b+1);for(var n=0,C=Math.min(a.length-c,2);n<C;++n)a[c+n]=(b&255<<8*(f?n:1-n))>>>8*(f?n:1-n)}function P(a,b,c,f){0>b&&(b=4294967295+b+1);for(var n=0,C=Math.min(a.length-c,4);n<C;++n)a[c+n]=b>>>8*(f?n:3-n)&255}function y(a,b,c,f,
n,C){if(c+f>a.length)throw new RangeError("Index out of range");if(0>c)throw new RangeError("Index out of range");}function e(a,b,c,f,n){n||y(a,b,c,4,3.4028234663852886E38,-3.4028234663852886E38);I.write(a,b,c,f,23,4);return c+4}function u(a,b,c,f,n){n||y(a,b,c,8,1.7976931348623157E308,-1.7976931348623157E308);I.write(a,b,c,f,52,8);return c+8}function v(a,b){b=b||Infinity;for(var c,f=a.length,n=null,C=[],G=0;G<f;++G){c=a.charCodeAt(G);if(55295<c&&57344>c){if(!n){if(56319<c){-1<(b-=3)&&C.push(239,
191,189);continue}else if(G+1===f){-1<(b-=3)&&C.push(239,191,189);continue}n=c;continue}if(56320>c){-1<(b-=3)&&C.push(239,191,189);n=c;continue}c=(n-55296<<10|c-56320)+65536}else n&&-1<(b-=3)&&C.push(239,191,189);n=null;if(128>c){if(0>--b)break;C.push(c)}else if(2048>c){if(0>(b-=2))break;C.push(c>>6|192,c&63|128)}else if(65536>c){if(0>(b-=3))break;C.push(c>>12|224,c>>6&63|128,c&63|128)}else if(1114112>c){if(0>(b-=4))break;C.push(c>>18|240,c>>12&63|128,c>>6&63|128,c&63|128)}else throw Error("Invalid code point");
}return C}function D(a){for(var b=[],c=0;c<a.length;++c)b.push(a.charCodeAt(c)&255);return b}function H(a){var b=N,c=b.toByteArray;a=(a.trim?a.trim():a.replace(/^\s+|\s+$/g,"")).replace(U,"");if(2>a.length)a="";else for(;0!==a.length%4;)a+="=";return c.call(b,a)}function K(a,b,c,f){for(var n=0;n<f&&!(n+c>=b.length||n>=a.length);++n)b[n+c]=a[n];return n}var N=l(13),I=l(14),T=l(15);m.Buffer=d;m.SlowBuffer=function(a){+a!=a&&(a=0);return d.alloc(+a)};m.INSPECT_MAX_BYTES=50;d.TYPED_ARRAY_SUPPORT=void 0!==
k.TYPED_ARRAY_SUPPORT?k.TYPED_ARRAY_SUPPORT:g();m.kMaxLength=d.TYPED_ARRAY_SUPPORT?2147483647:1073741823;d.poolSize=8192;d._augment=function(a){a.__proto__=d.prototype;return a};d.from=function(a,b,c){return h(null,a,b,c)};d.TYPED_ARRAY_SUPPORT&&(d.prototype.__proto__=Uint8Array.prototype,d.__proto__=Uint8Array,"undefined"!==typeof Symbol&&Symbol.species&&d[Symbol.species]===d&&Object.defineProperty(d,Symbol.species,{value:null,configurable:!0}));d.alloc=function(a,b,c){r(a);a=0>=a?p(null,a):void 0!==
b?"string"===typeof c?p(null,a).fill(b,c):p(null,a).fill(b):p(null,a);return a};d.allocUnsafe=function(a){return t(null,a)};d.allocUnsafeSlow=function(a){return t(null,a)};d.isBuffer=function(a){return!(null==a||!a._isBuffer)};d.compare=function(a,b){if(!d.isBuffer(a)||!d.isBuffer(b))throw new TypeError("Arguments must be Buffers");if(a===b)return 0;for(var c=a.length,f=b.length,n=0,C=Math.min(c,f);n<C;++n)if(a[n]!==b[n]){c=a[n];f=b[n];break}return c<f?-1:f<c?1:0};d.isEncoding=function(a){switch(String(a).toLowerCase()){case "hex":case "utf8":case "utf-8":case "ascii":case "latin1":case "binary":case "base64":case "ucs2":case "ucs-2":case "utf16le":case "utf-16le":return!0;
default:return!1}};d.concat=function(a,b){if(!T(a))throw new TypeError('"list" argument must be an Array of Buffers');if(0===a.length)return d.alloc(0);var c;if(void 0===b)for(c=b=0;c<a.length;++c)b+=a[c].length;b=d.allocUnsafe(b);var f=0;for(c=0;c<a.length;++c){var n=a[c];if(!d.isBuffer(n))throw new TypeError('"list" argument must be an Array of Buffers');n.copy(b,f);f+=n.length}return b};d.byteLength=E;d.prototype._isBuffer=!0;d.prototype.swap16=function(){var a=this.length;if(0!==a%2)throw new RangeError("Buffer size must be a multiple of 16-bits");
for(var b=0;b<a;b+=2)A(this,b,b+1);return this};d.prototype.swap32=function(){var a=this.length;if(0!==a%4)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var b=0;b<a;b+=4)A(this,b,b+3),A(this,b+1,b+2);return this};d.prototype.swap64=function(){var a=this.length;if(0!==a%8)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var b=0;b<a;b+=8)A(this,b,b+7),A(this,b+1,b+6),A(this,b+2,b+5),A(this,b+3,b+4);return this};d.prototype.toString=function(){var a=this.length|
0;return 0===a?"":0===arguments.length?R(this,0,a):B.apply(this,arguments)};d.prototype.equals=function(a){if(!d.isBuffer(a))throw new TypeError("Argument must be a Buffer");return this===a?!0:0===d.compare(this,a)};d.prototype.inspect=function(){var a="",b=m.INSPECT_MAX_BYTES;0<this.length&&(a=this.toString("hex",0,b).match(/.{2}/g).join(" "),this.length>b&&(a+=" ... "));return"<Buffer "+a+">"};d.prototype.compare=function(a,b,c,f,n){if(!d.isBuffer(a))throw new TypeError("Argument must be a Buffer");
void 0===b&&(b=0);void 0===c&&(c=a?a.length:0);void 0===f&&(f=0);void 0===n&&(n=this.length);if(0>b||c>a.length||0>f||n>this.length)throw new RangeError("out of range index");if(f>=n&&b>=c)return 0;if(f>=n)return-1;if(b>=c)return 1;b>>>=0;c>>>=0;f>>>=0;n>>>=0;if(this===a)return 0;var C=n-f,G=c-b,J=Math.min(C,G);f=this.slice(f,n);a=a.slice(b,c);for(b=0;b<J;++b)if(f[b]!==a[b]){C=f[b];G=a[b];break}return C<G?-1:G<C?1:0};d.prototype.includes=function(a,b,c){return-1!==this.indexOf(a,b,c)};d.prototype.indexOf=
function(a,b,c){return L(this,a,b,c,!0)};d.prototype.lastIndexOf=function(a,b,c){return L(this,a,b,c,!1)};d.prototype.write=function(a,b,c,f){if(void 0===b)f="utf8",c=this.length,b=0;else if(void 0===c&&"string"===typeof b)f=b,c=this.length,b=0;else if(isFinite(b))b|=0,isFinite(c)?(c|=0,void 0===f&&(f="utf8")):(f=c,c=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var n=this.length-b;if(void 0===c||c>n)c=n;if(0<a.length&&(0>c||0>b)||b>this.length)throw new RangeError("Attempt to write outside buffer bounds");
f||(f="utf8");for(n=!1;;)switch(f){case "hex":a:{b=Number(b)||0;f=this.length-b;c?(c=Number(c),c>f&&(c=f)):c=f;f=a.length;if(0!==f%2)throw new TypeError("Invalid hex string");c>f/2&&(c=f/2);for(f=0;f<c;++f){n=parseInt(a.substr(2*f,2),16);if(isNaN(n)){a=f;break a}this[b+f]=n}a=f}return a;case "utf8":case "utf-8":return K(v(a,this.length-b),this,b,c);case "ascii":return K(D(a),this,b,c);case "latin1":case "binary":return K(D(a),this,b,c);case "base64":return K(H(a),this,b,c);case "ucs2":case "ucs-2":case "utf16le":case "utf-16le":f=
a;n=this.length-b;for(var C=[],G=0;G<f.length&&!(0>(n-=2));++G){var J=f.charCodeAt(G);a=J>>8;J%=256;C.push(J);C.push(a)}return K(C,this,b,c);default:if(n)throw new TypeError("Unknown encoding: "+f);f=(""+f).toLowerCase();n=!0}};d.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var V=4096;d.prototype.slice=function(a,b){var c=this.length;a=~~a;b=void 0===b?c:~~b;0>a?(a+=c,0>a&&(a=0)):a>c&&(a=c);0>b?(b+=c,0>b&&(b=0)):b>c&&(b=c);b<a&&(b=a);if(d.TYPED_ARRAY_SUPPORT)b=
this.subarray(a,b),b.__proto__=d.prototype;else{c=b-a;b=new d(c,void 0);for(var f=0;f<c;++f)b[f]=this[f+a]}return b};d.prototype.readUIntLE=function(a,b,c){a|=0;b|=0;c||F(a,b,this.length);c=this[a];for(var f=1,n=0;++n<b&&(f*=256);)c+=this[a+n]*f;return c};d.prototype.readUIntBE=function(a,b,c){a|=0;b|=0;c||F(a,b,this.length);c=this[a+--b];for(var f=1;0<b&&(f*=256);)c+=this[a+--b]*f;return c};d.prototype.readUInt8=function(a,b){b||F(a,1,this.length);return this[a]};d.prototype.readUInt16LE=function(a,
b){b||F(a,2,this.length);return this[a]|this[a+1]<<8};d.prototype.readUInt16BE=function(a,b){b||F(a,2,this.length);return this[a]<<8|this[a+1]};d.prototype.readUInt32LE=function(a,b){b||F(a,4,this.length);return(this[a]|this[a+1]<<8|this[a+2]<<16)+16777216*this[a+3]};d.prototype.readUInt32BE=function(a,b){b||F(a,4,this.length);return 16777216*this[a]+(this[a+1]<<16|this[a+2]<<8|this[a+3])};d.prototype.readIntLE=function(a,b,c){a|=0;b|=0;c||F(a,b,this.length);c=this[a];for(var f=1,n=0;++n<b&&(f*=256);)c+=
this[a+n]*f;c>=128*f&&(c-=Math.pow(2,8*b));return c};d.prototype.readIntBE=function(a,b,c){a|=0;b|=0;c||F(a,b,this.length);c=b;for(var f=1,n=this[a+--c];0<c&&(f*=256);)n+=this[a+--c]*f;n>=128*f&&(n-=Math.pow(2,8*b));return n};d.prototype.readInt8=function(a,b){b||F(a,1,this.length);return this[a]&128?-1*(255-this[a]+1):this[a]};d.prototype.readInt16LE=function(a,b){b||F(a,2,this.length);a=this[a]|this[a+1]<<8;return a&32768?a|4294901760:a};d.prototype.readInt16BE=function(a,b){b||F(a,2,this.length);
a=this[a+1]|this[a]<<8;return a&32768?a|4294901760:a};d.prototype.readInt32LE=function(a,b){b||F(a,4,this.length);return this[a]|this[a+1]<<8|this[a+2]<<16|this[a+3]<<24};d.prototype.readInt32BE=function(a,b){b||F(a,4,this.length);return this[a]<<24|this[a+1]<<16|this[a+2]<<8|this[a+3]};d.prototype.readFloatLE=function(a,b){b||F(a,4,this.length);return I.read(this,a,!0,23,4)};d.prototype.readFloatBE=function(a,b){b||F(a,4,this.length);return I.read(this,a,!1,23,4)};d.prototype.readDoubleLE=function(a,
b){b||F(a,8,this.length);return I.read(this,a,!0,52,8)};d.prototype.readDoubleBE=function(a,b){b||F(a,8,this.length);return I.read(this,a,!1,52,8)};d.prototype.writeUIntLE=function(a,b,c,f){a=+a;b|=0;c|=0;f||M(this,a,b,c,Math.pow(2,8*c)-1,0);f=1;var n=0;for(this[b]=a&255;++n<c&&(f*=256);)this[b+n]=a/f&255;return b+c};d.prototype.writeUIntBE=function(a,b,c,f){a=+a;b|=0;c|=0;f||M(this,a,b,c,Math.pow(2,8*c)-1,0);f=c-1;var n=1;for(this[b+f]=a&255;0<=--f&&(n*=256);)this[b+f]=a/n&255;return b+c};d.prototype.writeUInt8=
function(a,b,c){a=+a;b|=0;c||M(this,a,b,1,255,0);d.TYPED_ARRAY_SUPPORT||(a=Math.floor(a));this[b]=a&255;return b+1};d.prototype.writeUInt16LE=function(a,b,c){a=+a;b|=0;c||M(this,a,b,2,65535,0);d.TYPED_ARRAY_SUPPORT?(this[b]=a&255,this[b+1]=a>>>8):S(this,a,b,!0);return b+2};d.prototype.writeUInt16BE=function(a,b,c){a=+a;b|=0;c||M(this,a,b,2,65535,0);d.TYPED_ARRAY_SUPPORT?(this[b]=a>>>8,this[b+1]=a&255):S(this,a,b,!1);return b+2};d.prototype.writeUInt32LE=function(a,b,c){a=+a;b|=0;c||M(this,a,b,4,4294967295,
0);d.TYPED_ARRAY_SUPPORT?(this[b+3]=a>>>24,this[b+2]=a>>>16,this[b+1]=a>>>8,this[b]=a&255):P(this,a,b,!0);return b+4};d.prototype.writeUInt32BE=function(a,b,c){a=+a;b|=0;c||M(this,a,b,4,4294967295,0);d.TYPED_ARRAY_SUPPORT?(this[b]=a>>>24,this[b+1]=a>>>16,this[b+2]=a>>>8,this[b+3]=a&255):P(this,a,b,!1);return b+4};d.prototype.writeIntLE=function(a,b,c,f){a=+a;b|=0;f||(f=Math.pow(2,8*c-1),M(this,a,b,c,f-1,-f));f=0;var n=1,C=0;for(this[b]=a&255;++f<c&&(n*=256);)0>a&&0===C&&0!==this[b+f-1]&&(C=1),this[b+
f]=(a/n>>0)-C&255;return b+c};d.prototype.writeIntBE=function(a,b,c,f){a=+a;b|=0;f||(f=Math.pow(2,8*c-1),M(this,a,b,c,f-1,-f));f=c-1;var n=1,C=0;for(this[b+f]=a&255;0<=--f&&(n*=256);)0>a&&0===C&&0!==this[b+f+1]&&(C=1),this[b+f]=(a/n>>0)-C&255;return b+c};d.prototype.writeInt8=function(a,b,c){a=+a;b|=0;c||M(this,a,b,1,127,-128);d.TYPED_ARRAY_SUPPORT||(a=Math.floor(a));0>a&&(a=255+a+1);this[b]=a&255;return b+1};d.prototype.writeInt16LE=function(a,b,c){a=+a;b|=0;c||M(this,a,b,2,32767,-32768);d.TYPED_ARRAY_SUPPORT?
(this[b]=a&255,this[b+1]=a>>>8):S(this,a,b,!0);return b+2};d.prototype.writeInt16BE=function(a,b,c){a=+a;b|=0;c||M(this,a,b,2,32767,-32768);d.TYPED_ARRAY_SUPPORT?(this[b]=a>>>8,this[b+1]=a&255):S(this,a,b,!1);return b+2};d.prototype.writeInt32LE=function(a,b,c){a=+a;b|=0;c||M(this,a,b,4,2147483647,-2147483648);d.TYPED_ARRAY_SUPPORT?(this[b]=a&255,this[b+1]=a>>>8,this[b+2]=a>>>16,this[b+3]=a>>>24):P(this,a,b,!0);return b+4};d.prototype.writeInt32BE=function(a,b,c){a=+a;b|=0;c||M(this,a,b,4,2147483647,
-2147483648);0>a&&(a=4294967295+a+1);d.TYPED_ARRAY_SUPPORT?(this[b]=a>>>24,this[b+1]=a>>>16,this[b+2]=a>>>8,this[b+3]=a&255):P(this,a,b,!1);return b+4};d.prototype.writeFloatLE=function(a,b,c){return e(this,a,b,!0,c)};d.prototype.writeFloatBE=function(a,b,c){return e(this,a,b,!1,c)};d.prototype.writeDoubleLE=function(a,b,c){return u(this,a,b,!0,c)};d.prototype.writeDoubleBE=function(a,b,c){return u(this,a,b,!1,c)};d.prototype.copy=function(a,b,c,f){c||(c=0);f||0===f||(f=this.length);b>=a.length&&
(b=a.length);b||(b=0);0<f&&f<c&&(f=c);if(f===c||0===a.length||0===this.length)return 0;if(0>b)throw new RangeError("targetStart out of bounds");if(0>c||c>=this.length)throw new RangeError("sourceStart out of bounds");if(0>f)throw new RangeError("sourceEnd out of bounds");f>this.length&&(f=this.length);a.length-b<f-c&&(f=a.length-b+c);var n=f-c;if(this===a&&c<b&&b<f)for(f=n-1;0<=f;--f)a[f+b]=this[f+c];else if(1E3>n||!d.TYPED_ARRAY_SUPPORT)for(f=0;f<n;++f)a[f+b]=this[f+c];else Uint8Array.prototype.set.call(a,
this.subarray(c,c+n),b);return n};d.prototype.fill=function(a,b,c,f){if("string"===typeof a){"string"===typeof b?(f=b,b=0,c=this.length):"string"===typeof c&&(f=c,c=this.length);if(1===a.length){var n=a.charCodeAt(0);256>n&&(a=n)}if(void 0!==f&&"string"!==typeof f)throw new TypeError("encoding must be a string");if("string"===typeof f&&!d.isEncoding(f))throw new TypeError("Unknown encoding: "+f);}else"number"===typeof a&&(a&=255);if(0>b||this.length<b||this.length<c)throw new RangeError("Out of range index");
if(c<=b)return this;b>>>=0;c=void 0===c?this.length:c>>>0;a||(a=0);if("number"===typeof a)for(f=b;f<c;++f)this[f]=a;else for(a=d.isBuffer(a)?a:v((new d(a,f)).toString()),n=a.length,f=0;f<c-b;++f)this[f+b]=a[f%n];return this};var U=/[^+\/0-9A-Za-z-_]/g}).call(this,l(12))},function(q,m){m=function(){return this}();try{m=m||(new Function("return this"))()}catch(l){"object"===typeof window&&(m=window)}q.exports=m},function(q,m,l){function k(h){var r=h.length;if(0<r%4)throw Error("Invalid string. Length must be a multiple of 4");
h=h.indexOf("=");-1===h&&(h=r);return[h,h===r?0:4-h%4]}m.byteLength=function(h){h=k(h);var r=h[1];return 3*(h[0]+r)/4-r};m.toByteArray=function(h){var r=k(h);var t=r[0];r=r[1];var w=new d(3*(t+r)/4-r),z=0,x=0<r?t-4:t,E;for(E=0;E<x;E+=4)t=p[h.charCodeAt(E)]<<18|p[h.charCodeAt(E+1)]<<12|p[h.charCodeAt(E+2)]<<6|p[h.charCodeAt(E+3)],w[z++]=t>>16&255,w[z++]=t>>8&255,w[z++]=t&255;2===r&&(t=p[h.charCodeAt(E)]<<2|p[h.charCodeAt(E+1)]>>4,w[z++]=t&255);1===r&&(t=p[h.charCodeAt(E)]<<10|p[h.charCodeAt(E+1)]<<
4|p[h.charCodeAt(E+2)]>>2,w[z++]=t>>8&255,w[z++]=t&255);return w};m.fromByteArray=function(h){for(var r=h.length,t=r%3,w=[],z=0,x=r-t;z<x;z+=16383){for(var E=w,B=E.push,A,L=h,Q=z+16383>x?x:z+16383,R=[],F=z;F<Q;F+=3)A=(L[F]<<16&16711680)+(L[F+1]<<8&65280)+(L[F+2]&255),R.push(g[A>>18&63]+g[A>>12&63]+g[A>>6&63]+g[A&63]);A=R.join("");B.call(E,A)}1===t?(h=h[r-1],w.push(g[h>>2]+g[h<<4&63]+"==")):2===t&&(h=(h[r-2]<<8)+h[r-1],w.push(g[h>>10]+g[h>>4&63]+g[h<<2&63]+"="));return w.join("")};var g=[],p=[],d=
"undefined"!==typeof Uint8Array?Uint8Array:Array;for(q=0;64>q;++q)g[q]="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"[q],p["ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charCodeAt(q)]=q;p[45]=62;p[95]=63},function(q,m){m.read=function(l,k,g,p,d){var h=8*d-p-1;var r=(1<<h)-1,t=r>>1,w=-7;d=g?d-1:0;var z=g?-1:1,x=l[k+d];d+=z;g=x&(1<<-w)-1;x>>=-w;for(w+=h;0<w;g=256*g+l[k+d],d+=z,w-=8);h=g&(1<<-w)-1;g>>=-w;for(w+=p;0<w;h=256*h+l[k+d],d+=z,w-=8);if(0===g)g=1-t;else{if(g===
r)return h?NaN:Infinity*(x?-1:1);h+=Math.pow(2,p);g-=t}return(x?-1:1)*h*Math.pow(2,g-p)};m.write=function(l,k,g,p,d,h){var r,t=8*h-d-1,w=(1<<t)-1,z=w>>1,x=23===d?Math.pow(2,-24)-Math.pow(2,-77):0;h=p?0:h-1;var E=p?1:-1,B=0>k||0===k&&0>1/k?1:0;k=Math.abs(k);isNaN(k)||Infinity===k?(k=isNaN(k)?1:0,p=w):(p=Math.floor(Math.log(k)/Math.LN2),1>k*(r=Math.pow(2,-p))&&(p--,r*=2),k=1<=p+z?k+x/r:k+x*Math.pow(2,1-z),2<=k*r&&(p++,r/=2),p+z>=w?(k=0,p=w):1<=p+z?(k=(k*r-1)*Math.pow(2,d),p+=z):(k=k*Math.pow(2,z-1)*
Math.pow(2,d),p=0));for(;8<=d;l[g+h]=k&255,h+=E,k/=256,d-=8);p=p<<d|k;for(t+=d;0<t;l[g+h]=p&255,h+=E,p/=256,t-=8);l[g+h-E]|=128*B}},function(q,m){var l={}.toString;q.exports=Array.isArray||function(k){return"[object Array]"==l.call(k)}}]);}).call(this || window)
