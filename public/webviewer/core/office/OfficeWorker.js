(function(){var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.arrayIteratorImpl=function(d){var c=0;return function(){return c<d.length?{done:!1,value:d[c++]}:{done:!0}}};$jscomp.arrayIterator=function(d){return{next:$jscomp.arrayIteratorImpl(d)}};$jscomp.ASSUME_ES5=!1;$jscomp.ASSUME_NO_NATIVE_MAP=!1;$jscomp.ASSUME_NO_NATIVE_SET=!1;$jscomp.SIMPLE_FROUND_POLYFILL=!1;$jscomp.ISOLATE_POLYFILLS=!1;$jscomp.FORCE_POLYFILL_PROMISE=!1;$jscomp.FORCE_POLYFILL_PROMISE_WHEN_NO_UNHANDLED_REJECTION=!1;
$jscomp.defineProperty=$jscomp.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(d,c,f){if(d==Array.prototype||d==Object.prototype)return d;d[c]=f.value;return d};$jscomp.getGlobal=function(d){d=["object"==typeof globalThis&&globalThis,d,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var c=0;c<d.length;++c){var f=d[c];if(f&&f.Math==Math)return f}throw Error("Cannot find global object");};$jscomp.global=$jscomp.getGlobal(this);
$jscomp.IS_SYMBOL_NATIVE="function"===typeof Symbol&&"symbol"===typeof Symbol("x");$jscomp.TRUST_ES6_POLYFILLS=!$jscomp.ISOLATE_POLYFILLS||$jscomp.IS_SYMBOL_NATIVE;$jscomp.polyfills={};$jscomp.propertyToPolyfillSymbol={};$jscomp.POLYFILL_PREFIX="$jscp$";var $jscomp$lookupPolyfilledValue=function(d,c,f){if(!f||null!=d){f=$jscomp.propertyToPolyfillSymbol[c];if(null==f)return d[c];f=d[f];return void 0!==f?f:d[c]}};
$jscomp.polyfill=function(d,c,f,e){c&&($jscomp.ISOLATE_POLYFILLS?$jscomp.polyfillIsolated(d,c,f,e):$jscomp.polyfillUnisolated(d,c,f,e))};$jscomp.polyfillUnisolated=function(d,c,f,e){f=$jscomp.global;d=d.split(".");for(e=0;e<d.length-1;e++){var a=d[e];if(!(a in f))return;f=f[a]}d=d[d.length-1];e=f[d];c=c(e);c!=e&&null!=c&&$jscomp.defineProperty(f,d,{configurable:!0,writable:!0,value:c})};
$jscomp.polyfillIsolated=function(d,c,f,e){var a=d.split(".");d=1===a.length;e=a[0];e=!d&&e in $jscomp.polyfills?$jscomp.polyfills:$jscomp.global;for(var l=0;l<a.length-1;l++){var g=a[l];if(!(g in e))return;e=e[g]}a=a[a.length-1];f=$jscomp.IS_SYMBOL_NATIVE&&"es6"===f?e[a]:null;c=c(f);null!=c&&(d?$jscomp.defineProperty($jscomp.polyfills,a,{configurable:!0,writable:!0,value:c}):c!==f&&(void 0===$jscomp.propertyToPolyfillSymbol[a]&&(f=1E9*Math.random()>>>0,$jscomp.propertyToPolyfillSymbol[a]=$jscomp.IS_SYMBOL_NATIVE?
$jscomp.global.Symbol(a):$jscomp.POLYFILL_PREFIX+f+"$"+a),$jscomp.defineProperty(e,$jscomp.propertyToPolyfillSymbol[a],{configurable:!0,writable:!0,value:c})))};$jscomp.initSymbol=function(){};
$jscomp.polyfill("Symbol",function(d){if(d)return d;var c=function(l,g){this.$jscomp$symbol$id_=l;$jscomp.defineProperty(this,"description",{configurable:!0,writable:!0,value:g})};c.prototype.toString=function(){return this.$jscomp$symbol$id_};var f="jscomp_symbol_"+(1E9*Math.random()>>>0)+"_",e=0,a=function(l){if(this instanceof a)throw new TypeError("Symbol is not a constructor");return new c(f+(l||"")+"_"+e++,l)};return a},"es6","es3");
$jscomp.polyfill("Symbol.iterator",function(d){if(d)return d;d=Symbol("Symbol.iterator");for(var c="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),f=0;f<c.length;f++){var e=$jscomp.global[c[f]];"function"===typeof e&&"function"!=typeof e.prototype[d]&&$jscomp.defineProperty(e.prototype,d,{configurable:!0,writable:!0,value:function(){return $jscomp.iteratorPrototype($jscomp.arrayIteratorImpl(this))}})}return d},"es6",
"es3");$jscomp.iteratorPrototype=function(d){d={next:d};d[Symbol.iterator]=function(){return this};return d};$jscomp.checkEs6ConformanceViaProxy=function(){try{var d={},c=Object.create(new $jscomp.global.Proxy(d,{get:function(f,e,a){return f==d&&"q"==e&&a==c}}));return!0===c.q}catch(f){return!1}};$jscomp.USE_PROXY_FOR_ES6_CONFORMANCE_CHECKS=!1;$jscomp.ES6_CONFORMANCE=$jscomp.USE_PROXY_FOR_ES6_CONFORMANCE_CHECKS&&$jscomp.checkEs6ConformanceViaProxy();
$jscomp.makeIterator=function(d){var c="undefined"!=typeof Symbol&&Symbol.iterator&&d[Symbol.iterator];if(c)return c.call(d);if("number"==typeof d.length)return $jscomp.arrayIterator(d);throw Error(String(d)+" is not an iterable or ArrayLike");};$jscomp.owns=function(d,c){return Object.prototype.hasOwnProperty.call(d,c)};$jscomp.MapEntry=function(){};
$jscomp.polyfill("Promise",function(d){function c(){this.batch_=null}function f(g){return g instanceof a?g:new a(function(b,h){b(g)})}if(d&&(!($jscomp.FORCE_POLYFILL_PROMISE||$jscomp.FORCE_POLYFILL_PROMISE_WHEN_NO_UNHANDLED_REJECTION&&"undefined"===typeof $jscomp.global.PromiseRejectionEvent)||!$jscomp.global.Promise||-1===$jscomp.global.Promise.toString().indexOf("[native code]")))return d;c.prototype.asyncExecute=function(g){if(null==this.batch_){this.batch_=[];var b=this;this.asyncExecuteFunction(function(){b.executeBatch_()})}this.batch_.push(g)};
var e=$jscomp.global.setTimeout;c.prototype.asyncExecuteFunction=function(g){e(g,0)};c.prototype.executeBatch_=function(){for(;this.batch_&&this.batch_.length;){var g=this.batch_;this.batch_=[];for(var b=0;b<g.length;++b){var h=g[b];g[b]=null;try{h()}catch(n){this.asyncThrow_(n)}}}this.batch_=null};c.prototype.asyncThrow_=function(g){this.asyncExecuteFunction(function(){throw g;})};var a=function(g){this.state_=0;this.result_=void 0;this.onSettledCallbacks_=[];this.isRejectionHandled_=!1;var b=this.createResolveAndReject_();
try{g(b.resolve,b.reject)}catch(h){b.reject(h)}};a.prototype.createResolveAndReject_=function(){function g(n){return function(u){h||(h=!0,n.call(b,u))}}var b=this,h=!1;return{resolve:g(this.resolveTo_),reject:g(this.reject_)}};a.prototype.resolveTo_=function(g){if(g===this)this.reject_(new TypeError("A Promise cannot resolve to itself"));else if(g instanceof a)this.settleSameAsPromise_(g);else{a:switch(typeof g){case "object":var b=null!=g;break a;case "function":b=!0;break a;default:b=!1}b?this.resolveToNonPromiseObj_(g):
this.fulfill_(g)}};a.prototype.resolveToNonPromiseObj_=function(g){var b=void 0;try{b=g.then}catch(h){this.reject_(h);return}"function"==typeof b?this.settleSameAsThenable_(b,g):this.fulfill_(g)};a.prototype.reject_=function(g){this.settle_(2,g)};a.prototype.fulfill_=function(g){this.settle_(1,g)};a.prototype.settle_=function(g,b){if(0!=this.state_)throw Error("Cannot settle("+g+", "+b+"): Promise already settled in state"+this.state_);this.state_=g;this.result_=b;2===this.state_&&this.scheduleUnhandledRejectionCheck_();
this.executeOnSettledCallbacks_()};a.prototype.scheduleUnhandledRejectionCheck_=function(){var g=this;e(function(){if(g.notifyUnhandledRejection_()){var b=$jscomp.global.console;"undefined"!==typeof b&&b.error(g.result_)}},1)};a.prototype.notifyUnhandledRejection_=function(){if(this.isRejectionHandled_)return!1;var g=$jscomp.global.CustomEvent,b=$jscomp.global.Event,h=$jscomp.global.dispatchEvent;if("undefined"===typeof h)return!0;"function"===typeof g?g=new g("unhandledrejection",{cancelable:!0}):
"function"===typeof b?g=new b("unhandledrejection",{cancelable:!0}):(g=$jscomp.global.document.createEvent("CustomEvent"),g.initCustomEvent("unhandledrejection",!1,!0,g));g.promise=this;g.reason=this.result_;return h(g)};a.prototype.executeOnSettledCallbacks_=function(){if(null!=this.onSettledCallbacks_){for(var g=0;g<this.onSettledCallbacks_.length;++g)l.asyncExecute(this.onSettledCallbacks_[g]);this.onSettledCallbacks_=null}};var l=new c;a.prototype.settleSameAsPromise_=function(g){var b=this.createResolveAndReject_();
g.callWhenSettled_(b.resolve,b.reject)};a.prototype.settleSameAsThenable_=function(g,b){var h=this.createResolveAndReject_();try{g.call(b,h.resolve,h.reject)}catch(n){h.reject(n)}};a.prototype.then=function(g,b){function h(r,A){return"function"==typeof r?function(q){try{n(r(q))}catch(v){u(v)}}:A}var n,u,x=new a(function(r,A){n=r;u=A});this.callWhenSettled_(h(g,n),h(b,u));return x};a.prototype.catch=function(g){return this.then(void 0,g)};a.prototype.callWhenSettled_=function(g,b){function h(){switch(n.state_){case 1:g(n.result_);
break;case 2:b(n.result_);break;default:throw Error("Unexpected state: "+n.state_);}}var n=this;null==this.onSettledCallbacks_?l.asyncExecute(h):this.onSettledCallbacks_.push(h);this.isRejectionHandled_=!0};a.resolve=f;a.reject=function(g){return new a(function(b,h){h(g)})};a.race=function(g){return new a(function(b,h){for(var n=$jscomp.makeIterator(g),u=n.next();!u.done;u=n.next())f(u.value).callWhenSettled_(b,h)})};a.all=function(g){var b=$jscomp.makeIterator(g),h=b.next();return h.done?f([]):new a(function(n,
u){function x(q){return function(v){r[q]=v;A--;0==A&&n(r)}}var r=[],A=0;do r.push(void 0),A++,f(h.value).callWhenSettled_(x(r.length-1),u),h=b.next();while(!h.done)})};return a},"es6","es3");$jscomp.checkStringArgs=function(d,c,f){if(null==d)throw new TypeError("The 'this' value for String.prototype."+f+" must not be null or undefined");if(c instanceof RegExp)throw new TypeError("First argument to String.prototype."+f+" must not be a regular expression");return d+""};
$jscomp.polyfill("String.prototype.endsWith",function(d){return d?d:function(c,f){var e=$jscomp.checkStringArgs(this,c,"endsWith");c+="";void 0===f&&(f=e.length);f=Math.max(0,Math.min(f|0,e.length));for(var a=c.length;0<a&&0<f;)if(e[--f]!=c[--a])return!1;return 0>=a}},"es6","es3");$jscomp.underscoreProtoCanBeSet=function(){var d={a:!0},c={};try{return c.__proto__=d,c.a}catch(f){}return!1};
$jscomp.setPrototypeOf=$jscomp.TRUST_ES6_POLYFILLS&&"function"==typeof Object.setPrototypeOf?Object.setPrototypeOf:$jscomp.underscoreProtoCanBeSet()?function(d,c){d.__proto__=c;if(d.__proto__!==c)throw new TypeError(d+" is not extensible");return d}:null;$jscomp.assign=$jscomp.TRUST_ES6_POLYFILLS&&"function"==typeof Object.assign?Object.assign:function(d,c){for(var f=1;f<arguments.length;f++){var e=arguments[f];if(e)for(var a in e)$jscomp.owns(e,a)&&(d[a]=e[a])}return d};
(function(d){function c(e){if(f[e])return f[e].exports;var a=f[e]={i:e,l:!1,exports:{}};d[e].call(a.exports,a,a.exports,c);a.l=!0;return a.exports}var f={};c.m=d;c.c=f;c.d=function(e,a,l){c.o(e,a)||Object.defineProperty(e,a,{enumerable:!0,get:l})};c.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});Object.defineProperty(e,"__esModule",{value:!0})};c.t=function(e,a){a&1&&(e=c(e));if(a&8||a&4&&"object"===typeof e&&e&&e.__esModule)return e;
var l=Object.create(null);c.r(l);Object.defineProperty(l,"default",{enumerable:!0,value:e});if(a&2&&"string"!=typeof e)for(var g in e)c.d(l,g,function(b){return e[b]}.bind(null,g));return l};c.n=function(e){var a=e&&e.__esModule?function(){return e["default"]}:function(){return e};c.d(a,"a",a);return a};c.o=function(e,a){return Object.prototype.hasOwnProperty.call(e,a)};c.p="/core/office/";return c(c.s=13)})([function(d,c,f){f.d(c,"b",function(){return a});f.d(c,"a",function(){return l});var e=f(1),
a=function(g,b){Object(e.a)("disableLogs")||(b?console.warn("".concat(g,": ").concat(b)):console.warn(g))},l=function(g,b){}},function(d,c,f){f.d(c,"a",function(){return l});f.d(c,"b",function(){return g});var e={},a={flattenedResources:!1,CANVAS_CACHE_SIZE:void 0,maxPagesBefore:void 0,maxPagesAhead:void 0,disableLogs:!1,wvsQueryParameters:{},_trnDebugMode:!1,_logFiltersEnabled:null},l=function(b){return a[b]},g=function(b,h){var n;a[b]=h;null===(n=e[b])||void 0===n?void 0:n.forEach(function(u){u(h)})}},
function(d,c,f){f.d(c,"a",function(){return y});f.d(c,"b",function(){return z});f.d(c,"c",function(){return F});var e=f(7),a=f(0),l=f(4),g=f(3),b="undefined"===typeof window?self:window,h=b.importScripts,n=!1,u=function(k,p){n||(h("".concat(b.basePath,"decode.min.js")),n=!0);k=self.BrotliDecode(Object(g.b)(k));return p?k:Object(g.a)(k)},x=function(k,p){return Object(e.a)(void 0,void 0,Promise,function(){var t;return Object(e.b)(this,function(B){switch(B.label){case 0:return n?[3,2]:[4,Object(l.a)("".concat(self.Core.getWorkerPath(),
"external/decode.min.js"),"Failed to download decode.min.js",window)];case 1:B.sent(),n=!0,B.label=2;case 2:return t=self.BrotliDecode(Object(g.b)(k)),[2,p?t:Object(g.a)(t)]}})})};(function(){function k(){this.remainingDataArrays=[]}k.prototype.processRaw=function(p){return p};k.prototype.processBrotli=function(p){this.remainingDataArrays.push(p);return null};k.prototype.GetNextChunk=function(p){this.decodeFunction||(this.decodeFunction=0===p[0]&&97===p[1]&&115===p[2]&&109===p[3]?this.processRaw:
this.processBrotli);return this.decodeFunction(p)};k.prototype.End=function(){if(this.remainingDataArrays.length){for(var p=this.arrays,t=0,B=0;B<p.length;++B)t+=p[B].length;t=new Uint8Array(t);var J=0;for(B=0;B<p.length;++B){var m=p[B];t.set(m,J);J+=m.length}return u(t,!0)}return null};return k})();var r=!1,A=function(k){r||(h("".concat(b.basePath,"pako_inflate.min.js")),r=!0);var p=10;if("string"===typeof k){if(k.charCodeAt(3)&8){for(;0!==k.charCodeAt(p);++p);++p}}else if(k[3]&8){for(;0!==k[p];++p);
++p}k=Object(g.b)(k);k=k.subarray(p,k.length-8);return b.pako.inflate(k,{windowBits:-15})},q=function(k,p){return p?k:Object(g.a)(k)},v=function(k){var p=!k.shouldOutputArray,t=new XMLHttpRequest;t.open("GET",k.url,k.isAsync);var B=p&&t.overrideMimeType;t.responseType=B?"text":"arraybuffer";B&&t.overrideMimeType("text/plain; charset=x-user-defined");t.send();var J=function(){var w=Date.now();var D=B?t.responseText:new Uint8Array(t.response);Object(a.a)("worker","Result length is ".concat(D.length));
D.length<k.compressedMaximum?(D=k.decompressFunction(D,k.shouldOutputArray),Object(a.b)("There may be some degradation of performance. Your server has not been configured to serve .gz. and .br. files with the expected Content-Encoding. See https://docs.apryse.com/documentation/web/faq/content-encoding/ for instructions on how to resolve this."),h&&Object(a.a)("worker","Decompressed length is ".concat(D.length))):p&&(D=Object(g.a)(D));h&&Object(a.a)("worker","".concat(k.url," Decompression took ").concat(Date.now()-
w));return D};if(k.isAsync)var m=new Promise(function(w,D){t.onload=function(){200===this.status||0===this.status?w(J()):D("Download Failed ".concat(k.url))};t.onerror=function(){D("Network error occurred ".concat(k.url))}});else{if(200===t.status||0===t.status)return J();throw Error("Failed to load ".concat(k.url));}return m},y=function(k){var p=k.lastIndexOf("/");-1===p&&(p=0);var t=k.slice(p).replace(".",".br.");h||(t.endsWith(".js.mem")?t=t.replace(".js.mem",".mem"):t.endsWith(".js")&&(t=t.concat(".mem")));
return k.slice(0,p)+t},H=function(k,p){var t=k.lastIndexOf("/");-1===t&&(t=0);var B=k.slice(t).replace(".",".gz.");p.url=k.slice(0,t)+B;p.decompressFunction=A;return v(p)},E=function(k,p){p.url=y(k);p.decompressFunction=h?u:x;return v(p)},I=function(k,p){k.endsWith(".js.mem")?k=k.slice(0,-4):k.endsWith(".mem")&&(k="".concat(k.slice(0,-4),".js.mem"));p.url=k;p.decompressFunction=q;return v(p)},K=function(k,p,t,B){return k.catch(function(J){Object(a.b)(J);return B(p,t)})},C=function(k,p,t){var B;if(t.isAsync){var J=
p[0](k,t);for(B=1;B<p.length;++B)J=K(J,k,t,p[B]);return J}for(B=0;B<p.length;++B)try{return p[B](k,t)}catch(m){Object(a.b)(m.message)}throw Error("");},F=function(k,p,t,B){return C(k,[H,E,I],{compressedMaximum:p,isAsync:t,shouldOutputArray:B})},z=function(k,p,t,B){return C(k,[E,H,I],{compressedMaximum:p,isAsync:t,shouldOutputArray:B})}},function(d,c,f){f.d(c,"b",function(){return e});f.d(c,"a",function(){return a});var e=function(l){if("string"===typeof l){for(var g=new Uint8Array(l.length),b=l.length,
h=0;h<b;h++)g[h]=l.charCodeAt(h);return g}return l},a=function(l){if("string"!==typeof l){for(var g="",b=0,h=l.length,n;b<h;)n=l.subarray(b,b+1024),b+=1024,g+=String.fromCharCode.apply(null,n);return g}return l}},function(d,c,f){function e(l,g,b){return new Promise(function(h){if(!l)return h();var n=b.document.createElement("script");n.type="text/javascript";n.onload=function(){h()};n.onerror=function(){g&&Object(a.b)(g);h()};n.src=l;b.document.getElementsByTagName("head")[0].appendChild(n)})}f.d(c,
"a",function(){return e});var a=f(0)},function(d,c,f){function e(b,h,n){function u(A){r=r||Date.now();return A?(Object(a.a)("load","Try instantiateStreaming"),fetch(Object(l.a)(b)).then(function(q){return WebAssembly.instantiateStreaming(q,h)}).catch(function(q){Object(a.a)("load","instantiateStreaming Failed ".concat(b," message ").concat(q.message));return u(!1)})):Object(l.b)(b,n,!0,!0).then(function(q){x=Date.now();Object(a.a)("load","Request took ".concat(x-r," ms"));return WebAssembly.instantiate(q,
h)})}var x,r;return u(!!WebAssembly.instantiateStreaming).then(function(A){Object(a.a)("load","WASM compilation took ".concat(Date.now()-(x||r)," ms"));return A})}f.d(c,"a",function(){return e});var a=f(0),l=f(2),g=f(4);f.d(c,"b",function(){return g.a})},function(d,c){c=function(){return this}();try{c=c||(new Function("return this"))()}catch(f){"object"===typeof window&&(c=window)}d.exports=c},function(d,c,f){function e(l,g,b,h){function n(u){return u instanceof b?u:new b(function(x){x(u)})}return new (b||
(b=Promise))(function(u,x){function r(v){try{q(h.next(v))}catch(y){x(y)}}function A(v){try{q(h["throw"](v))}catch(y){x(y)}}function q(v){v.done?u(v.value):n(v.value).then(r,A)}q((h=h.apply(l,g||[])).next())})}function a(l,g){function b(q){return function(v){return h([q,v])}}function h(q){if(u)throw new TypeError("Generator is already executing.");for(;A&&(A=0,q[0]&&(n=0)),n;)try{if(u=1,x&&(r=q[0]&2?x["return"]:q[0]?x["throw"]||((r=x["return"])&&r.call(x),0):x.next)&&!(r=r.call(x,q[1])).done)return r;
if(x=0,r)q=[q[0]&2,r.value];switch(q[0]){case 0:case 1:r=q;break;case 4:return n.label++,{value:q[1],done:!1};case 5:n.label++;x=q[1];q=[0];continue;case 7:q=n.ops.pop();n.trys.pop();continue;default:if(!(r=n.trys,r=0<r.length&&r[r.length-1])&&(6===q[0]||2===q[0])){n=0;continue}if(3===q[0]&&(!r||q[1]>r[0]&&q[1]<r[3]))n.label=q[1];else if(6===q[0]&&n.label<r[1])n.label=r[1],r=q;else if(r&&n.label<r[2])n.label=r[2],n.ops.push(q);else{r[2]&&n.ops.pop();n.trys.pop();continue}}q=g.call(l,n)}catch(v){q=
[6,v],x=0}finally{u=r=0}if(q[0]&5)throw q[1];return{value:q[0]?q[1]:void 0,done:!0}}var n={label:0,sent:function(){if(r[0]&1)throw r[1];return r[1]},trys:[],ops:[]},u,x,r,A;return A={next:b(0),"throw":b(1),"return":b(2)},"function"===typeof Symbol&&(A[Symbol.iterator]=function(){return this}),A}f.d(c,"a",function(){return e});f.d(c,"b",function(){return a})},function(d,c,f){c.a=function(){ArrayBuffer.prototype.slice||(ArrayBuffer.prototype.slice=function(e,a){void 0===e&&(e=0);void 0===a&&(a=this.byteLength);
e=Math.floor(e);a=Math.floor(a);0>e&&(e+=this.byteLength);0>a&&(a+=this.byteLength);e=Math.min(Math.max(0,e),this.byteLength);a=Math.min(Math.max(0,a),this.byteLength);if(0>=a-e)return new ArrayBuffer(0);var l=new ArrayBuffer(a-e),g=new Uint8Array(l);e=new Uint8Array(this,e,a-e);g.set(e);return l})}},function(d,c,f){f.d(c,"a",function(){return e});f(10);var e=function(a,l){return function(){}}},function(d,c,f){c.a=function(e){var a={};decodeURIComponent(e.slice(1)).split("&").forEach(function(l){l=
l.split("=",2);a[l[0]]=l[1]});return a}},function(d,c,f){f.d(c,"a",function(){return b});var e=f(2),a=f(5),l=f(12),g=function(){function h(n){var u=this;this.promise=n.then(function(x){u.response=x;u.status=200})}h.prototype.addEventListener=function(n,u){this.promise.then(u)};return h}(),b=function(h,n,u){if(Object(l.a)()&&!u){self.Module.instantiateWasm=function(r,A){return Object(a.a)("".concat(h,"Wasm.wasm"),r,n["Wasm.wasm"]).then(function(q){A(q.instance)})};if(n.disableObjectURLBlobs){importScripts("".concat(h,
"Wasm.js"));return}u=Object(e.b)("".concat(h,"Wasm.js.mem"),n["Wasm.js.mem"],!1,!1)}else{if(n.disableObjectURLBlobs){importScripts("".concat((self.Module.asmjsPrefix?self.Module.asmjsPrefix:"")+h,".js"));return}u=Object(e.b)("".concat((self.Module.asmjsPrefix?self.Module.asmjsPrefix:"")+h,".js.mem"),n[".js.mem"],!1);var x=Object(e.c)("".concat((self.Module.memoryInitializerPrefixURL?self.Module.memoryInitializerPrefixURL:"")+h,".mem"),n[".mem"],!0,!0);self.Module.memoryInitializerRequest=new g(x)}u=
new Blob([u],{type:"application/javascript"});importScripts(URL.createObjectURL(u))}},function(d,c,f){f.d(c,"a",function(){return q});var e,a="undefined"===typeof window?self:window;d=function(){var v=navigator.userAgent.toLowerCase();return(v=/(msie) ([\w.]+)/.exec(v)||/(trident)(?:.*? rv:([\w.]+)|)/.exec(v))?parseInt(v[2],10):v}();var l=function(){var v=a.navigator.userAgent.match(/OPR/),y=a.navigator.userAgent.match(/Maxthon/),H=a.navigator.userAgent.match(/Edge/);return a.navigator.userAgent.match(/Chrome\/(.*?) /)&&
!v&&!y&&!H}();(function(){if(!l)return null;var v=a.navigator.userAgent.match(/Chrome\/([0-9]+)\./);return v?parseInt(v[1],10):v})();var g=!!navigator.userAgent.match(/Edge/i)||navigator.userAgent.match(/Edg\/(.*?)/)&&a.navigator.userAgent.match(/Chrome\/(.*?) /);(function(){if(!g)return null;var v=a.navigator.userAgent.match(/Edg\/([0-9]+)\./);return v?parseInt(v[1],10):v})();c=/iPad|iPhone|iPod/.test(a.navigator.platform)||"MacIntel"===navigator.platform&&1<navigator.maxTouchPoints||/iPad|iPhone|iPod/.test(a.navigator.userAgent);
var b=function(){var v=a.navigator.userAgent.match(/.*\/([0-9\.]+)\s(Safari|Mobile).*/i);return v?parseFloat(v[1]):v}(),h=/^((?!chrome|android).)*safari/i.test(a.navigator.userAgent)||/^((?!chrome|android).)*$/.test(a.navigator.userAgent)&&c;h&&/^((?!chrome|android).)*safari/i.test(navigator.userAgent)&&parseInt(null===(e=navigator.userAgent.match(/Version\/(\d+)/))||void 0===e?void 0:e[1],10);var n=a.navigator.userAgent.match(/Firefox/);(function(){if(!n)return null;var v=a.navigator.userAgent.match(/Firefox\/([0-9]+)\./);
return v?parseInt(v[1],10):v})();d||/Android|webOS|Touch|IEMobile|Silk/i.test(navigator.userAgent);navigator.userAgent.match(/(iPad|iPhone|iPod)/i);a.navigator.userAgent.indexOf("Android");var u=/Mac OS X 10_13_6.*\(KHTML, like Gecko\)$/.test(a.navigator.userAgent),x=a.navigator.userAgent.match(/(iPad|iPhone).+\sOS\s((\d+)(_\d)*)/i)?14<=parseInt(a.navigator.userAgent.match(/(iPad|iPhone).+\sOS\s((\d+)(_\d)*)/i)[3],10):!1,r=!(!self.WebAssembly||!self.WebAssembly.validate),A=-1<a.navigator.userAgent.indexOf("Edge/16")||
-1<a.navigator.userAgent.indexOf("MSAppHost"),q=function(){return r&&!A&&!(!x&&(h&&14>b||u))}},function(d,c,f){d.exports=f(14)},function(d,c,f){f.r(c);f(15);f(20);d=f(8);f(21);Object(d.a)()},function(d,c,f){(function(e,a){function l(g){"@babel/helpers - typeof";return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(b){return typeof b}:function(b){return b&&"function"==typeof Symbol&&b.constructor===Symbol&&b!==Symbol.prototype?"symbol":typeof b},l(g)}(function(g){function b(){for(var m=
0;m<B.length;m++)B[m][0](B[m][1]);B=[];J=!1}function h(m,w){B.push([m,w]);J||(J=!0,t(b,0))}function n(m,w){function D(G){r(w,G)}function L(G){q(w,G)}try{m(D,L)}catch(G){L(G)}}function u(m){var w=m.owner,D=w.state_;w=w.data_;var L=m[D];m=m.then;if("function"===typeof L){D=z;try{w=L(w)}catch(G){q(m,G)}}x(m,w)||(D===z&&r(m,w),D===k&&q(m,w))}function x(m,w){var D;try{if(m===w)throw new TypeError("A promises callback cannot return that same promise.");if(w&&("function"===typeof w||"object"===l(w))){var L=
w.then;if("function"===typeof L)return L.call(w,function(G){D||(D=!0,w!==G?r(m,G):A(m,G))},function(G){D||(D=!0,q(m,G))}),!0}}catch(G){return D||q(m,G),!0}return!1}function r(m,w){m!==w&&x(m,w)||A(m,w)}function A(m,w){m.state_===C&&(m.state_=F,m.data_=w,h(y,m))}function q(m,w){m.state_===C&&(m.state_=F,m.data_=w,h(H,m))}function v(m){var w=m.then_;m.then_=void 0;for(m=0;m<w.length;m++)u(w[m])}function y(m){m.state_=z;v(m)}function H(m){m.state_=k;v(m)}function E(m){if("function"!==typeof m)throw new TypeError("Promise constructor takes a function argument");
if(!(this instanceof E))throw new TypeError("Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.");this.then_=[];n(m,this)}g.createPromiseCapability=function(){var m={};m.promise=new E(function(w,D){m.resolve=w;m.reject=D});return m};var I=g.Promise,K=I&&"resolve"in I&&"reject"in I&&"all"in I&&"race"in I&&function(){var m;new I(function(w){m=w});return"function"===typeof m}();"undefined"!==typeof exports&&exports?(exports.Promise=K?
I:E,exports.Polyfill=E):"function"===typeof define&&f(19)?define(function(){return K?I:E}):K||(g.Promise=E);var C="pending",F="sealed",z="fulfilled",k="rejected",p=function(){},t="undefined"!==typeof a?a:setTimeout,B=[],J;E.prototype={constructor:E,state_:C,then_:null,data_:void 0,then:function(m,w){m={owner:this,then:new this.constructor(p),fulfilled:m,rejected:w};this.state_===z||this.state_===k?h(u,m):this.then_.push(m);return m.then},"catch":function(m){return this.then(null,m)}};E.all=function(m){if("[object Array]"!==
Object.prototype.toString.call(m))throw new TypeError("You must pass an array to Promise.all().");return new this(function(w,D){function L(P){O++;return function(Q){G[P]=Q;--O||w(G)}}for(var G=[],O=0,M=0,N;M<m.length;M++)(N=m[M])&&"function"===typeof N.then?N.then(L(M),D):G[M]=N;O||w(G)})};E.race=function(m){if("[object Array]"!==Object.prototype.toString.call(m))throw new TypeError("You must pass an array to Promise.race().");return new this(function(w,D){for(var L=0,G;L<m.length;L++)(G=m[L])&&"function"===
typeof G.then?G.then(w,D):w(G)})};E.resolve=function(m){return m&&"object"===l(m)&&m.constructor===this?m:new this(function(w){w(m)})};E.reject=function(m){return new this(function(w,D){D(m)})}})("undefined"!==typeof window?window:"undefined"!==typeof e?e:"undefined"!==typeof self?self:void 0)}).call(this,f(6),f(16).setImmediate)},function(d,c,f){(function(e){function a(b,h){this._id=b;this._clearFn=h}var l="undefined"!==typeof e&&e||"undefined"!==typeof self&&self||window,g=Function.prototype.apply;
c.setTimeout=function(){return new a(g.call(setTimeout,l,arguments),clearTimeout)};c.setInterval=function(){return new a(g.call(setInterval,l,arguments),clearInterval)};c.clearTimeout=c.clearInterval=function(b){b&&b.close()};a.prototype.unref=a.prototype.ref=function(){};a.prototype.close=function(){this._clearFn.call(l,this._id)};c.enroll=function(b,h){clearTimeout(b._idleTimeoutId);b._idleTimeout=h};c.unenroll=function(b){clearTimeout(b._idleTimeoutId);b._idleTimeout=-1};c._unrefActive=c.active=
function(b){clearTimeout(b._idleTimeoutId);var h=b._idleTimeout;0<=h&&(b._idleTimeoutId=setTimeout(function(){b._onTimeout&&b._onTimeout()},h))};f(17);c.setImmediate="undefined"!==typeof self&&self.setImmediate||"undefined"!==typeof e&&e.setImmediate||this&&this.setImmediate;c.clearImmediate="undefined"!==typeof self&&self.clearImmediate||"undefined"!==typeof e&&e.clearImmediate||this&&this.clearImmediate}).call(this,f(6))},function(d,c,f){(function(e,a){(function(l,g){function b(C){delete y[C]}function h(C){if(H)setTimeout(h,
0,C);else{var F=y[C];if(F){H=!0;try{var z=F.callback,k=F.args;switch(k.length){case 0:z();break;case 1:z(k[0]);break;case 2:z(k[0],k[1]);break;case 3:z(k[0],k[1],k[2]);break;default:z.apply(g,k)}}finally{b(C),H=!1}}}}function n(){I=function(C){a.nextTick(function(){h(C)})}}function u(){if(l.postMessage&&!l.importScripts){var C=!0,F=l.onmessage;l.onmessage=function(){C=!1};l.postMessage("","*");l.onmessage=F;return C}}function x(){var C="setImmediate$"+Math.random()+"$",F=function(z){z.source===l&&
"string"===typeof z.data&&0===z.data.indexOf(C)&&h(+z.data.slice(C.length))};l.addEventListener?l.addEventListener("message",F,!1):l.attachEvent("onmessage",F);I=function(z){l.postMessage(C+z,"*")}}function r(){var C=new MessageChannel;C.port1.onmessage=function(F){h(F.data)};I=function(F){C.port2.postMessage(F)}}function A(){var C=E.documentElement;I=function(F){var z=E.createElement("script");z.onreadystatechange=function(){h(F);z.onreadystatechange=null;C.removeChild(z);z=null};C.appendChild(z)}}
function q(){I=function(C){setTimeout(h,0,C)}}if(!l.setImmediate){var v=1,y={},H=!1,E=l.document,I,K=Object.getPrototypeOf&&Object.getPrototypeOf(l);K=K&&K.setTimeout?K:l;"[object process]"==={}.toString.call(l.process)?n():u()?x():l.MessageChannel?r():E&&"onreadystatechange"in E.createElement("script")?A():q();K.setImmediate=function(C){"function"!==typeof C&&(C=new Function(""+C));for(var F=Array(arguments.length-1),z=0;z<F.length;z++)F[z]=arguments[z+1];y[v]={callback:C,args:F};I(v);return v++};
K.clearImmediate=b}})("undefined"===typeof self?"undefined"===typeof e?this:e:self)}).call(this,f(6),f(18))},function(d,c){function f(){throw Error("setTimeout has not been defined");}function e(){throw Error("clearTimeout has not been defined");}function a(y){if(u===setTimeout)return setTimeout(y,0);if((u===f||!u)&&setTimeout)return u=setTimeout,setTimeout(y,0);try{return u(y,0)}catch(H){try{return u.call(null,y,0)}catch(E){return u.call(this,y,0)}}}function l(y){if(x===clearTimeout)return clearTimeout(y);
if((x===e||!x)&&clearTimeout)return x=clearTimeout,clearTimeout(y);try{return x(y)}catch(H){try{return x.call(null,y)}catch(E){return x.call(this,y)}}}function g(){A&&q&&(A=!1,q.length?r=q.concat(r):v=-1,r.length&&b())}function b(){if(!A){var y=a(g);A=!0;for(var H=r.length;H;){q=r;for(r=[];++v<H;)q&&q[v].run();v=-1;H=r.length}q=null;A=!1;l(y)}}function h(y,H){this.fun=y;this.array=H}function n(){}d=d.exports={};try{var u="function"===typeof setTimeout?setTimeout:f}catch(y){u=f}try{var x="function"===
typeof clearTimeout?clearTimeout:e}catch(y){x=e}var r=[],A=!1,q,v=-1;d.nextTick=function(y){var H=Array(arguments.length-1);if(1<arguments.length)for(var E=1;E<arguments.length;E++)H[E-1]=arguments[E];r.push(new h(y,H));1!==r.length||A||a(b)};h.prototype.run=function(){this.fun.apply(null,this.array)};d.title="browser";d.browser=!0;d.env={};d.argv=[];d.version="";d.versions={};d.on=n;d.addListener=n;d.once=n;d.off=n;d.removeListener=n;d.removeAllListeners=n;d.emit=n;d.prependListener=n;d.prependOnceListener=
n;d.listeners=function(y){return[]};d.binding=function(y){throw Error("process.binding is not supported");};d.cwd=function(){return"/"};d.chdir=function(y){throw Error("process.chdir is not supported");};d.umask=function(){return 0}},function(d,c){d.exports={}},function(d,c,f){(function(e){"undefined"===typeof e.crypto&&(e.crypto={getRandomValues:function(a){for(var l=0;l<a.length;l++)a[l]=256*Math.random()}})})("undefined"===typeof window?self:window)},function(d,c,f){function e(b){"@babel/helpers - typeof";
return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(h){return typeof h}:function(h){return h&&"function"==typeof Symbol&&h.constructor===Symbol&&h!==Symbol.prototype?"symbol":typeof h},e(b)}var a=f(9),l=f(11),g=null;(function(b){function h(y){q||(q=[]);q.push(y)}var n,u,x,r,A=!1,q=[],v=function(){function y(){n=function(){}}function H(z){var k=[];return{resource_array:k,msg:JSON.stringify(z.data,function(p,t){if("object"===e(t)&&(p=null,t instanceof Uint8Array?p=t:t instanceof
ArrayBuffer&&(p=new Uint8Array(t)),p)){t=x(p.length);var B=r(t);B&&(new Uint8Array(Module.HEAPU8.buffer,B,p.length)).set(p);k.push(t);return{__trn_res_id:t}}return t})}}function E(){A=!0;postMessage({type:"abort",data:{error:"Office worker has terminated unexpectedly"}})}function I(z){if(!A)try{var k=H(z);u(k.msg)}catch(p){E(p)}}b.basePath="../";var K=b.officeWorkerPath||"";b.workerBasePath&&(b.basePath=b.workerBasePath);b.basePath=b.externalPath?b.externalPath:b.basePath+"external/";importScripts("".concat(b.basePath,
"Promise.js"));b.ContinueFunc=function(z){n("ContinueFunc called");setTimeout(function(){onmessage({data:{action:"continue"}})},z)};if(b.pdfWorkerPath)var C=b.pdfWorkerPath;if(b.officeAsmPath)var F=b.officeAsmPath;b.Module={memoryInitializerPrefixURL:C,asmjsPrefix:F,onRuntimeInitialized:function(){n||y();var z=Date.now()-g;Object(a.a)("load","time duration from start to ready: ".concat(JSON.stringify(z)));u=function(k){if(null!==k&&void 0!==k&&0!==k&&!A){var p=(k.length<<2)+1,t=Module._malloc(p);
0<stringToUTF8(k,t,p)&&Module._TRN_OnMessage(t)}};x=function(k){return Module._TRN_CreateBufferResource(k)};r=function(k){return Module._TRN_GetResourcePointer(k)};n("OnReady called");onmessage=I;Module._TRN_InitWorker();for(z=0;z<q.length;++z)onmessage(q[z]);q=null},fetchSelf:function(){g=Date.now();Object(l.a)("".concat(K,"WebOfficeWorker"),{"Wasm.wasm":5E6,"Wasm.js.mem":1E5,".js.mem":5E6,".mem":3E6,disableObjectURLBlobs:b.disableObjectURLBlobs},!!navigator.userAgent.match(/Edge/i)||b.wasmDisabled)},
onAbort:E,noExitRuntime:!0}};b.onmessage=function(y){"init"===y.data.action&&(b.wasmDisabled=!y.data.wasm,b.externalPath=y.data.externalPath,b.officeAsmPath=y.data.officeAsmPath,b.pdfWorkerPath=y.data.pdfWorkerPath,b.disableObjectURLBlobs=y.data.disableObjectURLBlobs,b.onmessage=h,v(),b.Module.fetchSelf())}})("undefined"===typeof window?self:window)}]);}).call(this || window)
