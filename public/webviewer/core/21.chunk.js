/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[21],{590:function(xa,ta,h){h.r(ta);var qa=h(0),oa=h(7),na=h(3);xa=h(58);var ja=h(34),ka=h(14);h=function(){function ha(){this.init()}ha.prototype.init=function(){this.qoa=!1;this.Kg=this.bp=this.connection=null;this.kl={};this.fa=this.hO=null};ha.prototype.aO=function(x){for(var z=this,r=0;r<x.length;++r){var n=x[r];switch(n.at){case "create":this.kl[n.author]||(this.kl[n.author]=n.aName);this.Lya(n);break;case "modify":this.fa.ns(n.xfdf).then(function(f){z.fa.Hb(f[0])});
break;case "delete":n="<delete><id>".concat(n.aId,"</id></delete>"),this.fa.ns(n)}}};ha.prototype.Lya=function(x){var z=this;this.fa.ns(x.xfdf).then(function(r){r=r[0];r.authorId=x.author;z.fa.Hb(r);z.fa.trigger(oa.c.UPDATE_ANNOTATION_PERMISSION,[r])})};ha.prototype.Zxa=function(x,z,r){this.bp&&this.bp(x,z,r)};ha.prototype.preloadAnnotations=function(x){this.addEventListener("webViewerServerAnnotationsEnabled",this.Zxa.bind(this,x,"add",{imported:!1}),{once:!0})};ha.prototype.initiateCollaboration=
function(x,z,r){var n=this;if(x){n.Kg=z;n.fa=r.ma();r.addEventListener(oa.h.DOCUMENT_UNLOADED,function(){n.disableCollaboration()});n.NLa(x);var f=new XMLHttpRequest;f.addEventListener("load",function(){if(200===f.status&&0<f.responseText.length)try{var b=JSON.parse(f.responseText);n.connection=exports.da.aNa(Object(ja.k)(n.Kg,"blackbox/"),"annot");n.hO=b.id;n.kl[b.id]=b.user_name;n.fa.UX(b.id);n.connection.zQa(function(a){a.t&&a.t.startsWith("a_")&&a.data&&n.aO(a.data)},function(){n.connection.send({t:"a_retrieve",
dId:x});n.trigger(ha.Events.WEBVIEWER_SERVER_ANNOTATIONS_ENABLED,[n.kl[b.id],n.hO])},function(){n.disableCollaboration()})}catch(a){Object(na.g)(a.message)}});f.open("GET",Object(ja.k)(this.Kg,"demo/SessionInfo.jsp"));f.withCredentials=!0;f.send();n.qoa=!0;n.fa.Uca(function(b){return n.kl[b.Author]||b.Author})}else Object(na.g)("Document ID required for collaboration")};ha.prototype.disableCollaboration=function(){this.bp&&(this.fa.removeEventListener(ka.a.Events.ANNOTATION_CHANGED,this.bp),this.bp=
null);this.connection&&this.connection.ru();this.fa&&this.fa.UX("Guest");this.init();this.trigger(ha.Events.WEBVIEWER_SERVER_ANNOTATIONS_DISABLED)};ha.prototype.NLa=function(x){var z=this;this.bp&&this.fa.removeEventListener(ka.a.Events.ANNOTATION_CHANGED,this.bp);this.bp=function(r,n,f){return Object(qa.b)(this,void 0,void 0,function(){var b,a,e,w,y,ba,fa,aa,ea;return Object(qa.d)(this,function(ca){switch(ca.label){case 0:if(f.imported)return[2];b={t:"a_".concat(n),dId:x,annots:[]};return[4,z.fa.J5()];
case 1:a=ca.aa();"delete"!==n&&(e=(new DOMParser).parseFromString(a,"text/xml"),w=new XMLSerializer);for(y=0;y<r.length;y++)ba=r[y],aa=fa=void 0,"add"===n?(fa=e.querySelector('[name="'.concat(ba.Id,'"]')),aa=w.serializeToString(fa),ea=null,ba.InReplyTo&&(ea=z.fa.gh(ba.InReplyTo).authorId||"default"),b.annots.push({at:"create",aId:ba.Id,author:z.hO,aName:z.kl[z.hO],parent:ea,xfdf:"<add>".concat(aa,"</add>")})):"modify"===n?(fa=e.querySelector('[name="'.concat(ba.Id,'"]')),aa=w.serializeToString(fa),
b.annots.push({at:"modify",aId:ba.Id,xfdf:"<modify>".concat(aa,"</modify>")})):"delete"===n&&b.annots.push({at:"delete",aId:ba.Id});0<b.annots.length&&z.connection.send(b);return[2]}})})}.bind(z);this.fa.addEventListener(ka.a.Events.ANNOTATION_CHANGED,this.bp)};ha.Events={WEBVIEWER_SERVER_ANNOTATIONS_ENABLED:"webViewerServerAnnotationsEnabled",WEBVIEWER_SERVER_ANNOTATIONS_DISABLED:"webViewerServerAnnotationsDisabled"};return ha}();Object(xa.a)(h);ta["default"]=h}}]);}).call(this || window)
