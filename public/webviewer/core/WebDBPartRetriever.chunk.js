/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[12],{583:function(xa,ta,h){h.r(ta);var qa=h(0),oa=h(1);h.n(oa);xa=h(121);h=h(494);xa=function(na){function ja(ka,ha,x){ha=na.call(this,ka,ha,x)||this;ha.db=ka;return ha}Object(qa.c)(ja,na);ja.prototype.request=function(ka){var ha=this;Object(oa.each)(ka,function(x){ha.db.get(x,function(z,r,n){return z?ha.trigger("partReady.partRetriever",{Kb:x,error:z}):ha.trigger("partReady.partRetriever",{Kb:x,data:r,sl:!1,ni:!1,error:null,ue:n})})})};
ja.prototype.Vy=function(ka){ka()};return ja}(xa.a);Object(h.a)(xa);Object(h.b)(xa);ta["default"]=xa}}]);}).call(this || window)
