/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[8],{574:function(xa,ta,h){h.r(ta);var qa=h(0);xa=h(58);var oa=h(494),na=h(271),ja=h(28),ka=window;h=function(){function ha(x){var z=this;this.Rza=function(r){return r&&("image"===r.type.split("/")[0].toLowerCase()||r.name&&!!r.name.match(/.(jpg|jpeg|png|gif)$/i))};this.file=x;this.eAa=new Promise(function(r){return Object(qa.b)(z,void 0,void 0,function(){var n;return Object(qa.d)(this,function(f){switch(f.label){case 0:return this.Rza(this.file)?
[4,Object(na.b)(x)]:[3,2];case 1:n=f.aa(),this.file=ja.q?new Blob([n],{type:x.type}):new File([n],null===x||void 0===x?void 0:x.name,{type:x.type}),f.label=2;case 2:return r(!0),[2]}})})})}ha.prototype.getFileData=function(x){var z=this,r=new FileReader;r.onload=function(n){z.trigger(ha.Events.DOCUMENT_LOADING_PROGRESS,[n.loaded,n.loaded]);x(new Uint8Array(n.target.result))};r.onprogress=function(n){n.lengthComputable&&z.trigger(ha.Events.DOCUMENT_LOADING_PROGRESS,[n.loaded,0<n.total?n.total:0])};
r.readAsArrayBuffer(this.file)};ha.prototype.getFile=function(){return Object(qa.b)(this,void 0,Promise,function(){return Object(qa.d)(this,function(x){switch(x.label){case 0:return[4,this.eAa];case 1:return x.aa(),ka.da.isJSWorker?[2,this.file.path]:[2,this.file]}})})};ha.Events={DOCUMENT_LOADING_PROGRESS:"documentLoadingProgress"};return ha}();Object(xa.a)(h);Object(oa.a)(h);Object(oa.b)(h);ta["default"]=h}}]);}).call(this || window)
