/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[2],{576:function(xa,ta,h){h.r(ta);xa=h(58);h=h(494);var qa=function(){function oa(na){this.buffer=na;this.fileSize=null===na||void 0===na?void 0:na.byteLength}oa.prototype.getFileData=function(na){na(new Uint8Array(this.buffer))};oa.prototype.getFile=function(){return Promise.resolve(null)};return oa}();Object(xa.a)(qa);Object(h.a)(qa);Object(h.b)(qa);ta["default"]=qa}}]);}).call(this || window)
