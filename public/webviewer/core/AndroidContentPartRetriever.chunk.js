/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[1],{575:function(xa,ta,h){h.r(ta);var qa=h(0),oa=h(326);xa=h(571);h=h(494);var na=window,ja=function(ka){function ha(x,z){var r=ka.call(this,x,z)||this;r.url=x;r.range=z;r.request=new XMLHttpRequest;r.request.open("GET",r.url,!0);na.Uint8Array&&(r.request.responseType="arraybuffer");r.request.setRequestHeader("X-Requested-With","XMLHttpRequest");r.status=oa.a.NOT_STARTED;return r}Object(qa.c)(ha,ka);return ha}(xa.ByteRangeRequest);
xa=function(ka){function ha(x,z,r,n){x=ka.call(this,x,z,r,n)||this;x.XE=ja;return x}Object(qa.c)(ha,ka);ha.prototype.gC=function(x,z){return"".concat(x,"/bytes=").concat(z.start,",").concat(z.stop?z.stop:"")};return ha}(xa["default"]);Object(h.a)(xa);Object(h.b)(xa);ta["default"]=xa}}]);}).call(this || window)
