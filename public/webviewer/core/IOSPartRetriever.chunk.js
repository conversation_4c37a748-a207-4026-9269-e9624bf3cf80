/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[6],{579:function(xa,ta,h){h.r(ta);var qa=h(0),oa=h(326);xa=h(571);h(34);h=h(494);var na=function(ja){function ka(ha,x){var z=ja.call(this,ha,x)||this;z.url=ha;z.range=x;z.status=oa.a.NOT_STARTED;return z}Object(qa.c)(ka,ja);ka.prototype.start=function(){var ha=document.createElement("IFRAME");ha.setAttribute("src",this.url);document.documentElement.appendChild(ha);ha.parentNode.removeChild(ha);this.status=oa.a.STARTED;this.GK()};
return ka}(xa.ByteRangeRequest);xa=function(ja){function ka(ha,x,z,r){ha=ja.call(this,ha,x,z,r)||this;ha.XE=na;return ha}Object(qa.c)(ka,ja);ka.prototype.gC=function(ha,x){return"".concat(ha,"#").concat(x.start,"&").concat(x.stop?x.stop:"")};return ka}(xa["default"]);Object(h.a)(xa);Object(h.b)(xa);ta["default"]=xa}}]);}).call(this || window)
