/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[5],{578:function(xa,ta,h){h.r(ta);var qa=h(0);xa=h(58);var oa=h(216),na=h(494),ja=h(271),ka=window;h=function(){function ha(x,z){this.Z8=function(r){r=r.split(".");return r[r.length-1].match(/(jpg|jpeg|png|gif)$/i)};z=z||{};this.url=x;this.filename=z.filename||x;this.Lf=z.customHeaders;this.ALa=!!z.useDownloader;this.withCredentials=!!z.withCredentials}ha.prototype.rN=function(x){this.Lf=x};ha.prototype.getCustomHeaders=function(){return this.Lf};
ha.prototype.getFileData=function(x){var z=this,r=this,n=new XMLHttpRequest,f=0===this.url.indexOf("blob:")?"blob":"arraybuffer";n.open("GET",this.url,!0);n.withCredentials=this.withCredentials;n.responseType=f;this.Lf&&Object.keys(this.Lf).forEach(function(a){n.setRequestHeader(a,z.Lf[a])});var b=/^https?:/i.test(this.url);n.addEventListener("load",function(a){return Object(qa.b)(this,void 0,void 0,function(){var e,w,y,ba,fa,aa;return Object(qa.d)(this,function(ea){switch(ea.label){case 0:if(200!==
this.status&&(b||0!==this.status))return[3,10];r.trigger(ha.Events.DOCUMENT_LOADING_PROGRESS,[a.loaded,a.loaded]);if("blob"!==this.responseType)return[3,4];e=this.response;return r.Z8(r.filename)?[4,Object(ja.b)(e)]:[3,2];case 1:return w=ea.aa(),r.fileSize=w.byteLength,x(new Uint8Array(w)),[3,3];case 2:y=new FileReader,y.onload=function(ca){ca=new Uint8Array(ca.target.result);r.fileSize=ca.length;x(ca)},y.readAsArrayBuffer(e),ea.label=3;case 3:return[3,9];case 4:ea.xd.push([4,8,,9]);ba=new Uint8Array(this.response);
if(!r.Z8(r.filename))return[3,6];e=new Blob([ba.buffer]);return[4,Object(ja.b)(e)];case 5:return w=ea.aa(),r.fileSize=w.byteLength,x(new Uint8Array(w)),[3,7];case 6:r.fileSize=ba.length,x(ba),ea.label=7;case 7:return[3,9];case 8:return ea.aa(),r.trigger(ha.Events.ERROR,["pdfLoad","Out of memory"]),[3,9];case 9:return[3,11];case 10:fa=a.currentTarget,aa=Object(oa.b)(fa),r.trigger(ha.Events.ERROR,["pdfLoad","".concat(this.status," ").concat(fa.statusText),aa]),ea.label=11;case 11:return r.zG=null,[2]}})})},
!1);n.onprogress=function(a){r.trigger(ha.Events.DOCUMENT_LOADING_PROGRESS,[a.loaded,0<a.total?a.total:0])};n.addEventListener("error",function(){r.trigger(ha.Events.ERROR,["pdfLoad","Network failure"]);r.zG=null},!1);n.send();this.zG=n};ha.prototype.getFile=function(){var x=this;return new Promise(function(z){ka.da.isJSWorker&&z(x.url);if(x.ALa){var r=Object(qa.a)({url:x.url},x.Lf?{customHeaders:x.Lf}:{});z(r)}z(null)})};ha.prototype.abort=function(){this.zG&&(this.zG.abort(),this.zG=null)};ha.Events=
{DOCUMENT_LOADING_PROGRESS:"documentLoadingProgress",ERROR:"error"};return ha}();Object(xa.a)(h);Object(na.a)(h);Object(na.b)(h);ta["default"]=h}}]);}).call(this || window)
