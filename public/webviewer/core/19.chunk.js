/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[19],{588:function(xa){(function(){xa.exports={Oa:function(){function ta(b,a){this.scrollLeft=b;this.scrollTop=a}function h(b){if(null===b||"object"!==typeof b||void 0===b.behavior||"auto"===b.behavior||"instant"===b.behavior)return!0;if("object"===typeof b&&"smooth"===b.behavior)return!1;throw new TypeError("behavior member of ScrollOptions "+b.behavior+" is not a valid value for enumeration ScrollBehavior.");}function qa(b,a){if("Y"===
a)return b.clientHeight+f<b.scrollHeight;if("X"===a)return b.clientWidth+f<b.scrollWidth}function oa(b,a){b=ha.getComputedStyle(b,null)["overflow"+a];return"auto"===b||"scroll"===b}function na(b){var a=qa(b,"Y")&&oa(b,"Y");b=qa(b,"X")&&oa(b,"X");return a||b}function ja(b){var a=(n()-b.startTime)/468;var e=.5*(1-Math.cos(Math.PI*(1<a?1:a)));a=b.TF+(b.x-b.TF)*e;e=b.UF+(b.y-b.UF)*e;b.method.call(b.iN,a,e);a===b.x&&e===b.y||ha.requestAnimationFrame(ja.bind(ha,b))}function ka(b,a,e){var w=n();if(b===x.body){var y=
ha;var ba=ha.scrollX||ha.pageXOffset;b=ha.scrollY||ha.pageYOffset;var fa=r.scroll}else y=b,ba=b.scrollLeft,b=b.scrollTop,fa=ta;ja({iN:y,method:fa,startTime:w,TF:ba,UF:b,x:a,y:e})}var ha=window,x=document;if(!("scrollBehavior"in x.documentElement.style&&!0!==ha.BNa)){var z=ha.HTMLElement||ha.Element,r={scroll:ha.scroll||ha.scrollTo,scrollBy:ha.scrollBy,B5:z.prototype.scroll||ta,scrollIntoView:z.prototype.scrollIntoView},n=ha.performance&&ha.performance.now?ha.performance.now.bind(ha.performance):Date.now,
f=RegExp("MSIE |Trident/|Edge/").test(ha.navigator.userAgent)?1:0;ha.scroll=ha.scrollTo=function(b,a){void 0!==b&&(!0===h(b)?r.scroll.call(ha,void 0!==b.left?b.left:"object"!==typeof b?b:ha.scrollX||ha.pageXOffset,void 0!==b.top?b.top:void 0!==a?a:ha.scrollY||ha.pageYOffset):ka.call(ha,x.body,void 0!==b.left?~~b.left:ha.scrollX||ha.pageXOffset,void 0!==b.top?~~b.top:ha.scrollY||ha.pageYOffset))};ha.scrollBy=function(b,a){void 0!==b&&(h(b)?r.scrollBy.call(ha,void 0!==b.left?b.left:"object"!==typeof b?
b:0,void 0!==b.top?b.top:void 0!==a?a:0):ka.call(ha,x.body,~~b.left+(ha.scrollX||ha.pageXOffset),~~b.top+(ha.scrollY||ha.pageYOffset)))};z.prototype.scroll=z.prototype.scrollTo=function(b,a){if(void 0!==b)if(!0===h(b)){if("number"===typeof b&&void 0===a)throw new SyntaxError("Value could not be converted");r.B5.call(this,void 0!==b.left?~~b.left:"object"!==typeof b?~~b:this.scrollLeft,void 0!==b.top?~~b.top:void 0!==a?~~a:this.scrollTop)}else a=b.left,b=b.top,ka.call(this,this,"undefined"===typeof a?
this.scrollLeft:~~a,"undefined"===typeof b?this.scrollTop:~~b)};z.prototype.scrollBy=function(b,a){void 0!==b&&(!0===h(b)?r.B5.call(this,void 0!==b.left?~~b.left+this.scrollLeft:~~b+this.scrollLeft,void 0!==b.top?~~b.top+this.scrollTop:~~a+this.scrollTop):this.scroll({left:~~b.left+this.scrollLeft,top:~~b.top+this.scrollTop,behavior:b.behavior}))};z.prototype.scrollIntoView=function(b){if(!0===h(b))r.scrollIntoView.call(this,void 0===b?!0:b);else{for(b=this;b!==x.body&&!1===na(b);)b=b.parentNode||
b.host;var a=b.getBoundingClientRect(),e=this.getBoundingClientRect();b!==x.body?(ka.call(this,b,b.scrollLeft+e.left-a.left,b.scrollTop+e.top-a.top),"fixed"!==ha.getComputedStyle(b).position&&ha.scrollBy({left:a.left,top:a.top,behavior:"smooth"})):ha.scrollBy({left:e.left,top:e.top,behavior:"smooth"})}}}}}})()}}]);}).call(this || window)
