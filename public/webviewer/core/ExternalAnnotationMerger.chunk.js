/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[4],{587:function(xa,ta,h){h.r(ta);var qa=h(0),oa=h(609),na=h(610),ja;(function(ka){ka[ka.EXTERNAL_XFDF_NOT_REQUESTED=0]="EXTERNAL_XFDF_NOT_REQUESTED";ka[ka.EXTERNAL_XFDF_NOT_AVAILABLE=1]="EXTERNAL_XFDF_NOT_AVAILABLE";ka[ka.EXTERNAL_XFDF_AVAILABLE=2]="EXTERNAL_XFDF_AVAILABLE"})(ja||(ja={}));xa=function(){function ka(ha){this.ba=ha;this.state=ja.EXTERNAL_XFDF_NOT_REQUESTED}ka.prototype.Rxa=function(){var ha=this;return function(x,
z,r){return Object(qa.b)(ha,void 0,void 0,function(){var n,f,b,a,e,w,y,ba=this,fa;return Object(qa.d)(this,function(aa){switch(aa.label){case 0:if(this.state!==ja.EXTERNAL_XFDF_NOT_REQUESTED)return[3,2];n=this.ba.getDocument().Xx();return[4,this.zva(n)];case 1:f=aa.aa(),b=this.opa(f),this.rS=null!==(fa=null===b||void 0===b?void 0:b.parse())&&void 0!==fa?fa:null,this.state=null===this.rS?ja.EXTERNAL_XFDF_NOT_AVAILABLE:ja.EXTERNAL_XFDF_AVAILABLE,aa.label=2;case 2:if(this.state===ja.EXTERNAL_XFDF_NOT_AVAILABLE)return r(x),
[2];a=new DOMParser;e=a.parseFromString(x,"text/xml");z.forEach(function(ea){ba.merge(e,ba.rS,ea-1)});w=new XMLSerializer;y=w.serializeToString(e);r(y);return[2]}})})}};ka.prototype.YX=function(ha){this.zva=ha};ka.prototype.Xf=function(){this.rS=void 0;this.state=ja.EXTERNAL_XFDF_NOT_REQUESTED};ka.prototype.opa=function(ha){return ha?Array.isArray(ha)?new oa.a(ha):"string"!==typeof ha?null:(new DOMParser).parseFromString(ha,"text/xml").querySelector("xfdf > add")?new oa.a(ha):new na.a(ha):null};ka.prototype.merge=
function(ha,x,z){var r=this;0===z&&(this.qBa(ha,x.St),this.sBa(ha,x.ZR));var n=x.ea[z];n&&(this.tBa(ha,n.qr),this.vBa(ha,n.yfa,x.EC),this.uBa(ha,n.page,z),this.rBa(ha,n.Y4));n=this.ba.Cb();if(z===n-1){var f=x.EC;Object.keys(f).forEach(function(b){f[b].mU||r.i$(ha,b,f[b])})}};ka.prototype.qBa=function(ha,x){null!==x&&(ha=this.BB(ha),this.uv(ha,"calculation-order",x))};ka.prototype.sBa=function(ha,x){null!==x&&(ha=this.BB(ha),this.uv(ha,"document-actions",x))};ka.prototype.tBa=function(ha,x){var z=
this,r=this.AB(ha.querySelector("xfdf"),"annots");Object.keys(x).forEach(function(n){z.uv(r,'[name="'.concat(n,'"]'),x[n])})};ka.prototype.vBa=function(ha,x,z){var r=this;if(0!==x.length){var n=this.BB(ha);x.forEach(function(f){var b=f.getAttribute("field"),a=z[b];a&&(r.i$(ha,b,a),r.uv(n,"null",f))})}};ka.prototype.i$=function(ha,x,z){var r=this.BB(ha),n=r.querySelector('ffield[name="'.concat(x,'"]'));null!==z.XJ&&null===n&&this.uv(r,'ffield[name="'.concat(x,'"]'),z.XJ);ha=this.AB(ha.querySelector("xfdf"),
"xfdf > fields","fields");x=x.split(".");this.VW(ha,x,0,z.value);z.mU=!0};ka.prototype.uBa=function(ha,x,z){null!==x&&(ha=this.BB(ha),ha=this.AB(ha,"pages"),this.uv(ha,'[number="'.concat(z+1,'"]'),x))};ka.prototype.rBa=function(ha,x){Object.keys(x).forEach(function(z){(z=ha.querySelector('annots [name="'.concat(z,'"]')))&&z.parentElement.removeChild(z)})};ka.prototype.VW=function(ha,x,z,r){if(z===x.length)x=document.createElementNS("","value"),x.textContent=r,this.uv(ha,"value",x);else{var n=x[z];
this.AB(ha,'[name="'.concat(n,'"]'),"field").setAttribute("name",n);ha=ha.querySelectorAll('[name="'.concat(n,'"]'));1===ha.length?this.VW(ha[0],x,z+1,r):(n=this.Jta(ha),this.VW(z===x.length-1?n:this.zKa(ha,n),x,z+1,r))}};ka.prototype.Jta=function(ha){for(var x=null,z=0;z<ha.length;z++){var r=ha[z];if(0===r.childElementCount||1===r.childElementCount&&"value"===r.children[0].tagName){x=r;break}}return x};ka.prototype.zKa=function(ha,x){for(var z=0;z<ha.length;z++)if(ha[z]!==x)return ha[z];return null};
ka.prototype.uv=function(ha,x,z){x=ha.querySelector(x);null!==x&&ha.removeChild(x);ha.appendChild(z)};ka.prototype.BB=function(ha){var x=ha.querySelector("pdf-info");if(null!==x)return x;x=this.AB(ha.querySelector("xfdf"),"pdf-info");x.setAttribute("xmlns","http://www.pdftron.com/pdfinfo");x.setAttribute("version","2");x.setAttribute("import-version","4");return x};ka.prototype.AB=function(ha,x,z){var r=ha.querySelector(x);if(null!==r)return r;r=document.createElementNS("",z||x);ha.appendChild(r);
return r};return ka}();ta["default"]=xa},598:function(xa,ta){xa=function(){function h(){}h.prototype.lI=function(qa){var oa={St:null,ZR:null,EC:{},ea:{}};qa=(new DOMParser).parseFromString(qa,"text/xml");oa.St=qa.querySelector("pdf-info calculation-order");oa.ZR=qa.querySelector("pdf-info document-actions");oa.EC=this.SCa(qa);oa.ea=this.fDa(qa);return oa};h.prototype.SCa=function(qa){var oa=qa.querySelector("fields");qa=qa.querySelectorAll("pdf-info > ffield");if(null===oa&&null===qa)return{};var na=
{};this.Bla(na,oa);this.zla(na,qa);return na};h.prototype.Bla=function(qa,oa){if(null!==oa&&oa.children){for(var na=[],ja=0;ja<oa.children.length;ja++){var ka=oa.children[ja];na.push({name:ka.getAttribute("name"),element:ka})}for(;0!==na.length;)for(oa=na.shift(),ja=0;ja<oa.element.children.length;ja++)ka=oa.element.children[ja],"value"===ka.tagName?qa[oa.name]={value:ka.textContent,XJ:null,mU:!1}:ka.children&&na.push({name:"".concat(oa.name,".").concat(ka.getAttribute("name")),element:ka})}};h.prototype.zla=
function(qa,oa){oa.forEach(function(na){var ja=na.getAttribute("name");qa[ja]?qa[ja].XJ=na:qa[ja]={value:null,XJ:na,mU:!1}})};h.prototype.fDa=function(qa){var oa=this,na={};qa.querySelectorAll("pdf-info widget").forEach(function(ja){var ka=parseInt(ja.getAttribute("page"),10)-1;oa.pL(na,ka);na[ka].yfa.push(ja)});qa.querySelectorAll("pdf-info page").forEach(function(ja){var ka=parseInt(ja.getAttribute("number"),10)-1;oa.pL(na,ka);na[ka].page=ja});this.a7(qa).forEach(function(ja){var ka=parseInt(ja.getAttribute("page"),
10),ha=ja.getAttribute("name");oa.pL(na,ka);na[ka].qr[ha]=ja});this.J6(qa).forEach(function(ja){var ka=parseInt(ja.getAttribute("page"),10);ja=ja.textContent;oa.pL(na,ka);na[ka].Y4[ja]=!0});return na};h.prototype.pL=function(qa,oa){qa[oa]||(qa[oa]={qr:{},Y4:{},yfa:[],page:null})};return h}();ta.a=xa},609:function(xa,ta,h){var qa=h(0),oa=h(1);h.n(oa);xa=function(na){function ja(ka){var ha=na.call(this)||this;ha.sta=Array.isArray(ka)?ka:[ka];return ha}Object(qa.c)(ja,na);ja.prototype.parse=function(){var ka=
this,ha={St:null,ZR:null,EC:{},ea:{}};this.sta.forEach(function(x){ha=Object(oa.merge)(ha,ka.lI(x))});return ha};ja.prototype.a7=function(ka){var ha=[];ka.querySelectorAll("add > *").forEach(function(x){ha.push(x)});ka.querySelectorAll("modify > *").forEach(function(x){ha.push(x)});return ha};ja.prototype.J6=function(ka){return ka.querySelectorAll("delete > *")};return ja}(h(598).a);ta.a=xa},610:function(xa,ta,h){var qa=h(0);xa=function(oa){function na(ja){var ka=oa.call(this)||this;ka.tta=ja;return ka}
Object(qa.c)(na,oa);na.prototype.parse=function(){return this.lI(this.tta)};na.prototype.a7=function(ja){return ja.querySelectorAll("annots > *")};na.prototype.J6=function(){return[]};return na}(h(598).a);ta.a=xa}}]);}).call(this || window)
