/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[11],{580:function(xa,ta,h){h.r(ta);var qa=h(0),oa=h(1);h.n(oa);var na=h(3),ja=h(194);xa=h(58);var ka=h(121),ha=h(327),x=h(100),z=h(326);h=h(494);var r=window,n=function(){function a(e,w,y){var ba=-1===e.indexOf("?")?"?":"&";switch(w){case x.a.NEVER_CACHE:this.url="".concat(e+ba,"_=").concat(Object(oa.uniqueId)());break;default:this.url=e}this.Lf=y;this.request=new XMLHttpRequest;this.request.open("GET",this.url,!0);this.request.setRequestHeader("X-Requested-With",
"XMLHttpRequest");this.request.overrideMimeType?this.request.overrideMimeType("text/plain; charset=x-user-defined"):this.request.setRequestHeader("Accept-Charset","x-user-defined");this.status=z.a.NOT_STARTED}a.prototype.start=function(e,w){var y=this,ba=this,fa=this.request,aa;ba.cE=0;e&&Object.keys(e).forEach(function(ea){y.request.setRequestHeader(ea,e[ea])});w&&(this.request.withCredentials=w);this.LM=setInterval(function(){var ea=0===window.document.URL.indexOf("file:///");ea=200===fa.status||
ea&&0===fa.status;if(fa.readyState!==z.b.DONE||ea){try{fa.responseText}catch(ca){return}ba.cE<fa.responseText.length&&(aa=ba.kEa())&&ba.trigger(a.Events.DATA,[aa]);0===fa.readyState&&(clearInterval(ba.LM),ba.trigger(a.Events.DONE))}else clearInterval(ba.LM),ba.trigger(a.Events.DONE,["Error received return status ".concat(fa.status)])},1E3);this.request.send(null);this.status=z.a.STARTED};a.prototype.kEa=function(){var e=this.request,w=e.responseText;if(0!==w.length)if(this.cE===w.length)clearInterval(this.LM),
this.trigger(a.Events.DONE);else return w=Math.min(this.cE+3E6,w.length),e=r.w7(e,this.cE,!0,w),this.cE=w,e};a.prototype.abort=function(){clearInterval(this.LM);var e=this;this.request.onreadystatechange=function(){Object(na.j)("StreamingRequest aborted");e.status=z.a.ABORTED;return e.trigger(a.Events.ABORTED)};this.request.abort()};a.prototype.finish=function(){var e=this;this.request.onreadystatechange=function(){e.status=z.a.SUCCESS;return e.trigger(a.Events.DONE)};this.request.abort()};a.Events=
{DONE:"done",DATA:"data",ABORTED:"aborted"};return a}();Object(xa.a)(n);var f;(function(a){a[a.LOCAL_HEADER=0]="LOCAL_HEADER";a[a.FILE=1]="FILE";a[a.CENTRAL_DIR=2]="CENTRAL_DIR"})(f||(f={}));var b=function(a){function e(){var w=a.call(this)||this;w.buffer="";w.state=f.LOCAL_HEADER;w.FY=4;w.Zp=null;w.Zy=ja.c;w.Qr={};return w}Object(qa.c)(e,a);e.prototype.aEa=function(w){var y;for(w=this.buffer+w;w.length>=this.Zy;)switch(this.state){case f.LOCAL_HEADER:this.Zp=y=this.oEa(w.slice(0,this.Zy));if(y.Nz!==
ja.g)throw Error("Wrong signature in local header: ".concat(y.Nz));w=w.slice(this.Zy);this.state=f.FILE;this.Zy=y.VQ+y.hv+y.CC+this.FY;this.trigger(e.Events.HEADER,[y]);break;case f.FILE:this.Zp.name=w.slice(0,this.Zp.hv);this.Qr[this.Zp.name]=this.Zp;y=this.Zy-this.FY;var ba=w.slice(this.Zp.hv+this.Zp.CC,y);this.trigger(e.Events.FILE,[this.Zp.name,ba,this.Zp.qR]);w=w.slice(y);if(w.slice(0,this.FY)===ja.h)this.state=f.LOCAL_HEADER,this.Zy=ja.c;else return this.state=f.CENTRAL_DIR,!0}this.buffer=w;
return!1};e.Events={HEADER:"header",FILE:"file"};return e}(ha.a);Object(xa.a)(b);xa=function(a){function e(w,y,ba,fa,aa){ba=a.call(this,w,ba,fa)||this;ba.url=w;ba.stream=new n(w,y);ba.Re=new b;ba.Saa=window.createPromiseCapability();ba.xba={};ba.Lf=aa;return ba}Object(qa.c)(e,a);e.prototype.jF=function(w){var y=this;this.request([this.um,this.Bo,this.sm]);this.stream.addEventListener(n.Events.DATA,function(ba){try{if(y.Re.aEa(ba))return y.stream.finish()}catch(fa){throw y.stream.abort(),y.vu(fa),
w(fa),fa;}});this.stream.addEventListener(n.Events.DONE,function(ba){y.xDa=!0;y.Saa.resolve();ba&&(y.vu(ba),w(ba))});this.Re.addEventListener(b.Events.HEADER,Object(oa.bind)(this.wba,this));this.Re.addEventListener(b.Events.FILE,Object(oa.bind)(this.FEa,this));return this.stream.start(this.Lf,this.withCredentials)};e.prototype.p7=function(w){var y=this;this.Saa.promise.then(function(){w(Object.keys(y.Re.Qr))})};e.prototype.Is=function(){return!0};e.prototype.request=function(w){var y=this;this.xDa&&
w.forEach(function(ba){y.xba[ba]||y.QKa(ba)})};e.prototype.wba=function(){};e.prototype.abort=function(){this.stream&&this.stream.abort()};e.prototype.QKa=function(w){this.trigger(ka.a.Events.PART_READY,[{Kb:w,error:"Requested part not found",sl:!1,ni:!1}])};e.prototype.FEa=function(w,y,ba){this.xba[w]=!0;this.trigger(ka.a.Events.PART_READY,[{Kb:w,data:y,sl:!1,ni:!1,error:null,ue:ba}])};return e}(ka.a);Object(h.a)(xa);Object(h.b)(xa);ta["default"]=xa}}]);}).call(this || window)
