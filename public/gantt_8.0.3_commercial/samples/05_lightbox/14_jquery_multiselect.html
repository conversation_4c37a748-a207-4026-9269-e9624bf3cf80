
<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>3rd party multiselect control</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.3"></script>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?">
	
	<script
			src="https://code.jquery.com/jquery-3.3.1.min.js?v=8.0.3"
			integrity="sha256-FgpCb/KJQlLNfOu91ta32o/NMZxltwRo8QtmkMRdAu8="
			crossorigin="anonymous"></script>

	<script src="https://cdnjs.cloudflare.com/ajax/libs/chosen/1.8.7/chosen.jquery.js?v=8.0.3"></script>

	<link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/chosen/1.8.7/chosen.css?v=8.0.3">

	<style>
		html, body {
			padding: 0px;
			margin: 0px;
			height: 100%;
		}

		#gantt_here {
			width:100%;
			height:100%;
		}

		.gantt_grid_scale .gantt_grid_head_cell,
		.gantt_task .gantt_task_scale .gantt_scale_cell {
			font-weight: bold;
			font-size: 14px;
			color: rgba(0, 0, 0, 0.7);
		}

		.owner-label{
			width: 20px;
			height: 20px;
			line-height: 20px;
			font-size: 12px;
			display: inline-block;
			border: 1px solid #cccccc;
			border-radius: 25px;
			background: #e6e6e6;
			color: #6f6f6f;
			margin: 0 3px;
			font-weight: bold;
		}

		.gantt_cal_larea{
			overflow:visible;
		}
		.gantt_cal_chosen,
		.gantt_cal_chosen select{
			width: 400px;
		}
	</style>
</head>
<body>

<div id="gantt_here"></div>
<script>
	gantt.form_blocks["multiselect"] = {
		render: function (sns) {
			var height = (sns.height || "23") + "px";
			var html = "<div class='gantt_cal_ltext gantt_cal_chosen gantt_cal_multiselect' style='height:" + height + ";'><select data-placeholder='...' class='chosen-select' multiple>";
			if (sns.options) {
				for (var i = 0; i < sns.options.length; i++) {
					if(sns.unassigned_value !== undefined && sns.options[i].key == sns.unassigned_value){
						continue;
					}
					html += "<option value='" + sns.options[i].key + "'>" + sns.options[i].label + "</option>";
				}
			}
			html += "</select></div>";
			return html;
		},

		set_value: function (node, value, ev, sns) {
			node.style.overflow = "visible";
			node.parentNode.style.overflow = "visible";
			node.style.display = "inline-block";
			var select = $(node.firstChild);

			if (value) {
				value = (value + "").split(",");
				select.val(value);
			}
			else {
				select.val([]);
			}

			select.chosen();
			if(sns.onchange){
				select.change(function(){
					sns.onchange.call(this);
				})
			}
			select.trigger('chosen:updated');
			select.trigger("change");
		},

		get_value: function (node, ev) {
			var value = $(node.firstChild).val();
			return value;
		},

		focus: function (node) {
			$(node.firstChild).focus();
		}
	};

	gantt.serverList("people", [
		{key: 6, label: "John"},
		{key: 7, label: "Mike"},
		{key: 8, label: "Anna"},
		{key: 9, label: "Bill"},
		{key: 10, label: "Floe"}
	]);

	function findUser(id){
		var list = gantt.serverList("people");
		for(var i = 0; i < list.length; i++){
			if(list[i].key == id){
				return list[i];
			}
		}
		return null;
	}

	gantt.config.columns = [
		{name: "text", tree: true, width: 200, resize: true},
		{name: "start_date", align: "center", width: 80, resize: true},
		{name: "owner", align: "center", width: 75, label: "Owner", template: function (task) {
			if (task.type == gantt.config.types.project) {
				return "";
			}

			var result = "";
		
			var owners = task.owners;

			if (!owners || !owners.length) {
				return "Unassigned";
			}

			if(owners.length == 1){
				return findUser(owners[0]).label;
			}

			owners.forEach(function(ownerId) {
				var owner = findUser(ownerId);
				if (!owner)
					return;
				result += "<div class='owner-label' title='" + owner.label + "'>" + owner.label.substr(0, 1) + "</div>";

			});

			return result;
			}, resize: true
		},
		{name: "duration", width: 60, align: "center"},
		{name: "add", width: 44}
	];

	
	gantt.locale.labels.section_owner = "Owner";
	gantt.config.lightbox.sections = [
		{name: "description", height: 38, map_to: "text", type: "textarea", focus: true},
		{name:"owner",height:60, type:"multiselect", options:gantt.serverList("people"), map_to:"owners"},
		{name: "time", type: "duration", map_to: "auto"}
	];

	gantt.config.order_branch = true;
	gantt.config.open_tree_initially = true;

	gantt.init("gantt_here");
	gantt.parse({
		data: [
			{ id: 1, text: "Office itinerancy", type: "project", start_date: "02-04-2022 00:00", duration: 17, progress: 0.4,owners: [], parent: 0},
			{ id: 2, text: "Office facing", type: "project", start_date: "02-04-2022 00:00", duration: 8, progress: 0.6,owners: [], parent: "1"},
			{ id: 3, text: "Furniture installation", type: "project", start_date: "11-04-2022 00:00", duration: 8, parent: "1", progress: 0.6, owners: []},
			{ id: 4, text: "The employee relocation", type: "project", start_date: "13-04-2022 00:00", duration: 5, parent: "1", progress: 0.5,owners: [], priority:3},
			{ id: 5, text: "Interior office", type: "task", start_date: "03-04-2022 00:00", duration: 7, parent: "2", progress: 0.6,owners: ["6"], priority:1},
			{ id: 6, text: "Air conditioners check", type: "task", start_date: "03-04-2022 00:00", duration: 7, parent: "2", progress: 0.6,owners: ["7"], priority:2},
			{ id: 7, text: "Workplaces preparation", type: "task", start_date: "12-04-2022 00:00", duration: 8, parent: "3", progress: 0.6, owners: ["10"]},
			{ id: 8, text: "Preparing workplaces", type: "task", start_date: "14-04-2022 00:00", duration: 5, parent: "4", progress: 0.5, owners: ["10","9"], priority:1},
			{ id: 9, text: "Workplaces importation", type: "task", start_date: "21-04-2022 00:00", duration: 4, parent: "4", progress: 0.5, owners: ["7"]},
			{ id: 10, text: "Workplaces exportation", type: "task", start_date: "27-04-2022 00:00", duration: 3, parent: "4", progress: 0.5,owners: ["8"], priority:2},
			{ id: 11, text: "Product launch", type: "project", progress: 0.6, start_date: "02-04-2022 00:00", duration: 13,owners: [], parent: 0},
			{ id: 12, text: "Perform Initial testing", type: "task", start_date: "03-04-2022 00:00", duration: 5, parent: "11", progress: 1, owners: ["7"]},
			{ id: 13, text: "Development", type: "project", start_date: "03-04-2022 00:00", duration: 11, parent: "11", progress: 0.5, owners: []},
			{ id: 14, text: "Analysis", type: "task", start_date: "03-04-2022 00:00", duration: 6, parent: "11", progress: 0.8, owners: []},
			{ id: 15, text: "Design", type: "project", start_date: "03-04-2022 00:00", duration: 5, parent: "11", progress: 0.2, owners: []},
			{ id: 16, text: "Documentation creation", type: "task", start_date: "03-04-2022 00:00", duration: 7, parent: "11", progress: 0,owners: ["7"], priority:1},
			{ id: 17, text: "Develop System", type: "task", start_date: "03-04-2022 00:00", duration: 2, parent: "13", progress: 1,owners: ["8"], priority:2},
			{ id: 25, text: "Beta Release", type: "milestone", start_date: "06-04-2022 00:00", parent: "13", progress: 0,owners: [], duration: 0},
			{ id: 18, text: "Integrate System", type: "task", start_date: "10-04-2022 00:00", duration: 2, parent: "13", progress: 0.8,owners: ["6"], priority:3},
			{ id: 19, text: "Test", type: "task", start_date: "13-04-2022 00:00", duration: 4, parent: "13", progress: 0.2, owners: ["6"]},
			{ id: 20, text: "Marketing", type: "task", start_date: "13-04-2022 00:00", duration: 4, parent: "13", progress: 0,owners: ["8"], priority:1},
			{ id: 21, text: "Design database", type: "task", start_date: "03-04-2022 00:00", duration: 4, parent: "15", progress: 0.5, owners: ["6"]},
			{ id: 22, text: "Software design", type: "task", start_date: "03-04-2022 00:00", duration: 4, parent: "15", progress: 0.1,owners: ["8"], priority:1},
			{ id: 23, text: "Interface setup", type: "task", start_date: "03-04-2022 00:00", duration: 5, parent: "15", progress: 0,owners: ["8"], priority:1},
			{ id: 24, text: "Release v1.0", type: "milestone", start_date: "20-04-2022 00:00", parent: "11", progress: 0,owners: [], duration: 0}

		],
		links: [

			{ id: "2", source: "2", target: "3", type: "0" },
			{ id: "3", source: "3", target: "4", type: "0" },
			{ id: "7", source: "8", target: "9", type: "0" },
			{ id: "8", source: "9", target: "10", type: "0" },
			{ id: "16", source: "17", target: "25", type: "0" },
			{ id: "17", source: "18", target: "19", type: "0" },
			{ id: "18", source: "19", target: "20", type: "0" },
			{ id: "22", source: "13", target: "24", type: "0" },
			{ id: "23", source: "25", target: "18", type: "0" }

		]
		});

	gantt.showLightbox(8);
</script>
</body>