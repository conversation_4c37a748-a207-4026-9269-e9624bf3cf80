<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Template control</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.3"></script>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.3">
	<script src="../common/testdata.js?v=8.0.3"></script>
	<style>
		html, body{
			padding:0;
			margin: 0;
		}

		#title1 {
			padding-left: 35px;
			color: black;
			font-weight: bold;
		}

		#title2 {
			padding-left: 15px;
			color: black;
			font-weight: bold;
		}
	</style>
</head>

<body>
<div id="gantt_here" style='height:100vh;'></div>

<script>
	gantt.locale.labels.section_template = "Details";
	gantt.config.lightbox.sections = [
		{name: "description", height: 38, map_to: "text", type: "textarea", focus: true},
		{name: "template", height: 16, type: "template", map_to: "my_template"},
		{name: "time", type: "duration", map_to: "auto"}
	];

	gantt.attachEvent("onBeforeLightbox", function (id) {
		var task = gantt.getTask(id);
		task.my_template = "<span id='title1'>Holders: </span>" + task.users + "<span id='title2'>Progress: </span>" + task.progress * 100 + " %";
		return true;
	});

	gantt.init("gantt_here");
	gantt.parse(users_data);
	gantt.showLightbox(1);
</script>
</body>