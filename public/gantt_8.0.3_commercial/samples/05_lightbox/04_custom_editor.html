<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Custom control in the lightbox</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.3"></script>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.3">
	<script src="../common/testdata.js?v=8.0.3"></script>
	<style>
		html, body{
			padding:0;
			margin: 0;
		}
	</style>
</head>

<body>
<div id="gantt_here" style='height:100vh;'></div>

<script>
	gantt.form_blocks["my_editor"] = {
		render: function (sns) {
			return "<div class='dhx_cal_ltext' style='height:60px;'>"+
				"Text&nbsp;<input class='editor_description' type='text'><br/>Holders&nbsp;<input class='editor_holders' type='text'>"+
				"</div>";
		},
		set_value: function (node, value, task) {
			node.querySelector(".editor_description").value = value || "";
			node.querySelector(".editor_holders").value = task.users || "";
		},
		get_value: function (node, task) {
			task.users = node.querySelector(".editor_holders").value;
			return node.querySelector(".editor_description").value;
		},
		focus: function (node) {
			var a = node.querySelector(".editor_description");
			a.select();
			a.focus();
		}
	};
	gantt.config.lightbox.sections = [
		{name: "description", height: 200, map_to: "text", type: "my_editor", focus: true},
		{name: "time", type: "duration", map_to: "auto"}
	];

	gantt.init("gantt_here");

	gantt.parse(users_data);
	gantt.showLightbox(1);
</script>
</body>