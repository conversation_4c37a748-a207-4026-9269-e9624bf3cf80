<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Resizable rows in grid</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.3"></script>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.3">

	<script src="../common/testdata.js?v=8.0.3"></script>
	<style>
		html, body {
			height: 100%;
			padding: 0px;
			margin: 0px;
			overflow: hidden;
		}
		.gantt_row_project{
			font-weight: bold;
		}
	</style>
</head>

<body>
<div id="gantt_here" style='width:100%; height:100%;'></div>
<script>
	gantt.message({
		text:[
			"You can increase the height of any row of the gantt by resizing it in the grid.",
			"The height if an individual row can be set using the <b>row_height</b> and <b>bar_height</b> properties of the task object."
		].join("<br><br>"),
		expire: -1
	});

	gantt.config.resize_rows = true;

	// return false to discard the resize
	gantt.attachEvent("onBeforeRowResize", function (item) {
		gantt.message("Start resizing <b>" + item.text + "</b>");
		return true;
	});

	var message = null;
	gantt.attachEvent("onRowResize", function (id, item, currentHeight) {
		if (!message) {
			message = gantt.message({
				expire: -1,
				text: "<b>" + item.text + "</b> is now <b id='height_placeholder'></b><b>px</b> height"
			});
		}
		document.getElementById("height_placeholder").innerText = currentHeight;
	});

	// return false to discard the resize
	gantt.attachEvent("onBeforeRowResizeEnd", function (id, item, newHeight) {
		gantt.message.hide(message);
		message = null;
		gantt.message("<b>" + item.text + "</b> is now <b>" + newHeight + "px</b> height");
		return true;
	});

	gantt.attachEvent("onAfterRowResize", function (id, item, oldHeight, newHeight) {
		gantt.message.hide(message);
		message = null;
		gantt.message("<b>" + item.text + "</b> was <b>" + oldHeight + "px</b> height. <br><b>" 
			+ item.text + "</b> is now <b>" + newHeight + "px</b> height");
	});

	// keep height of bars when rows resized
	function fixBarHeight(task){
		task.bar_height = 30;
		return true;
	}
	gantt.attachEvent("onTaskLoading", fixBarHeight);
	gantt.attachEvent("onTaskCreated", fixBarHeight);

	gantt.init("gantt_here");

	gantt.parse({
		data:[
			{id:11, text:"Project #1", type:"project", progress: 0.6, open: true},

			{id:12, text:"Task #1", start_date:"03-04-2023", duration:"5", parent:"11", progress: 1, open: true},
			{id:13, text:"Task #2", start_date:"03-04-2023", type:"project", parent:"11", progress: 0.5, open: true},
			{id:14, text:"Task #3", start_date:"02-04-2023", duration:"6", parent:"11", progress: 0.8, open: true},
			{id:15, text:"Task #4", type:"project", parent:"11", progress: 0.2, open: true},
			{id:16, text:"Final milestone", start_date:"15-04-2023", type:"milestone", parent:"11", progress: 0, open: true},

			{id:17, text:"Task #2.1", start_date:"03-04-2023", duration:"2", parent:"13", progress: 1, open: true},
			{id:18, text:"Task #2.2", start_date:"06-04-2023", duration:"3", parent:"13", progress: 0.8, open: true},
			{id:19, text:"Task #2.3", start_date:"10-04-2023", duration:"4", parent:"13", progress: 0.2, open: true},
			{id:20, text:"Task #2.4", start_date:"10-04-2023", duration:"4", parent:"13", progress: 0, open: true},
			{id:21, text:"Task #4.1", start_date:"03-04-2023", duration:"4", parent:"15", progress: 0.5, open: true},
			{id:22, text:"Task #4.2", start_date:"03-04-2023", duration:"4", parent:"15", progress: 0.1, open: true},
			{id:23, text:"Mediate milestone", start_date:"13-04-2023", type:"milestone", parent:"15", progress: 0, open: true}
		],
		links:[
			{id:"10",source:"11",target:"12",type:"1"},
			{id:"11",source:"11",target:"13",type:"1"},
			{id:"12",source:"11",target:"14",type:"1"},
			{id:"13",source:"11",target:"15",type:"1"},
			{id:"14",source:"23",target:"16",type:"0"},
			{id:"15",source:"13",target:"17",type:"1"},
			{id:"16",source:"17",target:"18",type:"0"},
			{id:"17",source:"18",target:"19",type:"0"},
			{id:"18",source:"19",target:"20",type:"0"},
			{id:"19",source:"15",target:"21",type:"2"},
			{id:"20",source:"15",target:"22",type:"2"},
			{id:"21",source:"15",target:"23",type:"0"}
		]
	});

</script>
</body>