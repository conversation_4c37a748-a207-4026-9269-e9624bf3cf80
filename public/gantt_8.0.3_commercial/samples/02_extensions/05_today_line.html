<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Today and Status lines in Gantt</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.3"></script>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.3">

	<script src="../common/testdata.js?v=8.0.3"></script>
</head>

<body>
<style>
	html, body {
		padding: 0px;
		margin: 0px;
		height: 100%;
	}

	.status_line {
		background-color: #0ca30a;
	}

</style>
<div id="gantt_here" style='width:100%; height:100%;'></div>
<script>
	gantt.plugins({
		marker: true
	});

	var dateToStr = gantt.date.date_to_str(gantt.config.task_date);
	var today = new Date(2023, 3, 5);
	gantt.addMarker({
		start_date: today,
		css: "today",
		text: "Today",
		title: "Today: " + dateToStr(today)
	});

	var start = new Date(2023, 2, 28);
	gantt.addMarker({
		start_date: start,
		css: "status_line",
		text: "Start project",
		title: "Start project: " + dateToStr(start)
	});

	gantt.config.scale_height = 50;
	gantt.config.scales = [
		{unit: "day", step: 1, format: "%j, %D"},
		{unit: "month", step: 1, format: "%F, %Y"},
	];

	gantt.init("gantt_here");

	gantt.parse(demo_tasks);


</script>
</body>