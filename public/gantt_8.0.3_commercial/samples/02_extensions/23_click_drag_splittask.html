<!DOCTYPE html>
<html>

<head lang="en">
	<meta charset="UTF-8">
	<title>Create split tasks by <PERSON>ag and Drop</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.3"></script>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.3">
</head>

<body>
	<div id="gantt_here" style="width: 100%; height: 500px;"></div>
	<script>
		gantt.plugins({
			click_drag: true
		});

		gantt.message({
			text: "Click and drag to create a new split task or a new part to existing one.",
			expire: -1
		});

		gantt.config.lightbox.sections = [
			{name: "description", height: 38, map_to: "text", type: "textarea", focus: true},
			{
				name: "priority", height: 22, map_to: "priority", type: "select", options: [
					{key: 1, label: "High"},
					{key: 2, label: "Normal"},
					{key: 3, label: "Low"}
				]
			},
			{name: "time", type: "duration", map_to: "auto"}
		];
		gantt.config.open_split_tasks = true;
		gantt.locale.labels.section_priority = "Priority";
		gantt.config.multiselect = true;
		gantt.config.click_drag = {
			callback: onDragEnd,
			singleRow: true
		}
		
		gantt.init("gantt_here");
		gantt.parse({
			data: [
				{id: 11, text: "Project #1", type: "project", progress: 0, open: true, start_date: "02-04-2023 00:00", duration: 13, parent: 0},
				{id: 12, text: "Task #1", start_date: "03-04-2023 00:00", duration: 5, parent: "11", progress: 0, open: true},
				{id: 13, text: "Task #2", start_date: "03-04-2023 00:00", type: "project", render:"split", parent: "11", progress: 0.5, open: false, duration: 11},
				{id: 17, text: "Stage #1", start_date: "03-04-2023 00:00", duration: 1, parent: "13", progress: 0, open: true},
				{id: 18, text: "Stage #2", start_date: "05-04-2023 00:00", duration: 2, parent: "13", progress: 0, open: true},
				{id: 19, text: "Stage #3", start_date: "08-04-2023 00:00", duration: 1, parent: "13", progress: 0, open: true},
				{id: 20, text: "Stage #4", start_date: "10-04-2023 00:00", duration: 4, parent: "13", progress: 0, open: true},
				{id: 14, text: "Task #3", start_date: "02-04-2023 00:00", duration: 6, parent: "11", progress: 0, open: true},
				{id: 15, text: "Task #4", type: "project", render:"split", parent: "11", progress: 0, open: true, start_date: "03-04-2023 00:00", duration: 11},
				{id: 21, text: "Stage #1", start_date: "03-04-2023 00:00", duration: 4, parent: "15", progress: 0, open: true},
				{id: 22, text: "Stage #2", start_date: "08-04-2023 00:00", duration: 3, parent: "15", progress: 0, open: true},
				{id: 23, text: "Mediate milestone", start_date: "14-04-2023 00:00", duration: 0, type: "milestone", parent: "15", progress: 0, open: true, duration: 0},
				{id: 16, text: "Final milestone", start_date: "15-04-2023 00:00", duration: 0, type: "milestone", parent: "11", progress: 0, open: true, duration: 0}
			],
			links: [
				{id: "1", source: "17", target: "18", type: "0"},
				{id: "2", source: "18", target: "19", type: "0"},
				{id: "3", source: "19", target: "20", type: "0"},
				{id: "4", source: "21", target: "22", type: "0"},
				{id: "5", source: "22", target: "23", type: "0"}
			]
		});

		function onDragEnd(startPoint, endPoint, startDate, endDate, tasksBetweenDates, tasksInRow) {
			if (tasksInRow.length === 1) {
				var currentTask = tasksInRow[0];
				if (currentTask.type === "project") {
					currentTask.render = "split";
					gantt.addTask({
						text: "Subtask of " + currentTask.text,
						start_date: gantt.roundDate(startDate),
						end_date: gantt.roundDate(endDate)
					}, currentTask.id);
				} else {
					var projectName = "new Project " + currentTask.text;
					var newProject = gantt.addTask({
						text: projectName,
						render: "split",
						type: "project",
					}, currentTask.parent);
					gantt.moveTask(newProject, gantt.getTaskIndex(currentTask.id), gantt.getParent(currentTask.id));
					gantt.moveTask(currentTask.id, 0, newProject);
					gantt.calculateTaskLevel(currentTask)

					var newTask = gantt.addTask({
						text: "Subtask of " + projectName,
						start_date: gantt.roundDate(startDate),
						end_date: gantt.roundDate(endDate)
					}, newProject);
					gantt.calculateTaskLevel(newTask);
				}
			} else if (tasksInRow.length === 0) {
				gantt.createTask({
					text: "New task",
					start_date: gantt.roundDate(startDate),
					end_date: gantt.roundDate(endDate)
				});
			}
		}
	</script>
</body>

</html>