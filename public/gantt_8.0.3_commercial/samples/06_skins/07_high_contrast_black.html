<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>High contrast theme - Black</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.3"></script>
	<link rel="stylesheet" href="../../codebase/skins/dhtmlxgantt_contrast_black.css?v=8.0.3">
	<link rel="stylesheet" href="../common/controls_styles.css?v=8.0.3">

	<script src="../common/testdata.js?v=8.0.3"></script>
	<style>
		html, body {
			background: #000;
			font-family: arial;
			height: 100%;
			padding: 0px;
			margin: 0px;
			overflow: hidden;
		}
		.main-content {
			height: 600px;
			height: calc(100vh - 50px);
		}
	</style>
</head>

<body>
<div class="gantt_control">
	<input type="button" value="Show Lightbox" onclick="gantt.createTask()"/>
	<input type="button" value="Show Quick Info" onclick="if (gantt.getTaskByTime()[0]) gantt.showQuickInfo(gantt.getTaskByTime()[0].id)"/>
	<input type="button" value="Show message" onclick="gantt.message({text:'Some text'})"/>
	<input type="button" value="Show error" onclick="gantt.message({text:'Some text', type:'error'})"/>
	<input type="button" value="Show alert" onclick="gantt.alert({text:'Some text'})"/>
	<input type="button" value="Show alert with header" onclick="gantt.alert({text:'Some text', title:'Title'})"/>
</div>
<div class="main-content">
	<div id="gantt_here" style='width:100%; height:100%;'></div>
</div>
<script>
	
	gantt.plugins({
		quick_info: true,
		tooltip: true
	});
	gantt.init("gantt_here");
	gantt.message({text: "Some text", expire: -1});
	gantt.message({text: "Some text", type: "error", expire: -1});
	gantt.parse(projects_with_milestones);
</script>
</body>