<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Material theme</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.3"></script>
	<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Open+Sans|Roboto:regular,medium,thin,bold">
	<link rel="stylesheet" href="../../codebase/skins/dhtmlxgantt_material.css?v=8.0.3">
	<link rel="stylesheet" href="../common/controls_styles.css?v=8.0.3">

	<script src="../common/testdata.js?v=8.0.3"></script>
	<style>
		html, body {
			background: #fff;
			font-family: arial;
			height: 100%;
			padding: 0px;
			margin: 0px;
			overflow: hidden;
		}
		.main-content {
			height: 600px;
			height: calc(100vh - 50px);
		}
	</style>
</head>

<body>
<div class="gantt_control">
	<input type="button" value="Show Lightbox" onclick="gantt.createTask()"/>
	<input type="button" value="Show Quick Info" onclick="if (gantt.getTaskByTime()[0]) gantt.showQuickInfo(gantt.getTaskByTime()[0].id)"/>
	<input type="button" value="Show message" onclick="gantt.message({text:'Some text',expire:50000})"/>
	<input type="button" value="Show error" onclick="gantt.message({text:'Some text', type:'error'})"/>
	<input type="button" value="Show alert" onclick="gantt.alert({text:'Some text'})"/>
	<input type="button" value="Show alert with header" onclick="gantt.alert({text:'Some text', title:'Title'})"/>
	<input type="button" value="Show critical" onclick="toggleCritical()"/>
</div>
<div class="main-content">
	<div id="gantt_here" style='width:100%; height:100%;padding: 0px;'></div>
</div>

<script>
	gantt.plugins({
		quick_info: true,
		tooltip: true,
		critical_path: true
	});

	var toggleCritical = function () {
		if (gantt.config.highlight_critical_path)
			gantt.config.highlight_critical_path = !true;
		else
			gantt.config.highlight_critical_path = true;
		gantt.render();
	};

	gantt.config.columns = [
		{name: "wbs", label: "WBS", width: 40, template: gantt.getWBSCode, resize: true},
		{name: "text", label: "Task name", tree: true, width: 170, resize: true, min_width: 10},
		{name: "start_date", align: "center", width: 90, resize: true},
		{name: "duration", align: "center", width: 80, resize: true},
		{name: "add", width: 40}
	];

	gantt.templates.rightside_text = function (start, end, task) {
		if (task.type == gantt.config.types.milestone)
			return task.text + " / ID: #" + task.id;
		return "";
	};

	gantt.config.start_on_monday = false;

	gantt.config.scale_height = 36 * 3;
	gantt.config.scales = [
		{unit: "month", step: 1, format: "%F"},
		{unit: "week", step: 1, format: function (date) {
			var dateToStr = gantt.date.date_to_str("%d %M");
			var endDate = gantt.date.add(gantt.date.add(date, 1, "week"), -1, "day");
			return dateToStr(date) + " - " + dateToStr(endDate);
		}},
		{unit: "day", step: 1, format: "%D"}
	];

	gantt.init("gantt_here");
	gantt.message({text: "Some text", expire: -1});
	gantt.message({text: "Some text", type: "error", expire: -1});
	gantt.parse(projects_milestones_critical);
</script>
</body>