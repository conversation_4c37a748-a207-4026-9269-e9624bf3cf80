<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Grid columns rightside of gantt</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.3"></script>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.3">
	<style>
		html, body {
			padding: 0px;
			margin: 0px;
			height: 100%;
		}

		.gantt_grid_scale .gantt_grid_head_cell,
		.gantt_task .gantt_task_scale .gantt_scale_cell {
			font-weight: bold;
			font-size: 14px;
			color: rgba(0, 0, 0, 0.7);
		}
	</style>
</head>
<body>
<div id="gantt_here" style='width:100%; height:100%;'></div>
<script>

	var secondGridColumns = {
		columns: [
			{
				name: "status", label: "Status", width: 60, align: "center", template: function (task) {
					var progress = task.progress || 0;
					return Math.floor(progress * 100) + "";
				}
			},
			{
				name: "impact", width: 80, label: "Impact", template: function (task) {
					return (task.duration * 1000).toLocaleString("en-US", {style: 'currency', currency: 'USD'});
				}
			}
		]
	};

	gantt.config.layout = {
		css: "gantt_container",
		rows: [
			{
				cols: [
					{view: "grid", width: 320, scrollY: "scrollVer"},
					{resizer: true, width: 1},
					{view: "timeline", scrollX: "scrollHor", scrollY: "scrollVer"},
					{resizer: true, width: 1},
					{view: "grid", width: 140, bind: "task", scrollY: "scrollVer", config: secondGridColumns},
					{view: "scrollbar", id: "scrollVer"}
				]

			},
			{view: "scrollbar", id: "scrollHor", height: 20}
		]
	};

	gantt.init("gantt_here");
	gantt.parse({
		data: [
			{id: 1, text: "Office itinerancy", type: gantt.config.types.project, progress: 0.4, open: false},
			{id: 2, text: "Office facing", type: gantt.config.types.project, start_date: "02-04-2022", duration: "8", progress: 0.6, parent: "1", open: true},
			{id: 3, text: "Furniture installation", type: gantt.config.types.project, start_date: "11-04-2022", duration: "8", parent: "1", progress: 0.6, open: true},
			{id: 4, text: "The employee relocation", type: gantt.config.types.project, start_date: "13-04-2022", duration: "6", parent: "1", progress: 0.5, open: true},
			{id: 5, text: "Interior office", start_date: "02-04-2022", duration: "7", parent: "2", progress: 0.6, open: true},
			{id: 6, text: "Air conditioners check", start_date: "03-04-2022", duration: "7", parent: "2", progress: 0.6, open: true},
			{id: 7, text: "Workplaces preparation", start_date: "11-04-2022", duration: "8", parent: "3", progress: 0.6, open: true},
			{id: 8, text: "Preparing workplaces", start_date: "14-04-2022", duration: "5", parent: "4", progress: 0.5, open: true},
			{id: 9, text: "Workplaces importation", start_date: "14-04-2022", duration: "4", parent: "4", progress: 0.5, open: true},
			{id: 10, text: "Workplaces exportation", start_date: "14-04-2022", duration: "3", parent: "4", progress: 0.5, open: true},
			{id: 11, text: "Product launch", type: gantt.config.types.project, progress: 0.6, open: true},
			{id: 12, text: "Perform Initial testing", start_date: "03-04-2022", duration: "5", parent: "11", progress: 1, open: true},
			{id: 13, text: "Development", type: gantt.config.types.project, start_date: "02-04-2022", duration: "7", parent: "11", progress: 0.5, open: true},
			{id: 14, text: "Analysis", start_date: "02-04-2022", duration: "6", parent: "11", progress: 0.8, open: true},
			{id: 15, text: "Design", type: gantt.config.types.project, start_date: "02-04-2022", duration: "5", parent: "11", progress: 0.2, open: false},
			{id: 16, text: "Documentation creation", start_date: "02-04-2022", duration: "7", parent: "11", progress: 0, open: true},
			{id: 17, text: "Develop System", start_date: "03-04-2022", duration: "2", parent: "13", progress: 1, open: true},
			{id: 25, text: "Beta Release", start_date: "06-04-2022", type: gantt.config.types.milestone, parent: "13", progress: 0, open: true},
			{id: 18, text: "Integrate System", start_date: "08-04-2022", duration: "2", parent: "13", progress: 0.8, open: true},
			{id: 19, text: "Test", start_date: "10-04-2022", duration: "4", parent: "13", progress: 0.2, open: true},
			{id: 20, text: "Marketing", start_date: "10-04-2022", duration: "4", parent: "13", progress: 0, open: true},
			{id: 21, text: "Design database", start_date: "03-04-2022", duration: "4", parent: "15", progress: 0.5, open: true},
			{id: 22, text: "Software design", start_date: "03-04-2022", duration: "4", parent: "15", progress: 0.1, open: true},
			{id: 23, text: "Interface setup", start_date: "03-04-2022", duration: "5", parent: "15", progress: 0, open: true},
			{id: 24, text: "Release v1.0", start_date: "15-04-2022", type: gantt.config.types.milestone, parent: "11", progress: 0, open: true}
		],
		links: [
			{id: "1", source: "1", target: "2", type: "1"},
			{id: "2", source: "2", target: "3", type: "0"},
			{id: "3", source: "3", target: "4", type: "0"},
			{id: "4", source: "2", target: "5", type: "2"},
			{id: "5", source: "2", target: "6", type: "2"},
			{id: "6", source: "3", target: "7", type: "2"},
			{id: "7", source: "4", target: "8", type: "2"},
			{id: "8", source: "4", target: "9", type: "2"},
			{id: "9", source: "4", target: "10", type: "2"},
			{id: "10", source: "11", target: "12", type: "1"},
			{id: "11", source: "11", target: "13", type: "1"},
			{id: "12", source: "11", target: "14", type: "1"},
			{id: "13", source: "11", target: "15", type: "1"},
			{id: "14", source: "11", target: "16", type: "1"},
			{id: "15", source: "13", target: "17", type: "1"},
			{id: "16", source: "17", target: "25", type: "0"},
			{id: "23", source: "25", target: "18", type: "0"},
			{id: "17", source: "18", target: "19", type: "0"},
			{id: "18", source: "19", target: "20", type: "0"},
			{id: "19", source: "15", target: "21", type: "2"},
			{id: "20", source: "15", target: "22", type: "2"},
			{id: "21", source: "15", target: "23", type: "2"},
			{id: "22", source: "13", target: "24", type: "0"}
		]
	});
</script>
</body>