<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Dynamic scales</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.3"></script>

	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.3">
	<link rel="stylesheet" href="../common/controls_styles.css?v=8.0.3">

	<script src="../common/testdata.js?v=8.0.3"></script>
	<style>
		html, body {
			height: 100%;
			padding: 0px;
			margin: 0px;
			overflow: hidden;
		}
	</style>
</head>

<body>
<form class="gantt_control">
	<input type="button" value="Zoom In" onclick="zoomIn()">
	<input type="button" value="Zoom Out" onclick="zoomOut()">

	<input type="radio" id="scale1" class="gantt_radio" name="scale" value="day">
	<label for="scale1">Day scale</label>

	<input type="radio" id="scale2" class="gantt_radio" name="scale" value="week">
	<label for="scale2">Week scale</label>

	<input type="radio" id="scale3" class="gantt_radio" name="scale" value="month">
	<label for="scale3">Month scale</label>

	<input type="radio" id="scale4" class="gantt_radio" name="scale" value="quarter">
	<label for="scale4">Quarter scale</label>

	<input type="radio" id="scale5" class="gantt_radio" name="scale" value="year" checked>
	<label for="scale5">Year scale</label>

</form>
<div id="gantt_here" style='width:100%; height:calc(100vh - 52px);'></div>

<script>
	var zoomConfig = {
		levels: [
			{
				name:"day",
				scale_height: 27,
				min_column_width:80,
				scales:[
					{unit: "day", step: 1, format: "%d %M"}
				]
			},
			{
				name:"week",
				scale_height: 50,
				min_column_width:50,
				scales:[
					{unit: "week", step: 1, format: function (date) {
						var dateToStr = gantt.date.date_to_str("%d %M");
						var endDate = gantt.date.add(date, -6, "day");
						var weekNum = gantt.date.date_to_str("%W")(date);
						return "#" + weekNum + ", " + dateToStr(date) + " - " + dateToStr(endDate);
					}},
					{unit: "day", step: 1, format: "%j %D"}
				]
			},
			{
				name:"month",
				scale_height: 50,
				min_column_width:120,
				scales:[
					{unit: "month", format: "%F, %Y"},
					{unit: "week", format: "Week #%W"}
				]
			},
			{
				name:"quarter",
				height: 50,
				min_column_width:90,
				scales:[
					{unit: "month", step: 1, format: "%M"},
					{
						unit: "quarter", step: 1, format: function (date) {
							var dateToStr = gantt.date.date_to_str("%M");
							var endDate = gantt.date.add(gantt.date.add(date, 3, "month"), -1, "day");
							return dateToStr(date) + " - " + dateToStr(endDate);
						}
					}
				]
			},
			{
				name:"year",
				scale_height: 50,
				min_column_width: 30,
				scales:[
					{unit: "year", step: 1, format: "%Y"}
				]
			}
		]
	};

	gantt.ext.zoom.init(zoomConfig);
	gantt.ext.zoom.setLevel("year");
	gantt.ext.zoom.attachEvent("onAfterZoom", function(level, config){
		document.querySelector(".gantt_radio[value='" +config.name+ "']").checked = true;
	})

	gantt.init("gantt_here", new Date(2022, 8, 1), new Date(2023, 10, 1));
	gantt.parse(demo_tasks);

	function zoomIn(){
		gantt.ext.zoom.zoomIn();
	}
	function zoomOut(){
		gantt.ext.zoom.zoomOut()
	}

	var radios = document.getElementsByName("scale");
	for (var i = 0; i < radios.length; i++) {
		radios[i].onclick = function (event) {
			gantt.ext.zoom.setLevel(event.target.value);
		};
	}
</script>
</body>