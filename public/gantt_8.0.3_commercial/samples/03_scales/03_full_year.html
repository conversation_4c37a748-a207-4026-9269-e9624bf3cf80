<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Year scale</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.3"></script>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.3">

	<script src="../common/testdata.js?v=8.0.3"></script>
	<style>
		html, body {
			height: 100%;
			padding: 0px;
			margin: 0px;
			overflow: hidden;
		}
	</style>
</head>

<body>
<div id="gantt_here" style='width:100%; height:100%;'></div>
<script>

	gantt.config.min_column_width = 50;

	gantt.config.scale_height = 90;

	var monthScaleTemplate = function (date) {
		var dateToStr = gantt.date.date_to_str("%M");
		var endDate = gantt.date.add(date, 2, "month");
		return dateToStr(date) + " - " + dateToStr(endDate);
	};

	gantt.config.scales = [
		{unit: "year", step: 1, format: "%Y"},
		{unit: "month", step: 3, format: monthScaleTemplate},
		{unit: "month", step: 1, format: "%M"}
	];

	gantt.init("gantt_here");
	gantt.parse(demo_tasks);
</script>
</body>