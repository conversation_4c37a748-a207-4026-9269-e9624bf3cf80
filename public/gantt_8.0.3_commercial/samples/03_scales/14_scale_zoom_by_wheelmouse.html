<!DOCTYPE html>
<html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Mouse wheel zoom</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.3"></script>
	<script src="../common/testdata.js?v=8.0.3"></script>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.3">
	<link rel="stylesheet" href="../common/controls_styles.css?v=8.0.3">

	<style>
		html, body {
			height: 100%;
			padding: 0px;
			margin: 0px;
		}
	</style>
</head>
<body>
<div id="gantt_here" style="width:100%; height:700px; position: relative;"></div>
<script>

	var hourToStr = gantt.date.date_to_str("%H:%i");
	var hourRangeFormat = function(step){
		return function(date){
			var intervalEnd = new Date(gantt.date.add(date, step, "hour") - 1)
			return hourToStr(date) + " - " + hourToStr(intervalEnd);
		};
	};


	gantt.config.min_column_width = 80;
	var zoomConfig = {
		minColumnWidth: 80,
		maxColumnWidth: 150,
		levels: [
			[
				{ unit: "month", format: "%M %Y", step: 1},
				{ unit: "week", step: 1, format: function (date) {
						var dateToStr = gantt.date.date_to_str("%d %M");
						var endDate = gantt.date.add(date, -6, "day");
						var weekNum = gantt.date.date_to_str("%W")(date);
						return "Week #" + weekNum + ", " + dateToStr(date) + " - " + dateToStr(endDate);
					}}
			],
			[
				{ unit: "month", format: "%M %Y", step: 1},
				{ unit: "day", format: "%d %M", step: 1}
			],
			[
				{ unit: "day", format: "%d %M", step: 1},
				{ unit: "hour", format: hourRangeFormat(12), step: 12}
			],
			[
				{unit: "day", format: "%d %M",step: 1},
				{unit: "hour",format: hourRangeFormat(6),step: 6}
			],
			[
				{ unit: "day", format: "%d %M", step: 1 },
				{ unit: "hour", format: "%H:%i", step: 1}
			]
		],
		startDate: new Date(2023, 02, 27),
		endDate: new Date(2023, 03, 20),
		useKey: "ctrlKey",
		trigger: "wheel",
		element: function(){
			return gantt.$root.querySelector(".gantt_task");
		}
	}

	gantt.ext.zoom.init(zoomConfig);
	gantt.init("gantt_here");
	gantt.parse(demo_tasks);

	gantt.message({
		text:"Use <b>ctrl + mousewheel</b> in order to zoom",
		expire: -1
	});
</script>
</body>