<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Year quarters scale</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.3"></script>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.3">

	<script src="../common/testdata.js?v=8.0.3"></script>
	<style>
		html, body {
			height: 100%;
			padding: 0px;
			margin: 0px;
			overflow: hidden;
		}
	</style>
</head>

<body>
<div id="gantt_here" style='width:100%; height:100%;'></div>
<script>
	gantt.config.min_column_width = 50;

	gantt.config.scale_height = 90;

	function quarterLabel(date) {
		var month = date.getMonth();
		var q_num;

		if (month >= 9) {
			q_num = 4;
		} else if (month >= 6) {
			q_num = 3;
		} else if (month >= 3) {
			q_num = 2;
		} else {
			q_num = 1;
		}

		return "Q" + q_num;
	}

	gantt.config.scales = [
		{unit: "year", step: 1, format: "%Y"},
		{unit: "quarter", step: 1, format: quarterLabel},
		{unit: "month", step: 1, format: "%M"}
	];

	gantt.init("gantt_here");
	gantt.parse(demo_tasks);

</script>
</body>