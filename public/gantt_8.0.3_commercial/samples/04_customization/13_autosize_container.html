<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Expand container</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.3"></script>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.3">

	<script src="../common/data.js?v=8.0.3"></script>
	<style>
		html, body {
			padding: 0px;
			margin: 0px;
		}
	</style>
</head>

<body>

<div id="gantt_here"></div>
<script>
	gantt.config.date_format = "%Y-%m-%d %H:%i:%s";
	gantt.config.autosize = true;
	gantt.init("gantt_here");
	gantt.parse(taskData);

	gantt.message("Gantt container is adjusted to the size of the content");
</script>
</body>