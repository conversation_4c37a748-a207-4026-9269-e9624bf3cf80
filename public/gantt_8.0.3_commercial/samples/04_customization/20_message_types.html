<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Gantt message types</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.3"></script>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.3">
	<link rel="stylesheet" href="../common/controls_styles.css?v=8.0.3">

	<script src="../common/testdata.js?v=8.0.3"></script>
	<style>
		html, body {
			height: 100%;
			padding: 0px;
			margin: 0px;
			overflow: hidden;
		}
		.buttons input {
			min-width: 100px;
		}
	</style>
</head>

<body>

	<div class="buttons gantt_control">
		<input type="button" value="Info" onclick="showMessage('info')"/>
		<input type="button" value="Warning" onclick="showMessage('warning')"/>
		<input type="button" value="Error" onclick="showMessage('error')"/>
	</div>
	<div id="gantt_here" style='width:100%; height:calc(100vh - 52px);'></div>

	<script>
		gantt.init("gantt_here");
		gantt.parse(projects_with_milestones);

		var showMessage = function(type) {
			var text = "";
			var countTask = gantt.getTaskCount();
			var countLink = gantt.getLinkCount();
			var version   = gantt.version;

			text += "Tasks count: " + countTask + "<br>";
			text += "Links count: " + countLink + "<br>";
			text += "Gantt version: " + version;

			gantt.message({type:type, text:text, expire: 10000});

		}
		gantt.message({text:"Click on the buttons to explore Gantt message types", expire:-1, type:"error"});
	</script>
</body>