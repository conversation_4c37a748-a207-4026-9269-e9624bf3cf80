<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Display subtasks when the Project is closed</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.3"></script>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.3">

	<script src="../common/testdata.js?v=8.0.3"></script>
	<style>
		html, body {
			height: 100%;
			padding: 0px;
			margin: 0px;
			overflow: hidden;
		}

		.child_preview {
			box-sizing: border-box;
			margin-top: 2px;
			position: absolute;
			z-index: 1;
			color: white;
			text-align: center;
			font-size: 12px;
		}

		.gantt_task_line.task-collapsed {
			height: 4px;
			opacity: 0.25;
		}

		.gantt_task_line.gantt_project.task-collapsed .gantt_task_content {
			display: none;
		}

		.gantt_row.task-parent {
			font-weight: bold;
		}
	</style>
</head>
<body>
<div id="gantt_here" style='width:100%; height:100%;'></div>


<script>
	gantt.config.row_height = 24;
	gantt.config.scale_height = 50;

	gantt.config.scales = [
		{unit: "month", step: 1, format: "%F, %Y"},
		{unit: "day", step: 1, format: "%j, %D"}
	];

	gantt.templates.rightside_text = function (start, end, task) {
		if (task.type == gantt.config.types.milestone) {
			return task.text;
		}
		return "";
	};
	gantt.config.lightbox.sections = [
		{name: "description", height: 70, map_to: "text", type: "textarea", focus: true},
		{name: "type", type: "typeselect", map_to: "type"},
		{name: "time", type: "duration", map_to: "auto"}
	];
	gantt.init("gantt_here");

	function createBox(sizes, class_name) {
		var box = document.createElement('div');
		box.style.cssText = [
			"height:" + sizes.height + "px",
			"line-height:" + sizes.height + "px",
			"width:" + sizes.width + "px",
			"top:" + sizes.top + 'px',
			"left:" + sizes.left + "px",
			"position:absolute"
		].join(";");
		box.className = class_name;
		return box;
	}

	gantt.templates.grid_row_class = gantt.templates.task_class = function (start, end, task) {
		var css = [];
		if (gantt.hasChild(task.id)) {
			css.push("task-parent");
		}
		if (!task.$open && gantt.hasChild(task.id)) {
			css.push("task-collapsed");
		}

		return css.join(" ");
	};

	gantt.addTaskLayer(function show_hidden(task) {
		if (!task.$open && gantt.hasChild(task.id)) {
			var sub_height = gantt.config.row_height - 5,
				el = document.createElement('div'),
				sizes = gantt.getTaskPosition(task);

			var sub_tasks = gantt.getChildren(task.id);

			var child_el;

			for (var i = 0; i < sub_tasks.length; i++) {
				var child = gantt.getTask(sub_tasks[i]);
				var child_sizes = gantt.getTaskPosition(child);

				child_el = createBox({
					height: sub_height,
					top: sizes.top,
					left: child_sizes.left,
					width: child_sizes.width
				}, "child_preview gantt_task_line");
				child_el.innerHTML = child.text;
				el.appendChild(child_el);
			}
			return el;
		}
		return false;
	});

	gantt.parse({
		data: [
			{id: 11, text: "Project #1", type: "project", progress: 0.6, open: true, start_date: "02-04-2023 00:00", duration: 13, parent: 0},
			{id: 12, text: "Task #1", start_date: "03-04-2023 00:00", duration: 5, parent: "11", progress: 1, open: true},
			{id: 13, text: "Task #2", start_date: "03-04-2023 00:00", type: "project", parent: "11", progress: 0.5, open: false, duration: 11},
			{id: 17, text: "Task #2.1", start_date: "03-04-2023 00:00", duration: 1, parent: "13", progress: 1, open: true},
			{id: 18, text: "Task #2.2", start_date: "05-04-2023 00:00", duration: 2, parent: "13", progress: 0.8, open: true},
			{id: 19, text: "Task #2.3", start_date: "08-04-2023 00:00", duration: 1, parent: "13", progress: 0.2, open: true},
			{id: 20, text: "Task #2.4", start_date: "10-04-2023 00:00", duration: 4, parent: "13", progress: 0, open: true},
			{id: 14, text: "Task #3", start_date: "02-04-2023 00:00", duration: 6, parent: "11", progress: 0.8, open: true},
			{id: 15, text: "Task #4", type: "project", parent: "11", progress: 0.2, open: true, start_date: "03-04-2023 00:00", duration: 11},
			{id: 21, text: "Task #4.1", start_date: "03-04-2023 00:00", duration: 4, parent: "15", progress: 0.5, open: true},
			{id: 22, text: "Task #4.2", start_date: "08-04-2023 00:00", duration: 3, parent: "15", progress: 0.1, open: true},
			{id: 23, text: "Mediate milestone", start_date: "14-04-2023 00:00", type: "milestone", parent: "15", progress: 0, open: true, duration: 0},
			{id: 16, text: "Final milestone", start_date: "15-04-2023 00:00", type: "milestone", parent: "11", progress: 0, open: true, duration: 0}
		],
		links: [
			{id: "10", source: "11", target: "12", type: "1"},
			{id: "11", source: "11", target: "13", type: "1"},
			{id: "12", source: "11", target: "14", type: "1"},
			{id: "13", source: "11", target: "15", type: "1"},
			{id: "14", source: "23", target: "16", type: "0"},
			{id: "15", source: "13", target: "17", type: "1"},
			{id: "16", source: "17", target: "18", type: "0"},
			{id: "17", source: "18", target: "19", type: "0"},
			{id: "18", source: "19", target: "20", type: "0"},
			{id: "19", source: "15", target: "21", type: "2"},
			{id: "20", source: "15", target: "22", type: "2"},
			{id: "21", source: "15", target: "23", type: "0"}
		]
	});
</script>
</body>