<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Displaying deadlines</title>

	<script src="../../codebase/dhtmlxgantt.js?v=8.0.3"></script>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.3">
	<script src="../common/data_extra.js?v=8.0.3"></script>
	<style>
		html, body {
			height: 100%;
			padding: 0px;
			margin: 0px;
			overflow: hidden;
		}

		.deadline {
			position: absolute;
			border-radius: 12px;
			border: 2px solid #585858;
			-moz-box-sizing: border-box;
			box-sizing: border-box;

			width: 22px;
			height: 22px;
			margin-left: -11px;
			margin-top: 6px;
			z-index: 1;
			background: url("common/deadline_icon.png") center no-repeat;
		}

		.overdue-indicator {
			width: 24px;
			margin-top: 5px;
			height: 24px;
			-moz-box-sizing: border-box;
			box-sizing: border-box;
			border-radius: 17px;
			color: white;
			background: rgb(255, 60, 60);
			line-height: 25px;
			text-align: center;
			font-size: 24px;
		}


	</style>
</head>
<body>
<div id="gantt_here" style='width:100%; height:100%;'></div>
<script>
	gantt.config.date_format = "%Y-%m-%d %H:%i:%s";

	gantt.locale.labels.deadline_enable_button = 'Set';
	gantt.locale.labels.deadline_disable_button = 'Remove';

	gantt.config.lightbox.sections = [
		{name: "description", height: 70, map_to: "text", type: "textarea", focus: true},
		{name: "time", map_to: "auto", type: "duration"},
		{
			name: "deadline", map_to: {start_date: "deadline"},
			type: "duration_optional",
			button: true,
			single_date: true
		}
	];


	gantt.config.columns = [
		{
			name: "overdue", label: "", width: 38, template: function (obj) {
				if (obj.deadline) {
					var deadline = gantt.date.parseDate(obj.deadline, "xml_date");
					if (deadline && obj.end_date > deadline) {
						return '<div class="overdue-indicator">!</div>';
					}
				}
				return '<div></div>';
			}
		},
		{name: "text", label: "Task name", width: "*", tree: true, resize: true},
		{name: "start_date", label: "Start time", align: "center", width: 80},
		{name: "deadline", label: "Deadline", width: 80, align: "center"},
		{name: "duration", label: "Duration", align: "center", width: 60},
		{name: "add", label: "", width: 36}
	];
	gantt.config.grid_width = 420;
	gantt.locale.labels.section_deadline = "Deadline";

	gantt.addTaskLayer({
		renderer: {
			render: function draw_deadline(task) {
				if (task.deadline) {
					var el = document.createElement('div');
					el.className = 'deadline';
					var sizes = gantt.getTaskPosition(task, task.deadline);

					el.style.left = sizes.left + 'px';
					el.style.top = sizes.top + 'px';

					el.setAttribute('title', gantt.templates.task_date(task.deadline));
					return el;
				}
				return false;
			},
			// define getRectangle in order to hook layer with the smart rendering
			getRectangle: function(task, view){
				if(task.deadline){
					return gantt.getTaskPosition(task, task.deadline);
				}
				return null;
			}
		}
	});

	gantt.templates.task_class = function (start, end, task) {
		if (task.deadline && end.valueOf() > task.deadline.valueOf()) {
			return 'overdue';
		}
	};

	gantt.templates.rightside_text = function (start, end, task) {
		if (task.deadline) {
			if (end.valueOf() > task.deadline.valueOf()) {
				var overdue = Math.ceil(Math.abs((end.getTime() - task.deadline.getTime()) / (24 * 60 * 60 * 1000)));
				var text = "<b>Overdue: " + overdue + " days</b>";
				return text;
			}
		}
	};


	gantt.attachEvent("onTaskLoading", function (task) {
		if (task.deadline)
			task.deadline = gantt.date.parseDate(task.deadline, "xml_date");
		return true;
	});

	gantt.init("gantt_here");
	gantt.parse(taskData);

</script>
</body>