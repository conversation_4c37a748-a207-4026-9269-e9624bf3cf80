<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Custom tree formatting</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.3"></script>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.3">
	<script src="../common/testdata.js?v=8.0.3"></script>

	<style>
		html, body {
			height: 100%;
			padding: 0px;
			margin: 0px;
			overflow: hidden;
		}

		.red .gantt_cell, .odd.red .gantt_cell,
		.red .gantt_task_cell, .odd.red .gantt_task_cell {
			background-color: #FDE0E0;
		}

		.green .gantt_cell, .odd.green .gantt_cell,
		.green .gantt_task_cell, .odd.green .gantt_task_cell {
			background-color: #BEE4BE;
		}
	</style>
</head>

<body>
<div id="gantt_here" style='width:100%; height:100%'></div>

<script>
	gantt.config.grid_width = 380;
	gantt.config.add_column = false;
	gantt.templates.grid_row_class = function (start_date, end_date, item) {
		if (item.progress == 0) return "red";
		if (item.progress >= 1) return "green";
	};
	gantt.templates.task_row_class = function (start_date, end_date, item) {
		if (item.progress == 0) return "red";
		if (item.progress >= 1) return "green";
	};
	gantt.config.columns = [
		{name: "text", label: "Task name", tree: true, width: '*'},
		{
			name: "progress", label: "Progress", width: 80, align: "center",
			template: function (item) {
				if (item.progress >= 1)
					return "Complete";
				if (item.progress == 0)
					return "Not started";
				return Math.round(item.progress * 100) + "%";
			}
		},
		{
			name: "assigned", label: "Assigned to", align: "center", width: 100,
			template: function (item) {
				if (!item.users) return "Nobody";
				return item.users.join(", ");
			}
		}
	];
	gantt.init("gantt_here");
	gantt.parse(users_data);
</script>
</body>