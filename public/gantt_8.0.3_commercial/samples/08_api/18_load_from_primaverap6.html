<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Import Primavera P6 file</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.3"></script>

	<script src="../common/snippets/dhx_file_dnd.js?v=8.0.3"></script>
	<link rel="stylesheet" href="../common/snippets/dhx_file_dnd.css?v=8.0.3">

	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.3">
	<link rel="stylesheet" href="../common/controls_styles.css?v=8.0.3">

	<style>
		html, body {
			height: 100%;
			padding: 0;
			margin: 0;
			overflow: hidden;
			font-family: Arial;
		}

		.primavera-sample {
			background: #d91e25;
			width: 32px;
			height: 32px;
			border-radius: 10px;
			color: white;
			background-repeat: no-repeat;
			line-height: 33px;
			text-decoration: none;
			font-weight: bold;
			font-size: 17px;
			display: inline-block;
		}
		#mspFile{
			border: none;
			background: #ededed;
		}
	</style>
</head>

<body>
<div class="gantt_control" style="text-align: center; padding: 5px;">
	<p>
		You can use any XER/XML Project file or download this sample <a class="primavera-sample"
																		   href="../common/PROJECT.xer"
																		   target="_blank">P6</a>
	</p>
	<p>
	<form id="mspImport" action="" method="POST" enctype="multipart/form-data">
		<input type="file" id="mspFile" name="file"
			   accept=".xer,.xml, text/xml, application/xml, application/xer"/>
		<button id="mspImportBtn" type="submit">Load from XER/XML</button>
	</form>
	</p>
</div>
<div id="gantt_here" style='width:100%;  height:calc(100vh - 121px);'></div>
<script>

	gantt.plugins({
		export_api: true,
	});

	gantt.config.row_height = 24;
	gantt.config.xml_date = "%Y-%m-%d %H:%i";

	gantt.attachEvent("onParse", function () {
		gantt.eachTask(function (task) {
			if (gantt.hasChild(task.id)) {
				task.type = gantt.config.types.project;
				gantt.updateTask(task.id);
			} else if (task.duration === 0) {
				task.type = gantt.config.types.milestone;
				gantt.updateTask(task.id);
			}
		});
	});

	gantt.message("Upload <b>XER</b> or <b>XML</b> Project file using 'Choose File' button or simply drag-and-drop it into the page");

	if (!window.FormData) {
		gantt.message({type:"error", text: "Your browsers does not support Ajax File upload, please open this demo in modern browser"});
	}

	gantt.config.static_background = true;

	gantt.config.xml_date = "%Y-%m-%d %H:%i";
	gantt.init("gantt_here");

	var fileDnD = fileDragAndDrop();
	fileDnD.fileTypeMessage = "Only XER and XML files are supported!";
	fileDnD.dndFileTypeMessage = "Please try XER and XML project file.";
	fileDnD.dndHint = "Drop XER file into Gantt";
	fileDnD.mode = "primaveraP6";
	fileDnD.init(gantt.$container);

	function sendFile(file) {
		fileDnD.showUpload();
		upload(file, function () {
			fileDnD.hideOverlay();
		})
	}

	function upload(file, callback) {
		gantt.importFromPrimaveraP6({
			data: file,
			callback: function (project) {
				if (project) {
					gantt.clearAll();

					if (project.config.duration_unit) {
						gantt.config.duration_unit = project.config.duration_unit;
					}

					gantt.parse(project.data);
				}

				if (callback)
					callback(project);
			}
		});
	}

	fileDnD.onDrop(sendFile);
	var form = document.getElementById("mspImport");
	form.onsubmit = function (event) {
		event.preventDefault();
		var fileInput = document.getElementById("mspFile");
		if (fileInput.files[0])
			sendFile(fileInput.files[0]);
	};
</script>
</body>