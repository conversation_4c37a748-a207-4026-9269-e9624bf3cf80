<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Fixed project dates</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.3"></script>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.3">

	<script src="../common/testdata.js?v=8.0.3"></script>
	<style>
		html, body {
			height: 100%;
			padding: 0px;
			margin: 0px;
			overflow: hidden;
		}

		.gantt_task_line.gantt_dependent_task {
			background-color: #65c16f;
			border: 1px solid #3c9445;

		}

		.gantt_task_line.gantt_dependent_task .gantt_task_progress {
			background-color: #46ad51;
		}

	</style>
</head>

<body>
<div id="gantt_here" style='width:100%; height:100%;'></div>
<script>
	var tasks = {
		data: [
			{
				id: 4, text: "Project #1", start_date: "02-04-2023", duration: 0,
				progress: 0.4, open: true
			},
			{
				id: 5, text: "Task #2.1", start_date: "02-04-2023", duration: 8,
				progress: 0.6, parent: 4
			},
			{
				id: 6, text: "Task #2.2", start_date: "11-04-2023", duration: 8,
				progress: 0.6, parent: 4
			},
			{
				id: 7, text: "Project #2", end_date: "19-04-2023", duration: 0,
				progress: 0.4, open: true
			},
			{
				id: 8, text: "Task #3.1", start_date: "02-04-2023", duration: 8,
				progress: 0.6, parent: 7
			},
			{
				id: 9, text: "Task #3.2", start_date: "11-04-2023", duration: 8,
				progress: 0.6, parent: 7
			}

		],
		links: []
	};

	gantt.templates.task_text = function (start, end, task) {
		var text = [task.text];

		if (task.$no_end && !task.$no_start) {
			text.push("Must start on " + gantt.templates.task_date(start));
		} else if (task.$no_start && !task.$no_end) {
			text.push("Must end by " + gantt.templates.task_date(end));
		}
		return text.join(", ");
	};

	gantt.config.lightbox.sections = [
		{name: "description", height: 70, map_to: "text", type: "textarea", focus: true},
		{name: "time", type: "duration", map_to: "auto"}
	];

	gantt.init("gantt_here");

	gantt.parse(tasks);


	function limitMoveLeft(task, limit) {
		var dur = task.end_date - task.start_date;
		task.end_date = new Date(limit.end_date);
		task.start_date = new Date(+task.end_date - dur);
	}

	function limitMoveRight(task, limit) {
		var dur = task.end_date - task.start_date;
		task.start_date = new Date(limit.start_date);
		task.end_date = new Date(+task.start_date + dur);
	}

	function limitResizeLeft(task, limit) {
		task.end_date = new Date(limit.end_date);
	}

	function limitResizeRight(task, limit) {
		task.start_date = new Date(limit.start_date)
	}

	gantt.attachEvent("onTaskDrag", function (id, mode, task, original, e) {
		var parent = task.parent ? gantt.getTask(task.parent) : null,
			children = gantt.getChildren(id),
			modes = gantt.config.drag_mode;

		var limitLeft = null,
			limitRight = null;

		if (!(mode == modes.move || mode == modes.resize)) return;

		if (mode == modes.move) {
			limitLeft = limitMoveLeft;
			limitRight = limitMoveRight;
		} else if (mode == modes.resize) {
			limitLeft = limitResizeLeft;
			limitRight = limitResizeRight;
		}

		//check parents constraints
		if (parent && +parent.end_date < +task.end_date) {
			limitLeft(task, parent);
		}
		if (parent && +parent.start_date > +task.start_date) {
			limitRight(task, parent);
		}

		//check children constraints
		for (var i = 0; i < children.length; i++) {
			var child = gantt.getTask(children[i]);
			if (+task.end_date < +child.end_date) {
				limitLeft(task, child);
			} else if (+task.start_date > +child.start_date) {
				limitRight(task, child)
			}
		}


	});


</script>
</body>