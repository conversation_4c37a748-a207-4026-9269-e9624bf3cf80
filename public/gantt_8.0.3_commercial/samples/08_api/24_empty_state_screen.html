<html>
<head>
<meta http-equiv="Content-type" content="text/html; charset=utf-8">
<title>Show empty state screen</title>
<script src="../../codebase/dhtmlxgantt.js?v=8.0.3"></script>
<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.3">
<link rel="stylesheet" href="../common/controls_styles.css?v=7.1.7">

<style>
      
</style>

</head>

<body>

<div class="gantt_control" >
  <input type=button value="Toggle grid" onclick="toggleGrid()">
</div>

<div id="gantt_here" style="width: 100%; height: 90%;position: relative;"></div>

  <script>


  function toggleGrid(){
    gantt.config.show_grid = !gantt.config.show_grid;
    gantt.render();
  }

  gantt.config.show_empty_state = true;

  gantt.config.start_date = new Date(2025, 04, 01);
  gantt.config.end_date = new Date(2025, 05, 01);
  gantt.init("gantt_here");

</script>
</body>