<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>D'n'D Events</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.3"></script>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.3">

	<script src="../common/testdata.js?v=8.0.3"></script>
	<style>
		html, body {
			height: 100%;
			padding: 0px;
			margin: 0px;
			overflow: hidden;
		}
	</style>
</head>

<body>
<div id="gantt_here" style='width:100%; height:100%;'></div>
<script>

	gantt.attachEvent("onAfterTaskDrag", function (id, mode) {
		var task = gantt.getTask(id);
		if (mode == gantt.config.drag_mode.progress) {
			var pr = Math.floor(task.progress * 100 * 10) / 10;
			gantt.message(task.text + " is now " + pr + "% completed!");
		} else {
			var convert = gantt.date.date_to_str("%H:%i, %F %j");
			var s = convert(task.start_date);
			var e = convert(task.end_date);
			gantt.message(task.text + " starts at " + s + " and ends at " + e);
		}
	});
	gantt.attachEvent("onBeforeTaskChanged", function (id, mode, old_event) {
		var task = gantt.getTask(id);
		if (mode == gantt.config.drag_mode.progress) {
			if (task.progress < old_event.progress) {
				gantt.message(task.text + " progress can't be undone!");
				return false;
			}
		}
		return true;
	});

	gantt.attachEvent("onBeforeTaskDrag", function (id, mode) {
		var task = gantt.getTask(id);
		var message = task.text + " ";

		if (mode == gantt.config.drag_mode.progress) {
			message += "progress is being updated";
		} else {
			message += "is being ";
			if (mode == gantt.config.drag_mode.move)
				message += "moved";
			else if (mode == gantt.config.drag_mode.resize)
				message += "resized";
		}

		gantt.message(message);
		return true;
	});
	gantt.init("gantt_here");
	gantt.parse(demo_tasks);
</script>
</body>