<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Show task dates on Drag and Drop</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.3"></script>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.3">

	<script src="../common/testdata.js?v=8.0.3"></script>
	<style>
		html, body {
			height: 100%;
			padding: 0px;
			margin: 0px;
			overflow: hidden;
		}
	</style>
</head>

<body>
<div id="gantt_here" style='width:100%; height:100%;'></div>


<script>
	gantt.config.min_column_width = 20;
	gantt.config.duration_unit = "minute";
	gantt.config.duration_step = 60;
	gantt.config.scale_height = 75;
	gantt.config.static_background = true;

	gantt.config.scales = [
		{unit: "day", step: 1, format: "%j %F, %l"},
		{unit: "hour", step: 1, format: "%g %a"}
	];

	var dateToStr = gantt.date.date_to_str("%j %F %H:%i");

	gantt.templates.leftside_text = function (start, end, task) {
		var state = gantt.getState(),
			modes = gantt.config.drag_mode;

		if (state.drag_id == task.id) {
			if (state.drag_mode == modes.move || (state.drag_mode == modes.resize && state.drag_from_start)) {
				return dateToStr(gantt.roundDate(start));
			}
		}

		return "";
	};

	gantt.templates.rightside_text = function (start, end, task) {
		var state = gantt.getState(),
			modes = gantt.config.drag_mode;

		if (state.drag_id == task.id) {
			if (state.drag_mode == modes.move || (state.drag_mode == modes.resize && !state.drag_from_start)) {
				return dateToStr(gantt.roundDate(end));
			}
		}
		return "";
	};
	gantt.init("gantt_here");


	gantt.parse(projects_with_milestones);

</script>
</body>