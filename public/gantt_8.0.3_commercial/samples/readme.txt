### Backend support for dhtmlxGantt samples

Some samples require a RESTful API on the backend.
This package contains only HTML/JS/CSS examples, thus the backend implementation is not included.
The API uses a simple JSON format and can be implemented pretty easily.

You can find the protocol details in our docs:
https://docs.dhtmlx.com/gantt/desktop__server_side.html

We also have step-by-step tutorials and demos for different platforms:

- PHP - https://docs.dhtmlx.com/gantt/desktop__howtostart_php.html
- PHP/Laravel - https://docs.dhtmlx.com/gantt/desktop__howtostart_php_laravel.html
- Node.JS - https://docs.dhtmlx.com/gantt/desktop__howtostart_nodejs.html
- ASP.NET MVC - https://docs.dhtmlx.com/gantt/desktop__howtostart_dotnet.html
- Ruby on Rails - https://docs.dhtmlx.com/gantt/desktop__howtostart_ruby.html

Here is a complete list:
https://docs.dhtmlx.com/gantt/desktop__howtostart_guides.html

You can also find all the demos on GitHub:
https://github.com/DHTMLX/?q=gantt-howto
