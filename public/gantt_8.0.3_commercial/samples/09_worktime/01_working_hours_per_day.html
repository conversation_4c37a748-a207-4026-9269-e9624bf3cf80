<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Calculate working hours</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.3"></script>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.3">
	<link rel="stylesheet" href="../common/controls_styles.css?v=8.0.3">

	<script src="../common/testdata.js?v=8.0.3"></script>
	<style>
		html, body {
			height: 100%;
			padding: 0px;
			margin: 0px;
			overflow: hidden;
		}

		.gantt_task_cell.day_end, .gantt_task_cell.no_work_hour.day_start {
			border-right-color: #C7DFFF;
		}

		.gantt_task_cell.week_end.day_end, .gantt_task_cell.week_end.day_start {
			border-right-color: #E2E1E1;
		}

		.gantt_task_cell.week_end, .gantt_task_cell.no_work_hour {
			background-color: #F5F5F5;
		}

		.gantt_task_row.gantt_selected .gantt_task_cell.week_end {
			background-color: #F8EC9C;
		}
	</style>
</head>

<body>
<div class="gantt_control">
	<input type="button" value="Show All Scale" onclick='showAll()'/>
	<input type="button" value="Show work days only" onclick='hideWeekEnds()'/>
	<input type="button" value="Show office hours" onclick='hideNotWorkingTime()'/>
</div>
<div id="gantt_here" style='width:100%; height:calc(100vh - 52px);'></div>

<script>
	gantt.config.work_time = true;
	gantt.setWorkTime({hours: [8, 12, 13, 17]});//global working hours. 8:00-12:00, 13:00-17:00

	gantt.config.min_column_width = 20;
	gantt.config.duration_unit = "hour";
	gantt.config.scale_height = 20 * 3;

	gantt.templates.timeline_cell_class = function (task, date) {
		var css = [];

		if (date.getHours() == 7) {
			css.push("day_start");
		}
		if (date.getHours() == 16) {
			css.push("day_end");
		}
		if (!gantt.isWorkTime(date, 'day')) {
			css.push("week_end");
		} else if (!gantt.isWorkTime(date, 'hour')) {
			css.push("no_work_hour");
		}

		return css.join(" ");
	};

	var weekScaleTemplate = function (date) {
		var dateToStr = gantt.date.date_to_str("%d %M");
		var weekNum = gantt.date.date_to_str("(week %W)");
		var endDate = gantt.date.add(gantt.date.add(date, 1, "week"), -1, "day");
		return dateToStr(date) + " - " + dateToStr(endDate) + " " + weekNum(date);
	};

	gantt.config.scales = [
		{unit: "week", step: 1, format: weekScaleTemplate},
		{unit: "day", step: 1, format: "%l, %F %d"},
		{unit: "hour", step: 1, format: "%G"}
	];


	function showAll() {
		gantt.ignore_time = null;
		gantt.render();
	}

	function hideWeekEnds() {
		gantt.ignore_time = function (date) {
			return !gantt.isWorkTime(date, "day");
		};
		gantt.render();
	}

	function hideNotWorkingTime() {
		gantt.ignore_time = function (date) {
			return !gantt.isWorkTime(date);
		};
		gantt.render();
	}

	gantt.init("gantt_here");
	gantt.parse(demo_tasks);
</script>
</body>