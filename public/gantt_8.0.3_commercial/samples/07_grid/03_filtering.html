<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Filtering</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.3"></script>
	<script src="../common/testdata.js?v=8.0.3"></script>

	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.3">
	<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
	<link rel="stylesheet" href="../common/controls_styles.css?v=8.0.3">
	<style>
		html, body {
			height: 100%;
			padding: 0px;
			margin: 0px;
			overflow: hidden;
		}
		.filters_wrapper {
			font: 600 14px Roboto;
		}

		.filters_wrapper span {
			font-weight: bold;
			padding-right: 5px;
			color: rgba(0,0,0,0.7);
		}

		.filters_wrapper label {
			padding-right: 3px;
		}

	</style>
</head>

<body>
<div class="gantt_control">
	<div class="filters_wrapper" id="filters_wrapper">
		<span>Display tasks with priority:</span>

		<label class="checked_label" id="scale1">
			<input type="checkbox" name="1" value="1" checked/>
			<i class="material-icons icon_color">check_box</i>High
		</label>

		<label id="scale2">
			<input type="checkbox" name="2" value="1"/>
			<i class="material-icons">check_box_outline_blank</i>Normal
		</label>

		<label id="scale3">
			<input type="checkbox" name="3" value="1"/>
			<i class="material-icons">check_box_outline_blank</i>Low
		</label>
	</div>
</div>

<div id="gantt_here" style='width:100%; height:calc(100vh - 52px);'></div>


<script>

	var filter_inputs = document.getElementById("filters_wrapper").getElementsByTagName("input");
	for (var i = 0; i < filter_inputs.length; i++) {
		var filter_input = filter_inputs[i];

		// attach event handler to update filters object and refresh data (so filters will be applied)
		filter_input.onchange = function () {
			gantt.refreshData();
			updIcon(this);
		}
	}

	function hasPriority(parent, priority) {
		if (gantt.getTask(parent).priority == priority)
			return true;

		var child = gantt.getChildren(parent);
		for (var i = 0; i < child.length; i++) {
			if (hasPriority(child[i], priority))
				return true;
		}
		return false;
	}

	gantt.attachEvent("onBeforeTaskDisplay", function (id, task) {
		for (var i = 0; i < filter_inputs.length; i++) {
			var filter_input = filter_inputs[i];


			if (filter_input.checked) {
				if (hasPriority(id, filter_input.name)) {
					return true;
				}
			}

		}
		return false;
	});

	gantt.config.columns = [
		{name: "text", label: "Task name", tree: true, align: "center", width: 160},
		{name: "start_date", label: "Start time", align: "center"},
		{
			name: "priority", label: "Priority", align: "center", template: function (obj) {
				if (obj.priority == 1) return "High";
				if (obj.priority == 2) return "Normal";
				return "Low";
			}
		}
	];
	gantt.init("gantt_here");
	gantt.parse(users_data);

	function updIcon(el){
		el.parentElement.classList.toggle("checked_label");

		var iconEl = el.parentElement.querySelector("i"),
			checked = "check_box",
			unchecked = "check_box_outline_blank",
			className = "icon_color";

		iconEl.textContent = iconEl.textContent==checked?unchecked:checked;
		iconEl.classList.toggle(className);
	}

</script>
</body>