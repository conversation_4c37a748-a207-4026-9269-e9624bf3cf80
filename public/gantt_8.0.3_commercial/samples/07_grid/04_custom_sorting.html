<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Custom sorting function</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.3"></script>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.3">
	<link rel="stylesheet" href="../common/controls_styles.css?v=8.0.3">
	<script src="../common/testdata.js?v=8.0.3"></script>
</head>

<body style="margin: 0">
<div class="gantt_control">
	<input type='button' value='Sort by the number of holders' onclick='sortByHolders(direction)'>
</div>
<div id="gantt_here" style='width:100%; height:calc(100vh - 52px);'></div>

<script>
	var direction = false;

	function sortByHolders(direction1) {
		direction = !direction;
		gantt.sort(sortHolders);
	}

	function sortHolders(a, b) {
		a = a.users.length;
		b = b.users.length;

		if (direction) {
			return a > b ? 1 : (a < b ? -1 : 0);
		} else {
			return a > b ? -1 : (a < b ? 1 : 0);
		}
	}

	gantt.config.columns = [
		{name: "text", label: "Task name", tree: true, width: 160}
	];

	gantt.init("gantt_here");
	gantt.parse(users_data);
</script>
</body>