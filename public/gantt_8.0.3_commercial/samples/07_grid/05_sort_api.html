<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Using sorting methods</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.3"></script>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.3">
	<link rel="stylesheet" href="../common/controls_styles.css?v=8.0.3">
	<script src="../common/testdata.js?v=8.0.3"></script>
</head>

<body style="margin: 0">
<div class="gantt_control">
	<input type='button' value='Sort by priority' onclick='sortByPriority()'>
	<input type='button' value='Sort by task name' onclick='sortByName()'>
</div>
<div id="gantt_here" style='width:100%; height:calc(100vh - 52px);'></div>

<script>
	var p_direction = false;
	var n_direction = false;

	function sortByPriority() {
		if (p_direction) {
			gantt.sort("priority", false);
		} else {
			gantt.sort("priority", true);
		}
		p_direction = !p_direction;
	}

	function sortByName() {
		if (n_direction) {
			gantt.sort("text", false);
		} else {
			gantt.sort("text", true);
		}
		n_direction = !n_direction;
	}

	gantt.config.columns = [
		{name: "text", label: "Task name", tree: true, align: "center", width: 160},
		{name: "start_date", label: "Start time", align: "center"},
		{
			name: "priority", label: "Priority", align: "center", template: function (obj) {
				if (obj.priority == 1) return "High";
				if (obj.priority == 2) return "Normal";
				return "Low";
			}
		}
	]

	gantt.init("gantt_here");
	gantt.parse(users_data);
</script>
</body>