<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Rollup tasks and milestones</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.3"></script>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.3">

	<script src="../common/testdata.js?v=8.0.3"></script>
	<style>
		html, body {
			height: 100%;
			padding: 0px;
			margin: 0px;
			overflow: hidden;
		}
	</style>
</head>

<body>
<div id="gantt_here" style='width:100%; height:100%;'></div>


<script>
	gantt.message({
		text:[
			"Note that milestones are displayed on over Summary rows.",
			"Double click any task to see available options."
		].join("<br><br>"),
		expire: -1
	});

	gantt.config.scale_height = 50;


	gantt.config.scales = [
		{unit: "month", step: 1, format: "%F, %Y"},
		{unit: "day", step: 1, format: "%j, %D"}
	];

	gantt.templates.rightside_text = function (start, end, task) {
		if (task.type == "milestone") {
			return task.text;
		}
		return "";
	};

	gantt.locale.labels.section_rollup = "Rollup";
	gantt.locale.labels.section_hide_bar = "Hide bar";
	gantt.config.lightbox.sections = [
		{name: "description", height: 70, map_to: "text", type: "textarea", focus: true},
		{name: "rollup", type: "checkbox", map_to: "rollup"},
		{name: "hide_bar", type: "checkbox", map_to: "hide_bar"},
		{name: "type", type: "typeselect", map_to: "type"},
		{name: "time", type: "duration", map_to: "auto"}
	];

	gantt.config.lightbox.milestone_sections = [
		{name: "description", height: 70, map_to: "text", type: "textarea", focus: true},
		{name: "rollup", type: "checkbox", map_to: "rollup"},
		{name: "hide_bar", type: "checkbox", map_to: "hide_bar"},
		{name: "type", type: "typeselect", map_to: "type"},
		{name: "time", type: "duration", map_to: "auto"}
	];

	gantt.config.lightbox.project_sections = [
		{name: "description", height: 70, map_to: "text", type: "textarea", focus: true},
		{name: "hide_bar", type: "checkbox", map_to: "hide_bar"},
		{name: "type", type: "typeselect", map_to: "type"},
		{name: "time", type: "duration", map_to: "auto"}
	];


	gantt.init("gantt_here");


	gantt.parse({
		data:[
			{id:11, text:"Project #1", type:"project", progress: 0.6, open: true},

			{id:12, text:"Task #1", start_date:"03-04-2023", duration:"5", parent:"11", progress: 1, open: true},
			{id:13, text:"Task #2", start_date:"03-04-2023", type:"project", parent:"11", progress: 0.5, open: true},
			{id:14, text:"Task #3", start_date:"02-04-2023", duration:"6", parent:"11", progress: 0.8, open: true},
			{id:15, text:"Task #4", type:"project", parent:"11", progress: 0.2, open: true},
			{id:16, text:"Final milestone", start_date:"15-04-2023", type:"milestone", rollup: true, parent:"11", progress: 0, open: true},

			{id:17, text:"Task #2.1", start_date:"03-04-2023", duration:"2", parent:"13", progress: 1, open: true},
			{id:18, text:"Task #2.2", start_date:"06-04-2023", duration:"3", parent:"13", progress: 0.8, open: true},
			{id:19, text:"Task #2.3", start_date:"10-04-2023", duration:"4", parent:"13", progress: 0.2, open: true},
			{id:20, text:"Task #2.4", start_date:"10-04-2023", duration:"4", parent:"13", progress: 0, open: true},
			{id:21, text:"Task #4.1", start_date:"03-04-2023", duration:"4", parent:"15", progress: 0.5, open: true},
			{id:22, text:"Task #4.2", start_date:"03-04-2023", duration:"4", parent:"15", progress: 0.1, open: true},
			{id:23, text:"Mediate milestone", start_date:"13-04-2023", type:"milestone", parent:"15", rollup: true, progress: 0, open: true}
		],
		links:[
			{id:"10",source:"11",target:"12",type:"1"},
			{id:"11",source:"11",target:"13",type:"1"},
			{id:"12",source:"11",target:"14",type:"1"},
			{id:"13",source:"11",target:"15",type:"1"},
			{id:"14",source:"23",target:"16",type:"0"},
			{id:"15",source:"13",target:"17",type:"1"},
			{id:"16",source:"17",target:"18",type:"0"},
			{id:"17",source:"18",target:"19",type:"0"},
			{id:"18",source:"19",target:"20",type:"0"},
			{id:"19",source:"15",target:"21",type:"2"},
			{id:"20",source:"15",target:"22",type:"2"},
			{id:"21",source:"15",target:"23",type:"0"}
		]
	});

</script>
</body>