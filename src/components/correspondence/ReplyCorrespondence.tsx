import { ApolloError } from '@apollo/client';
import { Icon } from '@commons';
import Center from '@components/Center';
import { onError } from '@utils/error';
import { Button, Card, Divider, Form, Input, message } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import moment from 'moment';
import dynamic from 'next/dynamic';
import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import * as Gql from 'src/graphql';
import Recipients from './Recipients';

const ClientEditor = dynamic(() => import('./TextEditor'), {
  ssr: false
});

type ReplyCorrespondenceProps = {
  show: boolean;
  email: Gql.Email | null;
  onClose: () => void;
  onNonMemberModalClick: () => void;
  onRefetch?: () => void;
}

export interface ReplyCorrespondenceRef {
  refetch: (v?: any) => void;
}

const ReplyCorrespondence = forwardRef<ReplyCorrespondenceRef, ReplyCorrespondenceProps>((props, ref) => {
  const [form] = useForm();
  const [projectId, setProjectId] = useState<string>();

  useImperativeHandle(ref, () => ({
    refetch: () => {
      refetch();
    },
  }));

  useEffect(() => {
    setProjectId(localStorage.getItem('ProjectId') || '');

    if (props.email) {
      form.setFieldsValue({
        subject: `Re: ${props.email.subject}`,
        sender: { label: props.email.sender?.name, value: props.email.sender?.id },
      });
    }
  }, [props.email]);

  // Query contacts email
  const { refetch } = Gql.useContactsEmailsQuery({
    variables: {
      filter: {
        projectId: { eq: projectId }
      },
      paging: { offset: 0, limit: 999 }
    },
    onError: onError
  });

  // get me
  const { data: meData, loading: getMeLoading } = Gql.useGetUserMeQuery({
    onError: onError
  });

  // Create email mutation
  const [createEmail, { loading: creating }] = Gql.useCreateOneEmailMutation({
    onError: onError as any,
    onCompleted: async () => {
      props?.onClose();
      props?.onRefetch?.();
      await updateEmail({
        variables: {
          input: {
            id: props.email?.id as string,
            update: {
              replyAt: moment()
            }
          }
        }
      });

      message.success('Your reply sent successfully');
    }
  });

  // update email mutation
  const [updateEmail, { loading: updating }] = Gql.useUpdateOneEmailMutation({
    onError: onError as any,
  });

  // Query contacts email
  const { data: contactsEmailsData, loading: contactsEmailsLoading } = Gql.useContactsEmailsQuery({
    variables: {
      filter: {
        projectId: { eq: projectId }
      },
      paging: { offset: 0, limit: 999 }
    },
    onError: (error: ApolloError) => {
      onError(error);
    }
  });  

  const onFinish = async (values: any) => {
    
    const { quills } = values;
    const files = quills?.files?.map((file: any) => file.originFileObj);
    await createEmail({
      variables: {
        input: {
          email: {
            subject: values.subject,
            reference: values.reference,
            body: values?.quills?.text,
            deliveryStatus: Gql.EmailDeliveryStatus.Sending,
            sentAt: moment(),
            receivers: [{ id: Number(values.sender.value)}],
            senderId: Number(meData?.getUserMe?.id),
            replyToId: props.email?.id ? Number(props.email.id) : undefined,
            attachmentFiles: await Promise.all(files?.map((file: File) => Promise.resolve(file))),
          }
        }
      }
    });
  };

  if (!props?.show) return null;

  const loading = contactsEmailsLoading || creating || updating || getMeLoading;

  return (
    <Card size="small" className='my-4' >
      <Form form={form} onFinish={onFinish} className="h-full">
        <Recipients
          label={'To'}
          name={'sender'}
          required={true}
          form={form}
          disable
          onNonMemberModalClick={props?.onNonMemberModalClick}
          contactsEmailsData={contactsEmailsData}
        />
        <Divider className="my-2" />
        <div className="flex">
          <div className="text-gray600 mt-1">Subject</div>
          <Form.Item
            name="subject"
            rules={[{ required: true, message: 'Subject is required' }]}
            className="mb-0 ml-1 w-full"
          >
            <Input className="w-full border-none focus:border-none" />
          </Form.Item>
        </div>
        <Divider className="my-2" />
        <div className="flex">
          <div className="text-gray600 mt-1">Reference</div>
          <Form.Item
            name="reference"
            rules={[{ required: true, message: 'Reference is required' }]}
            className="mb-0 ml-1 w-full"
          >
            <Input className="w-full border-none focus:border-none" />
          </Form.Item>
        </div>
        <Divider className="my-2" />
        <Form.Item name='quills'>
          <ClientEditor
            style={{ height: '250px', marginBottom: 20 }}
          />
        </Form.Item>

        <Button
          type="primary"
          onClick={() => form.submit()}
          loading={loading}
        >
          Send
        </Button>
      </Form>
    </Card>
  );
});

ReplyCorrespondence.displayName = 'ReplyCorrespondence';
export default ReplyCorrespondence;