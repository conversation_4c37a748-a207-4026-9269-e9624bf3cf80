import data from '@emoji-mart/data';
import Picker from '@emoji-mart/react';
import {
  faAt,
  faBold,
  faFont,
  faItalic,
  faListOl,
  faListUl,
  faSmile,
  faUnderline,
} from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import Quill from 'quill';
import { Mention, MentionBlot } from 'quill-mention';
import 'quill/dist/quill.snow.css';
import { FC, useEffect, useRef, useState } from 'react';

Quill.register('modules/mention', Mention);
Quill.register('blots/mention', MentionBlot);

interface MentionListItem {
  id: string | number;
  value: string;
  [key: string]: any;
}

interface MentionBlotItem extends MentionListItem {
  denotationChar?: string;
}

type User = {
  id: number | string;
  name: string;
};

type Props = {
  value: string;
  onChange: (val: string) => void;
  placeholder?: string;
  users: User[];
  onContentChange?: (isEmpty: boolean) => void; // Add this prop
};

const CommentToolbar: FC<Props> = ({ value, onChange, placeholder, users, onContentChange }) => {
  const quillRef = useRef<Quill | null>(null);
  const editorRef = useRef<HTMLDivElement>(null);
  const lastKnownHTML = useRef<string>('');
  const [showEmoji, setShowEmoji] = useState(false);
  const [showTextOptions, setShowTextOptions] = useState(false);
  const [activeMainButton, setActiveMainButton] = useState<string | null>(null);
  const [activeFormatButtons, setActiveFormatButtons] = useState<string[]>([]);

  useEffect(() => {
    if (!editorRef.current || users.length === 0) return;

    const quill = new Quill(editorRef.current, {
      theme: 'snow',
      placeholder,
      formats: ['bold', 'italic', 'underline', 'list', 'mention'],
      modules: {
        toolbar: false,
        mention: {
          allowedChars: /^[A-Za-z\sÅÄÖåäö]*$/,
          mentionDenotationChars: ['@'],
          source: async (
            searchTerm: string,
            renderList: (items: MentionListItem[], term: string) => void,
          ) => {
            let matchedUsers;
            if (searchTerm.length === 0) {
              matchedUsers = users.slice(0, 10);
            } else {
              matchedUsers = users.filter(user =>
                user.name.toLowerCase().includes(searchTerm.toLowerCase())
              );
            }
            renderList(
              matchedUsers.map(user => ({ id: user.id, value: user.name })),
              searchTerm
            );
          },
          renderItem: (item: MentionListItem): string | HTMLElement => {
            return `${item.value}`; 
          },
          onSelect: (item: MentionListItem, insertItem: (data: MentionBlotItem) => void) => {
            const dataToInsert: MentionBlotItem = {
                id: item.id,
                value: item.value,
                denotationChar: '@' 
            };
            insertItem(dataToInsert);
          },
          minChars: 0,
          maxChars: 31,
          offsetTop: 2,
          offsetLeft: 0,
          isolateCharacter: true,
          fixMentionsToQuill: true,
          defaultMenuOrientation: 'bottom',
          blotName: 'mention',
        },
      },
    });
    
    quill.root.innerHTML = value || '';

    quill.on('text-change', () => {
      const html = quill.root.innerHTML;
      const plainText = quill.getText().trim();
      lastKnownHTML.current = html;
      onChange(html);
      updateActiveFormats(quill);
      
      
  if (onContentChange) {
    const isEmpty = !html.replace(/<[^>]*>/g, '').trim() && !plainText;
    onContentChange(isEmpty);
  }
    });

    setTimeout(() => {
      if (quill.getLength() > 1) {
        quill.setSelection(quill.getLength(), 0);
      }
      quill.focus();
    }, 50);

    quillRef.current = quill;

    return () => {
      quill.off('text-change');
         // @ts-ignore
      if (quillRef.current && quillRef.current.destroy) {
         // @ts-ignore
        quillRef.current.destroy();
      }
      quillRef.current = null;
    };
  }, [users]); 

  useEffect(() => {
    const quill = quillRef.current;
    if (!quill || !value) return;

    const editorHTML = quill.root.innerHTML;

    const normalizeHTML = (html: string) => {
      const el = document.createElement('div');
      el.innerHTML = html;
      return el.innerHTML.replace(/\s+/g, ' ').replace(/<p><br><\/p>/g, '');
    };

    const cleanEditorHTML = normalizeHTML(editorHTML);
    const cleanValue = normalizeHTML(value);

    if (cleanEditorHTML !== cleanValue && lastKnownHTML.current !== value) {
        const selection = quill.getSelection();
        if (selection) {
            try {
                quill.setSelection(selection.index, selection.length, 'silent');
            } catch (e) {
                 quill.setSelection(quill.getLength(), 0, 'silent'); 
            }
        }
        lastKnownHTML.current = value;
    }
  }, [value]);

  const updateActiveFormats = (currentQuill: Quill) => {
    const range = currentQuill.getSelection();
    if (range) {
      const formats = currentQuill.getFormat(range);
      const activeFormats: string[] = [];
      if (formats.bold) activeFormats.push('bold');
      if (formats.italic) activeFormats.push('italic');
      if (formats.underline) activeFormats.push('underline');
      if (formats.list === 'bullet') activeFormats.push('bullet');
      if (formats.list === 'ordered') activeFormats.push('ordered');
      setActiveFormatButtons(activeFormats);
    } else {
      setActiveFormatButtons([]);
    }
  };

  const applyFormat = (format: string, formatValue: any = true) => {
    const quill = quillRef.current;
    if (!quill) return;

    let range = quill.getSelection();
    if (!range) {
        quill.focus();
        range = quill.getSelection() || { index: quill.getLength(), length: 0 };
    }

    const currentFormats = quill.getFormat(range);
    const isFormatActive = format === 'list' ? currentFormats.list === formatValue : currentFormats[format];

    if (format === 'list') {
      quill.format('list', isFormatActive ? false : formatValue);
    } else {
      quill.format(format, !isFormatActive);
    }

    updateActiveFormats(quill);
    setTimeout(() => {
      quill.focus();
      if(range && range.length > 0) {
        quill.setSelection(range.index, range.length, 'user');
      } else if (range) {
        quill.setSelection(range.index, 0, 'user');
      }
    }, 10);
  };

  const insertEmoji = (emoji: any) => { 
    const quill = quillRef.current;
    if (quill) {
      const range = quill.getSelection(true);
      if (!range) return;
      // Insert emoji followed by a space
      quill.insertText(range.index, emoji.native + ' ', 'user');
      setTimeout(() => {
        // Position cursor after the emoji and space
        quill.setSelection(range.index + emoji.native.length + 1, 0, 'user');
        quill.focus();
      }, 0);
      setShowEmoji(false);
      setActiveMainButton(null);
    }
  };

  const handleMainButtonClick = (buttonName: string) => {
    const quill = quillRef.current;
    if (!quill) return;

    if (buttonName !== 'emoji') {
        quill.focus();
    }

    if (buttonName === 'font') {
      setShowTextOptions(prev => !prev);
      if (!showTextOptions) setShowEmoji(false);
    } else if (buttonName === 'emoji') {
      setShowEmoji(prev => !prev);
      if (!showEmoji) setShowTextOptions(false);
    } else {
        setShowEmoji(false);
        setShowTextOptions(false);
    }
    setActiveMainButton(prev => (prev === buttonName && (buttonName === 'font' || buttonName === 'emoji')) ? null : buttonName);
  };

  const handleMentionButtonClick = () => {
    const quill = quillRef.current;
    if (quill) {
      quill.focus();
      const range = quill.getSelection(true) || { index: quill.getLength(), length: 0 };
      quill.insertText(range.index, '@', 'user');
      setTimeout(() => {
      quill.setSelection(range.index + 1, 0, 'user');
      quill.focus();
    }, 0);
      setShowEmoji(false);
      setShowTextOptions(false);
      setActiveMainButton('mention');
    }
  };

  const getMainButtonClass = (buttonName: string) => {
    const baseClass = "bg-transparent border-none text-lg p-0 mr-1 hover:text-blue-500";
    let isActive = activeMainButton === buttonName;
    if (buttonName === 'font' && (activeFormatButtons.length > 0 || showTextOptions)) {
        isActive = true;
    }
    if (buttonName === 'emoji' && showEmoji) {
        isActive = true;
    }
    return `${baseClass} ${isActive ? 'text-blue-500' : 'text-neutral-400'}`;
  };

  const getFormatButtonClass = (format: string) => {
    const baseClass = "bg-transparent border-none text-base p-2 hover:text-blue-500";
    return `${baseClass} ${activeFormatButtons.includes(format) ? 'text-blue-500' : 'text-neutral-600'}`;
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;

      if (!target.closest('.emoji-picker-container') && !target.closest('.emoji-button')) {
        if (showEmoji) {
          setShowEmoji(false);
          setActiveMainButton(prev => (prev === 'emoji' ? null : prev));
        }
      }

      if (!target.closest('.text-formatting-options') && !target.closest('.font-button')) {
         if (showTextOptions) {
            setShowTextOptions(false);
            setActiveMainButton(prev => (prev === 'font' ? null : prev));
         }
      }

      if (!target.closest('.toolbar-container') && !target.closest('.ql-mention-list-container')) {
        if (showEmoji) {
            setShowEmoji(false);
            setActiveMainButton(prev => (prev === 'emoji' ? null : prev));
        }
        if (showTextOptions) {
            setShowTextOptions(false);
            setActiveMainButton(prev => (prev === 'font' ? null : prev));
        }
        if (!showEmoji && !showTextOptions && !target.closest('.quill-editor') && !target.closest('.toolbar-container')) {
           setActiveMainButton(null);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showEmoji, showTextOptions]);

  const handleEmojiButtonClick = () => {
    handleMainButtonClick('emoji');
  };

  const handleFontButtonClick = () => {
    handleMainButtonClick('font');
  }

  return (
    <div className="border rounded-md p-2 bg-white">
      <div className="relative">
        <div ref={editorRef} className="min-h-[150px] p-2 quill-editor ml-2" />
        <div className="toolbar-container absolute bottom-0 left-0 right-0 mt-2 bg-gray-100 p-2 flex flex-wrap items-center gap-3 text-gray-600 border-t">
          <div className="relative">
            <button
              type="button"
              className={`font-button ml-2 ${getMainButtonClass('font')}`}
              onClick={handleFontButtonClick}
              title="Text formatting"
              aria-label="Text formatting options"
            >
              <FontAwesomeIcon icon={faFont} />
            </button>
            {showTextOptions && (
              <div className="text-formatting-options absolute bottom-full mb-2 flex gap-1 p-2 rounded bg-neutral-200 shadow-md z-50">
                <button type="button" title="Bold" className={getFormatButtonClass('bold')} onClick={(e) => { e.stopPropagation(); applyFormat('bold'); }}>
                  <FontAwesomeIcon icon={faBold} />
                </button>
                <button type="button" title="Italic" className={getFormatButtonClass('italic')} onClick={(e) => { e.stopPropagation(); applyFormat('italic'); }}>
                  <FontAwesomeIcon icon={faItalic} />
                </button>
                <button type="button" title="Underline" className={getFormatButtonClass('underline')} onClick={(e) => { e.stopPropagation(); applyFormat('underline'); }}>
                  <FontAwesomeIcon icon={faUnderline} />
                </button>
                <button type="button" title="Bulleted List" className={getFormatButtonClass('bullet')} onClick={(e) => { e.stopPropagation(); applyFormat('list', 'bullet'); }}>
                  <FontAwesomeIcon icon={faListUl} />
                </button>
                <button type="button" title="Numbered List" className={getFormatButtonClass('ordered')} onClick={(e) => { e.stopPropagation(); applyFormat('list', 'ordered'); }}>
                  <FontAwesomeIcon icon={faListOl} />
                </button>
              </div>
            )}
          </div>
          <button
            type="button"
            className={getMainButtonClass('mention')}
            onClick={handleMentionButtonClick}
            title="Mention user"
            aria-label="Mention user"
          >
            <FontAwesomeIcon icon={faAt} />
          </button>
          <div className="relative emoji-picker-container">
            <button
              type="button"
              className={`emoji-button ${getMainButtonClass('emoji')}`}
              onClick={handleEmojiButtonClick}
              title="Insert emoji"
              aria-label="Insert emoji"
            >
              <FontAwesomeIcon icon={faSmile} />
            </button>
            {showEmoji && (
              <div className="absolute z-50 bottom-full left-0 mb-2 shadow-md">
                <Picker
                  data={data}
                  onEmojiSelect={insertEmoji} // Type for emoji selected from Picker might be complex
                  theme="light"
                  previewPosition="none"
                  emojiButtonSize={28}
                  emojiSize={22}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CommentToolbar;
