import UserAvatar from '@components/UserAvatar';
import * as Gql from '@graphql';
import { onError } from '@utils/error';
import { Select, Space } from 'antd';
import _ from 'lodash';
import { FC, useEffect, useState } from 'react';
const { Option } = Select;

const AssigneePicker: FC<any> = ({ value, onChange, taskOwner, projectOwner, cloudCoordinator }) => {
  const { data } = Gql.useProjectUsersQuery({
    variables: {
      paging: {
        limit: 9999,
        offset: 0
      },
      filter: { role: { neq: Gql.ProjectUserRoleType.CanView }, user: { name: { isNot: null }, removedAt: { is: null } }}
    },
    onError: onError
  });
  const [dataSource, setDataSource] = useState<any>([]);
  const [searchText, setSearchText] = useState<string>('');
  const [filteredUsers, setFilteredUsers] = useState<any>([]);

  useEffect(() => {
    setDataSource(data?.projectUsers.nodes ?? []);
  }, [data]);

  return (
    <>
      <Select
        className="assignees-picker"
        id="assignees-picker"
        mode="multiple"
        allowClear
        placeholder="Please select assignees"
        value={value}
        filterOption={false}
        onSearch={searchText => {
          const filtered = _.filter(dataSource, projectUser => {
            if (projectUser?.user?.name) {
              return projectUser?.user?.name?.toLowerCase?.().indexOf(searchText?.toLowerCase?.()) !== -1;
            }
          });
          setSearchText(searchText);
          setFilteredUsers(filtered);
        }}
        onChange={(key: number[]) => {
          onChange(key);
        }}
        disabled={!taskOwner && !projectOwner && !cloudCoordinator}
      >
        {_.map(searchText.length > 0 ? filteredUsers : dataSource, (projectUser, index) => {
          return (
            <Option key={index} value={projectUser.id}>
              <Space className="p-1">
                <UserAvatar
                  className="font-semibold text-sm row-span-2 col-span-1 self-center avatar-default"
                  src={projectUser?.user?.avatar}
                  username={projectUser?.user?.name}
                  style={{ backgroundColor: projectUser?.user?.color || '', color: '#ffffff' }}
                />
                {projectUser.user.name}
              </Space>
            </Option>
          );
        })}
      </Select>
    </>
  );
};

export default AssigneePicker;
