name: <PERSON><PERSON> on PR Merge To develop

on:
  pull_request:
    types:
      - closed

jobs:
  trigger-jenkins:
    if: github.event.pull_request.merged == true && github.event.pull_request.base.ref == 'develop'
    runs-on: ubuntu-latest

    steps:
      - name: <PERSON><PERSON> Job
        env:
          JEN<PERSON>INS_USER: ${{ secrets.JENKINS_USER }}
          JENKINS_API_TOKEN: ${{ secrets.JENKINS_API_TOKEN }}
          JENKINS_TOKEN: ${{ secrets.JENKINS_TOKEN }}
        run: |
          curl -X POST "https://tukang.bina.cloud/job/Web/job/develop_post_merge_build/build?token=$JENKINS_TOKEN" \
            --user "$JENKINS_USER:$JENKINS_API_TOKEN"