# name: Bump version on push to main

# on:
#   push:
#     branches:
#       - main

# jobs:
#   version_bump:
#     name: Version Bump

#     runs-on: ubuntu-latest

#     steps:
#       - name: Checkout code
#         uses: actions/checkout@v2

#       - name: Bump version
#         id: bump-version
#         run: |
#           git config --local user.email "github-actions[bot]@users.noreply.github.com"
#           git config --local user.name "GitHub Actions"
#           npm version patch

#       - name: Push changes
#         uses: ad-m/github-push-action@master
#         with:
#           github_token: ${{ secrets.GITHUB_TOKEN }}

#   build_and_deploy:
#     name: Build and Deploy Docker Image

#     runs-on: ubuntu-latest

#     needs: version_bump

#     steps:
#       - name: Check out repository
#         uses: actions/checkout@v2

#       - name: Set up Docker Buildx
#         uses: docker/setup-buildx-action@v3

#       - name: Login to Container Registry
#         uses: docker/login-action@v3
#         with:
#           username: ${{ vars.DOCKERHUB_USERNAME }}
#           password: ${{ secrets.DOCKERHUB_TOKEN }}

#       - name: Preset Image Name
#         run: echo "IMAGE_URL=$(echo ${{ vars.DOCKERHUB_USERNAME }}/${{ vars.DOCKERHUB_REPO }}:$(echo ${{ github.sha }} | cut -c1-7) | tr '[:upper:]' '[:lower:]')-production" >> $GITHUB_ENV

#       - name: Build and push Docker Image
#         uses: docker/build-push-action@v5
#         with:
#           context: .
#           file: ./Dockerfile.production
#           push: true
#           tags: ${{ env.IMAGE_URL }}

#       - name: Deploy Image to CapRrover
#         uses: caprover/deploy-from-github@v1.1.2
#         with:
#           server: '${{ secrets.CAPROVER_SERVER }}'
#           app: '${{ secrets.PRODUCTION_APP_NAME }}'
#           token: '${{ secrets.PRODUCTION_APP_TOKEN }}'
#           image: ${{ env.IMAGE_URL }}
