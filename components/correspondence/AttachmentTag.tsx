import { Icon } from '@commons';
import { getFileIcon } from '@commons/FileIcon';
import { Tag, Typography, Tooltip } from 'antd';
import React from 'react';

const { Text } = Typography;

interface AttachmentTagProps {
  id: string;
  name: string;
  url: string;
  fileExtension: string;
  onClick: (url: string, fileExtension: string) => void;
}

const AttachmentTag: React.FC<AttachmentTagProps> = ({
  id,
  name,
  url,
  fileExtension,
  onClick
}) => {
  return (
    <Tooltip title={name.trim()}>
      <Tag
        key={id}
        className="text-black bg-[#E9F3FB] border-none flex items-center gap-2 px-1 py-0.5 max-w-[180px] truncate shadow-md cursor-pointer"
        onClick={() => onClick(url, fileExtension)}
      >
        <span className="flex-shrink-0 pt-1">{getFileIcon(fileExtension)}</span>
        <Text className="truncate">{name.trim()}</Text>
      </Tag>
    </Tooltip>
  );
};

export default AttachmentTag;